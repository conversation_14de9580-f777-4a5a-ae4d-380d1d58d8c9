{"cells": [{"cell_type": "code", "execution_count": 16, "id": "8e88b5684411ddad", "metadata": {"ExecuteTime": {"end_time": "2024-09-10T05:45:19.436232Z", "start_time": "2024-09-10T05:45:18.762049Z"}}, "outputs": [], "source": ["# encoding: UTF-8\n", "\n", "\"\"\"\n", "展示如何執行策略回測。\n", "\"\"\"\n", "import re\n", "import sys\n", "import os\n", "import pandas as pd\n", "from pandas import DataFrame"]}, {"cell_type": "code", "execution_count": 17, "id": "5ef404ae", "metadata": {"ExecuteTime": {"end_time": "2024-09-10T05:45:19.451022Z", "start_time": "2024-09-10T05:45:19.441318Z"}}, "outputs": [], "source": ["adpath = os.path.abspath(__file__) if '__file__' in globals() else os.getcwd()\n", "sys.path.extend([os.path.abspath(os.path.join(adpath, *(['..'] * i))) for i in range(3)])"]}, {"cell_type": "code", "execution_count": 18, "id": "e4430812ad3854c1", "metadata": {"ExecuteTime": {"end_time": "2024-09-10T05:45:28.338099Z", "start_time": "2024-09-10T05:45:19.934568Z"}}, "outputs": [], "source": ["def run(startdate, strategy, setting=None):\n", "    # 参数设置\n", "    engine = BacktestingEngine()  # 創建回測引擎\n", "\n", "    engine.set_parameters(\n", "        vt_symbol=f\"{under}.{exchange}\",\n", "        pids=undercode,\n", "        start=startdate,\n", "        end=startdate,\n", "        rate=0.0 / 10000,  # 手续费\n", "        slippage=0,  # 滑点\n", "        size=200,\n", "        pricetick=0.2,  # tick\n", "        interval=Interval.MINUTE,\n", "        tickmaxnum=5,\n", "        capital=1_000_000,\n", "        datamode=DataMode.SNAPSHOT_MODE,\n", "        mode=BacktestingMode.TICK\n", "    )\n", "\n", "    engine.add_strategy(strategy, setting)\n", "    # csvdown.download_csv(under, startdate)\n", "    engine.loadHistoryDataCsv(csvstr1 % startdate, startdate)\n", "    engine.run_backtesting()\n", "    engine.showBacktestingResult()\n", "    engine.plotResultWithTime(path.outdirs)\n", "\n", "    return engine\n", "\n", "if __name__ == '__main__':\n", "    # from iFinDPy import *\n", "    # from WindPy import w\n", "    from db_solve import Logger\n", "    from vnpy.app.vnpy_ctastrategy.backtesting import BacktestingEngine\n", "    from vnpy.app.vnpy_ctastrategy.base import BacktestingMode, DataMode\n", "    from vnpy.trader.constant import Interval\n", "    from strategies.strategy_IM import IMStrategy\n", "    from db_solve.configs import paths\n", "\n", "    date1 = 20240821\n", "    date2 = 20240821\n", "    under = ['SH300', 'SH500', 'CXFD8'][1]\n", "    exchange = ['CFFEX', 'LOCAL'][0]\n", "    undercode = ['IC2409', ]\n", "\n", "    strategy = IMStrategy\n", "\n", "    setting = dict(\n", "        refdealnum=10,\n", "        wide=0.2,\n", "        sig_thres=0.4,\n", "        maxpos=3,\n", "        mult1=0.5,\n", "        mult2=1,\n", "        sigmode='or',\n", "        max_sigadj=1,\n", "        stoppl=dict(active=True, stop_ratio=0.1, track_threshold=0.2, fallback_boundary=0.02, multiplier=3)\n", "    )\n", "\n", "    path = paths.Paths(under, sys.argv[0] if len(sys.argv) > 0 else '.')\n", "    csvstr1 = path.str1\n", "    origin = sys.stdout\n", "    sys.stdout = Logger(path.outdirs + \"\\\\%s-%s-%s.txt\" % (under, date1, date2))\n", "    df: DataFrame = DataFrame(columns=[])\n", "    for date in range(date1, date2 + 1):\n", "        # print save\n", "        date = str(date)\n", "        pattern = rf\"%s\" % date\n", "        dir_path = paths.Paths(under).dirs\n", "        for file in os.listdir(dir_path):\n", "            if not re.search(pattern, file):\n", "                # print(date, 'pass--------------------')\n", "                continue\n", "            else:\n", "                print(date, 'begine--------------------')\n", "                engine=run(date, strategy, setting)\n", "                engine.run_backtesting()\n", "                engine.showBacktestingResult()\n", "                df0 = engine.calculate_result()\n", "                df = df0.copy() if df.empty else pd.concat([df, df0], ignore_index=True)\n", "                break"]}, {"cell_type": "code", "execution_count": null, "id": "1541f940b6aeec54", "metadata": {"ExecuteTime": {"end_time": "2024-09-10T05:45:32.641968Z", "start_time": "2024-09-10T05:45:32.629231Z"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}