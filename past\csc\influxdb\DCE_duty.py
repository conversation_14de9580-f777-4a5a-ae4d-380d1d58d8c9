# -*- coding: utf-8 -*-
"""
Created on Wed Aug  4 10:00:06 2021

@author: yhw52174
"""

from OmmDatabase import OmmDatabase
import datetime
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False    
import re
import time


#%%  
def get_underlying(iid):
    underlying = re.match(r"\D+",iid[0:2]).group()
    return underlying

#%%
# 标准化
def standardizing(df, typ = 'OC'):
    standard_df = {'Unixstamp':[], 'insid':[], 'order_id':[], 'long_short':[], 'order_price':[], 'volume':[], 'typ':[]}
    if df is None:
        return
    if typ == 'OC' or typ == 'OO' or typ == 'OT':
        if typ == 'OC':
            typ1 = 'cancel'
        elif typ == 'OO':
            typ1 = 'order'
        elif typ == 'OT':
            typ1 = 'trade'
                
        for i, row in df.iterrows():
            Unixstamp = i
            insid = row['InstrumentId']
            try:
                order_id = int(row['OrderID'])
            except:
                order_id = str(row['OrderID'])
            long_short = row['LongShort']
            order_price = row['OrderPrice']
            volume = row['VolumeTotal']
            standard_df['Unixstamp'].append(Unixstamp)
            standard_df['insid'].append(insid)
            standard_df['order_id'].append(order_id)
            standard_df['long_short'].append(long_short)
            standard_df['order_price'].append(order_price)
            standard_df['volume'].append(volume)
            standard_df['typ'].append(typ1)
    elif typ == 'QA':
        typ1 = 'all_trade'
        for i, row in df.iterrows():
            Unixstamp = i
            insid = row['iid']
            try:
                order_id = int(row['toid'])
            except:
                order_id = str(row['toid'])
            long_short = row['tbs']
            order_price = row['ttrpr']
            volume = row['tvoltrd']
            standard_df['Unixstamp'].append(Unixstamp)
            standard_df['insid'].append(insid)
            standard_df['order_id'].append(order_id)
            standard_df['long_short'].append(long_short)
            standard_df['order_price'].append(order_price)
            standard_df['volume'].append(volume)
            standard_df['typ'].append(typ1)        
    elif typ == 'QC':
        typ1 = 'cancel'
        for i, row in df.iterrows():
            Unixstamp = i
            insid = row['InstrumentId']
            try:
                order_id = int(row['OrderID'])
            except:
                order_id = str(row['OrderID'])
            long_short = row['LongShort']
            order_price = row['OrderPrice']
            volume = row['VolumeTotal']
            standard_df['Unixstamp'].append(Unixstamp)
            standard_df['insid'].append(insid)
            standard_df['order_id'].append(order_id)
            standard_df['long_short'].append(long_short)
            standard_df['order_price'].append(order_price)
            standard_df['volume'].append(volume)
            standard_df['typ'].append(typ1)        
    elif typ == 'QO':
        typ1 = 'quote_order'
        for i, row in df.iterrows():
            Unixstamp = i
            insid = row['instrumentId']
            order_id = row['bidOrderID']
            if not order_id == '':
                try:
                    order_id = int(order_id)
                except:
                    order_id = str(order_id)
                long_short = 0
                order_price = row['bidOrderPrice']
                volume = row['bidVolumeTotal']               
                standard_df['Unixstamp'].append(Unixstamp)
                standard_df['insid'].append(insid)
                standard_df['order_id'].append(order_id)
                standard_df['long_short'].append(long_short)
                standard_df['order_price'].append(order_price)
                standard_df['volume'].append(volume)
                standard_df['typ'].append(typ1)    
            order_id = row['askOrderID']
            if not order_id == '':
                try:
                    order_id = int(order_id)
                except:
                    order_id = str(order_id)
                long_short = 1
                order_price = row['askOrderPrice']
                volume = row['askVolumeTotal']               
                standard_df['Unixstamp'].append(Unixstamp)
                standard_df['insid'].append(insid)
                standard_df['order_id'].append(order_id)
                standard_df['long_short'].append(long_short)
                standard_df['order_price'].append(order_price)
                standard_df['volume'].append(volume)
                standard_df['typ'].append(typ1)            
                        
    else:
        print('你是故意找茬是不是')
    standard_df = pd.DataFrame(standard_df)
    standard_df.index = standard_df.Unixstamp
    return standard_df

#%%
# 义务统计
def duty(bid_status, ask_status, tick, spr, vol, exchange = 'DCE'): #'DCE', 'ZCE', 'SHFE'
    if bid_status and ask_status:    
        bid_list = []
        ask_list = []
        bidP_list = []
        askP_list = []
        for i in bid_status.keys():
            bid_list.append(bid_status[i])
            bidP_list.append(bid_status[i]['price'])
        for i in ask_status.keys():
            ask_list.append(ask_status[i])
            askP_list.append(ask_status[i]['price'])    
        
        if exchange == 'DCE':             
            bid_order = bid_list[np.argmax(bidP_list)]
            ask_order = ask_list[np.argmin(askP_list)]
                            
            bidP = bid_order['price']
            bidV = bid_order['volume_total']
            askP = ask_order['price']
            askV = ask_order['volume_total']
            spread = np.max([askP - bidP, 0])/tick
            volume = np.min([bidV, askV])
        elif exchange == 'ZCE':
            for i in np.argsort(bidP_list[::-1]):
                bidV = bid_list[i]['volume_total']
                bidP = bid_list[i]['price']
                if bidV >= vol:
                    break
            for i in np.argsort(askP_list):
                askV = ask_list[i]['volume_total']
                askP = ask_list[i]['price']
                if askV >= vol:
                    break                         
            spread = np.max([askP - bidP, 0])/tick   
            volume = np.min([bidV, askV])
        else: # SHFE
            k = 0 # 记录是否是最内层
            bidV = 0
            askV = 0
            for i in np.argsort(bidP_list[::-1]):
                bidV += bid_list[i]['volume_total']
                bidP = bid_list[i]['price']
                if k == 0:
                    bidP_in = bidP
                    k += 1
                if bidV >= vol:
                    break 
            k = 0
            for i in np.argsort(askP_list):
                askV += ask_list[i]['volume_total']
                askP = ask_list[i]['price']
                if k == 0:
                    askP_in = askP
                    k += 1
                if askV >= vol:
                    break                         
            spread = np.max([askP - bidP, 0])/tick   
            volume = np.min([bidV, askV])        
    else:
        spread = np.inf
        volume = 0
    
    if spread <= spr and volume >= vol:
        is_duty = True
        if exchange == 'SHFE':
            spread = np.max([askP_in - bidP_in, 0])/tick
    else:
        is_duty = False
    return (spread, volume, is_duty)
            
        


def duty_accumulate(df, spread = 9, vol = 15, tick = 1, break_t = np.inf, exchange = 'DCE'):
    
    if len(df) == 0:
        return (0, 0)

    error_id = []
    
    is_duty_list = []
    is_duty_details = []
    for k in range(2): # 纠错, 应对omm漏记数据的情况    
        bid_status = {} # 字典中的元素为以订单号order_id为索引, 包含 long_short, price, volume_total 三个元素的字典 
        ask_status = {}
        
        t_total = 0
        spread_total = 0
        spread_split = [0 for i in range(int(spread))]

        
        row = df[0]
        t_now = row['Unixstamp']
        # 初始准备    
        if row['typ'] == 'order' or row['typ'] == 'quote_order': # typ 为 'trade', 'all_trade', 'order', 'quote_order', 'cancel' 之一
            insid = row['insid']
            long_short = row['long_short']
            order_id = row['order_id']
            price = row['order_price']
            volume_total = row['volume']
            if not order_id in error_id:
                if long_short == 0:
                    bid_status[order_id] = {'price': price, 'volume_total': volume_total}
                elif long_short == 1:
                    ask_status[order_id] = {'price': price, 'volume_total': volume_total}
        if k == 1:
            is_duty_list.append([t_now, False])
            is_duty_details.append((t_now, ask_status.copy(), bid_status.copy()))
        
        for row in df[1:]:
            t_last = t_now
            row = dict(row)
            t_now = row['Unixstamp']
            if t_now >= break_t:
                break
            spread_last, vol_last, is_duty = duty(bid_status, ask_status, tick, spread, vol, exchange)
            if k == 1:
                is_duty_list.append([t_now, is_duty])
                is_duty_details.append((t_now, ask_status.copy(), bid_status.copy()))
            
            if is_duty:
                # if t_now - t_last < 300: # 相隔太远可能为收盘
                    t_total += t_now - t_last
                    spread_total += (t_now - t_last)*spread_last
                    spread_split[int(spread_last)-1] += (t_now - t_last)
            order_id = row['order_id']
            
            if order_id in error_id: # 不记录错误信息
                continue
                        
            if row['typ'] == 'trade' or row['typ'] == 'all_trade':
                order_id = row['order_id']
                long_short = row['long_short']
                if long_short == 0:
                    if order_id in bid_status.keys():
                        if row['typ'] == 'trade':
                            bid_status[order_id]['volume_total'] = row['volume']
                        elif row['typ'] == 'all_trade':
                            bid_status[order_id]['volume_total'] -= row['volume']
                        if bid_status[order_id]['volume_total'] == 0:
                            bid_status.pop(order_id)
                if long_short == 1:
                    if order_id in ask_status.keys():
                        if row['typ'] == 'trade':
                            ask_status[order_id]['volume_total'] = row['volume']
                        elif row['typ'] == 'all_trade':
                            ask_status[order_id]['volume_total'] -= row['volume']
                        if ask_status[order_id]['volume_total'] == 0:
                            ask_status.pop(order_id)   
            if row['typ'] == 'cancel':
                order_id = row['order_id']
                long_short = row['long_short']
                if long_short == 0:
                    if order_id in bid_status.keys():
                        bid_status.pop(order_id)
                if long_short == 1:
                    if order_id in ask_status.keys():
                        ask_status.pop(order_id)  
            if row['typ'] == 'order' or row['typ'] == 'quote_order':
                order_id = row['order_id']
                long_short = row['long_short'] 
                price = row['order_price']
                volume_total = row['volume']
                if volume_total > 0:
                    if long_short == 0:
                        if not order_id in bid_status.keys():
                            bid_status[order_id] = {'price': price, 'volume_total': volume_total}
                    if long_short == 1:
                        if not order_id in ask_status.keys():
                            ask_status[order_id] = {'price': price, 'volume_total': volume_total}   
                        
        # 纠错
        if bid_status:
            for i in bid_status.keys():
                error_id.append(i)
        if ask_status:
            for i in ask_status.keys():
                error_id.append(i) 
        if not error_id:
            break
    # print(error_id)
    
    return (spread_total/t_total if t_total > 0 else 0, t_total/(345*60), list(np.array(spread_split)/(345*60)), is_duty_list, is_duty_details)

#%%
# 计算市场的平均价宽, 义务时间
# 获取有效价格
def duty_market(dfMkt, spread = 5, vol = 10, tick = 1, exchange = 'ZCE'):    
    def get_askP(row, v):
        askP = np.inf
        v_total = 0
        askP_total = 0
        for i in range(1, 6):
            if row['Ask'+str(i)+'Volume'] == np.nan:
                askP = np.inf
                break
            v0 = v-v_total
            v_total += np.min([row['Ask'+str(i)+'Volume'], v-v_total])
            if v_total >= v:
                askP_total += row['Ask'+str(i)+'Price'] * v0
                break
            else:
                askP_total += row['Ask'+str(i)+'Price'] * row['Ask'+str(i)+'Volume']
        if v_total < v:
            askP = np.inf
        else:
            askP = askP_total/v
        return askP
        
    def get_bidP(row, v):
        bidP = 0
        v_total = 0
        bidP_total = 0
        for i in range(1, 6):
            if row['Bid'+str(i)+'Volume'] == np.nan:
                bidP = 0
                break
            v0 = v-v_total
            v_total += np.min([row['Bid'+str(i)+'Volume'], v-v_total])
            if v_total >= v:
                bidP_total += row['Bid'+str(i)+'Price'] * v0
                break
            else:
                bidP_total += row['Bid'+str(i)+'Price'] * row['Bid'+str(i)+'Volume']
        if v_total < v:
            bidP = 0
        else:
            bidP = bidP_total/v
        return bidP
    
    
    def get_spread(row, v):
        return get_askP(row, v) - get_bidP(row, v)
    
    is_duty_list = []
    
    total_time_m = 0
    total_spread_m = 0
    Spread_m = []
    t1 = 0
    row = dfMkt[0]
    spread = get_spread(row, vol)/tick
    
    
    
    # for i, row in dfMkt.iterrows():
    for row in dfMkt:
        t2 = row['UnixStamp']
        if t2 - t1 < 600:
            if spread <= spr:
                total_time_m += t2-t1
                total_spread_m += (t2-t1)*spread
                is_duty_list.append((datetime.datetime.utcfromtimestamp(t1), datetime.datetime.utcfromtimestamp(t2), t2-t1, True))
            if spread > spr:
                is_duty_list.append((datetime.datetime.utcfromtimestamp(t1), datetime.datetime.utcfromtimestamp(t2), t2-t1, False))
            Spread_m.append(spread)
        spread = get_spread(row, vol)/tick    
        t1 = t2
    spread_ave_m = total_spread_m/total_time_m
    t_ave_m = total_time_m/(345*60)
    return (spread_ave_m, t_ave_m, is_duty_list)
#%%
# 交易延时统计, 包含自动顶单的情况
def command_delay(df):
    if len(df) == 0:
        return
    bid_status = {} # 字典中的元素为以订单号internal_order_id为索引, 以row为数据
    ask_status = {}    
    pre_order_status = {}
    pre_cancel_status = {}
    pre_order_delay = []
    pre_cancel_delay = []
    for row in df:
        if row['typ'] == 'trade' or row['typ'] == 'all_trade':
            internal_order_id = row['internal_order_id']
            long_short = row['long_short']
            if long_short == 0:
                if internal_order_id in bid_status.keys():
                    if row['typ'] == 'trade':
                        bid_status[internal_order_id]['volume_total'] = row['volume']
                    elif row['typ'] == 'all_trade':
                        bid_status[internal_order_id]['volume_total'] -= row['volume']
                    if bid_status[internal_order_id]['volume_total'] == 0:
                        bid_status.pop(internal_order_id)
            if long_short == 1:
                if internal_order_id in ask_status.keys():
                    if row['typ'] == 'trade':
                        ask_status[internal_order_id]['volume_total'] = row['volume']
                    elif row['typ'] == 'all_trade':
                        ask_status[internal_order_id]['volume_total'] -= row['volume']
                    if ask_status[internal_order_id]['volume_total'] == 0:
                        ask_status.pop(internal_order_id)   
        if row['typ'] == 'cancel':
            internal_order_id = row['internal_order_id']
            long_short = row['long_short']
            if long_short == 0:
                if internal_order_id in bid_status.keys():
                    bid_status.pop(internal_order_id)
            if long_short == 1:
                if internal_order_id in ask_status.keys():
                    ask_status.pop(internal_order_id)  
        if row['typ'] == 'order' or row['typ'] == 'quote_order':
            internal_order_id = row['internal_order_id']
            long_short = row['long_short'] 
            if volume_total > 0:
                if long_short == 0:
                    if not internal_order_id in bid_status.keys():
                        bid_status[internal_order_id] = row
                if long_short == 1:
                    if not internal_order_id in ask_status.keys():
                        ask_status[internal_order_id] = row     
        if row['typ'] == 'order_pre_order' or row['typ'] == 'quote_pre_order':
            internal_order_id = row['internal_order_id']
            if not internal_order_id in pre_order_status.keys():
                pre_order_status[internal_order_id] = row
            bid_quote = exist_quote(bid_status)
            if internal_order_id in bid_status.keys():
                
            
    
    
    
    
#%%
date = '2021-09-24'
print('日期', date)
#%%
# DCE
parameter = pd.read_csv('C:/Users/<USER>/Desktop/shfe/data_csv/做市参数0802.csv', encoding = 'gbk')

result_duty_DCE = {'iid':[], '报价时间占比':[], '平均报价价差':[], '价差占比':[], '市场报价时间占比':[], '市场平均报价价差':[]}

    
    
for m, row in parameter.iterrows():
    # iid = 'c2111'
    iid = row.iid
    
    db_path = 'C:/Users/<USER>/Desktop/shfe/dce_future2_' + date + '/dce_future/'
    underlying = get_underlying(iid).upper()
    # if not (underlying == 'L' or underlying == 'PP'):
    #     continue
    
    testDB = OmmDatabase(db_path)
    
    # OrderService
    dfOC = testDB.read_file(db_path + 'OrderService/cancel/{}/'.format(underlying), date) 
    dfOO = testDB.read_file(db_path + 'OrderService/order/{}/'.format(underlying), date) 
    dfOT = testDB.read_file(db_path + 'OrderService/trade/{}/'.format(underlying), date) 
    
    # QuoteService
    dfQA = testDB.read_file(db_path + 'QuoteService/all_trade/{}/'.format(underlying), date) 
    dfQC = testDB.read_file(db_path + 'QuoteService/cancel/{}/'.format(underlying), date) 
    dfQO = testDB.read_file(db_path + 'QuoteService/order/{}/'.format(underlying), date) 
    
    # MarketDataService
    dfMkt = testDB.read_file(db_path + 'MarketDataService/default_mkt/{}/'.format(underlying), date)
    
    if dfOC is not None:
        dfOC = dfOC[dfOC.InstrumentId == iid].copy()
        dfOC['UnixStamp'] = dfOC['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOC.index = dfOC.UnixStamp
    if dfOO is not None:    
        dfOO = dfOO[dfOO.InstrumentId == iid].copy()
        dfOO['UnixStamp'] = dfOO['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOO.index = dfOO.UnixStamp
    if dfOT is not None:
        dfOT = dfOT[dfOT.InstrumentId == iid].copy()
        dfOT['UnixStamp'] = dfOT['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOT.index = dfOT.UnixStamp
    if dfQA is not None:
        dfQA = dfQA[dfQA.iid == iid].copy()
        dfQA['UnixStamp'] = dfQA['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfQA.index = dfQA.UnixStamp
    if dfQC is not None:
        dfQC = dfQC[dfQC.InstrumentId == iid].copy()
        dfQC['UnixStamp'] = dfQC['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfQC.index = dfQC.UnixStamp
    if dfQO is not None:
        dfQO = dfQO[dfQO.instrumentId == iid].copy()
        dfQO['UnixStamp'] = dfQO['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfQO.index = dfQO.UnixStamp
    if dfMkt is not None:
        dfMkt = dfMkt[dfMkt.instrumentId == iid].copy()
        dfMkt['UnixStamp'] = dfMkt['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfMkt.index = dfMkt.UnixStamp
    
    standard_dfOC = standardizing(dfOC, typ = 'OC')
    standard_dfOT = standardizing(dfOT, typ = 'OT')
    standard_dfOO = standardizing(dfOO, typ = 'OO')
    standard_dfQA = standardizing(dfQA, typ = 'QA')
    standard_dfQC = standardizing(dfQC, typ = 'QC')
    standard_dfQO = standardizing(dfQO, typ = 'QO')
    
    df_summary = pd.concat([standard_dfOC, standard_dfOT, standard_dfOO, standard_dfQA, standard_dfQC, standard_dfQO])
    
    df_summary = df_summary.sort_index()
    # 主计算    
    spr = row['最大买卖价差']
    valid_V = row['最小报价数量']
    valid_V_m = valid_V
    valid_V_m2 = valid_V*10
    tick = row['最小变动单位']
    
    result_duty_DCE['iid'].append(iid)
    df_summary = df_summary.to_dict('records')
    dfMkt = dfMkt.to_dict('records')
    
    spread_ave, t_ave, spread_split= duty_accumulate(df_summary, spread = spr, vol = valid_V, tick = tick, exchange = 'DCE')
    spread_ave_m, t_ave_m = duty_market(dfMkt, spread = spr, vol = valid_V_m, tick = tick, exchange = 'DCE')
    spread_ave_m2, t_ave_m2 = duty_market(dfMkt, spread = spr, vol = valid_V_m2, tick = tick, exchange = 'DCE')
    
    result_duty_DCE['报价时间占比'].append(t_ave)
    result_duty_DCE['平均报价价差'].append(spread_ave)
    result_duty_DCE['价差占比'].append(spread_ave/spr)
    result_duty_DCE['市场报价时间占比'].append(t_ave_m)
    result_duty_DCE['市场平均报价价差'].append(spread_ave_m)    
    
    print(iid+'真实时间占比', t_ave, '平均报价价差', spread_ave)
    print('市场第一时间占比', t_ave_m, '平均报价价差', spread_ave_m)
    print('市场前十时间占比', t_ave_m2, '平均报价价差', spread_ave_m2)
    if m >= 2:
        break


result_duty_DCE = pd.DataFrame(result_duty_DCE)    


#%%
# ZCE
date = '2021-09-24'
print('日期', date)
result_duty_ZCE = {'iid':[], '报价时间占比':[], '平均报价价差':[], '价差占比':[], '市场报价时间占比':[], '市场平均报价价差':[],\
                   '市场前十时间占比':[], '市场前十报价价差':[]}

is_duty_list0 = {}
is_duty_details0 = {}
is_duty_list_mkt0 = {}

zce_list = [('CF111', 5), ('RM111', 1), ('SR111', 1)]    
    
for iid, tick in zce_list:
    # iid = 'c2111'
    # iid = row.iid
    
    db_path = 'C:/Users/<USER>/Desktop/zce_future_' + date + '/zce_future/'
    underlying = get_underlying(iid).upper()
    testDB = OmmDatabase(db_path)
    
    # OrderService
    dfOC = testDB.read_file(db_path + 'OrderService/cancel/{}/'.format(underlying), date) 
    dfOO = testDB.read_file(db_path + 'OrderService/order/{}/'.format(underlying), date) 
    dfOT = testDB.read_file(db_path + 'OrderService/trade/{}/'.format(underlying), date) 
    
    # QuoteService
    dfQA = testDB.read_file(db_path + 'QuoteService/all_trade/{}/'.format(underlying), date) 
    dfQC = testDB.read_file(db_path + 'QuoteService/cancel/{}/'.format(underlying), date) 
    dfQO = testDB.read_file(db_path + 'QuoteService/order/{}/'.format(underlying), date) 
    
    # MarketDataService
    dfMkt = testDB.read_file(db_path + 'MarketDataService/default_mkt/{}/'.format(underlying), date)
    
    if dfOC is not None:
        dfOC = dfOC[dfOC.InstrumentId == iid].copy()
        dfOC['UnixStamp'] = dfOC['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOC.index = dfOC.UnixStamp
    if dfOO is not None:    
        dfOO = dfOO[dfOO.InstrumentId == iid].copy()
        dfOO['UnixStamp'] = dfOO['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOO.index = dfOO.UnixStamp
    if dfOT is not None:
        dfOT = dfOT[dfOT.InstrumentId == iid].copy()
        dfOT['UnixStamp'] = dfOT['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOT.index = dfOT.UnixStamp
    if dfQA is not None:
        dfQA = dfQA[dfQA.iid == iid].copy()
        dfQA['UnixStamp'] = dfQA['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfQA.index = dfQA.UnixStamp
    if dfQC is not None:
        dfQC = dfQC[dfQC.InstrumentId == iid].copy()
        dfQC['UnixStamp'] = dfQC['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfQC.index = dfQC.UnixStamp
    if dfQO is not None:
        dfQO = dfQO[dfQO.instrumentId == iid].copy()
        dfQO['UnixStamp'] = dfQO['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfQO.index = dfQO.UnixStamp
    if dfMkt is not None:
        dfMkt = dfMkt[dfMkt.instrumentId == iid].copy()
        dfMkt['UnixStamp'] = dfMkt['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfMkt.index = dfMkt.UnixStamp
    
    
    standard_dfOC = standardizing(dfOC, typ = 'OC')
    standard_dfOT = standardizing(dfOT, typ = 'OT')
    standard_dfOO = standardizing(dfOO, typ = 'OO')
    standard_dfQA = standardizing(dfQA, typ = 'QA')
    standard_dfQC = standardizing(dfQC, typ = 'QC')
    standard_dfQO = standardizing(dfQO, typ = 'QO')
    
    df_summary = pd.concat([standard_dfOC, standard_dfOT, standard_dfOO, standard_dfQA, standard_dfQC, standard_dfQO])
    
    df_summary = df_summary.sort_index()
    # 主计算    
    spr = 5
    valid_V = 10
    valid_V_m = valid_V
    valid_V_m2 = valid_V*10
    tick = tick
    
    result_duty_ZCE['iid'].append(iid)
    df_summary = df_summary.to_dict('records')
    dfMkt = dfMkt.to_dict('records')
    spread_ave, t_ave, spread_split, is_duty_list, is_duty_details = duty_accumulate(df_summary, spread = spr, vol = valid_V, tick = tick, exchange = 'ZCE')
    spread_ave_m, t_ave_m, is_duty_list_mkt = duty_market(dfMkt, spread = spr, vol = valid_V_m, tick = tick, exchange = 'ZCE')
    spread_ave_m2, t_ave_m2, is_duty_list_mkt2 = duty_market(dfMkt, spread = spr, vol = valid_V_m2, tick = tick, exchange = 'ZCE')
    
    
    result_duty_ZCE['报价时间占比'].append(t_ave)
    result_duty_ZCE['平均报价价差'].append(spread_ave)
    result_duty_ZCE['价差占比'].append(spread_ave/spr)
    result_duty_ZCE['市场报价时间占比'].append(t_ave_m)
    result_duty_ZCE['市场平均报价价差'].append(spread_ave_m)    
    result_duty_ZCE['市场前十时间占比'].append(t_ave_m2)
    result_duty_ZCE['市场前十报价价差'].append(spread_ave_m2) 
    
    is_duty_list0[iid] = is_duty_list
    is_duty_details0[iid] = is_duty_details
    is_duty_list_mkt0[iid] = is_duty_list_mkt2
    
    print(iid+'真实时间占比', t_ave, '平均报价价差', spread_ave, '分段报价时间:', spread_split)
    print('市场真实时间占比', t_ave_m, '平均报价价差', spread_ave_m)
    print('市场前十时间占比', t_ave_m2, '平均报价价差', spread_ave_m2)
    # if m >= 2:        
    #     break

#%%
import datetime
datetime.datetime.utcfromtimestamp(1600000000)

is_duty_list1 = {}
is_duty_list2 = {}
for iid in is_duty_list0.keys():
    i = is_duty_list0[iid]
    i0 = []
    i0.append((datetime.datetime.utcfromtimestamp(i[0][0]), i[0][1]))
    j0 = i[0]
    
    for j in i[1:]:
        if j[1] != j0[1]:
            i0.append((datetime.datetime.utcfromtimestamp(j0[0]), j0[1]))
        j0=j
    i1 = []
    for j in range(len(i0)-1):
        if i0[j][1] == True and i0[j+1][1] == False:
            i1.append((i0[j][0], i0[j+1][0], i0[j+1][0]-i0[j][0]))
    is_duty_list2[iid] = pd.DataFrame(i0)        
    is_duty_list1[iid] = pd.DataFrame(i1)

#%%
is_duty_details1 = {}
for iid in is_duty_details0.keys():
    i = is_duty_details0[iid]
    i0 = []
    for j in i:
        i0.append((datetime.datetime.utcfromtimestamp(j[0]), j[1], j[2]))
    is_duty_details1[iid] = i0





#%%    
CFtest = is_duty_list1['CF111']
CFtest[2].loc[CFtest[2] < datetime.timedelta(minutes=15)].sum()
    
        

     
            


















