import numpy as np
import pandas as pd
import warnings
import matplotlib.pyplot as plt
import matplotlib
import scipy.stats as stats
import scipy.optimize as opt
from datetime import datetime, timedelta, time
import math
matplotlib.use('TkAgg')
warnings.filterwarnings("ignore")

# 分段时间段定义（8个时段，每个约30分钟）
TRADING_PERIODS = [
    (time(9, 30), time(10, 0)),    # 开盘前30分钟
    (time(10, 0), time(10, 30)),
    (time(10, 30), time(11, 0)),
    (time(11, 0), time(11, 30)),   # 上午收盘前30分钟
    (time(13, 0), time(13, 30)),   # 下午开盘前30分钟
    (time(13, 30), time(14, 0)),
    (time(14, 0), time(14, 30)),
    (time(14, 30), time(14, 57))   # 收盘前27分钟
]
N_PERIODS = len(TRADING_PERIODS)
DELTA = 0.001  # 最小价格变动单位
initial_inventory = 0  # 初始库存水平设置为0
initial_cash = 0  # 初始现金水平设置
initial_wealth = initial_cash


def assign_period(t):
    """为时间分配时间段索引（0-7）"""
    for i, (start, end) in enumerate(TRADING_PERIODS):
        if start <= t < end:
            return i
    # 处理边界情况（收盘时间）
    if t >= time(14, 57):
        return N_PERIODS - 1
    return None


def Lee_Ready_Direction(df_tick):
    df_tick = df_tick.drop_duplicates()
    df_tick['Trade_Direction'] = np.nan
    df_tick['Trade_Volume'] = df_tick['Volume'].diff()
    df_tick['Trade_Volume'].iloc[0] = df_tick['Volume'].iloc[0]
    df_tick['Trade_Value'] = df_tick['TotalValueTraded'].diff()
    df_tick['Trade_Value'].iloc[0] = df_tick['TotalValueTraded'].iloc[0]

    df_tick['AvgTradePrice'] = df_tick['Trade_Value']/df_tick['Trade_Volume']
    # 检查条件并更新列的值
    mask_1 = (df_tick['AvgTradePrice'] > (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2)
    mask_2 = (df_tick['AvgTradePrice'] < (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2)
    mask_3 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (df_tick['AvgTradePrice'] > df_tick['AvgTradePrice'].shift(1))
    mask_4 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (df_tick['AvgTradePrice'] < df_tick['AvgTradePrice'].shift(1))
    mask_5 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (df_tick['AvgTradePrice'] == df_tick['AvgTradePrice'].shift(1))

    df_tick.loc[mask_1, 'Trade_Direction'] = 1
    df_tick.loc[mask_2, 'Trade_Direction'] = -1
    df_tick.loc[mask_3, 'Trade_Direction'] = 1
    df_tick.loc[mask_4, 'Trade_Direction'] = -1

    #df_tick.loc[mask_5, 'Trade_Direction'] = df_tick['Trade_Direction'].shift(1)
    # 修复 mask_5 的赋值逻辑：每行独立向前查找最近的非NaN值
    if mask_5.any():  # 确保有满足条件的行
        # 对每一行满足mask_5的位置，向前查找最近的非NaN值
        for idx in df_tick[mask_5].index:
            if idx > 0:  # 跳过第一行，因为无法向前查找
                prev_values = df_tick.loc[:idx-1, 'Trade_Direction']
                if not prev_values.dropna().empty:
                    df_tick.loc[idx, 'Trade_Direction'] = prev_values.dropna().iloc[-1]

    return df_tick


def calculate_n_second_stats(etf_trade_data, n):
    # 将时间转换为可以计算的datetime对象
    etf_trade_data['timestamp'] = pd.to_datetime('2025-06-03 ' + etf_trade_data['timestamp_str'].astype(str))
    start_time = pd.to_datetime('2025-06-03 09:30:00')
    end_time = etf_trade_data['timestamp'].max()

    time_intervals = pd.date_range(start=start_time, end=end_time, freq=f'{n}S')
    result = []

    for i in range(len(time_intervals) - 1):
        start = time_intervals[i]
        end = time_intervals[i + 1]
        interval_data = etf_trade_data[(etf_trade_data['timestamp'] >= start) & (etf_trade_data['timestamp'] < end)]

        if not interval_data.empty:
            first_row = interval_data.iloc[0]
            bid_price_1 = first_row['BidPrice1']
            bid_vol_1 = first_row['BidVol1']
            ask_price_1 = first_row['AskPrice1']
            ask_vol_1 = first_row['AskVol1']
            spread = first_row['spread']
            mid_price = (bid_price_1 + ask_price_1)/2
            bid_higher = first_row['BidPrice1'] + DELTA
            ask_lower = first_row['AskPrice1'] - DELTA

            # 统计Trade_Direction为-1且AvgTradePrice小于等于BidPrice1的Trade_Volume总和
            sell_volume = interval_data[(interval_data['Trade_Direction'] == -1) & (interval_data['AvgTradePrice'] <= bid_price_1)]['Trade_Volume'].sum()
            sell_higher_volume = interval_data[(interval_data['Trade_Direction'] == -1) & (interval_data['AvgTradePrice'] <= bid_higher)]['Trade_Volume'].sum()
            # 统计Trade_Direction为1且AvgTradePrice大于等于AskPrice1的Trade_Volume总和
            buy_volume = interval_data[(interval_data['Trade_Direction'] == 1) & (interval_data['AvgTradePrice'] >= ask_price_1)]['Trade_Volume'].sum()
            buy_lower_volume = interval_data[(interval_data['Trade_Direction'] == 1) & (interval_data['AvgTradePrice'] >= ask_lower)]['Trade_Volume'].sum()

            result.append({
                'time_interval_start': start.time(),
                'sell_volume': sell_volume,
                'buy_volume': buy_volume,
                'sell_higher_volume': sell_higher_volume,
                'buy_lower_volume': buy_lower_volume,
                'BidPrice1': bid_price_1,
                'BidVol1': bid_vol_1,
                'AskPrice1': ask_price_1,
                'AskVol1': ask_vol_1,
                'spread': spread,
                'mid_price': mid_price,
                'Bid_Higher': bid_higher,
                'Ask_Lower': ask_lower
            })

    result_df = pd.DataFrame(result)
    return result_df


def backtesting_trade_main(file_path,n_seconds=15):
    # 读取数据
    trade_path = 'E:\\Internship\\huatai_fin_inno\\Optimal_Market_Making\\data\\ETF_trade\\'
    trade_date = '20250624'
    etf_trade_data = pd.read_parquet(trade_path + 'md_' + trade_date + '_udp_receiver_1_50072.parquet')
    # 转换时间格式并过滤
    etf_trade_data['timestamp_str'] = pd.to_datetime(etf_trade_data['timestamp_str'], format='%H:%M:%S.%f').dt.time
    # 只保留交易时段数据
    time_condition = False
    for start, end in TRADING_PERIODS:
        time_condition |= ((etf_trade_data['timestamp_str'] >= start) &(etf_trade_data['timestamp_str'] < end))
    etf_trade_data = etf_trade_data[time_condition].reset_index(drop=True)
    # 计算中间价和价差
    etf_trade_data['mid_price'] = (etf_trade_data['AskPrice1'] + etf_trade_data['BidPrice1']) / 2
    etf_trade_data = Lee_Ready_Direction(etf_trade_data)
    etf_trade_data['spread'] = etf_trade_data['AskPrice1'] - etf_trade_data['BidPrice1']
    etf_trade_data['spread'] = etf_trade_data['spread'].round(4)
    etf_trade_data['spread_chg'] = etf_trade_data['spread'].diff()
    etf_trade_data['spread_chg'].iloc[0] = etf_trade_data['spread'].iloc[0]

    #print(etf_trade_data)
    #print(etf_trade_data.columns)
    #print("Data loaded and preprocessed")
    #print(f"Total records: {len(etf_trade_data)}")

    result_df = calculate_n_second_stats(etf_trade_data, n_seconds)

    # 初始化
    # 每个区间开始时的库存和现金
    result_df['inventory_at_first'] = np.nan
    result_df['inventory_at_first'].iloc[0] = initial_inventory

    result_df['cash_at_first'] = np.nan
    result_df['cash_at_first'].iloc[0] = initial_cash

    result_df['wealth_at_first'] = np.nan
    result_df['wealth_at_first'].iloc[0] = initial_wealth

    market_making_strategy = np.load(file_path)

    y_grid = market_making_strategy['y_grid']
    t_grid = market_making_strategy['t_grid']
    spreads = market_making_strategy['spreads']
    lb = market_making_strategy['lb']
    qb = market_making_strategy['qb']
    la = market_making_strategy['la']
    qa = market_making_strategy['qa']
    ea = market_making_strategy['ea']

    for index,row in result_df.iterrows():
        current_spread = result_df.at[index, 'spread']
        current_inventory = result_df.at[index, 'inventory_at_first']
        # 找到目前存货水平和价差状态的索引
        y_idx = np.argmin(np.abs(y_grid - current_inventory))
        s_idx = np.argmin(np.abs(np.array(spreads) * 0.001 - current_spread))

        # 获取限价买单挂单位置
        current_bid_signal = qb[y_idx, index, s_idx]
        if current_bid_signal == 0:
            current_bid_price = row['BidPrice1']
        else:
            current_bid_price = row['Bid_Higher']

        # 获取限价买单挂单量
        current_bid_volume = lb[y_idx, index, s_idx]

        # 获取限价卖单挂单位置
        current_ask_signal = qa[y_idx, index, s_idx]
        if current_ask_signal == 0:
            current_ask_price = row['AskPrice1']
        else:
            current_ask_price = row['Ask_Lower']

        # 获取限价卖单挂单量
        current_ask_volume = la[y_idx, index, s_idx]

        # 获取市价单挂单量
        current_market_volume = ea[y_idx, index, s_idx]

        # 判断限价买单成交情况
        if current_bid_signal == 0:
            if row['BidVol1'] + current_bid_volume - row['sell_volume'] <= 0:
                trade_volume_bid = current_bid_volume
            else:
                if row['BidVol1'] - row['sell_volume'] <= 0:
                    trade_volume_bid = row['sell_volume'] - row['BidVol1']
                else:
                    trade_volume_bid = 0
        else:
            if current_bid_volume - row['sell_higher_volume'] <= 0:
                trade_volume_bid = current_bid_volume
            else:
                trade_volume_bid = row['sell_higher_volume']

        # 判断限价卖单成交情况
        if current_ask_signal == 0:
            if row['AskVol1'] + current_ask_volume - row['buy_volume'] <= 0:
                trade_volume_ask = current_ask_volume
            else:
                if row['AskVol1'] - row['buy_volume'] <= 0:
                    trade_volume_ask = row['buy_volume'] - row['AskVol1']
                else:
                    trade_volume_ask = 0
        else:
            if current_ask_volume - row['buy_lower_volume'] <= 0:
                trade_volume_ask = current_ask_volume
            else:
                trade_volume_ask = row['buy_lower_volume']

        # 判断限价买单支出
        limit_buy_cost = round(trade_volume_bid) * current_bid_price
        # 判断限价卖单收入
        limit_sell_revenue = round(trade_volume_ask) * current_ask_price
        # 判断市价单收支
        if current_market_volume < 0:
            #trade_volume_market = min(np.abs(current_market_volume), row['BidVol1'])
            #market_pnl = trade_volume_market * row['BidPrice1']
            market_pnl = round(current_market_volume) * row['BidPrice1']
        else:
            #trade_volume_market = min(np.abs(current_market_volume), row['AskVol1'])
            #market_pnl = -1*trade_volume_market * row['AskPrice1']
            market_pnl = round(current_market_volume) * row['AskPrice1']
        # 计算现金收支变化
        cash_chg = limit_sell_revenue - limit_buy_cost - market_pnl
        # 计算库存增减变化
        inventory_chg = round(trade_volume_bid) - round(trade_volume_ask) + round(current_market_volume)
        #print(index,round(trade_volume_bid), round(trade_volume_ask), round(current_market_volume),inventory_chg)
        print(inventory_chg, current_inventory, y_idx, current_spread, s_idx)

        # 更新现金和库存状况
        result_df.at[index+1,'inventory_at_first'] = result_df.at[index,'inventory_at_first'] + inventory_chg
        result_df.at[index+1, 'cash_at_first'] = result_df.at[index, 'cash_at_first'] + cash_chg
        result_df.at[index+1, 'wealth_at_first'] = result_df.at[index+1, 'cash_at_first'] + result_df.at[index+1,'inventory_at_first']*result_df.at[index+1,'mid_price']
        #print(index, current_spread,current_bid_signal)


    plt.figure(figsize=(15, 6))
    plt.plot(list(result_df.index),result_df['inventory_at_first'] * result_df['mid_price'],color='blue',label='inventory')
    plt.show()

    plt.figure(figsize=(15, 6))
    plt.plot(list(result_df.index),result_df['cash_at_first'],color='red',label='cash')
    plt.show()

    plt.figure(figsize=(15, 6))
    plt.plot(list(result_df.index),result_df['wealth_at_first'],color='black',label='wealth')
    plt.show()

    return result_df

if __name__ == '__main__':

    file_path = 'E:\\Internship\\huatai_fin_inno\\Optimal_Market_Making\\GP_model\\Advanced_GP\\results\\market_making_strategy_20250603.npz'
    result_df = backtesting_trade_main(file_path)
    print(result_df)
    print(result_df.columns)
