SVI Volatility Surface
---
The SVI model introduced by <PERSON><PERSON> and <PERSON><PERSON>.   
   
### Content
This repo include:
+ SVI model
+ An improved Quasi-Explicit model
+ China 50ETF option calibraiton
+ Butterfly arbitrage check
+ Calendar arbitrage check
  
### Dependency
+ Python 3.6+
+ Numpy
+ Scipy
+ Pandas
+ Matplotlib
  
## Reference
[1] <PERSON><PERSON>. A parsimonious arbitrage-free implied volatility parameterization with application to the valuation of volatility derivatives. Global Derivatives & Risk  
[2] Zeliade Systems, Quasi-explicit calibration of Gatheral's SVI model, Zeliade white paper, 2009.  
[3] Gatheral J. Lecture 2: The SVI arbitrage-free volatility surface parameterization. CFM-Imperial Distinguished Lecture Series, 2015.  
