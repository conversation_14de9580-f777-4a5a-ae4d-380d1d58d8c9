# -*- coding: utf-8 -*-

import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random

class BasisStrategy(StrategyTemplate):
    """"""

    author = "vnpy"

    buy_price = 0
    short_price = 0

    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'edge',
        'alpha',
        'gamma'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]

    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net=0
        self.fair_maker = 0
        self.fair_refer = 0
        self.rCount=0
        self.pauseCount=0
        self.lastRefer={"net":0, "tick":None, "timepoint":0}        
        self.lastMaker={"net":0, "tick":None, "timepoint":0}
        self.isQuoting=False
        self.lastTicks={self.maker:0,self.refer:0}
        self.comments  = None
        self.last_bidP = 0
        self.last_askP = 0
        self.ewma = 0
        self.Basis = 0
        self.pauseCount=0
        self.isQuoting = True
        self.offer_list = []


    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.maker:
                    self.on_tick_maker(ticks[key])
                elif key==self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick
        
    def Validtick(self,tick): #无效的tick不计算ewma
        if tick.ask_price_1 - tick.bid_price_1 > self.edge:
            return False
        
        return True
        
        
    def on_tick_refer(self,tick):
        self.rCount +=1  

        ts = self.priceticks[self.refer]
        net = self.net  
            
        self.fair_refer = (tick.bid_price_1 * tick.ask_volume_1 + tick.ask_price_1 * tick.bid_volume_1) / (tick.bid_volume_1 + tick.ask_volume_1) 
        
        shortResendFlag=False
        buyResendFlag=False
        
        # if self.buy_hedge_orderids or self.short_hedge_orderids:
        #     self.cancel_all()
        #     self.buy_hedge_orderids = []
        #     self.short_hedge_orderids = [] 
        
        # if self.net > 0:
        #     self.short_hedge_orderids = self.short(self.refer,tick.bid_price_1, abs(self.net),'referHedge')
        # if self.net < 0:
        #     self.buy_hedge_orderids = self.buy(self.refer,tick.ask_price_1, abs(self.net),'referHedge')        
        
        #1. Filter ：非可控情景暂停 
        # if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
        #     # 持仓超限暂停 、在途订单超限暂停、
        #     if abs(net)>self.maxPos:
        #         self.isQuoting=False 
        #         self.pauseCount=self.rCount+20      
        #         print("Net Position limit pause",tick.datetime+timedelta(hours=8))
        #     if len(self.strategy_engine.active_limit_orders)>10:     #在途订单超限暂停 or mdDelay>mdDelayLimit or tdDelay>tdDelayLimit 
        #         self.isQuoting=False 
        #         self.pauseCount=self.rCount+10    
        #         print("Delay limit pause",tick.datetime+timedelta(hours=8))
        #     # market cross pause
        #     if self.rCount>5 and (tick.ask_price_1 <= self.lastRefer["tick"].bid_price_1 -ts or tick.bid_price_1 >= self.lastRefer["tick"].ask_price_1 +ts):
        #         self.isQuoting=False 
        #         self.pauseCount=self.rCount+20    
        #         print("maker gap limit pause",tick.datetime+timedelta(hours=8)) #   
        #     # near to market limit price  
        #     if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (
        #         tick.bid_price_1 > float(tick.limit_up) - 10*ts or  # 涨停附近                   
        #         tick.ask_price_1 < float(tick.limit_down) + 10*ts):   # 跌停附近                  
        #             self.isQuoting=False
        #             self.pauseCount=self.rCount + 100
        #             print("price limit pause", tick.datetime+timedelta(hours=8))
        # else:
        #     if self.pauseCount<self.rCount:
        #         self.isQuoting=True 
                
        # 2. Quote：报价

        if self.rCount > 10 and self.isValid: #启动后10个tick开始定价,对有效的tick进行定价
            self.Basis = self.fair_maker - self.fair_refer
            if self.ewma == 0 :
                self.ewma = self.Basis
            else:
                self.ewma = (1-self.alpha) * self.ewma + self.alpha * self.Basis
        
        if self.rCount > 10+1/self.alpha:  #开始报价 （若alpha = 0.02,则用50个tick计算初始Basis）
        
            self.theto = self.fair_refer + self.ewma
            theto = self.theto
            pos_adjust = -self.gamma*(self.get_pos(self.maker)/self.lots)*ts   
            self.bidP = theto - 0.25*self.edge*ts + pos_adjust
            self.askP = theto + 0.25*self.edge*ts + pos_adjust
            self.bidP = ts*round(self.bidP/ts) 
            self.askP = ts*round(self.askP/ts)  

            if self.last_askP != self.askP:
                shortResendFlag=True
            if self.last_bidP != self.bidP:
                buyResendFlag=True

            self.last_bidP = self.bidP
            self.last_askP = self.askP
            
            self.comments = 'MM'
        
        dt = tick.datetime

        if (
            (dt.time() > dtime(13, 5) and dt.time() < dtime(14, 55))
            or (dt.time() > dtime(1, 5) and dt.time() < dtime(2, 10))
            or (dt.time() > dtime(2, 35) and dt.time() < dtime(3, 25))
            or (dt.time() > dtime(5, 35) and dt.time() < dtime(6, 55))
        ) and self.isValid: #若流动性差 或者刚开盘和收盘 不报价

            if self.net < self.maxPos:
                if not self.buy_vt_orderids:
                        self.buy_vt_orderids = self.buy(self.maker,self.bidP, self.lots,self.comments)
                elif buyResendFlag and self.buy_vt_orderids[0] :
                        if self.cancel_order(self.buy_vt_orderids[0]):  #cancel完会跳转至onOrder，然后自动发一个单
                            self.buy_vt_orderids = self.buy(self.maker,self.bidP, self.lots,self.comments)
            if self.net > -self.maxPos:      
                if not self.short_vt_orderids:
                        self.short_vt_orderids = self.short(self.maker,self.askP, self.lots,self.comments)
                elif shortResendFlag and self.short_vt_orderids[0] :
                        if self.cancel_order(self.short_vt_orderids[0]): #cancel完会跳转至onOrder，然后自动发一个单
                            self.short_vt_orderids = self.short(self.maker,self.askP, self.lots,self.comments)
    
        # 3. Save：数据存储                    
                
        self.lastRefer["timepoint"]=tick.datetime+timedelta(hours=8)
        self.lastRefer["net"]=self.net
        self.lastRefer["tick"]=tick
            
    def on_tick_maker(self, tick):

        ts = self.priceticks[self.maker]
        multiplier =  self.sizes[self.maker]
        net = self.net
        lots = self.lots
        maxV=50
        
        if self.buy_hedge_orderids or self.short_hedge_orderids:
            self.cancel_all()
            self.buy_hedge_orderids = []
            self.short_hedge_orderids = [] 
        
        self.isValid = self.Validtick(tick)
        
        if tick.datetime.time() > dtime(6, 55) and tick.datetime.time() < dtime(6, 56): 
            if self.get_pos(self.maker)!= 0:  
                if self.get_pos(self.maker) >0:
                    self.short_hedge_orderids = self.short(self.maker,tick.bid_price_1,abs(self.net),'hedge')
                else:
                    self.buy_hedge_orderids = self.buy(self.maker,tick.ask_price_1,abs(self.net),'hedge')
                
        # #### Algo : JoinMarket  ~~~~~~~无效价格情况暂未处理~~~~~~~~~~~~~~~~~~ 
        spread = tick.ask_price_1 - tick.bid_price_1       
        self.fair_maker = self.getFairPrice(self.fair_maker,tick.ask_price_1,tick.bid_price_1, 3) 
        if self.rCount > 10+1/self.alpha: 
            self.offer_list.append([tick.datetime,self.theto,self.bidP,self.askP,self.fair_refer,self.ewma,self.Basis])
        
    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if volume > 0:
            if trade.direction.value=='多':
                if self.net>=0 :            
                     self.avg = (self.net*self.avg +volume*price)/(self.net+volume)              
                elif volume+self.net>0: # net<0 # 平仓
                     self.avg = price         
                self.net += volume 
            #         
            elif trade.direction.value=='空':    
                if self.net<=0:
                    self.avg =(-self.net*self.avg + volume*price)/(-self.net+volume)
                elif volume-self.net>0: # net >0 # 平仓
                    self.avg=price
                self.net -= volume  
                
    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """ # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if not(
            (dt.time() > dtime(13, 5) and dt.time() < dtime(14, 56))
            or (dt.time() > dtime(1, 5) and dt.time() < dtime(2, 10))
            or (dt.time() > dtime(2, 35) and dt.time() < dtime(3, 25))
            or (dt.time() > dtime(5, 35) and dt.time() < dtime(6, 56))
        ):
            self.cancel_all()
