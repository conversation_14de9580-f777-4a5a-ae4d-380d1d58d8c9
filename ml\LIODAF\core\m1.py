"""
机器学习分析框架
@author: lining
"""
import os
import sys
import pandas as pd
from tqdm import tqdm
import re
import time

sys.path.insert(0, sys.path[0]+"/../")

from data.data_loader import OrderBookDataLoader
from data.features.main import FactorSystem
from models.model_trainer import OrderBookModelTrainer
from utils.visualizer import OrderBookVisualizer
from core import config
from data.labels.cal_labels import LabelGenerator
from utils.utils import log_print, search_days, clear_log
from data.data_loader import split_normalize_data
from models.model_evaluate import model_evaluate
from data.features.factors_for_use import use_features
from backtests.backtester_ss import OrderBookBacktester


class M1:
    daysnum = config.DAYSNUM # 数据天数
    test_size = config.TEST_SIZE # 测试集比例
    model_selection = False
    is_model_parameter_search = False
    is_factor_test = True
    is_shap = True
    
    is_sub_contract = config.IS_SUB_CONTRACT

    # 设置滚动窗口大小
    max_data = config.ROLLING_WINDOW_SIZE + 20000 if config.BACKMODE == 'rolling' else 1000000000000
    
    target_cols = config.TARGET_COLS
    output_dir = config.OUTDIR
    selected_features = use_features

    def __init__(self):
        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        self.mix_df = None
        self.sub_df = None
        self.feature_cols = None
        self.results = None
        self.trade_report = None
        self.days = None
        self.train_data = {}
        self.y_train_dict = {}
        self.test_data = {}
        self.y_test_dict = {}
        self.train_pred = None
        self.test_pred = None
        self.all_predictions = None
        self.train_pred_quantile_dict = None
        self.factor_test_results = None
        self.factor_doc_df = None


    def process_data(self, codes=None):
        if config.BACKMODE == 'days':
            self.days = search_days(config.DATE_STR, self.daysnum, config.DATA_PATH)
        else:
            self.days = [config.DATE_STR]
        df = pd.DataFrame()
        i = 0
        for day in self.days:
            try:
                # 加载数据
                data_loader = OrderBookDataLoader(data_path=config.DATA_PATH,  # 使用实际路径
                                                code_list=[],
                                                date_str=day,
                                                time_range=config.TIME_RANGE)

                data_loader.load_data()
                # 提取合约中的数字部分
                exp = data_loader.data_md['Symbol'].str.extract(r'(\d+)')

                if not re.search(r'\d+$', codes[0]):
                    # 找到最小的数字，作为主力合约（后续需要处理主力合约到期的情况）
                    codeslist = [codes[0] + str(int(exp.min().iloc[0])), ]
                else:
                    codeslist = codes
                data = data_loader.filtor(codeslist, day, config.TIME_RANGE)[:self.max_data]
                # 给最后100条数据添加待删除标记
                data['delete'] = False
                data.loc[data.index[-100:], 'delete'] = True
                i += 1
                log_print(f"数据加载完成{day}_{codeslist[0]}，共 {len(data)} 条记录, 完成{i}/{len(self.days)}")
                df = pd.concat([df, data])
            except Exception as e:
                log_print(f"数据加载失败{day}_{codeslist[0]}，{e}")
                continue
        return df

    def generate_features_and_labels(self):
        # 生成特征和特征列表
        self.factor_system = FactorSystem()
        features_df, self.feature_cols, self.all_features = self.factor_system.generate_features(self.mix_df, self.selected_features, self.target_cols)

        if self.is_sub_contract and self.sub_df is not None:
            log_print("生成子合约特征...")
            sub_features_df, self.sub_feature_cols, self.sub_all_features = self.factor_system.generate_features(self.sub_df, self.selected_features, self.target_cols)
            rename_dict = {col: f'{col}_sub' for col in self.sub_feature_cols}
            sub_features_df = sub_features_df.rename(columns=rename_dict)
            self.sub_features_cols = [f'{col}_sub' for col in self.sub_feature_cols]
            self.sub_selected_features = [f'{col}_sub' for col in self.selected_features]
            self.sub_all_features = {f'{col}_sub': self.sub_all_features[col] for col in self.sub_feature_cols}
            self.all_features.update(self.sub_all_features)

            features_df = pd.merge_asof(features_df, sub_features_df[self.sub_features_cols], left_index=True, right_index=True, direction='backward', tolerance=pd.Timedelta('1000ms'))
            self.feature_cols = self.feature_cols + self.sub_features_cols
            self.selected_features = self.selected_features + self.sub_selected_features
        else:
            log_print("不生成子合约特征...")

        # 生成标签
        label_generator = LabelGenerator(self.mix_df, horizons=config.EVALUATE_HORIZONS)
        label_df = label_generator.generate_all_labels()

        # 合并特征和标签数据
        df = pd.concat([features_df, label_df], axis=1)
        df = df.loc[:, ~df.columns.duplicated()]  # 删除重复列
        # 删除待删除标记
        self.mix_df = df[~df['delete']]

        # 非滚动窗口模式下，直接分割数据，标准化数据，返回训练集和测试集
        if config.BACKMODE != 'rolling':
            self.train_data, self.test_data, self.y_train_dict, self.y_test_dict, self.scalers = split_normalize_data(self.mix_df,self.feature_cols,config.SCALER,test_size=self.test_size)

    def model_selection(self, x_train, y_train):
        model_trainer = OrderBookModelTrainer(self.feature_cols, self.target_cols)
        model_performances, self.model_type = model_trainer.model_selection(x_train, y_train)

    def train_model(self):
        time_start = time.time()
        if config.BACKMODE == 'rolling':
            # 初始化模型训练器和存储预测结果的DataFrame
            all_predictions = pd.DataFrame(index=self.mix_df.index)
            # 确保有足够的数据进行初始训练
            min_train_size = config.ROLLING_WINDOW_SIZE
            # 训练模型并存储每次训练结果
            log_print(f"开始滚动窗口训练，窗口大小: {config.ROLLING_WINDOW_SIZE}")
            for i in tqdm(range(min_train_size, len(self.mix_df)), desc="滚动窗口训练进度"):
                # 从数据开始到当前索引的数据作为训练集(排除前pred_num条数据)
                self.train_data, self.test_data, self.y_train_dict, self.y_test_dict = split_normalize_data(
                    self.mix_df[i - config.ROLLING_WINDOW_SIZE:i - config.PREDICTION_HORIZONS + 1], self.feature_cols,
                    self.target_cols, config.BACKMODE, config.SCALER)

                # 设置训练数据
                model_trainer = OrderBookModelTrainer(self.feature_cols, self.target_cols)
                # 训练模型
                model_trainer.train_models(self.train_data, self.y_train_dict, model_type=self.model_type,
                                           params=config.MODEL_SETTING[self.model_type]['params'])
                # 预测当前索引对应的数据点
                pred = model_trainer.predict(self.test_data)
                # 将预测结果添加到整体预测结果中
                all_predictions.loc[self.test_data.index, self.target_cols[0] + '_pred'] = pred.iloc[0, 0]

            if len(all_predictions.dropna()) - config.ROLLING_WINDOW_SIZE > 0:
                log_print(
                    f"滚动窗口预测完成，共生成 {len(all_predictions.dropna()) - config.ROLLING_WINDOW_SIZE} 条预测")

        else:
            model_trainer = OrderBookModelTrainer(self.feature_cols, self.target_cols, model_type=config.MODEL_TYPE)
            # 训练模型
            model_trainer.train_models(self.train_data, self.y_train_dict, 
                                       params=config.MODEL_SETTING[config.MODEL_TYPE]['params'], is_model_parameter_search=self.is_model_parameter_search)
            train_time = time.time()
            # 预测
            self.train_pred = model_trainer.predict(self.train_data[self.target_cols[0]])
            train_pred_time = time.time()
            self.test_pred = model_trainer.predict(self.test_data[self.target_cols[0]])
            test_pred_time = time.time()
            model_trainer.predict(self.test_data[self.target_cols[0]][-1:])
            one_pred_time = time.time()
            self.all_predictions = pd.concat([self.train_pred, self.test_pred], axis=0)
            self.train_pred_quantile_dict = {i:float(self.train_pred.abs().quantile(i).values[0]) for i in config.QUANTILE_LIST}
            log_print(
                f"训练时间: {(train_time-time_start)*1000:.3f}ms, "
                f"预测时间: {(train_pred_time-train_time)*1000:.3f}ms, "
                f"{(test_pred_time-train_pred_time)*1000:.3f}ms, "
                f"单条预测时间: {(one_pred_time-test_pred_time)*1000:.3f}ms. " +
                ". ".join([f"{i}:{v:.2e}" for i, v in self.train_pred_quantile_dict.items()])
            )

        self.model_trainer = model_trainer


    def save_results(self):
        # 可视化
        log_print("可视化...")
        visualizer = OrderBookVisualizer(use_chinese=True)
        # 回测结果可视化
        visualizer.plot_backtest_results(self.results)
        # 绘制特征重要性
        least_important_features,top_important_features = visualizer.plot_feature_importance(self.model_trainer.models[config.TARGET_COLS[0]], self.feature_cols)
        # 绘制特征相关性
        highlight_features = visualizer.plot_feature_correlation(self.test_data, self.y_test_dict, features=self.feature_cols,
                                            target=config.TARGET_COLS[0])
        
        # 绘制因子测试结果
        iclow=visualizer.plot_IC_IR_win_rate(self.factor_test_results)

        # 取交集
        common_features = list(set(least_important_features) & set(iclow['feature']))
        log_print(f"least_10、ic<0.02的特征: {common_features}")
        log_print(f"相关性>0.7的特征: {highlight_features}")

        # 绘制交易分析
        visualizer.plot_trade_analysis(self.trade_report, self.results['return'], standardize_qq=True, title=None)
    
        if self.is_shap:
            # 绘制SHAP重要性条形图
            visualizer.plot_shap_summary(self.shap_values['importance'])

            # 影响最小的10个特征
            least_important_features = self.shap_values['importance'][:10]['feature'].tolist()
            log_print(f"影响最小的10个特征: {least_important_features}")

            # 绘制SHAP摘要图
            visualizer.plot_shap_values(self.shap_values)


        # 保存结果
        log_print("保存结果...")
        self.mix_df.to_csv(os.path.join(self.output_dir, 'features.csv'), index=True, encoding='utf-8')
        self.results.to_csv(os.path.join(self.output_dir, 'backtest_results.csv'), index=True, encoding='utf-8')
        if len(self.trade_report) > 0:
            self.trade_report.to_csv(os.path.join(self.output_dir, 'trade_report.csv'), index=True, encoding='utf-8')


def main():
    clear_log()
    m1 = M1()
    m1.mix_df = m1.process_data(config.CODE_LIST)
    m1.sub_df = m1.process_data(config.SUB_CODE_LIST) if m1.is_sub_contract else None
    m1.generate_features_and_labels()
    # m1.model_selection(m1.train_data,m1.y_train_dict)
    m1.train_model()
    for targ in m1.target_cols:
        model_evaluate(m1.y_train_dict[targ], m1.train_pred[targ + '_pred'],m1.train_pred_quantile_dict, 'train')
    targ=m1.target_cols[0]
    for horizon in config.EVALUATE_HORIZONS:
        actual_target = m1.mix_df[f"{config.TARGET_LABEL}_{horizon}"][m1.test_data[targ].index]
        model_evaluate(actual_target, m1.test_pred[targ + '_pred'], m1.train_pred_quantile_dict, 'test')
    # 回测
    backtester = OrderBookBacktester(m1.mix_df.loc[m1.test_data[targ].index], m1.test_pred, m1.train_pred_quantile_dict)
    m1.results, m1.summary, m1.trade_report = backtester.run_backtest(target_col=targ)
    if m1.is_factor_test:
        m1.factor_test_results, m1.shap_values = m1.factor_system.factor_test(m1.test_data[targ], m1.y_test_dict[targ], 
                                                                              m1.feature_cols, model=m1.model_trainer.models[targ], is_shap=m1.is_shap)
    m1.factor_system.generate_reports(m1.mix_df, m1.feature_cols, m1.all_features)
    m1.save_results()
    log_print(f"分析完成，结果已保存到 {m1.output_dir}")


if __name__ == "__main__":
    main()

