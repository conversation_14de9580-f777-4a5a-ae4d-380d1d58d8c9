#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('DCE.prod')
import warnings
warnings.filterwarnings('ignore')
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所东坝机房    
"""
 
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.referDetection import referDetection
from strategies.basisStrategy import BasisStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
import time
 
maker='pg2209.DCE'
refer='pg2208.DCE'

multiplier=20
ts = 1
 
engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=True,save_result=False,
                           refer_test=False,fast_join=True,refer_late=True,rule_out=True,invisible=False) #Counter代表是否只统计对价成交，duty代表是否统计义务
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔

    start=datetime.datetime(2022,7, 6, 21, 0), # 开始时间
    end=datetime.datetime(2022, 7,7, 15, 0), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: ts,refer: ts}, # 一个tick大小
    capital=1_000_000, # 初始资金
)
 
# 添加回测策略，并修改内部参数
engine.clear_data()
 
# joinVolume = 200
# secondConfirmationFlag = 1
# smartSecondConfirmationFlag = True
# typ = 'grid'
# endHedgeFlag = True
 
# engine.add_strategy(referDetection, {'lots': {refer:1},'maker':maker,'refer':refer,'priceticks':engine.priceticks,'endHedgeFlag':endHedgeFlag,
#                                       'sizes':engine.sizes,'maxPos':{refer:30},'level':1, 'secondConfirmationFlag':secondConfirmationFlag,
#                                       'typ':typ,'smartSecondConfirmationFlag':smartSecondConfirmationFlag,'joinVolume':joinVolume})
minDelta=3
maxDelta=6
redifEWMAFlag=False
floatEdgeFlag=True
referHedgeFlag=False
 
lots=3
edge=6
alpha=0.1
gamma=1
maxPos=18
minEdge=4
 
validVolume = 21
SVMJoinVolume = 200
predict_refer_trend = False
fair_refer_calculator = 0
 
engine.add_strategy(BasisStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                    'edge':edge,'maxPos':maxPos,'alpha':alpha,'gamma':gamma,'typ':'maker','maxDelta':maxDelta,
                                    'minDelta':minDelta,'redifEWMAFlag':redifEWMAFlag,
                                    'floatEdgeFlag':floatEdgeFlag,'minEdge':minEdge,'validVolume':validVolume,
                                    'SVMJoinVolume':SVMJoinVolume,'predict_refer_trend':predict_refer_trend,'fair_refer_calculator':fair_refer_calculator})
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
engine.duty_statistics()
 
#%%
trades = pd.DataFrame([{'time':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'insid':engine.trades[x].symbol+'.'+engine.trades[x].exchange.value,'comments':engine.trades[x].comments,
           'basePrice':engine.trades[x].basePrice,'midPrice':engine.trades[x].midPrice
           } for x in engine.trades])
all_ins = [maker,refer]
all_trades={}
all_ins = [refer,maker]
for iid in all_ins:
    trade = trades[trades.insid==iid].copy()
    trade['net'] = (trade.volume*trade.direction).cumsum()
    
    trade['spread'] = trade['direction']*(trade['midPrice']-trade['price'])
    trade['basis'] = trade['midPrice'] - trade['basePrice']
    
    cash_base = (trade['direction']*(-1)*trade['volume']*trade['basePrice'])*multiplier
    cash_basis = (trade['direction']*(-1)*trade['volume']*trade['basis'])*multiplier
    
    cash = (trade['direction']*(-1)*trade['volume']*trade['price'])*multiplier
    cashCum = np.cumsum(cash)
    
    cashCum_base = np.cumsum(cash_base)
    cashCum_basis = np.cumsum(cash_basis)
    
    trade['pnl'] = cashCum + trade.net*trade.midPrice*multiplier
    trade['delta_pnl'] = cashCum_base + trade.net*trade.basePrice*multiplier
    trade['basis_pnl'] = cashCum_basis + trade.net*trade.basis*multiplier
    trade['spread_pnl'] = np.cumsum(trade['spread']*trade.volume)*multiplier
    
    all_trades[iid] = trade
 
#%%
a = engine.strategies[1].offer_list
a = pd.DataFrame(engine.strategies[1].offer_list)
a.columns = engine.strategies[1].offer_list_head
 
 
#%%
from vnpy_tools.PostTradeAnalysis import PostTradeAnalysis
Analysis = PostTradeAnalysis(engine,multiplier)
Analysis.plot_equity()
 
 #%%
df_mkt = engine.get_risk(multiplier=multiplier)
 
# 存表
number = 900
name = 'refer_detection'
maxPos = 30
 
level = 1
secondConfirmationFlag = 1
parameters = {'maxPos':maxPos, 'level':level, 'secondConfirmationFlag':secondConfirmationFlag}    
 
save_flag = True
# df_strategy = engine.get_strategy()
df_strategy = pd.DataFrame([])
if save_flag:
    engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=True, save_strategy=True, save_order=True, save_trade=True, save_mkt=True)
 
#%%
 
name='basis_strategy'
save_strategy=True
save_risk = True
save_order = True
save_trade = True
parameters = {'alpha':alpha, 'edge':edge, 'maxPos':maxPos, 'gamma':gamma}
number=3
df_mkt = engine.get_risk(multiplier=multiplier)
        
df_strategy = engine.get_strategy()
     
engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade) 
 
#%%
 
import pandas as pd
import numpy as np
from datetime import timedelta
 
trades = pd.DataFrame([{'time':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
    'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
    'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments
    } for x in engine.trades])
df_orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
    'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
    } for x in engine.get_all_orders()])
 
mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
           'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
           'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
           'last_price','volume','turnover']]
mkt_maker = mkt[mkt.symbol=='TA208']
 
def get_time(x):
    try:
        t = datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ')
    except:
        try:
            t = datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%SZ')
        except:
            t = datetime.datetime.strptime(x,'%Y-%m-%d %H:%M:%S.%f')
    return t
 
 
trades['net'] = (trades.volume*trades.direction).cumsum()
trades['cash'] = (trades['direction']*(-1)*trades['volume']*trades['price'])*engine.sizes[engine.vt_symbols[0]]
trades['realPnl'] = trades['cash'].cumsum()
trades['pnl'] = trades.realPnl + trades.net * trades.price * multiplier
trades['datetime']=trades.time.apply(lambda x: get_time(x)-timedelta(seconds=0.1))
trades.index = trades['datetime']
#%%
df_orders_rd = df_orders[df_orders['comments']=='referDetection']
df_orders_rd1 = df_orders_rd.copy()
#%%
df_orders_cancel = df_orders[(df_orders['traded']==0)&(df_orders['comments']=='referDetection')]
#%%
# 批量回测结果, 包括refer_detection, basis_strategy或者两个策略叠加.
date_list = [(datetime.datetime(2022,2, 14+i, 21, 10), datetime.datetime(2022,2, 15+i, 15, 00)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 18, 21, 10), datetime.datetime(2022,2, 21, 15, 00))]
date_list += [(datetime.datetime(2022,2, 21+i, 21, 10), datetime.datetime(2022,2, 22+i, 15, 00)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 25, 21, 10), datetime.datetime(2022,2, 28, 15, 00)), (datetime.datetime(2022,2, 28, 21, 10), datetime.datetime(2022,3, 1, 15, 00))]
date_list += [(datetime.datetime(2022,3, 1+i, 21, 10), datetime.datetime(2022,3, 2+i, 15, 00)) for i in range(3)]
 
 
# maker_list = [('c2205.DCE', 10, 1), ('m2205.DCE', 10, 1)]
# maker_list = [('CF205.CZCE', 5, 5), ('RM205.CZCE', 10, 1), ('SR205.CZCE', 10, 1)]
secondConfirmation_list = [0]
maxPos_list = [10]
# level_list = [('all', 1), ('all', 2), ('all', 12), ('grid', 2), ('grid', 3), ('grid2', 2), ('grid2', 3)]
name = 'refer_detection'
number = 0
parameter_list = [('c2211.DCE', 'c2205.DCE', 10, 1, 'all', 1), ('m2207.DCE', 'm2205.DCE', 10, 1, 'grid', 2)] # maker, refer, multiplier, tick, typ, level
# parameter_list = [('pp2206.DCE', 'pp2205.DCE', 5, 1, 'grid', 3), ('l2206.DCE', 'l2205.DCE', 5, 1, 'grid', 2)] # maker, refer, multiplier, tick, typ, level
# parameter_list = [('pp2206.DCE', 'pp2205.DCE', 5, 1, 'grid', 3)] # maker, refer, multiplier, tick, typ, level
 
edge_list = [2, 3]
refer_detection_list = [True, False]
 
result1 = {
    'maker':[],
    # 'maxPos':[],
    # 'secondConfirmationFlag':[],
    'level':[],
    'typ':[],
    'edge':[],
    'referDetectionFlag':[],
    # '主力撤单次数':[],
 
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []
 }
save_flag = False
save_risk=True
save_strategy=False 
save_order=True
save_trade=True
count = 0
t = time.time()
for start, end in date_list:
    for maker, refer, multiplier, pricetick, typ, level in parameter_list:
        # refer=maker
        save_mkt = True
        for secondConfirmationFlag in secondConfirmation_list:
            for maxPos in maxPos_list:
                for edge in edge_list[:]:
                    for referDetectionFlag in refer_detection_list:
                        count += 1
                        try:
                            parameters = {'maxPos':maxPos, 'secondConfirmationFlag':secondConfirmationFlag, 'level':level, 'typ':typ}    
                            engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=False,save_result=False,refer_test=True,fast_join=True,refer_late=False,rule_out = True) #Counter代表是否只统计对价成交，duty代表是否统计义务
                            engine.set_parameters(
                                vt_symbols=[maker,refer], # 回测品种
                                interval=Interval.TICK, # 回测模式的数据间隔
                            
                                start=start, # 开始时间
                                end=end, # 结束时间
                                rates={maker: 0,refer: 0}, # 手续费率
                                slippages={maker: 0,refer: 0}, # 滑点
                                sizes={maker: multiplier,refer: multiplier}, # 合约规模
                                priceticks={maker: pricetick,refer: pricetick}, # 一个tick大小
                                capital=1_000_000, # 初始资金
                            )
                            
                            # 添加回测策略，并修改内部参数
                            engine.clear_data()
                            #refer_detection策略
                            if referDetectionFlag:
                                engine.add_strategy(referDetection, {'lots': {refer:1},'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'maxPos':{refer:np.inf},'level':level, 'secondConfirmationFlag':0, 'typ':typ,'smartSecondConfirmationFlag':False, 'endHedgeFlag':False})                        
                            minDelta=100
                            maxDelta=300
                            redifEWMAFlag=False
                            lots=15
                            # edge=6
                            alpha=0.03
                            gamma=0
                            maxPos=45
                            
                            #主力定价策略 
                            # engine.add_strategy(BasisStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':maxPos,'alpha':alpha,'gamma':gamma,'typ':'maker','maxDelta':maxDelta,'minDelta':minDelta,'redifEWMAFlag':redifEWMAFlag})
                            
                            engine.load_data() # 加载历史数据
                            engine.run_backtesting() # 进行回测
                            df = engine.calculate_tick_result() # 计算逐日盯市盈亏
                            stat = engine.calculate_tick_statistics() # 统计日度策略指标   
                            # df_orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
                            #                            'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
                            #                            } for x in engine.get_all_orders()])
                            # df_orders_cancel = df_orders[(df_orders['traded']==0)&(df_orders['comments']=='referDetection')]
                            
    
                            for key in engine.result.keys():
                                result1[key].append(engine.result[key])  
                            result1['maker'].append(maker)
                            # result1['maxPos'].append(maxPos)
                            # result1['secondConfirmationFlag'].append(secondConfirmationFlag)
                            result1['level'].append(level)
                            result1['typ'].append(typ)
                            result1['edge'].append(edge)
                            result1['referDetectionFlag'].append(referDetectionFlag)
                            # result1['主力撤单次数'].append(df_orders_cancel.shape[0])
                            df_mkt = pd.DataFrame()
                            if save_flag:
                                if save_risk:
                                    df_mkt = engine.get_risk(multiplier=multiplier)
                                else:
                                    df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
                                if save_strategy:
                                    df_strategy = engine.get_strategy()
                                else:
                                    df_strategy = pd.DataFrame([])
                                engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade, save_mkt=save_mkt)
                                if save_mkt:
                                    save_mkt = False
                            result2 = pd.DataFrame(result1)
                            result2.to_csv('result_rd_c&m.csv')
                            print(time.time()-t, count, 'maker={}'.format(maker), 'typ={}'.format(typ), 'level={}'.format(level), 'referDetectionFlag={}'.format(referDetectionFlag), 'success')
                        except Exception as err:
                            print(time.time()-t, count, 'maker={}'.format(maker), 'typ={}'.format(typ), 'level={}'.format(level), 'referDetectionFlag={}'.format(referDetectionFlag), 'failed')
                            print(err)
 
 
#%%
# 批量回测结果, 考察refer_detection的探测效应
import pandas as pd
import datetime
from datetime import timedelta
from influxdb import InfluxDBClient
import pandas as pd
import numpy as np
 
def get_time(x):
    try:
        t = datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ')
    except:
        try:
            t = datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%SZ')
        except:
            t = datetime.datetime.strptime(x,'%Y-%m-%d %H:%M:%S.%f')
    return t
 
client = InfluxDBClient('************',9001,'reader','iamreader','testbase') #东坝new
 
date_list = [(datetime.datetime(2022,2, 14+i, 21, 10), datetime.datetime(2022,2, 15+i, 15, 00)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 18, 21, 10), datetime.datetime(2022,2, 21, 15, 00))]
date_list += [(datetime.datetime(2022,2, 21+i, 21, 10), datetime.datetime(2022,2, 22+i, 15, 00)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 25, 21, 10), datetime.datetime(2022,2, 28, 15, 00)), (datetime.datetime(2022,2, 28, 21, 10), datetime.datetime(2022,3, 1, 15, 00))]
date_list += [(datetime.datetime(2022,3, 1+i, 21, 10), datetime.datetime(2022,3, 2+i, 15, 00)) for i in range(3)]
 
 
# maker_list = [('c2205.DCE', 10, 1), ('m2205.DCE', 10, 1)]
# maker_list = [('CF205.CZCE', 5, 5), ('RM205.CZCE', 10, 1), ('SR205.CZCE', 10, 1)]
secondConfirmation_list = [0]
maxPos_list = [10]
# level_list = [('all', 1), ('all', 2), ('all', 12), ('grid', 2), ('grid', 3), ('grid2', 2), ('grid2', 3)]
name = 'refer_detection'
number = 0
parameter_list = [('c2211.DCE', 'c2205.DCE', 10, 1, 'all', 1), ('m2207.DCE', 'm2205.DCE', 10, 1, 'grid', 2)] # maker, refer, multiplier, tick, typ, level
# parameter_list = [('pp2206.DCE', 'pp2205.DCE', 5, 1, 'grid', 3), ('l2206.DCE', 'l2205.DCE', 5, 1, 'grid', 2)] # maker, refer, multiplier, tick, typ, level
# parameter_list = [('pp2206.DCE', 'pp2205.DCE', 5, 1, 'grid', 3)] # maker, refer, multiplier, tick, typ, level
 
# edge_list = [4,5,6,7]
edge_list = [3]
 
result1 = {
    'maker':[],
    # 'maxPos':[],
    # 'secondConfirmationFlag':[],
    'level':[],
    'typ':[],
    'edge':[],
    '强信号':[],
    '弱信号':[],
    '同步':[],
    # '主力撤单次数':[],
 
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []
 }
save_flag = False
save_risk=True
save_strategy=False 
save_order=True
save_trade=True
count = 0
t = time.time()
for start, end in date_list:
    for maker, refer, multiplier, pricetick, typ, level in parameter_list:
        # refer=maker
        save_mkt = True
        for secondConfirmationFlag in secondConfirmation_list:
            for maxPos in maxPos_list:
                for edge in edge_list:
                    count += 1
                    try:
                        parameters = {'maxPos':maxPos, 'secondConfirmationFlag':secondConfirmationFlag, 'level':level, 'typ':typ}    
                        engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=False,save_result=False,refer_test=False,fast_join=True,refer_late=False,rule_out = True) #Counter代表是否只统计对价成交，duty代表是否统计义务
                        engine.set_parameters(
                            vt_symbols=[maker,refer], # 回测品种
                            interval=Interval.TICK, # 回测模式的数据间隔
                        
                            start=start, # 开始时间
                            end=end, # 结束时间
                            rates={maker: 0,refer: 0}, # 手续费率
                            slippages={maker: 0,refer: 0}, # 滑点
                            sizes={maker: multiplier,refer: multiplier}, # 合约规模
                            priceticks={maker: pricetick,refer: pricetick}, # 一个tick大小
                            capital=1_000_000, # 初始资金
                        )
                        
                        # 添加回测策略，并修改内部参数
                        engine.clear_data()
                        #refer_detection策略
                        engine.add_strategy(referDetection, {'lots': {refer:1},'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'maxPos':{refer:10},'level':level, 'secondConfirmationFlag':0, 'typ':typ,'smartSecondConfirmationFlag':False, 'endHedgeFlag':False})                        
                        # minDelta=100
                        # maxDelta=300
                        # redifEWMAFlag=False
                        # lots=4
                        # # edge=6
                        # alpha=0.03
                        # gamma=0.3
                        # maxPos=12
                        
                        # #主力定价策略 
                        engine.add_strategy(BasisStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':maxPos,'alpha':alpha,'gamma':gamma,'typ':'maker','maxDelta':maxDelta,'minDelta':minDelta,'redifEWMAFlag':redifEWMAFlag})
                        
                        engine.load_data() # 加载历史数据
                        engine.run_backtesting() # 进行回测
                        df = engine.calculate_tick_result() # 计算逐日盯市盈亏
                        stat = engine.calculate_tick_statistics() # 统计日度策略指标   
                        # df_orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
                        #                            'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
                        #                            } for x in engine.get_all_orders()])
                        # df_orders_cancel = df_orders[(df_orders['traded']==0)&(df_orders['comments']=='referDetection')]
                        
 
                        for key in engine.result.keys():
                            result1[key].append(engine.result[key])  
                        result1['maker'].append(maker)
                        # result1['maxPos'].append(maxPos)
                        # result1['secondConfirmationFlag'].append(secondConfirmationFlag)
                        result1['level'].append(level)
                        result1['typ'].append(typ)
                        result1['edge'].append(edge)
                        # result1['主力撤单次数'].append(df_orders_cancel.shape[0])
                        
                        trades = pd.DataFrame([{'time':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
                            'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
                            'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments
                            } for x in engine.trades])    
                        trades['datetime']=trades.time.apply(lambda x: get_time(x)-timedelta(seconds=0.1))
                        trades.index = trades['datetime']                        
 
                        begin1 = start.timestamp() *1000000000
                        end1 = end.timestamp() *1000000000
                        
                        
                        refer1=refer.split('.')[0]
                        maker1=maker.split('.')[0]
                        try:
                            result_refer = client.query("select * from testdalian where time >= %d and time <= %d and insid_md='"%(begin1,end1)+refer1+"';") 
                            result_maker = client.query("select * from testdalian where time >= %d and time <= %d and insid_md='"%(begin1,end1)+maker1+"';") 
                            
                            points_refer = result_refer.get_points()
                            l1=[]
                            for d in points_refer:
                                l1.append(d)
                            print(len(l1))
                            
                            dfm_refer = pd.DataFrame(l1)
                            dfm_refer['datetime']=dfm_refer.time.apply(lambda x: get_time(x))+timedelta(hours=8)
                            dfm_refer.index = dfm_refer['datetime']
                            
                            points_maker = result_maker.get_points()
                            l2=[]
                            
                            for d in points_maker:
                                l2.append(d)
                            print(len(l2))
                            row_r_l = l1[0]
                            a_p_l = row_r_l['a_p1']
                            b_p_l = row_r_l['b_p1']
                            signal = 0
                            for row_r_n in l1:
                                a_p_n = row_r_n['a_p1']
                                b_p_n = row_r_n['b_p1']
                                if a_p_n < a_p_l and b_p_n > b_p_l:
                                    signal = 0
                                elif a_p_n > a_p_l and b_p_n < b_p_l:
                                    signal = 0    
                                elif a_p_n < a_p_l or b_p_n < b_p_l:
                                    signal = -1
                                elif a_p_n > a_p_l or b_p_n > b_p_l:
                                    signal = 1
                                else:
                                    signal = 0
                                row_r_n['signal'] = signal
                                a_p_l = a_p_n
                                b_p_l = b_p_n
                                
                            dfm_refer = pd.DataFrame(l1)
                            dfm_refer['datetime']=dfm_refer.time.apply(lambda x: get_time(x))+timedelta(hours=8)
                            dfm_refer.index = dfm_refer['datetime']
                            
                            
                            row_m_l = l2[0]
                            a_p_l = row_m_l['a_p1']
                            b_p_l = row_m_l['b_p1']
                            signal = 0
                            for row_m_n in l2:
                                a_p_n = row_m_n['a_p1']
                                b_p_n = row_m_n['b_p1']
                                if a_p_n < a_p_l and b_p_n > b_p_l:
                                    signal = 0
                                elif a_p_n > a_p_l and b_p_n < b_p_l:
                                    signal = 0    
                                elif a_p_n < a_p_l or b_p_n < b_p_l:
                                    signal = -1
                                elif a_p_n > a_p_l or b_p_n > b_p_l:
                                    signal = 1
                                else:
                                    signal = 0
                                row_m_n['signal'] = signal
                                a_p_l = a_p_n
                                b_p_l = b_p_n
                            
                            dfm_maker = pd.DataFrame(l2)
                            dfm_maker['datetime']=dfm_maker.time.apply(lambda x: get_time(x))+timedelta(hours=8)
                            dfm_maker.index = dfm_maker['datetime']
                            
                            df_mkt = pd.concat([dfm_refer, dfm_maker], axis = 0)
                            df_mkt.sort_index(inplace=True)                            
    
                       
                            # trade对maker的引领效应
                            df_test1 = pd.concat([dfm_maker, trades], axis = 0)
                            df_test1.sort_index(inplace=True)
                            df_mt_list = df_test1[['signal', 'direction']]
                            right_list1 = []
                            direction = 0
                            df_mt_list2 = df_mt_list.to_dict('records')
                            for row in df_mt_list2:
                                direction_n = row['direction']
                                signal_n = row['signal']
                                
                                if not np.isnan(direction_n):
                                    if direction == -direction_n:
                                        # right_list1.append(0.001)
                                        pass
                                    direction = direction_n
                                
                                if not np.isnan(signal_n) and signal_n != 0:
                                    if direction != 0:
                                        if signal_n == -direction:
                                            right_list1.append(1)
                                        else:
                                            right_list1.append(0)
                                        direction = 0
                            
                            rate1 = np.sum(right_list1)/len(right_list1)
                            result1['强信号'].append(rate1)  
                            print('强信号', rate1)                            
    
    
                            right_list2 = []
                            direction = 0
    
                            for row in df_mt_list2:
                                direction_n = row['direction']
                                signal_n = row['signal']
                                
                                if not np.isnan(direction_n):
                                    if direction == -direction_n:
                                        right_list2.append(0.001)
                                        pass
                                    direction = direction_n
                                
                                if not np.isnan(signal_n) and signal_n != 0:
                                    if direction != 0:
                                        if signal_n == -direction:
                                            right_list2.append(1)
                                        else:
                                            right_list2.append(0)
                                        direction = 0
                            
                            rate2 = np.sum(right_list2)/len(right_list2)
                            result1['弱信号'].append(rate2)  
                            print('弱信号', rate2)                                                    
                            # trade对maker的引领效应(同时移动的概率)
    
                            right_list3 = []
                            direction = 0
    
                            for row in df_mt_list2:
                                direction_n = row['direction']
                                signal_n = row['signal']
                                
                                if not np.isnan(direction_n):
                                    if direction == -direction_n:
                                        # right_list3.append(0.001)
                                        pass
                                    direction = direction_n
                                
                                if not np.isnan(signal_n):
                                    if direction != 0:
                                        if signal_n == -direction:
                                            right_list3.append(1)
                                        else:
                                            right_list3.append(0)
                                        direction = 0
                            
                            rate3 = np.sum(right_list3)/len(right_list3)
                            result1['同步'].append(rate3)  
                            print('同步', rate3)   
                        except:
                            result1['强信号'].append(np.nan)  
                            result1['弱信号'].append(np.nan)  
                            result1['同步'].append(np.nan)  
                        df_mkt = pd.DataFrame()
                        if save_flag:
                            if save_risk:
                                df_mkt = engine.get_risk(multiplier=multiplier)
                            else:
                                df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
                            if save_strategy:
                                df_strategy = engine.get_strategy()
                            else:
                                df_strategy = pd.DataFrame([])
                            engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade, save_mkt=save_mkt)
                            if save_mkt:
                                save_mkt = False
                        result2 = pd.DataFrame(result1)
                        result2.to_csv('refer_c&m.csv')
                        print(time.time()-t, count, 'maker={}'.format(maker), 'typ={}'.format(typ), 'level={}'.format(level), 'success')
                    except Exception as err:
                        print(time.time()-t, count, 'maker={}'.format(maker), 'typ={}'.format(typ), 'level={}'.format(level), 'failed')
                        print(err)
 
 
#%%
from influxdb import InfluxDBClient
client = InfluxDBClient('************',9001,'reader','iamreader','omm')
 
 
 
#%%
trades[['price', 'net']].diff().iloc[:180].corr()
 
#%%
# 成交后是否往该方向移动
trades1 = trades.to_dict('record')
ticksize = 1
right_list = []
p_list = []
row = trades1[0]
p_l = row['price']
d_l = row['direction']
p_adjust_l = p_l+0.5*d_l*ticksize
for row in trades1:
    p_n = row['price']
    d_n = row['direction']
    p_adjust_n = p_n+0.5*d_n*ticksize 
    if p_n == p_l and d_n == d_l:
        continue
    if d_l == 1:
        right_list.append(1 if p_adjust_n <= p_adjust_l else 0)
    elif d_l == -1:
        right_list.append(1 if p_adjust_n >= p_adjust_l else 0)
    p_l = p_n
    d_l = d_n
    p_adjust_l = p_adjust_n
    p_list.append((p_n, d_n, p_adjust_n))
    
rate = np.sum(right_list)/len(right_list)    
print(rate)
 
#%%
from datetime import timedelta
from influxdb import InfluxDBClient
import pandas as pd
import numpy as np
 
client = InfluxDBClient('************',9001,'reader','iamreader','testbase') #东坝new
 
#%%
import datetime
beginStr = datetime.datetime(2022, 3, 2, 21, 10)
endStr = datetime.datetime(2022, 3, 3, 15, 0)
begin = beginStr.timestamp() *1000000000
end = endStr.timestamp() *1000000000
 
# beginStr = '2022-03-02T21:10:00.0Z'
# endStr = '2022-03-03T15:00:10.0Z'
# begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
# end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
 
# begin_datetime = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ')
# end_datetime = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ')
 
refer='m2205'
maker='m2207'
 
result_refer = client.query("select * from testdalian where time >= %d and time <= %d and insid_md='"%(begin,end)+refer+"';") 
result_maker = client.query("select * from testdalian where time >= %d and time <= %d and insid_md='"%(begin,end)+maker+"';") 
 
points_refer = result_refer.get_points()
l1=[]
for d in points_refer:
    l1.append(d)
print(len(l1))
    
def get_time(x):
    try:
        t = datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ')
    except:
        try:
            t = datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%SZ')
        except:
            t = datetime.datetime.strptime(x,'%Y-%m-%d %H:%M:%S.%f')
    return t
 
import pandas as pd
import datetime
dfm_refer = pd.DataFrame(l1)
dfm_refer['datetime']=dfm_refer.time.apply(lambda x: get_time(x))+timedelta(hours=8)
dfm_refer.index = dfm_refer['datetime']
 
points_maker = result_maker.get_points()
l2=[]
for d in points_maker:
    l2.append(d)
print(len(l2))
    
 
 
import pandas as pd
import datetime
dfm_maker = pd.DataFrame(l2)
dfm_maker['datetime']=dfm_maker.time.apply(lambda x: get_time(x))+timedelta(hours=8)
dfm_maker.index = dfm_maker['datetime']
 
 
#%%
row_r_l = l1[0]
a_p_l = row_r_l['a_p1']
b_p_l = row_r_l['b_p1']
signal = 0
for row_r_n in l1:
    a_p_n = row_r_n['a_p1']
    b_p_n = row_r_n['b_p1']
    if a_p_n < a_p_l and b_p_n > b_p_l:
        signal = 0
    elif a_p_n > a_p_l and b_p_n < b_p_l:
        signal = 0    
    elif a_p_n < a_p_l or b_p_n < b_p_l:
        signal = -1
    elif a_p_n > a_p_l or b_p_n > b_p_l:
        signal = 1
    else:
        signal = 0
    row_r_n['signal'] = signal
    a_p_l = a_p_n
    b_p_l = b_p_n
    
dfm_refer = pd.DataFrame(l1)
dfm_refer['datetime']=dfm_refer.time.apply(lambda x: get_time(x))+timedelta(hours=8)
dfm_refer.index = dfm_refer['datetime']
 
 
row_m_l = l2[0]
a_p_l = row_m_l['a_p1']
b_p_l = row_m_l['b_p1']
signal = 0
for row_m_n in l2:
    a_p_n = row_m_n['a_p1']
    b_p_n = row_m_n['b_p1']
    if a_p_n < a_p_l and b_p_n > b_p_l:
        signal = 0
    elif a_p_n > a_p_l and b_p_n < b_p_l:
        signal = 0    
    elif a_p_n < a_p_l or b_p_n < b_p_l:
        signal = -1
    elif a_p_n > a_p_l or b_p_n > b_p_l:
        signal = 1
    else:
        signal = 0
    row_m_n['signal'] = signal
    a_p_l = a_p_n
    b_p_l = b_p_n
 
dfm_maker = pd.DataFrame(l2)
dfm_maker['datetime']=dfm_maker.time.apply(lambda x: get_time(x))+timedelta(hours=8)
dfm_maker.index = dfm_maker['datetime']
 
df_mkt = pd.concat([dfm_refer, dfm_maker], axis = 0)
df_mkt.sort_index(inplace=True)
 
#%%
# refer对maker的引领效应(强弱信号)
df_mkt_list = df_mkt[(df_mkt['signal']!=0)][['insid_md', 'signal']].to_dict('records')
right_list = []
signal_l = df_mkt_list[0]['signal']
for row in df_mkt_list:
    insid = row['insid_md']
    signal_n = row['signal']
    if insid == refer:
        if signal_n != signal_l:
            if signal_l != 0:
                # right_list.append(0.001)
                pass
            signal_l = signal_n
    if insid == maker:
        if signal_n == signal_l and signal_l != 0:
            right_list.append(1)    
        elif signal_n != signal_l and signal_l != 0:
            right_list.append(0)
        signal_l = 0
 
            
rate = np.sum(right_list)/len(right_list)
print(rate)
 
#%%
# refer对maker的同tick变动效应
df_mkt_list = df_mkt[['insid_md', 'signal']].to_dict('records')
right_list = []
signal_l = df_mkt_list[0]['signal']
for row in df_mkt_list:
    insid = row['insid_md']
    signal_n = row['signal']
    if insid == refer:
        if signal_n != signal_l:
            if signal_l != 0:
                # right_list.append(0.001)
                pass
            signal_l = signal_n
    if insid == maker:
        if signal_n == signal_l and signal_l != 0:
            right_list.append(1)    
        elif signal_n != signal_l and signal_l != 0:
            right_list.append(0)
        signal_l = 0
 
            
rate = np.sum(right_list)/len(right_list)
print(rate)
 
#%%
# trade对maker的引领效应
df_test1 = pd.concat([dfm_maker, trades], axis = 0)
df_test1.sort_index(inplace=True)
df_mt_list = df_test1[['signal', 'direction']]
right_list2 = []
direction = 0
df_mt_list2 = df_mt_list.to_dict('records')
for row in df_mt_list2:
    direction_n = row['direction']
    signal_n = row['signal']
    
    if not np.isnan(direction_n):
        if direction == -direction_n:
            # right_list2.append(0.001)
            pass
        direction = direction_n
    
    if not np.isnan(signal_n) and signal_n != 0:
        if direction != 0:
            if signal_n == -direction:
                right_list2.append(1)
            else:
                right_list2.append(0)
            direction = 0
 
rate = np.sum(right_list2)/len(right_list2)
print(rate)    
    
#%%
# trade对maker的引领效应(同时移动的概率)
df_test1 = pd.concat([dfm_maker, trades], axis = 0)
df_test1.sort_index(inplace=True)
df_mt_list = df_test1[['signal', 'direction']]
right_list3 = []
direction = 0
df_mt_list2 = df_mt_list.to_dict('records')
for row in df_mt_list2:
    direction_n = row['direction']
    signal_n = row['signal']
    
    if not np.isnan(direction_n):
        if direction == -direction_n:
            # right_list3.append(0.001)
            pass
        direction = direction_n
    
    if not np.isnan(signal_n):
        if direction != 0:
            if signal_n == -direction:
                right_list3.append(1)
            else:
                right_list3.append(0)
            direction = 0
 
rate = np.sum(right_list3)/len(right_list3)
print(rate)    
#%%
result2 = pd.DataFrame(result1)
result2.to_csv('result2.csv')
 
 
#%%
from vnpy.trader.analysis import Analysis
 
analysis = Analysis(engine)
df = analysis.pnl_plot(savefig=False) 
 
trades = analysis.trade()
 
#%%
 
 
#%%
 
#maker='m2203.DCE'
#refer='m2201.DCE'
# maker='hc2202.SHFE'
# refer='hc2201.SHFE'
 
#maker='pg2112.DCE'
#refer='pg2111.DCE'
#maker='c2111.DCE'
#refer='c2201.DCE'
# maker='CF203.CZCE'
# refer='CF201.CZCE'
 
 
 
maker='hc2209.SHFE'
refer='hc2205.SHFE'
 
 
multiplier=10
 
underlying_list = ['m']
maker_list = ['2203.DCE', '2207.DCE','2208.DCE']
 
 
#time_list = list(pd.read_csv('trading_day.csv')['DateTime'])
'''time_list= [
     '2021/9/10',
 '2021/9/13',
 '2021/9/14',
 '2021/9/15',
 '2021/9/16',
 '2021/9/17',
 '2021/9/20',
 '2021/9/21',
 '2021/9/22',
 '2021/9/23',
 '2021/9/24',
 '2021/9/27',
 '2021/9/28',
 '2021/9/29',
 '2021/9/30',
 '2021/10/8',
time_list= ['2021/10/11',
 '2021/10/12',
 '2021/10/13',
 '2021/10/14',
 '2021/10/15',
 '2021/10/18',
 '2021/10/19',
 '2021/10/20',
 '2021/10/21',
 '2021/10/22',
 '2021/10/25',
 '2021/10/26',
 '2021/10/27',
 '2021/10/28',
 '2021/10/29','''
time_list= ['2021/12/24',
 '2021/12/27',
 '2021/12/28',
 '2021/12/29',
 '2021/12/30',
 '2021/12/31']
edge_list = [2,3]
gamma_list = [0.3, 0.5]
alpha_list = [0.03]
maxPos_list = [30]
 
# def run_backtest(i):
#%%
def run_backtest(startdate, enddate, maker, refer, edge = 2, maxPos = 3, gamma = 0.3, alpha = 0.01, save_flag = False, name = 'basis_strategy', number = 0, typ = 'taker', onlyCrossFlag=False):
    save_strategy=True
    save_risk = True
    save_order = True
    save_trade = True
    onlyCrossFlag=False
    typ = 'maker'
    number=number
    tick_size = 1
    duty=False
 
    lots=5
    redifEWMAFlag=False
    
    parameters = {'alpha':alpha, 'edge':edge, 'maxPos':maxPos, 'gamma':gamma}
    
    engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=onlyCrossFlag,duty=duty,save_result=False,refer_test=False,rule_out = False) #Counter代表是否只统计对价成交，duty代表是否统计义务
    start=datetime.datetime(startdate[0], startdate[1], startdate[2], 9, 10) # 开始时间
 
    end=datetime.datetime(enddate[0], enddate[1],enddate[2], 15, 0) # 结束时间
    
    engine.set_parameters(
        vt_symbols=[maker,refer], # 回测品种
        interval=Interval.TICK, # 回测模式的数据间隔
    
        start=start, # 开始时间
        end=end, # 结束时间
        rates={maker: 0,refer: 0}, # 手续费率
        slippages={maker: 0,refer: 0}, # 滑点
        sizes={maker: multiplier,refer: multiplier}, # 合约规模
        priceticks={maker: tick_size,refer: tick_size}, # 一个tick大小
        capital=1_000_000, # 初始资金
    )
    
    # 添加回测策略，并修改内部参数
    engine.clear_data()
    
    # engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})
    
    engine.add_strategy(BasisStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':maxPos,'alpha':alpha,'gamma':gamma,'typ':typ,'redifEWMAFlag':redifEWMAFlag, 'maxDelta':100, 'minDelta':100})
    
    engine.load_data() # 加载历史数据
    engine.run_backtesting() # 进行回测
    
    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
    stat = engine.calculate_tick_statistics() # 统计日度策略指标
    df_mkt = pd.DataFrame()
    if save_flag:
        if save_risk:
            df_mkt = engine.get_risk(multiplier=multiplier)
        else:
            df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
        if save_strategy:
            df_strategy = engine.get_strategy()
        else:
            df_strategy = pd.DataFrame([])
        engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade)
    
    
    # print(engine.result)
    # engine.duty_statistics()
    # from vnpy.trader.analysis import Analysis
    
    # analysis = Analysis(engine)
    # df = analysis.pnl_plot() 
    return engine, df_mkt #, df
# # =======
# # time_list = [7,8,11,12,13,14,15]
# time_list = [7,8]
#%%
 
multiplier=5
 
startdate, enddate, maker, refer, edge, maxPos, gamma, alpha, number = ([2022, 2, 9], [2022, 2, 9], 'eb2204.DCE', 'eb2204.DCE', 5, 20, 0.5, 1, 3)
engine, df_mkt = run_backtest(startdate, enddate, maker, refer, edge, maxPos, gamma, alpha, save_flag=False, number=number)
 
df_offer_list = pd.DataFrame(engine.strategy.offer_list)
df_offer_list.columns = engine.strategy.offer_list_head
df_mkt = engine.get_risk(multiplier=multiplier)
# df_test = df_mkt[df_mkt.symbol=='rb2202']
 
#%% 调试order表
# 调试order表
from influxdb import InfluxDBClient
parameters = {'alpha':alpha, 'edge':edge, 'maxPos':maxPos, 'gamma':gamma}
name = 'basis_strategy'
 
client = InfluxDBClient('************',9001,'reader','iamreader','omm')
measurement = name+'_future_order_vnpy'
 
insid_list = list(df_mkt.symbol.drop_duplicates())
 
t_start = int(df_mkt.iloc[0]['datetime'].timestamp()*10**9)
t_end = int(df_mkt.iloc[-1]['datetime'].timestamp()*10**9)
 
for insid in insid_list:
 
    query = "delete from "+measurement+" where time>="+str(t_start)+" and time<="+str(t_end)+" and number="+"'"+str(number)+"'"
    for parameter in parameters.keys():
        query += " and "+parameter+"="+"'"+str(parameters[parameter])+"'"
    query += " and insid="+"'"+str(insid)+"'"
    client.query(query)
print('order表删除成功')
 
json_body = []
orders = [{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
    'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
    } for x in engine.get_all_orders()]    
 
for row in orders:
    order_time = row['time']
    price = row['price']
    direction = row['direction']
    volume = row['volume']
    traded = row['traded']
    orderid = row['orderid']
    symbol = row['symbol']
    comments = row['comments']
    
    measurement = name+'_future_order_vnpy'
    
    
    
    current_time = int(datetime.datetime.strptime(order_time,'%Y-%m-%d %H:%M:%S.%f').timestamp()*10**9)
    
    order_price = float(price)
    long_short = 0 if direction == '多' else 1
    volume_original_total = int(volume)
    volume_traded = int(traded)
    volume_total = int(volume_original_total - volume_traded)
    order_id = str(orderid)
    insid = str(symbol)
    comments = str(comments)
 
    tags = {"insid": insid, "number": number, "order_id": order_id}
    tags.update(parameters)            
    
    body = {
            "measurement": measurement, 
            "time": current_time, 
            "tags": tags, 
            "fields": {
                "order_price": order_price, 
                "long_short": long_short,                 
                "volume_original_total": volume_original_total,    
                "volume_traded": volume_traded,   
                "volume_total": volume_total,                        
                "comments": comments   
            }, 
        }
    json_body.append(body)  
client = InfluxDBClient('************',9001,'reader','iamreader','omm')
res = client.write_points(json_body, batch_size = 10000)
print('order表存储完毕')   
    
#%%
#a = get_risk(engine, multiplier=10)
    
#test1 = a[a.symbol=='rb2202']
 
 
 
#%%
import time
import os
 
t = time.time()
result = {'insid' : [],
          'edge':[],
          'maxPos':[],
          'gamma':[],
          'alpha':[],
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []}
count = 0
 
# k = 2
 
 
number = 3
 
typ = 'maker'
save_flag=True
 
 
for i in range(1, len(time_list)):
    startdate = [int(x) for x in time_list[i-1].split('/')]
    enddate = [int(x) for x in time_list[i].split('/')]
    for underlying in underlying_list:
        refer = underlying + '2205.DCE'
        for m in maker_list:
            maker = underlying + m
            for edge in edge_list:
                for gamma in gamma_list:
                    for maxPos in maxPos_list:
                        for alpha in alpha_list:
                            try:
                                count += 1
                                engine, df_mkt = run_backtest(startdate=startdate, enddate=enddate, maker=maker, refer=refer, edge=edge, maxPos=maxPos, gamma=gamma, alpha=alpha, save_flag = save_flag, name = 'basis_strategy', number = number, typ=typ, onlyCrossFlag=False)
                                df = pd.DataFrame(engine.strategy.offer_list)
                                df.columns = engine.strategy.offer_list_head
                                if df_mkt.empty:
                                    df_mkt = engine.get_risk(multiplier=multiplier)
                                for key in engine.result.keys():
                                    result[key].append(engine.result[key])
                                result['insid'].append(maker)
                                result['edge'].append(edge)
                                result['maxPos'].append(maxPos)
                                result['gamma'].append(gamma)
                                result['alpha'].append(alpha)
                                
                                #path = 'C:/Users/<USER>/Desktop/backtestresult_'+typ+'/'+underlying+'/'+str(engine.end.date())+'/'
                                #folder = os.path.exists(path)
                                #if not folder:
                                    #os.makedirs(path)
                                #df.to_csv(path+typ+'_'+maker.split('.')[0]+'-'+refer.split('.')[0]+'-'+str(edge)+'-'+str(maxPos)+'-'+str(int(10*gamma))+'-'+str(int(100*alpha))+'.csv')
                                #df_mkt.to_csv(path+typ+'_mkt_'+maker.split('.')[0]+'-'+refer.split('.')[0]+'-'+str(edge)+'-'+str(maxPos)+'-'+str(int(10*gamma))+'-'+str(int(100*alpha))+'.csv')
                                
                                #df_result=pd.DataFrame(result)
 
                                #df_result.to_csv('C:/Users/<USER>/Desktop/backtestresult_'+typ+'/result_total_'+typ+str(k)+'.csv')
                                print(time.time()-t, count, (startdate, enddate, maker, refer, edge, maxPos, gamma, alpha), '存储成功')                                
                            except Exception as err:
                                print(time.time()-t, count, (startdate, enddate, maker, refer, edge, maxPos, gamma, alpha), '存储失败',err)
 
 
 
 
 
#%%
df_dict = df.to_dict('records')
df_correct = []
net_last = 0
ask_last = 0
bid_last = 0
 
askamount = 0
bidamount = 0
askamount_refer = 0
bidamount_refer = 0
 
net = 0
pnl = 0
net_refer = 0
pnl_refer = 0
multiplier = 10
 
for row in df_dict:
    net = row['net']
    trade = net-net_last
    net_refer = - net
    fair_refer = row['fair_refer']
    maker_bidP = row['maker_bidP']
    maker_askP = row['maker_askP']
    my_bidP = row['my_bidP']
    my_askP = row['my_askP']    
    
    askamount += -trade*bid_last if trade < 0 else 0
    bidamount += trade*ask_last if trade > 0 else 0
    askamount_refer += trade*fair_refer if trade > 0 else 0
    bidamount_refer += -trade*fair_refer if trade < 0 else 0
    
    pnl = (askamount-bidamount+net*(row['maker_bidP']+row['maker_askP'])/2)*multiplier
    pnl_refer = (askamount_refer-bidamount_refer+net_refer*fair_refer)*multiplier
    
    
    net_last = net
    row['cumPnl'] = pnl+pnl_refer
    row['pnl'] = pnl
    row['pnl_refer'] = pnl_refer
    row['trade_price'] = maker_bidP if trade < 0 else maker_askP if trade > 0 else 0
    ask_last = row['maker_askP']
    bid_last = row['maker_bidP']
    
    df_correct.append(row.copy())
 
df_correct = pd.DataFrame(df_correct)
#%%
mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
        'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
        'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
        'last_price','volume','turnover']]
mkt['time'] = mkt['datetime'].apply(lambda x : (x).strftime('%Y-%m-%d %H:%M:%S.%f'))
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))
 
mkt_main = mkt[mkt.symbol==engine.main_symbol]
mkt_main['dV'] = mkt_main.volume - mkt_main.volume.shift(1)
mkt_main['dT'] = (mkt_main.turnover - mkt_main.turnover.shift(1)).apply(lambda x:str(x))
 
#%%
df_test = df_correct[df_correct.trade_price!=0]
df_test = df_test.reset_index()
#%%
from matplotlib import pyplot as plt
fig = plt.figure()
ax1 = fig.add_subplot(111)
ax1.plot(trades['price'], 'b')
ax1.set_ylabel('price')
ax2=ax1.twinx()
ax2.plot(trades['net'], 'y')
ax2.set_ylabel('net')
plt.show()