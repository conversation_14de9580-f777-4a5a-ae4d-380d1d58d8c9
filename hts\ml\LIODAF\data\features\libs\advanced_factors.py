
"""
高级因子模块
包含订单簿深度、斜率、流量等高级微观结构因子
@author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory




def calculate_vol_imbalance(data: pd.DataFrame) -> pd.Series:
    
    total_bid_vol = [f'BidVol{i}' for i in range(1, 6) if f'BidVol{i}' in data.columns]
    total_ask_vol = [f'AskVol{i}' for i in range(1, 6) if f'AskVol{i}' in data.columns]
    return (total_bid_vol - total_ask_vol) / (total_bid_vol + total_ask_vol)


factor_manager.register_factor(Factor(
    name="vol_imbalance",
    category=FactorCategory.BASIC,
    description="总买卖委托量不平衡度",
    calculation=calculate_vol_imbalance,
    dependencies=["total_bid_vol", "total_ask_vol"],
    source="lining"
))


def calculate_volume_intensity(data: pd.DataFrame) -> pd.Series:
    
    if 'spread' not in data.columns:
        spread = data['AskPrice1'] - data['BidPrice1']
    else:
        spread = data['spread']
    return data['tradedVol'] / spread


factor_manager.register_factor(Factor(
    name="volume_intensity",
    category=FactorCategory.ADVANCED,
    description="交易量与价差的比率，反映交易强度",
    calculation=calculate_volume_intensity,
    dependencies=["tradedVol", "AskPrice1", "BidPrice1"],
    source="lining"
))




def calc_depth(df):
    
    depth = df['BidPrice1'] * df['BidVol1'] + df['AskPrice1'] * df['AskVol1'] + df['BidPrice2'] * df['BidVol2'] + df[
        'AskPrice2'] * df['AskVol2']
    return depth


factor_manager.register_factor(Factor(
    name="depth",
    category=FactorCategory.ADVANCED,
    description="订单簿深度",
    calculation=calc_depth,
    dependencies=["BidPrice1", "BidVol1", "AskPrice1", "AskVol1", "BidPrice2", "BidVol2", "AskPrice2", "AskVol2"],
    source="lining-zh/291948456/2486930660"
))




def calc_slope(df):
    
    midvol = (df['BidVol1'] + df['AskVol1']) / 2
    midprice = (df['BidPrice1'] + df['AskPrice1']) / 2
    slope_bid = ((df['BidVol1'] / midvol) - 1) / abs((df['BidPrice1'] / midprice) - 1) + (
            (df['BidVol2'] / df['BidVol1']) - 1) / abs((df['BidPrice2'] / df['BidPrice1']) - 1)
    slope_ask = ((df['AskVol1'] / midvol) - 1) / abs((df['AskPrice1'] / midprice) - 1) + (
            (df['AskVol2'] / df['AskVol1']) - 1) / abs((df['AskPrice2'] / df['AskPrice1']) - 1)
    return abs(slope_bid - slope_ask)


factor_manager.register_factor(Factor(
    name="slope2",
    category=FactorCategory.ADVANCED,
    description="订单簿斜率不平衡度",
    calculation=lambda data: calc_slope(data),
    dependencies=["BidPrice1", "BidVol1", "AskPrice1", "AskVol1", "BidPrice2", "BidVol2", "AskPrice2", "AskVol2"],
    source="lining-zh/291948456/2486930660"
))




def calc_ofi(df):
    
    a = df['BidVol1']*np.where(df['BidPrice1'].diff()>=0,1,0)
    b = df['BidVol1'].shift()*np.where(df['BidPrice1'].diff()<=0,1,0)
    c = df['AskVol1']*np.where(df['AskPrice1'].diff()<=0,1,0)
    d = df['AskVol1'].shift()*np.where(df['AskPrice1'].diff()>=0,1,0)
    return a - b - c + d

factor_manager.register_factor(Factor(
    name="ofi2",
    category=FactorCategory.ADVANCED,
    description="订单流不平衡",
    calculation=lambda data: calc_ofi(data),
    dependencies=["BidVol1", "BidPrice1", "AskVol1", "AskPrice1"],
    source="lining-zh/291948456/2486930660|diy"
))



def calc_wss12(df):
    
    ask = (df['AskPrice1'] * df['AskVol1'] + df['AskPrice2'] * df['AskVol2'])/(df['AskVol1']+df['AskVol2'])
    bid = (df['BidPrice1'] * df['BidVol1'] + df['BidPrice2'] * df['BidVol2'])/(df['BidVol1']+df['BidVol2'])
    midprice = (df['AskPrice1'] + df['BidPrice1']) / 2
    return (ask - bid) / midprice

factor_manager.register_factor(Factor(
    name="wss12",
    category=FactorCategory.ADVANCED,
    description="加权标准化价差, 计算方法: (加权ask - 加权bid) / 中间价",
    calculation=lambda data: calc_wss12(data),
    dependencies=["AskPrice1", "AskVol1", "BidPrice1", "BidVol1", "AskPrice2", "AskVol2", "BidPrice2", "BidVol2"],
    source="lining-zh/291948456/2486930660|diy"
))


def calculate_accumulated_price_spread(data: pd.DataFrame) -> pd.Series:
    
    return sum(data[f"AskPrice{i}"] - data[f"BidPrice{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_price_spread",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖价差的累计值",
    calculation=calculate_accumulated_price_spread,
    dependencies=[f"AskPrice{i}" for i in range(1, 6)] + [f"BidPrice{i}" for i in range(1, 6)],
    source="lining-orderbook-dynamic-features v5"
))



def calculate_accumulated_volume_spread(data: pd.DataFrame) -> pd.Series:
    
    return sum(data[f"AskVol{i}"] - data[f"BidVol{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_volume_spread",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖量差异的累计值",
    calculation=calculate_accumulated_volume_spread,
    dependencies=[f"AskVol{i}" for i in range(1, 6)] + [f"BidVol{i}" for i in range(1, 6)],
    source="lining-orderbook-dynamic-features v5"
))


def calculate_volume_intensity(data: pd.DataFrame) -> pd.Series:
    
    if 'spread' not in data.columns:
        spread = data['AskPrice1'] - data['BidPrice1']
    else:
        spread = data['spread']
    return data['tradedVol'] / spread


factor_manager.register_factor(Factor(
    name="volume_intensity",
    category=FactorCategory.ADVANCED,
    description="交易量与价差的比率，反映交易强度",
    calculation=calculate_volume_intensity,
    dependencies=["tradedVol", "AskPrice1", "BidPrice1"],
))


def total_turnover(data: pd.DataFrame) -> pd.Series:
    
    p1 = data['AskPrice1'] * data['AskVol1'] + data['BidPrice1'] * data['BidVol1']
    p2 = data['AskPrice2'] * data['AskVol2'] + data['BidPrice2'] * data['BidVol2']
    return p2 - p1


factor_manager.register_factor(Factor(
    name="total_turnover",
    category=FactorCategory.DIY,
    description="总成交量",
    calculation=total_turnover,
    dependencies=["AskPrice1", "AskVol1", "BidPrice1", "BidVol1", "AskPrice2", "AskVol2", "BidPrice2", "BidVol2"],
    source="lining-zh/291948456/answer/2486930660"
))