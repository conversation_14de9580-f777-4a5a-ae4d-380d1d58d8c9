# -*- coding: utf-8 -*-
"""
Created on Sun Apr  6 21:52:45 2025

@author: admin
"""

import pandas as pd
import pulp

# 生成候选价格：取两个时段的 bidprice1 和 askprice1 的最小值作为起点，最大值作为终点，每隔 price_step 生成一个价格
def generate_candidate_prices(prev_data, curr_data, price_step=0.25):
    min_p = min(prev_data['BidPrice1'], prev_data['AskPrice1'],
                curr_data['BidPrice1'], curr_data['AskPrice1'])
    max_p = max(prev_data['BidPrice1'], prev_data['AskPrice1'],
                curr_data['BidPrice1'], curr_data['AskPrice1'])
    prices = []
    p = min_p
    while p <= max_p + 1e-6:
        prices.append(round(p, 2))
        p += price_step
    return sorted(set(prices))  # 去重并排序

# 从前一时刻数据中给定一个价格，找到它对应的挂单量（遍历 bidprice1~bidprice5 和 askprice1~askprice5）
def get_order_volume_from_prev(price, prev_data):
    for i in range(1, 6):
        if abs(price - prev_data[f'BidPrice{i}']) < 1e-6:
            return prev_data[f'BidVol{i}']
        if abs(price - prev_data[f'AskPrice{i}']) < 1e-6:
            return prev_data[f'AskVol{i}']
    return 0

def solve_trade_allocation(prev_data, curr_data, lastprice, price_step=0.1, contract_multiplier=1000):
    """
    用线性规划求解成交分布，确保成交价格连续分布，并保证在 lastprice 的价格上成交量至少为 1
    参数:
      prev_data: 上一时刻数据，包含 'Volume', 'TotalValueTraded', 'time' 等
      curr_data: 当前时刻数据，包含 'Volume', 'TotalValueTraded', 'time' 等
      lastprice: 最近成交价，用于约束 lastprice 层至少成交 1
      price_step: 生成候选价格的步长
      contract_multiplier: 合约乘数，用于金额单位换算

    要求:
      1. 成交总量等于 diff_volume（当前成交量 - 上一时刻成交量）
      2. 成交总金额等于 diff_value（当前成交市值 - 上一时刻成交市值），计算金额时需使用 contract_multiplier
      3. 对候选价格中最小与最大价格，根据上一时刻挂单量作约束
      4. 成交分布必须连续：若候选价格序列两端成交，则中间所有价格都必须成交（避免 X0X 分布）
      5. 在 lastprice 处必须至少成交 1 手（若 lastprice 在候选价格内）
    """
    # 计算成交量及成交市值的差值
    diff_volume = curr_data['Volume'] - prev_data['Volume']
    diff_value = curr_data['TotalValueTraded'] - prev_data['TotalValueTraded']

    if diff_volume == 0:
        return 0

    # 计算平均成交价格
    avg_price = diff_value / (diff_volume * contract_multiplier)
    #print(f"diff_volume: {diff_volume}, diff_value: {diff_value}, avg_price: {avg_price}")

    # 生成候选成交价格，需根据实际业务逻辑实现该函数
    candidate_prices = generate_candidate_prices(prev_data, curr_data, price_step)
    if not candidate_prices:
        return 0

    # 建立 LP 模型
    prob = pulp.LpProblem("TradeAllocation", pulp.LpMinimize)

    # 变量定义：在候选价格 p 上的成交量（整数变量）
    x = {p: pulp.LpVariable(f"x_{p}", lowBound=0, cat='Integer') for p in candidate_prices}
    # y[p] 二元变量，若在价格 p 上成交则为 1，否则为 0
    y = {p: pulp.LpVariable(f"y_{p}", cat='Binary') for p in candidate_prices}

    # 关联变量: 确保若在价格 p 上有成交（x[p]>0），则 y[p] 必须为 1
    M = diff_volume  # M 设置为 diff_volume 足够大
    for p in candidate_prices:
        prob += x[p] <= M * y[p], f"Link_{p}"

    # 强连续性约束：
    # 对候选价格中任意两个相隔至少2个位置的价格，若两端均有成交，则中间必须全部成交
    n = len(candidate_prices)
    for i in range(n):
        for j in range(i + 2, n):
            for k in range(i + 1, j):
                prob += y[candidate_prices[i]] + y[candidate_prices[j]] - 1 <= y[candidate_prices[k]], \
                        f"Contiguous_{candidate_prices[i]}_{candidate_prices[j]}_{candidate_prices[k]}"

    # 约束1：成交总量必须等于 diff_volume
    prob += pulp.lpSum(x[p] for p in candidate_prices) == diff_volume, "TotalVolume"

    # 约束2：成交总金额必须等于 diff_value
    # 注意：金额 = 成交量 * 价格 * contract_multiplier
    prob += pulp.lpSum(x[p] * p * contract_multiplier for p in candidate_prices) == diff_value, "TotalValue"

    # 对候选价格中的最小和最大价格，根据上一时刻挂单量设定边界约束
    min_price = candidate_prices[0]
    max_price = candidate_prices[-1]
    vol_min = get_order_volume_from_prev(min_price, prev_data)
    vol_max = get_order_volume_from_prev(max_price, prev_data)
    prob += x[min_price] <= vol_min, "MinPriceVolume"
    prob += x[max_price] <= vol_max, "MaxPriceVolume"

    # 新增约束：在 lastprice 上至少成交 1 手
    if lastprice in candidate_prices:
        prob += x[lastprice] >= 1, "LastPriceConstraint"
    else:
        # 如果 lastprice 不在候选价格内，根据业务逻辑可以选择将 lastprice 加入候选价格列表
        # 此处也可选择找到与 lastprice 最接近的候选价格并约束其成交量
        # 例如：
        closest_price = min(candidate_prices, key=lambda p: abs(p - lastprice))
        prob += x[closest_price] >= 1, "ClosestToLastPriceConstraint"

    # 目标函数：最小化使用的价格层数，鼓励成交集中在连续区间内
    prob += pulp.lpSum(y[p] for p in candidate_prices), "MinimizeUsedPrices"

    # 求解模型
    status = prob.solve()
    if pulp.LpStatus[status] == 'Optimal':
        solution = {p: int(x[p].varValue) for p in candidate_prices}
        return {
            'status': pulp.LpStatus[status],
            'diff_volume': diff_volume,
            'avg_price': avg_price,
            'candidate_prices': candidate_prices,
            'allocation': solution
        }
    else:
        print("首次求解失败，扩大价格区间重新尝试")

        # 多轮扩大 price 范围后重试（最多两轮，每轮上下各加 5 个 tick）
        for expansion_round in range(1, 3):
            tick_expand = expansion_round * 5 * price_step
            new_min = min(candidate_prices) - tick_expand
            new_max = max(candidate_prices) + tick_expand
            extended_prices = [round(new_min + i * price_step, 2) for i in range(int((new_max - new_min) / price_step) + 1)]
            extended_prices = sorted(set(extended_prices))

            # 重建模型
            prob = pulp.LpProblem("TradeAllocationExpanded", pulp.LpMinimize)
            x = {p: pulp.LpVariable(f"x_{p}", lowBound=0, cat='Integer') for p in extended_prices}
            y = {p: pulp.LpVariable(f"y_{p}", cat='Binary') for p in extended_prices}
            M = diff_volume
            for p in extended_prices:
                prob += x[p] <= M * y[p]

            for i in range(len(extended_prices)):
                for j in range(i + 2, len(extended_prices)):
                    for k in range(i + 1, j):
                        prob += y[extended_prices[i]] + y[extended_prices[j]] - 1 <= y[extended_prices[k]]

            prob += pulp.lpSum(x[p] for p in extended_prices) == diff_volume
            prob += pulp.lpSum(x[p] * p * contract_multiplier for p in extended_prices) == diff_value

            if lastprice in extended_prices:
                prob += x[lastprice] >= 1
            else:
                closest_price = min(extended_prices, key=lambda p: abs(p - lastprice))
                prob += x[closest_price] >= 1

            prob += pulp.lpSum(y[p] for p in extended_prices)

            status = prob.solve()
            if pulp.LpStatus[status] == 'Optimal':
                solution = {p: int(x[p].varValue) for p in extended_prices}
                return {
                    'status': f"ExpandedSuccess_round{expansion_round}",
                    'diff_volume': diff_volume,
                    'avg_price': avg_price,
                    'candidate_prices': extended_prices,
                    'allocation': solution
                }

        # 最后兜底策略：直接用 avg_price 左右各 4 tick 分配成交
        print("最终兜底：用均价附近 tick 分布成交")
        base = round(avg_price / price_step) * price_step
        prices = [round(base + i * price_step, 2) for i in range(-4, 5)]
        allocation = {p: diff_volume // len(prices) for p in prices}
        remainder = diff_volume - sum(allocation.values())
        for p in prices[:remainder]:  # 补足余数
            allocation[p] += 1

        return {
            'status': 'Fallback',
            'diff_volume': diff_volume,
            'avg_price': avg_price,
            'candidate_prices': prices,
            'allocation': allocation
        }
        return None

# 主程序：读取 CSV 数据并对每对连续行情数据做求解
def main_loop(csv_file, price_step=0.1, contract_multiplier=1000):
    df = pd.read_csv(csv_file,dtype={'TotalValueTraded':"Int64"})
    #print(df.columns.tolist())
    df.sort_values(by='time', inplace=True)
    
    results = []
    # 这里需要确保 CSV 中包含 lastprice 字段，或者用当前时刻的中间价作为 lastprice
    for i in range(1, len(df)):
        prev_data = df.iloc[i-1].to_dict()
        #print(prev_data,"prev")
        curr_data = df.iloc[i].to_dict()
        #print(curr_data,"curr")
        lastprice = curr_data.get('LastPrice', (curr_data['BidPrice1'] + curr_data['AskPrice1'])/2)
        result = solve_trade_allocation(prev_data, curr_data, lastprice, price_step, contract_multiplier)
        # 当没有成交时，result 为 0
        if result == 0:
            results.append({
                'start_time': prev_data['time'],
                'end_time': curr_data['time'],
                'diff_volume': 0,
                'avg_price': None,
                'candidate_prices': [],
                'allocation': 0
            })
        elif result is not None:
            result['start_time'] = prev_data['time']
            result['end_time'] = curr_data['time']
            results.append(result)
    return results

if __name__ == "__main__":
    csv_file = "resulttimemd_20250331_cffex_multicast292.csv"
    m = pd.read_csv(csv_file)
    results = main_loop(csv_file, price_step=0.2, contract_multiplier=200)
    df_result = pd.DataFrame([
        {
            'start_time': r['start_time'],
            'end_time': r['end_time'],
            'diff_volume': r['diff_volume'],
            'avg_price': r['avg_price'],
            'candidate_prices': '|'.join(map(str, r['candidate_prices'])) if isinstance(r['candidate_prices'], list) else '',
            'allocation': '|'.join(f"{k}:{v}" for k, v in r['allocation'].items()) if isinstance(r['allocation'], dict) else r['allocation']
        }
        for r in results
    ])

    # 保存到 CSV 文件
    output_path = "trade_allocation_result6.csv"
    df_result.to_csv(output_path, index=False)
    print(f"\n✅ 成功保存到: {output_path}")