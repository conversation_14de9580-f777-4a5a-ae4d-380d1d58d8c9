import datetime
import pandas as pd
from PySide6.QtWidgets import (QVBoxLayout, QHBoxLayout, QWidget, QPushButton,
                               QFileDialog, QSlider, QLineEdit, QLabel, QComboBox, QTableView, QSplitter,
                               QCheckBox, QStyle, QTabWidget)
from PySide6.QtCore import Qt, QSortFilterProxyModel, QTimer, QSize, Signal, QThread
from PySide6.QtGui import QIntValidator, QStandardItem, QKeySequence, QShortcut, QStandardItemModel
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas, \
    NavigationToolbar2QT as NavigationToolbar
from PySide6.QtWidgets import QMainWindow
from matplotlib.figure import Figure
import matplotlib
import numpy as np
from custom_widgets import CheckableComboBox
import matplotlib.pyplot as plt
import sys
import os
import time

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

from pnlback.volsym.back.get_ws import login_qt, logout
from pnlback.volsym.back.data_processor import DataManager
from pnlback.volsym.back.data_proc_ui import DataUpdateThread
from pnlback.volsym.ui import config

matplotlib.use('Qt5Agg')


class BaseVolatilityCurveUI(QMainWindow):
    tradetime00 = [['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']]
    debug = False

    def __init__(self):
        super().__init__()
        self.initialize_variables()
        self.setWindowTitle("TEngine")
        self.setWindowIcon(self.style().standardIcon(QStyle.SP_DialogHelpButton))
        self.setGeometry(100, 100, 1200, 1280)
        self.setup_ui()
        self.setup_connections()
        self.setup_shortcuts()

        # 添加中文字体支持
        # matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']  # 优先使用中文字体
        matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    def initialize_variables(self):
        self.annotation = None
        self.highlight_point = None
        self.timer = QTimer()
        self.current_direction = 0
        self.is_continuous = False
        self.press_time = None
        self.decimal_places = 4
        self.show_bar_chart = True
        self.normalize_by_edgepnl = False
        self._main_plot_drag_start = None
        self.pnl_data = {}
        self.ax_spot = None
        self.update_tradpnl_enabled = True
        self.annotation_enabled = False  # 添加标注开关状态变量
        self.live_mode = False  # 添加实时模式状态变量
        self.data_subscriber = None  # 移除定时器，添加订阅者变量

        self.custom_r = 0
        self.use_new_iv = False
        # 修改配置参数，添SVI拟合开关
        self.curve_parmames = config.volparams
        self.config = config.volconfig

        # 初始化数据管理器
        self.data_manager = DataManager()
        # self.data_manager.data_updated.connect(self.update_plot)
        self.data_manager.updateparams(self.custom_r, self.use_new_iv, self.config)

    def is_dark_mode(self):
        # 这里需要根据具体的操作系统来实现
        # 以下是一个示例,实际使用时可能需要根据不同操作系统进行调整
        if sys.platform == "darwin":  # macOS
            import subprocess
            cmd = 'defaults read -g AppleInterfaceStyle'
            p = subprocess.Popen(cmd, stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE, shell=True)
            return p.communicate()[0].decode("utf-8").strip() == 'Dark'
        elif sys.platform == "win32":  # Windows
            import winreg
            try:
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                                     r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize")
                value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
                return value == 0
            except WindowsError:
                return False
        else:  # 其他系统,默认为非暗黑模式
            return False

    def update_window_title(self, current_time):
        original_title = "TEngine"  # 假设这是原始标题
        new_title = f"{original_title} - Current Time: {current_time}"
        self.setWindowTitle(new_title)

    def setup_ui(self):
        if self.is_dark_mode():
            plt.style.use('dark_background')
        else:
            pass
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 创建顶部控制栏
        top_layout = QHBoxLayout()

        # 文件选择按
        self.file_button = QPushButton()
        self.file_button.setIcon(self.style().standardIcon(QStyle.SP_FileDialogContentsView))
        self.file_button.setToolTip("选择数据文件")
        top_layout.addWidget(self.file_button)

        # 月份选择
        month_label = QLabel("选择月份:")
        top_layout.addWidget(month_label)
        self.month_combo = CheckableComboBox()
        self.month_combo.setEnabled(False)
        self.month_combo.setMinimumWidth(200)  # 增加宽度以显示更多内容
        top_layout.addWidget(self.month_combo)

        # 添加无风险利率输入框
        r_label = QLabel("r:")
        top_layout.addWidget(r_label)
        self.r_input = QLineEdit()
        self.r_input.setPlaceholderText("rf")
        self.r_input.setText("0")  # 设置初始值为 "0"
        self.r_input.textChanged.connect(self.update_custom_r)
        self.r_input.setFixedWidth(50)  # 设置固定宽度
        top_layout.addWidget(self.r_input)

        # 添加使用原始IV的复选框，紧接着 rf 输入框
        self.use_new_iv_checkbox = QCheckBox("newIV")
        self.use_new_iv_checkbox.setChecked(False)  # 设置默认不选中
        self.use_new_iv_checkbox.stateChanged.connect(self.toggle_use_original_iv)
        top_layout.addWidget(self.use_new_iv_checkbox)

        # 时间控制
        self.prev_button = QPushButton()
        self.prev_button.setIcon(self.style().standardIcon(QStyle.SP_MediaSeekBackward))
        self.prev_button.setToolTip("上一个")

        self.next_button = QPushButton()
        self.next_button.setIcon(self.style().standardIcon(QStyle.SP_MediaSeekForward))
        self.next_button.setToolTip("下一个")

        self.time_input = QLineEdit()
        self.time_input.setPlaceholderText("间隔")
        self.time_input.setText("1")
        self.time_input.setValidator(QIntValidator(1, 3600))
        self.time_unit_label = QLabel("秒")
        for widget in (self.prev_button, self.next_button, self.time_input):
            widget.setFixedSize(QSize(30, 30))
        top_layout.addWidget(self.prev_button)
        top_layout.addWidget(self.next_button)
        top_layout.addWidget(self.time_input)
        top_layout.addWidget(self.time_unit_label)

        top_layout.addStretch(1)  # 添加弹性空间，将工具栏推到最右边

        # 创建matplotlib画布和工具栏
        self.figure = Figure(figsize=(16, 8))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        top_layout.addWidget(self.canvas)  # 将工具栏添到顶部布局的最右边

        # 在布局中添加小数位数选择框
        self.decimal_combo = QComboBox()
        self.decimal_combo.addItems(['0', '1', '2', '3', '4', '5', '6'])
        self.decimal_combo.setCurrentText('4')  # 默认选择4位小数
        self.decimal_combo.currentTextChanged.connect(self.update_decimal_places)
        top_layout.addWidget(self.decimal_combo)

        # 添加加载成交记录按钮
        self.load_trade_button = QPushButton()
        self.load_trade_button.setIcon(self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
        self.load_trade_button.setToolTip("加载成交记录")
        self.load_trade_button.clicked.connect(self.load_trade_data)
        top_layout.addWidget(self.load_trade_button)

        # 添加分开显示 call 和 put 的复选框
        self.separate_call_put_checkbox = QCheckBox("分开显示 Call 和 Put")
        self.separate_call_put_checkbox.setChecked(False)  # 默认不选中
        self.separate_call_put_checkbox.stateChanged.connect(self.update_plot)
        top_layout.addWidget(self.separate_call_put_checkbox)

        # 添加柱图显示内容选择框
        self.bar_display_combo = QComboBox()
        self.bar_display_combo.addItems(['TV Diff',"vol_diff", "持仓量", "Vega加权", "Delta加权"])
        self.bar_display_combo.currentIndexChanged.connect(self.update_plot)
        top_layout.addWidget(self.bar_display_combo)

        # 添加显示柱状图的复选框
        self.show_bar_checkbox = QCheckBox("显示柱状图")
        self.show_bar_checkbox.setChecked(True)
        self.show_bar_checkbox.stateChanged.connect(self.toggle_bar_chart)
        top_layout.addWidget(self.show_bar_checkbox)

        # 在顶部添加配置按钮
        self.config_button = QPushButton("FIT配置", self)
        self.config_button.clicked.connect(self.show_config_dialog)
        # 将按钮添加
        top_layout.addWidget(self.config_button)

        # 在文件选择按钮边加实时模式切换按
        self.live_mode_button = QPushButton("实时模式")
        self.live_mode_button.setCheckable(True)  # 使按钮可切换
        self.live_mode_button.clicked.connect(self.toggle_live_mode)
        top_layout.addWidget(self.live_mode_button)

        main_layout.addLayout(top_layout)

        # 创建一个垂直分器
        self.splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(self.splitter, 1)  # 给 splitter 一个伸展因子

        # 创建主图区域
        main_chart_widget = QWidget()
        main_chart_layout = QVBoxLayout(main_chart_widget)

        # 添加matplotlib工具栏并默认隐藏
        self.toolbar = NavigationToolbar(self.canvas, main_chart_widget)
        self.toolbar.hide()  # 默认隐藏
        main_chart_layout.addWidget(self.toolbar)
        main_chart_layout.addWidget(self.canvas)
        self.splitter.addWidget(main_chart_widget)
        self.toolbar_button = QPushButton("toolbar", self)
        self.toolbar_button.clicked.connect(self.toggle_toolbars)
        # 将按钮添加
        top_layout.addWidget(self.toolbar_button)

        # 在顶部工具栏中添加标注显示开关
        self.show_annotation_checkbox = QCheckBox("显示标注")
        self.show_annotation_checkbox.setChecked(True)  # 默认开启
        self.show_annotation_checkbox.stateChanged.connect(self.toggle_annotation)
        self.toolbar.addWidget(self.show_annotation_checkbox)

        # 创建折线图和表格区域
        self.tradpnl_tab_widget = QTabWidget()
        self.splitter.addWidget(self.tradpnl_tab_widget)

        # 创建折线图标签页
        tradpnl_widget = QWidget()
        tradpnl_layout = QVBoxLayout(tradpnl_widget)

        # 创建一个水平布局来放置复选框和其他控件
        tradpnl_controls_layout = QHBoxLayout()

        # 添加"只显示前后10分钟"的复选框
        self.show_recent_checkbox = QCheckBox("前后10分钟")
        self.show_recent_checkbox.stateChanged.connect(self.update_trade_data)
        tradpnl_controls_layout.addWidget(self.show_recent_checkbox)

        # 添加 Normalize by EdgePNL 复选框
        self.normalize_checkbox = QCheckBox("NormaPNL")
        self.normalize_checkbox.stateChanged.connect(self.toggle_normalize)
        tradpnl_controls_layout.addWidget(self.normalize_checkbox)

        # 添加PNL类型的复选框
        pnl_types = ['TradePNL', 'TDeltaPNL', 'TVegaPNL']  # 可以根据需要修改这个列表
        self.pnl_colors = ['b', 'g', 'r', 'c', 'm', 'y', 'k']  # 预定义的颜色列表
        for i, pnl_type in enumerate(pnl_types):
            checkbox = QCheckBox(pnl_type)
            checkbox.setChecked(True)
            tradpnl_controls_layout.addWidget(checkbox)
            self.pnl_data[pnl_type] = {
                'checkbox': checkbox,
                'color': self.pnl_colors[i % len(self.pnl_colors)],
                'line': None
            }
            checkbox.stateChanged.connect(self.update_trade_data)

        # 添加控制是否更新tradpnl图的复选框
        self.update_tradpnl_checkbox = QCheckBox("更新 TradPNL 图")
        self.update_tradpnl_checkbox.setChecked(True)
        self.update_tradpnl_checkbox.stateChanged.connect(self.on_update_tradpnl_changed)

        # 将复选框添加到 tradpnl_controls_layout
        tradpnl_controls_layout.addWidget(self.update_tradpnl_checkbox)

        # 添加个弹性空间，将控件推到左边
        tradpnl_controls_layout.addStretch(1)

        # 将水平布局添加到垂直布局中
        tradpnl_layout.addLayout(tradpnl_controls_layout)

        self.tradpnl_figure = Figure(figsize=(16, 5))
        self.tradpnl_canvas = FigureCanvas(self.tradpnl_figure)
        self.ax_tradpnl = self.tradpnl_figure.add_subplot(111)
        tradpnl_layout.addWidget(self.tradpnl_canvas)
        self.tradpnl_tab_widget.addTab(tradpnl_widget, "折线图")

        # 创建表格标签页
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)

        # 创建一个水平布局来放置搜索框和保存按钮
        search_save_layout = QHBoxLayout()

        # 添加搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索...")
        search_save_layout.addWidget(self.search_input)

        # 添加保存表格按钮
        self.save_table_button = QPushButton("保存表格", self)
        self.save_table_button.clicked.connect(self.save_table_data)
        search_save_layout.addWidget(self.save_table_button)

        # 将水平布局添加到表格布局中
        table_layout.addLayout(search_save_layout)

        # 添加表格视图
        self.table_view = QTableView()
        self.table_view.setSortingEnabled(True)
        table_layout.addWidget(self.table_view)

        self.tradpnl_tab_widget.addTab(table_widget, "数据表格")

        # 设置初始分割比例（例如，主图占75%，折线图和表格区域占25%）
        self.splitter.setSizes([650, 350])

        # 添加时间滑块
        self.time_slider = QSlider(Qt.Horizontal)
        self.time_slider.setEnabled(False)
        self.time_slider.setToolTip("")  # 初始化一个空的工具提示
        main_layout.addWidget(self.time_slider)

        # 创建底部布局，包含计算UI和信息标签
        bottom_layout = QHBoxLayout()

        # 添加计算UI
        calculation_widget = QWidget()
        calculation_layout = QHBoxLayout(calculation_widget)
        self.code1_combo = QComboBox()
        self.code2_combo = QComboBox()
        self.formula_input = QLineEdit()
        self.formula_input.setText("code1.sigma-code2.sigma")
        self.calculate_button = QPushButton("计算")
        self.info_label = QLabel()  # 使用 QLabel 替代 QLineEdit

        for combo in [self.code1_combo, self.code2_combo]:
            combo.setMinimumWidth(150)
            combo.setMaxVisibleItems(15)
            combo.view().setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        calculation_layout.addWidget(QLabel("代码1:"))
        calculation_layout.addWidget(self.code1_combo)
        calculation_layout.addWidget(QLabel("代码2:"))
        calculation_layout.addWidget(self.code2_combo)
        calculation_layout.addWidget(QLabel("公式:"))
        calculation_layout.addWidget(self.formula_input)
        calculation_layout.addWidget(self.decimal_combo)
        calculation_layout.addWidget(self.calculate_button)
        calculation_layout.addWidget(self.info_label)  # 将 info_label 添加到布局中
        bottom_layout.addWidget(calculation_widget)

        # 添加信息标签
        self.info_label = QLabel()
        bottom_layout.addWidget(self.info_label)

        main_layout.addLayout(bottom_layout)

    def setup_connections(self):
        self.file_button.clicked.connect(self.select_file)
        self.month_combo.view().pressed.connect(self.update_month_combo_state)
        self.time_slider.valueChanged.connect(self.on_time_slider_changed)
        self.prev_button.pressed.connect(lambda: self.start_adjust(-1))
        self.prev_button.released.connect(self.stop_adjust)
        self.next_button.pressed.connect(lambda: self.start_adjust(1))
        self.next_button.released.connect(self.stop_adjust)
        self.time_input.textChanged.connect(self.update_time_interval)
        self.search_input.textChanged.connect(self.filter_table)
        self.calculate_button.clicked.connect(self.calculate_formula)
        self.setup_tradpnl_mouse_events()
        self.setup_main_plot_mouse_events()
        self.tradpnl_figure.canvas.mpl_connect('pick_event', self.on_legend_pick)
        self.use_new_iv_checkbox.stateChanged.connect(self.toggle_use_original_iv)

    def select_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择数据文件", "", "Parquet Files (*.parquet);;All Files (*)")
        if file_path:
            """加载并处理数据"""
            if self.data_manager.load_and_process_data(file_path, self.live_mode):
                self.set_month_combo()
                self.update_all_plot()
                self.update_time_slider()
            else:
                self.statusBar().showMessage("据加载失败")

    def set_month_combo(self):
        self.month_combo.clear()
        model = self.month_combo.model()
        item = QStandardItem("所有月份")
        item.setCheckState(Qt.Checked)
        model.appendRow(item)

        for month in sorted(self.data_manager.data['exp'].unique()):
            if str(month).strip():
                item = QStandardItem(str(month))
                item.setCheckState(Qt.Unchecked)
                model.appendRow(item)

        self.month_combo.setEnabled(True)

    def update_month_combo_state(self):
        """更新current_data基于选择的月份"""
        if self.data_manager.data is None:
            return

        selected_months = self.get_checked_items(self.month_combo)
        self.data_manager.selected_months = selected_months
        self.data_manager.filter_data_by_months()
        self.data_manager.cal_current_data()

        self.update_all_plot()

    def update_all_plot(self):
        """更新y轴范围并触发重绘"""
        if self.data_manager.current_data is None:
            return

        sigma_values = self.data_manager.current_data['sigma'].dropna()
        if len(sigma_values) > 0:
            self.y_min = max(sigma_values.min() * 0.9, 0)
            self.y_max = min(sigma_values.max() * 1.1, 2)
        else:
            self.y_min, self.y_max = 0, 1

        if not self.live_mode:
            self.data_manager.current_time = self.data_manager.unique_times[self.time_slider.value()]
        self.data_manager.updateparams(self.custom_r, self.use_new_iv, self.config)
        self.update_code_combos(self.data_manager.current_data)
        self.update_plot()

    def update_time_slider(self):
        self.time_slider.setRange(0, len(self.data_manager.unique_times) - 1)
        self.time_slider.setValue(0)
        self.time_slider.setEnabled(True)
        self.update_slider_tooltip(0)  # 初始化工具提示

    def on_time_slider_changed(self, value):
        """处理时间滑块值变化的方法"""
        if self.data_manager.data is not None:
            self.data_manager.current_time = self.data_manager.unique_times[value]
            self.update_slider_tooltip(value)
            self.data_manager.cal_current_data()
            self.update_all_plot()  # 更新主图
            if self.update_tradpnl_enabled:
                self.update_tradpnl_plot()  # 更新交易PNL图

    def calculate_y_range(self, data):
        sigma_values = data['sigma'].dropna()
        if len(sigma_values) > 0:
            return max(sigma_values.min(), 0), min(sigma_values.max(), 2)
        return 0, 1

    def update_table(self):
        table_data = self.data_manager.current_data.sort_values(['exp', 'K']).copy()
        table_data = table_data.round(4)

        # 创建标准项模型
        model = QStandardItemModel()

        # 设置列标题
        model.setHorizontalHeaderLabels(table_data.columns)

        # 填充数据
        for row in range(len(table_data)):
            for col in range(len(table_data.columns)):
                item = QStandardItem(str(table_data.iloc[row, col]))
                model.setItem(row, col, item)

        # 设置代理模和表格视图
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setSourceModel(model)
        self.table_view.setModel(self.proxy_model)

    def start_adjust(self, direction):
        self.current_direction = direction
        self.is_continuous = False
        self.press_time = pd.Timestamp.now()
        self.timer.start(200)  # 开始计时，如果200ms内没有释放按钮，则认为是连续点击
        self.timer.timeout.connect(self.continuous_adjust)

    def stop_adjust(self):
        self.timer.stop()
        self.timer.timeout.disconnect()
        if not self.is_continuous:
            # 如果不是连续点击，执行单次调整
            self.adjust_time(self.current_direction)

    def continuous_adjust(self):
        self.is_continuous = True
        self.adjust_time(self.current_direction)

    def adjust_time(self, direction):
        if self.data_manager.data is None:
            return
        current_index = self.time_slider.value()
        max_index = len(self.data_manager.unique_times) - 1

        target_time = self.data_manager.current_time + pd.Timedelta(seconds=direction * self.time_interval)

        step = 1 if direction > 0 else -1

        for i in range(max_index + 1):
            new_index = (current_index + i * step) % (max_index + 1)
            new_time = self.data_manager.unique_times[new_index]
            condition = new_time >= target_time if direction > 0 else new_time <= target_time

            if condition and not self.data_manager.data[self.data_manager.data['time'] == new_time].empty:
                self.data_manager.current_time = new_time
                self.time_slider.setValue(new_index)
                return

        print("没有找到有数据的时间点")

    def update_time_interval(self):
        try:
            self.time_interval = max(1, int(self.time_input.text()))
        except ValueError:
            self.time_interval = 1
        self.time_input.setText(str(self.time_interval))
        if self.timer.isActive():
            self.timer.stop()
            self.timer.start(200)  # 重新启动定时器

    def filter_table(self, text):
        self.proxy_model.setFilterRegExp(text)
        self.proxy_model.setFilterKeyColumn(-1)

    @property
    def time_interval(self):
        return int(self.time_input.text() or 1)

    @time_interval.setter
    def time_interval(self, value):
        self.time_input.setText(str(value))

    def on_mouse_move(self, event):
        if self.data_manager.current_data is not None:
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                if self.annotation_enabled:
                    self.show_annotation(event)
                if x > max(self.data_manager.current_data['K']) * 1.1 or x < min(
                        self.data_manager.current_data['K']) * 0.9 or y > max(
                    self.data_manager.current_data['sigma']) * 1.1 or y < min(
                    self.data_manager.current_data['sigma']) * 0.9:
                    self.hide_annotation()
            else:
                self.hide_annotation()
        else:
            self.hide_annotation()

    def show_annotation(self, event):
        """显示标注"""
        x, y = event.xdata, event.ydata
        closest_point = min(self.data_manager.current_data.itertuples(),
                            key=lambda p: (p.K - x) ** 2 + (p.sigma - y) ** 2)
        annotation_text = (
            f"{closest_point.K:.2f}-{closest_point.exp} - {closest_point.delta:.2f}\n"
            f"{'code:':<10} {closest_point.code} - {closest_point.neg_code}\n"
            f"{'posi:':<10} {closest_point.posi:<8} - {closest_point.neg_posi:<8}\n"
            f"{'call Vol:':<10} {closest_point.bid_vol:.4f} - {closest_point.ask_vol:.4f}\n"
            f"{'put Vol:':<10} {closest_point.neg_bid_vol:.4f} - {closest_point.neg_ask_vol:.4f}\n"
            f"{'Sigma:':<10} {closest_point.sigma:.4f}"
        )

        # 计算点的大小和颜色
        point_size = 500  # 默认大小
        point_color = 'red'  # 默认颜色
        if hasattr(closest_point, 'model_vol'):
            annotation_text += f"\n{'MarketVol:':<10} {closest_point.market_sigma:.4f}\n"
            annotation_text += f"{'ModelVol:':<10} {closest_point.model_vol:.4f}\n"
            annotation_text += f"{'VolDiff:':<10} {closest_point.vol_diff:.4f}"

            # 获取当前y轴范围
            y_range = self.ax.get_ylim()[1] - self.ax.get_ylim()[0]
            # 直接使用vol_diff相对于y轴范围的比例来确定点的大小
            relative_size = abs(closest_point.vol_diff) / y_range
            # 将对大小转换为实际显示大小（放大10000倍)}
            point_size = min(relative_size * 10000, 500)  # Ensure maximum size
            point_color = 'red' if closest_point.vol_diff > 0 else 'green'
        else:
            if self.debug:
                print(closest_point)
            print('no annotation')

        # 如果在图表的侧边缘附近则将注释放在左边
        x_offset = -200 if event.xdata > max(self.data_manager.current_data['K']) * 0.7 else 20
        # 如果点在图表的上方边缘附近，则将注释放在下方
        y_offset = -50 if event.ydata > max(self.data_manager.current_data['sigma']) * 0.7 else 20
        if self.annotation:
            self.annotation.set_text(annotation_text)
            self.annotation.xy = (event.xdata, event.ydata)
            self.annotation.xytext = (x_offset, y_offset)
        else:
            self.annotation = self.ax.annotate(annotation_text, xy=(event.xdata, event.ydata),
                                               xytext=(x_offset, y_offset), textcoords="offset points",
                                               bbox=dict(boxstyle="round", fc="w", ec="0.5", alpha=0.7), fontsize=12,
                                               color='black',
                                               arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=0", lw=2),
                                               zorder=1000)

        if self.highlight_point:
            self.highlight_point.remove()
            self.highlight_point = None
        self.highlight_point = self.ax.scatter(closest_point.K, closest_point.sigma, color=point_color, s=point_size,
                                               alpha=0.6, zorder=999)

        self.ax.figure.canvas.draw_idle()

    def hide_annotation(self):
        """隐藏标注"""
        if self.annotation:
            self.annotation.remove()
            self.annotation = None
        if self.highlight_point:
            self.highlight_point.remove()
            self.highlight_point = None
            self.ax.figure.canvas.draw_idle()

    def on_scroll(self, event):
        if event.inaxes != self.ax:
            return

        cur_xlim = self.ax.get_xlim()
        cur_ylim = self.ax.get_ylim()
        cur_ylim2 = self.ax2.get_ylim()
        xdata = event.xdata
        ydata = event.ydata
        if event.button == 'up':
            scale_factor = 1 / 1.1
        elif event.button == 'down':
            scale_factor = 1.1
        else:
            scale_factor = 1

        new_width = (cur_xlim[1] - cur_xlim[0]) * scale_factor
        new_height = (cur_ylim[1] - cur_ylim[0]) * scale_factor
        new_height2 = (cur_ylim2[1] - cur_ylim2[0]) * scale_factor

        relx = (cur_xlim[1] - xdata) / (cur_xlim[1] - cur_xlim[0])
        rely = (cur_ylim[1] - ydata) / (cur_ylim[1] - cur_ylim[0])
        rely2 = (cur_ylim2[1] - ydata) / (cur_ylim2[1] - cur_ylim2[0])

        new_xlim = [xdata - new_width * (1 - relx), xdata + new_width * relx]
        new_ylim = [ydata - new_height * (1 - rely), ydata + new_height * rely]
        new_ylim2 = [ydata - new_height2 * (1 - rely2), ydata + new_height2 * rely2]

        self.ax.set_xlim(new_xlim)
        self.ax.set_ylim(new_ylim)
        self.ax2.set_ylim(new_ylim2)

        # 确保两个轴的 x 轴范围保持一致
        self.ax2.set_xlim(new_xlim)

        # 重新绘制图形
        self.canvas.draw_idle()

        # 更新 y 轴范围
        self.y_min, self.y_max = new_ylim

    def update_code_combos(self, data=None):
        if data is None:
            data = self.data_manager.data
        all_codes = sorted(data['code'].unique())
        all_codes_str = [str(code) for code in all_codes]
        self.code1_combo.clear()
        self.code2_combo.clear()
        self.code1_combo.addItems(all_codes_str)
        self.code2_combo.addItems(all_codes_str)

    def calculate_formula(self):
        code1 = self.code1_combo.currentText()
        code2 = self.code2_combo.currentText()
        formula = self.formula_input.text()

        if not code1 or not code2 or not formula:
            return

        current_data = self.data_manager.data[self.data_manager.data['time'] == self.data_manager.current_time]

        if code1 not in current_data['code'].values or code2 not in current_data['code'].values:
            self.update_info_text("选择的代码在当前时间点不存在")
            return

        value1 = current_data[current_data['code'] == code1].iloc[0]
        value2 = current_data[current_data['code'] == code2].iloc[0]

        try:
            result = eval(formula, {"__builtins__": None}, {
                "code1": value1,
                "code2": value2,
                "abs": abs,
                "max": max,
                "min": min
            })
            formatted_result = f"{result:.{self.decimal_places}f}"
            self.update_info_text(f"计算结果: {formatted_result}")
        except Exception as e:
            self.update_info_text(f"计算错误: {str(e)}")

    def update_info_text(self, text):
        self.info_label.setText(text)
        # 设置字体大小
        font = self.info_label.font()
        font.setPointSize(12)
        self.info_label.setFont(font)

    def setup_shortcuts(self):
        # 为"上一个"按钮添加Ctrl+左箭头快捷键
        self.prev_shortcut = QShortcut(QKeySequence("Ctrl+Left"), self)
        self.prev_shortcut.activated.connect(lambda: self.adjust_time(-1))

        # 为"下一个"按钮添加Ctrl+右箭头快捷键
        self.next_shortcut = QShortcut(QKeySequence("Ctrl+Right"), self)
        self.next_shortcut.activated.connect(lambda: self.adjust_time(1))

        # 添加工具栏显示/隐藏快捷键 (Ctrl+T)
        self.toolbar_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        self.toolbar_shortcut.activated.connect(self.toggle_toolbars)

    def toggle_toolbars(self):
        """切换工具栏的显示/隐藏状态"""
        if self.toolbar.isVisible():
            self.toolbar.hide()
        else:
            self.toolbar.show()

    def update_decimal_places(self, value):
        self.decimal_places = int(value)

    def update_custom_r(self, text):
        try:
            self.custom_r = float(text) if text else None
            self.update_plot()  # 当 r 改变时更新图表
        except ValueError:
            # 如果输入的不是有效的浮点数，则忽略
            pass

    def toggle_use_original_iv(self, state):
        self.use_new_iv = bool(state)
        print(f"IV calculation mode: {'new' if self.use_new_iv else 'original'}")
        self.update_plot()

    def load_trade_data(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择成交记录文件", "",
                                                   "parquet Files (*.parquet);;All Files (*)")
        if file_path:
            self.data_manager.load_trade_data(file_path)
            self.update_trade_data()

    def update_trade_data(self):
        if self.data_manager.trade_data is not None:
            self.update_plot()  # 更新图表以显示成交记录
            self.initialize_tradpnl_plot(self.data_manager._processed_data, self.data_manager.tradetime_num)
            self.update_tradpnl_plot()  # 添加这行来更新tradpnl图

    def toggle_bar_chart(self, state):
        self.show_bar_chart = state == Qt.Checked
        self.update_plot()

    def on_legend_pick(self, event):
        legline = event.artist
        pnl_type = legline.get_label().split(':')[0].strip()
        origline = self.pnl_data[pnl_type]['line']
        visible = not origline.get_visible()
        origline.set_visible(visible)
        legline.set_alpha(1.0 if visible else 0.2)
        self.tradpnl_canvas.draw()

    def on_main_legend_pick(self, event):
        # 获取被点击的图例项
        legline = event.artist
        label = legline.get_label()

        # 尝试从标签中提取到期日
        exp = label.split(': ')[-1] if ': ' in label else label

        try:
            exp_key = int(exp)
        except ValueError:
            print(f"Warning: Unable to convert exp '{exp}' to integer")
            return

        if exp_key in self.main_lines:
            # 更新与该到期日相关的所有图形元素的可见性
            for element in self.ax.get_children():
                if hasattr(element, 'get_label') and element.get_label() == label:
                    element.set_visible(not element.get_visible())
                    if element.get_visible():
                        legline.set_alpha(1.0)
                    else:
                        legline.set_alpha(0.2)

            self.canvas.draw()
        else:
            print(f"Warning: No line found for exp '{exp_key}'")

    def toggle_normalize(self, state):
        self.normalize_by_edgepnl = state == Qt.Checked
        self.update_trade_data()  # 加这行来更新 tradpnl 图

    def setup_tradpnl_mouse_events(self):
        self.tradpnl_canvas.mpl_connect('scroll_event', self.on_tradpnl_scroll)
        self.tradpnl_canvas.mpl_connect('button_press_event', self.on_tradpnl_press)
        self.tradpnl_canvas.mpl_connect('button_release_event', self.on_tradpnl_release)
        self.tradpnl_canvas.mpl_connect('motion_notify_event', self.on_tradpnl_motion)
        self._tradpnl_drag_start = None

    def on_tradpnl_scroll(self, event):
        if event.inaxes != self.ax_tradpnl:
            return

        cur_xlim = self.ax_tradpnl.get_xlim()
        cur_ylim = self.ax_tradpnl.get_ylim()
        xdata = event.xdata
        ydata = event.ydata
        if event.button == 'up':
            scale_factor = 1 / 1.1
        elif event.button == 'down':
            scale_factor = 1.1
        else:
            scale_factor = 1

        new_width = (cur_xlim[1] - cur_xlim[0]) * scale_factor
        new_height = (cur_ylim[1] - cur_ylim[0]) * scale_factor

        relx = (cur_xlim[1] - xdata) / (cur_xlim[1] - cur_xlim[0])
        rely = (cur_ylim[1] - ydata) / (cur_ylim[1] - cur_ylim[0])

        self.ax_tradpnl.set_xlim([xdata - new_width * (1 - relx), xdata + new_width * relx])
        self.ax_tradpnl.set_ylim([ydata - new_height * (1 - rely), ydata + new_height * rely])

        self.tradpnl_canvas.draw_idle()

    def on_tradpnl_press(self, event):
        if event.inaxes != self.ax_tradpnl:
            return
        self._tradpnl_drag_start = (event.xdata, event.ydata)

    def on_tradpnl_release(self, event):
        self._tradpnl_drag_start = None

    def on_tradpnl_motion(self, event):
        if event.inaxes != self.ax_tradpnl or self._tradpnl_drag_start is None:
            return

        dx = event.xdata - self._tradpnl_drag_start[0]
        dy = event.ydata - self._tradpnl_drag_start[1]

        cur_xlim = self.ax_tradpnl.get_xlim()
        cur_ylim = self.ax_tradpnl.get_ylim()

        self.ax_tradpnl.set_xlim(cur_xlim - dx)
        self.ax_tradpnl.set_ylim(cur_ylim - dy)

        self._tradpnl_drag_start = (event.xdata, event.ydata)  # 更新拖拽起始点

        self.tradpnl_canvas.draw_idle()

    def update_slider_tooltip(self, value):
        if self.data_manager.unique_times is not None and 0 <= value < len(self.data_manager.unique_times):
            time = self.data_manager.unique_times[value]
            self.time_slider.setToolTip(datetime.datetime.fromtimestamp(
                (time - np.datetime64('1970-01-01T00:00:00')) / np.timedelta64(1, 's')).strftime("%Y-%m-%d %H:%M:%S"))
        else:
            self.time_slider.setToolTip("")

    def setup_main_plot_mouse_events(self):
        self.canvas.mpl_connect('scroll_event', self.on_scroll)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.canvas.mpl_connect('button_press_event', self.on_main_plot_press)
        self.canvas.mpl_connect('button_release_event', self.on_main_plot_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_main_plot_motion)
        self._main_plot_drag_start = None

    def on_main_plot_press(self, event):
        if event.inaxes != self.ax:
            return
        self._main_plot_drag_start = (event.xdata, event.ydata)

    def on_main_plot_release(self, event):
        self._main_plot_drag_start = None

    def on_main_plot_motion(self, event):
        if event.inaxes != self.ax or self._main_plot_drag_start is None:
            return

        dx = event.xdata - self._main_plot_drag_start[0]
        dy = event.ydata - self._main_plot_drag_start[1]

        cur_xlim = self.ax.get_xlim()
        cur_ylim = self.ax.get_ylim()

        self.ax.set_xlim(cur_xlim - dx)
        self.ax.set_ylim(cur_ylim - dy)

        self._main_plot_drag_start = (event.xdata, event.ydata)  # 更新拖拽起始点

        self.canvas.draw_idle()

    def on_update_tradpnl_changed(self, state):
        self.update_tradpnl_enabled = state == Qt.Checked
        if self.update_tradpnl_enabled:
            self.update_tradpnl_plot()

    def get_checked_items(self, combo_box):
        checked_items = []
        model = combo_box.model()
        for i in range(model.rowCount()):
            item = model.item(i)
            if item.checkState() == Qt.Checked:
                checked_items.append(item.text())
        return checked_items

    def show_config_dialog(self):
        """显示配置对话框"""
        if self.toolbar.isVisible():
            self.toolbar.hide()
            self.config_button.setText("配置")
        else:
            self.toolbar.show()
            self.config_button.setText("隐藏配置")

    def toggle_annotation(self, state):
        """切换注的显示/隐藏状态"""
        self.annotation_enabled = bool(state)
        if not self.annotation_enabled and self.annotation:
            self.hide_annotation()

    def toggle_live_mode(self, checked):
        """切换实时/文件模式"""
        if checked:
            self.statusBar().showMessage("正在连接实时数据...")
            self.live_mode_button.setEnabled(False)

            self.data_manager.md_manager = login_qt()
            self.live_mode = True
            self.file_button.setEnabled(False)
            self.live_mode_button.setText("关闭实时")
            self.live_mode_button.setEnabled(True)
            # 启动数据更新线程
            self.data_update_thread = DataUpdateThread(self.data_manager, self.live_mode)
            self.data_update_thread.data_updated.connect(self.update_all_plot)  # 连接信号到更新图表的方法
            self.data_update_thread.start()

            # 初始加载数据
            time.sleep(2)
            if self.data_manager.data is not None:
                self.set_month_combo()
                self.update_all_plot()
            else:
                print("加载数据初始化失败")

        else:
            # 关闭实时模式
            if self.live_mode:
                logout(self.data_manager.md_manager)
                self.live_mode = False
                self.data_update_thread.stop()  # 停止数据更新线程
            self.file_button.setEnabled(True)
            self.live_mode_button.setText("实时模式")
            self.statusBar().showMessage("已关闭实时模式")

    def on_data_update(self, data):
        """处理数据更新事件"""
        if data is not None:
            self.data_manager.load_and_process_data(None, live_mode=True)

    def closeEvent(self, event):
        """处理窗口关闭事件，清理资源并关闭连接"""
        try:
            # 关闭实时模式相关资源
            if self.live_mode:
                logout(self.data_manager.md_manager)
                self.data_update_thread.stop()  # 停止数据更新线程

            # 清理数据管理器资源
            if hasattr(self, 'data_manager'):
                self.data_manager.cleanup()

            event.accept()
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            event.accept()  # 即使出错也确保窗口能关闭
