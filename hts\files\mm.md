| __目录__                               |
|--------------------------------------|
| [__1.stock level__](#stock level)    |
| [__2. expire level__](#expire level) |
| [__3. opt level__](#opt level)       |
| [__3. vol level__](#vol level)       |

## stock level
<a id="stock level"></a>

| 参数                                                               | 意义                                                                                             |
|------------------------------------------------------------------|------------------------------------------------------------------------------------------------|
| `PDELTATOL2` `MAXEDGE2` `EDGE3`                                  | `delta`超过`maxundertol`后，等比例放大到maxedge2，超过PDELTATOL2后，放大到edge3                                  |
| `tv_mid_dis`                                                     | wq，tv和mid偏离的最小绝对值，防止虚值合约百分比过小                                                                  |
| `totaladjcap`                                                    | 控制所有对spot的adj                                                                                  |
| `volfac`                                                         | =2期货动1x iopv动2x                                                                                |
| `maxunderadjustment`                                             | 就是我们会adjust多少for 总delta暴露,现在深300是0.0006,如果delta ~700000份 etf我们试过低于0.0005，不行0.0008太高            |
| `resendthreshold`                                                |                                                                                                |
| `rthres` `rratio` `rrratio` `rlimit``rthres2`                    | [IH IF](#rthres)      ic im 会等30微秒                                                             |
| `reserve_pos`                                                    | 0:不拆留仓 1: 拆留仓 只对冲日内                                                                            |
| `small_thres`                                                    | wq 报价合约 max(tv,mktask)小于该值*mintick，只挂单边ask+1tick                                               |
| spot_min_change spot_max_change                                  | 定价标的变动区间 过大不发 过小不动                                                                             |
| UNDELY_MIN_BID , SF                                              | 超过3倍，根据0.5倍一档期货价差扩大报价   ADDSPR=SF*0.5*(ASK1-BID1)      期权价差=（edge*gledge*optedge+addspr）*delta |
| sg2_hack sg2_amult sg2_athres sg2_edmult sg2_edthres sg2_asdmult | signal                                                                                         |

## expire level
<a id="54646"></a>

| 参数            | 意义                                                                           |
|---------------|------------------------------------------------------------------------------|
| vd_smalldelta | 弱化小于该值的delta和vega，乘数为(delta/vd_smalldelta)^2                                 |
| slope_thres   | slopechg超过该值，弹窗，停策略，绝对值，0不开启                                                 |
| mindgrdelta   | 超过该delta值的tq合约，行情来之前提前撤单                                                     |
| VAY           | 超过evt后生效，edge=(1+(vay-1)(vega-vlow)/(vhigh-vlow)),vhigh=1.5*evt,vlow=0.5*evt |

## opt level
<a id="opt level"></a>

| 参数         | 意义                             |
|------------|--------------------------------|
| unwindmode | 3 买 unwindmode=4 卖             |
| PRI        | 也记住变成1,如果开ITM的tq pri 是强迫系统先处理它 |


## vol excexp_controls
<a id="vol level"></a>

| 参数        | 意义                                                                                                                                                                           |
|-----------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| dvol      | 越大和市场越贴合.它原本是加减波动率的，但现在实盘不是,这个参数应该叫 useMktVol,0 = 信fit,1 = 信mkt,比如500， 6.75 难fit,只要把dvol调到 0.7-1，应该就能解决fit得不好的问题,6.75 有两个期权，我用的是 max(call_dvol, put_dvol),所以改call 或 put 就可以了 |
| UFI       | 越小 越贴合市场 越大越贴合模型  平时0.85，到期日0.5，到期日收盘0.3                                                                                                                                     |
| VOLweight | 越小权重越小                                                                                                                                                                       |
| keystr    | 行权日 只开平值两个合约         -> -1                                                                                                                                                   |
| fixvol    | 波动率固定                                                                                                                                                                        |


## 附录
### rthres
<a id="rthres"></a>
rthres=0.00035
rratio=0.5
rrratio=0.25
rlimit=0.00025

if(IFoldOld > LITTLEVALUE && IFold > LITTLEVALUE)
{double tot_diff = (IFnew - IFold) + rratio2 * (IFold - IFoldOld);

if(fabs(tot_diff) > Rthres)
{return max(-if_rlimit,min(if_rlimit,tot_diff * 0.001 * Rratio));
ifdiff.timestamp = TimeUtil::Time();

NANO_LOG(ERROR, "IFChange,tot_diff=[%f],IFChange=[%f],IFnew=[%f],IFold=[%f],IFoldOld=[%f],time=[%s]", 
tot_diff,ifdiff.IFChange,IFnew,IFold,IFoldOld,ifdiff.timestamp.to_string().c_str());
}}IFoldOld = IFold;
IFold = IFnew;


