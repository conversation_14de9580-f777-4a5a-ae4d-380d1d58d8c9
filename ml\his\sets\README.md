# 订单簿特征提取与可视化

这是一个用于从金融市场订单簿数据中提取特征并进行可视化的Python库。该库基于Scala项目`orderbook-dynamics`移植而来，提供了多种时间无关的订单簿特征计算方法。

## 功能特点

- **数据处理**：处理订单簿数据的缺失值和异常情况
- **特征提取**：提取订单簿中的时间无关特征
- **可视化**：生成订单簿数据和特征的直观图表
- **灵活配置**：支持不同深度的订单簿分析

## 主要特征

该库可提取以下订单簿特征：

1. **价格相关特征**
   - 价格差异 (Price Spread)
   - 中间价格 (Mid Price)
   - 买入/卖出价格步长 (Bid/Ask Step)
   - 平均买入/卖出价格 (Mean Bid/Ask)
   - 累计价格差异 (Accumulated Price Spread)

2. **成交量相关特征**
   - 成交量差异 (Volume Spread)
   - 平均买入/卖出量 (Mean Bid/Ask Volume)
   - 累计成交量差异 (Accumulated Volume Spread)

## 数学公式

### 价格相关特征

**价格差异 (Price Spread)**
```
PriceSpread_i = AskPrice_i - BidPrice_i
```

**中间价格 (Mid Price)**
```
MidPrice_i = (AskPrice_i + BidPrice_i) / 2
```

**买入价格步长 (Bid Step)**
```
BidStep_i = |BidPrice_i - BidPrice_{i+1}|
```

**卖出价格步长 (Ask Step)**
```
AskStep_i = |AskPrice_i - AskPrice_{i+1}|
```

**平均卖出价格 (Mean Ask)**
```
MeanAsk = (1/n) * Σ(AskPrice_i) for i=1 to n
```

**平均买入价格 (Mean Bid)**
```
MeanBid = (1/n) * Σ(BidPrice_i) for i=1 to n
```

**累计价格差异 (Accumulated Price Spread)**
```
AccumulatedPriceSpread = Σ(AskPrice_i - BidPrice_i) for i=1 to n
```

### 成交量相关特征

**成交量差异 (Volume Spread)**
```
VolumeSpread_i = AskVolume_i - BidVolume_i
```

**平均卖出量 (Mean Ask Volume)**
```
MeanAskVolume = (1/n) * Σ(AskVolume_i) for i=1 to n
```

**平均买入量 (Mean Bid Volume)**
```
MeanBidVolume = (1/n) * Σ(BidVolume_i) for i=1 to n
```

**累计成交量差异 (Accumulated Volume Spread)**
```
AccumulatedVolumeSpread = Σ(AskVolume_i - BidVolume_i) for i=1 to n
```

## 安装依赖

```bash
pip install numpy matplotlib
```

## 使用方法

### 基本使用

```python
from time_insensitive_attributes import OrderBook, TimeInsensitiveSet

# 创建一个订单簿示例
ob = OrderBook(
    ask_prices={1: 105, 2: 106, 3: 107, 4: 108, 5: 109},
    bid_prices={1: 104, 2: 103, 3: 102, 4: 101, 5: 100},
    ask_volumes={1: 100, 2: 200, 3: 300, 4: 400, 5: 500},
    bid_volumes={1: 150, 2: 250, 3: 350, 4: 450, 5: 550}
)

# 创建时间无关特征集
ti_set = TimeInsensitiveSet(order_book_depth=5)

# 计算价格差异
price_spread = ti_set.price_spread(ob, 1).get()
print(f"1级价格差异: {price_spread}")

# 计算所有特征
features = ti_set.get_all_features(ob)
print(features)
```

### 可视化

```python
from time_insensitive_attributes import create_multi_level_example, visualize_orderbook, visualize_features

# 创建复杂订单簿示例
complex_ob = create_multi_level_example()

# 可视化订单簿数据
visualize_orderbook(complex_ob)

# 可视化特征
visualize_features(complex_ob)
```

## 可视化结果示例

### 订单簿数据可视化
![订单簿可视化](orderbook_visualization.png)

### 特征可视化
![特征可视化](orderbook_features.png)

## 参考

- 原始Scala项目: [orderbook-dynamics](https://github.com/codacy/orderbook-dynamics)
- 订单簿数据分析相关论文: "High-frequency trading in a limit order book" by Marco Avellaneda, Sasha Stoikov 