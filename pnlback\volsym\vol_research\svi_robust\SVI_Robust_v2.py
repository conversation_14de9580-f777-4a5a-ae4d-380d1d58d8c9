import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from scipy.optimize import minimize
from scipy.interpolate import interp1d
import pandas as pd


class RobustSVI:
    def __init__(self, F, K, T, imp_vol):
        """
        初始化SVI校准器
        F: 远期价格
        K: 行权价数组
        T: 到期时间
        imp_vol: 市场隐含波动率数组
        """
        self.F = F
        self.K = K
        self.T = T
        self.imp_vol = imp_vol
        self.w_market = imp_vol ** 2 * T  # 市场总方差

        # 计算对数在值程度 (log-moneyness)
        self.k = np.log(K / F)

        # 设置合理初值
        self.initial_params = self._get_initial_params()

        # 优化后的参数
        self.opt_params = None

    def _get_initial_params(self):
        """根据论文建议设置合理初始值"""
        w_min = np.min(self.w_market)
        w_max = np.max(self.w_market)

        # 初值
        a = 0.5 * w_min  # 总方差最小值的50%
        b = 0.1  # 中间值
        rho = -0.5  # 股权市场典型负偏
        m = 0.1  # 中间值
        sigma = 0.1  # 中间值

        return np.array([a, b, rho, m, sigma])

    def svi_model(self, k, params):
        """计算SVI总方差模型值"""
        a, b, rho, m, sigma = params
        return a + b * (rho * (k - m) + np.sqrt((k - m) ** 2 + sigma ** 2))

    def g_function(self, k, params):
        """计算无套利约束函数g(k)"""
        a, b, rho, m, sigma = params
        # 计算总方差及其导数
        w = self.svi_model(k, params)
        # 一阶导数
        dw_dk = b * (rho + (k - m) / np.sqrt((k - m) ** 2 + sigma ** 2))
        # 二阶导数
        d2w_dk2 = b * sigma ** 2 / ((k - m) ** 2 + sigma ** 2) ** 1.5
        # 计算g(k)函数
        term1 = (1 - (k * dw_dk) / (2 * w)) ** 2
        term2 = (dw_dk ** 2) / 4 * (1 / w + 1 / 4)
        g = term1 - term2 + d2w_dk2 / 2

        return g

    def objective_function(self, params, weights=None):
        """目标函数：加权最小二乘（均方误差）"""
        w_model = self.svi_model(self.k, params)
        errors = w_model - self.w_market

        # 应用权重 (ATM区域更高权重)
        if weights is None:
            # ATM附近更高权重（高斯衰减）
            weights = np.exp(-self.k ** 2 / (2 * np.max(self.k) ** 2))

        return np.sum(weights * errors ** 2)

    def arbitrage_constraints(self, params, epsilon=0.001):
        """生成无套利约束条件"""
        constraints = []

        # 1. 蝶式套利约束：g(k) > epsilon
        for k_val in self.k:
            constraints.append({
                'type': 'ineq',
                'fun': lambda p, k=k_val: self.g_function(k, p) - epsilon
            })

        # 2. 参数边界约束
        a, b, rho, m, sigma = params

        # 确保最小总方差为正
        min_w = a + b * sigma * np.sqrt(1 - rho ** 2)
        constraints.append({'type': 'ineq', 'fun': lambda p: p[0] - 1e-5})  # a > 0
        constraints.append({'type': 'ineq', 'fun': lambda p: min_w - epsilon})  # min(w) > ε

        # 右翼斜率约束 (Roger Lee矩公式)
        right_slope = b * (1 + rho)
        constraints.append({'type': 'ineq', 'fun': lambda p: 2 - right_slope - 1e-5})  # b(1+ρ) < 2

        return constraints

    def fit(self, weights=None, epsilon=0.01):
        """执行稳健校准"""
        # 参数边界
        bounds = [
            (1e-5, np.max(self.w_market)),  # a: [1e-5, max(w_market)]
            (0.001, 0.999),  # b: (0.001, 1)
            (-0.999, 0.999),  # rho: (-1, 1)
            (2 * np.min(self.k), 2 * np.max(self.k)),  # m: [2*min(k), 2*max(k)]
            (0.01, 1.0)  # sigma: [0.01, 1]
        ]

        # 无套利约束
        constraints = self.arbitrage_constraints(self.initial_params, epsilon)

        # 执行优化
        result = minimize(
            fun=self.objective_function,
            x0=self.initial_params,
            args=(weights,),
            method='SLSQP',
            bounds=bounds,
            constraints=constraints,
            options={'maxiter': 1000, 'ftol': 1e-8}
        )

        if not result.success:
            print("优化警告:", result.message)

        self.opt_params = result.x
        return self.opt_params

    def evaluate(self, k, output='imp_vol'):
        """评估SVI模型"""
        w = self.svi_model(k, self.opt_params)

        if output == 'w':
            return w
        elif output == 'imp_vol':
            return np.sqrt(w / self.T)
        else:
            raise ValueError("输出类型必须是'w'或'imp_vol'")

    def plot_fit(self):
        """绘制拟合结果"""
        if self.opt_params is None:
            raise RuntimeError("请先执行fit()方法")

        # 创建更密集的k值范围用于平滑绘图
        k_min, k_max = np.min(self.k), np.max(self.k)
        k_dense = np.linspace(k_min, k_max, 100)
        K_dense = self.F * np.exp(k_dense)

        # 计算模型隐含波动率
        imp_vol_model = self.evaluate(k_dense, 'imp_vol')

        # 绘图
        plt.figure(figsize=(12, 8))

        # 市场数据散点图
        plt.scatter(self.K, self.imp_vol * 100, c='red', label='市场隐含波动率', alpha=0.7)

        # 模型曲线
        plt.plot(K_dense, imp_vol_model * 100, 'b-', linewidth=2, label='SVI拟合')

        # 添加ATM线
        atm_line = self.F
        plt.axvline(x=atm_line, color='g', linestyle='--', label=f'ATM (F={atm_line:.2f})')

        plt.title(f'SVI稳健校准 (T={self.T:.2f}年)', fontsize=14)
        plt.xlabel('行权价K', fontsize=12)
        plt.ylabel('隐含波动率 (%)', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()


class SVISurface:
    def __init__(self, data):
        """
        初始化SVI波动率曲面
        data: 包含期权数据的DataFrame
        """
        self.data = data
        self.svi_models = {}

    def fit_surface(self, weights=None, epsilon=0.01, calendar_epsilon=0.001):
        """校准整个波动率曲面，确保日历价差无套利"""
        # 按期限从小到大排序
        maturities = sorted(self.data['contract_month'].unique(),
                            key=lambda x: self.data[self.data['contract_month'] == x]['maturity'].iloc[0])

        prev_svi = None  # 存储前一个期限的SVI模型

        for i, month in enumerate(maturities):
            group = self.data[self.data['contract_month'] == month]
            F = group['F'].iloc[0]
            K = group['exerciseprice'].values
            T = group['maturity'].iloc[0]
            imp_vol = group['market_imp_vol'].values

            svi = RobustSVI(F, K, T, imp_vol)

            # 如果是第一个期限，直接校准
            if i == 0:
                svi.fit(weights, epsilon)
                self.svi_models[month] = svi
                prev_svi = svi
                continue

            # 对于后续期限，添加日历价差约束
            def calendar_constraint(params):
                """确保当前期限的总方差大于前一个期限"""
                w_current = svi.svi_model(prev_svi.k, params)
                w_prev = prev_svi.svi_model(prev_svi.k, prev_svi.opt_params)
                return w_current - w_prev - calendar_epsilon

            # 添加到优化约束中
            constraints = svi.arbitrage_constraints(svi.initial_params, epsilon)
            constraints.append({
                'type': 'ineq',
                'fun': calendar_constraint
            })

            # 执行带约束的优化
            bounds = [
                (1e-5, np.max(svi.w_market)),
                (0.001, 0.999),
                (-0.999, 0.999),
                (2 * np.min(svi.k), 2 * np.max(svi.k)),
                (0.01, 1.0)
            ]

            result = minimize(
                fun=svi.objective_function,
                x0=svi.initial_params,
                args=(weights,),
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000, 'ftol': 1e-8}
            )

            svi.opt_params = result.x
            self.svi_models[month] = svi
            prev_svi = svi

    def plot_surface(self):
        """绘制3D波动率曲面"""
        if not self.svi_models:
            raise RuntimeError("请先执行fit_surface()方法")

        # 创建网格
        maturities = sorted(self.svi_models.keys())
        k_min, k_max = float('inf'), float('-inf')

        # 确定k的范围
        for svi in self.svi_models.values():
            k_min = min(k_min, np.min(svi.k))
            k_max = max(k_max, np.max(svi.k))

        k_vals = np.linspace(k_min, k_max, 100)
        T_vals = np.array([svi.T for svi in self.svi_models.values()])

        # 创建网格
        T_grid, k_grid = np.meshgrid(T_vals, k_vals)
        imp_vol_grid = np.zeros_like(T_grid)

        # 填充波动率网格
        for i, T_val in enumerate(T_vals):
            svi = list(self.svi_models.values())[i]
            imp_vol_grid[:, i] = svi.evaluate(k_vals, 'imp_vol')

        # 转换为行权价
        K_grid = self.data['F'].iloc[0] * np.exp(k_grid)

        # 3D绘图
        fig = plt.figure(figsize=(14, 10))
        ax = fig.add_subplot(111, projection='3d')

        # 曲面图
        surf = ax.plot_surface(
            T_grid, K_grid, imp_vol_grid * 100,
            cmap='viridis', edgecolor='none', alpha=0.8
        )

        # 添加原始数据点
        for month, svi in self.svi_models.items():
            ax.scatter(
                svi.T * np.ones_like(svi.K),
                svi.K,
                svi.imp_vol * 100,
                c='red', s=30, alpha=0.8
            )

        ax.set_title('SVI隐含波动率曲面', fontsize=14)
        ax.set_xlabel('到期时间 (年)', fontsize=12)
        ax.set_ylabel('行权价', fontsize=12)
        ax.set_zlabel('隐含波动率 (%)', fontsize=12)

        fig.colorbar(surf, shrink=0.5, aspect=10, label='隐含波动率 (%)')
        plt.tight_layout()
        plt.show()

    def plot_all_fits(self):
        """绘制所有期限的拟合曲线"""
        if not self.svi_models:
            raise RuntimeError("请先执行fit_surface()方法")

        n = len(self.svi_models)
        n_cols = 3
        n_rows = (n + n_cols - 1) // n_cols

        fig, axs = plt.subplots(n_rows, n_cols, figsize=(18, 5 * n_rows))
        axs = axs.flatten()

        for i, (month, svi) in enumerate(self.svi_models.items()):
            # 创建更密集的k值范围用于平滑绘图
            k_min, k_max = np.min(svi.k), np.max(svi.k)
            k_dense = np.linspace(k_min, k_max, 100)
            K_dense = svi.F * np.exp(k_dense)

            # 计算模型隐含波动率
            imp_vol_model = svi.evaluate(k_dense, 'imp_vol')

            # 绘图
            ax = axs[i]
            ax.scatter(svi.K, svi.imp_vol * 100, c='red', label='市场数据')
            ax.plot(K_dense, imp_vol_model * 100, 'b-', label='SVI拟合')
            ax.axvline(x=svi.F, color='g', linestyle='--', label=f'ATM')

            ax.set_title(f'{month} (T={svi.T:.2f}年)', fontsize=12)
            ax.set_xlabel('行权价')
            ax.set_ylabel('隐含波动率 (%)')
            ax.legend()
            ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for j in range(i + 1, len(axs)):
            axs[j].axis('off')

        plt.tight_layout()
        plt.show()



if __name__ == "__main__":
    # 加载数据
    data_raw = pd.read_pickle("sse50_option_data.pkl")
    data_raw = data_raw.sort_values(["contract_month", "strike_price"])

    # 创建并拟合波动率曲面
    surface = SVISurface(data_raw)

    # 定义权重函数,这里使用高斯衰减 (ATM附近更高权重)
    def weights_func(k):
        return np.exp(-k ** 2 / (2 * np.max(k) ** 2))

    # 执行稳健校准
    surface.fit_surface(weights=weights_func, epsilon=0.05)
    # 可视化结果
    surface.plot_surface()
    surface.plot_all_fits()



    # 对于单个期限的详细分析
    example_month = data_raw['contract_month'].unique()[0]
    group = data_raw[data_raw['contract_month'] == example_month]

    F = group['F'].iloc[0]
    K = group['strike_price'].values
    T = group['maturity'].iloc[0]
    imp_vol = group['market_imp_vol'].values

    svi = RobustSVI(F, K, T, imp_vol)
    opt_params = svi.fit()
    svi.plot_fit()

    # 打印优化后的参数
    param_names = ['a', 'b', 'rho', 'm', 'sigma']
    print("\n优化后的SVI参数:")
    for name, value in zip(param_names, opt_params):
        print(f"{name}: {value:.6f}")

    # 检查无套利约束
    k_vals = np.linspace(np.min(svi.k), np.max(svi.k), 100)
    g_values = [svi.g_function(k, opt_params) for k in k_vals]

    plt.figure(figsize=(10, 6))
    plt.plot(k_vals, g_values, 'b-')
    plt.axhline(y=0.05, color='r', linestyle='--', label='约束边界')
    plt.title('无套利约束函数 g(k)')
    plt.xlabel('对数在值程度 (k)')
    plt.ylabel('g(k)')
    plt.legend()
    plt.grid(True)
    plt.show()
