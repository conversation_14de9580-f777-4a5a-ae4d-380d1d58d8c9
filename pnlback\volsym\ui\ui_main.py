# -*- coding: utf-8 -*-
import matplotlib
import sys
from temple import BaseVolatilityCurveUI
from PySide6.QtWidgets import QApplication, QPushButton, QDialog, QDialogButtonBox, QTabWidget, QFormLayout, \
    QDoubleSpinBox, QCheckBox, QHBoxLayout, QLabel, QFileDialog, QMessageBox
from PySide6.QtWidgets import QVBoxLayout, QWidget
import pandas as pd
import numpy as np
import matplotlib.dates as mdates
from matplotlib.dates import date2num, num2date
import matplotlib.patheffects as path_effects
from config import volconfig,MULT

multi=MULT

class VolatilityCurveUI(BaseVolatilityCurveUI):

    def __init__(self):
        super().__init__()
        self.config = volconfig

    def update_plot(self):
        """更新绘图"""
        if self.data_manager.current_data is None:
            return

        # 清除当前图形
        self.ax.clear()
        for ax in self.ax.figure.axes:
            if ax != self.ax:
                self.ax.figure.delaxes(ax)

        current_spot = self.data_manager.current_data['spot'].iloc[0]

        self.setup_axes()

        if self.show_bar_chart:
            display_option = self.bar_display_combo.currentText()
            if display_option in ["TV Diff", "vol_diff"] and 'vol_diff' in self.data_manager.current_data.columns:
                self.plot_voldiff_bar(self.data_manager.current_data)
            elif self.data_manager.current_data['posi'].nunique() > 1:
                self.plot_bar_chart(self.data_manager.current_data)

        self.calculate_and_plot_forward(current_spot)
        self.plot_volatility_curves()
        self.setup_legend()
        self.finalize_plot(current_spot)
        self.data_manager.last_current_data = self.data_manager.current_data

    def plot_bar_chart(self, current_data):
        color_map = matplotlib.pyplot.colormaps['tab10']
        grouped_posi = current_data.groupby(['exp', 'K'])

        display_option = self.bar_display_combo.currentText()
        call_data, put_data, y_label = self.calculate_bar_data(grouped_posi, display_option)

        bar_width = self.calculate_bar_width(current_data)
        self.draw_bars(current_data, call_data, put_data, color_map, bar_width)

        self.ax2.set_ylabel(y_label, fontsize=14)
        self.ax2.relim()
        self.ax2.autoscale_view()
        self.ax2.yaxis.set_major_formatter(matplotlib.pyplot.FuncFormatter(lambda x, p: format(int(x), ',')))
        self.finalize_bar_chart()
    
    def plot_voldiff_bar(self, current_data):
        color_map = matplotlib.pyplot.colormaps['tab10']

        display_option = self.bar_display_combo.currentText()
        y_label = display_option
        if display_option=='vol_diff':
            y1=current_data['vol_diff']
            y2=current_data['vol_diff2']
            roundnum=4
        else:
            y1=current_data['vol_diff']*current_data['vega']*multi*100
            y2=current_data['vol_diff2']*current_data['vega']*multi*100
            roundnum=1
        bar_width = self.calculate_bar_width(current_data)
        
        # 绘制柱状图
        for i, exp in enumerate(sorted(current_data['exp'].unique())):
            exp_mask = current_data['exp'] == exp
            colors = color_map(i % 10)
            
            # 计算x位置
            x_base = current_data[exp_mask]['K']
            x1 = x_base - bar_width/4 - i*bar_width
            x2 = x_base + bar_width/4 + i*bar_width
            
            # 绘制柱状图
            self.ax2.bar(x1, y1[exp_mask], width=bar_width/2, color=colors, alpha=0.7, label='Vol Diff', zorder=1)
            if not self.data_manager.use_new_iv:
                self.ax2.bar(x2, y2[exp_mask], width=bar_width/2, color='none', edgecolor=colors, 
                            linestyle='--', linewidth=1, alpha=0.7, label='Vol Diff2', zorder=1)


        self.ax2.set_ylabel(y_label, fontsize=14)
        self.ax2.relim()
        self.ax2.autoscale_view()
        self.ax2.yaxis.set_major_formatter(matplotlib.pyplot.FuncFormatter(lambda x, p: f'{x:.{roundnum}f}'))
        self.finalize_bar_chart()

    def calculate_bar_data(self, grouped_posi, display_option):
        if display_option == "Vega加权":
            call_data = grouped_posi.apply(lambda x: (x['posi'] * x['vega']).sum(), include_groups=False) * multi
            put_data = grouped_posi.apply(lambda x: (x['neg_posi'] * x['vega']).sum(), include_groups=False) * multi
            y_label = 'Vega Weighted Position'
        elif display_option == "Delta加权":
            call_data = grouped_posi.apply(lambda x: (x['posi'] * x['delta'] * x['spot']).sum(),
                                           include_groups=False) * multi
            put_data = grouped_posi.apply(lambda x: (x['neg_posi'] * x['neg_delta'] * x['spot']).sum(),
                                          include_groups=False) * multi
            y_label = 'Delta Weighted Position'
        else:
            call_data, put_data = grouped_posi['posi'].sum(), grouped_posi['neg_posi'].sum()
            y_label = 'Posi'
        return call_data, put_data, y_label

    def calculate_bar_width(self, current_data):
        unique_strikes = current_data['K'].unique()
        return (unique_strikes.max() - unique_strikes.min()) / len(unique_strikes) * 0.8 / max(2, len(
            current_data['exp'].unique()))

    def draw_bars(self, current_data, call_data, put_data, color_map, bar_width):
        for i, exp in enumerate(sorted(current_data['exp'].unique())):
            color = color_map(i % 10)
            exp_call_data, exp_put_data = call_data.loc[exp], put_data.loc[exp]
            x_positions = exp_call_data.index + i * bar_width - (len(current_data['exp'].unique()) - 1) * bar_width / 2

            if self.separate_call_put_checkbox.isChecked():
                self.ax2.bar(x_positions, exp_call_data.values, width=bar_width, alpha=0.7, label=f'Call {exp}',
                             color=color, zorder=1)
                self.ax2.bar(x_positions, exp_put_data.values, width=bar_width, alpha=0.7, label=f'Put {exp}',
                             color='none', edgecolor=color, hatch='//', zorder=1)
            else:
                total_data = exp_call_data + exp_put_data
                self.ax2.bar(x_positions, total_data.values, width=bar_width, alpha=0.7, label=f'Total {exp}',
                             color=color, zorder=1)

    def finalize_bar_chart(self):
        self.ax2.set_zorder(self.ax.get_zorder() - 1)
        self.ax.patch.set_visible(False)
        self.ax2.set_xlim(self.ax.get_xlim())

    def setup_axes(self):
        # 使用预先计算的 y_min 和 y_max
        self.ax.set_ylim(self.y_min, self.y_max)

        if self.show_bar_chart:
            self.ax2 = self.ax.twinx()
            self.ax2.set_zorder(self.ax.get_zorder() - 1)
            self.ax.patch.set_visible(False)  # 主轴透明，以便看到柱状图

    def calculate_and_plot_forward(self, current_spot):
        # 获取所选数据中最小月份的远期价格
        min_month_forward = \
            self.data_manager.current_data.loc[
                self.data_manager.current_data['exp'] == self.data_manager.current_data['exp'].min(), 'forward'].iloc[0]

        # 计算与数据第一笔的变动百分比
        first_min_month_spot = \
            self.data_manager.data.loc[
                self.data_manager.data['exp'] == self.data_manager.data['exp'].min(), 'spot'].iloc[0]
        change_percentage = (current_spot - first_min_month_spot) / first_min_month_spot * 100

        # 绘制当前最小月份的远期价格竖线
        self.ax.axvline(x=min_month_forward, color='r', linestyle='--', label=f'Forward: {min_month_forward:.4f}',
                        linewidth=2, zorder=3)

        if not hasattr(self, 'last_min_month_forward'):
            self.last_min_month_forward = first_min_month_spot

        # 在竖线顶部添 forward 值和变动百分比
        y_pos = self.ax.get_ylim()[1]  # 获取 y 轴的最大值
        self.ax.text(min_month_forward, y_pos,
                     f'{min_month_forward:.4f}\n{change_percentage:+.2f}%\n{((min_month_forward - self.last_min_month_forward) / self.last_min_month_forward) * 100:+.2f}%',
                     color='r', ha='center', va='bottom', fontweight='bold', zorder=4)

        # 绘制上一次的竖线
        self.ax.axvline(x=self.last_min_month_forward, color='b', linestyle='--',
                        label=f'Last Forward: {self.last_min_month_forward:.4f}',
                        linewidth=1, zorder=2)

        # 更新 last_min_month_forward 为当前的 min_month_forward
        self.last_min_month_forward = min_month_forward

        # 使用预先计算的 y_min 和 y_max
        self.ax.set_ylim(self.y_min, self.y_max)

    def plot_volatility_curves(self):
        """
        绘制波动率曲线，包括市场数据和SVI拟合曲线
        """
        color_map = matplotlib.pyplot.colormaps['tab10']
        self.main_lines = {}

        for i, exp in enumerate(sorted(self.data_manager.current_data['exp'].unique())):
            exp_data = self.data_manager.current_data.loc[self.data_manager.current_data['exp'] == exp].copy()
            color = color_map(i % 10)

            # 绘制市场数据的实线
            current_line = self.plot_exp_data(exp_data, exp, zorder=2, color=color, linestyle='-.')
            self.main_lines[exp] = current_line

            # 只在启用SVI拟合且存在SVI数据时执行拟合
            if self.config['svi_enabled'] and exp in self.data_manager.svis:
                try:
                    # 添加SVI拟合曲线
                    exp, sigma_fit, voltime = self.data_manager.svis[exp]
                    if sigma_fit is not None:
                        # 从字典中直接获取值
                        svi_data = voltime
                        # 获取所有SVI参数和指标
                        params = svi_data['params']
                        msg_parts = []

                        # 构建展示信息
                        msg = f"{exp} {self.data_manager.current_time} SVI: "
                        for i, (name, param) in enumerate(params.items()):
                            msg_parts.append(f"{name}={param:.2f}")

                        # 在ATM点绘制标记
                        if 'atm_features' in svi_data :
                            atm_features = svi_data['atm_features']
                            if 'atm_vol' in atm_features:
                                atm_vol = atm_features['atm_vol']
                                for name, param in atm_features.items():
                                    msg_parts.append(f"{name}={param:.2f}")
                                self.ax.plot(exp_data['forward'].iloc[0], atm_vol, '*', color=color,
                                            markersize=12, markerfacecolor='none',
                                            markeredgewidth=2, zorder=3)
                            # 更新图例信息
                            if 'atm_vol' in atm_features and 'skew' in atm_features and 'convexity' in atm_features:
                                skew=atm_features['skew']
                                convexity=atm_features['convexity']
                                self.ax.plot([], [], ' ',
                                            label=f"ATM Vol: {atm_vol:.3f}\n"
                                                f"ATM Skew: {skew:.3f}\n"
                                                f"ATM Conv: {convexity:.3f}")
                        
                        msg_parts.append(f"RMSE: {svi_data['rmse_error']:.2f}")
                        msg_parts.append(f"Method: {svi_data['best_method']}")
                        msg_parts.append(f"PxDiff: {svi_data['sum_px_diff']:.0f}")
                        
                        # 展示所有参数
                        self.statusBar().showMessage(msg + ", ".join(msg_parts))

                        # 绘制拟合曲线
                        self.ax.plot(exp_data['K'], sigma_fit,
                                     linestyle='-', color=color, linewidth=1.5,
                                     alpha=0.8, label=f'SVI fit {exp}', zorder=1)

                        self.ax.plot([], [], ' ',
                                     label=f'Fit Quality:\n'
                                           f'RMSE: {svi_data["rmse_error"]:.6f}')

                except (KeyError, AttributeError) as e:
                    print(f"Warning: Failed to plot SVI fit for expiry {exp}: {str(e)}")
                    continue

            # 如果存在历史数据，绘制虚线表示
            if self.data_manager.last_current_data is not None and exp in self.data_manager.last_current_data['exp'].unique():
                last_exp_data = self.data_manager.last_current_data[self.data_manager.last_current_data['exp'] == exp]
                self.plot_exp_data(last_exp_data, exp, zorder=1, color=color,
                                   linestyle=':', is_last=True)

    def plot_exp_data(self, exp_data, exp_label, zorder=2, color=None, linestyle='-', is_last=False):
        label = f'exp: {exp_label}' if not is_last else f'last exp: {exp_label}'
        line, = self.ax.plot(exp_data['K'], exp_data['sigma'], label=label, linewidth=2, zorder=zorder,
                             color=color, linestyle=linestyle)
        if color is None:
            color = line.get_color()

        if not is_last:  # 只为当前数据绘制标记点和特殊情况
            # 调整三角形大小函数
            def adjust_marker_size(posi, is_bid):
                base_size = 30
                return base_size * 5 if (posi > 0 and is_bid) or (posi < 0 and not is_bid) else base_size

            # 计算仓位变化
            # 存在任意不为0的posi
            posi_change = exp_data['posi'].values
            neg_posi_change = exp_data['neg_posi'].values
            if exp_data['posi'].nunique() > 1:
                if (self.data_manager.last_current_data is not None
                        and exp_label in self.data_manager.last_current_data['exp'].unique()):
                    last_exp_data = self.data_manager.last_current_data[
                        self.data_manager.last_current_data['exp'] == exp_label]
                    posi_change = exp_data['posi'].values - last_exp_data['posi'].values
                    neg_posi_change = exp_data['neg_posi'].values - last_exp_data['neg_posi'].values

            # 绘卖价格点并添加标签
            for data, marker, change, vol_col, is_call, is_bid in [
                (exp_data, '^', posi_change, 'bid_vol', True, True),
                (exp_data, 'v', posi_change, 'ask_vol', True, False),
                (exp_data, '^', neg_posi_change, 'neg_bid_vol', False, True),
                (exp_data, 'v', neg_posi_change, 'neg_ask_vol', False, False)
            ]:
                self.ax.scatter(
                    data['K'], data[vol_col],
                    marker=marker,
                    color=color if is_call else 'none',
                    edgecolors=color,
                    s=[adjust_marker_size(p, is_bid) for p in change],
                    alpha=0.7,
                    zorder=zorder,
                    linewidth=1 if not is_call else None
                )

                # 添加标签
                for x, y, p in zip(data['K'], data[vol_col], change):
                    if (p > 0 and is_bid) or (p < 0 and not is_bid):
                        label_color = 'red' if is_call else 'green'
                        xytext = (0, -10 if is_call else 10)
                        va = 'top' if is_bid else 'bottom'
                        self.ax.annotate(str(int(p)), (x, y), xytext=xytext,
                                         textcoords='offset points', ha='center', va=va,
                                         fontsize=12, color=label_color, weight='bold',
                                         path_effects=[path_effects.withStroke(linewidth=1, foreground='white')])

            # 定义并标记特殊情况
            special_cases = [
                (exp_data['bid'] == exp_data['ask'], 'x', 'red', 'Call Bid=Ask'),
                (exp_data['bid'] == 0, 'o', 'red', 'Call Bid=0'),
                (exp_data['ask'] == 0, 's', 'red', 'Call Ask=0'),
                (exp_data['neg_bid'] == exp_data['neg_ask'], 'x', 'blue', 'Put Bid=Ask'),
                (exp_data['neg_bid'] == 0, 'o', 'blue', 'Put Bid=0'),
                (exp_data['neg_ask'] == 0, 's', 'blue', 'Put Ask=0')
            ]

            for condition, marker, special_color, _ in special_cases:
                self.ax.scatter(exp_data.loc[condition, 'K'], exp_data.loc[condition, 'sigma'],
                                marker=marker, color=special_color, s=100, zorder=zorder + 1,
                                facecolors='none' if marker in ['o', 's'] else special_color)

        return line

    def setup_legend(self):
        # 修改图例设置 - 将ncol改为1使每个月份一行
        leg = self.ax.legend(fontsize=8, loc='upper center', bbox_to_anchor=(0.5, -0.1),
                             ncol=max(len(self.main_lines), 4), borderaxespad=0.)

        # 设置图例可交互
        for legline in leg.get_lines():
            legline.set_picker(5)  # 5 points tolerance

        # 移除之前的连接（如果存在）
        if hasattr(self, '_main_pick_event_cid'):
            self.canvas.mpl_disconnect(self._main_pick_event_cid)

        # 重新连接 pick_event
        self._main_pick_event_cid = self.canvas.mpl_connect('pick_event', self.on_main_legend_pick)

    def finalize_plot(self, current_spot):
        # 调整布局以适应图例
        self.figure.tight_layout()

        self.ax.grid(True, zorder=0)  # 将网格放在最底层
        self.ax.tick_params(axis='both', which='major', labelsize=12)

        self.canvas.draw()

        # 获取所选数据中最小月份的远期价格
        min_month_forward = \
            self.data_manager.current_data.loc[
                self.data_manager.current_data['exp'] == self.data_manager.current_data['exp'].min(), 'forward'].iloc[0]

        # 计算与数据第一笔的变动百分比
        first_min_month_spot = \
            self.data_manager.data.loc[
                self.data_manager.data['exp'] == self.data_manager.data['exp'].min(), 'spot'].iloc[0]
        change_percentage = (current_spot - first_min_month_spot) / first_min_month_spot * 100

        info_text = (f"当前时间: {self.data_manager.current_time}, "
                     f"当前Spot: {current_spot:.4f}, "
                     f"Forward: {min_month_forward:.4f} ({change_percentage:+.2f}%)")
        self.update_info_text(info_text)

        self.update_table()

        # 添加这行来自动更新计算结果
        self.calculate_formula()

        # 确保两个轴的 x 轴范围一致（只在显示柱状图时执行）
        if self.show_bar_chart:
            self.ax2.set_xlim(self.ax.get_xlim())

        # 重新绘制图形
        self.canvas.draw_idle()

        # 更新窗口标
        self.update_window_title(self.data_manager.current_time)

        # 重新绘制整个画布
        self.canvas.draw()
        self.tradpnl_canvas.draw()

    def update_tradpnl_plot(self):
        if not self.update_tradpnl_enabled or self.data_manager.trade_data is None:
            return

        # 如果是第一次绘图或需要完全重绘
        if not hasattr(self, 'tradpnl_initialized') or not self.tradpnl_initialized:
            self.initialize_tradpnl_plot(self.data_manager._processed_data, self.data_manager.tradetime_num)

        # 找到最接近 current_time 的索引
        closest_time_index = np.searchsorted(self.data_manager.tradetime_num,
                                             date2num(self.data_manager.current_time)) - 1
        latest_data = self.data_manager._processed_data.iloc[closest_time_index]

        # 更新竖线位置
        self._update_current_time_line(closest_time_index, self.data_manager.tradetime_num)

        # 更新图例
        self._update_tradpnl_legend(latest_data)

        # 调整x轴范围
        self._adjust_tradpnl_x_axis(self.data_manager.current_time, self.data_manager.tradetime_num)

        # 重新绘制画布
        self.tradpnl_canvas.draw()

    def initialize_tradpnl_plot(self, trade_data, time_num):
        self.ax_tradpnl.clear()
        if self.ax_spot is None:
            self.ax_spot = self.ax_tradpnl.twinx()
        else:
            self.ax_spot.clear()

        # 绘制PNL曲线
        for pnl_type, pnl_info in self.pnl_data.items():
            if pnl_info['checkbox'].isChecked():
                y_data = trade_data[f'{pnl_type}_normalized'].values if self.normalize_by_edgepnl else trade_data[
                    pnl_type].values
                line, = self.ax_tradpnl.plot(time_num, y_data, label=pnl_type, color=pnl_info['color'], zorder=2)
                pnl_info['line'] = line

        # 添加这行来绘制spot数据
        self.spot_line, = self.ax_spot.plot(time_num, trade_data['Spot'], label='Spot', color='grey', linewidth=0.5,
                                            zorder=0)

        self.setup_tradpnl_axes(trade_data['Spot'])
        self.setup_tradpnl_legend()

        # 修改这行来将竖线色改为红色
        self.current_time_line = self.ax_tradpnl.axvline(x=time_num[0], color='red', linestyle='--', linewidth=1)
        self.current_time_text = self.ax_tradpnl.text(time_num[0], 0, '', ha='center', va='top', fontweight='bold',
                                                      fontsize=10,
                                                      bbox=dict(facecolor='none', edgecolor='none', alpha=0.7))

        self.adjust_tradpnl_y_axis()
        self.tradpnl_initialized = True

    def _update_current_time_line(self, closest_time_index, time_num):
        closest_time = num2date(time_num[closest_time_index])
        if pd.notna(closest_time):
            self.current_time_line.set_xdata([time_num[closest_time_index]])  # 将单个值包装在列表中
            y_min, y_max = self.ax_tradpnl.get_ylim()
            time_str = closest_time.strftime('%H:%M:%S')
            self.current_time_text.set_position((time_num[closest_time_index], y_max + (y_max - y_min) * 0.1))
            self.current_time_text.set_text(time_str)

    def _update_tradpnl_legend(self, latest_data):
        for pnl_type, pnl_info in self.pnl_data.items():
            if pnl_info['checkbox'].isChecked():
                label = f'{pnl_type}: {latest_data[pnl_type]:.0f} {latest_data[f"{pnl_type}_normalized"]:.2f}'
                pnl_info['line'].set_label(label)
        self.ax_tradpnl.legend(loc='upper left')

    def _adjust_tradpnl_x_axis(self, current_time, time_num):
        if self.show_recent_checkbox.isChecked():
            start_time = date2num(current_time - pd.Timedelta(minutes=10))
            end_time = date2num(current_time + pd.Timedelta(minutes=10))
        else:
            start_time, end_time = time_num[0], time_num[-1]
        self.ax_tradpnl.set_xlim(start_time, end_time)
        self.ax_spot.set_xlim(start_time, end_time)

    def _highlight_current_time(self, closest_time_index, time_num):
        closest_time = num2date(time_num[closest_time_index])
        if pd.notna(closest_time):
            self.ax_tradpnl.axvline(x=time_num[closest_time_index], color='red', linestyle='--', linewidth=1)
            y_min, y_max = self.ax_tradpnl.get_ylim()
            time_str = closest_time.strftime('%H:%M:%S')
            self.ax_tradpnl.text(time_num[closest_time_index], y_max + (y_max - y_min) * 0.1, time_str,
                                 ha='center', va='top', fontweight='bold', fontsize=10,
                                 bbox=dict(facecolor='none', edgecolor='none', alpha=0.1))

    def adjust_tradpnl_y_axis(self):
        xlim = self.ax_tradpnl.get_xlim()
        visible_data = self.data_manager._processed_data[
            (date2num(self.data_manager._processed_data['tradetime']) >= xlim[0]) &
            (date2num(self.data_manager._processed_data['tradetime']) <= xlim[1])]

        selected_pnl_types = [pnl_type for pnl_type, pnl_info in self.pnl_data.items() if
                              pnl_info['checkbox'].isChecked()]

        if self.normalize_by_edgepnl:
            y_data = [visible_data[f'{pnl_type}_normalized'] for pnl_type in selected_pnl_types]
        else:
            y_data = [visible_data[pnl_type] for pnl_type in selected_pnl_types]

        if all(data.empty for data in y_data):
            return

        y_min = min(data.min() for data in y_data if not data.empty)
        y_max = max(data.max() for data in y_data if not data.empty)

        if self.normalize_by_edgepnl:
            y_min = max(y_min, -1)
            y_max = min(y_max, 0.5)

        data_range = y_max - y_min
        margin_ratio = 0.1
        new_y_min = y_min - data_range * margin_ratio
        new_y_max = y_max + data_range * margin_ratio

        self.ax_tradpnl.set_ylim(new_y_min, new_y_max)

    def setup_tradpnl_axes(self, spot_data):
        self.ax_tradpnl.set_ylabel('Normalized PNL' if self.normalize_by_edgepnl else 'PNL')
        if self.normalize_by_edgepnl and not self.show_recent_checkbox.isChecked():
            self.ax_tradpnl.set_ylim(-1, 0.5)

        # 设置spot价格的y轴
        self.ax_spot.set_ylabel('Spot Price')
        self.ax_spot.spines['right'].set_position(('axes', 1.05))  # 将spot的y轴移到右侧

        self.ax_spot.set_ylim(self.data_manager.spot_min, self.data_manager.spot_max)

        # 确保spot轴在最下层
        self.ax_spot.set_zorder(0)
        self.ax_tradpnl.set_zorder(1)
        self.ax_tradpnl.patch.set_visible(False)

        # 设置x轴标
        self.ax_tradpnl.set_xlabel('Time')

        # 格式化x轴刻度为时间
        self.ax_tradpnl.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))

        # 添加网格线
        self.ax_tradpnl.grid(True, which='both', axis='both', linestyle='--', alpha=0.6, zorder=1)

        # 格式化Spot轴的刻度标签，保留小数点后两位
        self.ax_spot.yaxis.set_major_formatter(matplotlib.ticker.FuncFormatter(lambda x, p: f"{x:.4f}"))

    def setup_tradpnl_legend(self):
        lines = [pnl_info['line'] for pnl_info in self.pnl_data.values() if pnl_info['checkbox'].isChecked()]
        labels = [line.get_label() for line in lines]

        # 添加spot线到图例
        lines.append(self.spot_line)
        labels.append('Spot')

        leg = self.ax_tradpnl.legend(lines, labels, loc='upper left')
        for legline in leg.get_lines():
            legline.set_picker(5)  # 5 pts tolerance
        self.tradpnl_canvas.mpl_connect('pick_event', self.on_legend_pick)

    def finalize_tradpnl_plot(self):
        # 确保两个轴x轴范围一致
        self.ax_spot.set_xlim(self.ax_tradpnl.get_xlim())

        # 重新绘制画布
        self.tradpnl_canvas.draw()

    def show_config_dialog(self):
        """显示配置对话框"""
        dialog = ConfigDialog(self.config, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.config = dialog.get_config()
            # 更新完配置后重新计算
            self.update_plot()

    def save_table_data(self):
        """保存表格数据到CSV文件"""
        if self.data_manager.current_data is None:
            return

        # 获取保存路径
        file_dialog = QFileDialog(self)
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        file_dialog.setNameFilter("CSV files (*.csv)")

        # 设置默认文件名
        timestamp = self.data_manager.current_time.strftime("%Y%m%d_%H%M%S")
        default_filename = f"volatility_table_{timestamp}.csv"
        file_dialog.selectFile(default_filename)

        if file_dialog.exec() == QDialog.DialogCode.Accepted:
            filename = file_dialog.selectedFiles()[0]
            if not filename.endswith('.csv'):
                filename += '.csv'

            try:
                # 直接保存current_data为CSV
                self.data_manager.current_data.to_csv(filename, index=False, encoding='utf-8-sig')
                self.statusBar().showMessage(f"表格已保存至 {filename}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存文件时发生错误：{str(e)}")


class ConfigDialog(QDialog):
    def __init__(self, config, parent=None):
        super().__init__(parent)
        self.config = config.copy()
        self.setup_ui()

    def setup_ui(self):
        """设置对话框UI"""
        self.setWindowTitle("SVI拟合参数配置")
        layout = QVBoxLayout()

        # 创建选项卡窗口
        tab_widget = QTabWidget()
        tab_widget.setMinimumWidth(400)
        tab_widget.setMinimumHeight(300)
        layout.addWidget(tab_widget)

        # SVI权重选项卡
        svi_tab = QWidget()
        svi_layout = QFormLayout()
        self.svi_inputs = {}
        for key, value in self.config['svi_weights'].items():
            self.svi_inputs[key] = QDoubleSpinBox()
            self.svi_inputs[key].setRange(0, 100)
            self.svi_inputs[key].setValue(value)
            self.svi_inputs[key].setSingleStep(0.1)
            svi_layout.addRow(f"{key}权重:", self.svi_inputs[key])
        svi_tab.setLayout(svi_layout)
        tab_widget.addTab(svi_tab, "SVI总体权重")

        # 参数权重选项卡
        param_tab = QWidget()
        param_layout = QFormLayout()
        self.param_inputs = {}
        for key, value in self.config['param_weights'].items():
            self.param_inputs[key] = QDoubleSpinBox()
            self.param_inputs[key].setRange(0, 10)
            self.param_inputs[key].setValue(value)
            self.param_inputs[key].setSingleStep(0.01)
            param_layout.addRow(f"{key}参数权重:", self.param_inputs[key])
        param_tab.setLayout(param_layout)
        tab_widget.addTab(param_tab, "参数权重")

        # ATM特征权重选项卡
        atm_tab = QWidget()
        atm_layout = QFormLayout()
        self.atm_inputs = {}
        for key, value in self.config['atm_weights'].items():
            self.atm_inputs[key] = QDoubleSpinBox()
            self.atm_inputs[key].setRange(0, 10)
            self.atm_inputs[key].setValue(value)
            self.atm_inputs[key].setSingleStep(0.1)
            atm_layout.addRow(f"{key}特征权重:", self.atm_inputs[key])
        atm_tab.setLayout(atm_layout)
        tab_widget.addTab(atm_tab, "ATM特征权重")

        # 创建底部布局
        bottom_layout = QHBoxLayout()

        # 添加SVI拟合开关到左侧
        svi_enabled_layout = QHBoxLayout()
        self.svi_enabled_checkbox = QCheckBox()
        self.svi_enabled_checkbox.setChecked(self.config['svi_enabled'])
        svi_enabled_layout.addWidget(QLabel("启用SVI拟合"))
        svi_enabled_layout.addWidget(self.svi_enabled_checkbox)
        bottom_layout.addLayout(svi_enabled_layout)

        # 添加弹性空间
        bottom_layout.addStretch(1)

        # 添加按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        bottom_layout.addWidget(button_box)

        # 将底部布局添加到主布局
        layout.addLayout(bottom_layout)

        self.setLayout(layout)

    def get_config(self):
        """获取更新后的配置"""
        config = {
            'svi_enabled': self.svi_enabled_checkbox.isChecked(),  # 添加SVI拟合开关状态
            'svi_weights': {
                key: self.svi_inputs[key].value()
                for key in self.config['svi_weights']
            },
            'param_weights': {
                key: self.param_inputs[key].value()
                for key in self.config['param_weights']
            },
            'atm_weights': {
                key: self.atm_inputs[key].value()
                for key in self.config['atm_weights']
            }
        }
        print(config)
        return config


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = VolatilityCurveUI()
    window.show()
    sys.exit(app.exec())
