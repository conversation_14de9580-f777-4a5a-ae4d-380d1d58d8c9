import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, TimeSeriesSplit, GridSearchCV
from sklearn.linear_model import LinearRegression,Lasso

from utils import normalize_columns

class BaseModel:
    def __init__(self, data_path):
        """
        初始化基类。

        Args:
            data_path (str): 数据文件的路径。
        """
        self.data_path = data_path
        self.data = None
        self.X_train = None
        self.X_train_sd = None
        self.X_test = None
        self.X_test_sd = None
        self.y_train = None
        self.y_test = None
        self.model = None


    def load_data(self, test_set=None):
        """
        加载数据并进行预处理。
        """
        # 加载数据
        self.data = pd.read_csv(self.data_path)

        #############@修改
        # 替换无穷值为NaN并删除包含NaN的行
        self.data = self.data.replace([np.inf, -np.inf], np.nan)
        self.data = self.data.dropna()
        #############

        # 提取特征和目标
        X = self.data.drop(['target'], axis=1)
        y = self.data['target']

        # 删除时间列（如果存在）
        if 'TimeStamp' in X.columns:
            X.drop(['TimeStamp'], axis=1, inplace=True)

        if test_set == 'day':
        # 划分训练集和测试集
            if 'label' in X.columns:
                label = X['label']
                X.drop(['label'], axis=1, inplace=True)
                self.X_train, self.y_train = X[label == 0], y[label == 0]
                self.X_test, self.y_test = X[label == 1], y[label == 1]
            else: print('No label to split.')
        else:
            if 'label' in X.columns:
                X.drop(['label'], axis=1, inplace=True)
            self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )

        #########修改@
        # 再次检查训练集和测试集是否有无效值
        self.X_train = self.X_train.replace([np.inf, -np.inf], np.nan).fillna(0)  # 填充或删除
        self.X_test = self.X_test.replace([np.inf, -np.inf], np.nan).fillna(0)
        #########

        # 归一化
        self.X_train_sd, self.X_test_sd = normalize_columns(self.X_train, self.X_test)

        print(f"特征个数：{X.shape[1]}, 数据数量：{X.shape[0]}")
        print(f"训练集大小：{self.X_train_sd.shape[0]}, 测试集大小：{self.X_test_sd.shape[0]}")


    def evaluate_model(self, y_true, y_pred, set_name):
        """
        评估模型性能。

        Args:
            y_true (pd.Series): 真实值。
            y_pred (pd.Series): 预测值。
            set_name (str): 数据集名称（如 'Training Set' 或 'Test Set'）。
        """
        mse = mean_squared_error(y_true, y_pred)
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        accuracy = np.mean(np.sign(y_true) == np.sign(y_pred))

        # 输出评价结果
        print(f'在 {set_name} 上的评估结果:')
        print(f'MSE: {mse}')
        print(f'MAE: {mae}')
        print(f'R²: {r2}')
        print(f'方向准确率: {accuracy * 100:.2f}%')

        # 较大涨跌幅的准确率
        big_up = np.mean(np.sign(y_pred[y_true > 8e-5]) == np.sign(y_true[y_true > 8e-5]))
        big_down = np.mean(np.sign(y_pred[y_true < -8e-5]) == np.sign(y_true[y_true < -8e-5]))
        print(f'较大涨幅正确率: {big_up}')
        print(f'较大跌幅正确率: {big_down}')
        print('-' * 40)

class RandomForestModel(BaseModel):
    def train_model(self):
        """
        训练随机森林模型并进行参数搜索。
        """
        # 定义参数网格
        param_grid = {
            'n_estimators': [10, 20, 40],  # 树的数量
            'max_depth': [None, 10, 20],  # 树的最大深度
            'min_samples_split': [2, 5, 10],  # 分裂内部节点所需的最小样本数
            'min_samples_leaf': [1, 2, 4]  # 叶节点所需的最小样本数
        }

        # 构建随机森林模型
        model = RandomForestRegressor(random_state=42)

        # 使用 GridSearchCV 进行参数搜索
        tscv = TimeSeriesSplit(n_splits=5)  # 5 个时间序列分割
        grid_search = GridSearchCV(
            estimator=model, param_grid=param_grid, cv=tscv, scoring='neg_mean_squared_error', n_jobs=-1
        )

        print("开始参数搜索...")
        grid_search.fit(self.X_train_sd, self.y_train)
        print("参数搜索完成。")

        # 输出最佳参数
        print("最佳参数:", grid_search.best_params_)

        # 使用最佳参数的模型
        self.model = grid_search.best_estimator_

    def run(self, test_set=None):
        """
        运行模型训练和评估流程。
        """
        # 加载数据
        self.load_data(test_set = test_set)

        # 训练模型
        self.train_model()

        # 在训练集和测试集上进行预测
        y_train_pred = self.model.predict(self.X_train_sd)
        y_test_pred = self.model.predict(self.X_test_sd)

        # 评估模型
        self.evaluate_model(self.y_train, y_train_pred, '训练集')
        self.evaluate_model(self.y_test, y_test_pred, '测试集')

class OLSModel(BaseModel):
    def train_model(self):
        """
        训练 OLS 模型。
        """
        # 构建 OLS 模型
        self.model = LinearRegression()

        # 训练模型
        print("开始训练 OLS 模型...")
        self.model.fit(self.X_train_sd, self.y_train)
        print("训练完成。")

    def run(self, test_set=None):
        """
        运行模型训练和评估流程。
        """
        # 加载数据
        self.load_data(test_set = test_set)

        # 训练模型
        self.train_model()

        # 在训练集和测试集上进行预测
        y_train_pred = self.model.predict(self.X_train_sd)
        y_test_pred = self.model.predict(self.X_test_sd)
        
        print('alpha_size_mean:', np.mean(np.abs(y_test_pred)))

        # 评估模型
        self.evaluate_model(self.y_train, y_train_pred, '训练集')
        self.evaluate_model(self.y_test, y_test_pred, '测试集')

    def predict(self, X_data):
        _, X_data_nor = normalize_columns(self.X_train, X_data)
        return self.model.predict(X_data_nor)


class LassoModel(BaseModel):
    def train_model(self):
        """
        训练 Lasso 模型。
        """
        # 构建 Lasso 模型
        self.model = Lasso(alpha=1e-7)

        # 训练模型
        print("开始训练 Lasso 模型...")
        self.model.fit(self.X_train_sd, self.y_train)
        print("训练完成。")

    def run(self, test_set=None):
        """
        运行模型训练和评估流程。
        """
        # 加载数据
        self.load_data(test_set = test_set)

        # 训练模型
        self.train_model()

        # 在训练集和测试集上进行预测
        y_train_pred = self.model.predict(self.X_train_sd)
        y_test_pred = self.model.predict(self.X_test_sd)
        
        print('alpha_size_mean:', np.mean(np.abs(y_test_pred)))

        # 提取系数为 0 的特征
        feature_names = self.X_train.columns.tolist()
        coefficients = self.model.coef_
        zero_coef_indices = np.where(coefficients == 0)[0]
        zero_features = [feature_names[i] for i in zero_coef_indices]

        print("系数为 0 的特征：", zero_features)

        # 评估模型
        self.evaluate_model(self.y_train, y_train_pred, '训练集')
        self.evaluate_model(self.y_test, y_test_pred, '测试集')

    def predict(self, X_data):
        _, X_data_nor = normalize_columns(self.X_train, X_data)
        return self.model.predict(X_data_nor)

