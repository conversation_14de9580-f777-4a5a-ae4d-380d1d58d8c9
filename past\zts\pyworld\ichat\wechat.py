# -*- coding:utf-8 -*-
import itchat
import datetime


class WeChat(object):
    __files = []
    __name = []

    def __init__(self):
        self.login = itchat.auto_login(hotReload=True)

    # #获取自己的UserName
    # userName = account[0]['UserName']

    def add_mps(self, name):
        # 返回完整的公众号列表
        # mps0 = itchat.get_mps()
        # 获取名字中含有特定字符的公众号，也就是按公众号名称查找,返回值为一个字典的列表
        mps = itchat.search_mps(name)
        print(mps)
        # 发送方法和上面一样
        userName = mps[0]['UserName']
        self.__name.append(userName)

    def add_filehelper(self):
        self.__name.append('filehelper')

    def add_friends(self, name):
        # #获取所有好友信息
        # account=itchat.get_friends()
        # #获取自己的UserName
        # userName = account[0]['UserName']
        # 想给谁发信息，先查找到这个朋友,name后填微信备注即可,deepin测试成功
        users = itchat.search_friends(name=name)
        # 获取好友全部信息,返回一个列表,列表内是一个字典
        print(users)
        # 获取`UserName`,用于发送消息
        userName = users[0]['UserName']
        # itchat.send("hello",toUserName = userName)
        self.__name.append(userName)

    def add_rooms(self, name):
        iRoom = itchat.search_chatrooms(name=name)

        userName = iRoom[0]['UserName']
        for room in iRoom:
            if room['NickName'] == name:
                userName = room['UserName']
                break
        self.__name.append(userName)

    # dt = datetime.datetime.now()
    # dt=datetime.datetime.strftime(dt, "%Y%m%d")
    # # dt="********"
    # copyPath = u'\\\\10.25.18.38\\home\\盘后损益\\图片\\%s盘后损益.jpg' % dt
    # pic = 'pl.jpg'
    # shutil.move(copyPath, pic)

    def send_image(self, file):
        for name in self.__name:
            itchat.send_image(file, name)

    def send_msg(self, msg):
        for name in self.__name:
            itchat.send_msg(msg, name)


if __name__ == '__main__':
    dt = datetime.datetime.now()
    dt = datetime.datetime.strftime(dt, "%Y%m%d")
    dt = "20180202"
    copyPath = u'\\\\10.25.18.38\\home\\盘后损益\\图片\\%s盘后损益.jpg' % dt

    wechat1 = WeChat()
    wechat1.add_filehelper()
    wechat1.add_friends(u'哟')
    # wechat1.add_rooms(u'努力工作')
    wechat1.add_mps(u'八卦')

    wechat1.send_msg(u'你')
    wechat1.send_image(u'wechat_cloud.png')
