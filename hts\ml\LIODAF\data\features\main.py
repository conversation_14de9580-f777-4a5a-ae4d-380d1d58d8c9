
"""
因子管理系统主模块
整合因子管理、验证和分析功能
@author: lining
"""
import pandas as pd
from typing import Dict
import os
import sys

sys.path.insert(0, sys.path[0]+"/../../")

from data.features.factor_manager import factor_manager, FactorCategory
from data.features.factor_analysis_system import factor_analysis_system
from data.features.feature_generator import OrderBookFeatureGenerator
from core import config


class FactorSystem:
    
    def __init__(self):
        self.feature_generator = None
        self.factors = []
        self.analysis_results = {}
        self.validation_results = {}
        self.output_dir = config.OUTDIR + '/features'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def initialize(self, data: pd.DataFrame, feature_cols: list,target_cols: list):
        
        self.feature_generator = OrderBookFeatureGenerator(data)
        self.data = data
        self.feature_cols = feature_cols
        self.target_cols = target_cols
        
    def generate_features(self) -> pd.DataFrame:
        
        if not self.feature_generator:
            raise ValueError("系统未初始化")
        
        self.data, self.factors = self.feature_generator.generate_all_features()
        self.all_features = self.feature_generator.all_features
        return self.data, self.factors
    
    def analyze_factors(self) -> Dict:
        
        if not self.factors:
            raise ValueError("未生成因子")
        
        self.analysis_results = factor_analysis_system.analyze_factor_set(self.data, self.factors)
        return self.analysis_results
    
    def factor_test(self, std_train_data: pd.DataFrame, target_train_data: pd.DataFrame, model=None):
        
        self.factor_test_results = factor_analysis_system.test_factors(std_train_data, target_train_data, self.factors, self.target_cols,model)
        self.shap_values = factor_analysis_system.shap_values
        
        
        
        self.mixfactor_doc_df = pd.merge(self.factor_doc_df, self.factor_test_results, left_on='因子名称', right_on='feature', how='left').round(4)
        self.mixfactor_doc_df = pd.merge(self.mixfactor_doc_df, self.shap_values['importance'], left_on='因子名称', right_on='feature', how='left').round(4)
        self.mixfactor_doc_df.to_csv(os.path.join(self.output_dir, "factor_documentation.csv"), index=False, encoding="utf-8")
        
        return self.factor_test_results,self.shap_values
    
        
    def generate_reports(self):
        
        if not self.factors:
            raise ValueError("未生成因子")
        
        
        self.factor_doc_df = factor_manager.get_factor_documentation(self.all_features)
        self.factor_doc_df.to_csv(os.path.join(self.output_dir, "factor_documentation.csv"), index=False, encoding="utf-8")

        
        with open(os.path.join(self.output_dir, "factor_analysis_report.md"), "w", encoding="utf-8") as f:
            f.write(factor_analysis_system.generate_analysis_report(self.data, self.factors))
    
    
    def get_factor_summary(self) -> Dict:
        
        return {
            'total_factors': len(self.factors),
            'categories': {
                category.value: len(factor_manager.get_factors_by_category(category))
                for category in FactorCategory
            },
            'analysis_results': self.analysis_results,
        }


if __name__ == "__main__":
    from core.m1 import M1
    from utils.visualizer import OrderBookVisualizer
    from utils.utils import log_print

    factor_name = 'ofi2'

    
    m1 = M1()
    target_col = config.TARGET_COLS[0]
    m1.process_data()
    m1.factor_system.generate_reports()
    m1.train_model()
    
    
    visualizer = OrderBookVisualizer(use_chinese=True)
    
    
    log_print("执行标准因子测试...")
    factor_analysis_system.test_factors(m1.train_data[target_col], m1.y_train_dict[target_col], m1.feature_cols, config.TARGET_COLS,model=m1.model_trainer.models[target_col])
    
    
    visualizer.plot_IC_IR_win_rate(factor_analysis_system.test_results)
    visualizer.plot_feature_correlation(m1.train_data, m1.y_train_dict, features=m1.feature_cols, target=target_col)
    visualizer.analyze_factor_distribution(m1.train_data[target_col][factor_name])
    
    
    log_print("训练模型用于SHAP分析...")
    
    
    visualizer.plot_shap_values(factor_analysis_system.shap_values)
    
    
    visualizer.plot_shap_summary(factor_analysis_system.shap_values['importance'])
    
    
    top_features = factor_analysis_system.shap_values['importance'].head(3)['feature'].tolist()
    for feature in top_features:
        visualizer.plot_shap_dependence(factor_analysis_system.shap_values, feature)
    