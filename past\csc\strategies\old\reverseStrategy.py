# -*- coding: utf-8 -*-
"""
Created on Tue Jul 20 14:52:36 2021

@author: humy2
"""

""""""
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle

class ReverseStrategy(StrategyTemplate):
    """"""

    author = "vnpy"

    boll_window = 20
    boll_dev = 2
    trailing_long = 0.5
    trailing_short = 0.5
    lots = 1

    boll_up = 0
    boll_down = 0
    trading_size = 0
    intra_trade_high = 0
    intra_trade_low = 0
    long_stop = 0
    short_stop = 0

    buy_price = 0
    short_price = 0

    parameters = [
        'boll_window',
        'boll_dev',
        'trailing_long',
        'trailing_short',
        'lots',
        'edge',
        'minEdge',
        'gamma',
        'eta',
        'maxPos',
        'sizes',
        'loss',
        'maker',
        'refer',
        'priceticks',
        'validVolume',
        'safeVolume',
        'loss',
        'stop',
        'referHedgeFlag',
        'neverStopFlag',
        'useReverseOrder',
        'useReverseOrderTargetPosition',
        'reverseOrderTargetPosition',
        'useSVMSingal',
        'fixedFader',
        'tradingOffsetSignal'
    ]
    variables = [
        'boll_up',
        'boll_down',
        'trading_size',
        'intra_trade_high',
        'intra_trade_low',
        'long_stop',
        'short_stop',
        'buy_price',
        'short_price'
    ]

    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        #self.sbg = SecondBarGenerator(self.on_bar)
        #self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        #self.am = ArrayManager(size=300)

        self.buy_vt_orderids = []
        self.short_vt_orderids = []
        self.buy_reverse_orderids = []
        self.short_reverse_orderids = []

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net=0
        self.realPnl=0
        self.fair = 0
        self.cumPnl=0
        length=99999
        self.mFair=np.zeros(length) # maker fair price 
        self.mAsk=np.zeros(length)  
        self.mBid=np.zeros(length)
        self.mCount=0
        self.rCount=0
        self.pauseCount=0
        self.lastMaker={"net":0, "tick":None, "timepoint":0}
        self.maxLoss = self.loss
        self.isQuoting=False
        self.lastTicks={self.maker:0,self.refer:0}
        self.refer_oid=0
        self.reverseOrderModeFlag=False
        self.bidOffset=[]
        self.askOffset=[]
        self.avg_price_trade_refer=0
        self.avg_price_trade_maker=0
        self.fairPriceByReferTrading = 0
        self.tradingOffset = 0
        if self.useSVMSingal:
            iid='hc2111'
            date='2021-07-01'
            #TODO 引入正确模型
            Input = open('D:/vnpy_repo/config/svm/hc_svmB_%s_%s.pkl'%(iid,date), 'rb')
            self.svcmA = pickle.load(Input)
            Input.close()
            
            Input = open('D:/vnpy_repo/config/svm/hc_svmB_%s_%s.pkl'%(iid,date), 'rb')
            self.svcmB = pickle.load(Input)
            Input.close()
            
            Input = open('D:/vnpy_repo/config/svm/hc_scalerB_%s_%s.pkl'%(iid,date), 'rb')
            self.scalerA = pickle.load(Input)
            Input.close()
            
            Input = open('D:/vnpy_repo/config/svm/hc_scalerB_%s_%s.pkl'%(iid,date), 'rb')
            self.scalerB = pickle.load(Input)
            Input.close()
            
            self.SVMSignal=0
            self.SVMSignalBid=0
            self.BidCount=0
            self.SVMSignalAsk=0
            self.AskCount=0
        
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def getAsk5(self,tick,ask_volume,away_price):
        volume=0
        for i in range(1,6):
            try:
                volume += tick.__getattribute__('ask_volume_%d'%i)
            except:
                volume +=0
            if volume>=ask_volume:
                short_price = tick.__getattribute__('ask_price_%d'%i)
                break
        if volume<ask_volume:
            while i>0:
                try:
                    short_price = tick.__getattribute__('ask_price_%d'%i)+away_price
                    break
                except:
                    i-=1
        return short_price
        
    def getBid5(self,tick,buy_volume,away_price):
        volume=0
        for i in range(1,6):
            try:
                volume += tick.__getattribute__('bid_volume_%d'%i)
            except:
                volume +=0
            if volume>=buy_volume:
                buy_price = tick.__getattribute__('bid_price_%d'%i)
                break
        if volume<buy_volume:
            while i>0:
                try:
                    buy_price = tick.__getattribute__('bid_price_%d'%i) - away_price
                    break
                except:
                    i-=1
        return buy_price
    
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair 
    
    # position fade volume
    def getFadeVolume(self,net,gamma): # 
        bidFade=0
        askFade=0    
        if net>0:
            if net>=0.6*self.maxPos:            
                #askFade= -round(gamma*net)      # <0
                bidFade= round(3*gamma*net)     # >0
            else:
                #askFade= -round(0.5*gamma*net)  # <0
                bidFade= round(gamma*net)       # >0
        if net<0:
            if net<=-0.6*self.maxPos:
                askFade= -round(3*gamma*net)    # >0
                #bidFade= round(gamma*net)       # <0            
            else:
                askFade= -round(gamma*net)      # >0
                #bidFade= round(0.5*gamma*net)   # <0     
        return bidFade,askFade

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.maker:
                    self.on_tick_maker(ticks[key])
                elif key==self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        
        
        #储存之前的tick
        
    def on_tick_refer(self,tick):
        if self.referHedgeFlag:
            if self.get_pos(self.maker)+self.get_pos(self.refer)!=0:
                if self.refer_oid!=0:
                    self.cancel_order(self.refer_oid[0])
                if self.get_pos(self.maker)+self.get_pos(self.refer)<0:
                    self.refer_oid = self.buy(self.refer,tick.ask_price_1, abs(self.get_pos(self.maker)+self.get_pos(self.refer)),'referHedge') 
                else:
                    self.refer_oid = self.short(self.refer,tick.bid_price_1, abs(self.get_pos(self.refer)+self.get_pos(self.maker)),'referHedge')
        '''if self.useSVMSingal and self.mCount>1:
            #TODO priceticks改为sizes
            #预测
            self.bidOffset.append(self.getBid5(self.lastMaker["tick"],300,0)- tick.bid_price_1)
            self.askOffset.append(self.getAsk5(self.lastMaker["tick"],300,0)- tick.ask_price_1)
            self.bidOffset = self.bidOffset[-40:]
            self.bidOffset = self.askOffset[-40:]
            if self.rCount>40:
                offset_bid,b,c = tb.MACD(np.array(self.bidOffset[:]),2,30)
                offset_ask,b,c = tb.MACD(np.array(self.bidOffset[:]),2,30)
                vol = tick.volume - self.tickReferPre.volume
                if vol>0:
                    tradePrice_avg = (tick.turnover-self.tickReferPre.turnover)/vol/self.priceticks[self.refer]
                    TRADEDPRICE_AVG_BID1 = tradePrice_avg-self.tickReferPre.bid_price_1
                    TRADEDPRICE_AVG_ASK1 = tradePrice_avg-self.tickReferPre.ask_price_1
                else:
                    TRADEDPRICE_AVG_BID1=0
                    TRADEDPRICE_AVG_ASK1=0
                X_test_A = [vol, TRADEDPRICE_AVG_ASK1, tick.bid_volume_1,  tick.ask_price_1-self.tickReferPre.ask_price_1, offset_ask[-1]]
                X_test_B = [vol, TRADEDPRICE_AVG_BID1, tick.ask_volume_1,  tick.ask_price_1-self.tickReferPre.ask_price_1, offset_bid[-1]]
                self.SVMSignal = self.svcmA.predict(self.scalerA.transform([X_test_A])) + self.svcmB.predict(self.scalerB.transform([X_test_B]))*(-1)
                self.SVMSignal = self.SVMSignal[0]'''
        try:
            turnover = tick.turnover - self.tickReferPre.turnover
            vol = tick.volume - self.tickReferPre.volume
            if vol>0:
                self.avg_price_trade_refer = turnover/vol/self.sizes[self.refer]
            else:
                self.avg_price_trade_refer = 0
        except:
            turnover=0
            vol=0
            self.avg_price_trade_refer = 0

        if self.useSVMSingal:
            if self.rCount>1 and tick.volume-self.tickReferPre.volume>=0:
                
                if (tick.bid_price_1> self.tickReferPre.bid_price_1) or (tick.volume-self.tickReferPre.volume>=100 \
                                                                          and 2*turnover/vol/self.sizes > self.tickReferPre.bid_price_1+self.tickReferPre.ask_price_1 ):
                # if (tick.bid_price_1> self.tickReferPre.bid_price_1):
                    self.SVMSignalAsk= 1
                    # self.SVMSignalBid=0
                    self.AskCount=5
                if (tick.ask_price_1< self.tickReferPre.ask_price_1) or (tick.volume-self.tickReferPre.volume>=100 \
                                                                          and 2*turnover/vol/self.sizes < self.tickReferPre.bid_price_1+self.tickReferPre.ask_price_1 ):
                # elif (tick.ask_price_1< self.tickReferPre.ask_price_1): 
                    self.SVMSignalBid= -1
                    # self.SVMSignalAsk=0
                    self.BidCount=5
            if self.BidCount<=0:
                self.SVMSignalBid=0
            if self.AskCount<=0:
                self.SVMSignalAsk=0
        
            
        
        self.tickReferPre = tick
        self.rCount+=1

    def on_tick_maker(self, tick):
        
        stopFlag = False
        if (tick.datetime+timedelta(hours=8)).timestamp()>1634217131: #datetime.datetime(2021, 10, 14, 21, 12, 11).timestamp()
            tmpA=0
        #ts = self.get_pricetick()
        
        ts = self.priceticks[self.maker]
        minEdge = self.minEdge
        edge=self.edge
        net = self.net
        lots = self.lots
        eta = self.eta
        stdRange=0.5
        maxV=50
        shortResendFlag=False
        buyResendFlag=False
        adjust=1
        validVolume = self.validVolume
        mCount = self.mCount
        isBetter=True           #  是否自动更优报价， 某些条件满足选择更积极报价 
        
        #计算市场成交均价
        if self.mCount>0:
            turnover = tick.turnover - self.lastMaker["tick"].turnover 
            vol = tick.volume - self.lastMaker["tick"].volume
            if vol>0:
                self.avg_price_trade_maker = turnover/vol/self.sizes[self.maker]
            else:
                self.avg_price_trade_maker = 0
            #更新价差
            if self.tradingOffsetSignal:
                #更新价差
                if self.avg_price_trade_maker != 0 and self.avg_price_trade_refer != 0:
                    if self.tradingOffset!=0:
                        self.tradingOffset = (self.avg_price_trade_maker - self.avg_price_trade_refer)*0.01 + self.tradingOffset*0.99
                    else: #初始化
                        self.tradingOffset = self.avg_price_trade_maker - self.avg_price_trade_refer
                #更新成交理论价格
                if self.avg_price_trade_refer!=0:
                    self.fairPriceByReferTrading = self.avg_price_trade_refer + self.tradingOffset
                elif self.fairPriceByReferTrading!=0: #保持交易理论价不变
                    pass
                else:
                    self.fairPriceByReferTrading = 0
                #avg_price_trade_refer清0
                self.avg_price_trade_refer = 0
            
            
        
        # 1. Filter ：非可控情景暂停 
        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
            # 持仓超限暂停 、在途订单超限暂停、
            if self.isQuoting and abs(net)>self.maxPos:
                self.isQuoting=False 
                self.pauseCount=self.rCount+100      
                print("Net Position limit pause",tick.datetime+timedelta(hours=8))
            '''
            if self.isQuoting and len(orderList)>10:     # or mdDelay>mdDelayLimit or tdDelay>tdDelayLimit 
                self.isQuoting=False 
                self.pauseCount=rCount+10    
                print("Delay limit pause",tick.datetime)    
            '''                   
            if self.isQuoting and self.cumPnl< -self.maxLoss: # 亏损超限暂停
                self.isQuoting=False 
                self.pauseCount=self.rCount+100
                self.maxLoss += self.loss  # 
                print("maker Loss limit pause",tick.datetime+timedelta(hours=8))
            # market cross pause
            if self.isQuoting and mCount>5 and (tick.ask_price_1 < self.lastMaker["tick"].bid_price_1 -ts or tick.bid_price_1 > self.lastMaker["tick"].ask_price_1 +ts):
                self.isQuoting=False 
                self.pauseCount=self.rCount+10      
                print("maker gap limit pause",tick.datetime+timedelta(hours=8)) #   
            # market big volume pause 
            '''
            if self.isQuoting and mCount>5 and (tick.volume -self.lastMaker["tick"].volume) > safeVolume : # 安全量可能不足、价格可能被击穿 
                self.isQuoting=False 
                self.pauseCount=rCount+10   
                print("maker Big volume pause", tick.datetime) #  
            '''
            # near to market limit price  or  缺失流动性 

            if (tick.limit_up and tick.limit_down) and self.isQuoting and (tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (
                tick.bid_price_1 > float(tick.limit_up) - 5*ts or  # 涨停附近                   
                tick.ask_price_1 < float(tick.limit_down) + 5*ts):   # 跌停附近                  
                    self.isQuoting=False
                    self.pauseCount=self.rCount + 100
                    print("price limit pause", tick.datetime+timedelta(hours=8))

            #TODO： 时间逻辑： 1、 时间暂停 22:59 10:14 11:29  14:59.. 暂未实现  2、 收盘前 adjust=2 不增仓 ，积极处理持仓
            '''
            if self.isQuoting and not refer.datetime: # "2020-07-26 23:04:21.000001"
                 self.isQuoting=False 
                 self.pauseCount=self.rCount + 100  
                 print("time section pause", tick.datetime)
                 '''
        #check resume quoting............................................... 
        else:
            if abs(net)<self.maxPos and self.pauseCount<self.rCount:
                self.isQuoting=True 
                
                
        
        
        
        #### Algo : JoinMarket  ~~~~~~~无效价格情况暂未处理~~~~~~~~~~~~~~~~~~ 
        spread = tick.ask_price_1 - tick.bid_price_1
        away_price = ts*max(1, adjust*eta*max(edge/3, spread/2, 3*stdRange)) 
        bidP = self.getBid5(tick,validVolume, away_price)
        askP = self.getAsk5(tick,validVolume, away_price)         #   L2行情更稳定、L1行情偏积极     
        bidP_safe = self.getBid5(tick,self.safeVolume, away_price)         #   
        askP_safe = self.getAsk5(tick,self.safeVolume, away_price)         #   
        self.fair = self.getFairPrice(self.fair,askP,bidP, edge*ts)                            #   有效量价对应的中间价 
        pnl= net*(self.fair-self.avg)/(abs(net)*ts) if net!=0 and self.fair>0 else 0
        bidFade,askFade = self.getFadeVolume(net,self.gamma) 
        
        if self.useReverseOrder:
            askP = self.getAsk5(tick, eta*validVolume + bidFade, away_price)   # askFade 
            bidP = self.getBid5(tick, eta*validVolume + askFade, away_price)   # bidFade    
            if net<self.lastMaker["net"]<-1:
                bidP -=2*away_price   
            if net>self.lastMaker["net"]>1: 
                askP +=2*away_price
        else:
            askP = self.getAsk5(tick, eta*validVolume + askFade, away_price)   # askFade 
            bidP = self.getBid5(tick, eta*validVolume + bidFade, away_price)   # bidFade
            if net<self.lastMaker["net"]<-1:
                askP +=2*away_price 
            if net>self.lastMaker["net"]>1: 
                bidP -=2*away_price 
        # price fade : not best if hold positions  
        '''
        if net>=lots:# 盘口量特别大的情况；Fade Volume 有可能失效 ,   Fade price  
             bidP= min(bidP, tick.bid_price_1 - ts)
        if net<=-lots:
             askP= max(askP, tick.ask_price_1 + ts)  
        '''
        # 增仓 fade price # 尽量不连续增仓 
        
        #SVM Part
        if self.useSVMSingal:
            if self.BidCount>=1:
                self.BidCount-=1
                bidP+= self.SVMSignalBid * ts
                
            if self.AskCount>=1:
                self.AskCount-=1
                askP+= self.SVMSignalAsk * ts
            
            
        #fixedFader Part
        if self.fixedFader!=0:
            askP+= self.fixedFader
            bidP+= self.fixedFader
        # safety 做市单避免被慌偏 not cross history + only better 1 tick of last & safe price             
        #if mCount>10:                        
            #askP=max(askP, self.mBid[mCount-6:mCount-1].max(), self.mAsk[mCount-1]-ts, askP_safe -ts) #  
            #bidP=min(bidP, self.mAsk[mCount-6:mCount-1].min(), self.mBid[mCount-1]+ts, bidP_safe +ts) #  not cross history + only better 1 tick   
            #stdRange= self.mFair[mCount-8:mCount-1].std()/ts # 
        stdRange= self.mFair[mCount-8:mCount-1].std()/ts # 
        # try better price next ： Best+1 or adjust volume 
        
        #askP = min(askP, tick.ask_price_1 + edge*ts)
        #bidP = max(bidP, tick.bid_price_1 - edge*ts)
        loss=100000
        if isBetter and (mCount>150 and  self.rCount>30+self.pauseCount and self.cumPnl>-2*loss and 
                         abs(net)<0.7*self.maxPos):              
             adjust=0.5  # 下一笔 Tick 尝试缩小 有效量 
        else:
             adjust=1   # 收盘前 adjust=3 , 有最新成交 adjust=3  
        # 最小价宽限制   
             
        if minEdge>1:
            gg=(ts*minEdge -(askP-bidP))
            if gg>0:
                if self.tradingOffsetSignal: #使用refer的成交来调节多余的tick
                    midP = (askP+bidP)/2
                    if gg>=round(2*abs(midP-self.fairPriceByReferTrading)): #足够gg分配
                        if midP>self.fairPriceByReferTrading: #定价偏高
                            bidP-= round(2*abs(midP-self.fairPriceByReferTrading))
                        else:
                            askP+= round(2*abs(midP-self.fairPriceByReferTrading))
                        #分配剩余gg
                        gg = gg - round(2*abs(midP-self.fairPriceByReferTrading))
                        smallerSide = int(gg / 2 / ts) *ts
                        biggerSide = gg - smallerSide
                        if  net>=0: 
                            askP += smallerSide
                            bidP -= biggerSide
                        else:
                            askP += biggerSide
                            bidP -= smallerSide
                    else:
                        if midP>self.fairPriceByReferTrading: #定价偏高
                            bidP-=gg
                        else:
                            askP+=gg
                   
                    
                else:
                    smallerSide = int(gg / 2 / ts) *ts
                    biggerSide = gg - smallerSide
                    if self.useReverseOrder:
                        if  net>=0: 
                            askP += biggerSide
                            bidP -= smallerSide
                        else:
                            askP += smallerSide
                            bidP -= biggerSide
                    else:
                        if  net>=0: 
                            askP += smallerSide
                            bidP -= biggerSide
                        else:
                            askP += biggerSide
                            bidP -= smallerSide
        '''
        if minEdge>1:
               gg=(ts*minEdge -(askP-bidP))/2 
               if  gg>0: 
                   askP += gg+0.01
                   bidP -= gg'''
               #if  spread > minEdge*ts/2:    # 市场价宽较大不报价  （超过2tick 不报价 ， if edge=5， 60%义务足够了）
                    #isQuoting  =False 
                    #pauseCount =rCount +5 
        # 报价取整
        bidP = ts*round(bidP/ts) 
        askP = ts*round(askP/ts)            
        # 微调
        ''' cpp版本已经删除
        if minEdge>1:
            if askP-bidP>minEdge*ts + 0.1*ts:     #  有可能取整宽了
                if net<=0 :
                    bidP +=ts 
                else:
                    askP -=ts
            if askP-bidP<minEdge*ts - 0.1*ts:     #  有可能取整窄了
                if net>=0 :
                    bidP -=ts 
                else:
                    askP +=ts      
        '''
        stdEdge=round((askP-bidP)/ts)   #  标准价差tick数  
        #### Algo End ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        # save
        if self.reverseOrderModeFlag:
            bidP = self._revBidP
            askP = self._revAskP
        self.cumPnl=round((net*self.fair + self.realPnl)*self.sizes[self.maker])  # 累计盈亏 ？？  
        if bidP>0 and askP>0:               
            self.mBid[mCount]=bidP
            self.mAsk[mCount]=askP
            self.mFair[mCount]=self.fair # mid price  save 
            self.mCount +=1  
            
            
         
        self.lastMaker["timepoint"]=mCount
        self.lastMaker["net"] =net
        self.lastMaker["tick"]=tick 
        
        #优先处理,反手单
        if self.reverseOrderModeFlag: #默认反手单只持续一个tick
            self.reverseOrderModeFlag=False
            return 
        elif self.short_reverse_orderids!=[] or self.buy_reverse_orderids!=[]: 
            self.cancel_all()
            self.short_reverse_orderids=[]
            self.buy_reverse_orderids=[]
        
        
        
        stop=max(self.stop *edge,2*stdRange)*(lots/(abs(net)+lots)) # stop 根据行情变化动态调整
        if self.neverStopFlag:
            stop=99999
        if net>0 and 0<spread<=max(1, 0.8*edge)*ts and stdRange<1 and stdEdge<1.2*edge: 
              if pnl>=stop:  
                  stopFlag = True
                  self.cancel_all() # 避免自成交
                  VV=min(maxV-1,max(1,round(abs(net/1.3))))
                  if VV==lots:
                       VV +=1   # 不是好办法， 只是为了区分 做市单和 其他订单，保证其他订单量不为 lots   
                  self.short_vt_orderids = self.short(self.maker,tick.bid_price_1, VV,'stop_profit')
                  print("send hedge order",tick.bid_price_1,VV)
              if pnl<=-stop:    
                  stopFlag = True
                  self.cancel_all() # 避免自成交
                  self.short_vt_orderids = self.short(self.maker,tick.bid_price_1, abs(net),'stop_loss')
                  print("send hedge order",tick.bid_price_1,abs(net))
        # 仅Maker价格稳定、有流动性情况对冲： 其他情况考虑主力对冲（暂未实现）        
        #if not hedgeOrder and mCount>35 and net<0 and 0<spread<=max(1, 0.8*edge)*ts and stdRange<1 and stdEdge<1.2*edge: 
        if net<0 and 0<spread<=max(1, 0.8*edge)*ts and stdRange<1 and stdEdge<1.2*edge: 
              if pnl>=stop:  
                   stopFlag = True
                   self.cancel_all() # 避免自成交
                   VV=min(maxV-1,max(1,round(abs(net/1.3))))  
                   if VV==lots:
                       VV +=1   # 不是好办法， 只是为了区分 做市单和 其他订单，保证其他订单量不为 lots                      
                   self.buy_vt_orderids = self.buy(self.maker,tick.ask_price_1, VV,'stop_profit') 
                   print("send hedge order",tick.ask_price_1,VV)
              if pnl<=-stop: 
                   stopFlag = True
                   self.cancel_all() # 避免自成交
                   self.buy_vt_orderids = self.buy(self.maker,tick.ask_price_1, abs(net),'stop_loss')  
                   print("send hedge order",tick.ask_price_1, abs(net),'stop_loss')
        # TODO:  收盘前自动处理净持仓， 连续成交保护( 市场单边大幅波动)           
       
        # 实盘用 FOK hedge, 不需要这段 
        #if hedgeOrder:                 
             #logging.info(str(mCount)+",PnL:"+str(pnl)+",HedgePrice:"+str(hedgeOrder.limit_price)+",HedgeVolume:"+str(hedgeOrder.volume_orign)+
                          #","+str(tick.datetime)+", hedged............................")           
             #api.cancel_order(hedgeOrder)
             #hedgeOrder=None  
        # 3.check Quote
        
        if askP!=self.short_price:
            shortResendFlag=True
        if bidP!=self.buy_price:
            buyResendFlag=True
            
        self.short_price = askP
        self.buy_price = bidP
        if mCount>35 and not stopFlag:
            if not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(self.maker,self.buy_price, self.lots,'MM')
            elif buyResendFlag and self.buy_vt_orderids[0]:
                if self.cancel_order(self.buy_vt_orderids[0]):  #cancel完会跳转至onOrder，然后自动发一个单
                    self.buy_vt_orderids = self.buy(self.maker,self.buy_price, self.lots,'MM')
            
            if not self.short_vt_orderids:
                self.short_vt_orderids = self.short(self.maker,self.short_price, self.lots,'MM')
            elif shortResendFlag and self.short_vt_orderids[0]:
                if self.cancel_order(self.short_vt_orderids[0]): #cancel完会跳转至onOrder，然后自动发一个单
                    self.short_vt_orderids = self.short(self.maker,self.short_price, self.lots,'MM')


    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if trade.direction.value=='多':
            if self.net>=0 :            
                 self.avg = (self.net*self.avg +volume*price)/(self.net+volume)              
            elif volume+self.net>0: # net<0 # 平仓
                 self.avg = price         
            self.net += volume 
            self.realPnl -=volume*price
        #         
        if trade.direction.value=='空':    
            if self.net<=0:
                self.avg =(-self.net*self.avg + volume*price)/(-self.net+volume)
            elif volume-self.net>0: # net >0 # 平仓
                self.avg=price
            self.net -= volume 
            self.realPnl +=volume*price
        if volume==self.lots and self.useReverseOrder and (not self.reverseOrderModeFlag):
            if trade.direction.value=='多':
                if self.useReverseOrderTargetPosition:
                    if self.net>self.reverseOrderTargetPosition:
                        self.reverseOrderModeFlag=True
                        self.cancel_all()  #cancel完会跳转至onOrder，然后自动发一个单？
                        self.buy_reverse_orderids=[]
                        self.short_reverse_orderids=[]
                        self._revBidP = price  - self.edge * self.priceticks[self.maker]
                        self._revAskP = price-1
                        self.buy_reverse_orderids = self.buy(self.maker, self._revBidP, self.lots, 'MM')
                        self.short_reverse_orderids = self.short(self.maker, self._revAskP, min(self.net-self.reverseOrderTargetPosition,self.lots*2),'reverseOrderTargetPosition')
                else:
                    self.reverseOrderModeFlag=True
                    self.cancel_all()  
                    self.buy_reverse_orderids=[]
                    self.short_reverse_orderids=[]
                    self._revBidP = price  - self.edge * self.priceticks[self.maker]
                    self._revAskP = price
                    self.buy_reverse_orderids = self.buy(self.maker, self._revBidP, self.lots, 'MM')
                    self.short_reverse_orderids = self.short(self.maker, self._revAskP, self.lots*2, 'reverse')
                    
            if trade.direction.value=='空':
                if self.useReverseOrderTargetPosition:
                    if self.net<self.reverseOrderTargetPosition:
                        self.reverseOrderModeFlag=True
                        self.cancel_all()  
                        self.buy_reverse_orderids=[]
                        self.short_reverse_orderids=[]
                        self._revBidP = price+1  
                        self._revAskP = price + self.edge * self.priceticks[self.maker]
                        self.buy_reverse_orderids = self.buy(self.maker, self._revBidP, min(self.reverseOrderTargetPosition-self.net,self.lots*2), 'reverseOrderTargetPosition')
                        self.short_reverse_orderids = self.short(self.maker, self._revAskP, self.lots ,'MM')
                else:
                    self.reverseOrderModeFlag=True
                    self.cancel_all() 
                    self.buy_reverse_orderids=[]
                    self.short_reverse_orderids=[]
                    self._revBidP = price
                    self._revAskP = price + self.edge * self.priceticks[self.maker]
                    self.buy_reverse_orderids = self.buy(self.maker, self._revBidP, self.lots*2, 'reverse')
                    self.short_reverse_orderids = self.short(self.maker, self._revAskP, self.lots, 'MM')
                    
        #logging.info("trade:,"+str(trade.price)+","+str(trade.volume)+","+trade.direction +
                     #","+str(tick.datetime)+",Net:"+str(net)+",Avg:"+str(avg))     
        #return net,avg,realPnl

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
        ]:
            if order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
        
            
    #def cancel_all(self):
        #if self.buy_vt_orderids:
            #self.cancel_order(self.buy_vt_orderids[0])
        #if self.short_vt_orderids:
            #self.cancel_order(self.short_vt_orderids[0])

