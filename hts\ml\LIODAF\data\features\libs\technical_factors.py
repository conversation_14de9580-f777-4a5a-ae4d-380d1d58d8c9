
"""
技术指标因子
@author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory
from core import config
cols=[
    "ob_depth",
    "feature_rank_cov_2",
    "trade_impact",
    "trade_flow",
    "last_out_ma",
    "nettradeprice_mid",
    "mom_last",
    "up_pct",
    "down_pct",
    "trend_strength",
    "alpha_49_enhanced",
    "alpha_101_enhanced",
    "feature_rising_n_falling_trends",
    "feature_long_short_mean_diff",
    "volumn_corr"
]




def ob_depth(df):
    
    return np.log((df['AskPrice5'] - df['AskPrice1']) / (df['BidPrice1'] - df['BidPrice5']))


factor_manager.register_factor(Factor(
    name="ob_depth",
    category=FactorCategory.TECHNICAL,
    description="计算订单簿深度",
    calculation=ob_depth,
    source="lining-sxod",
    dependencies=['AskPrice5', 'AskPrice1', 'BidPrice1', 'BidPrice5']
))


def feature_rank_cov_2(df, window=None):  
    if window is None:
        window = 6
    a = df['AskPrice1']
    b = df['BidPrice1']
    c = df['LastPrice'].diff()
    
    
    
    
    
    
    
    
    
    
    
    
    df_corr = pd.concat([a, b], axis=1)
    
    rolling_corr = df_corr.rolling(window=window, min_periods=1).corr()
    
    
    corr_values = rolling_corr.iloc[1::2, 0].values  
    
    corr_series = pd.Series(corr_values, index=c.index)
    
    factor = (-1) * c.rank() * corr_series
    
    
    
    
    
    return np.nan_to_num(factor, posinf=0, neginf=0)


factor_manager.register_factor(Factor(
    name="feature_rank_cov_2",
    category=FactorCategory.TECHNICAL,
    description="计算买卖盘价格相关性的排名组合因子",
    calculation=feature_rank_cov_2,
    source="lining-sxod",
    dependencies=['AskPrice1', 'BidPrice1', 'LastPrice'],
    parameters={'window': 6}
))


def trade_impact(df, window=None, contract_mul=config.MULT):
    
    if window is None:
        window = 10

    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    net_tradeprice = trade_value / (trade_vol * contract_mul)

    
    trade_price = np.where(net_tradeprice > mid, df['BidPrice1'], -df['AskPrice1'])
    ema_vol = trade_vol.ewm(span=window, adjust=False).mean()
    return ema_vol / trade_price


factor_manager.register_factor(Factor(
    name="trade_impact",
    category=FactorCategory.TECHNICAL,
    description="计算交易冲击因子，衡量交易量对价格的影响程度",
    calculation=trade_impact,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'window': 10}
))


def trade_flow(df, window=None, contract_mul=200):
    
    if window is None:
        window = 10
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    net_tradeprice = trade_value / (trade_vol * contract_mul)

    
    trade_vol_with_dir = pd.Series(np.where(net_tradeprice > mid, trade_vol, -trade_vol),index=df.index)
    return trade_vol_with_dir.ewm(span=window, adjust=False).mean()


factor_manager.register_factor(Factor(
    name="trade_flow",
    category=FactorCategory.TECHNICAL,
    description="计算交易流量因子，衡量买卖方向的交易量",
    calculation=trade_flow,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'window': 10, 'contract_mul': 200}
))

def nettradeprice_mid(df, contract_mul=200):  
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    net_tradeprice = trade_value / (trade_vol * contract_mul)
    net_tradeprice = np.where(pd.isna(net_tradeprice), mid, net_tradeprice)
    return net_tradeprice - mid


factor_manager.register_factor(Factor(
    name="nettradeprice_mid",
    category=FactorCategory.TECHNICAL,
    description="计算净交易价格与中间价的差异",
    calculation=nettradeprice_mid,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'contract_mul': 200}
))


def mom_last(df, window=None):  
    
    if window is None:
        window = 10
    shift_price = df['LastPrice'].shift(window)
    return np.where(pd.isna(shift_price), 0, np.log(df['LastPrice'] / shift_price))


factor_manager.register_factor(Factor(
    name="mom_last",
    category=FactorCategory.TECHNICAL,
    description="计算动量因子",
    calculation=mom_last,
    source="lining-sxod",
    dependencies=['LastPrice'],
    parameters={'window': 1}
))


def last_out_ma(df, window:int):
    
    ma = df['LastPrice'].rolling(window=window).mean()
    std = df['LastPrice'].rolling(window=window).std()

    return np.nan_to_num((df['LastPrice'] - ma) / std, posinf=0, neginf=0)


for window in [10,30,60,240]:
    factor_manager.register_factor(Factor(
        name=f"last_out_ma_{window}",
        category=FactorCategory.TECHNICAL,
    description="计算最新价格的反转因子，衡量价格偏离均值的程度",
    calculation=lambda df: last_out_ma(df, window),
    source="lining-sxod",
        dependencies=['LastPrice'],
        parameters={'window': window}
    ))



def up_pct(df, window=None):
    
    if window is None:
        window = 240
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    mid_pct = mid.pct_change()
    is_up = np.where(mid_pct > 0, 1, 0)
    mid_up_pct = is_up * mid_pct
    return (mid_up_pct ** 2).rolling(window=window, min_periods=1).sum() / (mid_pct ** 2).rolling(window=window,
                                                                                                  min_periods=1).sum()


factor_manager.register_factor(Factor(
    name="up_pct",
    category=FactorCategory.TECHNICAL,
    description="计算上行波动占比，衡量上涨波动在总波动中的比例",
    calculation=up_pct,
    source="lining-sxod",
    dependencies=['AskPrice1', 'BidPrice1'],
    parameters={'window': 240}
))

def volumn_corr(df, window=None):
    if window is None:
        window=60
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1'])/2

    return mid.rolling(window=window, min_periods=1).corr(trade_vol)


factor_manager.register_factor(Factor(
    name="volumn_corr",
    category=FactorCategory.TECHNICAL,
    description="计算成交量相关性因子",
    calculation=volumn_corr,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'window': 60}
))



def down_pct(df, window=None):
    
    if window is None:
        window = 240
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    mid_pct = mid.pct_change()
    is_down = np.where(mid_pct > 0, 0, 1)
    mid_down_pct = is_down * mid_pct
    return (mid_down_pct ** 2).rolling(window=window, min_periods=1).sum() / (mid_pct ** 2).rolling(window=window,
                                                                                                    min_periods=1).sum()


factor_manager.register_factor(Factor(
    name="down_pct",
    category=FactorCategory.TECHNICAL,
    description="计算下行波动占比，衡量下跌波动在总波动中的比例",
    calculation=down_pct,
    source="lining-sxod",
    dependencies=['AskPrice1', 'BidPrice1'],
    parameters={'window': 240}
))



def trend_strength(df, window=None):
    
    if window is None:
        window = 240
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    mid_abs_diff = np.abs(mid.diff())

    return (mid - mid.shift(window)) / mid_abs_diff.rolling(window=window, min_periods=1).sum()


factor_manager.register_factor(Factor(
    name="trend_strength",
    category=FactorCategory.TECHNICAL,
    description="计算趋势强度，衡量价格变化的方向性",
    calculation=trend_strength,
    source="lining-sxod",
    dependencies=['AskPrice1', 'BidPrice1'],
    parameters={'window': 240}
))



def alpha_49_enhanced(df, window_long=20, window_short=10, threshold_scale=0.5):
    
    
    price = df['LastPrice']
    ret_long = (price.shift(window_short) - price.shift(window_long)) / price.shift(window_long)  
    ret_short = (price - price.shift(window_short)) / price.shift(window_short)  
    part1 = (ret_long - ret_short)

    
    threshold = -threshold_scale * part1.rolling(60).std()

    
    condition = (part1 < threshold)
    factor = np.where(condition, 1, -1 * price.pct_change(1))
    return factor


factor_manager.register_factor(Factor(
    name="alpha_49_enhanced",
    category=FactorCategory.TECHNICAL,
    description="计算增强版alpha_49因子，基于价格变化加速度的反转策略",
    calculation=alpha_49_enhanced,
    source="lining-sxod",
    dependencies=['LastPrice'],
    parameters={'window_long': 20, 'window_short': 10, 'threshold_scale': 0.5}
))


def alpha_101_enhanced(df, window=20):
    
    
    price_diff = df['LastPrice'].diff(1)
    daily_range = df['High'] - df['Low']
    
    range_smoothed = daily_range.rolling(window, min_periods=1).mean() + 1e-6
    
    factor = price_diff / range_smoothed
    return factor


factor_manager.register_factor(Factor(
    name="alpha_101_enhanced",
    category=FactorCategory.TECHNICAL,
    description="计算增强版alpha_101因子，波动率调整后的价格变化",
    calculation=alpha_101_enhanced,
    source="lining-sxod",
    dependencies=['LastPrice', 'High', 'Low'],
    parameters={'window': 20}
))



def feature_rising_n_falling_trends(df, window1=1, window2=5):  
    

    
    lp_rate = df['LastPrice'].diff(window1)
    
    
    
    is_continuous_up = lp_rate.rolling(window=window2, min_periods=1).min() > 0
    
    is_continuous_down = lp_rate.rolling(window=window2, min_periods=1).max() < 0
    
    is_oscillating = ~(is_continuous_up | is_continuous_down)
    
    
    
    
    
    
    
    
    factor = np.where(is_continuous_up | is_continuous_down, lp_rate, -lp_rate)
    return np.nan_to_num(factor, posinf=0, neginf=0)


factor_manager.register_factor(Factor(
    name="feature_rising_n_falling_trends",
    category=FactorCategory.TECHNICAL,
    description="基于价格变化趋势的因子，在震荡市场中提供逆势交易信号，IC值为-0.06",
    calculation=feature_rising_n_falling_trends,
    dependencies=['LastPrice'],
    parameters={'window1': 1, 'window2': 5},
    source="lining-sxod"
))




def feature_long_short_mean_diff(df, long_window=50, short_window=10):  
    long_mean = df['LastPrice'].rolling(window=long_window).mean()
    short_mean = df['LastPrice'].rolling(window=short_window).mean()
    factor = np.nan_to_num((short_mean - long_mean) / df['LastPrice'], posinf=0, neginf=0)
    return np.nan_to_num(factor, posinf=0, neginf=0)


factor_manager.register_factor(Factor(
    name="feature_long_short_mean_diff",
    category=FactorCategory.TECHNICAL,
    description="计算长短期价格均值的相对差异，IC值为-0.02",
    calculation=feature_long_short_mean_diff,
    dependencies=['LastPrice'],
    parameters={'long_window': 50, 'short_window': 10},
    source="lining-YA-sxod"
))