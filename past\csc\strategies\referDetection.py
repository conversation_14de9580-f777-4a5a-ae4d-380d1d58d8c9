# -*- coding: utf-8 -*-
"""
Created on Tue Jul 20 14:52:36 2021
 
@author: humy2
"""

""""""
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle


class referDetection(StrategyTemplate):
    """"""

    author = "vnpy"

    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'level',
        'typ',  # typ='all' 代表按价位挂单, typ='grid' 代表隔位挂单
        'secondConfirmationFlag',  # 1正手, -1反手, 0
        'smartSecondConfirmationFlag',
        'endHedgeFlag',
        'joinVolume'
    ]

    def __init__(
            self,
            strategy_engine: StrategyEngine,
            strategy_name: str,
            vt_symbol: str,
            setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        #self.sbg = SecondBarGenerator(self.on_bar)
        #self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        #self.am = ArrayManager(size=300)

        self.buy_vt_orderids = []
        self.short_vt_orderids = []
        self.buy_reverse_orderids = []
        self.short_reverse_orderids = []
        self.fak_orderids = []

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net = 0
        self.realPnl = 0
        self.fair = 0
        self.cumPnl = 0
        length = 99999
        self.mFair = np.zeros(length)  # maker fair price
        self.mAsk = np.zeros(length)
        self.mBid = np.zeros(length)
        self.mCount = 0
        self.rCount = 0
        self.pauseCount = 0
        self.lastMaker = {"net": 0, "tick": None, "timepoint": 0}
        # self.maxLoss = self.loss
        self.isQuoting = False
        self.lastTicks = {self.maker: 0, self.refer: 0}
        self.refer_oid = 0
        self.reverseOrderModeFlag = False
        self.bidOffset = []
        self.askOffset = []
        self.avg_price_trade_refer = 0
        self.avg_price_trade_maker = 0
        self.fairPriceByReferTrading = 0
        self.tradingOffset = 0
        self.bidFade = 0
        self.askFade = 0
        self.short_price = np.inf
        self.buy_price = 0
        self.maker_oid_bid = ['']
        self.maker_oid_ask = ['']

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def getAsk5(self, tick, ask_volume, away_price):
        volume = 0
        for i in range(1, 6):
            try:
                volume += tick.__getattribute__('ask_volume_%d' % i)
            except:
                volume += 0
            if volume >= ask_volume:
                short_price = tick.__getattribute__('ask_price_%d' % i)
                break
        if volume < ask_volume:
            while i > 0:
                try:
                    short_price = tick.__getattribute__('ask_price_%d' % i) + away_price
                    break
                except:
                    i -= 1
        return short_price

    def getBid5(self, tick, buy_volume, away_price):
        volume = 0
        for i in range(1, 6):
            try:
                volume += tick.__getattribute__('bid_volume_%d' % i)
            except:
                volume += 0
            if volume >= buy_volume:
                buy_price = tick.__getattribute__('bid_price_%d' % i)
                break
        if volume < buy_volume:
            while i > 0:
                try:
                    buy_price = tick.__getattribute__('bid_price_%d' % i) - away_price
                    break
                except:
                    i -= 1
        return buy_price

    def getFairPrice(self, lastP, askP, bidP, edgeP):
        fair = lastP
        if askP > 0 and bidP > 0:
            if askP - bidP <= edgeP:  # 有流动性，中间价
                fair = 0.5 * (askP + bidP)
            else:  # 流动性不足, 不变
                if lastP > askP:
                    fair = askP
                if lastP < bidP:
                    fair = bidP
        return fair

    def getCumulativeBidVolume(self, tick, price):
        volume = 0
        for i in range(1, 6):
            if tick.__getattribute__('bid_price_%d' % i) >= price:
                try:
                    volume += tick.__getattribute__('bid_volume_%d' % i)
                except:
                    volume += 0
        return volume

    def getCumulativeAskVolume(self, tick, price):
        volume = 0
        for i in range(1, 6):
            if tick.__getattribute__('ask_price_%d' % i) <= price:
                try:
                    volume += tick.__getattribute__('ask_volume_%d' % i)
                except:
                    volume += 0
        return volume

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key] != self.lastTicks[key]:
                if key == self.maker:
                    self.on_tick_maker(ticks[key])
                if key == self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick

        #储存之前的tick

    def on_tick_refer(self, tick):
        # 先撤fak单
        for oid in self.fak_orderids.copy():
            self.cancel_order(oid)
            self.fak_orderids.remove(oid)

        ts = self.priceticks[self.refer]
        net = self.net

        spread = tick.ask_price_1 - tick.bid_price_1
        refer = self.refer
        maxPos = self.maxPos[refer]
        lots = self.lots[refer]
        maxPos = self.maxPos[refer]

        if self.endHedgeFlag:
            if ((dtime(14, 55) < tick.datetime.time() < dtime(14, 56))
                    or (dtime(22, 57) < tick.datetime.time() < dtime(22, 58))):
                self.cancel_all()
                if self.get_pos(self.refer) != 0:
                    if self.get_pos(self.refer) > 0:
                        refer_oid = self.short(self.refer, tick.bid_price_1, abs(self.get_pos(self.refer)), 'endHedge')
                        self.fak_orderids.append(refer_oid[0])
                    else:
                        refer_oid = self.buy(self.refer, tick.ask_price_1, abs(self.get_pos(self.refer)), 'endHedge')
                        self.fak_orderids.append(refer_oid[0])

        self.lastTicks[refer] = tick
        if spread < 100 and ((dtime(21, 0) < tick.datetime.time() < dtime(22, 57)
                              or (dtime(9, 0) < tick.datetime.time() < dtime(14, 55)))):

            all_orders = self.strategy_engine.active_limit_orders.values()

            if self.typ == 'all':
                askP_list = [tick.ask_price_1, tick.ask_price_2, tick.ask_price_3, tick.ask_price_4, tick.ask_price_5]
                bidP_list = [tick.bid_price_1, tick.bid_price_2, tick.bid_price_3, tick.bid_price_4, tick.bid_price_5]
                askV_list = [tick.ask_volume_1, tick.ask_volume_2, tick.ask_volume_3, tick.ask_volume_4,
                             tick.ask_volume_5]
                bidV_list = [tick.bid_volume_1, tick.bid_volume_2, tick.bid_volume_3, tick.bid_volume_4,
                             tick.bid_volume_5]

                canSendAsk_list = [True for i in range(5)]
                canSendBid_list = [True for i in range(5)]

                level = [np.min([int(i) - 1, 4]) for i in str(self.level)]  #level=123 代表bid1, bid2, bid3
                for order in all_orders:
                    for i in level:
                        if order.price == askP_list[i]:
                            canSendAsk_list[i] = False
                        if order.price == bidP_list[i]:
                            canSendBid_list[i] = False

                for i in level:
                    canSendAsk = canSendAsk_list[i]
                    askP = askP_list[i]
                    askV = askV_list[i]
                    if canSendAsk and askP and askV >= self.joinVolume and self.net > -maxPos:
                        self.short(self.refer, askP, lots, 'referDetection')
                    canSendBid = canSendBid_list[i]
                    bidP = bidP_list[i]
                    bidV = bidV_list[i]
                    if canSendBid and bidP and bidV >= self.joinVolume and self.net < maxPos:
                        self.buy(self.refer, bidP, lots, 'referDetection')

            if self.typ == 'grid' or self.typ == 'grid2':
                level = self.level  # 这里 level代表挂单距离
                askP = tick.ask_price_1
                bidP = tick.bid_price_1
                askV = tick.ask_volume_1
                bidV = tick.bid_volume_1
                canSendAsk = True
                canSendBid = True

                for order in all_orders:  # level=2时，若bid1, bid1-1, bid1-2, bid1-3全没单, 则挂bid1-2
                    for i in range(np.max([2 * level, 1])):
                        if order.price == askP + i * ts:
                            canSendAsk = False
                        if order.price == bidP - i * ts:
                            canSendBid = False
                if canSendAsk and askP and askV >= self.joinVolume and self.net > -maxPos:
                    self.short(self.refer, askP + level * ts, lots, 'referDetection')
                if canSendBid and bidP and bidV >= self.joinVolume and self.net < maxPos:
                    self.buy(self.refer, bidP - level * ts, lots, 'referDetection')

                if self.typ == 'grid2':  # 可从买一挂
                    level = self.level
                    askP = tick.ask_price_1
                    bidP = tick.bid_price_1
                    canSendAsk = True
                    canSendBid = True

                    for order in all_orders:
                        for i in range(np.max([level, 1])):
                            if order.price == askP + i * ts:
                                canSendAsk = False
                            if order.price == bidP - i * ts:
                                canSendBid = False
                    if canSendAsk and askP and askV >= self.joinVolume and self.net > -maxPos:
                        self.short(self.refer, askP, lots, 'referDetection')
                    if canSendBid and bidP and bidV >= self.joinVolume and self.net < maxPos:
                        self.buy(self.refer, bidP, lots, 'referDetection')
        self.last_tick = tick

    def on_tick_maker(self, tick):
        pass

    def on_trade(self, trade: TradeData):
        ticks = self.strategy_engine.ticks
        refer = self.refer
        tick_refer = ticks[refer]
        maxPos = self.maxPos
        spread = tick_refer.ask_price_1 - tick_refer.bid_price_1
        secondConfirmationFlag = self.secondConfirmationFlag
        tick = self.last_tick
        joinVolume = self.joinVolume
        if spread < 100:
            #智能2次确认
            if self.smartSecondConfirmationFlag:
                if trade.direction.value == '多':
                    if self.getCumulativeBidVolume(tick, trade.price) > 4 * joinVolume:  #不利成交
                        secondConfirmationFlag = 1
                    else:
                        secondConfirmationFlag = 0
                if trade.direction.value == '空':
                    if self.getCumulativeAskVolume(tick, trade.price) > 4 * joinVolume:  #不利成交
                        secondConfirmationFlag = 1
                    else:
                        secondConfirmationFlag = 0
            # 二次确认fak逻辑
            if secondConfirmationFlag == -1:  # 1代表正手, -1代表反手, 0代表不进行二次确认
                if trade.comments == 'referDetection':  # 反手
                    if trade.direction.value == '空':
                        refer_oid = self.buy(trade.vt_symbol, trade.price, trade.volume, 'secondConfirmationReverse')
                        self.fak_orderids.append(refer_oid[0])
                    if trade.direction.value == '多':
                        refer_oid = self.short(trade.vt_symbol, trade.price, trade.volume, 'secondConfirmationReverse')
                        self.fak_orderids.append(refer_oid[0])
            elif secondConfirmationFlag == 1:  # 正手
                if trade.comments == 'referDetection':
                    if trade.direction.value == '空':
                        refer_oid = self.short(trade.vt_symbol, trade.price, trade.volume, 'secondConfirmation')
                        self.askFade = 1
                        self.bidFade = 0
                        self.fak_orderids.append(refer_oid[0])
                    if trade.direction.value == '多':
                        refer_oid = self.buy(trade.vt_symbol, trade.price, trade.volume, 'secondConfirmation')
                        self.askFade = 0
                        self.bidFade = 1
                        self.fak_orderids.append(refer_oid[0])
            else:  # 不二次确认
                if trade.comments == 'referDetection':  # 反手
                    if trade.direction.value == '空':
                        self.askFade = 1
                        self.bidFade = 0
                    if trade.direction.value == '多':
                        self.askFade = 0
                        self.bidFade = 1
        return
