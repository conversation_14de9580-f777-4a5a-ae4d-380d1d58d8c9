from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Callable, Optional

from com.scalafi.openbook.orderbook import OrderBook

T = TypeVar('T')
T2 = TypeVar('T2')

class Cell(Generic[T]):
    """
    模拟Scala中的Cell类，用于处理可能为空的值
    """
    @staticmethod
    def from_option(option: Optional[T]) -> 'Cell[T]':
        """
        从可选值创建一个Cell
        """
        if option is None:
            return EmptyCell()
        else:
            return ValueCell(option)
    
    def map(self, f: Callable[[T], T2]) -> 'Cell[T2]':
        """
        将函数应用于Cell中的值
        """
        if isinstance(self, ValueCell):
            return ValueCell(f(self.value))
        else:
            return EmptyCell()
    
    def zip_map(self, other: 'Cell[T2]', f: Callable[[T, T2], T]) -> 'Cell[T]':
        """
        将两个Cell组合，并应用函数
        """
        if isinstance(self, ValueCell) and isinstance(other, ValueCell):
            return ValueCell(f(self.value, other.value))
        else:
            return EmptyCell()

class ValueCell(Cell[T]):
    """
    包含值的Cell
    """
    def __init__(self, value: T):
        self.value = value

class EmptyCell(Cell[T]):
    """
    空的Cell
    """
    pass

class BasicAttribute(ABC, Generic[T]):
    """
    基本属性特征
    """
    @abstractmethod
    def __call__(self, order_book: OrderBook) -> Cell[T]:
        pass
    
    def map(self, f: Callable[[T], T2]) -> 'BasicAttribute[T2]':
        """
        将函数应用于属性结果
        """
        original_call = self.__call__
        
        class MappedAttribute(BasicAttribute[T2]):
            def __call__(self, order_book: OrderBook) -> Cell[T2]:
                return original_call(order_book).map(f)
        
        return MappedAttribute()

class BasicAttributeFactory:
    """
    BasicAttribute工厂类，替代Scala的companion object
    """
    @staticmethod
    def from_func(f: Callable[[OrderBook], Cell[T]]) -> BasicAttribute[T]:
        """
        从函数创建BasicAttribute
        """
        class FunctionAttribute(BasicAttribute[T]):
            def __call__(self, order_book: OrderBook) -> Cell[T]:
                return f(order_book)
        
        return FunctionAttribute()

class BasicSetConfig(ABC):
    """
    基本配置接口
    """
    @property
    @abstractmethod
    def order_book_depth(self) -> int:
        pass
    
    def check_level(self, i: int) -> None:
        """
        检查级别是否有效
        """
        assert i > 0, "Level index should be greater than 0"
        assert i <= self.order_book_depth, f"Level index should be less than {self.order_book_depth}"

class DefaultConfig(BasicSetConfig):
    """
    默认配置实现
    """
    @property
    def order_book_depth(self) -> int:
        return 10

class BasicSet:
    """
    基本属性集
    """
    def __init__(self, config: BasicSetConfig):
        self.config = config
    
    def _ask_price(self, order_book: OrderBook, i: int) -> Cell[int]:
        """
        获取第i个卖价
        """
        sell_keys = sorted(order_book.sell.keys())
        if len(sell_keys) >= i:
            return Cell.from_option(sell_keys[i-1])
        else:
            return Cell.from_option(None)
    
    def _ask_volume(self, order_book: OrderBook, i: int) -> Cell[int]:
        """
        获取第i个卖量
        """
        price_cell = self._ask_price(order_book, i)
        return price_cell.map(lambda price: order_book.sell[price])
    
    def _bid_price(self, order_book: OrderBook, i: int) -> Cell[int]:
        """
        获取第i个买价
        """
        buy_keys = sorted(order_book.buy.keys(), reverse=True)
        if len(buy_keys) >= i:
            return Cell.from_option(buy_keys[i-1])
        else:
            return Cell.from_option(None)
    
    def _bid_volume(self, order_book: OrderBook, i: int) -> Cell[int]:
        """
        获取第i个买量
        """
        price_cell = self._bid_price(order_book, i)
        return price_cell.map(lambda price: order_book.buy[price])
    
    def _check_level(self, i: int, f: Callable[[], T]) -> T:
        """
        检查级别是否有效
        """
        self.config.check_level(i)
        return f()
    
    def _create_attribute(self, f: Callable[[OrderBook], Cell[T]]) -> BasicAttribute[T]:
        """
        创建属性
        """
        return BasicAttributeFactory.from_func(f)
    
    def ask_price(self, i: int) -> BasicAttribute[int]:
        """
        获取第i个卖价属性
        """
        def check():
            return self._create_attribute(lambda ob: self._ask_price(ob, i))
        return self._check_level(i, check)
    
    def bid_price(self, i: int) -> BasicAttribute[int]:
        """
        获取第i个买价属性
        """
        def check():
            return self._create_attribute(lambda ob: self._bid_price(ob, i))
        return self._check_level(i, check)
    
    def ask_volume(self, i: int) -> BasicAttribute[int]:
        """
        获取第i个卖量属性
        """
        def check():
            return self._create_attribute(lambda ob: self._ask_volume(ob, i))
        return self._check_level(i, check)
    
    def bid_volume(self, i: int) -> BasicAttribute[int]:
        """
        获取第i个买量属性
        """
        def check():
            return self._create_attribute(lambda ob: self._bid_volume(ob, i))
        return self._check_level(i, check)
    
    @property
    def mean_price(self) -> BasicAttribute[float]:
        """
        获取平均价格属性
        """
        ask1 = self.ask_price(1)
        bid1 = self.bid_price(1)
        
        def mean_price_func(order_book: OrderBook) -> Cell[float]:
            ask_cell = ask1(order_book)
            bid_cell = bid1(order_book)
            return ask_cell.zip_map(bid_cell, lambda ask, bid: (float(ask) + float(bid)) / 2)
        
        return BasicAttributeFactory.from_func(mean_price_func) 