{"cells": [{"cell_type": "code", "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-03-17T05:47:21.009540Z", "start_time": "2025-03-17T05:47:20.158217Z"}}, "source": ["%pylab inline\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["%pylab is deprecated, use %matplotlib inline and import the required libraries.\n", "Populating the interactive namespace from numpy and matplotlib\n"]}], "execution_count": 1}, {"cell_type": "code", "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-03-17T05:47:21.025552Z", "start_time": "2025-03-17T05:47:21.018540Z"}}, "source": ["pwd"], "outputs": [{"data": {"text/plain": ["'D:\\\\code\\\\pythonworld\\\\ml\\\\SGX-Full-OrderBook-Tick-Data-Trading-Strategy-master\\\\Data_Transformation\\\\Train_Test_Builder'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T05:47:21.272183Z", "start_time": "2025-03-17T05:47:21.257576Z"}}, "cell_type": "code", "source": ["def order_book(month,day):\n", "    data = []\n", "    datapath = 'order_book_3_2014'\\\n", "                + '_' + str(month) + '_' + str(day) + '.csv'\n", "    order_book = pd.read_csv(datapath,sep=',')\n", "    bid_price_1 = np.array(list(map(float, order_book['Bid'][1::4]))) / 100.0\n", "    bid_price_2 = np.array(list(map(float, order_book['Bid'][2::4]))) / 100.0\n", "    bid_price_3 = np.array(list(map(float, order_book['Bid'][3::4]))) / 100.0\n", "    ask_price_1 = np.array(list(map(float, order_book['Ask'][1::4]))) / 100.0\n", "    ask_price_2 = np.array(list(map(float, order_book['Ask'][2::4]))) / 100.0\n", "    ask_price_3 = np.array(list(map(float, order_book['Ask'][3::4]))) / 100.0\n", "    timestamp = np.array(order_book['Bid_Quantity'][0::4])\n", "\n", "    bid_quantity_1 = np.array(list(map(float, order_book['Bid_Quantity'][1::4])))\n", "    bid_quantity_2 = np.array(list(map(float, order_book['Bid_Quantity'][2::4])))\n", "    bid_quantity_3 = np.array(list(map(float, order_book['Bid_Quantity'][3::4])))\n", "    ask_quantity_1 = np.array(list(map(float, order_book['Ask_Quantity'][1::4])))\n", "    ask_quantity_2 = np.array(list(map(float, order_book['Ask_Quantity'][2::4])))\n", "    ask_quantity_3 = np.array(list(map(float, order_book['Ask_Quantity'][3::4])))\n", "\n", "    \n", "    bid_quantity_1[np.isnan(bid_quantity_1)] = 0\n", "    bid_quantity_2[np.isnan(bid_quantity_2)] = 0\n", "    bid_quantity_3[np.isnan(bid_quantity_3)] = 0\n", "    ask_quantity_1[np.isnan(ask_quantity_1)] = 0\n", "    ask_quantity_2[np.isnan(ask_quantity_2)] = 0\n", "    ask_quantity_3[np.isnan(ask_quantity_3)] = 0\n", "    \n", "    return timestamp,order_book,bid_price_1,bid_price_2,bid_price_3,bid_quantity_1,\\\n", "            bid_quantity_2,bid_quantity_3,ask_price_1,ask_price_2,ask_price_3,ask_quantity_1,\\\n", "            ask_quantity_2,ask_quantity_3"], "outputs": [], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T05:47:21.303320Z", "start_time": "2025-03-17T05:47:21.288805Z"}}, "cell_type": "code", "source": ["def time_transform(timestamp_time):\n", "    time_second_basic = []\n", "    time_second = []\n", "    for i in range(0,len(timestamp_time),1):\n", "        second = float(timestamp_time[i][11])*36000 + float(timestamp_time[i][12])*3600+\\\n", "                    float(timestamp_time[i][14])*600 + float(timestamp_time[i][15])*60+\\\n", "                    float(timestamp_time[i][17])*10 + float(timestamp_time[i][18])  \n", "        time_second_basic.append(second - 32400.0)\n", "        time_second.append(second)\n", "    return np.array(time_second),np.array(time_second_basic)"], "outputs": [], "execution_count": 4}, {"cell_type": "code", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-17T05:47:21.334313Z", "start_time": "2025-03-17T05:47:21.321314Z"}}, "source": ["def weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3):\n", "    \n", "    Weight_Ask = (w1 * ask_quantity_1 + w2 * ask_quantity_2 + w3 * ask_quantity_3)\n", "    Weight_Bid = (w1 * bid_quantity_1 + w2 * bid_quantity_2 + w3 * bid_quantity_3)\n", "    W_AB = Weight_Ask/Weight_Bid\n", "    W_A_B = (Weight_Ask - Weight_Bid)/(Weight_Ask + Weight_Bid)\n", "    \n", "    return W_AB, W_A_B"], "outputs": [], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T05:47:21.365798Z", "start_time": "2025-03-17T05:47:21.350682Z"}}, "cell_type": "code", "source": ["def Feature_DataFrame_UP(traded_time,time_second_basic,bid_price_1,ask_price_1,rise_ratio_ask_1,\\\n", "                         rise_ratio_ask_2,rise_ratio_ask_3,rise_ratio_ask_4,rise_ratio_ask_5,\\\n", "                         rise_ratio_ask_6,rise_ratio_ask_7,rise_ratio_ask_8,rise_ratio_ask_9,\\\n", "                         rise_ratio_ask_10,rise_ratio_ask_11,rise_ratio_ask_12,rise_ratio_ask_13,\\\n", "                         rise_ratio_ask_14,rise_ratio_ask_15,rise_ratio_ask_16,rise_ratio_ask_17,\\\n", "                         rise_ratio_ask_18,rise_ratio_ask_19,rise_ratio_ask_20,rise_ratio_ask_21,\\\n", "                         rise_ratio_ask_22,rise_ratio_ask_23,rise_ratio_ask_24,rise_ratio_ask_25,\\\n", "                         rise_ratio_ask_26,rise_ratio_ask_27,rise_ratio_ask_28,rise_ratio_ask_29,\\\n", "                         rise_ratio_ask_30,W_AB_100, W_A_B_100, W_AB_010, W_A_B_010, W_AB_001,\\\n", "                         W_A_B_001, W_<PERSON>_910, W_A_B_910, W_AB_820, W_A_B_820, W_AB_730 , W_A_B_730,\\\n", "                         W_AB_640, W_A_B_640, W_AB_550, W_A_B_550,W_AB_721, W_A_B_721, W_AB_532,\\\n", "                         W_A_B_532, W_<PERSON>_111, W_<PERSON>_<PERSON>_111, W_AB_190, W_A_B_190, W_<PERSON>_280 , W_A_B_280,\\\n", "                         W_AB_370, W_A_B_370, W_AB_460, W_A_B_460, W_AB_127, W_A_B_127, W_AB_235, W_A_B_235):\n", "    # 09:00 ~ 11:30\n", "    time1 = 0\n", "    time2 = 9000\n", "    print(len(W_AB_910))\n", "    traded,index_,rise_ratio_second_1,rise_ratio_second_2,rise_ratio_second_3,\\\n", "    rise_ratio_second_4,rise_ratio_second_5,rise_ratio_second_6,rise_ratio_second_7,\\\n", "    rise_ratio_second_8,rise_ratio_second_9,rise_ratio_second_10,rise_ratio_second_11,\\\n", "    rise_ratio_second_12,rise_ratio_second_13,rise_ratio_second_14,rise_ratio_second_15,\\\n", "    rise_ratio_second_16,rise_ratio_second_17,rise_ratio_second_18,rise_ratio_second_19,\\\n", "    rise_ratio_second_20,rise_ratio_second_21,rise_ratio_second_22,rise_ratio_second_23,\\\n", "    rise_ratio_second_24,rise_ratio_second_25,rise_ratio_second_26,rise_ratio_second_27,\\\n", "    rise_ratio_second_28,rise_ratio_second_29,rise_ratio_second_30,w_divid_100,w_diff_100,\\\n", "    w_divid_010,w_diff_010,w_divid_001,w_diff_001,w_divid_910,w_diff_910,w_divid_820,w_diff_820,\\\n", "    w_divid_730,w_diff_730,w_divid_640,w_diff_640,w_divid_550,w_diff_550,w_divid_721,w_diff_721,\\\n", "    w_divid_532,w_diff_532,w_divid_111,w_diff_111,w_divid_190,w_diff_190,w_divid_280,w_diff_280,\\\n", "    w_divid_370,w_diff_370,w_divid_460,w_diff_460,w_divid_127,w_diff_127,w_divid_235,w_diff_235=\\\n", "        traded_label_one_second(time1,time2,time_second_basic,bid_price_1,ask_price_1,traded_time,\\\n", "                                rise_ratio_ask_1,rise_ratio_ask_2,rise_ratio_ask_3,rise_ratio_ask_4,\\\n", "                                rise_ratio_ask_5,rise_ratio_ask_6,rise_ratio_ask_7,rise_ratio_ask_8,\\\n", "                                rise_ratio_ask_9,rise_ratio_ask_10,rise_ratio_ask_11,rise_ratio_ask_12,\\\n", "                                rise_ratio_ask_13,rise_ratio_ask_14,rise_ratio_ask_15,rise_ratio_ask_16,\\\n", "                                rise_ratio_ask_17,rise_ratio_ask_18,rise_ratio_ask_19,rise_ratio_ask_20,\\\n", "                                rise_ratio_ask_21,rise_ratio_ask_22,rise_ratio_ask_23,rise_ratio_ask_24,\\\n", "                                rise_ratio_ask_25,rise_ratio_ask_26,rise_ratio_ask_27,rise_ratio_ask_28,\\\n", "                                rise_ratio_ask_29,rise_ratio_ask_30,W_AB_100, W_A_B_100, W_AB_010, W_A_B_010,\\\n", "                                W_AB_001,W_A_B_001, W_<PERSON>_910, W_A_B_910, W_AB_820, W_<PERSON>_B_820, W_AB_730,\\\n", "                                W_A_B_730,W_<PERSON>_640, W_A_B_640, W_AB_550, W_A_B_550,W_AB_721, W_A_B_721,\\\n", "                                W_<PERSON>_532,W_<PERSON>_<PERSON>_532, W_<PERSON>_111, W_<PERSON>_<PERSON>_111, W_AB_190, W_A_B_190, W_<PERSON>_280,\\\n", "                                W_A_B_280,W_AB_370, W_A_B_370, W_AB_460, W_A_B_460, W_AB_127, W_A_B_127,\\\n", "                                W_AB_235, W_A_B_235)\n", "    \n", "    data = np.array([traded,rise_ratio_second_1,rise_ratio_second_2,rise_ratio_second_3,\\\n", "                    rise_ratio_second_4,rise_ratio_second_5,rise_ratio_second_6,rise_ratio_second_7,\\\n", "                    rise_ratio_second_8,rise_ratio_second_9,rise_ratio_second_10,rise_ratio_second_11,\\\n", "                    rise_ratio_second_12,rise_ratio_second_13,rise_ratio_second_14,rise_ratio_second_15,\\\n", "                    rise_ratio_second_16,rise_ratio_second_17,rise_ratio_second_18,rise_ratio_second_19,\\\n", "                    rise_ratio_second_20,rise_ratio_second_21,rise_ratio_second_22,rise_ratio_second_23,\\\n", "                    rise_ratio_second_24,rise_ratio_second_25,rise_ratio_second_26,rise_ratio_second_27,\\\n", "                    rise_ratio_second_28,rise_ratio_second_29,rise_ratio_second_30,w_divid_100,w_diff_100,\\\n", "                    w_divid_010,w_diff_010,w_divid_001,w_diff_001,w_divid_910,w_diff_910,w_divid_820,w_diff_820,\\\n", "                    w_divid_730,w_diff_730,w_divid_640,w_diff_640,w_divid_550,w_diff_550,w_divid_721,w_diff_721,\\\n", "                    w_divid_532,w_diff_532,w_divid_111,w_diff_111,w_divid_190,w_diff_190,w_divid_280,w_diff_280,\\\n", "                    w_divid_370,w_diff_370,w_divid_460,w_diff_460,w_divid_127,w_diff_127,w_divid_235,w_diff_235]).T\n", "\n", "    return pd.DataFrame(data)#,traded_1 #, columns = ['label', 'rise', 'depth_divid', 'depth_diff'])\n", "\n", "\n", "def Feature_DataFrame_DOWN(traded_time,time_second_basic,bid_price_1,ask_price_1,rise_ratio_ask_1,\\\n", "                         rise_ratio_ask_2,rise_ratio_ask_3,rise_ratio_ask_4,rise_ratio_ask_5,\\\n", "                         rise_ratio_ask_6,rise_ratio_ask_7,rise_ratio_ask_8,rise_ratio_ask_9,\\\n", "                         rise_ratio_ask_10,rise_ratio_ask_11,rise_ratio_ask_12,rise_ratio_ask_13,\\\n", "                         rise_ratio_ask_14,rise_ratio_ask_15,rise_ratio_ask_16,rise_ratio_ask_17,\\\n", "                         rise_ratio_ask_18,rise_ratio_ask_19,rise_ratio_ask_20,rise_ratio_ask_21,\\\n", "                         rise_ratio_ask_22,rise_ratio_ask_23,rise_ratio_ask_24,rise_ratio_ask_25,\\\n", "                         rise_ratio_ask_26,rise_ratio_ask_27,rise_ratio_ask_28,rise_ratio_ask_29,\\\n", "                         rise_ratio_ask_30,W_AB_100, W_A_B_100, W_AB_010, W_A_B_010, W_AB_001,\\\n", "                         W_A_B_001, W_<PERSON>_910, W_A_B_910, W_AB_820, W_A_B_820, W_AB_730 , W_A_B_730,\\\n", "                         W_AB_640, W_A_B_640, W_AB_550, W_A_B_550,W_AB_721, W_A_B_721, W_AB_532,\\\n", "                         W_A_B_532, W_<PERSON>_111, W_<PERSON>_<PERSON>_111, W_AB_190, W_A_B_190, W_<PERSON>_280 , W_A_B_280,\\\n", "                         W_AB_370, W_A_B_370, W_AB_460, W_A_B_460, W_AB_127, W_A_B_127, W_AB_235, W_A_B_235):\n", "    # 13:00 ~ 16:00\n", "    time1 = 14400\n", "    time2 = 25200\n", "    traded,index_,rise_ratio_second_1,rise_ratio_second_2,rise_ratio_second_3,\\\n", "    rise_ratio_second_4,rise_ratio_second_5,rise_ratio_second_6,rise_ratio_second_7,\\\n", "    rise_ratio_second_8,rise_ratio_second_9,rise_ratio_second_10,rise_ratio_second_11,\\\n", "    rise_ratio_second_12,rise_ratio_second_13,rise_ratio_second_14,rise_ratio_second_15,\\\n", "    rise_ratio_second_16,rise_ratio_second_17,rise_ratio_second_18,rise_ratio_second_19,\\\n", "    rise_ratio_second_20,rise_ratio_second_21,rise_ratio_second_22,rise_ratio_second_23,\\\n", "    rise_ratio_second_24,rise_ratio_second_25,rise_ratio_second_26,rise_ratio_second_27,\\\n", "    rise_ratio_second_28,rise_ratio_second_29,rise_ratio_second_30,w_divid_100,w_diff_100,\\\n", "    w_divid_010,w_diff_010,w_divid_001,w_diff_001,w_divid_910,w_diff_910,w_divid_820,w_diff_820,\\\n", "    w_divid_730,w_diff_730,w_divid_640,w_diff_640,w_divid_550,w_diff_550,w_divid_721,w_diff_721,\\\n", "    w_divid_532,w_diff_532,w_divid_111,w_diff_111,w_divid_190,w_diff_190,w_divid_280,w_diff_280,\\\n", "    w_divid_370,w_diff_370,w_divid_460,w_diff_460,w_divid_127,w_diff_127,w_divid_235,w_diff_235 =\\\n", "        traded_label_one_second(time1,time2,time_second_basic,bid_price_1,ask_price_1,traded_time,\\\n", "                                rise_ratio_ask_1,rise_ratio_ask_2,rise_ratio_ask_3,rise_ratio_ask_4,\\\n", "                                rise_ratio_ask_5,rise_ratio_ask_6,rise_ratio_ask_7,rise_ratio_ask_8,\\\n", "                                rise_ratio_ask_9,rise_ratio_ask_10,rise_ratio_ask_11,rise_ratio_ask_12,\\\n", "                                rise_ratio_ask_13,rise_ratio_ask_14,rise_ratio_ask_15,rise_ratio_ask_16,\\\n", "                                rise_ratio_ask_17,rise_ratio_ask_18,rise_ratio_ask_19,rise_ratio_ask_20,\\\n", "                                rise_ratio_ask_21,rise_ratio_ask_22,rise_ratio_ask_23,rise_ratio_ask_24,\\\n", "                                rise_ratio_ask_25,rise_ratio_ask_26,rise_ratio_ask_27,rise_ratio_ask_28,\\\n", "                                rise_ratio_ask_29,rise_ratio_ask_30,W_AB_100, W_A_B_100, W_AB_010, W_A_B_010,\\\n", "                                W_AB_001,W_A_B_001, W_<PERSON>_910, W_A_B_910, W_AB_820, W_<PERSON>_B_820, W_AB_730,\\\n", "                                W_A_B_730,W_<PERSON>_640, W_A_B_640, W_AB_550, W_A_B_550,W_AB_721, W_A_B_721,\\\n", "                                W_<PERSON>_532,W_<PERSON>_<PERSON>_532, W_<PERSON>_111, W_<PERSON>_<PERSON>_111, W_AB_190, W_A_B_190, W_<PERSON>_280,\\\n", "                                W_A_B_280,W_AB_370, W_A_B_370, W_AB_460, W_A_B_460, W_AB_127, W_A_B_127,\\\n", "                                W_AB_235, W_A_B_235)\n", "\n", "    data = np.array([traded,rise_ratio_second_1,rise_ratio_second_2,rise_ratio_second_3,\\\n", "            rise_ratio_second_4,rise_ratio_second_5,rise_ratio_second_6,rise_ratio_second_7,\\\n", "            rise_ratio_second_8,rise_ratio_second_9,rise_ratio_second_10,rise_ratio_second_11,\\\n", "            rise_ratio_second_12,rise_ratio_second_13,rise_ratio_second_14,rise_ratio_second_15,\\\n", "            rise_ratio_second_16,rise_ratio_second_17,rise_ratio_second_18,rise_ratio_second_19,\\\n", "            rise_ratio_second_20,rise_ratio_second_21,rise_ratio_second_22,rise_ratio_second_23,\\\n", "            rise_ratio_second_24,rise_ratio_second_25,rise_ratio_second_26,rise_ratio_second_27,\\\n", "            rise_ratio_second_28,rise_ratio_second_29,rise_ratio_second_30,w_divid_100,w_diff_100,\\\n", "            w_divid_010,w_diff_010,w_divid_001,w_diff_001,w_divid_910,w_diff_910,w_divid_820,w_diff_820,\\\n", "            w_divid_730,w_diff_730,w_divid_640,w_diff_640,w_divid_550,w_diff_550,w_divid_721,w_diff_721,\\\n", "            w_divid_532,w_diff_532,w_divid_111,w_diff_111,w_divid_190,w_diff_190,w_divid_280,w_diff_280,\\\n", "            w_divid_370,w_diff_370,w_divid_460,w_diff_460,w_divid_127,w_diff_127,w_divid_235,w_diff_235]).T\n", "        \n", "    return pd.DataFrame(data)#,traded_2 #, columns = ['label', 'rise', 'depth_divid', 'depth_diff'])\n", "\n"], "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T05:47:21.397799Z", "start_time": "2025-03-17T05:47:21.382799Z"}}, "cell_type": "code", "source": ["def rise_ask(Ask1,timestamp_time_second,before_time):\n", "    Ask1[Ask1 == 0] = mean(Ask1)\n", "    rise_ratio = []\n", "    index = np.where(timestamp_time_second >= before_time)[0][0]\n", "    #open first before_time mins\n", "    for i in range(0,index,1):\n", "        rise_ratio_ = round((Ask1[i] - Ask1[0])*(1.0)/Ask1[0]*100,5)\n", "        rise_ratio.append(rise_ratio_)\n", "    for i in range(index,len(Ask1),1):\n", "        #print np.where(timestamp_time_second[:i] >= timestamp_time_second[i] - before_time)\n", "        #print timestamp_time_second[i],timestamp_time_second[i] - before_time\n", "        index_start = np.where(timestamp_time_second[:i] >= timestamp_time_second[i] - before_time)[0][0]\n", "        rise_ratio_ = round((Ask1[i] - Ask1[index_start])*(1.0)/Ask1[index_start]*100,5)\n", "        rise_ratio.append(rise_ratio_)\n", "    return np.array(rise_ratio)"], "outputs": [], "execution_count": 7}, {"cell_type": "code", "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-03-17T05:47:21.444389Z", "start_time": "2025-03-17T05:47:21.415799Z"}}, "source": ["def traded_label_one_second(time1,time2,time_second_basic,bid_price_1,ask_price_1,traded_time,\\\n", "                            rise_ratio_ask_1,rise_ratio_ask_2,rise_ratio_ask_3,rise_ratio_ask_4,\\\n", "                            rise_ratio_ask_5,rise_ratio_ask_6,rise_ratio_ask_7,rise_ratio_ask_8,\\\n", "                            rise_ratio_ask_9,rise_ratio_ask_10,rise_ratio_ask_11,rise_ratio_ask_12,\\\n", "                            rise_ratio_ask_13,rise_ratio_ask_14,rise_ratio_ask_15,rise_ratio_ask_16,\\\n", "                            rise_ratio_ask_17,rise_ratio_ask_18,rise_ratio_ask_19,rise_ratio_ask_20,\\\n", "                            rise_ratio_ask_21,rise_ratio_ask_22,rise_ratio_ask_23,rise_ratio_ask_24,\\\n", "                            rise_ratio_ask_25,rise_ratio_ask_26,rise_ratio_ask_27,rise_ratio_ask_28,\\\n", "                            rise_ratio_ask_29,rise_ratio_ask_30,W_AB_100, W_A_B_100, W_AB_010, W_A_B_010,\\\n", "                            W_AB_001,W_A_B_001, W_<PERSON>_910, W_A_B_910, W_AB_820, W_<PERSON>_B_820, W_AB_730,\\\n", "                            W_A_B_730,W_<PERSON>_640, W_A_B_640, W_AB_550, W_A_B_550,W_AB_721, W_A_B_721,\\\n", "                            W_<PERSON>_532,W_<PERSON>_<PERSON>_532, W_<PERSON>_111, W_<PERSON>_<PERSON>_111, W_AB_190, W_A_B_190, W_<PERSON>_280,\\\n", "                            W_A_B_280,W_AB_370, W_A_B_370, W_AB_460, W_A_B_460, W_AB_127, W_A_B_127,\\\n", "                            W_AB_235, W_A_B_235):\n", "    global index\n", "    \n", "    traded = []\n", "    index_ = []\n", "    \n", "    rise_ratio_second_1 = []\n", "    rise_ratio_second_2 = []   \n", "    rise_ratio_second_3 = []\n", "    rise_ratio_second_4 = []\n", "    rise_ratio_second_5 = []\n", "    rise_ratio_second_6 = []\n", "    rise_ratio_second_7 = []\n", "    rise_ratio_second_8 = []\n", "    rise_ratio_second_9 = []\n", "    rise_ratio_second_10 = []\n", "    rise_ratio_second_11 = []\n", "    rise_ratio_second_12 = []\n", "    rise_ratio_second_13 = []\n", "    rise_ratio_second_14 = []\n", "    rise_ratio_second_15 = []\n", "    rise_ratio_second_16 = []\n", "    rise_ratio_second_17 = []\n", "    rise_ratio_second_18 = []\n", "    rise_ratio_second_19 = []\n", "    rise_ratio_second_20 = []\n", "    rise_ratio_second_21 = []\n", "    rise_ratio_second_22 = []\n", "    rise_ratio_second_23 = []\n", "    rise_ratio_second_24 = []\n", "    rise_ratio_second_25 = []\n", "    rise_ratio_second_26 = []\n", "    rise_ratio_second_27 = []\n", "    rise_ratio_second_28 = []\n", "    rise_ratio_second_29 = []\n", "    rise_ratio_second_30 = []\n", "\n", "    w_divid_100 = []\n", "    w_diff_100 = []\n", "    w_divid_010 = []\n", "    w_diff_010 = []\n", "    w_divid_001 = []\n", "    w_diff_001 = []\n", "    w_divid_910 = []\n", "    w_diff_910 = []\n", "    w_divid_820 = []\n", "    w_diff_820 = []\n", "    w_divid_730 = []\n", "    w_diff_730 = []\n", "    w_divid_640 = []\n", "    w_diff_640 = []\n", "    w_divid_550 = []\n", "    w_diff_550 = []\n", "    w_divid_721 = []\n", "    w_diff_721 = []\n", "    w_divid_532 = []\n", "    w_diff_532 = []\n", "    w_divid_111 = []\n", "    w_diff_111 = []\n", "    w_divid_190 = []\n", "    w_diff_190 = []\n", "    w_divid_280 = []\n", "    w_diff_280 = []\n", "    w_divid_370 = []\n", "    w_diff_370 = []\n", "    w_divid_460 = []\n", "    w_diff_460 = []\n", "    w_divid_127 = []\n", "    w_diff_127 = []\n", "    w_divid_235 = []\n", "    w_diff_235 = []\n", "    \n", "    if time1 == 0:\n", "        index_one = np.where(time_second_basic <= 0)[0][-1]\n", "    elif time1 == 14400:\n", "        index_one = np.where(time_second_basic <= 14400)[0][-1]\n", "        \n", "    for i in range(time1, time2, 1):\n", "        if i == 0 or i == 14400:\n", "            index_array = np.where(time_second_basic <= i)[-1]\n", "        else:\n", "            index_array = np.where((time_second_basic < i+1) & (time_second_basic >= i))[-1]\n", "        \n", "        if len(index_array) > 0:\n", "            index = index_array[-1]\n", "            if i == time1:\n", "                index_.append(index)\n", "            if i == time2 - 1:\n", "                index_.append(index)\n", "            if i < 25200 - traded_time:\n", "                index_min = np.where(time_second_basic <= i + traded_time)[0][-1]\n", "                traded_min = ask_price_1[index:index_min]\n", "                if bid_price_1[index] > min(traded_min):\n", "                    traded.append(1)\n", "                else:\n", "                    traded.append(0)\n", "            elif i >= 25200 - traded_time:\n", "                if bid_price_1[index] > ask_price_1[-1]:\n", "                    traded.append(1)\n", "                else:\n", "                    traded.append(0)\n", "                    \n", "            rise_ratio_second_1.append(rise_ratio_ask_1[(index - index_one)])\n", "            rise_ratio_second_2.append(rise_ratio_ask_2[(index - index_one)])\n", "            rise_ratio_second_3.append(rise_ratio_ask_3[(index - index_one)])\n", "            rise_ratio_second_4.append(rise_ratio_ask_4[(index - index_one)])\n", "            rise_ratio_second_5.append(rise_ratio_ask_5[(index - index_one)])\n", "            rise_ratio_second_6.append(rise_ratio_ask_6[(index - index_one)])\n", "            rise_ratio_second_7.append(rise_ratio_ask_7[(index - index_one)])\n", "            rise_ratio_second_8.append(rise_ratio_ask_8[(index - index_one)])\n", "            rise_ratio_second_9.append(rise_ratio_ask_9[(index - index_one)])\n", "            rise_ratio_second_10.append(rise_ratio_ask_10[(index - index_one)])\n", "            rise_ratio_second_11.append(rise_ratio_ask_11[(index - index_one)])\n", "            rise_ratio_second_12.append(rise_ratio_ask_12[(index - index_one)])\n", "            rise_ratio_second_13.append(rise_ratio_ask_13[(index - index_one)])\n", "            rise_ratio_second_14.append(rise_ratio_ask_14[(index - index_one)])\n", "            rise_ratio_second_15.append(rise_ratio_ask_15[(index - index_one)])\n", "            rise_ratio_second_16.append(rise_ratio_ask_16[(index - index_one)])\n", "            rise_ratio_second_17.append(rise_ratio_ask_17[(index - index_one)])\n", "            rise_ratio_second_18.append(rise_ratio_ask_18[(index - index_one)])\n", "            rise_ratio_second_19.append(rise_ratio_ask_19[(index - index_one)])\n", "            rise_ratio_second_20.append(rise_ratio_ask_20[(index - index_one)])\n", "            rise_ratio_second_21.append(rise_ratio_ask_21[(index - index_one)])\n", "            rise_ratio_second_22.append(rise_ratio_ask_22[(index - index_one)])\n", "            rise_ratio_second_23.append(rise_ratio_ask_23[(index - index_one)])\n", "            rise_ratio_second_24.append(rise_ratio_ask_24[(index - index_one)])\n", "            rise_ratio_second_25.append(rise_ratio_ask_25[(index - index_one)])\n", "            rise_ratio_second_26.append(rise_ratio_ask_26[(index - index_one)])\n", "            rise_ratio_second_27.append(rise_ratio_ask_27[(index - index_one)])\n", "            rise_ratio_second_28.append(rise_ratio_ask_28[(index - index_one)])\n", "            rise_ratio_second_29.append(rise_ratio_ask_29[(index - index_one)])\n", "            rise_ratio_second_30.append(rise_ratio_ask_30[(index - index_one)])\n", "            \n", "            w_divid_100.append(W_AB_100[index_one + (index - index_one)])\n", "            w_diff_100.append(W_A_B_100[index_one + (index - index_one)])\n", "            w_divid_010.append(W_AB_010[index_one + (index - index_one)])\n", "            w_diff_010.append(W_A_B_010[index_one + (index - index_one)])\n", "            w_divid_001.append(W_AB_001[index_one + (index - index_one)])\n", "            w_diff_001.append(W_A_B_001[index_one + (index - index_one)])\n", "            w_divid_910.append(W_AB_910[index_one + (index - index_one)])\n", "            w_diff_910.append(W_A_B_910[index_one + (index - index_one)])\n", "            w_divid_820.append(W_AB_820[index_one + (index - index_one)])\n", "            w_diff_820.append(W_A_B_820[index_one + (index - index_one)])\n", "            w_divid_730.append(W_AB_730[index_one + (index - index_one)])\n", "            w_diff_730.append(W_A_B_730[index_one + (index - index_one)])\n", "            w_divid_640.append(W_AB_640[index_one + (index - index_one)])\n", "            w_diff_640.append(W_A_B_640[index_one + (index - index_one)])\n", "            w_divid_550.append(W_AB_550[index_one + (index - index_one)])\n", "            w_diff_550.append(W_A_B_550[index_one + (index - index_one)])\n", "            w_divid_721.append(W_AB_721[index_one + (index - index_one)])\n", "            w_diff_721.append(W_A_B_721[index_one + (index - index_one)])\n", "            w_divid_532.append(W_AB_532[index_one + (index - index_one)])\n", "            w_diff_532.append(W_A_B_532[index_one + (index - index_one)])\n", "            w_divid_111.append(W_AB_111[index_one + (index - index_one)])\n", "            w_diff_111.append(W_A_B_111[index_one + (index - index_one)])\n", "            w_divid_190.append(W_AB_190[index_one + (index - index_one)])\n", "            w_diff_190.append(W_A_B_190[index_one + (index - index_one)])\n", "            w_divid_280.append(W_AB_280[index_one + (index - index_one)])\n", "            w_diff_280.append(W_A_B_280[index_one + (index - index_one)])\n", "            w_divid_370.append(W_AB_370[index_one + (index - index_one)])\n", "            w_diff_370.append(W_A_B_370[index_one + (index - index_one)])\n", "            w_divid_460.append(W_AB_460[index_one + (index - index_one)])\n", "            w_diff_460.append(W_A_B_460[index_one + (index - index_one)])\n", "            w_divid_127.append(W_AB_127[index_one + (index - index_one)])\n", "            w_diff_127.append(W_A_B_127[index_one + (index - index_one)])\n", "            w_divid_235.append(W_AB_235[index_one + (index - index_one)])\n", "            w_diff_235.append(W_A_B_235[index_one + (index - index_one)])\n", "        \n", "        elif len(index_array) == 0:\n", "            if i < 25200 - traded_time:\n", "                index_min = np.where(time_second_basic <= i + traded_time)[0][-1]\n", "                traded_min = ask_price_1[index:index_min]\n", "                if bid_price_1[index] > min(traded_min):\n", "                    traded.append(1)\n", "                else:\n", "                    traded.append(0)\n", "            elif i >= 25200 - traded_time:\n", "                if bid_price_1[index] > ask_price_1[-1]:\n", "                    traded.append(1)\n", "                else:\n", "                    traded.append(0)\n", "            rise_ratio_second_1.append(rise_ratio_second_1[-1])\n", "            rise_ratio_second_2.append(rise_ratio_second_2[-1])\n", "            rise_ratio_second_3.append(rise_ratio_second_3[-1])\n", "            rise_ratio_second_4.append(rise_ratio_second_4[-1])\n", "            rise_ratio_second_5.append(rise_ratio_second_5[-1])\n", "            rise_ratio_second_6.append(rise_ratio_second_6[-1])\n", "            rise_ratio_second_7.append(rise_ratio_second_7[-1])\n", "            rise_ratio_second_8.append(rise_ratio_second_8[-1])\n", "            rise_ratio_second_9.append(rise_ratio_second_9[-1])\n", "            rise_ratio_second_10.append(rise_ratio_second_10[-1])\n", "            rise_ratio_second_11.append(rise_ratio_second_11[-1])\n", "            rise_ratio_second_12.append(rise_ratio_second_12[-1])\n", "            rise_ratio_second_13.append(rise_ratio_second_13[-1])\n", "            rise_ratio_second_14.append(rise_ratio_second_14[-1])\n", "            rise_ratio_second_15.append(rise_ratio_second_15[-1])\n", "            rise_ratio_second_16.append(rise_ratio_second_16[-1])\n", "            rise_ratio_second_17.append(rise_ratio_second_17[-1])\n", "            rise_ratio_second_18.append(rise_ratio_second_18[-1])\n", "            rise_ratio_second_19.append(rise_ratio_second_19[-1])\n", "            rise_ratio_second_20.append(rise_ratio_second_20[-1])\n", "            rise_ratio_second_21.append(rise_ratio_second_21[-1])\n", "            rise_ratio_second_22.append(rise_ratio_second_22[-1])\n", "            rise_ratio_second_23.append(rise_ratio_second_23[-1])\n", "            rise_ratio_second_24.append(rise_ratio_second_24[-1])\n", "            rise_ratio_second_25.append(rise_ratio_second_25[-1])\n", "            rise_ratio_second_26.append(rise_ratio_second_26[-1])\n", "            rise_ratio_second_27.append(rise_ratio_second_27[-1])\n", "            rise_ratio_second_28.append(rise_ratio_second_28[-1])\n", "            rise_ratio_second_29.append(rise_ratio_second_29[-1])\n", "            rise_ratio_second_30.append(rise_ratio_second_30[-1])\n", "            \n", "            w_divid_100.append(w_divid_100[-1])\n", "            w_diff_100.append(w_diff_100[-1])\n", "            w_divid_010.append(w_divid_010[-1])\n", "            w_diff_010.append(w_diff_010[-1])\n", "            w_divid_001.append(w_divid_001[-1])\n", "            w_diff_001.append(w_diff_001[-1])\n", "            w_divid_910.append(w_divid_910[-1])\n", "            w_diff_910.append(w_diff_910[-1])\n", "            w_divid_820.append(w_divid_820[-1])\n", "            w_diff_820.append(w_diff_820[-1])\n", "            w_divid_730.append(w_divid_730[-1])\n", "            w_diff_730.append(w_diff_730[-1])\n", "            w_divid_640.append(w_divid_640[-1])\n", "            w_diff_640.append(w_diff_640[-1])\n", "            w_divid_550.append(w_divid_550[-1])\n", "            w_diff_550.append(w_diff_550[-1])\n", "            w_divid_721.append(w_divid_721[-1])\n", "            w_diff_721.append(w_diff_721[-1])\n", "            w_divid_532.append(w_divid_532[-1])\n", "            w_diff_532.append(w_diff_532[-1])\n", "            w_divid_111.append(w_divid_111[-1])\n", "            w_diff_111.append(w_diff_111[-1])\n", "            w_divid_190.append(w_divid_190[-1])\n", "            w_diff_190.append(w_diff_190[-1])\n", "            w_divid_280.append(w_divid_280[-1])\n", "            w_diff_280.append(w_diff_280[-1])\n", "            w_divid_370.append(w_divid_370[-1])\n", "            w_diff_370.append(w_diff_370[-1])\n", "            w_divid_460.append(w_divid_460[-1])\n", "            w_diff_460.append(w_diff_460[-1])\n", "            w_divid_127.append(w_divid_127[-1])\n", "            w_diff_127.append(w_diff_127[-1])\n", "            w_divid_235.append(w_divid_235[-1])\n", "            w_diff_235.append(w_diff_235[-1])\n", "            \n", "    return traded,index_,rise_ratio_second_1,rise_ratio_second_2,rise_ratio_second_3,\\\n", "            rise_ratio_second_4,rise_ratio_second_5,rise_ratio_second_6,rise_ratio_second_7,\\\n", "            rise_ratio_second_8,rise_ratio_second_9,rise_ratio_second_10,rise_ratio_second_11,\\\n", "            rise_ratio_second_12,rise_ratio_second_13,rise_ratio_second_14,rise_ratio_second_15,\\\n", "            rise_ratio_second_16,rise_ratio_second_17,rise_ratio_second_18,rise_ratio_second_19,\\\n", "            rise_ratio_second_20,rise_ratio_second_21,rise_ratio_second_22,rise_ratio_second_23,\\\n", "            rise_ratio_second_24,rise_ratio_second_25,rise_ratio_second_26,rise_ratio_second_27,\\\n", "            rise_ratio_second_28,rise_ratio_second_29,rise_ratio_second_30,w_divid_100,w_diff_100,\\\n", "            w_divid_010,w_diff_010,w_divid_001,w_diff_001,w_divid_910,w_diff_910,w_divid_820,w_diff_820,\\\n", "            w_divid_730,w_diff_730,w_divid_640,w_diff_640,w_divid_550,w_diff_550,w_divid_721,w_diff_721,\\\n", "            w_divid_532,w_diff_532,w_divid_111,w_diff_111,w_divid_190,w_diff_190,w_divid_280,w_diff_280,\\\n", "            w_divid_370,w_diff_370,w_divid_460,w_diff_460,w_divid_127,w_diff_127,w_divid_235,w_diff_235"], "outputs": [], "execution_count": 8}, {"cell_type": "code", "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-03-17T05:47:21.475826Z", "start_time": "2025-03-17T05:47:21.461821Z"}}, "source": ["def data(month,day,traded_time):\n", "    \n", "    timestamp,order_book_ ,bid_price_1, bid_price_2, bid_price_3,\\\n", "    bid_quantity_1, bid_quantity_2, bid_quantity_3,\\\n", "    ask_price_1, ask_price_2, ask_price_3,ask_quantity_1,\\\n", "    ask_quantity_2, ask_quantity_3 = order_book(month,day)\n", "    \n", "    time_second,time_second_basic = time_transform(timestamp)\n", "    Ask1 = ask_price_1[np.where(time_second_basic <= 0.0)[0][-1]:]\n", "\n", "    before_time = 60.0 * 6\n", "    rise_ratio_ask_1 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 6 + 30 \n", "    rise_ratio_ask_2 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 7\n", "    rise_ratio_ask_3 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 7 + 30 \n", "    rise_ratio_ask_4 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 8\n", "    rise_ratio_ask_5 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 8 + 30\n", "    rise_ratio_ask_6 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 9\n", "    rise_ratio_ask_7 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 9 + 30\n", "    rise_ratio_ask_8 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 10\n", "    rise_ratio_ask_9 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 10 + 30\n", "    rise_ratio_ask_10 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 11\n", "    rise_ratio_ask_11 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 11 + 30\n", "    rise_ratio_ask_12 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 12\n", "    rise_ratio_ask_13 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 12 + 30\n", "    rise_ratio_ask_14 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 13\n", "    rise_ratio_ask_15 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 13 + 30\n", "    rise_ratio_ask_16 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 14\n", "    rise_ratio_ask_17 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 14 + 30 \n", "    rise_ratio_ask_18 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 15\n", "    rise_ratio_ask_19 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 15 + 30\n", "    rise_ratio_ask_20 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 16\n", "    rise_ratio_ask_21 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 16 + 30\n", "    rise_ratio_ask_22 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 17\n", "    rise_ratio_ask_23 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 17 + 30\n", "    rise_ratio_ask_24 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 18\n", "    rise_ratio_ask_25 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 18 + 30\n", "    rise_ratio_ask_26 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 19\n", "    rise_ratio_ask_27 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 19 + 30\n", "    rise_ratio_ask_28 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 20 \n", "    rise_ratio_ask_29 = rise_ask(Ask1, time_second_basic, before_time)\n", "    before_time = 60.0 * 20 + 30\n", "    rise_ratio_ask_30 = rise_ask(Ask1, time_second_basic, before_time)\n", "    \n", "    #Weight Depth\n", "    w1,w2,w3 = [100.0, 0.0, 0.0]\n", "    W_AB_100 , W_A_B_100 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [0.0, 100.0, 0.0]\n", "    W_AB_010 , W_A_B_010 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [0.0, 0.0, 100.0]\n", "    W_AB_001 , W_A_B_001 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [90.0, 10.0, 0.0]\n", "    W_AB_910 , W_A_B_910 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [80.0, 20.0, 0.0]\n", "    W_AB_820 , W_A_B_820 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [70.0, 30.0, 0.0]\n", "    W_AB_730 , W_A_B_730 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [60.0, 40.0, 0.0]\n", "    W_AB_640 , W_A_B_640 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [50.0, 50.0, 0.0]\n", "    W_AB_550 , W_A_B_550 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [70.0, 20.0, 10.0]\n", "    W_AB_721 , W_A_B_721 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [50.0, 30.0, 20.0]\n", "    W_AB_532 , W_A_B_532 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [1.0, 1.0, 1.0]\n", "    W_AB_111 , W_A_B_111 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [10.0, 90.0, 1.0]\n", "    W_AB_190 , W_A_B_190 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [20.0, 80.0, 0.0]\n", "    W_AB_280 , W_A_B_280 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [30.0, 70.0, 0.0]\n", "    W_AB_370 , W_A_B_370 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [40.0, 60.0, 0.0]\n", "    W_AB_460 , W_A_B_460 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [10.0, 20.0, 70.0]\n", "    W_AB_127 , W_A_B_127 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    w1,w2,w3 = [20.0, 30.0, 50.0]\n", "    W_AB_235 , W_A_B_235 = weight_pecentage(w1,w2,w3,ask_quantity_1,ask_quantity_2,ask_quantity_3,\\\n", "                     bid_quantity_1,bid_quantity_2,bid_quantity_3)\n", "    \n", "    data_2014_UP =\\\n", "    Feature_DataFrame_UP(traded_time,time_second_basic,bid_price_1,ask_price_1,rise_ratio_ask_1,\\\n", "                         rise_ratio_ask_2,rise_ratio_ask_3,rise_ratio_ask_4,rise_ratio_ask_5,\\\n", "                         rise_ratio_ask_6,rise_ratio_ask_7,rise_ratio_ask_8,rise_ratio_ask_9,\\\n", "                         rise_ratio_ask_10,rise_ratio_ask_11,rise_ratio_ask_12,rise_ratio_ask_13,\\\n", "                         rise_ratio_ask_14,rise_ratio_ask_15,rise_ratio_ask_16,rise_ratio_ask_17,\\\n", "                         rise_ratio_ask_18,rise_ratio_ask_19,rise_ratio_ask_20,rise_ratio_ask_21,\\\n", "                         rise_ratio_ask_22,rise_ratio_ask_23,rise_ratio_ask_24,rise_ratio_ask_25,\\\n", "                         rise_ratio_ask_26,rise_ratio_ask_27,rise_ratio_ask_28,rise_ratio_ask_29,\\\n", "                         rise_ratio_ask_30,W_AB_100, W_A_B_100, W_AB_010, W_A_B_010, W_AB_001,\\\n", "                         W_A_B_001, W_<PERSON>_910, W_A_B_910, W_AB_820, W_A_B_820, W_AB_730 , W_A_B_730,\\\n", "                         W_AB_640, W_A_B_640, W_AB_550, W_A_B_550,W_AB_721, W_A_B_721, W_AB_532,\\\n", "                         W_A_B_532, W_<PERSON>_111, W_<PERSON>_<PERSON>_111, W_AB_190, W_A_B_190, W_<PERSON>_280 , W_A_B_280,\\\n", "                         W_AB_370, W_A_B_370, W_AB_460, W_A_B_460, W_<PERSON>_127, W_A_B_127, W_<PERSON>_235, W_A_B_235)\n", "    data_2014_DOWN =\\\n", "    Feature_DataFrame_DOWN(traded_time,time_second_basic,bid_price_1,ask_price_1,rise_ratio_ask_1,\\\n", "                           rise_ratio_ask_2,rise_ratio_ask_3,rise_ratio_ask_4,rise_ratio_ask_5,\\\n", "                           rise_ratio_ask_6,rise_ratio_ask_7,rise_ratio_ask_8,rise_ratio_ask_9,\\\n", "                           rise_ratio_ask_10,rise_ratio_ask_11,rise_ratio_ask_12,rise_ratio_ask_13,\\\n", "                           rise_ratio_ask_14,rise_ratio_ask_15,rise_ratio_ask_16,rise_ratio_ask_17,\\\n", "                           rise_ratio_ask_18,rise_ratio_ask_19,rise_ratio_ask_20,rise_ratio_ask_21,\\\n", "                           rise_ratio_ask_22,rise_ratio_ask_23,rise_ratio_ask_24,rise_ratio_ask_25,\\\n", "                           rise_ratio_ask_26,rise_ratio_ask_27,rise_ratio_ask_28,rise_ratio_ask_29,\\\n", "                           rise_ratio_ask_30,W_AB_100, W_A_B_100, W_AB_010, W_A_B_010, W_AB_001,\\\n", "                           W_A_B_001, W_<PERSON>_910, W_A_B_910, W_AB_820, W_A_B_820, W_AB_730 , W_A_B_730,\\\n", "                           W_AB_640, W_A_B_640, W_AB_550, W_A_B_550,W_AB_721, W_A_B_721, W_AB_532,\\\n", "                           W_A_B_532, W_<PERSON>_111, W_<PERSON>_<PERSON>_111, W_AB_190, W_A_B_190, W_<PERSON>_280 , W_A_B_280,\\\n", "                           W_AB_370, W_A_B_370, W_AB_460, W_A_B_460, W_<PERSON>_127, W_A_B_127, W_<PERSON>_235, W_A_B_235)\n", "\n", "    \n", "    return data_2014_UP,data_2014_DOWN,len(W_AB_111)#,trade_1,trade_2#,timestamp"], "outputs": [], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T05:47:21.507156Z", "start_time": "2025-03-17T05:47:21.492932Z"}}, "cell_type": "code", "source": ["def train_test_to_csv(month,day,traded_time):\n", "    data_UP,data_DOWN,len_ = data(month,day,traded_time)\n", "    path_up = 'order_book_3_2014'\\\n", "            +'_'+str(month)+'_'+str(day)+'_'+'UP'+'.csv'\n", "    path_down = 'order_book_3_2014'\\\n", "            +'_'+str(month)+'_'+str(day)+'_'+'DOWN'+'.csv'\n", "    data_UP.to_csv(path_up, index = False)\n", "    data_DOWN.to_csv(path_down, index = False)    "], "outputs": [], "execution_count": 10}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-17T05:48:08.097766Z", "start_time": "2025-03-17T05:47:42.254166Z"}}, "cell_type": "code", "source": ["month = 1\n", "day_ = [2]\n", "traded_time = 600\n", "for i in day_:\n", "    print(i)\n", "    train_test_to_csv(month,i,600)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "46680\n"]}], "execution_count": 12}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 0}