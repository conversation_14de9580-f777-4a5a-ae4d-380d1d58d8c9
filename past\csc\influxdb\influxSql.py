# -*- coding: utf-8 -*-
"""
Created on Mon Jul 12 17:20:53 2021

@author: humy2
"""

from datetime import timedelta
from influxdb import InfluxDBClient
client = InfluxDBClient('192.168.203.11',8989,'','','testbase') #上期所prod
client = InfluxDBClient('10.17.88.168',9001,'','','testbase') #东坝
client = InfluxDBClient('10.101.237.137',9090,'','','testbase') #东坝测试，带实时行情
client = InfluxDBClient('202.0.3.209',8989,'','','testbase') #大商所
client = InfluxDBClient('202.0.3.200',8989,'','','testbase') #郑商所

import datetime
beginStr = '2021-11-23T21:00:00.0Z'
endStr = '2021-11-24T15:00:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000

result = client.query("select * from testdalian where time >= %d and time <= %d;"%(begin,end)) 
result = client.query("select * from test10 where insid_md='hc2110' and time >= %d and time <= %d;"%(begin,end)) 

result = client.query("select * from dce_option_order where time >= %d and time <= %d;"%(begin,end)) 

result = client.query("select * from future_trade where insid='rb2202';")
#%%
#########################
#检验数据大小，上期所一天4万，缺夜盘25000，大商所一天8万，缺夜盘5万
########################
date = '2021-07-09'
start = datetime.datetime(2021, 7, 12)
end = datetime.datetime(2021, 7, 28)
delta = datetime.timedelta(days=1)
day = start
weekend=set([5,6])
date_formate = "%Y-%m-%d"
while day<=end:
    if day.weekday() in weekend:
        day+=delta
        continue
    else:
        beginStr = date+'T21:00:00.0Z'
        date = day.strftime(date_formate)
        endStr = date+'T15:00:00.0Z'
        beginTs = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
        endTs = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
        
        day+=delta
        result = client.query("select * from test10 where insid_md='hc2110' and time >= %d and time <= %d;"%(beginTs,endTs)) 
        points = result.get_points()
        l=[]
        for d in points:
            l.append(d)
        print('points between %s and %s are:%d'%(beginStr,endStr,len(l)))
        
#%%  
        
points = result.get_points()
l=[]
for d in points:
    l.append(d)
print(len(l))
    
import pandas as pd
import datetime
df = pd.DataFrame(l)
df['datetime']=df.time.apply(lambda x:datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ'))+timedelta(hours=8)
df.index = df['datetime']

dfRequest = df[((df.note=='OS准备撤单')|(df.note=='OS准备下单'))]
resample = dfRequest.time.resample('S').count()
plt.plot(resample)


df['unixStamp'] = df.time.apply(lambda x:datetime.datetime.strptime(str(x),'%Y-%m-%dT%H:%M:%S.%fZ').timestamp())
diff = (df['unixStamp'] - df['unixStamp'].shift(1)).fillna(0)
diff = diff[((diff<0.75)&(diff>0.25))]

dfTest = df.iloc[100000:100000+20000][cols]
dfTest.local_t=dfTest.local_t.apply(lambda x:datetime.datetime.fromtimestamp(x/1000000000).strftime('%Y-%m-%dT%H:%M:%S.%f'))

diff.std()
diff.min()
diff.max()

import matplotlib.pyplot as plt
plt.boxplot(diff)


df['dv'] = df.v.diff().fillna(0)
df['value'] = df.turnover.diff().fillna(0)
df['avg'] = df['value'] / df['dv'] /10
