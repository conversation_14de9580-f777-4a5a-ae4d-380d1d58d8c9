import json
from urllib import request
from bs4 import BeautifulSoup
import re
import time
import os

# 设置保存图片的路径，否则会保存到程序当前路径
path = os.path.abspath(os.curdir)  # 路径前的r是保持字符串原始值的意思，就是说不对其中的符号进行转义
offset=0
for offset in range(0, 10000, 5):
    # url = "https://www.zhihu.com/question/22918070"
    url = u"https://www.zhihu.com/api/v4/questions/297715922/answers?include=data%5B*%5D.is_normal%2Cadmin_closed_comment%2Creward_info%2Cis_collapsed%2Cannotation_action%2Cannotation_detail%2Ccollapse_reason%2Cis_sticky%2Ccollapsed_by%2Csuggest_edit%2Ccomment_count%2Ccan_comment%2Ccontent%2Ceditable_content%2Cvoteup_count%2Creshipment_settings%2Ccomment_permission%2Ccreated_time%2Cupdated_time%2Creview_info%2Crelevant_info%2Cquestion%2Cexcerpt%2Crelationship.is_authorized%2Cis_author%2Cvoting%2Cis_thanked%2Cis_nothelp%2Cis_labeled%2Cis_recognized%2Cpaid_info%3Bdata%5B*%5D.mark_infos%5B*%5D.url%3Bdata%5B*%5D.author.follower_count%2Cbadge%5B*%5D.topics&offset={}&limit=5&sort_by=default&platform=desktop".format(
        offset)
    req = request.Request(url)
    req.add_header('Referer', "https://www.zhihu.com/question/297715922")
    html = request.urlopen(req).read().decode('utf-8')

    jd = json.loads(html)

    for data in jd['data']:
        links=re.findall('img .*?src="(.*?)"', data['content'])
        for link in links:
            if link[:4]=="data":
                continue
            print(link)
            # 保存链接并命名，time.time()返回当前时间戳防止命名冲突
            try:
                request.urlretrieve(link, path + '/images2/%s.jpg' % time.time())  # 使用request.urlretrieve直接将所有远程链接数据下载到本地
            except:
                print(0)
    print(offset)
    if jd['paging']['is_end']:
        break

    # soup = BeautifulSoup(html, 'html.parser')
    # # print(soup.prettify())
    #
    # # 用Beautiful Soup结合正则表达式来提取包含所有图片链接（img标签中，class=**，以.jpg结尾的链接）的语句
    # links = soup.find_all('img', "origin_image zh-lightbox-thumb", src=re.compile(r'.jpg$'))
    # print(links)


