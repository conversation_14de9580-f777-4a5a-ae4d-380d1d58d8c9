import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import Qt.labs.platform as Platform

Item {
    id: root
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 5
        
        TableView {
            id: tableView
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            model: backend.tableModel
            
            delegate: Rectangle {
                implicitWidth: 100
                implicitHeight: 30
                color: row % 2 ? "#f0f0f0" : "white"
                
                Text {
                    anchors.fill: parent
                    text: display
                    elide: Text.ElideRight
                    verticalAlignment: Text.AlignVCenter
                    horizontalAlignment: Text.AlignHCenter
                }
            }
        }
    }
}