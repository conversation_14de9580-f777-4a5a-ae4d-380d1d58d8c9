# -*- coding:utf-8 -*-
from __future__ import print_function
import pyodbc
import pandas as pd
import numpy as np
from WindPy import w
from pandas import DataFrame
import math
from datetime import datetime
from scipy.stats import norm
import xlwt
import sys

sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')
from optionweek import greeks

w.start()

datebegin = "2015-02-09"
dt = "2017-06-30"
uscode = '510050.SH'


def savexl(sqllist2):
    f = xlwt.Workbook()  # 创建工作簿
    sheet1 = f.add_sheet(u'vix', cell_overwrite_ok=True)  # 创建sheet
    for i, row in enumerate(sqllist2):
        for j, col in enumerate(row):
            sheet1.write(i, j, col)
    f.save('vol3.xls')  # 保存文件


def findvol():
    date, opt_list = getMonthIndex()

    codes = []
    for i in opt_list['option_code']:
        codes.append(i)

    wdata = w.wss(codes, "windcode,sec_name,close,us_close,us_preclose,volume,oi,dlmonth,exe_price",
                  "tradeDate=%s;priceAdj=U;cycle=D" % datenow)
    opt_listall = pd.DataFrame(wdata.Data, index=wdata.Fields, columns=opt_list.index.values)
    opt_listall = opt_listall.T  # 将矩阵转置

    us_price = opt_listall['US_CLOSE'].values[0]

    opt_listall.loc[:, 'option_code'] = opt_list.loc[:, 'option_code']
    opt_listall.loc[:, 'call_put'] = opt_list.loc[:, 'call_put']
    opt_listall.loc[:, 'T'] = opt_list.loc[:, 'expiredate'] / 365

    oi1 = opt_listall[opt_listall['call_put'] == u'认购'].OI.sum()
    oi2 = opt_listall[opt_listall['call_put'] == u'认沽'].OI.sum()
    oi = oi1 + oi2
    PC = float(oi2) / oi1
    volume1 = opt_listall[opt_listall['call_put'] == u'认购'].VOLUME.sum()
    volume2 = opt_listall[opt_listall['call_put'] == u'认沽'].VOLUME.sum()
    volume = volume1 + volume2
    PC2 = float(volume2) / volume1

    # opt_list1 = opt_listall[opt_listall['DLMONTH'] == str(date[0])]

    T0 = float(opt_list['expiredate'].values[0]) / 365
    # df0 = dfmaker(opt_listall)

    try:
        df0 = dfmaker(opt_listall[~opt_listall.SEC_NAME.str.contains('A')])  # 去除分红加上的合约
        df1 = df0[df0['DLMONTH'] == str(date[0])]
        df2 = df0[df0['DLMONTH'] == str(date[1])]
        T1 = float(df1['T'].values[0])
        # if df1.empty or df2.empty:
        #     print('仅有非标准合约')
        #     df0 = dfmaker(opt_listall)
        #     df1 = df0[df0['DLMONTH'] == str(date[0])]
        #     df2 = df0[df0['DLMONTH'] == str(date[1])]
    except:
        print('近月仅有非标准合约')
        df0 = dfmaker(opt_listall)
        df1 = df0[df0['DLMONTH'] == str(date[0])]
        df2 = df0[df0['DLMONTH'] == str(date[1])]

    if df1.empty or df2.empty:
        print('次月仅有非标准合约')
        df0 = dfmaker(opt_listall)
        df1 = df0[df0['DLMONTH'] == str(date[0])]
        df2 = df0[df0['DLMONTH'] == str(date[1])]

    mincode, min, pricecall, priceput, imvol1, imvol2, imvol, imvolmean = findimvol(df0, T0)

    T1 = float(df1['T'].values[0])
    sigma1, s1 = findsigma(df1, T1)
    T2 = float(df2['T'].values[0])
    sigma2, s2 = findsigma(df2, T2)
    NT1 = T1 * 365
    NT2 = T2 * 365

    w1_30 = (NT2 - 30) / (NT2 - NT1)
    w2_30 = (30 - NT1) / (NT2 - NT1)
    a = (w1_30 * T1 * sigma1 + w2_30 * T2 * sigma2) * 365.0 / 30
    VIX30 = 100 * np.sqrt(a)

    w1_60 = (NT2 - 60) / (NT2 - NT1)
    w2_60 = (60 - NT1) / (NT2 - NT1)
    a = (w1_60 * T1 * sigma1 + w2_60 * T2 * sigma2) * 365.0 / 60
    VIX60 = 100 * np.sqrt(a)

    s3 = w1_30 * s1 + (1 - w1_30) * s2
    skew = -s3

    return us_price, mincode, min, pricecall, priceput, T0, T1, T2, volume, PC2, oi, PC, imvol1, imvol2, imvol, imvolmean, VIX30, VIX60, skew


def getMonthIndex():
    wdata = w.wset("optionchain",
                   u"date=%s;us_code=510050.SH;option_var=全部;call_put=全部;"
                   u"field=option_var,option_code,option_name,strike_price,month,call_put,last_tradedate,expiredate"
                   % datenow)

    opt_list = pd.DataFrame(wdata.Data, index=wdata.Fields, columns=None)
    opt_list = opt_list.T  # 将矩阵转置

    opt_list2 = opt_list[opt_list['expiredate'] > 7]
    date = []
    month = opt_list2['month'].values[0]
    date.append(month)
    for i in opt_list2['month']:
        if i != month:
            month = i
            date.append(month)
    return date, opt_list


def dfmaker(opt_list0):
    optcall = opt_list0[opt_list0['call_put'] == u'认购']
    optput = opt_list0[opt_list0['call_put'] == u'认沽']
    optcall = optcall.reset_index(drop=True)
    optput = optput.reset_index(drop=True)

    df = optcall
    df.loc[:, 'put'] = 0
    df.loc[:, 'delta2'] = 0  # s-k
    # for i in range(0,len(optcall.CLOSE)):
    # index=df.index[i]
    # index2 = opt_list0.index[i+len(optcall.CLOSE)]
    # df.loc[index,'put'] = optput.loc[index2,'CLOSE']

    df.loc[:, 'put'] = optput.loc[:, 'CLOSE']
    df.loc[:, 'delta'] = df.loc[:, 'CLOSE'] - df.loc[:, 'put']
    df.loc[:, 'delta2'] = df.loc[:, 'EXE_PRICE'] - df.loc[:, 'US_CLOSE']
    # df
    return df


def findimvol(df, T):
    mindelta = df['delta2'].abs().min()
    try:
        min = df[df['delta2'] == -mindelta]['EXE_PRICE']
        indexmin2 = min.index[0]
    except:
        min = df[df['delta2'] == mindelta]['EXE_PRICE']
        indexmin2 = min.index[0]

    min = min.values[0]
    mincode = df.loc[indexmin2, 'WINDCODE']

    # df['QK2'] = 0
    # df.loc[:indexmin2, 'QK2'] = df.loc[:indexmin2, 'put']
    # df.loc[indexmin2:, 'QK2'] = df.loc[indexmin2:, 'CLOSE']  # CALL
    pricecall = df.loc[indexmin2, 'CLOSE']
    priceput = df.loc[indexmin2, 'put']
    # df.loc[indexmin2, 'QK2'] = df.loc[indexmin2, 'CLOSE']

    df.loc[:, 'imvol'] = 0
    df.loc[:, 'vega'] = 0
    for i in range(0, len(df.index)):
        index = df.index[i]
        T = df.loc[index, 'T']
        try:
            S = df.loc[index, 'CLOSE'] - df.loc[index, 'put'] + df.loc[index, 'EXE_PRICE']
            # S = df.loc[index, 'US_CLOSE']
        except:
            continue
        # S=df.loc[index, 'US_CLOSE']
        # df.loc[index, 'imvol2'] = greeks.BSOptionISDGoalSeekNR(1, S, df.loc[index, 'EXE_PRICE'], R2, 0, T,
        # df.loc[index, 'CLOSE'])

        try:
            df.loc[index, 'imvol'] = greeks.BSOptionISDGoalSeekNR(1, S, df.loc[index, 'EXE_PRICE'], Rvol * 0, 0, T,
                                                                  df.loc[index, 'CLOSE'])
        except:
            df.loc[index, 'imvol'] = 0

        if df.loc[index, 'imvol'] == 0:
            df.loc[index, 'vega'] = 0
        else:
            df.loc[index, 'vega'] = greeks.BSOptionVega(S, df.loc[index, 'EXE_PRICE'], Rvol, 0, T,
                                                        df.loc[index, 'imvol'])

    # imvol = df[df['imvol']>0]['imvol'].min()

    imvol = 0
    imvolmean = 0
    imvol1 = 0
    imvol2 = 0
    try:
        df2 = df[df['EXE_PRICE'] == min]
        # imvol=np.sqrt(((df2['imvol']**2)*(df2['vega']**2)).sum())/(df2['vega'].sum())
        # imvolmean = np.sqrt(((df['imvol']**2)*(df['vega']**2)).sum())/(df['vega'].sum())
        imvol = (((df2['imvol']) * (df2['vega'])).sum()) / (df2['vega'].sum())
        imvolmean = (((df['imvol']) * (df['vega'])).sum()) / (df['vega'].sum())
        df2 = df[(df['EXE_PRICE'] == min) & (df['T'] > 2 / 365)] #######交割日前两天
        index = df2.index[0]
        imvol1 = df2.loc[index, 'imvol']
        index2 = df2.index[1]
        imvol2 = df2.loc[index2, 'imvol']
    except Exception as e2:
        print(e2)
    # except:
    #     pass
    return mincode, min, pricecall, priceput, imvol1, imvol2, imvol, imvolmean


def hisvol(pf0, nrows, day1):
    pf0.loc[:, 'daily_vol%s' % day1] = 0
    pf0.loc[:, 'ann_vol%s' % day1] = 0
    for j in range(nrows-1, len(pf0.index)):
        index2 = pf0.index[j]
        if p != 1:
            a = math.log(pf0.loc[pf0.index[j], ['us_price']] / pf0.loc[pf0.index[j - 1], ['us_price']])
            pf0.loc[index2, ['log_return']] = a
        if j >= day1-1:
            a = pf0[j - day1+1:j+1]['log_return']
            stan = np.std(a)
            pf0.loc[index2, ['daily_vol%s' % day1]] = stan
            pf0.loc[index2, ['ann_vol%s' % day1]] = stan * math.sqrt(252)


def findsigma(df, T):
    mindelta = df['delta'].abs().min()
    try:
        min = df[df['delta'] == mindelta]['EXE_PRICE']
        indexmin = min.index[0]
    except:
        min = df[df['delta'] == -mindelta]['EXE_PRICE']
        indexmin = min.index[0]

    min = min.values[0]

    F = min + math.exp(Rvix / 365 * T) * mindelta
    k0 = df[df['EXE_PRICE'] < F]['EXE_PRICE']
    indexmin2 = k0.index.max()
    min2 = k0.max()  # K0
    df.loc[:, 'QK'] = 0
    df.loc[:indexmin2, 'QK'] = df.loc[:indexmin2, 'put']
    df.loc[indexmin2:, 'QK'] = df.loc[indexmin2:, 'CLOSE']
    df.loc[indexmin2, 'QK'] = (df.loc[indexmin2, 'CLOSE'] + df.loc[indexmin2, 'put']) / 2

    df.loc[:, 'rr'] = 0
    for i in range(1, len(df.index) - 1):
        index = df.index[i]
        df.loc[index, 'rr'] = (df.loc[df.index[i + 1], 'EXE_PRICE'] - df.loc[
            df.index[i - 1], 'EXE_PRICE']) / 2 / (df.loc[df.index[i], 'EXE_PRICE'] ** 2) * math.exp(
            Rvix * T) * df.loc[df.index[i], 'QK']

    df.loc[df.index[0], 'rr'] = (df.loc[df.index[2], 'EXE_PRICE'] - df.loc[df.index[0], 'EXE_PRICE']) / 2 / (
            df.loc[df.index[0], 'EXE_PRICE'] ** 2) * math.e ** (Rvix * T) * df.loc[df.index[0], 'QK']
    im = len(df.index) - 1
    df.loc[df.index[im], 'rr'] = (df.loc[df.index[im], 'EXE_PRICE'] - df.loc[
        df.index[im - 2], 'EXE_PRICE']) / 2 / (df.loc[df.index[im], 'EXE_PRICE'] ** 2) * math.e ** (Rvix * T) * \
                                 df.loc[df.index[im], 'QK']

    df.loc[:, 'x'] = df.loc[:, 'rr']

    a = df.loc[:, 'EXE_PRICE'].values / F
    a = np.log(np.array(a, dtype=np.float32))
    df.loc[:, 'y'] = np.multiply(df['x'].values, np.square((1 - a)))
    df.loc[:, 'z'] = df.loc[:, 'x'] * (2 * a - a ** 2) * 3
    # df['zz'] = np.multiply(df['x'] , (2 * a - np.square(a))) * 3

    E1 = -(1 + math.log(F / min2) - F / min2)
    E2 = 2 * math.log(F / min2) * (F / min2 - 1) + 1.0 / 2 * math.log(F / min2) ** 2
    E3 = 3 * math.log(F / min2) ** 2 * (1.0 / 3 * math.log(F / min2) - 1 + (F / min2))

    sumop = df.rr.sum()
    sigma = sumop * 2 / T - (F / min2 - 1) ** 2 / T

    P1 = df.x.sum() * math.exp(Rvix * T) + E1
    P2 = df.y.sum() * math.exp(Rvix * T) + E2
    P3 = df.z.sum() * math.exp(Rvix * T) + E3
    s1 = (P3 - 3 * P1 * P2 + 2 * P1 ** 3) / ((P2 - P1 ** 2) ** (3.0 / 2))

    return sigma, s1


if __name__ == '__main__':
    sqllist2 = []
    sqllist = []
    sqllist.append('datetime')
    sqllist.append('t')
    sqllist.append('t1')
    sqllist.append('t2')
    sqllist.append('us_price')
    sqllist.append('windcode')
    sqllist.append('strikeprice')
    sqllist.append('call')
    sqllist.append('put')
    sqllist.append('volume')
    sqllist.append('PC1')
    sqllist.append('oi')
    sqllist.append('PC2')
    sqllist.append('im1')
    sqllist.append('imm')
    sqllist.append('VIX30')
    sqllist.append('VIX60')
    sqllist.append('skew')
    sqllist.append('imvol1')
    sqllist.append('imvol2')
    sqllist2.append(sqllist)
    rdata = w.wsd("SHIBOR3M.IR", "close", datebegin, dt, "PriceAdj=F")
    rlist = pd.DataFrame(rdata.Data, index=rdata.Fields, columns=rdata.Times)
    rlist = rlist.T  # 将矩阵转置

    rdata2 = w.wsd("LPR1Y.IR", "close", datebegin, dt, "PriceAdj=F")
    rlist2 = pd.DataFrame(rdata2.Data, index=rdata2.Fields, columns=rdata2.Times)
    rlist2 = rlist2.T  # 将矩阵转置
    for datenow in w.tdays(datebegin, dt, "").Data[0]:
        # datenow ='2015-02-9'
        rlist.index = pd.DatetimeIndex(rlist.index)
        Rvol = rlist.loc[datetime.strftime(datenow, "%Y-%m-%d"), 'CLOSE'] / 100
        rlist2.index = pd.DatetimeIndex(rlist.index)
        Rvix = rlist2.loc[datetime.strftime(datenow, "%Y-%m-%d"), 'CLOSE'] / 100
        us_price, windcode, strikeprice, pricecall, priceput, T0, T1, T2, volume, PC2, oi, PC, imvol1, imvol2, imvol, imvolmean, VIX30, VIX60, skew = findvol()

        sqllist = []
        print(datenow)
        sqllist.append(datetime.strftime(datenow, "%Y-%m-%d"))
        sqllist.append(T0 * 365)
        sqllist.append(T1 * 365)
        sqllist.append(T2 * 365)
        sqllist.append(us_price)
        sqllist.append(windcode)
        sqllist.append(strikeprice)
        sqllist.append(pricecall)
        sqllist.append(priceput)
        sqllist.append(volume)
        sqllist.append(PC2)
        sqllist.append(oi)
        sqllist.append(PC)
        sqllist.append(imvol)
        sqllist.append(imvolmean)
        sqllist.append(VIX30)
        sqllist.append(VIX60)
        sqllist.append(skew)
        sqllist.append(imvol1)
        sqllist.append(imvol2)
        sqllist2.append(sqllist)

    pand = pd.DataFrame(sqllist2[1:], index=w.tdays(datebegin, dt, "").Data[0], columns=sqllist2[0])

    p = 0
    nrows=1
    pand.loc[:, 'log_return'] = 0
    try:
        hisvol(pand, nrows, 20)
        p = 1  # pass re work
        hisvol(pand, nrows, 60)
        hisvol(pand, nrows, 120)
    except Exception as e:
        print(str(e))

    try:
        pand.to_excel('volvixskew5.xls', sheet_name='50vol')
        # savexl(sqllist2)
    except:
        savexl(sqllist2)
        print(pand)
