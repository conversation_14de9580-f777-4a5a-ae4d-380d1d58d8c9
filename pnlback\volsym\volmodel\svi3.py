#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2020/4/3 12:59 PM
# <AUTHOR> 稻草人
# @contact : <EMAIL>
# @File    : svi.py
# @Desc    :

import sys

from numpy import (power, sign, maximum, round, argmin, sqrt, abs, array, clip, minimum, nan_to_num, ndarray,
                   log as np_log)
from numpy.linalg import solve
from scipy.optimize import minimize
import numpy as np


class BaseSVI(object):
    @staticmethod
    def svi_raw(k, a, b, m, rho, sigma):
        """raw SVI parameterization

        :param k: log-moneyness at which to evaluate the total implied variance
        :param a: level of variance
        :param b: gives the angle between the left and right asymptotes
        :param m: translates smile to right
        :param rho: counter-clockwise rotation of smile
        :param sigma: determines how smooth the vertex is，reduces ATM curvature of the smile
        :return: estimated total variance at k
        """
        # make sure that parameter restrictions are satisfied
        assert b >= 0, 'b has to be non-negative'
        assert abs(rho) <= 1, '|rho| has to be smaller than 1'
        assert sigma >= 0, 'sigma has to be positive'
        assert a + b * sigma * sqrt(1 - rho ** 2) >= 0, 'a + b sigma (1-rho^2)^0.5 has to be non-negative'

        return a + b * (rho * (k - m) + sqrt(power(k - m, 2) + power(sigma, 2)))

    @staticmethod
    def asymptotes(k, a, b, m, rho):
        """
        raw SVI parameterization left and right asymptotes
        :param k:
        :param a:
        :param b:
        :param m:
        :param rho:
        :return:
        """
        left = a - b * (1 - rho) * (k - m)
        right = a + b * (1 + rho) * (k - m)
        return left, right

    @staticmethod
    def svi_natural(k, delta, mu, rho, omega, zeta):
        """natural SVI parameterization

        :param k: log-moneyness at which to evaluate the total implied variance
        :param delta: level of variance
        :param mu: slope of wings
        :param rho: translates smile to right
        :param omega: counter-clockwise rotation of smile
        :param zeta: reduces ATM curvature of the smile
        :return: estimated total variance at k
        """
        # make sure that parameter restrictions are satisfied
        assert omega >= 0, 'omega has to be non-negative'
        assert abs(rho) < 1, '|rho| has to be smaller than 1'
        assert zeta > 0, 'zeta has to be positive'
        assert delta + omega * (1 - rho ** 2) >= 0, 'delta + omega (1-rho^2) has to be non-negative'

        return delta + omega / 2 * (1 + zeta * rho * (k - mu) + sqrt((zeta * (k - mu) + rho) ** 2 + (1 - rho ** 2)))

    def svi_jump_wing(self, k, tau, v, psi, p, c, vt):
        """jump-wing SVI parameterization
        This function implements the jump-wings formulation.

        :param k: moneyness at which to evaluate the surface
        :param tau:
        :param v: ATM variance
        :param psi: ATM skew
        :param p: slope of left/put wing
        :param c: slope of right/call wing
        :param vt: minimum implied variance
        :return:
        """
        # make sure that parameter restrictions are satisfied
        assert v >= 0, 'v has to be non-negative'
        assert p >= 0, 'p has to be non-negative'
        assert c >= 0, 'c has to be non-negative'
        assert vt >= 0, 'vt has to be non-negative'

        a, b, m, rho, sigma = self.convert_param_from_jump_wing_to_raw(tau, v, psi, p, c, vt)
        return self.svi_raw(k, a, b, m, rho, sigma)

    @staticmethod
    def convert_param_from_raw_to_natural(a, b, m, rho, sigma):
        """

        :param a:
        :param b:
        :param m:
        :param rho:
        :param sigma:
        :return:
        """
        omega = 2 * b * sigma / sqrt(1 - rho ** 2)
        delta = a - omega / 2 * (1 - rho ** 2)
        mu = m + rho * sigma / sqrt(1 - rho ** 2)
        zeta = sqrt(1 - rho ** 2) / sigma
        return delta, mu, rho, omega, zeta

    @staticmethod
    def convert_param_from_raw_to_jump_wing(tau, a, b, m, rho, sigma):
        """

        :param tau:
        :param a:
        :param b:
        :param m:
        :param rho:
        :param sigma:
        :return:
        """
        w = a + b * (-rho * m + sqrt(m ** 2 + sigma ** 2))
        v = w / tau
        psi = 1 / sqrt(w) * b / 2 * (-m / sqrt(m ** 2 + sigma ** 2) + rho)
        p = 1 / sqrt(w) * b * (1 - rho)
        c = 1 / sqrt(w) * b * (1 + rho)
        vt = 1 / tau * (a + b * sigma * sqrt(1 - rho ** 2))
        return v, psi, p, c, vt

    @staticmethod
    def convert_param_from_natural_to_raw(delta, mu, rho, omega, zeta):
        """

        :param delta:
        :param mu:
        :param rho:
        :param omega:
        :param zeta:
        :return:
        """
        a = delta + omega / 2 * (1 - rho ** 2)
        b = omega * zeta / 2
        m = mu - rho / zeta
        sigma = sqrt(1 - rho ** 2) / zeta
        return a, b, m, rho, sigma

    @staticmethod
    def convert_param_from_jump_wing_to_raw(tau, v, psi, p, c, vt):
        """

        :param tau:
        :param v:
        :param psi:
        :param p:
        :param c:
        :param vt:
        :return:
        """
        w = v * tau

        b = sqrt(w) / 2 * (c + p)
        rho = 1 - p * sqrt(w) / b
        beta = rho - 2 * psi * sqrt(w) / b
        alpha = sign(beta) * sqrt(1 / beta ** 2 - 1)
        m = (v - vt) * tau / (b * (-rho + sign(alpha) * sqrt(1 + alpha ** 2) - alpha * sqrt(1 - rho ** 2)))
        if m == 0:
            sigma = (vt * tau - w) / b / (sqrt(1 - rho ** 2) - 1)
        else:
            sigma = alpha * m
        a = vt * tau - b * sigma * sqrt(1 - rho ** 2)
        sigma = maximum(sigma, 0)
        return a, b, m, rho, sigma

    def convert_param_from_natural_to_jump_wing(self, tau, delta, mu, rho, omega, zeta):
        """

        :param tau:
        :param delta:
        :param mu:
        :param rho:
        :param omega:
        :param zeta:
        :return:
        """
        a, b, m, rho, sigma = self.convert_param_from_natural_to_raw(delta, mu, rho, omega, zeta)
        return self.convert_param_from_raw_to_jump_wing(tau, a, b, m, rho, sigma)

    def convert_param_from_jump_wing_to_natural(self, tau, v, psi, p, c, vt):
        """

        :param tau:
        :param v:
        :param psi:
        :param p:
        :param c:
        :param vt:
        :return:
        """
        a, b, m, rho, sigma = self.convert_param_from_jump_wing_to_raw(tau, v, psi, p, c, vt)
        return self.convert_param_from_raw_to_natural(a, b, m, rho, sigma)

    @staticmethod
    def convert_param_from_surface_to_jump_wing(tau, theta, rho, phi):
        """

        :param tau:
        :param theta:
        :param rho:
        :param phi:
        :return:
        """
        v = theta / tau
        psi = 0.5 * rho * sqrt(theta) * phi
        p = 0.5 * sqrt(theta) * phi * (1 - rho)
        c = p + 2 * psi
        vt = v * (4 * p * c) / ((p + c) ** 2)
        return v, psi, p, c, vt


class SVI(BaseSVI):

    def loss(self, tiv: ndarray, vega: ndarray, y: ndarray, c: float, d: float, a: float) -> float:
        """object function

        :param tiv: total implied variance
        :param vega:
        :param y:
        :param c:
        :param d:
        :param a:
        :return:
        """

        diff = tiv - (a + d * y + c * sqrt(y * y + 1))
        return (vega * diff * diff).mean()

    def acceptable(self, tiv: ndarray, sigma: float, c: float, d: float, a: float) -> bool:
        """convex domain (a parallelepipedon), parameter boundary check

        :param tiv:total implied variance
        :param sigma:
        :param c: b * sigma * T
        :param d: rho * b * sigma * T
        :param a: a * T
        :param eps: float extreme limit
        :return:
        """
        eps = sys.float_info.epsilon
        con1 = -eps < c < 4 * sigma + eps
        con2 = abs(d) - eps < minimum(c, 4 * sigma - c) + eps
        con3 = -eps < a < minimum(tiv.max(), 1e6) + eps
        return con1 and con2 and con3

    def calibration(self, tiv: ndarray, vega: ndarray, k: ndarray, m: float, sigma: float) -> tuple[float, float, float,
                                                                                               float]:
        """
        Quasi-Explicit Calibration of Gatheral’s SVI model
        Step 1. find the global minimizer of f, solving the linear system ∇f = 0. If the output belongs to D, then stop;
        Step 2. if Step 1 yields a global minimum outside D, then look for min partial Df.
        :param tiv:
        :param vega:
        :param k:
        :param m:
        :param sigma:
        :return:
        """
        vega /= vega.max()  # winsorize
        w = vega.mean()  # weight replace n, n = tiv.shape[0]

        # scale
        y = (k - m) / sigma

        # 2020-04-10 sum -> mean 消除batch-size 数量级对epsilon影响,
        # vega adjusted the weight of tail contracts in loss function
        y1 = (vega * y).mean()
        y2 = (vega * y * y).mean()
        y3 = (vega * sqrt(y * y + 1)).mean()
        y4 = (vega * y * sqrt(y * y + 1)).mean()
        y5 = (vega * (y * y + 1)).mean()

        vy2 = (vega * tiv * sqrt(y * y + 1)).mean()
        vy = (vega * tiv * y).mean()
        v = (vega * tiv).mean()

        # Step 1. find the global minimizer of f, solving the linear system ∇f = 0. If the output belongs to D, then stop;
        matrix = [
            [y5, y4, y3],
            [y4, y2, y1],
            [y3, y1, w]
        ]
        vector = [vy2, vy, v]
        c, d, a, = solve(array(matrix), array(vector))

        # check if parameters in free-arbitrage constraints
        if self.acceptable(tiv, sigma, c, d, a):
            loss = self.loss(tiv, vega, y, c, d, a)
            return c, d, a, loss

        # Step 2. if Step 1 yields a global minimum outside D, then look for min partial Df.
        matrix_list = []
        vector_list = []

        # Finds the solutions for which one of the three parameters is at its bounds
        # system for a = 0
        matrix = [
            [y5, y4, y3],
            [y4, y2, y1],
            [0, 0, 1]
        ]
        vector = [vy2, vy, 0]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # system for a = max of total implied variance
        # note the optimal fit cannot be systematically greater than the largest observed variance.
        matrix = [
            [y5, y4, y3],
            [y4, y2, y1],
            [0, 0, 1]
        ]
        vector = [vy2, vy, tiv.max()]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # system for d = c or d = -c => c+d=0 c-d=0
        matrix = [
            [y5, y4, y3],
            [1, -1, 0],
            [y3, y1, w]
        ]
        vector = [vy2, 0, v]
        matrix_list.append(matrix)
        vector_list.append(vector)

        matrix = [
            [y5, y4, y3],
            [1, 1, 0],
            [y3, y1, w]
        ]
        vector = [vy2, 0, v]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # system part for |d| = 4*sigma-c => |d| + c = 4 * sigma
        matrix = [
            [y5, y4, y3],
            [1, 1, 0],
            [y3, y1, w]
        ]
        vector = [vy2, 4 * sigma, v]
        matrix_list.append(matrix)
        vector_list.append(vector)

        matrix = [
            [y5, y4, y3],
            [1, -1, 0],
            [y3, y1, w]
        ]
        vector = [vy2, 4 * sigma, v]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # # system for c = 0
        # matrix = [
        #     [1, 0, 0],
        #     [y4, y2, y1],
        #     [y3, y1, w]
        # ]
        # vector = [0, vy, v]
        # matrix_list.append(matrix)
        # vector_list.append(vector)
        #
        # # system for c = 4*sigma
        # matrix = [
        #     [1, 0, 0],
        #     [y4, y2, y1],
        #     [y3, y1, w]
        # ]
        # vector = [4*sigma, vy, v]
        # matrix_list.append(matrix)
        # vector_list.append(vector)

        # Finds the solution for which two of the three parameters are at its bounds.
        # The Zeliade whitepaper speaks about doing one-dimensinal search to find the minimum,
        # but since fixing 2 out of the three parameters just leads to a quadratic equation in the third,
        # finding the global minimum and then cutting it off if it falls outside the  valid boundaries, works.

        # c = 0, implies d = 0, find optimal a
        matrix = [
            [1, 0, 0],
            [0, 1, 0],
            [y3, y1, w]
        ]
        vector = [0, 0, v]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # c = 4*sigma, implied d = 0, find optimal a
        matrix = [
            [1, 0, 0],
            [0, 1, 0],
            [y3, y1, w]
        ]
        vector = [4 * sigma, 0, v]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # a = 0, d = c, find optimal c
        matrix = [
            [y5, y4, y3],
            [1, -1, 0],
            [0, 0, 1]
        ]
        vector = [vy2, 0, 0]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # a = 0, d = -c, find optimal c
        matrix = [
            [y5, y4, y3],
            [1, 1, 0],
            [0, 0, 1]
        ]
        vector = [vy2, 0, 0]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # a = 0, d = 4*sigma-c, find optimal c => d+c=4*sigma
        matrix = [
            [y5, y4, y3],
            [1, 1, 0],
            [0, 0, 1]
        ]
        vector = [vy2, 4 * sigma, 0]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # a = 0, d = c-4s, find optimal c => c-d = 4*sigma
        matrix = [
            [y5, y4, y3],
            [1, -1, 0],
            [0, 0, 1]
        ]
        vector = [vy2, 4 * sigma, 0]
        matrix_list.append(matrix)
        vector_list.append(vector)

        # optimizer, solve
        params = []
        for matrix, vector in zip(matrix_list, vector_list):
            c, d, a = solve(array(matrix), array(vector))

            # clip make params in convex domain
            c = clip(c, 0, maximum(0, 4 * sigma))
            a = clip(a, 0, maximum(tiv.max(), 0))
            d_ub = minimum(maximum(c, 0), maximum(4 * sigma - c, 0))
            d_lb = maximum(-maximum(c, 0), -maximum(4 * sigma - c, 0))
            d = clip(d, d_lb, d_ub)

            loss = self.loss(tiv, vega, y, c, d, a)
            if self.acceptable(tiv, sigma, c, d, a):
                params.append([c, d, a, loss])

        # 处理sigma 值为负数的情况
        if len(params) == 0:
            loss = maximum(nan_to_num(loss), 1e-6) * 1e6
            return c, d, a, loss

        params = array(params)
        min_idx = argmin(params[:, -1])
        return params[min_idx]

    def _score(self, param: tuple, tiv: ndarray, vega: ndarray, k: ndarray) -> float:
        """

        :param param: sigma, m
        :param tiv:
        :param vega:
        :param k:
        :return:
        """
        (sigma, m) = param
        return self.calibration(tiv, vega, k, m, sigma)[3]

    def fit(self, tiv: ndarray, vega: ndarray, k: ndarray, tau: float = None, m: float = 0, sigma: float = 0.1,
            epsilon: float = 1e-16) -> tuple[float, float, float, float, float]:
        """implied total variance if tau is None, else implied variance.

        :param tiv: total implied variance, iv * iv * t
        :param vega:
        :param k: moneyness
        :param tau: time to expiry
        :param m:
        :param sigma:
        :param epsilon:
        :return: svi_demo params to generate implied total variance if tau is None, else implied variance.
        """
        if tau is None:
            tau = 1.

        residual = minimize(self._score, array([sigma, m]), args=(tiv, vega, k),
                            bounds=[(0.001, None), (None, None)], tol=epsilon,
                            method="Nelder-Mead")
        assert residual.success
        sigma, m = residual.x

        c, d, a, loss = self.calibration(tiv, vega, k, m, sigma)
        if c != 0:
            a, rho, b = a / tau, d / c, c / (sigma * tau)
        else:
            a, rho, b = a / tau, 0, 0

        # assert tau >= 0 and sigma >= 0 and abs(rho) <= 1
        return [a, b, m, rho, sigma]


def fit_svi_model(config, exp_data, exp, last_exp_data):
    """使用SVI模型拟合波动率曲线
    
    Args:
        config: 配置参数
        exp_data: 期权数据
        exp: 到期日
        last_exp_data: 上一次的拟合结果
        
    Returns:
        tuple: (sigma_fit, voltime, derivatives)
    """
    # 准备输入数据
    tau = exp_data['time2expiry']
    k = np.log(exp_data['K'] / exp_data['forward'])
    v = exp_data['market_sigma'] ** 2 * tau

    # 计算vega权重
    vega = exp_data['vega'].clip(lower=0.0001)

    # 初始化SVI模型并拟合
    svi = SVI()
    result = svi.fit(v, vega, k, tau)

    a, b, m, rho, sigma = result

    variance = svi.svi_raw(k, a.iloc[-1], b.iloc[-1], rho, m, sigma)

    sigma_fit = np.sqrt(variance / tau)

    atm_features = {'atm_vol': 0, 'skew': 0, 'convexity': 0,
                    'otm_slope': 0,
                    'itm_slope': 0}

    rmse_error = np.sqrt(np.mean((vega * (sigma_fit - exp_data['market_sigma'])) ** 2))

    r2 = 1 - np.sum((exp_data['market_sigma'] - sigma_fit) ** 2) / np.sum(
        (exp_data['market_sigma'] - np.mean(exp_data['market_sigma'])) ** 2)

    # 创建包含所有数据的字典
    voltime = {
        **dict(zip(['a', 'b', 'rho', 'm', 'sigma'], result)),  # SVI参数
        **atm_features,  # ATM特征
        'rmse_error': rmse_error,
        'r2': r2
    }

    return exp, sigma_fit, voltime
