# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-10-20
from WindPy import w
import pyodbc
import sys

# import os
# print(os.path.abspath('.'))
sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')
from SQL.choice2sql.update import datemakerSQL

from EmQuantAPI import *
c.start("ForceLogin=1")


def updatefrwind(cpcode, date):
    wcodes = c.sector(cpcode,date)
    if wcodes.ErrorCode != 0:
        print("空")
    codes3 = wcodes.Codes
    # for iii in range(0, len(wcodes.Data[1])):
    #     codes3.append(wcodes.Data[1][iii])
    # print(codes3)
    windtolist(codes3, date)


def windtolist(codes2, date):
    wdata = c.css(codes2, "Open,High,low,Close,Clear,Preclose,PreClear,Differ,ClearDiffer,Volume,OI,OIChg,Amount,DELTA,UnderlyingStdev,STRIKEPRICE,LTDATE","TradeDate=%s,FrIndex=5" % date)

    if wdata.ErrorCode != 0:
        import time
        print(len(wdata.Data[1]))
        print(u"暂停提取数据30秒..请稍等")
        time.sleep(30)
        return windtolist(codes2, date)

    # pf2 = pf.ix['2017-04-24', 'SR707C6200']
    # print(pf2)
    print(codes2)
    print(len(codes2))

    for ii in codes2:
        sqllist = []

        date1 = date
        sqllist.append(date1)
        sqllist.append(ii)

        for k in range(0, len(wdata.Indicators)):
            sqllist.append(wdata.Data[ii][k])

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)


def list2sql(table,opt):
    indexnum = 100
    dt, beginDate, lastday = datemakerSQL.datemakerDay(table, conn, cursor, indexnum)
    datelist = c.tradedates(beginDate, dt, "period=1,order=1,market=CNSESH").Data #w.tdays(beginDate, dt, "").Data[0]
    for i in datelist:
        date = i
        print(date)
        for ii in opt:
            updatefrwind(ii, date)

    sql2 = "INSERT INTO %s VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" % table

    cursor.executemany(sql2, sqllist2)
    conn.commit()


if __name__=='__main__':
    server = '10.25.18.36'
    user = 'Alex'
    password = '789456'
    table = "[Alex].[dbo].[Opt_M]"

    # 命令如何写可以用命令生成器来辅助完成
    # 定义打印输出函数，用来展示数据使用
    # 连接数据库
    conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
    cursor = conn.cursor()

    sqllist2 = []
    print('M')
    list2sql("[Alex].[dbo].[Opt_M]", ['123001'])
    sqllist2 = []
    print('SR')
    list2sql("[Alex].[dbo].[Opt_SR]", ['125001'])

    conn.close()
