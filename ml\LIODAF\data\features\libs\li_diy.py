import pandas as pd
import numpy as np
from data.features.factor_manager import factor_manager, Factor, FactorCategory

complex_factors = [        
        # 订单簿特征 - 买卖失衡指标
        'im5',          # 5档买卖失衡指标
        'im5vol',       # 5档成交量失衡指标
        'mid_minnum',   # 基于最小成交量的中间价格偏离
        # 'mid_minnum2',  # 基于加权平均价格的中间价格偏离
        # 'mid_level',    # 中间价格档位
        'turnover_mid', # 成交均价与中间价格的偏离
        'im1',          # 1档买卖失衡指标
        # 'press',        # 价格压力指标
        # 'im2mult',      # 2档加权买卖失衡指标
        'voi',          # 订单失衡量指标
        'edge_minnum',  # 基于最小成交量的买卖价差
        ]


# 注册DIY因子
def calculate_im5(data: pd.DataFrame) -> pd.Series:
    """计算5档买卖失衡指标
    公式: im5 = round((∑(Price_i * imvol * imprc) / ∑(imvol * imprc) - mid), 2)
    其中:
    imvol = 1 / Vol_i
    imprc = |Price_i / mid - 1|
    """
    i1 = 1
    i2 = 1
    imsum = pd.Series(0, index=data.index)
    im5 = pd.Series(0, index=data.index)

    for j in ['Bid', 'Ask']:
        for i in range(1, 6):
            imvol = 1 / data[f'{j}Vol{i}'] ** i1
            imprc = abs(data[f'{j}Price{i}'] / data['mid'] - 1) ** i2
            imsum += imvol * imprc
            im5 += data[f'{j}Price{i}'] * imvol * imprc

    return np.round((im5 / imsum - data['mid']), 2)


factor_manager.register_factor(Factor(
    name="im5",
    category=FactorCategory.DIY,
    description="5档买卖失衡指标，考虑价格和成交量的加权",
    calculation=calculate_im5,
    dependencies=[f"{side}Vol{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] +
                 [f"{side}Price{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] + ['mid'],
    source="li"
))


# 计算5档成交量失衡指标
def calculate_im5vol(data: pd.DataFrame) -> pd.Series:
    """计算5档成交量失衡指标
    公式: im5vol = (bid_sum - ask_sum) / (bid_sum + ask_sum)
    其中:
    bid_sum = ∑(BidVol_i / |BidPrice_i/mid - 1|^i2)
    ask_sum = ∑(AskVol_i / |AskPrice_i/mid - 1|^i2)
    i2 = 1
    """
    i2 = 1
    bid_sum = pd.Series(0, index=data.index)
    ask_sum = pd.Series(0, index=data.index)

    for i in range(1, 6):
        bid_sum += data[f'BidVol{i}'] / (abs(data[f'BidPrice{i}'] / data['mid'] - 1) ** i2)
        ask_sum += data[f'AskVol{i}'] / (abs(data[f'AskPrice{i}'] / data['mid'] - 1) ** i2)

    return np.round((bid_sum - ask_sum) / (bid_sum + ask_sum), 2)


factor_manager.register_factor(Factor(
    name="im5vol",
    category=FactorCategory.DIY,
    description="5档成交量失衡指标，考虑价格距离的加权",
    calculation=calculate_im5vol,
    dependencies=[f"{side}Vol{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] +
                 [f"{side}Price{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] + ['mid'],
    source="li"
))


# 计算买卖双方加权成交量压力差异
def calculate_weighted_vol_pressure(data: pd.DataFrame, side: str) -> pd.Series:
    """计算加权成交量压力 weighted standardized spread measure
    公式: WP = ∑(Vol_i * Price_i/mid) / ∑Vol_i
    """
    price_cols = [f'{side}Price{i}' for i in range(1, 6) if f'{side}Price{i}' in data.columns]
    vol_cols = [f'{side}Vol{i}' for i in range(1, 6) if f'{side}Vol{i}' in data.columns]

    if not price_cols or not vol_cols or len(price_cols) != len(vol_cols):
        return pd.Series(0, index=data.index)

    mid = data['mid_price']

    weighted_sum = pd.Series(0, index=data.index)
    total_vol = pd.Series(0, index=data.index)

    for i in range(min(len(price_cols), len(vol_cols))):
        price_ratio = data[price_cols[i]] / mid
        weighted_sum += data[vol_cols[i]] * price_ratio
        total_vol += data[vol_cols[i]]

    return weighted_sum / total_vol


factor_manager.register_factor(Factor(
    name="wvp",
    category=FactorCategory.BASIC,
    description="买卖双方加权成交量压力差异 (WVP = WP_bid - WP_ask)",
    calculation=lambda data: calculate_weighted_vol_pressure(data, 'Bid') - calculate_weighted_vol_pressure(data,
                                                                                                            'Ask'),
    dependencies=["bid_weighted_pressure", "ask_weighted_pressure"] +
                 [f"{side}Price{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] +
                 [f"{side}Vol{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] +
                 ["mid", "AskPrice1", "BidPrice1"],
    source="li"
))


# 计算最小成交量对应的价格和档位
def calculate_min_vol(data: pd.DataFrame, minvol: float) -> pd.DataFrame:
    """计算最小成交量对应的价格和档位
    公式: 对每个方向（买/卖）累计成交量，找到第一个累计量超过minvol的档位
    """
    result = pd.DataFrame(index=data.index, columns=['Bid_min_prc', 'Ask_min_prc', 'Bid_min_level', 'Ask_min_level'])

    for j in ['Bid', 'Ask']:
        vol_cols = [f'{j}Vol{i}' for i in range(1, 6) if f'{j}Vol{i}' in data.columns]

        cumsum = pd.DataFrame({f'vol_{i}': data[vol_cols[i - 1]] for i in range(1, len(vol_cols) + 1)}).cumsum(axis=1)
        # 找到第一个累计量超过minvol的列索引
        min_level = (cumsum > minvol).idxmax(axis=1).apply(lambda x: int(x.split('_')[1]) if pd.notnull(x) else 5)
        min_prc = pd.Series(index=data.index)

        for i in range(1, 6):
            mask = (min_level == i)
            min_prc[mask] = data[f'{j}Price{i}'][mask]

        result[f'{j}_min_prc'] = min_prc
        result[f'{j}_min_level'] = min_level

    return result


def calculate_mid_minnum(data: pd.DataFrame, minvol: float = 5) -> pd.Series:
    """计算基于最小成交量的中间价格偏离
    公式: mid_minnum = (Bid_min_prc/2 + Ask_min_prc/2) - mid
    其中Bid_min_prc和Ask_min_prc是累计成交量达到minvol的价格
    """
    min_vol_data = calculate_min_vol(data, minvol)
    return np.round(
        (min_vol_data['Bid_min_prc'] / 2 + min_vol_data['Ask_min_prc'] / 2 - data['mid']), 2
    )


factor_manager.register_factor(Factor(
    name="mid_minnum",
    category=FactorCategory.DIY,
    description="基于最小成交量的中间价格偏离",
    calculation=lambda data: calculate_mid_minnum(data, 5),
    dependencies=[f"{side}Vol{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] +
                 [f"{side}Price{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] +
                 ['mid', 'AskPrice1', 'BidPrice1'],
    parameters={"minvol": 5},
    source="li"
))


# 计算成交均价与中间价格的偏离
def calculate_turnover_mid(data: pd.DataFrame) -> pd.Series:
    """计算成交均价与中间价格的偏离
    公式: turnover_mid = avg_prc - (mid_{t-1} + mid_t)/2
    """
    temp=np.round(
        (data['avg_prc'] - (data['mid'].shift() + data['mid']) / 2), 2
    )
    return np.where((data['avg_prc'] == 0), 0, temp)


factor_manager.register_factor(Factor(
    name="turnover_mid",
    category=FactorCategory.DIY,
    description="成交均价与中间价格的偏离",
    calculation=calculate_turnover_mid,
    dependencies=['avg_prc', 'mid'],
    source="li"
))


# 计算订单失衡量指标
def calculate_voi(data: pd.DataFrame, level: int = 5) -> pd.Series:
    """计算订单失衡量指标
    公式: voi = ∑(bid_increment - ask_increment)/(AskPrice_i - BidPrice_i) / ∑(1/(AskPrice_i - BidPrice_i))
    其中:
    bid_increment = BidVol_i 如果BidPrice_i上升
                  = 0 如果BidPrice_i下降
                  = BidVol_i的变化量 如果BidPrice_i不变
    ask_increment = AskVol_i 如果AskPrice_i下降
                  = 0 如果AskPrice_i上升
                  = AskVol_i的变化量 如果AskPrice_i不变
    """
    voi = pd.Series(0, index=data.index)
    sumedge = 0

    for i in range(1, level + 1):
        bid_price_diff = data[f'BidPrice{i}'].diff(1)
        bvol_diff = data[f'BidVol{i}'].diff(1)
        bid_increment = np.where(bid_price_diff > 0, data[f'BidVol{i}'],
                                 np.where(bid_price_diff < 0, 0, bvol_diff))

        ask_price_diff = data[f'AskPrice{i}'].diff(1)
        avol_diff = data[f'AskVol{i}'].diff(1)
        ask_increment = np.where(ask_price_diff < 0, data[f'AskVol{i}'],
                                 np.where(ask_price_diff > 0, 0, avol_diff))

        increment = pd.Series(bid_increment - ask_increment, index=data.index)
        increment.loc[increment.groupby(increment.index.date).apply(lambda x: x.index[0])] = np.nan

        voi += increment / (data[f'AskPrice{i}'] - data[f'BidPrice{i}'])
        sumedge += 1 / (data[f'AskPrice{i}'] - data[f'BidPrice{i}'])

    return np.round(voi / sumedge, 2)


factor_manager.register_factor(Factor(
    name="voi",
    category=FactorCategory.DIY,
    description="订单失衡量指标，参考<Order imbalance Based Strategy in High Frequency Trading>",
    calculation=lambda data: calculate_voi(data, 5),
    dependencies=[f"{side}Vol{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)] +
                 [f"{side}Price{i}" for side in ['Bid', 'Ask'] for i in range(1, 6)],
    parameters={"level": 5},
    source="li"
))