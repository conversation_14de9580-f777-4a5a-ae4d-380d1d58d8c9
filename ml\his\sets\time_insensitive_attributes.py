#!/usr/bin/env python
# -*- coding: utf-8 -*-

import numpy as np
from typing import Dict, List, Optional, Tuple, Callable, Any, Union
from dataclasses import dataclass


@dataclass
class Cell:
    """模拟Scala中的Cell类型，用于处理可能缺失的值"""
    value: Optional[Any] = None
    is_na: bool = False

    @classmethod
    def from_value(cls, value):
        return cls(value=value, is_na=False)

    @classmethod
    def na(cls):
        return cls(value=None, is_na=True)
    
    @property
    def is_value(self):
        return not self.is_na
    
    def get(self):
        if self.is_na:
            raise ValueError("Trying to get value from NA")
        return self.value
    
    def map(self, f):
        if self.is_na:
            return Cell.na()
        return Cell.from_value(f(self.value))


class OrderBook:
    """订单簿数据结构"""
    def __init__(self, ask_prices: Dict[int, int], bid_prices: Dict[int, int],
                 ask_volumes: Dict[int, int], bid_volumes: Dict[int, int]):
        """
        初始化一个订单簿
        
        Args:
            ask_prices: 卖出价格，键为档位，值为价格
            bid_prices: 买入价格，键为档位，值为价格
            ask_volumes: 卖出量，键为档位，值为成交量
            bid_volumes: 买入量，键为档位，值为成交量
        """
        self.ask_prices = ask_prices
        self.bid_prices = bid_prices
        self.ask_volumes = ask_volumes
        self.bid_volumes = bid_volumes


class BasicSet:
    """基础特征集合"""
    def __init__(self, order_book_depth: int = 10):
        """
        初始化基础特征集
        
        Args:
            order_book_depth: 订单簿深度，默认为10
        """
        self.order_book_depth = order_book_depth
    
    def check_level(self, i: int) -> None:
        """
        检查订单簿深度级别是否有效
        
        Args:
            i: 订单簿深度级别
        
        Raises:
            ValueError: 如果级别无效
        """
        if i <= 0:
            raise ValueError(f"Level index should be greater than 0")
        if i > self.order_book_depth:
            raise ValueError(f"Level index should be less than or equal to {self.order_book_depth}")
    
    def ask_price(self, order_book: OrderBook) -> Callable[[int], Cell]:
        """获取特定深度的卖出价格"""
        def _get_ask_price(i: int) -> Cell:
            return Cell.from_value(order_book.ask_prices.get(i)) if i in order_book.ask_prices else Cell.na()
        return _get_ask_price
    
    def bid_price(self, order_book: OrderBook) -> Callable[[int], Cell]:
        """获取特定深度的买入价格"""
        def _get_bid_price(i: int) -> Cell:
            return Cell.from_value(order_book.bid_prices.get(i)) if i in order_book.bid_prices else Cell.na()
        return _get_bid_price
    
    def ask_volume(self, order_book: OrderBook) -> Callable[[int], Cell]:
        """获取特定深度的卖出量"""
        def _get_ask_volume(i: int) -> Cell:
            return Cell.from_value(order_book.ask_volumes.get(i)) if i in order_book.ask_volumes else Cell.na()
        return _get_ask_volume
    
    def bid_volume(self, order_book: OrderBook) -> Callable[[int], Cell]:
        """获取特定深度的买入量"""
        def _get_bid_volume(i: int) -> Cell:
            return Cell.from_value(order_book.bid_volumes.get(i)) if i in order_book.bid_volumes else Cell.na()
        return _get_bid_volume


class TimeInsensitiveSet:
    """时间无关特征集合"""
    def __init__(self, order_book_depth: int = 10):
        """
        初始化时间无关特征集
        
        Args:
            order_book_depth: 订单簿深度，默认为10
        """
        self.basic_set = BasicSet(order_book_depth)
    
    def _zip_map(self, cell1: Cell, cell2: Cell, func: Callable) -> Cell:
        """合并两个Cell并应用函数"""
        if cell1.is_na or cell2.is_na:
            return Cell.na()
        # 将两个Cell对象中的值提取出来，应用func函数，然后将结果包装成新的Cell对象返回
        # cell1.get()和cell2.get()分别获取两个Cell的实际值，func是传入的处理函数
        # 例如：当计算买卖差价时，func可能是lambda lv, rv: lv - rv
        return Cell.from_value(func(cell1.get(), cell2.get()))
    
    def spread(self, order_book: OrderBook, i: int, left_extractor, right_extractor) -> Cell:
        """
        计算买卖差价
        
        Args:
            order_book: 订单簿
            i: 深度级别
            left_extractor: 左侧数据提取器（通常是ask）
            right_extractor: 右侧数据提取器（通常是bid）
            
        Returns:
            Cell: 包含差价的Cell
        """
        left_cell = left_extractor(order_book)(i)
        right_cell = right_extractor(order_book)(i)
        return self._zip_map(left_cell, right_cell, lambda lv, rv: lv - rv)
    
    def mean(self, order_book: OrderBook, extractor) -> Cell:
        """
        计算均值
        
        Args:
            order_book: 订单簿
            extractor: 数据提取器函数
            
        Returns:
            Cell: 包含均值的Cell
        """
        defined_values = []
        
        for i in range(1, self.basic_set.order_book_depth + 1):
            cell = extractor(order_book)(i)
            if not cell.is_value:
                break
            defined_values.append(cell.get())
        
        if not defined_values:
            return Cell.na()
        
        return Cell.from_value(sum(defined_values) / len(defined_values))
    
    def acc(self, order_book: OrderBook, ask_extractor, bid_extractor) -> Cell:
        """
        计算累计值
        
        Args:
            order_book: 订单簿
            ask_extractor: 卖出数据提取器
            bid_extractor: 买入数据提取器
            
        Returns:
            Cell: 包含累计值的Cell
        """
        spreads = []
        
        for i in range(1, self.basic_set.order_book_depth + 1):
            ask_cell = ask_extractor(order_book)(i)
            bid_cell = bid_extractor(order_book)(i)
            spread_cell = self._zip_map(ask_cell, bid_cell, lambda a, b: a - b)
            
            if not spread_cell.is_value:
                break
                
            spreads.append(spread_cell.get())
        
        if not spreads:
            return Cell.na()
            
        return Cell.from_value(sum(spreads))
    
    def price_spread(self, order_book: OrderBook, i: int) -> Cell:
        """计算特定深度的买卖价差"""
        self.basic_set.check_level(i)
        return self.spread(
            order_book, 
            i, 
            self.basic_set.ask_price, 
            self.basic_set.bid_price
        )
    
    def volume_spread(self, order_book: OrderBook, i: int) -> Cell:
        """计算特定深度的买卖量差异"""
        self.basic_set.check_level(i)
        return self.spread(
            order_book, 
            i, 
            self.basic_set.ask_volume, 
            self.basic_set.bid_volume
        )
    
    def mid_price(self, order_book: OrderBook, i: int) -> Cell:
        """计算中间价格"""
        self.basic_set.check_level(i)
        ask_cell = self.basic_set.ask_price(order_book)(i)
        bid_cell = self.basic_set.bid_price(order_book)(i)
        return self._zip_map(ask_cell, bid_cell, lambda ask, bid: (ask + bid) // 2)
    
    def bid_step(self, order_book: OrderBook, i: int) -> Cell:
        """计算买入价格相邻档位间的步长"""
        self.basic_set.check_level(i)
        bid1_cell = self.basic_set.bid_price(order_book)(i)
        bid2_cell = self.basic_set.bid_price(order_book)(i+1)
        return self._zip_map(bid1_cell, bid2_cell, lambda bid1, bid2: abs(bid1 - bid2))
    
    def ask_step(self, order_book: OrderBook, i: int) -> Cell:
        """计算卖出价格相邻档位间的步长"""
        self.basic_set.check_level(i)
        ask1_cell = self.basic_set.ask_price(order_book)(i)
        ask2_cell = self.basic_set.ask_price(order_book)(i+1)
        return self._zip_map(ask1_cell, ask2_cell, lambda ask1, ask2: abs(ask1 - ask2))
    
    def mean_ask(self, order_book: OrderBook) -> Cell:
        """计算平均卖出价格"""
        return self.mean(order_book, self.basic_set.ask_price)
    
    def mean_bid(self, order_book: OrderBook) -> Cell:
        """计算平均买入价格"""
        return self.mean(order_book, self.basic_set.bid_price)
    
    def mean_ask_volume(self, order_book: OrderBook) -> Cell:
        """计算平均卖出量"""
        return self.mean(order_book, self.basic_set.ask_volume)
    
    def mean_bid_volume(self, order_book: OrderBook) -> Cell:
        """计算平均买入量"""
        return self.mean(order_book, self.basic_set.bid_volume)
    
    def accumulated_price_spread(self, order_book: OrderBook) -> Cell:
        """计算累计价差"""
        return self.acc(
            order_book,
            self.basic_set.ask_price,
            self.basic_set.bid_price
        )
    
    def accumulated_volume_spread(self, order_book: OrderBook) -> Cell:
        """计算累计成交量差异"""
        return self.acc(
            order_book,
            self.basic_set.ask_volume,
            self.basic_set.bid_volume
        )
    
    def get_all_features(self, order_book: OrderBook, max_level: int = 5) -> Dict[str, float]:
        """
        计算并返回所有特征
        
        Args:
            order_book: 订单簿
            max_level: 最大深度级别，默认为5
            
        Returns:
            Dict[str, float]: 特征名称和值的字典
        """
        features = {}
        
        # 计算各深度级别的特征
        for i in range(1, min(max_level + 1, self.basic_set.order_book_depth + 1)):
            # 价格特征
            price_spread = self.price_spread(order_book, i)
            if price_spread.is_value:
                features[f"price_spread_{i}"] = price_spread.get()
                
            volume_spread = self.volume_spread(order_book, i)
            if volume_spread.is_value:
                features[f"volume_spread_{i}"] = volume_spread.get()
                
            mid_price = self.mid_price(order_book, i)
            if mid_price.is_value:
                features[f"mid_price_{i}"] = mid_price.get()
                
            if i < max_level:  # 步长特征需要i+1级别的数据
                bid_step = self.bid_step(order_book, i)
                if bid_step.is_value:
                    features[f"bid_step_{i}"] = bid_step.get()
                    
                ask_step = self.ask_step(order_book, i)
                if ask_step.is_value:
                    features[f"ask_step_{i}"] = ask_step.get()
        
        # 全局特征
        mean_ask = self.mean_ask(order_book)
        if mean_ask.is_value:
            features["mean_ask"] = mean_ask.get()
            
        mean_bid = self.mean_bid(order_book)
        if mean_bid.is_value:
            features["mean_bid"] = mean_bid.get()
            
        mean_ask_volume = self.mean_ask_volume(order_book)
        if mean_ask_volume.is_value:
            features["mean_ask_volume"] = mean_ask_volume.get()
            
        mean_bid_volume = self.mean_bid_volume(order_book)
        if mean_bid_volume.is_value:
            features["mean_bid_volume"] = mean_bid_volume.get()
            
        acc_price_spread = self.accumulated_price_spread(order_book)
        if acc_price_spread.is_value:
            features["accumulated_price_spread"] = acc_price_spread.get()
            
        acc_volume_spread = self.accumulated_volume_spread(order_book)
        if acc_volume_spread.is_value:
            features["accumulated_volume_spread"] = acc_volume_spread.get()
            
        return features


# 示例用法
def example_usage():
    # 创建一个示例订单簿
    ob = OrderBook(
        ask_prices={1: 105, 2: 106, 3: 107, 4: 108, 5: 109},
        bid_prices={1: 104, 2: 103, 3: 102, 4: 101, 5: 100},
        ask_volumes={1: 100, 2: 200, 3: 300, 4: 400, 5: 500},
        bid_volumes={1: 150, 2: 250, 3: 350, 4: 450, 5: 550}
    )
    
    # 创建时间无关特征集
    ti_set = TimeInsensitiveSet(order_book_depth=5)
    
    # 计算各种特征
    features = {
        "price_spread_1": ti_set.price_spread(ob, 1).get(),  # 1级买卖价差
        "volume_spread_1": ti_set.volume_spread(ob, 1).get(),  # 1级买卖量差异
        "mid_price_1": ti_set.mid_price(ob, 1).get(),  # 1级中间价格
        "bid_step_1": ti_set.bid_step(ob, 1).get(),  # 1级买入价格步长
        "ask_step_1": ti_set.ask_step(ob, 1).get(),  # 1级卖出价格步长
        "mean_ask": ti_set.mean_ask(ob).get(),  # 平均卖出价格
        "mean_bid": ti_set.mean_bid(ob).get(),  # 平均买入价格
        "mean_ask_volume": ti_set.mean_ask_volume(ob).get(),  # 平均卖出量
        "mean_bid_volume": ti_set.mean_bid_volume(ob).get(),  # 平均买入量
        "accumulated_price_spread": ti_set.accumulated_price_spread(ob).get(),  # 累计价差
        "accumulated_volume_spread": ti_set.accumulated_volume_spread(ob).get()  # 累计成交量差异
    }
    
    # 打印结果
    for name, value in features.items():
        print(f"{name}: {value}")
    
    # 使用简化方法获取所有特征
    print("\n使用get_all_features方法获取所有特征:")
    all_features = ti_set.get_all_features(ob)
    for name, value in all_features.items():
        print(f"{name}: {value}")


def visualize_orderbook(order_book: OrderBook, max_level: int = 5):
    """
    可视化订单簿数据
    
    Args:
        order_book: 订单簿对象
        max_level: 最大深度级别
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        
        # 设置全局字体
        plt.rcParams['font.family'] = 'sans-serif'
        
        # 准备数据
        levels = list(range(1, max_level + 1))
        ask_prices = [order_book.ask_prices.get(i, 0) for i in levels]
        bid_prices = [order_book.bid_prices.get(i, 0) for i in levels]
        ask_volumes = [order_book.ask_volumes.get(i, 0) for i in levels]
        bid_volumes = [order_book.bid_volumes.get(i, 0) for i in levels]
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 价格图 - 使用英文标题
        ax1.set_title("Order Book Price Structure")
        ax1.plot(levels, ask_prices, 'r-o', label='Ask Price')
        ax1.plot(levels, bid_prices, 'g-o', label='Bid Price')
        ax1.set_xlabel('Level')
        ax1.set_ylabel('Price')
        ax1.grid(True)
        ax1.legend()
        
        # 成交量图 - 使用水平条形图
        ax2.set_title("Order Book Volume Distribution")
        ax2.barh(levels, [-v for v in bid_volumes], color='g', alpha=0.7, label='Bid Volume')
        ax2.barh(levels, ask_volumes, color='r', alpha=0.7, label='Ask Volume')
        ax2.set_xlabel('Volume')
        ax2.set_ylabel('Level')
        ax2.grid(True)
        ax2.legend()
        
        # 添加中间价格线
        ti_set = TimeInsensitiveSet(order_book_depth=max_level)
        mid_prices = []
        for i in levels:
            mid_price_cell = ti_set.mid_price(order_book, i)
            if mid_price_cell.is_value:
                mid_prices.append(mid_price_cell.get())
            else:
                mid_prices.append(None)
        
        # 只绘制有效的中间价格
        valid_levels = [levels[i] for i in range(len(mid_prices)) if mid_prices[i] is not None]
        valid_mid_prices = [p for p in mid_prices if p is not None]
        if valid_mid_prices:
            ax1.plot(valid_levels, valid_mid_prices, 'b--', label='Mid Price')
            ax1.legend()
        
        plt.tight_layout()
        plt.savefig('orderbook_visualization.png')
        print("订单簿可视化已保存为 'orderbook_visualization.png'")
        plt.close()
        
    except ImportError:
        print("要使用可视化功能，请安装matplotlib: pip install matplotlib")


def create_multi_level_example():
    """创建一个更复杂的多层次订单簿示例"""
    # 模拟股票市场中的订单簿 - 价格跨度更大
    ask_prices = {i: 1000 + i * 5 for i in range(1, 11)}  # 卖出价格从1005开始，间隔5
    bid_prices = {i: 1000 - i * 5 for i in range(1, 11)}  # 买入价格从995开始，间隔5
    
    # 成交量分布 - 通常靠近中间价格的成交量较大
    ask_volumes = {i: int(1000 / i) for i in range(1, 11)}  # 卖出量递减
    bid_volumes = {i: int(1000 / i) for i in range(1, 11)}  # 买入量递减
    
    return OrderBook(ask_prices, bid_prices, ask_volumes, bid_volumes)


def visualize_features(order_book: OrderBook):
    """
    可视化订单簿的关键特征
    
    Args:
        order_book: 订单簿对象
    """
    try:
        import matplotlib.pyplot as plt
        
        # 设置全局字体
        plt.rcParams['font.family'] = 'sans-serif'
        
        # 计算特征
        ti_set = TimeInsensitiveSet(order_book_depth=10)
        features = ti_set.get_all_features(order_book, max_level=5)
        
        # 提取数据
        price_spreads = [features.get(f"price_spread_{i}", 0) for i in range(1, 6)]
        mid_prices = [features.get(f"mid_price_{i}", 0) for i in range(1, 6)]
        bid_steps = [features.get(f"bid_step_{i}", 0) for i in range(1, 5)]
        ask_steps = [features.get(f"ask_step_{i}", 0) for i in range(1, 5)]
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        
        # 价格差异图
        levels = list(range(1, 6))
        ax1.set_title("Price Spread by Level")
        ax1.bar(levels, price_spreads, color='purple')
        ax1.set_xlabel('Level')
        ax1.set_ylabel('Price Spread')
        ax1.grid(True, axis='y')
        
        # 中间价格图
        ax2.set_title("Mid Price by Level")
        ax2.plot(levels, mid_prices, 'b-o')
        ax2.set_xlabel('Level')
        ax2.set_ylabel('Mid Price')
        ax2.grid(True)
        
        # 价格步长图
        step_levels = list(range(1, 5))
        ax3.set_title("Price Step Comparison")
        ax3.plot(step_levels, bid_steps, 'g-o', label='Bid Step')
        ax3.plot(step_levels, ask_steps, 'r-o', label='Ask Step')
        ax3.set_xlabel('Level')
        ax3.set_ylabel('Price Step')
        ax3.grid(True)
        ax3.legend()
        
        # 均值和累计值
        ax4.set_title("Global Features")
        global_features = [
            features.get("mean_ask", 0),
            features.get("mean_bid", 0),
            features.get("accumulated_price_spread", 0) / 10  # 缩放以适应图表
        ]
        feature_names = ['Mean Ask', 'Mean Bid', 'Acc Price Spread/10']
        ax4.bar(feature_names, global_features, color=['red', 'green', 'blue'])
        ax4.set_ylabel('Value')
        ax4.grid(True, axis='y')
        
        # 添加文本说明
        plt.figtext(0.5, 0.01, f"Accumulated Price Spread: {features.get('accumulated_price_spread', 0)}, "
                             f"Accumulated Volume Spread: {features.get('accumulated_volume_spread', 0)}", 
                   ha='center', fontsize=12, bbox={'facecolor': 'lightgray', 'alpha': 0.5, 'pad': 5})
        
        plt.tight_layout()
        plt.savefig('orderbook_features.png')
        print("特征可视化已保存为 'orderbook_features.png'")
        plt.close()
        
    except ImportError:
        print("要使用可视化功能，请安装matplotlib: pip install matplotlib")


if __name__ == "__main__":
    # 运行基本示例
    print("基本示例结果:")
    example_usage()
    
    # 创建更复杂的订单簿示例
    print("\n创建更复杂的订单簿示例...")
    complex_ob = create_multi_level_example()
    
    # 可视化订单簿
    print("\n生成订单簿可视化...")
    visualize_orderbook(complex_ob)
    
    # 可视化特征
    print("\n生成特征可视化...")
    visualize_features(complex_ob) 