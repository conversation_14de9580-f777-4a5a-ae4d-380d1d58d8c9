# -*- coding: utf-8 -*-
"""
Created on Fri Aug  6 14:51:09 2021

@author: yhw52174
"""

from influxdb import InfluxDBClient
from OmmDatabase import OmmDatabase
import datetime
import time
import numpy as np
import pandas as pd
from datetime import datetime

#%%
client = InfluxDBClient('localhost',8086,'','','test')

#%%
def var_statistics(data, l = 30, tick = 0.5): # l 为向前回溯切片数
    d = []
    n = len(data)
    
    for i in range(n-l, n):
        d.append(datetime.strptime(data[i]['time'], '%Y-%m-%dT%H:%M:%S.%fZ').timestamp())
    dt = []
    for i in range(len(d)):
        if i >= 1:
            delta_t = d[i] - d[j]
            adjusted_delta_t = delta_t/round(delta_t/tick, 0) if round(delta_t/tick) > 0 else 0
            if adjusted_delta_t > 0:
                dt.append(adjusted_delta_t)        
        j = i
    return np.mean(dt), np.std(dt)


        


#%%

my_data = []
tick = 0.5
l = 15
dt = []
insid_md = 'hc2111'

t_l = int(time.time()*10**9)

while True:    
    
    t = time.time()
    t = int(t*10**9)
    result = client.query("select * from testHC where insid_md='hc2111' and (time>{} and time<={})".format(t_l, t)) 
    
    is_change = 0 
    points = result.get_points()                                 
    for d in points:
        my_data.append(d)
        is_change = 1
    
    
    if len(my_data) >= l and is_change == 1:
        # if len(my_data) >= 60:
        #     my_data = my_data[-60:]
        
        mean, var = var_statistics(my_data, l=l, tick = 0.5)
        dt.append([t, mean, var])
        json_body = []
        current_time = t
        
        measurement = 'HC_delay'        
        body = {
                "measurement": measurement, 
                "time": current_time, 
                "tags": {
                    "insid_md": insid_md
                }, 
                "fields": {
                    "mean": mean,
                    "var": var
                }, 
            }        
    
        json_body.append(body)
        res = client.write_points(json_body, batch_size = 10000)
    
    if t > 1628559600*10**9:
        break
    time.sleep(0.1)
    t_l = t

My_data = pd.DataFrame(my_data[:])
Dt = pd.DataFrame(dt)

#%%


#%%
a = My_data['time'].iloc[0]
dt = datetime.strptime(a, '%Y-%m-%dT%H:%M:%S.%fZ')
t = time.mktime(dt.timetuple())

#%%
# a = [{'time': 0.5*i}for i in range(30)] + [{'time': 15+0.9*i}for i in range(30)]
# var_statistics(a, l = 30, tick = 0.5)

# result = client.query("select * from testHC where insid_md='hc2111' and time>1628479100000000000") 








