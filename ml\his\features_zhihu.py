import os
import sys
import re  # 添加re模块导入
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb

"""
实现波动率相关特征：
    实现绝对变异(realized absolute variation)
    实现双幂变异(realized bipower variation)和跳跃过程
    实现偏度(realized skew)
    实现峰度(realized kurtosis)
    实现四次方(integrated quarticity)
订单簿微观结构特征：
    订单簿深度(order book depth)
    订单簿斜率(order book slope)
    订单簿离散度(order book dispersion)
    价格冲击(price impact)
    订单流量不平衡(order flow imbalance)
    加权标准化价差(weighted standardized spread)
    总成交量(total turnover)
    WAP变体(variations of weighted average price)
这些特征对于高频交易和市场微观结构分析非常有价值，可以捕捉市场的流动性、波动性和价格形成过程的细微变化。
https://journals.plos.org/plosone/article/figure?id=10.1371/journal.pone.0234107.t003
"""


class OrderBookFeatureGenerator:
    """订单簿特征生成器"""
    
    def __init__(self, data):
        self.data = data
    
    def generate_basic_features(self):
        """生成基本订单簿特征"""
        df = self.data.copy()
        
        # 价差特征
        df['spread'] = df['AskPrice1'] - df['BidPrice1']
        df['spread_ratio'] = df['spread'] / df['mid']
        
        # 订单簿不平衡特征
        df['imbalance_1'] = (df['BidVol1'] - df['AskVol1']) / (df['BidVol1'] + df['AskVol1'])
        if 'BidVol2' in df.columns and 'AskVol2' in df.columns:
            df['imbalance_2'] = (df['BidVol2'] - df['AskVol2']) / (df['BidVol2'] + df['AskVol2'])
            df['total_bid_vol'] = df['BidVol1'] + df['BidVol2']
            df['total_ask_vol'] = df['AskVol1'] + df['AskVol2']
            df['vol_imbalance'] = (df['total_bid_vol'] - df['total_ask_vol']) / (df['total_bid_vol'] + df['total_ask_vol'])
        
        # 价格压力特征
        if 'BidPrice2' in df.columns and 'AskPrice2' in df.columns:
            df['bid_price_diff'] = df['BidPrice1'] - df['BidPrice2']
            df['ask_price_diff'] = df['AskPrice2'] - df['AskPrice1']
            df['price_pressure'] = df['ask_price_diff'] - df['bid_price_diff']
        
        return df
    
    def generate_time_series_features(self, windows=[5, 10, 20]):
        """生成时间序列特征"""
        df = self.data.copy()
        
        # 价格动量特征
        for window in windows:
            df[f'mid_return_{window}'] = df['mid'].pct_change(window)
            df[f'mid_ma_{window}'] = df['mid'].rolling(window=window).mean()
            df[f'mid_std_{window}'] = df['mid'].rolling(window=window).std()
            
            # 交易量特征
            df[f'vol_ma_{window}'] = df['tradedVol'].rolling(window=window).mean()
            df[f'vol_std_{window}'] = df['tradedVol'].rolling(window=window).std()
            
            # 订单簿不平衡滚动特征
            if 'imbalance_1' in df.columns:
                df[f'imbalance_ma_{window}'] = df['imbalance_1'].rolling(window=window).mean()
        
        return df
    
    def generate_advanced_features(self):
        """生成高级订单簿特征"""
        df = self.data.copy()
        
        # 订单流量失衡(OFI)
        if 'BidVol1' in df.columns and 'AskVol1' in df.columns:
            df['bid_vol_change'] = df['BidVol1'].diff()
            df['ask_vol_change'] = df['AskVol1'].diff()
            df['OFI'] = df['bid_vol_change'] - df['ask_vol_change']
            df['OFI_ma_5'] = df['OFI'].rolling(window=5).mean()
        
        # 交易量强度指标
        df['volume_intensity'] = df['tradedVol'] / df['tradedVol'].rolling(window=20).mean()
        
        # 价格波动率
        df['volatility_5'] = df['mid'].pct_change().rolling(window=5).std()
        df['volatility_10'] = df['mid'].pct_change().rolling(window=10).std()
        
        return df
    
    def realized_absvar(self, series):
        """计算实现绝对变异(Realized Absolute Variation)
        
        该指标用于衡量价格变动的绝对幅度，对异常值不敏感。
        
        公式: RAV = sqrt(π/(2*n)) * Σ|r_t|
        
        其中:
        - n: 样本数量
        - r_t: t时刻的收益率
        - Σ|r_t|: 收益率绝对值的总和
        
        参考文献: Andersen, T. G., & Bollerslev, T. (1998). Answering the skeptics: 
        Yes, standard volatility models do provide accurate forecasts.
        """
        return np.sqrt(np.pi/(2*series.count()))*np.sum(np.abs(series))
    
    def realized_bipowvar(self, series):
        """计算实现双幂变异(Realized Bipower Variation)
        
        该指标对价格跳跃不敏感，可以捕捉连续价格变动的波动性。
        
        公式: RBV = (π/2)*(n/(n-2))*Σ|r_t|*|r_{t-1}|
        
        其中:
        - n: 样本数量
        - r_t: t时刻的收益率
        - Σ|r_t|*|r_{t-1}|: 相邻收益率绝对值乘积的总和
        
        参考文献: Barndorff-Nielsen, O. E., & Shephard, N. (2004). Power and bipower 
        variation with stochastic volatility and jumps.
        """
        cnt = series.count()
        if cnt < 3:
            return np.nan
        else:
            cons = (np.pi/2)*(cnt/(cnt-2))
            return cons*np.nansum(np.abs(series)*np.abs(series.shift()))
    
    def realized_skew(self, series):
        """计算实现偏度(Realized Skewness)
        
        该指标衡量收益率分布的不对称性，正值表示右偏，负值表示左偏。
        
        公式: RS = (√n*Σr_t^3)/(RV^(3/2))
        
        其中:
        - n: 样本数量
        - r_t: t时刻的收益率
        - RV: 实现波动率
        - Σr_t^3: 收益率三次方的总和
        
        参考文献: Amaya, D., Christoffersen, P., Jacobs, K., & Vasquez, A. (2015). 
        Does realized skewness predict the cross-section of equity returns?
        """
        return np.sqrt(series.count())*np.sum(series**3)/(self.realized_volatility(series)**3)
    
    def realized_kurtosis(self, series):
        """计算实现峰度(Realized Kurtosis)
        
        该指标衡量收益率分布的尖峰或平坦程度，高值表示分布有较厚的尾部。
        
        公式: RK = (n*Σr_t^4)/(RV^2)
        
        其中:
        - n: 样本数量
        - r_t: t时刻的收益率
        - RV: 实现波动率
        - Σr_t^4: 收益率四次方的总和
        
        参考文献: Amaya, D., Christoffersen, P., Jacobs, K., & Vasquez, A. (2015).
        Does realized skewness predict the cross-section of equity returns?
        """
        return series.count()*np.sum(series**4)/(self.realized_volatility(series)**4)
    
    def realized_quarticity(self, series):
        """计算实现四次方(Realized Quarticity)
        
        该指标用于估计实现波动率的标准误差，是波动率的波动率度量。
        
        公式: RQ = (n/3)*Σr_t^4
        
        其中:
        - n: 样本数量
        - r_t: t时刻的收益率
        - Σr_t^4: 收益率四次方的总和
        
        参考文献: Barndorff-Nielsen, O. E., & Shephard, N. (2002). Econometric analysis 
        of realized volatility and its use in estimating stochastic volatility models.
        """
        return (series.count()/3)*np.sum(series**4)
    
    def realized_volatility(self, series):
        """计算实现波动率(Realized Volatility)
        
        该指标是高频数据中最基本的波动率估计量，衡量价格变动的总体波动性。
        
        公式: RV = √(Σr_t^2)
        
        其中:
        - r_t: t时刻的收益率
        - Σr_t^2: 收益率平方的总和
        
        参考文献: Andersen, T. G., Bollerslev, T., Diebold, F. X., & Labys, P. (2003).
        Modeling and forecasting realized volatility.
        """
        return np.sqrt(np.sum(series**2))
    
    def generate_realized_features(self, windows=[5, 10, 20]):
        """生成实现波动率特征"""
        df = self.data.copy()
        
        # 计算对数收益率
        df['log_return'] = np.log(df['mid'] / df['mid'].shift(1))
        
        for window in windows:
            # 使用滚动窗口计算实现波动率特征
            returns = df['log_return'].rolling(window=window)
            
            # 实现波动率
            df[f'realized_volatility_{window}'] = returns.apply(self.realized_volatility)
            
            # 实现绝对变异
            df[f'realized_absvar_{window}'] = returns.apply(self.realized_absvar)
            
            # 实现双幂变异
            df[f'realized_bipowvar_{window}'] = returns.apply(self.realized_bipowvar)
            
            # 计算跳跃过程
            df[f'BPV_jump_{window}'] = df[f'realized_volatility_{window}'] - df[f'realized_bipowvar_{window}']
            df.loc[df[f'BPV_jump_{window}'] < 0, f'BPV_jump_{window}'] = 0
            
            # 实现偏度
            df[f'realized_skew_{window}'] = returns.apply(self.realized_skew)
            
            # 实现峰度
            df[f'realized_kurtosis_{window}'] = returns.apply(self.realized_kurtosis)
            
            # 实现四次方
            df[f'realized_quarticity_{window}'] = returns.apply(self.realized_quarticity)
        
        return df
    
    def generate_orderbook_microstructure(self):
        """生成订单簿微观结构特征"""
        df = self.data.copy()
        
        # 重命名列以匹配函数中使用的列名
        col_mapping = {
            'AskPrice1': 'ask_price1', 'BidPrice1': 'bid_price1',
            'AskVol1': 'ask_size1', 'BidVol1': 'bid_size1',
            'AskPrice2': 'ask_price2', 'BidPrice2': 'bid_price2',
            'AskVol2': 'ask_size2', 'BidVol2': 'bid_size2',
            'mid': 'midprice'
        }
        
        # 检查必要的列是否存在
        required_cols = ['AskPrice1', 'BidPrice1', 'AskVol1', 'BidVol1']
        if not all(col in df.columns for col in required_cols):
            print("警告：缺少计算订单簿微观结构特征所需的列")
            return df
        
        # 创建临时数据框用于计算
        temp_df = df.copy()
        for old_col, new_col in col_mapping.items():
            if old_col in temp_df.columns:
                temp_df[new_col] = temp_df[old_col]
        
        if 'mid' in temp_df.columns and 'midprice' not in temp_df.columns:
            temp_df['midprice'] = temp_df['mid']
        
        # 检查是否有足够的列计算高级特征
        if all(col in temp_df.columns for col in ['ask_price1', 'bid_price1', 'ask_size1', 'bid_size1']):
            # 计算订单簿深度
            if all(col in temp_df.columns for col in ['ask_price2', 'bid_price2', 'ask_size2', 'bid_size2']):
                temp_df['depth'] = temp_df['bid_price1'] * temp_df['bid_size1'] + temp_df['ask_price1'] * temp_df['ask_size1'] + \
                                  temp_df['bid_price2'] * temp_df['bid_size2'] + temp_df['ask_price2'] * temp_df['ask_size2']
                df['order_book_depth'] = temp_df['depth']
            
            # 计算订单簿斜率
            if all(col in temp_df.columns for col in ['ask_price2', 'bid_price2', 'ask_size2', 'bid_size2']):
                v0 = (temp_df['bid_size1'] + temp_df['ask_size1']) / 2
                p0 = (temp_df['bid_price1'] + temp_df['ask_price1']) / 2
                
                # 处理可能的零除错误
                with np.errstate(divide='ignore', invalid='ignore'):
                    slope_bid = ((temp_df['bid_size1'] / v0) - 1) / abs((temp_df['bid_price1'] / p0) - 1) + \
                               ((temp_df['bid_size2'] / temp_df['bid_size1']) - 1) / abs((temp_df['bid_price2'] / temp_df['bid_price1']) - 1)
                    slope_ask = ((temp_df['ask_size1'] / v0) - 1) / abs((temp_df['ask_price1'] / p0) - 1) + \
                               ((temp_df['ask_size2'] / temp_df['ask_size1']) - 1) / abs((temp_df['ask_price2'] / temp_df['ask_price1']) - 1)
                
                # 替换无穷大和NaN值
                slope_bid = np.nan_to_num(slope_bid, nan=0.0, posinf=0.0, neginf=0.0)
                slope_ask = np.nan_to_num(slope_ask, nan=0.0, posinf=0.0, neginf=0.0)
                
                df['order_book_slope_mean'] = (slope_bid + slope_ask) / 2
                df['order_book_slope_diff'] = abs(slope_bid - slope_ask)
            
            # 计算订单簿离散度
            if all(col in temp_df.columns for col in ['ask_price2', 'bid_price2', 'ask_size2', 'bid_size2']):
                bspread = temp_df['bid_price1'] - temp_df['bid_price2']
                aspread = temp_df['ask_price2'] - temp_df['ask_price1']
                bmid = (temp_df['bid_price1'] + temp_df['ask_price1']) / 2 - temp_df['bid_price1']
                bmid2 = (temp_df['bid_price1'] + temp_df['ask_price1']) / 2 - temp_df['bid_price2']
                amid = temp_df['ask_price1'] - (temp_df['bid_price1'] + temp_df['ask_price1']) / 2
                amid2 = temp_df['ask_price2'] - (temp_df['bid_price1'] + temp_df['ask_price1']) / 2
                
                # 处理可能的零除错误
                with np.errstate(divide='ignore', invalid='ignore'):
                    bdisp = (temp_df['bid_size1'] * bmid + temp_df['bid_size2'] * bspread) / (temp_df['bid_size1'] + temp_df['bid_size2'])
                    bdisp2 = (temp_df['bid_size1'] * bmid + temp_df['bid_size2'] * bmid2) / (temp_df['bid_size1'] + temp_df['bid_size2'])
                    adisp = (temp_df['ask_size1'] * amid + temp_df['ask_size2'] * aspread) / (temp_df['ask_size1'] + temp_df['ask_size2'])
                    adisp2 = (temp_df['ask_size1'] * amid + temp_df['ask_size2'] * amid2) / (temp_df['ask_size1'] + temp_df['ask_size2'])
                
                # 替换NaN值
                bdisp = np.nan_to_num(bdisp, nan=0.0)
                bdisp2 = np.nan_to_num(bdisp2, nan=0.0)
                adisp = np.nan_to_num(adisp, nan=0.0)
                adisp2 = np.nan_to_num(adisp2, nan=0.0)
                
                df['order_book_bspread'] = bspread
                df['order_book_aspread'] = aspread
                df['order_book_bmid'] = bmid
                df['order_book_amid'] = amid
                df['order_book_bdisp'] = bdisp
                df['order_book_adisp'] = adisp
                df['order_book_disp_mean'] = (bdisp + adisp) / 2
                df['order_book_disp2_mean'] = (bdisp2 + adisp2) / 2
            
            # 计算价格冲击
            if all(col in temp_df.columns for col in ['ask_price2', 'bid_price2', 'ask_size2', 'bid_size2']):
                with np.errstate(divide='ignore', invalid='ignore'):
                    ask = (temp_df['ask_price1'] * temp_df['ask_size1'] + temp_df['ask_price2'] * temp_df['ask_size2']) / (temp_df['ask_size1'] + temp_df['ask_size2'])
                    bid = (temp_df['bid_price1'] * temp_df['bid_size1'] + temp_df['bid_price2'] * temp_df['bid_size2']) / (temp_df['bid_size1'] + temp_df['bid_size2'])
                    ask_impact = (temp_df['ask_price1'] - ask) / temp_df['ask_price1']
                    bid_impact = (temp_df['bid_price1'] - bid) / temp_df['bid_price1']
                
                # 替换NaN值
                ask_impact = np.nan_to_num(ask_impact, nan=0.0)
                bid_impact = np.nan_to_num(bid_impact, nan=0.0)
                
                df['price_impact_ask'] = ask_impact
                df['price_impact_bid'] = bid_impact
            
            # 计算订单流量不平衡
            a = temp_df['bid_size1'] * np.where(temp_df['bid_price1'].diff() >= 0, 1, 0)
            b = temp_df['bid_size1'].shift() * np.where(temp_df['bid_price1'].diff() <= 0, 1, 0)
            c = temp_df['ask_size1'] * np.where(temp_df['ask_price1'].diff() <= 0, 1, 0)
            d = temp_df['ask_size1'].shift() * np.where(temp_df['ask_price1'].diff() >= 0, 1, 0)
            df['order_flow_imbalance'] = a - b - c + d
            
            # 计算加权标准化价差
            if all(col in temp_df.columns for col in ['ask_price2', 'bid_price2', 'ask_size2', 'bid_size2', 'midprice']):
                with np.errstate(divide='ignore', invalid='ignore'):
                    ask = (temp_df['ask_price1'] * temp_df['ask_size1'] + temp_df['ask_price2'] * temp_df['ask_size2']) / (temp_df['ask_size1'] + temp_df['ask_size2'])
                    bid = (temp_df['bid_price1'] * temp_df['bid_size1'] + temp_df['bid_price2'] * temp_df['bid_size2']) / (temp_df['bid_size1'] + temp_df['bid_size2'])
                    wss = (ask - bid) / temp_df['midprice']
                
                # 替换NaN值
                wss = np.nan_to_num(wss, nan=0.0)
                df['weighted_spread_standard'] = wss
            
            # 计算总成交量
            if all(col in temp_df.columns for col in ['ask_price2', 'bid_price2', 'ask_size2', 'bid_size2']):
                p1 = temp_df['ask_price1'] * temp_df['ask_size1'] + temp_df['bid_price1'] * temp_df['bid_size1']
                p2 = temp_df['ask_price2'] * temp_df['ask_size2'] + temp_df['bid_price2'] * temp_df['bid_size2']
                df['total_turnover'] = p2 - p1
            
            # 计算WAP变体
            if all(col in temp_df.columns for col in ['ask_size1', 'bid_size1']):
                # WAP1
                with np.errstate(divide='ignore', invalid='ignore'):
                    wap1 = (temp_df['bid_price1'] * temp_df['ask_size1'] + temp_df['ask_price1'] * temp_df['bid_size1']) / (temp_df['bid_size1'] + temp_df['ask_size1'])
                wap1 = np.nan_to_num(wap1, nan=0.0)
                df['wap1'] = wap1
                
                # WAP3
                with np.errstate(divide='ignore', invalid='ignore'):
                    wap3 = (temp_df['bid_price1'] * temp_df['bid_size1'] + temp_df['ask_price1'] * temp_df['ask_size1']) / (temp_df['bid_size1'] + temp_df['ask_size1'])
                wap3 = np.nan_to_num(wap3, nan=0.0)
                df['wap3'] = wap3
                
                # SWAP1
                df['swap1'] = df['wap1'] - df['wap3']
                
                # TSWAP1
                df['tswap1'] = -df['swap1'].diff()
            
            # 如果有二级价格和数量，计算更多WAP变体
            if all(col in temp_df.columns for col in ['ask_price2', 'bid_price2', 'ask_size2', 'bid_size2']):
                # WAP2
                with np.errstate(divide='ignore', invalid='ignore'):
                    wap2 = (temp_df['bid_price2'] * temp_df['ask_size2'] + temp_df['ask_price2'] * temp_df['bid_size2']) / (temp_df['bid_size2'] + temp_df['ask_size2'])
                wap2 = np.nan_to_num(wap2, nan=0.0)
                df['wap2'] = wap2
                
                # WAP12
                with np.errstate(divide='ignore', invalid='ignore'):
                    var1 = temp_df['bid_price1'] * temp_df['ask_size1'] + temp_df['ask_price1'] * temp_df['bid_size1']
                    var2 = temp_df['bid_price2'] * temp_df['ask_size2'] + temp_df['ask_price2'] * temp_df['bid_size2']
                    den = temp_df['bid_size1'] + temp_df['ask_size1'] + temp_df['bid_size2'] + temp_df['ask_size2']
                    wap12 = (var1 + var2) / den
                wap12 = np.nan_to_num(wap12, nan=0.0)
                df['wap12'] = wap12
                
                # WAP34
                with np.errstate(divide='ignore', invalid='ignore'):
                    var1 = temp_df['bid_price1'] * temp_df['bid_size1'] + temp_df['ask_price1'] * temp_df['ask_size1']
                    var2 = temp_df['bid_price2'] * temp_df['bid_size2'] + temp_df['ask_price2'] * temp_df['ask_size2']
                    den = temp_df['bid_size1'] + temp_df['ask_size1'] + temp_df['bid_size2'] + temp_df['ask_size2']
                    wap34 = (var1 + var2) / den
                wap34 = np.nan_to_num(wap34, nan=0.0)
                df['wap34'] = wap34
                
                # SWAP12
                df['swap12'] = df['wap12'] - df['wap34']
                
                # TSWAP12
                df['tswap12'] = -df['swap12'].diff()
        
        return df

