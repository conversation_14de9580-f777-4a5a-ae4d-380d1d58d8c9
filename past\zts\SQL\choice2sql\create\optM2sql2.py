# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-10-20
from WindPy import w
import pyodbc
from datetime import datetime
from datetime import timedelta
import pandas as pd

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
beginDate = datetime.strptime("2017-03-31 09:01:12", "%Y-%m-%d %H:%M:%S")
dt = datetime.strptime("2017-11-22 15:01:12", "%Y-%m-%d %H:%M:%S")  # datetime.now()
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
cursor = conn.cursor()

sqllist2 = []


def update():
    sql = "SELECT DISTINCT [DateTime] FROM [Alex].[dbo].[Opt_SR] order by DateTime "

    pf = pd.read_sql(sql, conn, index_col=["DateTime"], coerce_float=True, params=None, parse_dates=None,
                     columns=None, chunksize=None)

    print(pf)

    date = pf.index.values.tolist()

    for i in date:
        print(i)
        sql2 = "SELECT distinct [Codes] FROM [Alex].[dbo].Opt_SR where DateTime= '%s' order by codes " % i

        pf2 = pd.read_sql(sql2, conn, index_col=["Codes"], coerce_float=True, params=None, parse_dates=None,
                         columns=None, chunksize=None)
        #print(pf2)

        codes = pf2.index.values.tolist()

        n = int(len(codes) / 100)
        for j in range(0, n+1):
            codes2 = []
            if j == n:
                for ii in codes[100*j:]:
                    ii = ii + ".CZC"
                    codes2.append(ii)
            else:
                for ii in codes[100*j:100*(j+1)]:
                    ii = ii + ".CZC"
                    codes2.append(ii)

            readwind(codes2, i)


def windcodesup(cpcode,date):
        wcodes = w.wset("sectorconstituent", "date=%s;sectorid=%s" % (date, cpcode))
        if wcodes.ErrorCode != 0:
            print("空")
        codes3 = []
        for iii in range(0, len(wcodes.Data[1])):
            codes3.append(wcodes.Data[1][iii])
        print(codes3)
        readwind(codes3, date)


def readwind(codes2, date):
    wdata = w.wss(codes2, "open,high,low,close,settle,pre_close,pre_settle,chg,chg_settlement,volume,oi,oi_chg,amt,delta,us_impliedvol,exe_price,lasttradingdate","tradeDate= %s;priceAdj=U;cycle=D;unit=1" % date)

    if wdata.ErrorCode != 0:
        import time
        print(wdata)
        print("暂停提取数据30秒..请稍等")
        time.sleep(30)
        return readwind(codes2, date)

    #pf2 = pf.ix['2017-04-24', 'SR707C6200']
#print(pf2)

    print(wdata)

    for ii in range(0, len(wdata.Data[0])):
        sqllist = []

        date1 = date
        sqllist.append(date1)
        sqllist.append(wdata.Codes[ii])

        for k in range(0, len(wdata.Fields)):
            sqllist.append(wdata.Data[k][ii])

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)


for i in range((dt - beginDate).days+1):
    date = beginDate + timedelta(days=i)
    print(date)
    windcodesup(1000015593000000, date)
    windcodesup(1000015594000000, date)
sql2 = "INSERT INTO [Alex].[dbo].[M_OptN] VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

cursor.execute("""
 IF OBJECT_ID('M_OptN', 'U') IS NOT NULL
    DROP TABLE M_OptN
 CREATE TABLE M_OptN (
     [DateTime]  DATE NOT NULL
      ,[Codes]  VARCHAR(20) NOT NULL
      ,[OpenPrice] VARCHAR(20)
      ,[High] VARCHAR(20)
      ,[Low] VARCHAR(20) 
      ,[ClosePrice] VARCHAR(20) 
      ,[SettlePrice] VARCHAR(20)
      ,[PreClose]  VARCHAR(20)
      ,[PreSettle]  VARCHAR(20)
      ,[CHG] VARCHAR(20) 
      ,[chg_settlement] VARCHAR(20)
      ,[Volume] VARCHAR(20)
      ,[oi] VARCHAR(20)
      ,[oi_Change] VARCHAR(20)
      ,[Amount] VARCHAR(20)
      ,[DELTA] VARCHAR(20)
      ,[US_Impliedvol] VARCHAR(20)
      ,[EXE_PRICE] VARCHAR(20)
      ,[EXE_DATE] DATE
    )
 """)
cursor.executemany(sql2, sqllist2)
conn.commit()


