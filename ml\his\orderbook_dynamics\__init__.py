"""
Orderbook Dynamics package.

A Python library for analyzing high-frequency limit order book data and
building predictive models for financial market movements.
"""

# Import main package components for easier access
from orderbook_dynamics.src.models import OrderBook, OpenBookMsg, Side, Cell
from orderbook_dynamics.src.open_book import OpenBook, OpenBookFile
from orderbook_dynamics.src.features_extractor import FeaturesExtractor
from orderbook_dynamics.src.decision_tree_dynamics import DecisionTreeDynamics

# Version information
__version__ = '0.1.0' 