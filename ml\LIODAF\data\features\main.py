"""
因子管理系统主模块
整合因子管理、验证和分析功能
@author: lining
"""
import pandas as pd
from typing import Dict
import os
import sys

sys.path.insert(0, sys.path[0]+"/../../")

from data.features.factor_manager import factor_manager, FactorCategory
from data.features.factor_analysis_system import factor_analysis_system
from data.features.feature_generator import OrderBookFeatureGenerator
from core import config


class FactorSystem:
    """因子系统"""
    def __init__(self):
        self.feature_generator = None
        self.analysis_results = {}
        self.validation_results = {}
        self.output_dir = config.OUTDIR + '/features'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
    def generate_features(self, data: pd.DataFrame, selected_features: list, target_cols: list) -> pd.DataFrame:
        """生成特征"""
        self.feature_generator = OrderBookFeatureGenerator(data,selected_features)
        self.target_cols = target_cols
        if not self.feature_generator:
            raise ValueError("系统未初始化")
        
        data, factors = self.feature_generator.generate_all_features()
        all_features = self.feature_generator.all_features
        return data, factors, all_features
    
    def analyze_factors(self, data: pd.DataFrame, factors: list) -> Dict:
        """分析因子"""
        if not factors:
            raise ValueError("未生成因子")
        
        self.analysis_results = factor_analysis_system.analyze_factor_set(data, factors)
        return self.analysis_results
    
    def factor_test(self, std_train_data: pd.DataFrame, target_train_data: pd.DataFrame, factors: list, model=None, is_shap=True):
        """因子测试"""
        self.factor_test_results = factor_analysis_system.test_factors(std_train_data, target_train_data, factors, self.target_cols,model, is_shap)
        self.shap_values = factor_analysis_system.shap_values
        
        return self.factor_test_results,self.shap_values
    
        
    def generate_reports(self, data: pd.DataFrame, factors: list, all_features: dict):
        """生成报告"""
        if not factors:
            raise ValueError("未生成因子")
        
        # 生成因子文档
        self.factor_doc_df = factor_manager.get_factor_documentation(all_features)
        self.factor_doc_df.to_csv(os.path.join(self.output_dir, "factor_documentation.csv"), index=False, encoding="utf-8")

        # 生成因子分析报告
        with open(os.path.join(self.output_dir, "factor_analysis_report.md"), "w", encoding="utf-8") as f:
            f.write(factor_analysis_system.generate_analysis_report(data, factors))
            
        #合并factor_doc_lsit和m1.factor_test_results
        if self.factor_test_results is not None:
            # 根据不同的列名匹配进行合并
            self.mixfactor_doc_df = pd.merge(self.factor_doc_df, self.factor_test_results, left_on='因子名称', right_on='feature', how='left').round(4)
            self.mixfactor_doc_df.to_csv(os.path.join(self.output_dir, "factor_documentation.csv"), index=False, encoding="utf-8")
    
    
    def get_factor_summary(self) -> Dict:
        """获取因子汇总信息"""
        return {
            'total_factors': len(self.factors),
            'categories': {
                category.value: len(factor_manager.get_factors_by_category(category))
                for category in FactorCategory
            },
            'analysis_results': self.analysis_results,
        }


if __name__ == "__main__":
    from core.m1 import M1
    from utils.visualizer import OrderBookVisualizer
    from utils.utils import log_print

    factor_name = 'ofi2'

    # 初始化M1模型和数据
    m1 = M1()
    target_col = config.TARGET_COLS[0]
    m1.mix_df = m1.process_data(config.CODE_LIST)
    m1.generate_features_and_labels()
    m1.train_model()
    
    # 初始化可视化器
    visualizer = OrderBookVisualizer(use_chinese=True)
    
    # 1. 标准因子测试（不包含SHAP分析）
    log_print("执行标准因子测试...")
    factor_analysis_system.test_factors(m1.train_data[target_col], m1.y_train_dict[target_col], m1.feature_cols, config.TARGET_COLS,model=m1.model_trainer.models[target_col])

    m1.factor_system.generate_reports(m1.mix_df, m1.feature_cols, m1.all_features)

    # 2. 绘制传统因子分析图表
    visualizer.plot_IC_IR_win_rate(factor_analysis_system.test_results)
    visualizer.plot_feature_correlation(m1.train_data, m1.y_train_dict, features=m1.feature_cols, target=target_col)
    visualizer.analyze_factor_distribution(m1.train_data[target_col][factor_name])
    
    # 绘制SHAP摘要图
    visualizer.plot_shap_values(factor_analysis_system.shap_values)
    
    # 绘制SHAP重要性条形图
    visualizer.plot_shap_summary(factor_analysis_system.shap_values['importance'])
    
    # 为Top 3重要特征绘制依赖图
    top_features = factor_analysis_system.shap_values['importance'].head(3)['feature'].tolist()
    for feature in top_features:
        visualizer.plot_shap_dependence(factor_analysis_system.shap_values, feature)
    