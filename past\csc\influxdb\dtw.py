# -*- coding: utf-8 -*-
"""
Created on Wed Oct 27 10:50:13 2021

@author: dell
"""
from influxdb import InfluxDBClient
import datetime
import time
import numpy as np
import pandas as pd
# from OmmDatabase import OmmDatabase

import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False 

#%%
def dtw(l1, l2):
    def d(x1, x2):
        return np.abs(x1-x2)
    
    len1 = len(l1)
    len2 = len(l2)
    d_matrix = [[np.inf for i in range(len2+1)] for i in range(len1+1)]
    path_matrix = [[None for i in range(len2+1)] for i in range(len1+1)]
    x1 = l1[0][1]
    x2 = l2[0][1]
    d_matrix[1][1] = d(x1, x2)
    path_matrix[1][1] = None
    for i in range(1, len1+1): # (i-1, j), (i-1, j-1), (i, j-1)
        for j in range(1, len2+1):
            if i == 1 and j == 1:
                continue
            x1 = l1[i-1][1]
            x2 = l2[j-1][1]
            choise = [(i-1, j), (i-1, j-1), (i, j-1)]
            typ = np.argmin([d_matrix[choise[typ][0]][choise[typ][1]] + d(x1, x2) if typ != 1 else d_matrix[choise[typ][0]][choise[typ][1]] + 2*d(x1, x2)for typ in range(3)])
            d_matrix[i][j] = d_matrix[choise[typ][0]][choise[typ][1]] + d(x1, x2) if typ != 1 else d_matrix[choise[typ][0]][choise[typ][1]] + 2*d(x1, x2)

            path_matrix[i][j] = choise[typ]
    path = []
    path.append((len1-1, len2-1))
    i, j = -1, -1
    while True:
        if path_matrix[i][j] == None:
            break
        i, j = path_matrix[i][j]
        path.append((i-1, j-1))
    path.reverse()
    return (path, d_matrix[-1][-1])

#%%
l1 = [(i, i) for i in range(10)]
l2 = [(i, i) for i in range(10)]

result = dtw(l1, l2)  
print(result)     


#%%
# client = InfluxDBClient('192.168.203.11',8989,'','','testbase') #上期所prod
client = InfluxDBClient('10.17.88.168',9001,'reader','reader','testbase') #东坝
# client = InfluxDBClient('10.101.237.137',9090,'','','testbase') #东坝测试，带实时行情
# client = InfluxDBClient('202.0.3.209',8989,'','','testbase') #大商所

#%%
beginStr = '2021-10-28T20:50:00.0Z'
endStr = '2021-10-29T15:10:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000

#%%
iid_list = ['pp2201', 'eb2112', 'pg2112', 'l2201', 'pp2202']
data_origin = {}
for iid in iid_list:
    result = client.query("select * from testdalian where insid_md='"+iid+"' and time >= %d and time <= %d;"%(begin,end)) 
    points = result.get_points()
    l=[]
    for d in points:
        l.append(d)
    print(len(l))
        
    df = pd.DataFrame(l)
    df['UnixStamp'] = df['local_t']/10**9
    df['datetime'] = df['UnixStamp'].apply(lambda x: datetime.datetime.fromtimestamp(x))
    df.index = df.datetime
    df.sort_index(inplace = True)    
    df['micro_price'] = (df['a_p1']*df['b_v1']+df['b_p1']*df['a_v1'])/(df['a_v1']+df['b_v1'])
    df['dmp'] = df['micro_price'].diff()
    df['dmp'].fillna(0, inplace = True)
    df['return'] = df['dmp']/df['micro_price']
    df['log_return'] = df['return'].apply(lambda x: np.log(x+1))
    df['mid_price'] = (df['a_p1'] + df['b_p1'])/2
    df['d_mid'] = df['mid_price'].diff()
    df['d_mid'].fillna(0, inplace = True)
    data_origin[iid] = df.copy()
        
            
#%%
df_resample = {}
frequency = 0.25
for iid in iid_list:
    df = data_origin[iid]    
    df['datetime'] = df['UnixStamp'].apply(lambda x: datetime.datetime.fromtimestamp(x))
    df.index = df['datetime']
    
    df1 = df.dmp.resample(str(int(1000*frequency))+'ms').sum()
    df2 = df.log_return.resample(str(int(1000*frequency))+'ms').sum()
    df3 = df.micro_price.resample(str(int(1000*frequency))+'ms').last()
    df4 = df.mid_price.resample(str(int(1000*frequency))+'ms').last()
    df5 = df.d_mid.resample(str(int(1000*frequency))+'ms').sum()
    
    
    df1 = pd.DataFrame(df1)    
    df1['datetime'] = df1.index
    df1['UnixStamp'] = df1.datetime.apply(lambda x: x.timestamp())    
    df2 = pd.DataFrame(df2)    
    df2['datetime'] = df2.index
    df3 = pd.DataFrame(df3)    
    df3['datetime'] = df3.index
    df4 = pd.DataFrame(df4)    
    df4['datetime'] = df4.index
    df5 = pd.DataFrame(df5)    
    df5['datetime'] = df5.index
    
    df0 = pd.concat([df1, df2, df3, df4, df5], axis = 1)
    df0 = df0.ffill()
    
    df_resample[iid] = df0 

#%%
iid1 = 'pp2201'
iid2 = 'pp2202'       
typ1 = 'dmp'
typ2 = 'd_mid'

#%%
df1 = df_resample[iid1][['UnixStamp', typ1]].values.tolist()
df2 = df_resample[iid2][['UnixStamp', typ2]].values.tolist()

#%%
begin = 2200
end = 2400

#%%
test1 = df1[begin:end]
test2 = df2[begin:end]

dtw_result = dtw(test1, test2)

#%%
x_list = []
y_list = []
for i in dtw_result[0]:
    x_list.append(df1[i[0]][0])
    y_list.append(df2[i[1]][0]-df1[i[0]][0])

#%%
plt.figure()
plt.title(typ1+'&'+typ2)
plt.plot(x_list, y_list)
# plt.legend()
plt.show()

#%%
df1_mkt = data_origin[iid1].resample('250ms').last()
df2_mkt = data_origin[iid2].resample('250ms').last()

df1_mkt = df1_mkt.ffill()
df2_mkt = df2_mkt.ffill()

df1_mkt['log_mp'] = df1_mkt.micro_price.apply(lambda x: np.log(x))
df2_mkt['log_mp'] = df2_mkt.micro_price.apply(lambda x: np.log(x))

df2_mkt.log_mp -= (df2_mkt.log_mp.iloc[begin]-df1_mkt.log_mp.iloc[begin])
df2_mkt.mid_price -= (df2_mkt.mid_price.iloc[begin]-df1_mkt.micro_price.iloc[begin])

plt.figure()
plt.plot(df1_mkt.datetime.iloc[begin:end], df1_mkt.micro_price.iloc[begin:end], label = iid1, alpha = 0.5)
plt.plot(df2_mkt.datetime.iloc[begin:end], df2_mkt.mid_price.iloc[begin:end], label = iid2, alpha = 0.5)

plt.legend()
plt.show()

print(df1_mkt.iloc[begin], df2_mkt.iloc[begin])


            
            
    
    
    
    
    
    
    
    
    
    
    
    
    