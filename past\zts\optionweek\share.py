# -*- coding:utf-8 -*-
import os
import shutil
from datetime import datetime
import sys
import logging
import os

from WindPy import w

codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
sys.path.append(codepath)
from MMaker import sendmail
from daily import pylog


logging.basicConfig(handlers=[logging.FileHandler('%s\\daily\\myapp.log' % codepath, 'a', 'utf-8')],
                    level=logging.DEBUG,
                    format='%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s',
                    datefmt='%Y-%m-%d %a %H:%M:%S'
                    )

command = 'taskkill /F /IM EXCEL.exe'
i = 0
dt = datetime.now()

receivers1 = ["<EMAIL>", ]  # 接收邮箱
receivers2 = ["<EMAIL>", ]  # 错误邮箱

filepath2 = u'\\\\10.25.18.36\\共享\\GR李宁\\共享\\'
filepath = u"D:\\onedrive\\中泰衍生\\周汇报\\"
keyWord = ["50ETF期权周报(20", "周例会-衍生产品部(20","50ETF期权日报(20",]

file1 = '{0}{1}).docx'.format(
    keyWord[0][:-2], datetime.strftime(dt, "%Y-%m-%d"))
file2 = '{0}{1}).xlsx'.format(
    keyWord[1][:-2], datetime.strftime(dt, "%Y-%m-%d"))

w.start()
if w.isconnected():
    t = '\nwind opened successfully'
else:
    t = '\nwind api false'
pylog.writeFile(t)

try:
    pylog.writeFile(u"\n\n             %s\n*\n*\n*数据表格更新\n*\n*" % i)
    text = pylog.exeCmd('%s\\optionweek\\update.py' % codepath)
    pylog.writeFile(text)
    pylog.exeCmd(command)
    pylog.writeFile(u'表格更新成功')

    if dt.weekday() == 4:
        pylog.writeFile(u"今天不需要更新周报")
        pylog.writeFile(u"日报发送成功")
        os._exit(0)
    else:
        pylog.writeFile(u"日报发送成功")

    shutil.copy("{0}{1}".format(filepath, file2),
                "{0}{1}".format(filepath2, file2))
    pylog.writeFile(u'共享完毕')
except:
    pylog.writeFile(u'未更新')
