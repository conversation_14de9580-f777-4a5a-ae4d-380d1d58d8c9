import numpy as np
import scipy.optimize as opt
import QuantLib as ql

# 将原始参数转换为修改后的参数
def original_to_modified(*args):
    a, b, rho, m, sigma = args[:5]

    z = np.sqrt(m ** 2 + sigma ** 2)  # 计算 z 值

    x_a = a + b * (-rho * m + z)  # 计算 x_a

    x_s = b * (rho - m / z)  # 计算 x_s
    x_c = b * sigma ** 2 / z ** 3  # 计算 x_c
    x_l = b * (rho - 1)  # 计算 x_l
    x_r = b * (rho + 1)  # 计算 x_r

    return x_a, x_s, x_c, x_l, x_r  # 返回修改后的参数

# 将修改后的参数转换回原始参数
def modified_to_original(*args):
    x_a, x_s, x_c, x_l, x_r = args[:5]

    b = (x_r - x_l) / 2  # 计算 b
    rho = 0 if b == 0 else (x_r + x_l) / (x_r - x_l)  # 计算 rho
    m = 2 * (x_r - x_s) * (x_s - x_l) * (x_r + x_l - 2 * x_s) / x_c / (x_r - x_l) ** 2  # 计算 m
    sigma = 4 * np.sqrt((x_r - x_s) ** 3 * (x_s - x_l) ** 3 / x_c ** 2) / (x_r - x_l) ** 2  # 计算 sigma
    a = x_a + (x_r - x_s) * (x_s - x_l) * (4 * x_r * x_l - 2 * x_r * x_s - 2 * x_l * x_s) / x_c / (x_r - x_l) ** 2  # 计算 a

    return a, b, rho, m, sigma  # 返回原始参数

# 计算原始 SVI 波动率
def svi_original(k, *args):
    a, b, rho, m, sigma = args[:5]

    z = k - m  # 计算 z 值

    return a + b * (rho * z + np.sqrt(z * z + sigma * sigma))  # 返回 SVI 波动率

# 计算修改后的 SVI 波动率
def svi_modified(k, *args):
    a, b, rho, m, sigma = modified_to_original(args[0], args[1], args[2], args[3], args[4])  # 转换参数

    return svi_original(k, a, b, rho, m, sigma)  # 返回修改后的 SVI 波动率

# 计算原始 SVI 的雅可比矩阵
def svi_original_jac(k, *args):
    a, b, rho, m, sigma = args[:5]

    z = k - m  # 计算 z 值
    s = np.sqrt(z * z + sigma * sigma)  # 计算 s 值

    d_a = np.ones_like(k)  # 计算对 a 的导数
    d_b = rho * z + s  # 计算对 b 的导数
    d_rho = b * z  # 计算对 rho 的导数
    d_m = -b * (rho + z / s)  # 计算对 m 的导数
    d_sigma = b * sigma / s  # 计算对 sigma 的导数

    return np.vstack((d_a, d_b, d_rho, d_m, d_sigma)).T  # 返回雅可比矩阵

# 拟合 SVI 微笑曲线
def fit_svi_smile(ks, vars, x_initial=None):
    if x_initial is None:
        x_initial = [.09, 1.0, 0.1, -.1, .2]  # 默认初始参数
    popt, pcov = opt.curve_fit(svi_original, ks, vars, method='trf', p0=x_initial, jac=svi_original_jac,
                               bounds=([-1, 0, -1, -np.inf, 0], [np.inf, np.inf, 1, np.inf, np.inf]))  # 拟合

    return popt.tolist()  # 返回拟合参数

# 使用 QuantLib 拟合 SVI
def fit_svi_with_ql(days, forward, strikes, vols, m=None, sigma=None):
    as_of_date = ql.Settings.instance().evaluationDate  # 获取当前日期
    option_date = as_of_date + ql.Period(days, ql.Days)  # 计算期权到期日
    maturity = days / 365.0  # 计算到期年限

    smile = ql.SviInterpolatedSmileSection(
        option_date,
        forward,
        ql.DoubleVector(strikes),
        False,
        ql.nullDouble(),
        ql.DoubleVector(vols),
        0.01,
        1.0,
        0.1 if sigma is None else sigma,
        0.0,
        0 if m is None else m,
        False,
        False,
        sigma is not None,
        False,
        m is not None,
        False
    )  # 创建 SVI 微笑曲线

    a = smile.a() / maturity  # 计算 a
    b = smile.b() / maturity  # 计算 b
    rho = smile.rho()  # 计算 rho
    m = smile.m()  # 计算 m
    sigma = smile.sigma()  # 计算 sigma

    rms_err = smile.rmsError()  # 计算均方根误差

    return a, b, rho, m, sigma, rms_err  # 返回参数和误差

if __name__ == '__main__':
    a = 0.09
    b = 1.0
    rho = -0.2
    m = -0.1
    sigma = 0.15

    print(a, b, rho, m, sigma)  # 打印初始参数
    x_a, x_s, x_c, x_l, x_r = original_to_modified(a, b, rho, m, sigma)  # 转换参数
    print(x_a, x_s, x_c, x_l, x_r)  # 打印转换后的参数
    #a, b, rho, m, sigma = modified_to_original(x_a, x_s, x_c, x_l, x_r)
    #print(a, b, rho, m, sigma)

    ks = np.linspace(-0.5, 0.5, 41)  # 生成 k 值

    vars_a = []
    vars_b = []
    vars_c = []

    for k in ks:
        var_1 = svi_original(k, a, b, rho, m, sigma)  # 计算原始 SVI 波动率
        var_2 = svi_modified(k, x_a, x_s, x_c, x_l, x_r)  # 计算修改后的 SVI 波动率
        var_3 = svi_original(k, a, b, -rho, -m, sigma)  # 计算反向 SVI 波动率
        var_4 = svi_modified(k, x_a, -x_s, x_c, -x_r, -x_l)  # 计算反向修改后的 SVI 波动率

        vars_a.append(var_1)  # 存储原始波动率
        vars_b.append(var_2)  # 存储修改后的波动率
        vars_c.append(var_4)  # 存储反向修改后的波动率

        print('%.3f\t%.8f\t%.8f\n' % (k, var_1, var_2))  # 打印 k 值和波动率
