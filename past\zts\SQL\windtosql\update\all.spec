# -*- mode: python -*-

block_cipher = None


a = Analysis(['..\\..\\..\\..\\..\\mine_python\\quantpy\\SQL\\windtosql\\update\\all.py'],
             pathex=['D:\\onedrive\\\xd6\xd0\xcc\xa9\xd1\xdc\xc9\xfa\\MINE_P~1\\quantpy\\SQL\\WINDTO~1\\update'],
             binaries=[],
             datas=[],
             hiddenimports=[],
             hookspath=[],
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher)
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)
exe = EXE(pyz,
          a.scripts,
          a.binaries,
          a.zipfiles,
          a.datas,
          name='all',
          debug=False,
          strip=False,
          upx=True,
          console=True )
