# -*- coding:utf-8 -*-
from __future__ import print_function

import logging
import os
import sys
from datetime import datetime

# reload(sys)
# sys.setdefaultencoding('utf-8')

# encode('gbk', 'ignore').decode('gbk') #.encode('gbk')

codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
sys.path.append(codepath)
from daily import pylog

logging.basicConfig(handlers=[logging.FileHandler('%s\\daily\\myapp.log' % codepath, 'a', 'utf-8')],
                    level=logging.DEBUG,
                    format='%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s',
                    datefmt='%Y-%m-%d %a %H:%M:%S'
                    )

now = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
pylog.writeFile(u"\n\n\nstart\n")

command = 'taskkill /F /IM EXCEL.exe'

pylog.exeCmd(command)
i = 0
i = i + 1
pylog.writeFile(u"\n\n             %s\n*\n*\n*(内网)损益报表制作\n*\n*" % i)
text = pylog.exeCmd('%s\\MMaker\\pldaily\\plmaker.py' % codepath)
pylog.writeFile(text)

pylog.exeCmd(command)
i = i + 1
pylog.writeFile(u"\n\n             %s\n*\n*\n*(内网)损益报表制作\n*\n*" % i)
text = pylog.exeCmd('%s\\MMaker\\pldaily\\plmaker.py' % codepath)
pylog.writeFile(text)

i = i + 1
pylog.writeFile(u"\n\n             %s\n*\n*\n*(外网)损益发送\n*\n*" % i)
text = pylog.exeCmd('%s\\MMaker\\pldaily\\sendpic.py' % codepath)
pylog.writeFile(text)
try:
    pylog.exeCmd(command)
except:
    print(0)
    pass
