# -*- coding: utf-8 -*-

import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random
import math


class SARSA(object):

    def __init__(self, all_states, all_actions, random=False):
        self.all_states = all_states
        self.all_actions = all_actions
        self.Q = np.zeros((len(all_states), len(all_actions)))  #Q q_table

    def reward_mm(equity0, equity1):
        reward = equity1 - equity0
        return reward

    def show_q_table(self):
        return pd.concat([pd.DataFrame(self.Q, columns=self.all_actions, index=self.all_states)], axis=1)

    def state(self, factor):
        #解析状态，根据状态找到state index位置
        '''
        num=0
        for i in range(len(factor)):
            f=factor[i]
            for k in range(i+1,len(factorLengthList)):
               f*=factorLengthList[k]
            num+=f
        '''
        return int(factor)

    def action(self, act):
        #根据实际动作找到 act index位置
        return int(act)

    def q_values(self, factor):
        return self.Q[self.state(factor), :]

    def q_value(self, factor, price_delta):
        return self.Q[self.state(factor), self.action(price_delta)]

    def update_learner(self, s, a, r, s_, a_, alpha=0.01, gamma=0.9):
        if math.isnan(alpha * (r + gamma * self.q_value(s_, a_) - self.q_value(s, a))):
            print(s, a, r, s_, a_)
            print(self.q_value(self.Q, s, a), self.q_value(s_, a_))
        self.Q[self.state(s), self.action(a)] += alpha * (r + gamma * self.q_value(s_, a_) - self.q_value(s, a))

    def mm_learning_strategy(self, factor, epsilon=0.1):
        values = self.q_values(factor)
        if np.random.random() <= epsilon:
            action = np.random.choice(np.where(~np.isnan(values))[0])
        else:
            try:
                max_value = np.nanmax(values)
                action = np.random.choice(np.where(values == max_value)[0])
            except:
                print('Q', self.Q)
                print('factor', factor)
                print('values', values)
                print('max_value', max_value)
        return action


class LiquidityStrategy(StrategyTemplate):
    """"""

    author = "vnpy"

    buy_price = 0
    short_price = 0

    parameters = [
        'maxPos',
        'lots',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'edge',
        'lots'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]

    def __init__(
            self,
            strategy_engine: StrategyEngine,
            strategy_name: str,
            vt_symbol: str,
            setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        #self.sbg = SecondBarGenerator(self.on_bar)
        #self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        #self.am = ArrayManager(size=300)

        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        self.buy_reverse_orderids = None
        self.short_reverse_orderids = None

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net = 0
        self.realPnl = 0
        self.fair = 0
        self.cumPnl = 0
        length = 99999
        self.mFair = np.zeros(length)  # maker fair price
        self.mAsk = np.zeros(length)
        self.mBid = np.zeros(length)
        self.mCount = 0
        self.rCount = 0
        self.pauseCount = 0
        self.lastRefer = {"net": 0, "tick": None, "timepoint": 0}
        self.lastMaker = {"net": 0, "tick": None, "timepoint": 0}
        self.isQuoting = False
        self.lastTicks = {self.maker: 0, self.refer: 0}
        self.comments = None
        self.signal = None
        self.last_bidP = 0
        self.last_askP = 0
        self.signal = None
        self.signal = 0
        self.avg_impact = None
        self.mEdge_best = np.zeros(length)  # 记录市场价差
        self.mEdge_safe = np.zeros(length)  # 记录市场价差
        self.trade_signal = 0  #记录我们最新成交的信号
        self.trade_price = 0  #记录我们最新成交的价格
        self.offer_list = []
        self.offer_list_head = []
        self.last_signal = None

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def getAsk5(self, tick, ask_volume, away_price):
        volume = 0
        for i in range(1, 6):
            volume += tick.__getattribute__('ask_volume_%d' % i)
            if volume >= ask_volume:
                short_price = tick.__getattribute__('ask_price_%d' % i)
                break
        if volume < ask_volume:
            short_price = tick.__getattribute__('ask_price_%d' % i) + away_price
        return short_price

    def getBid5(self, tick, buy_volume, away_price):
        volume = 0
        for i in range(1, 6):
            volume += tick.__getattribute__('bid_volume_%d' % i)
            if volume >= buy_volume:
                buy_price = tick.__getattribute__('bid_price_%d' % i)
                break
        if volume < buy_volume:
            buy_price = tick.__getattribute__('bid_price_%d' % i) - away_price
        return buy_price

    def getFairPrice(self, lastP, askP, bidP, edgeP):
        fair = lastP
        if askP > 0 and bidP > 0:
            if askP - bidP <= edgeP:  # 有流动性，中间价
                fair = 0.5 * (askP + bidP)
            else:  # 流动性不足, 不变
                if lastP > askP:
                    fair = askP
                if lastP < bidP:
                    fair = bidP
        return fair

    def min_book_volume(self, tick: TickData):
        """统计盘口挂单量"""
        total_bid = 0
        total_ask = 0

        for level in range(1, 6):
            total_bid += tick.__getattribute__(f'bid_volume_{level}')
            total_ask += tick.__getattribute__(f'ask_volume_{level}')

        total = min(total_bid, total_ask)
        return max(total, 1)

    def Depth(self, tick: TickData):
        askDepth = 0
        askV = 0
        bidDepth = 0
        bidV = 0
        fair = self.fair
        for level in range(1, 6):
            askDepth += (tick.__getattribute__(f'ask_price_{level}') - fair) * tick.__getattribute__(
                f'ask_volume_{level}')
            askV += tick.__getattribute__(f'ask_volume_{level}')
            bidDepth += (fair - tick.__getattribute__(f'bid_price_{level}')) * tick.__getattribute__(
                f'bid_volume_{level}')
            bidV += tick.__getattribute__(f'bid_volume_{level}')
        askDepth /= askV
        bidDepth /= bidV
        DepthLiquidity = (askDepth - bidDepth) / (askDepth + bidDepth)
        return DepthLiquidity

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key] != self.lastTicks[key]:
                if key == self.maker:
                    self.on_tick_maker(ticks[key])
                elif key == self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick

    def on_tick_refer(self, tick):
        self.rCount += 1

    def on_tick_maker(self, tick):

        ts = self.priceticks[self.maker]
        multiplier = self.sizes[self.maker]
        net = self.net
        lots = self.lots[self.maker]
        maxV = 50
        shortResendFlag = False
        buyResendFlag = False
        mCount = self.mCount
        spread = tick.ask_price_1 - tick.bid_price_1
        self.fair = self.getFairPrice(self.fair, tick.ask_price_1, tick.bid_price_1, self.edge[self.maker] * ts)

        if self.short_reverse_orderids != [] or self.buy_reverse_orderids != []:
            self.cancel_all()
            self.short_reverse_orderids = []
            self.buy_reverse_orderids = []

        if self.buy_hedge_orderids or self.short_hedge_orderids:
            self.cancel_all()
            self.short_hedge_orderids = []
            self.buy_hedge_orderids = []

        # 1. Filter ：非可控情景暂停 

        if self.pauseCount < self.rCount and not self.isQuoting:
            self.isQuoting = True

        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
            if len(self.strategy_engine.active_limit_orders) > 10:  #在途订单超限暂停 or mdDelay>mdDelayLimit or tdDelay>tdDelayLimit
                self.isQuoting = False
                self.pauseCount = self.rCount + 10
                print("Delay limit pause", tick.datetime)
            if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1 > 0 and tick.bid_volume_1 > 0) and (
                    tick.bid_price_1 > float(tick.limit_up) - 5 * ts or tick.ask_price_1 < float(
                    tick.limit_down) + 5 * ts):  # 跌停附近
                self.isQuoting = False
                self.pauseCount = self.rCount + 100
                print("price limit pause", tick.datetime)
            if self.min_book_volume(tick) < self.lots[self.maker] * 8:  #市场做市商不少于10个
                self.isQuoting = False
                self.pauseCount = self.rCount + 1
                print("liquidity pause min", tick.datetime)
            if tick.ask_price_1 - tick.bid_price_1 > self.edge[self.maker] * ts:  #无效市场
                self.isQuoting = False
                self.pauseCount = self.rCount + 1
                print("liquidity spread pause", tick.datetime)

        #check resume quoting............................................... 

        #### Algo : JoinMarket  ~~~~~~~无效价格情况暂未处理~~~~~~~~~~~~~~~~~~ 

        #止盈止损
        stdRange = spread / ts
        stop = max(self.edge[self.maker], stdRange) * 2 * self.lots[self.maker] / (
                    abs(self.net) + self.lots[self.maker])
        pnl = (self.fair - self.avg) / ts * np.sign(self.net)

        if pnl <= -stop and self.net != 0 and stdRange < self.edge[self.maker]:
            if self.net > 0:
                self.buy_hedge_orderids = []
                self.short_hedge_orderids = self.short(self.maker, tick.bid_price_1, abs(self.net), 'stop_hedge')
            else:
                self.short_hedge_orderids = []
                self.buy_hedge_orderids = self.buy(self.maker, tick.ask_price_1, abs(self.net), 'stop_hedge')
            return

        stdRange = spread / ts

        _tick = self.order_book(tick)

        #流动性报价
        if self.Depth(_tick) > 0.1:
            self.signal = 1
        elif self.Depth(_tick) < -0.1:
            self.signal = -1
        else:
            self.signal = 0

        # 数挡位
        edge_best = self.edge[self.maker]  #初始值
        edge_safe = self.edge[self.maker]  #初始值
        self.mEdge_best[mCount] = (self.getAsk5(tick, 3 * self.lots[self.maker], 0) - self.getBid5(tick, 3 * self.lots[
            self.maker], 0)) / ts  #设定报价价差,随市场价宽变动
        self.mEdge_safe[mCount] = (self.getAsk5(tick, 6 * self.lots[self.maker], 0) - self.getBid5(tick, 6 * self.lots[
            self.maker], 0)) / ts  #设定报价价差,随市场价宽变动

        self.mFair[mCount] = self.fair
        if self.mCount > 10:
            edge_best = max(1, self.edge[self.maker] * 0.5, self.mEdge_best[mCount],
                            np.median(self.mEdge_best[mCount - 10:mCount]))  #防谎骗
            edge_safe = max(1, self.edge[self.maker] * 0.5, self.mEdge_safe[mCount],
                            np.median(self.mEdge_safe[mCount - 10:mCount]))  #防谎骗

        central_price = self.fair

        bidP_best = self.getBid5(tick, 4 * self.lots[self.maker], 0)
        askP_best = self.getAsk5(tick, 4 * self.lots[self.maker], 0)
        bidP_safe = self.getBid5(tick, 6 * self.lots[self.maker], 0)
        askP_safe = self.getAsk5(tick, 6 * self.lots[self.maker], 0)

        pos_adjust = -0.5 * self.net / self.lots[self.maker] * ts

        if self.signal == 1:
            bidP = bidP_best
            askP = askP_safe
            self.comments = 'MM_u'
        elif self.signal == -1:
            bidP = bidP_safe
            askP = askP_best
            self.comments = 'MM_d'
        else:
            bidP = bidP_safe
            askP = askP_safe
            self.comments = 'MM_s'

        bidP += pos_adjust
        askP += pos_adjust

        loss = 3000

        # 报价取整
        bidP = ts * round(bidP / ts + 0.0000001)
        askP = ts * round(askP / ts + 0.0000001)

        if askP - bidP == (self.edge[self.maker] + 1) * ts:  #由于取整或仓位调整造成的价差问题
            if self.net < 0:
                bidP += 1 * ts
            if self.net > 0:
                askP -= 1 * ts

        if bidP == tick.bid_price_1 and tick.bid_volume_1 <= 3 * lots and self.net > lots:
            bidP -= ts

        if askP == tick.ask_price_1 and tick.ask_volume_1 <= 3 * lots and self.net < -1 * lots:
            askP += ts

        if mCount > 10:
            if self.net <= -1 * self.maxPos and tick.bid_volume_1 >= 3 * lots:
                bidP = min(bidP, tick.bid_price_1 + ts)
            else:
                bidP = min(bidP, tick.bid_price_1)
            if self.net >= 1 * self.maxPos and tick.ask_volume_1 >= 3 * lots:
                askP = max(askP, tick.ask_price_1 - ts)
            else:
                askP = max(askP, tick.ask_price_1)

                #### Algo End ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        self.cumPnl = round((net * self.fair + self.realPnl) * self.sizes[self.maker])  # 累计盈亏 ？？
        if bidP > 0 and askP > 0:
            self.mBid[mCount] = bidP
            self.mAsk[mCount] = askP  # mid price  save
            self.mCount += 1

            # save
        self.lastMaker["timepoint"] = tick.datetime
        self.lastMaker["net"] = net
        self.lastMaker["tick"] = tick

        # TODO:  收盘前自动处理净持仓， 连续成交保护( 市场单边大幅波动)

        if self.last_askP != askP:
            shortResendFlag = True
        if self.last_bidP != bidP:
            buyResendFlag = True

        self.last_bidP = bidP
        self.last_askP = askP

        if not self.isQuoting:  #非Quoting状态需要撤单，等同于C++的checkneeddelete
            if self.buy_vt_orderids:
                self.cancel_order(self.buy_vt_orderids[0])
            if self.short_vt_orderids:
                self.cancel_order(self.short_vt_orderids[0])

        if mCount > 30 and self.isQuoting:
            if not self.buy_vt_orderids or not self.short_vt_orderids:
                if self.buy_vt_orderids:
                    self.cancel_order(self.buy_vt_orderids[0])
                    self.buy_vt_orderids = self.buy(self.maker, bidP, self.lots[self.maker], self.comments)
                else:
                    self.buy_vt_orderids = self.buy(self.maker, bidP, self.lots[self.maker], self.comments)
                if self.short_vt_orderids:
                    self.cancel_order(self.short_vt_orderids[0])
                    self.short_vt_orderids = self.short(self.maker, askP, self.lots[self.maker], self.comments)
                else:
                    self.short_vt_orderids = self.short(self.maker, askP, self.lots[self.maker], self.comments)
            elif buyResendFlag or shortResendFlag:  # 重发新的quote
                if self.buy_vt_orderids:
                    self.cancel_order(self.buy_vt_orderids[0])
                    self.buy_vt_orderids = self.buy(self.maker, bidP, self.lots[self.maker], self.comments)
                if self.short_vt_orderids:
                    self.cancel_order(self.short_vt_orderids[0])
                    self.short_vt_orderids = self.short(self.maker, askP, self.lots[self.maker], self.comments)

            self.offer_list.append(
                [tick.datetime, str(self.maker.split('.')[0]), float(bidP), float(askP), float(pos_adjust), float(pnl),
                 float(self.net)])
            self.offer_list_head = ['datetime', 'insid', 'my_bid', 'my_ask', 'pos_adjust', 'pnl', 'net']

        self.bidP = bidP
        self.askP = askP
        self.last_signal = self.signal

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume = trade.volume
        price = trade.price
        trade.offer = (self.bidP + self.askP) / 2
        if volume > 0:
            if trade.direction.value == '多':
                if self.net >= 0:
                    self.avg = (self.net * self.avg + volume * price) / (self.net + volume)
                elif volume + self.net > 0:  # net<0 # 平仓
                    self.avg = price
                self.net += volume
                self.realPnl -= volume * price
            #         
            elif trade.direction.value == '空':
                if self.net <= 0:
                    self.avg = (-self.net * self.avg + volume * price) / (-self.net + volume)
                elif volume - self.net > 0:  # net >0 # 平仓
                    self.avg = price
                self.net -= volume
                self.realPnl += volume * price

        self.trade_signal = 1 if trade.comments == 'MM_u' else -1 if trade.comments == 'MM_d' else 0
        self.trade_price = price

        if volume == self.lots[self.maker]:
            if trade.direction.value == '多':
                self.cancel_all()  #cancel完会跳转至onOrder，然后自动发一个单？
                self.buy_reverse_orderids = []
                self.short_reverse_orderids = []
                self.short_reverse_orderids = self.short(self.maker, trade.price, self.lots[self.maker], 'rev_hedge')

            if trade.direction.value == '空':
                self.cancel_all()  #cancel完会跳转至onOrder，然后自动发一个单？
                self.buy_reverse_orderids = []
                self.short_reverse_orderids = []
                self.buy_reverse_orderids = self.buy(self.maker, trade.price, self.lots[self.maker], 'rev_hedge')

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)

    def order_book(self, tick):
        ts = self.priceticks[self.maker]
        midP = ts * np.floor(self.fair / ts)
        if midP == self.fair:
            bid_best = midP - 1 * ts
            ask_best = midP + 1 * ts
        else:
            bid_best = midP  #取整数部分
            ask_best = midP + 1 * ts
        orderbook = TickData(
            bid_price_1=bid_best - 0 * ts,
            bid_price_2=bid_best - 1 * ts,
            bid_price_3=bid_best - 2 * ts,
            bid_price_4=bid_best - 3 * ts,
            bid_price_5=bid_best - 4 * ts,

            bid_volume_1=1,
            bid_volume_2=1,
            bid_volume_3=1,
            bid_volume_4=1,
            bid_volume_5=1,

            ask_price_1=ask_best + 0 * ts,
            ask_price_2=ask_best + 1 * ts,
            ask_price_3=ask_best + 2 * ts,
            ask_price_4=ask_best + 3 * ts,
            ask_price_5=ask_best + 4 * ts,

            ask_volume_1=1,
            ask_volume_2=1,
            ask_volume_3=1,
            ask_volume_4=1,
            ask_volume_5=1,

            gateway_name=self.strategy_engine.gateway_name,
            symbol=tick.symbol,
            exchange=tick.exchange,
            datetime=tick.datetime
        )

        bid_pirce_list = [tick.__getattribute__(f'bid_price_{level}') for level in range(1, 6)]
        ask_pirce_list = [tick.__getattribute__(f'ask_price_{level}') for level in range(1, 6)]

        for level in range(1, 6):  #bid修正
            p = orderbook.__getattribute__(f'bid_price_{level}')
            if p in bid_pirce_list:
                i = bid_pirce_list.index(p) + 1
                orderbook.__setattr__(f'bid_volume_{level}',
                                      self.volume_adjust(tick.__getattribute__(f'bid_volume_{i}')))

        for level in range(1, 6):  #ask修正
            p = orderbook.__getattribute__(f'ask_price_{level}')
            if p in ask_pirce_list:
                i = ask_pirce_list.index(p) + 1
                orderbook.__setattr__(f'ask_volume_{level}',
                                      self.volume_adjust(tick.__getattribute__(f'ask_volume_{i}')))
        return orderbook

    def volume_adjust(self, v):
        v_adjust = 5 * self.lots[self.maker]
        v_adjust = np.median([0.5 * self.lots[self.maker], v, v_adjust])

        return v_adjust

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """  # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        # if (
        #     (dt.time() > dtime(13, 5) and dt.time() < dtime(14, 55))
        #     or (dt.time() > dtime(1, 5) and dt.time() < dtime(2, 13))
        #     or (dt.time() > dtime(2, 32) and dt.time() < dtime(3, 28))
        #     or (dt.time() > dtime(5, 32) and dt.time() < dtime(6, 55))
        # ):
        #     self.trading = True
        #     self.isQuoting = True
        if (dt.time() > dtime(22, 58) and dt.time() < dtime(23, 00)) or (
                dt.time() > dtime(14, 58) and dt.time() < dtime(15, 00)) or (
                dt.time() > dtime(11, 29) and dt.time() < dtime(11, 30)):
            self.trading = True
            self.isQuoting = False  #临近收盘,清仓
            self.cancel_all()
            if self.net > 0:
                self.buy_hedge_orderids = []
                self.short_hedge_orderids = self.short(self.maker, self.strategy_engine.ticks[self.maker].bid_price_1,
                                                       abs(self.net), 'hedge')
            if self.net < 0:
                self.short_hedge_orderids = []
                self.buy_hedge_orderids = self.buy(self.maker, self.strategy_engine.ticks[self.maker].ask_price_1,
                                                   abs(self.net), 'hedge')
