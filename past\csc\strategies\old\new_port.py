""""""
import numpy as np
from typing import Any,List, Dict
from datetime import datetime, time as dtime

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData
from vnpy.trader.constant import Status

class TickWithRefer(StrategyTemplate):
    """"""

    author = "sincerefall"

    fixed_size = 10

    fast_ma0_2 = 0.0
    fast_ma1_2 = 0.0
    slow_ma0_2 = 0.0
    slow_ma1_2 = 0.0

    parameters = ["fixed_size"]
    variables = ["fast_ma0","fast_ma1","slow_ma0","slow_ma1"]

    def __init__(
        self,
        strategy_engine: Any,
        strategy_name: str,
        vt_symbols: List[str],
        setting: dict
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbols, setting)

        self.buy_vt_orderids = []
        self.short_vt_orderids = []        

        self.maker_bid = 0
        self.maker_ask = 0
        self.refer_bid = 0
        self.refer_ask = 0

        # 拆分目标单和主力单
        self.maker_symbol, self.refer_symbol = vt_symbols


    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_ticks(self, ticks: Dict[str, TickData]):
        """"""

        # Return if one leg data is missing

        if self.maker_symbol not in self.strategy_engine.update_vt_symbols:  #很重要！
            return

        tick = ticks[self.maker_symbol]

        if not self.buy_vt_orderids:
            self.buy_vt_orderids = self.buy(self.maker_symbol,tick.bid_price_1, self.fixed_size)
        else:
            self.cancel_order(self.buy_vt_orderids[0])  #cancel完会跳转至onOrder，然后自动发一个单
            self.buy_vt_orderids = self.buy(self.maker_symbol,tick.bid_price_3, self.fixed_size)

        if not self.short_vt_orderids:
            self.short_vt_orderids = self.short(self.maker_symbol,tick.ask_price_3, self.fixed_size)
        else:
            self.cancel_order(self.short_vt_orderids[0])  #cancel完会跳转至onOrder，然后自动发一个单
            self.short_vt_orderids = self.short(self.maker_symbol,tick.ask_price_3, self.fixed_size)
        

    def on_trade(self, trade: TradeData) -> None:
        """
        Callback of new trade data update.
        """
        pass

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
        ]:
            if order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
        

