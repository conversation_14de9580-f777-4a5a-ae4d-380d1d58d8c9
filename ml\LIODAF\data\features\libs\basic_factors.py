"""
基础因子模块
包含中间价格、成交量、价差等基础因子
@author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory
from core import config

basic_col = [
    # 基础数据列
    "mid_price",
    "mid_price_level_2",
    "mid_price_level_3",
    "mid_price_level_4",
    "mid_price_level_5",
    "tradedVol",
    
    # 基本特征 - 价差特征
    "spread_1", 
    "spread_2",
    "spread_3",
    "spread_4",
    "spread_5",
    "accumulated_price_spread",
    "accumulated_volume_spread",
    
    # 基本特征 - 订单簿不平衡特征
    "imbalance_1", 
    "imbalance_2", 
    "imbalance_3", 
    "imbalance_4", 
    "imbalance_5",
    "total_bid_vol", 
    "total_ask_vol", 
    "vol_imbalance",
    
    # 基本特征 - 价格压力特征
    "bid_price_diff_1", 
    "bid_price_diff_2", 
    "bid_price_diff_3", 
    "bid_price_diff_4",
    "bid_price_diff_max",
    "ask_price_diff_1", 
    "ask_price_diff_2", 
    "ask_price_diff_3", 
    "ask_price_diff_4",
    "ask_price_diff_max",


    ]


# 基础因子

def calculate_mid_price(data: pd.DataFrame, level: int = 1) -> pd.Series:
    """计算中间价格核心函数
    公式: mid_price = (AskPrice{level} + BidPrice{level}) / 2
    """
    col = 'mid_price' if level == 1 else f'mid_price_{level}'
    if col not in data.columns:
        data[col] = (data[f'AskPrice{level}'] + data[f'BidPrice{level}']) / 2
    return data[col]


# 统一注册中间价格因子 (1-5档)
for i in range(1, 6):
    is_base_level = i == 1  # 第一档作为基础因子
    factor_manager.register_factor(Factor(
        name=f"mid_price_{i}" if not is_base_level else "mid_price",
        category=FactorCategory.BASIC,
        description=f"{i}档位的中间价格",
        calculation=lambda data, i=i: calculate_mid_price(data, i),  # 通过lambda绑定当前i值
        dependencies=[f"AskPrice{i}", f"BidPrice{i}"],
        parameters={"level": i} if not is_base_level else None,
        source="orderbook-dynamic-features v2"
    ))


# 计算中间价收益率
def calculate_mid_return(data: pd.DataFrame, level: int = 1, window: int = 1) -> pd.Series:
    """计算中间价收益率
    公式: mid_return = (mid_price_t - mid_price_{t-window}) / mid_price_{t-window}
    """
    if level == 1:
        col = 'mid_price'
    else:
        col = f'mid_price_{level}'
    if col not in data.columns:
        mid = calculate_mid_price(data, level)
    else:
        mid = data[col]

    return mid.pct_change(window)


factor_manager.register_factor(Factor(
    name="mid_return",
    category=FactorCategory.BASIC,
    description="中间价一期收益率",
    calculation=lambda data: calculate_mid_return(data, 1),
    dependencies=["mid_price", "AskPrice1", "BidPrice1"],
    parameters={"window": 1}
))

factor_manager.register_factor(Factor(
    name="mid5_return",
    category=FactorCategory.BASIC,
    description="中间价5收益率",
    calculation=lambda data: calculate_mid_return(data, 5, 1),
    dependencies=["mid_price_5", "AskPrice5", "BidPrice5"],
    parameters={"window": 1}
))


# 计算成交量
def calculate_tradedVol(data: pd.DataFrame) -> pd.Series:
    """计算成交量
    公式: volume = tradedVol
    """
    if 'tradedVol' not in data.columns:
        data['tradedVol'] = data['Volume'].diff(1)
        return data['tradedVol']
    else:
        return data['tradedVol']


factor_manager.register_factor(Factor(
    name="tradedVol",
    category=FactorCategory.BASIC,
    description="成交量",
    calculation=calculate_tradedVol,
    dependencies=["tradedVol"]
))

def calculate_tradedValue(data: pd.DataFrame) -> pd.Series:
    #计算成交额
    if 'tradedValue' not in data.columns:
        data['tradedValue'] = data['TotalValueTraded'].diff(1)
        return data['tradedValue']
    else:
        return data['tradedValue']
    

factor_manager.register_factor(Factor(
    name="tradedValue",
    category=FactorCategory.BASIC,
    description="成交额",
    calculation=calculate_tradedValue,
    dependencies=["tradedValue"]
))


def calculate_avg_prc(data: pd.DataFrame) -> pd.Series:
    """计算平均价格, 如果tradedVol为0, 则使用mid_price"""
    trade_vol = data['Volume'].diff()
    trade_value = data['TotalValueTraded'].diff()
    # 向量化计算平均价格
    mask = trade_vol == 0
    avg_prc = trade_value / (trade_vol * config.MULT)
    avg_prc[mask] = 0
    # 处理交易量为0的情况
    avg_prc = avg_prc.replace([np.inf, -np.inf], 0).fillna(0)
    return avg_prc

factor_manager.register_factor(Factor(
    name="avg_prc",
    category=FactorCategory.BASIC,
    description="平均价格",
    calculation=calculate_avg_prc,
    dependencies=[]
))

for i in range(1, 6):
    factor_manager.register_factor(Factor(
        name=f"spread_{i}",
        category=FactorCategory.BASIC,
        description=f"{i}档位的价差",
        calculation=lambda data, level=i: data[f'AskPrice{level}'] - data[f'BidPrice{level}'],
        dependencies=[f"AskPrice{i}", f"BidPrice{i}"],
        parameters={"level": i},
        source="orderbook-dynamic-features v2"
    ))


# 注册价格步长因子,价格压力特征 
def calculate_price_diff(data: pd.DataFrame, side: str, level: int) -> pd.Series:
    """计算价格差异
    公式(bid): price_diff = BidPrice_level - BidPrice_{level+1}
    公式(ask): price_diff = AskPrice_{level+1} - AskPrice_level
    """
    if side == 'Bid':
        return data[f'BidPrice{level}'] - data[f'BidPrice{level + 1}']
    else:
        return data[f'AskPrice{level + 1}'] - data[f'AskPrice{level}']

def calculate_price_diff_max(data: pd.DataFrame, side: str, max_level: int, min_level: int) -> pd.Series:
    """计算价格差异最大值"""
    return data[f'{side}Price{max_level}'] - data[f'{side}Price{min_level}']


# 注册买入档位价格差异
for side in ['Bid', 'Ask']:
    for i in range(1, 5):
        factor_manager.register_factor(Factor(
            name=f"{side.lower()}_price_diff_{i}",
            category=FactorCategory.BASIC,
            description=f"{side}档位{i}档与{i + 1}档之间的价格差异",
            calculation=lambda data, level=i, s=side: calculate_price_diff(data, s, level),
            dependencies=[f"{side}Price{i}", f"{side}Price{i + 1}"],
            parameters={"level": i},
            source="orderbook-dynamic-features v3"
        ))
        factor_manager.register_factor(Factor(
            name=f"{side.lower()}_price_diff_max",
            category=FactorCategory.BASIC,
            description=f"{side}档位5档与1档之间的价格差异",
            calculation=lambda data, s=side: calculate_price_diff_max(data, s, 5, 1),
            dependencies=["BidPrice5", "BidPrice1", "AskPrice5", "AskPrice1"],
            parameters={"level": 5},
            source="orderbook-dynamic-features v3"
        ))


# 计算五档平均价格和成交量
def calculate_mean_price_volume(data: pd.DataFrame, side: str, type: str) -> pd.Series:
    """计算五档平均价格或成交量
    
    参数:
        data: 行情数据
        side: 'Ask'(卖方)或'Bid'(买方)
        type: 'Price'(价格)或'Vol'(成交量)
    """
    # 获取存在的列名
    cols = [f'{side}{type}{i}' for i in range(1, 6)]

    # 如果没有相关列，返回全0序列，否则计算平均值
    if not cols:
        return pd.Series(0, index=data.index)
    else:
        return data[cols].mean(axis=1)


# 注册四个平均因子：买方平均价格、卖方平均价格、买方平均成交量、卖方平均成交量
for side, side_desc in [('Ask', '卖方'), ('Bid', '买方')]:
    for type, type_desc in [('Price', '价格'), ('Vol', '成交量')]:
        factor_manager.register_factor(Factor(
            name=f"mean_{side.lower()}_{type.lower()}",
            category=FactorCategory.BASIC,
            description=f"{side_desc}五档平均{type_desc}",
            calculation=lambda data, s=side, t=type: calculate_mean_price_volume(data, s, t),
            dependencies=[f"{side}{type}{i}" for i in range(1, 6)],
            source="orderbook-dynamic-features v4"
        ))


# 计算累计成交量差异
def calculate_accumulated_volume_diff(data: pd.DataFrame) -> pd.Series:
    """计算累计成交量差异"""
    return sum(data[f"AskVol{i}"] - data[f"BidVol{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_volume_diff",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖量差异的累计值",
    calculation=calculate_accumulated_volume_diff,
    dependencies=[f"AskVol{i}" for i in range(1, 6)] + [f"BidVol{i}" for i in range(1, 6)],
    source="orderbook-dynamic-features v5"
))


# 计算不平衡度函数
def calculate_imbalance(data: pd.DataFrame, level: int = 1) -> pd.Series:
    """计算不平衡度
    公式: imbalance = (BidVol{level} - AskVol{level})/(BidVol{level} + AskVol{level})
    """
    bid_vol = data[f'BidVol{level}']
    ask_vol = data[f'AskVol{level}']
    denominator = bid_vol + ask_vol
    # 避免除零
    return (bid_vol - ask_vol).div(denominator.where(denominator != 0, np.nan))


# 注册不平衡度因子 (1-5档)
for i in range(1, 6):
    factor_manager.register_factor(Factor(
        name=f"imbalance_{i}",
        category=FactorCategory.BASIC,
        description=f"{i}档位的买卖不平衡度",
        calculation=lambda data, level=i: calculate_imbalance(data, level),
        dependencies=[f"BidVol{i}", f"AskVol{i}"],
        parameters={"level": i},
        source="orderbook-dynamic-features v6"
    ))
    

###########################################
# 价格差异特征因子
###########################################

def calculate_price_cross_avg(data: pd.DataFrame, side: str, levels: list) -> pd.Series:
    """计算平均盘口价差"""
    return data[[f'{side}_price_diff_{j}' for j in levels]].mean(axis=1)


def calculate_sum_vol(data: pd.DataFrame, side: str, levels: list) -> pd.Series:
    """计算成交量之和"""
    return data[[f'{side}Vol{j}' for j in levels]].sum(axis=1)

# 注册价格差异特征因子
for side in ['Bid', 'Ask']:
    # 注册平均盘口价差因子
    factor_manager.register_factor(Factor(
        name=f"{side.lower()}_price_diff_avg",
        category=FactorCategory.ADVANCED,
        description=f"{side}前两档的平均盘口价差",
        calculation=lambda data, s=side: calculate_price_cross_avg(data, s.lower(), range(1, 3)),
        dependencies=[f"{side.lower()}pxcx{j}" for j in range(1, 3)],
        parameters={"side": side, "levels": list(range(1, 3))},
        source="market_microstructure_factors"
    ))
    
    # 注册平均价格差异因子
    for i in range(1, 6):
        factor_manager.register_factor(Factor(
            name=f"sum{side.lower()}vol{i}",
            category=FactorCategory.ADVANCED,
            description=f"{side}前{i}档的成交量之和",
            calculation=lambda data, s=side, l=i: calculate_sum_vol(data, s, range(1, l+1)),
            dependencies=[f"{side}Vol{j}" for j in range(1, i+1)],
            parameters={"side": side, "levels": list(range(1, i+1))},
            source="market_microstructure_factors"
        ))





