"""
基于gplearn的遗传算法因子生成器
@author: lining
"""
import numpy as np
import pandas as pd
from typing import List
from gplearn.genetic import SymbolicRegressor
import sys
import os
import copy
sys.path.insert(0, sys.path[0]+"/../../../")
from data.features.factor_manager import Factor, FactorCategory, factor_manager
# 定义返回类型


def convert_inverse_prim(prim, args):
    """转换逆操作"""
    prim = copy.copy(prim)
    
    converter = {
        'add': lambda *args_: "{}+{}".format(*args_),
        'sub': lambda *args_: "{}-{}".format(*args_),
        'mul': lambda *args_: "{}*{}".format(*args_),
        'div': lambda *args_: "{}/{}".format(*args_),
        'abs': lambda *args_: "abs({})".format(*args_),
        'log': lambda *args_: "log({})".format(*args_),
        'exp': lambda *args_: "exp({})".format(*args_),
        'sqrt': lambda *args_: "sqrt({})".format(*args_),
        'neg': lambda *args_: "-{}".format(*args_),
        'inv': lambda *args_: "1/{}".format(*args_),
        'max': lambda *args_: "max({})".format(*args_),
        'min': lambda *args_: "min({})".format(*args_),
    }
    
    prim_formatter = converter.get(prim.name, prim.format)
    return prim_formatter(*args)

def stringify_for_sympy(f):
    """将表达式转换为可读字符串"""
    string = ""
    stack = []
    for node in f:
        stack.append((node, []))
        while len(stack[-1][1]) == stack[-1][0].arity:
            prim, args = stack.pop()
            string = convert_inverse_prim(prim, args)
            if len(stack) == 0:
                break
            stack[-1][1].append(string)
    return string

def beautify_formula(formula):
    """
    美化公式，将函数名称转换为数学符号
    例如：将sqrt转为√符号、log转为ln、exp转为e^等数学符号转换
    
    Args:
        formula: 原始公式字符串
        
    Returns:
        str: 美化后的公式
    """
    if formula is None:
        return ""
    
    # 定义替换规则
    replacements = [
        ('sqrt(', '√('),
        ('log(', 'ln('),
        ('exp(', 'e^('),
        ('abs(', '|'),
        ('))', ')|'),  # 处理abs函数的结束部分
        ('neg(', '-('),
        ('inv(', '1/('),
        ('mul(', '('),
        ('add(', '('),
        ('sub(', '('),
        ('div(', '('),
    ]
    
    # 应用替换
    result = formula
    for old, new in replacements:
        result = result.replace(old, new)
    
    # 移除多余的括号
    result = result.replace(', ', ' ').replace('(', ' ').replace(')', ' ')
    
    # 去除多余空格
    while '  ' in result:
        result = result.replace('  ', ' ')
    
    return result.strip()


class GplearnFactorGenerator:
    """基于gplearn的遗传算法因子生成器"""
    def __init__(self, data: pd.DataFrame, target_cols: List[str], factor_manager):
        self.data = data
        self.target_cols = target_cols
        self.factor_manager = factor_manager
        self.available_columns = [col for col in data.columns if col not in target_cols]
        
        # 初始化gplearn配置
        self.function_set = ['add', 'sub', 'mul', 'div', 'sqrt', 'log', 'abs', 'neg', 'inv', 'max', 'min']
        self.population_size = 100
        self.generations = 50
        self.tournament_size = 20
        self.stopping_criteria = 0.01
        self.p_crossover = 0.7
        self.p_subtree_mutation = 0.1
        self.p_hoist_mutation = 0.05
        self.p_point_mutation = 0.1
        self.max_samples = 0.9
        self.verbose = 1
        self.n_jobs = -1
        self.random_state = 42
        
    def evaluate_fitness(self, y, y_pred, sample_weight):
        """自定义适应度函数"""
        # 将 y_pred 转换为 pandas Series
        y_pred = pd.Series(y_pred, index=y.index)
        
        # 计算IC
        ic = y.corr(y_pred)
        
        # 计算Rank IC
        rank_ic = y.rank().corr(y_pred.rank())
        
        # 计算胜率
        pred_direction = (y_pred > 0).astype(int)
        actual_direction = (y > 0).astype(int)
        win_rate = (pred_direction == actual_direction).mean()
        
        # 综合评分
        fitness = 0.4 * abs(ic) + 0.3 * abs(rank_ic) + 0.3 * win_rate
        return fitness
        
    def generate_factors(self, n_factors: int = 10) -> List[Factor]:
        """生成新因子"""
        factors = []
        
        for i in range(n_factors):
            # 创建符号回归器
            est = SymbolicRegressor(
                population_size=self.population_size,
                generations=self.generations,
                tournament_size=self.tournament_size,
                stopping_criteria=self.stopping_criteria,
                p_crossover=self.p_crossover,
                p_subtree_mutation=self.p_subtree_mutation,
                p_hoist_mutation=self.p_hoist_mutation,
                p_point_mutation=self.p_point_mutation,
                max_samples=self.max_samples,
                verbose=self.verbose,
                n_jobs=self.n_jobs,
                random_state=self.random_state,
                function_set=self.function_set,
                metric='pearson'  # 使用 Pearson 相关系数作为评估指标
            )
            
            # 训练模型
            X = self.data[self.available_columns]
            y = self.data[self.target_cols[0]]
            
            # 数据预处理：只选择数值型列
            numeric_columns = X.select_dtypes(include=[np.number]).columns
            non_numeric_columns = set(X.columns) - set(numeric_columns)
            if non_numeric_columns:
                print(f"过滤掉非数值型列: {non_numeric_columns}")
            X = X[numeric_columns]
            
            # 验证目标变量
            if not pd.api.types.is_numeric_dtype(y):
                raise ValueError("目标变量必须是数值型")
            
            # 处理 NaN 值
            from sklearn.impute import SimpleImputer
            imputer = SimpleImputer(strategy='median')
            X_imputed = imputer.fit_transform(X)
            X = pd.DataFrame(X_imputed, columns=X.columns, index=X.index)
            
            # 记录 NaN 处理情况
            nan_count = X.isna().sum().sum()
            if nan_count > 0:
                print(f"填充了 {nan_count} 个 NaN 值")
            
            # 训练模型
            est.fit(X, y)
            
            # 使用自定义评估函数计算得分
            y_pred = est.predict(X)
            fitness_score = self.evaluate_fitness(y, y_pred, None)
            print(f"因子 {i+1} 的自定义评估得分: {fitness_score:.4f}")
            
            # 获取公式
            formula = str(est._program)
            
            # 创建计算函数
            def make_calculator(program):
                def calculator(data):
                    return program.execute(data)
                return calculator
            
            # 创建因子
            factor = Factor(
                name=f"gplearn_factor_{i+1}",
                category=FactorCategory.DIY,
                description=f"gplearn生成的第{i+1}个因子\n公式: {formula}",
                calculation=make_calculator(est._program),
                dependencies=self.available_columns,
                source="gplearn_algorithm"
            )
            factors.append(factor)
            
        return factors 
    
    
if __name__ == "__main__":
    from core import config
    from core.m1 import M1
    from utils.utils import log_print
    import os
    
    from data.features.libs.basic_factors import basic_col
    
    import warnings
    warnings.filterwarnings("ignore", category=FutureWarning)
    
    selected_features = basic_col
    
    # 初始化M1模型和数据
    m1 = M1()
    target_col = config.TARGET_COLS[0]
    m1.mix_df=m1.process_data(config.CODE_LIST)
    m1.selected_features = selected_features
    m1.generate_features_and_labels()
    gen_df = m1.mix_df[config.BASE_COL + m1.feature_cols + [config.TARGET_COLS[0]]]
        # 使用gplearn生成新因子
    log_print("开始使用gplearn生成新因子...", level='info')
    gplearn_generator = GplearnFactorGenerator(m1.mix_df, config.TARGET_COLS, factor_manager)
    gplearn_factors = gplearn_generator.generate_factors(n_factors=10)
    
    # 保存因子
    if not os.path.exists(config.OUTDIR):
        os.makedirs(config.OUTDIR)
    
    # 保存因子文档
    # 注: 使用beautify_formula函数对公式进行美化，使其更易读
    # 美化包括：将sqrt转为√符号、log转为ln、exp转为e^等数学符号转换
    doc_data = []
    for factor in gplearn_factors:
        # 获取原始公式
        raw_formula = factor.calculation.__doc__ or factor.calculation.__name__
        # 应用美化函数
        beautified_formula = beautify_formula(raw_formula)
        
        doc_data.append({
            'name': factor.name,
            'category': factor.category.value,
            'description': factor.description,
            'formula': beautified_formula,  # 使用美化后的公式
            'dependencies': ', '.join(factor.dependencies),
            'source': factor.source
        })
    
    doc_df = pd.DataFrame(doc_data)
    doc_df.to_csv(os.path.join(config.OUTDIR, 'features/auto_gen/gplearn_factor_documentation.csv'), index=False, encoding='utf-8')

