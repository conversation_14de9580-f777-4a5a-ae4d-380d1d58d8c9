# -*- coding: utf-8 -*-
"""
Created on Sun Mar 23 17:28:55 2025

@author: lining
"""

#Close = ct
#Lowest = lt
#Highest = ht
#Volume = Vt

#Techinal Analysis
#
import numpy as np
import pandas as pd
from ...factor_manager import factor_manager, Factor, FactorCategory
from scipy.signal import savgol_filter
from scipy.linalg import inv
from scipy.signal import filtfilt

technical_factors2_cols = [
    "Accumulation_Distribution",
    "Awesome_Oscillator",
    "ADX",
    "ADX_DI_Plus",
    "ADX_DI_Minus",
    "ADX_DX",
    "ADXR",
    "ATR",
    "Alligator",
    "Alligator_teeth",
    "Alligator_lips",
    "APO",
    "Aroon_Indicator", # 
    "Aroon_Oscillator",
    "Bollinger_Bands",
    "Bollinger_Bands_Upper",
    "Bollinger_Bands_Lower",
    "Bollinger_Bands_Signal",
    "Ichimoku_Cloud",
    "Ichimoku_Cloud_Conversion",
    "Ichimoku_Cloud_Base",
    "Ichimoku_Cloud_Leading_B",
    "CMO",
    "Chaikin_Oscillator",
    "Chandelier_Exit",
    "Chandelier_Exit_Short",
    "Donchian_Channels_Upper",
    "Donchian_Channels_Middle",
    "Donchian_Channels_Lower",
    "COG",
    "DEMA",
    "DPO",
    # "Heikin_Ashi", 
    "Highest_High",
    "Lowest_Low",
    "HMA",
    "IBS",
    "Keltner_Channels_Upper",
    "Keltner_Channels_Middle",
    "Keltner_Channels_Lower",
    "MACD_Oscillator",
    "Median_Price",
    "Momentum",
    "VMA",
    "NATR",
    "PPO",
    "ROC",
    "RSI",
    "Stochastic_RSI",
    "Rising_SAR",
    "Falling_SAR",
    "Deviation",
    "Standard_Deviation",
    # "Fractals",
    # "Fractals_sell",
    "Linear_Regression_Line",
    "Rational_Transfer_Filter",
    "Savitzky_Golay_Filter",
    "Zero_Phase_Filter",
    "Remove_Offset",
    "Detrend_Least_Squares",
    "Beta_Calculation",
    "Support_Resistance"
]



# 列名映射定义，用于处理不同数据源可能的列名差异
COLUMN_MAPPING = {
    'high': ['High', 'high'],
    'low': ['Low', 'low'],
    'close': ['Close', 'close', 'LastPrice'],
    'open': ['Open', 'open'],
    'volume': ['Volume', 'volume', 'TotalValueTraded']
}

def get_column(df, col_name):
    """
    从DataFrame中按照优先级获取对应列名的数据
    
    参数:
    - df (pd.DataFrame): 输入的DataFrame
    - col_name (str): 要获取的列名（标准化名称）
    
    返回:
    - pd.Series: 对应的列数据
    
    如果找不到匹配的列，则抛出ValueError异常
    """
    for name in COLUMN_MAPPING.get(col_name.lower(), [col_name]):
        if name in df.columns:
            return df[name]
    raise ValueError(f"找不到与'{col_name}'对应的列，可用列: {df.columns.tolist()}")


# 基础工具函数
def SMA(series, window):
    """
    简单移动平均
    
    参数:
    - series: 输入序列
    - window: 窗口大小
    
    返回:
    - pd.Series: SMA值
    """
    return series.rolling(window).mean()

def EMA(series, window):
    """
    指数移动平均
    
    参数:
    - series: 输入序列
    - window: 窗口大小
    
    返回:
    - pd.Series: EMA值
    """
    return series.ewm(span=window, adjust=False).mean()

def WMA(series, window):
    """
    加权移动平均
    
    参数:
    - series: 输入序列
    - window: 窗口大小
    
    返回:
    - pd.Series: WMA值
    """
    weights = np.arange(1, window+1)
    return series.rolling(window).apply(lambda x: np.dot(x, weights)/weights.sum(), raw=True)


#print(data)

# 指标实现 -------------------------------------------------

def Accumulation_Distribution(df):
    """
    输入:
        df (pd.DataFrame): 包含high, low, close, volume的DataFrame
    输出:
        pd.Series: ADL指标值
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    volume = get_column(df, 'volume')
    
    money_flow_multiplier = ((close - low) - (high - close)) / (high - low).replace(0, 1e-5)
    money_flow_volume = money_flow_multiplier * volume
    return money_flow_volume.cumsum()

factor_manager.register_factor(Factor(
    name="Accumulation_Distribution",
    category=FactorCategory.TECHNICAL,
    description="累积/分配指标",
    calculation=Accumulation_Distribution,
    dependencies=['high', 'low', 'close', 'volume'],
    parameters={}
))

def Awesome_Oscillator(df, window1=5, window2=34):
    """
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        window1 (int): 短期窗口（默认5）
        window2 (int): 长期窗口（默认34）
    输出:
        pd.Series: AO指标值
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    mid_price = (high + low) / 2
    return SMA(mid_price, window1) - SMA(mid_price, window2)

factor_manager.register_factor(Factor(
    name="Awesome_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="Awesome Oscillator",
    calculation=Awesome_Oscillator,
    dependencies=['high', 'low'],
    parameters={'window1': 5, 'window2': 34}
))

def calculate_adx(df, period: int = 14):
    """
    计算 ADX（Average Directional Index）
    
    参数:
    - df: pd.DataFrame, 包含high, low, close的DataFrame
    - period: int, 平滑周期，默认 14
    
    返回:
    - pd.DataFrame，包含 +DI, -DI, DX, ADX
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    # 计算 +DM 和 -DM
    high_diff = high.diff()
    low_diff = -low.diff()
    
    # 只保留真正的 +DM 和 -DM
    plus_dm = pd.Series(0, index=high.index, dtype=float)
    minus_dm = pd.Series(0, index=high.index, dtype=float)
    
    valid_plus = (high_diff > low_diff) & (high_diff > 0)
    valid_minus = (low_diff > high_diff) & (low_diff > 0)
    
    # 使用显式类型转换避免FutureWarning
    plus_dm.loc[valid_plus] = high_diff.loc[valid_plus].astype(float)
    minus_dm.loc[valid_minus] = low_diff.loc[valid_minus].astype(float)
    
    # 计算 True Range (TR)
    prev_close = close.shift(1)
    tr = pd.DataFrame({
        'hl': high - low,
        'hc': (high - prev_close).abs(),
        'lc': (low - prev_close).abs()
    }).max(axis=1)
    
    # 使用指数平滑方法计算
    tr14 = tr.ewm(alpha=1/period, adjust=False).mean()
    plus_dm14 = plus_dm.ewm(alpha=1/period, adjust=False).mean()
    minus_dm14 = minus_dm.ewm(alpha=1/period, adjust=False).mean()
    
    # 计算 +DI14 和 -DI14
    plus_di14 = 100 * (plus_dm14 / tr14.replace(0, 1e-5))
    minus_di14 = 100 * (minus_dm14 / tr14.replace(0, 1e-5))
    
    # 计算 DX
    di_diff = (plus_di14 - minus_di14).abs()
    di_sum = plus_di14 + minus_di14
    dx = 100 * (di_diff / di_sum.replace(0, 1e-5))
    
    # 计算 ADX
    adx = dx.ewm(alpha=1/period, adjust=False).mean()
    
    # 计算 ADXR
    adxr = (adx + adx.shift(period)) / 2
    
    # 计算 ATR
    atr = tr.ewm(alpha=1/period, adjust=False).mean()
    
    return pd.DataFrame({
        '+DI14': plus_di14,
        '-DI14': minus_di14,
        'DX': dx,
        'ADX': adx,
        'ADXR': adxr,
        'ATR': atr
    })

def calculate_adx_adapter(df, period=14):
    """ADX指标适配器"""
    return calculate_adx(df, period)['ADX']

def calculate_adx_di_plus_adapter(df, period=14):
    """ADX正向动向指标(+DI)适配器"""
    return calculate_adx(df, period)['+DI14']

def calculate_adx_di_minus_adapter(df, period=14):
    """ADX负向动向指标(-DI)适配器"""
    return calculate_adx(df, period)['-DI14']

def calculate_adx_dx_adapter(df, period=14):
    """方向指数(DX)适配器"""
    return calculate_adx(df, period)['DX']

def calculate_adx_adxr_adapter(df, period=14):
    """ADX平均值(ADXR)适配器"""
    return calculate_adx(df, period)['ADXR']

factor_manager.register_factor(Factor(
    name="ADX",
    category=FactorCategory.TECHNICAL,
    description="平均方向指数（Average Directional Index）",
    calculation=calculate_adx_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADX_DI_Plus",
    category=FactorCategory.TECHNICAL,
    description="ADX正向动向指标（+DI）",
    calculation=calculate_adx_di_plus_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADX_DI_Minus",
    category=FactorCategory.TECHNICAL,
    description="ADX负向动向指标（-DI）",
    calculation=calculate_adx_di_minus_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADX_DX",
    category=FactorCategory.TECHNICAL,
    description="方向指数（DX）",
    calculation=calculate_adx_dx_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADXR",
    category=FactorCategory.TECHNICAL,
    description="ADX平均值（ADXR）",
    calculation=calculate_adx_adxr_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))
def ATR(df, period: int = 14):
    """
    计算 ATR（Average True Range）- 优化版本
    
    参数:
    - df: pd.DataFrame, 包含high, low, close的DataFrame
    - period: int, 平滑周期，默认 14
    
    返回:
    - pd.Series
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    # 计算前一日收盘价
    prev_close = close.shift(1)
    
    # 使用向量化操作计算真实波幅
    tr = pd.DataFrame({
        'hl': high - low,  # 当日最高价 - 当日最低价
        'hpc': (high - prev_close).abs(),  # 当日最高价 - 前一日收盘价的绝对值
        'lpc': (low - prev_close).abs()  # 当日最低价 - 前一日收盘价的绝对值
    }).max(axis=1)
    
    # 使用pandas的ewm函数实现Wilder平滑
    # Wilder的平滑相当于alpha=1/period的EMA
    # 注意：先计算period周期的SMA作为初始值
    atr = pd.Series(index=tr.index, dtype=float)
    atr.iloc[:period] = tr.iloc[:period].rolling(window=period, min_periods=1).mean()
    
    # 使用向量化操作计算后续值
    if len(tr) > period:
        atr.iloc[period:] = tr.iloc[period:].ewm(alpha=1/period, adjust=False).mean()
    
    return atr

def ATR_adapter(df, period=14):
    """
    ATR适配器函数
    
    参数:
    - df: pd.DataFrame, 包含high, low, close的DataFrame
    - period: int, 平滑周期，默认 14
    
    返回:
    - pd.Series: ATR值
    """
    return ATR(df, period)

factor_manager.register_factor(Factor(
    name="ATR",
    category=FactorCategory.TECHNICAL,
    description="平均真实波幅（Average True Range）",
    calculation=ATR_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

def Alligator(df, jaw=13, teeth=8, lips=5):
    """
    Williams Alligator Indicator
    
    参数:
    - df: pd.DataFrame, 包含high, low的DataFrame
    - jaw: int, 下颚线周期，默认13
    - teeth: int, 牙齿线周期，默认8
    - lips: int, 嘴唇线周期，默认5
    
    返回:
    - dict: 包含jaw, teeth, lips的字典
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    mid_price = (high + low) / 2
    return {
        'jaw': SMA(mid_price, jaw),
        'teeth': SMA(mid_price, teeth),
        'lips': SMA(mid_price, lips)
    }

def Alligator_adapter(df, jaw=13, teeth=8, lips=5):
    """
    Alligator颚线适配器函数
    
    参数:
    - df: pd.DataFrame, 包含high, low的DataFrame
    - jaw: int, 下颚线周期，默认13
    - teeth: int, 牙齿线周期，默认8
    - lips: int, 嘴唇线周期，默认5
    
    返回:
    - pd.Series: 颚线值
    """
    result = Alligator(df, jaw, teeth, lips)
    return result['jaw']

def Alligator_teeth_adapter(df, jaw=13, teeth=8, lips=5):
    """
    Alligator牙齿线适配器函数
    
    参数:
    - df: pd.DataFrame, 包含high, low的DataFrame
    - jaw: int, 下颚线周期，默认13
    - teeth: int, 牙齿线周期，默认8
    - lips: int, 嘴唇线周期，默认5
    
    返回:
    - pd.Series: 牙齿线值
    """
    result = Alligator(df, jaw, teeth, lips)
    return result['teeth']

def Alligator_lips_adapter(df, jaw=13, teeth=8, lips=5):
    """
    Alligator唇线适配器函数
    
    参数:
    - df: pd.DataFrame, 包含high, low的DataFrame
    - jaw: int, 下颚线周期，默认13
    - teeth: int, 牙齿线周期，默认8
    - lips: int, 嘴唇线周期，默认5
    
    返回:
    - pd.Series: 唇线值
    """
    result = Alligator(df, jaw, teeth, lips)
    return result['lips']

factor_manager.register_factor(Factor(
    name="Alligator",
    category=FactorCategory.TECHNICAL,
    description="威廉姆斯鳄鱼指标（Williams Alligator Indicator）- 颚线",
    calculation=Alligator_adapter,
    dependencies=['high', 'low'],
    parameters={'jaw': 13, 'teeth': 8, 'lips': 5}
))

factor_manager.register_factor(Factor(
    name="Alligator_teeth",
    category=FactorCategory.TECHNICAL,
    description="威廉姆斯鳄鱼指标（Williams Alligator Indicator）- 牙齿线",
    calculation=Alligator_teeth_adapter,
    dependencies=['high', 'low'],
    parameters={'jaw': 13, 'teeth': 8, 'lips': 5}
))

factor_manager.register_factor(Factor(
    name="Alligator_lips",
    category=FactorCategory.TECHNICAL,
    description="威廉姆斯鳄鱼指标（Williams Alligator Indicator）- 唇线",
    calculation=Alligator_lips_adapter,
    dependencies=['high', 'low'],
    parameters={'jaw': 13, 'teeth': 8, 'lips': 5}
))

def absolute_price_oscillator(df, fast_window=5, slow_window=13):
    """
    绝对价格振荡器 (APO)
    
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        fast_window (int): 快速EMA窗口 (默认5)
        slow_window (int): 慢速EMA窗口 (默认13)
        
    输出:
        pd.Series: APO指标值
        
    公式:
        M_t = (H_t + L_t)/2
        APO = EMA(M_t, fast_window) - EMA(M_t, slow_window)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    mid_price = (high + low) / 2
    apo = EMA(mid_price, fast_window) - EMA(mid_price, slow_window)
    return apo

factor_manager.register_factor(Factor(
    name="APO",
    category=FactorCategory.TECHNICAL,
    description="绝对价格振荡器（Absolute Price Oscillator）",
    calculation=absolute_price_oscillator,
    dependencies=['high', 'low'],
    parameters={'fast_window': 5, 'slow_window': 13}
))

def aroon_indicator(df, window=20):
    """
    Aroon指标 (包含上行和下行)
    
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        window (int): 计算窗口 (默认20)
        
    输出:
        pd.DataFrame: 包含'Aroon_Up'和'Aroon_Down'两列
        
    公式:
        Aroon_Up = (window - 最高价出现周期) / window * 100
        Aroon_Down = (window - 最低价出现周期) / window * 100
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    # 预先计算常量，避免重复计算
    window_f = float(window)
    scale_factor = 100.0 / window_f
    
    # 使用向量化操作代替apply函数
    high_idx = high.rolling(window).apply(lambda x: window - np.argmax(x) - 1, raw=True)
    low_idx = low.rolling(window).apply(lambda x: window - np.argmin(x) - 1, raw=True)
    
    # 直接乘以比例因子，避免除法操作
    aroon_up = high_idx * scale_factor
    aroon_down = low_idx * scale_factor
    
    # 使用预分配的DataFrame，避免连接操作
    result = pd.DataFrame({
        'Aroon_Up': aroon_up,
        'Aroon_Down': aroon_down
    })
    
    return result

factor_manager.register_factor(Factor(
    name="Aroon_Indicator",
    category=FactorCategory.TECHNICAL,
    description="Aroon指标（Aroon Indicator）",
    calculation=aroon_indicator,
    dependencies=['high', 'low'],
    parameters={'window': 20}
))

def aroon_oscillator(df, window=14):
    """
    Aroon振荡器
    
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        window (int): 计算窗口 (默认14)
        
    输出:
        pd.Series: Aroon Oscillator值
        
    公式:
        Aroon Oscillator = Aroon_Up - Aroon_Down
    """
    # 直接计算Aroon指标，避免创建中间DataFrame
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    # 预先计算常量
    window_f = float(window)
    scale_factor = 100.0 / window_f
    
    # 使用向量化操作
    high_idx = high.rolling(window).apply(lambda x: window - np.argmax(x) - 1, raw=True)
    low_idx = low.rolling(window).apply(lambda x: window - np.argmin(x) - 1, raw=True)
    
    # 直接计算振荡器，避免中间步骤
    return (high_idx - low_idx) * scale_factor

factor_manager.register_factor(Factor(
    name="Aroon_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="Aroon振荡器（Aroon Oscillator）",
    calculation=aroon_oscillator,
    dependencies=['high', 'low', 'close'],
    parameters={'window': 14}
))

def Bollinger_Bands(df, window=20, std_dev=2):
    """
    布林带指标
    
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        window (int): 窗口大小（默认20）
        std_dev (float): 标准差倍数（默认2）
    输出:
        dict: 包含上轨、中轨、下轨的字典以及信号
        
    信号生成规则:
        1. 价格突破上轨: 看跌信号 (1)
        2. 价格突破下轨: 看涨信号 (-1)
        3. 价格在通道内: 中性信号 (0)
        4. 带宽扩大: 波动性增加
        5. 带宽收窄: 波动性减少，可能即将突破
    """
    close = get_column(df, 'close')
    sma = SMA(close, window)
    std = close.rolling(window).std()
    
    upper = sma + std_dev * std
    middle = sma
    lower = sma - std_dev * std
    
    # 计算带宽
    bandwidth = (upper - lower) / middle
    
    # 生成信号 - 使用float类型而非int避免类型不兼容警告
    signal = pd.Series(0.0, index=close.index)  # 默认为中性信号，使用浮点类型
    signal[close > upper] = close[close > upper] - upper[close > upper]  # 价格突破上轨，看跌信号
    signal[close < lower] = close[close < lower] - lower[close < lower]  # 价格突破下轨，看涨信号
    
    # 计算带宽变化率
    bandwidth_pct_change = bandwidth.pct_change(periods=5)
    
    return {
        'upper': upper,
        'middle': middle,
        'lower': lower,
        'signal': signal,
        'bandwidth': bandwidth,
        'bandwidth_change': bandwidth_pct_change
    }

def Bollinger_Bands_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['middle']

def Bollinger_Bands_upper_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['upper']

def Bollinger_Bands_lower_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['lower']

factor_manager.register_factor(Factor(
    name="Bollinger_Bands",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 中轨",
    calculation=Bollinger_Bands_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))

factor_manager.register_factor(Factor(
    name="Bollinger_Bands_Upper",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 上轨",
    calculation=Bollinger_Bands_upper_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))

factor_manager.register_factor(Factor(
    name="Bollinger_Bands_Lower",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 下轨",
    calculation=Bollinger_Bands_lower_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))

def Bollinger_Bands_signal_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['signal']

factor_manager.register_factor(Factor(
    name="Bollinger_Bands_Signal",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 信号",
    calculation=Bollinger_Bands_signal_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))


def Ichimoku_Cloud(df, conversion=9, base=26, leading_span=52):
    """
    一目均衡表
    
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        conversion (int): 转换线周期（默认9）
        base (int): 基准线周期（默认26）
        leading_span (int): 前导跨度周期（默认52）
    输出:
        dict: 包含转换线、基准线、前导跨度A和B的字典
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    conversion_line = (high.rolling(conversion).max() + low.rolling(conversion).min()) / 2
    base_line = (high.rolling(base).max() + low.rolling(base).min()) / 2
    leading_span_a = (conversion_line + base_line) / 2
    leading_span_b = (high.rolling(leading_span).max() + low.rolling(leading_span).min()) / 2
    return {
        'conversion': conversion_line,
        'base': base_line,
        'leading_a': leading_span_a,
        'leading_b': leading_span_b
    }

def Ichimoku_Cloud_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['leading_a']

def Ichimoku_Cloud_conversion_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['conversion']

def Ichimoku_Cloud_base_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['base']

def Ichimoku_Cloud_leading_b_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['leading_b']

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 前导跨度A",
    calculation=Ichimoku_Cloud_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud_Conversion",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 转换线",
    calculation=Ichimoku_Cloud_conversion_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud_Base",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 基准线",
    calculation=Ichimoku_Cloud_base_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud_Leading_B",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 前导跨度B",
    calculation=Ichimoku_Cloud_leading_b_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

def Chande_Momentum_Oscillator(df, period=19):
    """
    Chande动量振荡器
    
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期（默认19）
    输出:
        pd.Series: CMO值
    """
    close = get_column(df, 'close')
    delta = close.diff()
    su = delta.where(delta > 0, 0).rolling(window=period).sum()
    sd = (-delta.where(delta < 0, 0)).rolling(window=period).sum()
    cmo = 100 * (su - sd) / (su + sd).replace(0, 1e-5)
    return cmo

factor_manager.register_factor(Factor(
    name="CMO",
    category=FactorCategory.TECHNICAL,
    description="Chande动量振荡器（Chande Momentum Oscillator）",
    calculation=Chande_Momentum_Oscillator,
    dependencies=['close'],
    parameters={'period': 19}
))

def Chaikin_Oscillator(df):
    """
    Chaikin振荡器
    
    输入:
        df (pd.DataFrame): 包含high, low, close, volume的DataFrame
    输出:
        pd.Series: Chaikin值
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    volume = get_column(df, 'volume')
    
    # 计算ADL
    mfm = ((close - low) - (high - close)) / (high - low).replace(0, 1e-5)
    mfv = mfm * volume
    adl = mfv.cumsum()
    
    # 计算EMA
    ema3 = adl.ewm(span=3, adjust=False).mean()
    ema10 = adl.ewm(span=10, adjust=False).mean()
    return ema3 - ema10

factor_manager.register_factor(Factor(
    name="Chaikin_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="Chaikin振荡器（Chaikin Oscillator）",
    calculation=Chaikin_Oscillator,
    dependencies=['high', 'low', 'close', 'volume'],
    parameters={}
))

# Chandelier Exit
def Chandelier_Exit(df, atr_period=22, mult=3):
    """
    Chandelier离场指标
    输入:
        df (pd.DataFrame): 包含high, low, close的DataFrame
        atr_period (int): ATR周期（默认22）
        mult (int): ATR倍数（默认3）
    输出:
        tuple: (长线离场值, 短线离场值)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    # 直接计算最大值和最小值，避免多次调用rolling
    h22 = high.rolling(window=atr_period).max()
    l22 = low.rolling(window=atr_period).min()
    
    # 直接使用calculate_adx中的ATR计算结果，避免重复计算
    tr = pd.DataFrame({
        'hl': high - low,
        'hc': (high - get_column(df, 'close').shift(1)).abs(),
        'lc': (low - get_column(df, 'close').shift(1)).abs()
    }).max(axis=1)
    
    # 使用ewm直接计算ATR，避免调用ATR函数的开销
    atr = tr.ewm(alpha=1/atr_period, adjust=False).mean()
    
    # 向量化计算，避免循环
    long_exit = h22 - atr * mult
    short_exit = l22 + atr * mult
    
    return long_exit, short_exit

def Chandelier_Exit_adapter(df, atr_period=22, mult=3):
    """长线离场值适配器"""
    long_exit, _ = Chandelier_Exit(df, atr_period, mult)
    return long_exit

def Chandelier_Exit_short_adapter(df, atr_period=22, mult=3):
    """短线离场值适配器"""
    _, short_exit = Chandelier_Exit(df, atr_period, mult)
    return short_exit

factor_manager.register_factor(Factor(
    name="Chandelier_Exit",
    category=FactorCategory.TECHNICAL,
    description="Chandelier离场指标（Chandelier Exit）- 长线离场值",
    calculation=Chandelier_Exit_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'atr_period': 22, 'mult': 3}
))

factor_manager.register_factor(Factor(
    name="Chandelier_Exit_Short",
    category=FactorCategory.TECHNICAL,
    description="Chandelier离场指标（Chandelier Exit）- 短线离场值",
    calculation=Chandelier_Exit_short_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'atr_period': 22, 'mult': 3}
))
# Donchian Channels
def Donchian_Channels(df, period=20):
    """
    Donchian通道
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        period (int): 计算周期（默认20）
    输出:
        tuple: (上轨, 中轨, 下轨)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    upper = high.rolling(window=period).max()
    lower = low.rolling(window=period).min()
    middle = (upper + lower) / 2
    return upper, middle, lower

def Donchian_Channels_upper_adapter(df, period=20):
    upper, _, _ = Donchian_Channels(df, period)
    return upper

def Donchian_Channels_middle_adapter(df, period=20):
    _, middle, _ = Donchian_Channels(df, period)
    return middle

def Donchian_Channels_lower_adapter(df, period=20):
    _, _, lower = Donchian_Channels(df, period)
    return lower

factor_manager.register_factor(Factor(
    name="Donchian_Channels_Upper",
    category=FactorCategory.TECHNICAL,
    description="唐奇安通道上轨（Donchian Channels Upper）",
    calculation=Donchian_Channels_upper_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

factor_manager.register_factor(Factor(
    name="Donchian_Channels_Middle",
    category=FactorCategory.TECHNICAL,
    description="唐奇安通道中轨（Donchian Channels Middle）",
    calculation=Donchian_Channels_middle_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

factor_manager.register_factor(Factor(
    name="Donchian_Channels_Lower",
    category=FactorCategory.TECHNICAL,
    description="唐奇安通道下轨（Donchian Channels Lower）",
    calculation=Donchian_Channels_lower_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

def Center_of_Gravity_Oscillator(df, r=0.6):
    """
    重心振荡器 (Center of Gravity Oscillator)
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        r (float): 权重系数（默认为0.6，可根据需求调整）
    输出:
        pd.Series: COG值
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    # 计算中间价 M_t = (high + low) / 2
    M_t = (high + low) / 2
    M_t_1 = M_t.shift(1)  # 前一期中间价
    
    # 计算COG: -(M_t + r*M_t_1) / (M_t + M_t_1)
    numerator = -(M_t + r * M_t_1)
    denominator = (M_t + M_t_1).replace(0, 1e-5)  # 避免除零错误
    cog = numerator / denominator
    
    return cog

factor_manager.register_factor(Factor(
    name="COG",
    category=FactorCategory.TECHNICAL,
    description="重心振荡器（Center of Gravity Oscillator）",
    calculation=Center_of_Gravity_Oscillator,
    dependencies=['high', 'low'],
    parameters={'r': 0.6}
))

# Double Exponential Moving Average (DEMA)
def DEMA(df, period=20):
    """
    双指数移动平均 (Double Exponential Moving Average)
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        period (int): 计算周期（默认20）
    输出:
        pd.Series: DEMA值
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    M_t = (high + low) / 2  # 中间价
    ema1 = M_t.ewm(span=period, adjust=False).mean()
    ema2 = ema1.ewm(span=period, adjust=False).mean()
    return 2 * ema1 - ema2

factor_manager.register_factor(Factor(
    name="DEMA",
    category=FactorCategory.TECHNICAL,
    description="双指数移动平均（Double Exponential Moving Average）",
    calculation=DEMA,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

# Detrended Price Oscillator (DPO)
def DPO(df, period=10):
    """
    去趋势价格振荡器 (Detrended Price Oscillator)
    输入:
        df (pd.DataFrame): 包含high, close的DataFrame
        period (int): 计算周期（默认10）
    输出:
        pd.Series: DPO值
    """
    high = get_column(df, 'high')
    close = get_column(df, 'close')
    
    H_high = high.rolling(window=period).max()
    SMA_close = close.rolling(window=period).mean()
    return (H_high / (period + 2)) - SMA_close

factor_manager.register_factor(Factor(
    name="DPO",
    category=FactorCategory.TECHNICAL,
    description="去趋势价格振荡器（Detrended Price Oscillator）",
    calculation=DPO,
    dependencies=['high', 'close'],
    parameters={'period': 10}
))

def Heikin_Ashi(df):
    """
    平均K线 (Heikin-Ashi Candles)
    输入:
        df (pd.DataFrame): 包含open, high, low, close的DataFrame
    输出:
        pd.DataFrame: Heikin-Ashi四列(open, high, low, close)
    """
    open_p = get_column(df, 'open')
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    ha_close = (open_p + high + low + close) / 4
    ha_open = (open_p.shift(1) + close.shift(1)) / 2
    ha_open.iloc[0] = open_p.iloc[0]  # 首项处理
    
    ha_high = pd.concat([high, ha_open, ha_close], axis=1).max(axis=1)
    ha_low = pd.concat([low, ha_open, ha_close], axis=1).min(axis=1)
    
    return pd.DataFrame({
        'HA_open': ha_open,
        'HA_high': ha_high,
        'HA_low': ha_low,
        'HA_close': ha_close
    })

factor_manager.register_factor(Factor(
    name="Heikin_Ashi",
    category=FactorCategory.TECHNICAL,
    description="平均K线（Heikin-Ashi Candles）",
    calculation=Heikin_Ashi,
    dependencies=['open', 'high', 'low', 'close'],
    parameters={}
))

def Highest_High_Lowest_Low(df, period=20):
    """
    最高高价和最低低价通道
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        period (int): 计算周期（默认20）
    输出:
        tuple: (最高高价序列, 最低低价序列)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    return (
        high.rolling(window=period).max(),
        low.rolling(window=period).min()
    )

def Highest_High_adapter(df, period=20):
    highest, _ = Highest_High_Lowest_Low(df, period)
    return highest

def Lowest_Low_adapter(df, period=20):
    _, lowest = Highest_High_Lowest_Low(df, period)
    return lowest

factor_manager.register_factor(Factor(
    name="Highest_High",
    category=FactorCategory.TECHNICAL,
    description="最高高价（Highest High）",
    calculation=Highest_High_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

factor_manager.register_factor(Factor(
    name="Lowest_Low",
    category=FactorCategory.TECHNICAL,
    description="最低低价（Lowest Low）",
    calculation=Lowest_Low_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

def Hull_MA(df, period=10):
    """
    赫尔移动平均线 (Hull Moving Average)
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        period (int): 计算周期（默认10）
    输出:
        pd.Series: Hull MA值
    """
    close = get_column(df, 'close')
    
    # 直接使用收盘价计算，避免多次计算平均值
    wma_half_period = close.rolling(window=period//2).apply(lambda x: np.average(x, weights=range(1, len(x)+1)), raw=True)
    wma_full = close.rolling(window=period).apply(lambda x: np.average(x, weights=range(1, len(x)+1)), raw=True)
    
    # 使用向量化操作加速计算
    diff = 2 * wma_half_period - wma_full
    sqrt_period = int(np.sqrt(period))
    wma_half = diff.rolling(window=sqrt_period).apply(lambda x: np.average(x, weights=range(1, len(x)+1)), raw=True)
    
    return wma_half

factor_manager.register_factor(Factor(
    name="HMA",
    category=FactorCategory.TECHNICAL,
    description="赫尔移动平均线（Hull Moving Average）",
    calculation=Hull_MA,
    dependencies=['high', 'low'],
    parameters={'period': 10}
))

def IBS(df):
    """
    内部柱强度 (Internal Bar Strength)
    输入:
        df (pd.DataFrame): 包含high, low, close的DataFrame
    输出:
        pd.Series: IBS值 (范围0-1)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    return (close - low) / (high - low).replace(0, 1e-5)

factor_manager.register_factor(Factor(
    name="IBS",
    category=FactorCategory.TECHNICAL,
    description="内部柱强度（Internal Bar Strength）",
    calculation=IBS,
    dependencies=['high', 'low', 'close'],
    parameters={}
))

def Keltner_Channels(df, ema_period=20, atr_period=10, multiplier=2):
    """
    凯尔特纳通道 (Keltner Channels)
    输入:
        df (pd.DataFrame): 包含high, low, close的DataFrame
        ema_period (int): 中轨EMA周期（默认20）
        atr_period (int): ATR周期（默认10）
        multiplier (int): ATR倍数（默认2）
    输出:
        tuple: (上轨, 中轨, 下轨)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    # 计算中轨（使用收盘价EMA）
    middle = close.ewm(span=ema_period, adjust=False).mean()
    
    # 计算ATR
    atr = ATR(df, atr_period)
    
    upper = middle + multiplier * atr
    lower = middle - multiplier * atr
    return upper, middle, lower

def Keltner_Channels_upper_adapter(df, ema_period=20, atr_period=10, multiplier=2):
    upper, _, _ = Keltner_Channels(df, ema_period, atr_period, multiplier)
    return upper

def Keltner_Channels_middle_adapter(df, ema_period=20, atr_period=10, multiplier=2):
    _, middle, _ = Keltner_Channels(df, ema_period, atr_period, multiplier)
    return middle

def Keltner_Channels_lower_adapter(df, ema_period=20, atr_period=10, multiplier=2):
    _, _, lower = Keltner_Channels(df, ema_period, atr_period, multiplier)
    return lower

factor_manager.register_factor(Factor(
    name="Keltner_Channels_Upper",
    category=FactorCategory.TECHNICAL,
    description="凯尔特纳通道上轨（Keltner Channels Upper）",
    calculation=Keltner_Channels_upper_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'ema_period': 20, 'atr_period': 10, 'multiplier': 2}
))

factor_manager.register_factor(Factor(
    name="Keltner_Channels_Middle",
    category=FactorCategory.TECHNICAL,
    description="凯尔特纳通道中轨（Keltner Channels Middle）",
    calculation=Keltner_Channels_middle_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'ema_period': 20, 'atr_period': 10, 'multiplier': 2}
))

factor_manager.register_factor(Factor(
    name="Keltner_Channels_Lower",
    category=FactorCategory.TECHNICAL,
    description="凯尔特纳通道下轨（Keltner Channels Lower）",
    calculation=Keltner_Channels_lower_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'ema_period': 20, 'atr_period': 10, 'multiplier': 2}
))

# MACD Oscillator (基于高低均价)
def MACD_Oscillator(df, fast_period=12, slow_period=26):
    """
    MACD振荡器（基于高低均价）
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        fast_period (int): 快线周期（默认12）
        slow_period (int): 慢线周期（默认26）
    输出:
        pd.Series: MACD值
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    ahl = (high + low) / 2  # Average High-Low
    macd = ahl.ewm(span=fast_period, adjust=False).mean() - ahl.ewm(span=slow_period, adjust=False).mean()
    return macd

factor_manager.register_factor(Factor(
    name="MACD_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="MACD振荡器（基于高低均价）",
    calculation=MACD_Oscillator,
    dependencies=['high', 'low'],
    parameters={'fast_period': 12, 'slow_period': 26}
))

def Median_Price(df):
    """
    中间价 (Median Price)
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
    输出:
        pd.Series: 中间价序列
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    return (high + low) / 2

factor_manager.register_factor(Factor(
    name="Median_Price",
    category=FactorCategory.TECHNICAL,
    description="中间价（Median Price）",
    calculation=Median_Price,
    dependencies=['high', 'low'],
    parameters={}
))

# Momentum
def Momentum(df, period=1):
    """
    动量指标 (Momentum)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期（默认1）
    输出:
        pd.Series: 动量值
    """
    close = get_column(df, 'close')
    
    return close.diff(periods=period)

factor_manager.register_factor(Factor(
    name="Momentum",
    category=FactorCategory.TECHNICAL,
    description="动量指标（Momentum）",
    calculation=Momentum,
    dependencies=['close'],
    parameters={'period': 1}
))

def Variable_MA(df, period=3):
    """
    可变移动平均线 (Variable Moving Average)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期（默认3）
    输出:
        pd.Series: VMA值
    """
    close = get_column(df, 'close')
    
    # 初始化结果序列
    vma = pd.Series(index=close.index, dtype=float)
    vma.iloc[:period] = np.nan
    
    # 计算效率比率(ER)
    direction = abs(close - close.shift(period))
    volatility = abs(close.diff()).rolling(window=period).sum()
    er = direction / volatility.replace(0, 1e-5)
    
    # 计算自适应权重
    alpha = 2 / (period + 1)
    weights = alpha * er
    
    # 使用循环计算VMA以避免索引越界问题
    for i in range(period, len(close)):
        if i < period:
            continue
            
        window_data = close.iloc[i-period+1:i+1]
        window_weights = weights.iloc[i-period+1:i+1]
        
        # 确保权重和数据长度一致
        if len(window_data) == len(window_weights):
            sum_w = window_weights.sum()
            if sum_w > 0:
                vma.iloc[i] = (window_data * window_weights).sum() / sum_w
            else:
                vma.iloc[i] = window_data.mean()
    
    return vma

factor_manager.register_factor(Factor(
    name="VMA",
    category=FactorCategory.TECHNICAL,
    description="可变移动平均线（Variable Moving Average）",
    calculation=Variable_MA,
    dependencies=['close'],
    parameters={'period': 3}
))

def NATR(df, atr_period=14):
    """
    标准化平均真实波幅 (Normalized ATR)
    输入:
        df (pd.DataFrame): 包含high, low, close的DataFrame
        atr_period (int): ATR计算周期（默认14）
    输出:
        pd.Series: NATR值（百分比）
    """
    close = get_column(df, 'close')
    atr = ATR(df, atr_period)
    
    return (atr / close) * 100

factor_manager.register_factor(Factor(
    name="NATR",
    category=FactorCategory.TECHNICAL,
    description="标准化平均真实波幅（Normalized ATR）",
    calculation=NATR,
    dependencies=['high', 'low', 'close'],
    parameters={'atr_period': 14}
))

# Percentage Price Oscillator (PPO)
def PPO(df, fast_period=12, slow_period=26):
    """
    百分比价格振荡器 (Percentage Price Oscillator)
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        fast_period (int): 快线周期（默认12）
        slow_period (int): 慢线周期（默认26）
    输出:
        pd.Series: PPO值（百分比）
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    ahl = (high + low) / 2
    macd = ahl.ewm(span=fast_period, adjust=False).mean() - ahl.ewm(span=slow_period, adjust=False).mean()
    slow_ema = ahl.ewm(span=slow_period, adjust=False).mean()
    return (macd / slow_ema.replace(0, 1e-5)) * 100

factor_manager.register_factor(Factor(
    name="PPO",
    category=FactorCategory.TECHNICAL,
    description="百分比价格振荡器（Percentage Price Oscillator）",
    calculation=PPO,
    dependencies=['high', 'low'],
    parameters={'fast_period': 12, 'slow_period': 26}
))

def ROC(df, period=12):  
    """
    变动率指标 (Rate of Change)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期（默认12）
    输出:
        pd.Series: ROC值（百分比）
    """
    close = get_column(df, 'close')
    
    return ((close - close.shift(period)) / close.shift(period).replace(0, 1e-5)) * 100

factor_manager.register_factor(Factor(
    name="ROC",
    category=FactorCategory.TECHNICAL,
    description="变动率指标（Rate of Change）",
    calculation=ROC,
    dependencies=['close'],
    parameters={'period': 12}
))

def RSI(df, period=14):
    """
    相对强弱指数 (Relative Strength Index)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期（默认14）
    输出:
        pd.Series: RSI值（百分比）
    """
    close = get_column(df, 'close')
    
    delta = close.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss.replace(0, 1e-5)
    return 100 - (100 / (1 + rs))

factor_manager.register_factor(Factor(
    name="RSI",
    category=FactorCategory.TECHNICAL,
    description="相对强弱指数（Relative Strength Index）",
    calculation=RSI,
    dependencies=['close'],
    parameters={'period': 14}
))

def Stochastic_RSI(df, rsi_period=14, stoch_period=10):
    """
    随机相对强弱指数 (Stochastic RSI)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        rsi_period (int): RSI计算周期（默认14）
        stoch_period (int): 随机周期（默认10）
    输出:
        pd.Series: 随机RSI值
    """
    rsi_values = RSI(df, rsi_period)
    min_rsi = rsi_values.rolling(window=stoch_period).min()
    max_rsi = rsi_values.rolling(window=stoch_period).max()
    return (rsi_values - min_rsi) / (max_rsi - min_rsi).replace(0, 1e-5)

factor_manager.register_factor(Factor(
    name="Stochastic_RSI",
    category=FactorCategory.TECHNICAL,
    description="随机相对强弱指数（Stochastic RSI）",
    calculation=Stochastic_RSI,
    dependencies=['close'],
    parameters={'rsi_period': 14, 'stoch_period': 10}
))
def Rising_Falling_SAR(df, af_start=0.02, af_step=0.02, af_max=0.2):
    """
    分别计算上升和下降的SAR值（无自动反转逻辑）
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
        af_start (float): 初始加速因子(默认0.02)
        af_step (float): 加速因子增量(默认0.02)
        af_max (float): 最大加速因子(默认0.2)
    输出:
        tuple: (Rising_SAR序列, Falling_SAR序列)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    # 获取数据长度，预分配内存
    length = len(high)
    rising_sar = np.zeros(length)
    falling_sar = np.zeros(length)
    
    # 初始值（假设第一日为上升趋势起点）
    rising_sar[0] = low.iloc[0]
    falling_sar[0] = high.iloc[0]
    
    # 上升趋势参数
    rising_ep = high.iloc[0]  # 极值点（最高价）
    rising_af = af_start      # 加速因子
    
    # 下降趋势参数
    falling_ep = low.iloc[0]  # 极值点（最低价）
    falling_af = af_start
    
    # 转换为numpy数组以加速计算
    high_values = high.values
    low_values = low.values
    
    for i in range(1, length):
        # --- 计算上升SAR ---
        rising_sar[i] = rising_sar[i-1] + rising_af * (rising_ep - rising_sar[i-1])
        # 更新极值点
        if high_values[i] > rising_ep:
            rising_ep = high_values[i]
            rising_af = min(rising_af + af_step, af_max)
        # 确保SAR不超过最低价
        rising_sar[i] = min(rising_sar[i], low_values[i-1], low_values[i])
        
        # --- 计算下降SAR ---
        falling_sar[i] = falling_sar[i-1] - falling_af * (falling_sar[i-1] - falling_ep)
        # 更新极值点
        if low_values[i] < falling_ep:
            falling_ep = low_values[i]
            falling_af = min(falling_af + af_step, af_max)
        # 确保SAR不低于最高价
        falling_sar[i] = max(falling_sar[i], high_values[i-1], high_values[i])
    
    # 转换回pandas Series
    return pd.Series(rising_sar, index=high.index), pd.Series(falling_sar, index=high.index)

def Rising_SAR_adapter(df, af_start=0.02, af_step=0.02, af_max=0.2):
    rising_sar, _ = Rising_Falling_SAR(df, af_start, af_step, af_max)
    return rising_sar

def Falling_SAR_adapter(df, af_start=0.02, af_step=0.02, af_max=0.2):
    _, falling_sar = Rising_Falling_SAR(df, af_start, af_step, af_max)
    return falling_sar

factor_manager.register_factor(Factor(
    name="Rising_SAR",
    category=FactorCategory.TECHNICAL,
    description="上升抛物线转向指标（Rising Parabolic SAR）",
    calculation=Rising_SAR_adapter,
    dependencies=['high', 'low'],
    parameters={'af_start': 0.02, 'af_step': 0.02, 'af_max': 0.2}
))

factor_manager.register_factor(Factor(
    name="Falling_SAR",
    category=FactorCategory.TECHNICAL,
    description="下降抛物线转向指标（Falling Parabolic SAR）",
    calculation=Falling_SAR_adapter,
    dependencies=['high', 'low'],
    parameters={'af_start': 0.02, 'af_step': 0.02, 'af_max': 0.2}
))

def Standard_Deviation(df, period=10):
    """
    标准偏差指标 (Standard Deviation)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期（默认10）
    输出:
        tuple: (偏差序列, 标准偏差序列)
    """
    close = get_column(df, 'close')
    
    sma = close.rolling(window=period).mean()
    deviation = close - sma
    squared_deviation = deviation.pow(2)
    std = np.sqrt(squared_deviation.rolling(window=period).mean())
    return deviation, std

def Deviation_adapter(df, period=10):
    deviation, _ = Standard_Deviation(df, period)
    return deviation

def Std_adapter(df, period=10):
    _, std = Standard_Deviation(df, period)
    return std

factor_manager.register_factor(Factor(
    name="Deviation",
    category=FactorCategory.TECHNICAL,
    description="价格偏差（Price Deviation）",
    calculation=Deviation_adapter,
    dependencies=['close'],
    parameters={'period': 10}
))

factor_manager.register_factor(Factor(
    name="Standard_Deviation",
    category=FactorCategory.TECHNICAL,
    description="标准偏差（Standard Deviation）",
    calculation=Std_adapter,
    dependencies=['close'],
    parameters={'period': 10}
))

def Fractals(df):
    """
    分形指标 (Fractals)
    输入:
        df (pd.DataFrame): 包含high, low的DataFrame
    输出:
        tuple: (买分形序列, 卖分形序列)
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    # 初始化输出序列
    buy_fractals = pd.Series(False, index=high.index)
    sell_fractals = pd.Series(False, index=low.index)
    
    # 使用向量化操作代替循环，提高计算速度
    high_shifted = pd.DataFrame({
        'i-2': high.shift(2),
        'i-1': high.shift(1),
        'i': high,
        'i+1': high.shift(-1),
        'i+2': high.shift(-2)
    })
    
    low_shifted = pd.DataFrame({
        'i-2': low.shift(2),
        'i-1': low.shift(1),
        'i': low,
        'i+1': low.shift(-1),
        'i+2': low.shift(-2)
    })
    
    # 买分形条件（中间最高价高于前后两个）,true返回1，false返回0
    buy_fractals = (high_shifted['i'] > high_shifted['i-2']) & \
                   (high_shifted['i'] > high_shifted['i-1']) & \
                   (high_shifted['i'] > high_shifted['i+1']) & \
                   (high_shifted['i'] > high_shifted['i+2'])
    
    # 卖分形条件（中间最低价低于前后两个）,true返回1，false返回0
    sell_fractals = (low_shifted['i'] < low_shifted['i-2']) & \
                    (low_shifted['i'] < low_shifted['i-1']) & \
                    (low_shifted['i'] < low_shifted['i+1']) & \
                    (low_shifted['i'] < low_shifted['i+2'])
                    
    # 将布尔值转换为浮点数
    buy_fractals = buy_fractals.astype(float)
    sell_fractals = sell_fractals.astype(float)
    
    # 返回结果
    return buy_fractals, sell_fractals

def Fractals_buy_adapter(df):
    """
    分形指标买入信号适配器
    """
    buy_fractals, _ = Fractals(df)
    return buy_fractals.astype(float)

def Fractals_sell_adapter(df):
    """
    分形指标卖出信号适配器
    """
    _, sell_fractals = Fractals(df)
    return sell_fractals.astype(float)

factor_manager.register_factor(Factor(
    name="Fractals",
    category=FactorCategory.TECHNICAL,
    description="分形指标（Fractals）- 买分形",
    calculation=Fractals_buy_adapter,
    dependencies=['high', 'low'],
    parameters={}
))

factor_manager.register_factor(Factor(
    name="Fractals_sell",
    category=FactorCategory.TECHNICAL,
    description="分形指标（Fractals）- 卖分形",
    calculation=Fractals_sell_adapter,
    dependencies=['high', 'low'],
    parameters={}
))

def Linear_Regression_Line(df, period=10):
    """
    线性回归线 (Linear Regression Line) - 高性能实现
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期
    输出:
        pd.Series: 回归预测值（PV）
    """
    close = get_column(df, 'close')
    
    # 预计算常量以避免重复计算
    x = np.arange(period)
    x_mean = (period - 1) / 2  # 等差数列平均值简化计算
    x_minus_mean = x - x_mean
    x_minus_mean_squared_sum = np.sum(x_minus_mean ** 2)  # 等差数列平方和公式: period*(period^2-1)/12
    
    # 使用NumPy的卷积操作进行快速滚动计算
    y_rolling_sum = np.convolve(close, np.ones(period), mode='valid')
    y_rolling_mean = y_rolling_sum / period
    
    # 初始化结果数组
    result = pd.Series(index=close.index, dtype=float)
    valid_indices = close.index[period-1:]
    
    if len(valid_indices) > 0:
        # 使用矩阵运算计算协方差
        y_windows = np.array([close.iloc[i-(period-1):i+1].values for i in range(period-1, len(close))])
        y_means = y_rolling_mean
        
        # 广播计算每个窗口的协方差
        y_minus_mean = y_windows - y_means[:, np.newaxis]
        covs = np.sum(x_minus_mean * y_minus_mean, axis=1)
        
        # 计算斜率和截距
        slopes = np.divide(covs, x_minus_mean_squared_sum, out=np.zeros_like(covs), where=x_minus_mean_squared_sum!=0)
        intercepts = y_means - slopes * x_mean
        
        # 计算预测值
        predictions = intercepts + slopes * (period - 1)
        
        # 将结果填充到Series中
        result.loc[valid_indices] = predictions
    
    return result

factor_manager.register_factor(Factor(
    name="Linear_Regression_Line",
    category=FactorCategory.TECHNICAL,
    description="线性回归线（Linear Regression Line）",
    calculation=Linear_Regression_Line,
    dependencies=['close'],
    parameters={'period': 10}
))

def Rational_Transfer_Filter(df, numerator_coeffs=[1.0], denominator_coeffs=[1.0], lookback=10):
    """
    有理传递函数滤波器 (Rational Transfer Function)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        numerator_coeffs (list): 分子系数（默认[1.0]）
        denominator_coeffs (list): 分母系数（默认[1.0]）
        lookback (int): 回溯周期（默认10）
    输出:
        pd.Series: 滤波后输出
    """
    from scipy.signal import lfilter
    close = get_column(df, 'close')
    
    # 直接对整个序列应用滤波器，避免逐点计算
    filtered_values = lfilter(numerator_coeffs, denominator_coeffs, close.values)
    
    # 创建结果Series，前lookback个值设为NaN
    output = pd.Series(np.nan, index=close.index)
    output.iloc[lookback:] = filtered_values[lookback:]
    
    return output

factor_manager.register_factor(Factor(
    name="Rational_Transfer_Filter",
    category=FactorCategory.TECHNICAL,
    description="有理传递函数滤波器（Rational Transfer Function）",
    calculation=Rational_Transfer_Filter,
    dependencies=['close'],
    parameters={'numerator_coeffs': [1.0], 'denominator_coeffs': [1.0], 'lookback': 10}
))

def Savitzky_Golay_Filter(df, window_length=5, poly_order=2, weights=None):
    """
    Savitzky-Golay数字滤波器 (Savitzky-Golay Filter)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        window_length (int): 窗口长度（默认5）
        poly_order (int): 多项式阶数（默认2）
        weights (array): 权重向量（默认None，即等权重）
    输出:
        pd.Series: 滤波后信号
    """
    from scipy.signal import savgol_filter
    
    series = get_column(df, 'close')
    
    # 参数校验
    if window_length % 2 == 0:
        raise ValueError("窗口长度必须是奇数")
    if poly_order >= window_length:
        raise ValueError("多项式阶数必须小于窗口长度")
    
    # 使用scipy的savgol_filter直接计算，避免手动实现中的广播错误
    # 处理NaN值
    valid_mask = ~np.isnan(series.values)
    result = pd.Series(index=series.index, dtype=float)
    
    if np.sum(valid_mask) >= window_length:  # 确保有足够的有效数据点
        # 只对有效数据应用滤波器
        valid_data = series.values[valid_mask]
        filtered_valid = savgol_filter(valid_data, window_length, poly_order)
        
        # 将结果放回原始位置
        result_array = np.full_like(series.values, np.nan)
        result_array[valid_mask] = filtered_valid
        result = pd.Series(result_array, index=series.index)
    
    return result

factor_manager.register_factor(Factor(
    name="Savitzky_Golay_Filter",
    category=FactorCategory.TECHNICAL,
    description="Savitzky-Golay数字滤波器（Savitzky-Golay Filter）",
    calculation=Savitzky_Golay_Filter,
    dependencies=['close'],
    parameters={'window_length': 5, 'poly_order': 2, 'weights': None}
))

def Zero_Phase_Filter(df, window_length=5, polyorder=2):
    """
    零相位滤波器 (双向Savitzky-Golay滤波)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        window_length (int): 窗口长度（必须为奇数，默认5）
        polyorder (int): 多项式阶数（默认2）
    输出:
        pd.Series: 滤波后信号
    """
    series = get_column(df, 'close')
    
    # 参数校验
    if window_length % 2 == 0:
        raise ValueError("窗口长度必须是奇数")
    if polyorder >= window_length:
        raise ValueError("多项式阶数必须小于窗口长度")
    
    # 转换为numpy数组处理
    y = series.values.astype(float)
    
    # 计算Savitzky-Golay滤波器系数
    b = savgol_filter(np.eye(window_length), window_length, polyorder, axis=0)[window_length//2]
    
    # 双向滤波
    filtered = filtfilt(
        b,                # 分子系数
        [1.0],           # 分母系数（FIR滤波器设为1）
        y,               # 输入信号
        padtype='odd',   # 边界填充方式
        padlen=window_length-1  # 填充长度
    )
    
    return pd.Series(filtered, index=series.index)

factor_manager.register_factor(Factor(
    name="Zero_Phase_Filter",
    category=FactorCategory.TECHNICAL,
    description="零相位滤波器（Zero Phase Filter）",
    calculation=Zero_Phase_Filter,
    dependencies=['close'],
    parameters={'window_length': 5, 'polyorder': 2}
))

def Remove_Offset(df, period=14):
    """
    去除偏移 (Remove Offset)
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 回溯周期（默认10）
    输出:
        pd.Series: 去除偏移后的序列
    """
    close = get_column(df, 'close')
    offset = close - close.rolling(window=period).mean()
    return offset

factor_manager.register_factor(Factor(
    name="Remove_Offset",
    category=FactorCategory.TECHNICAL,
    description="去除偏移（Remove Offset）",
    calculation=Remove_Offset,
    dependencies=['close'],
    parameters={'period': 14}
))

def Detrend_Least_Squares(df, period=10):
    """
    最小二乘去趋势 (Linear Detrending) - 修正版本
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 回溯周期（默认10）
    输出:
        pd.Series: 去趋势后的残差
    """
    close = get_column(df, 'close')
    
    # 预先计算所有窗口的x值和X矩阵，避免重复计算
    x = np.arange(period)
    X = np.vstack([x, np.ones(period)]).T
    
    # 使用向量化操作代替apply函数
    values = close.values
    result = np.full_like(values, np.nan)
    
    # 只对有足够数据的位置计算
    for i in range(period-1, len(values)):
        window = values[i-period+1:i+1]
        a, b = np.linalg.lstsq(X, window, rcond=None)[0]
        result[i] = window[-1] - (a * x[-1] + b)
    
    return pd.Series(result, index=close.index)

factor_manager.register_factor(Factor(
    name="Detrend_Least_Squares",
    category=FactorCategory.TECHNICAL,
    description="最小二乘去趋势（Linear Detrending）",
    calculation=Detrend_Least_Squares,
    dependencies=['close'],
    parameters={'period': 10}
))

def Beta_Calculation(df, period=10):
    """
    Beta类似指标
    输入:
        df (pd.DataFrame): 包含close的DataFrame
        period (int): 计算周期（默认10）
    输出:
        pd.Series: Beta值
    """
    close = get_column(df, 'close')
    # 计算指数变化
    index_cl = close.pct_change() + 1
    index_av = close.rolling(window=2).mean().pct_change() + 1
    
    # 计算偏差
    dev_cl = index_cl - index_cl.rolling(window=period).mean()
    dev_av = index_av - index_av.rolling(window=period).mean()
    
    # 计算协方差和方差
    cov = dev_cl.rolling(window=period).cov(dev_av)
    var = dev_av.rolling(window=period).var()
    
    return cov / var.replace(0, 1e-5)

factor_manager.register_factor(Factor(
    name="Beta_Calculation",
    category=FactorCategory.TECHNICAL,
    description="Beta类似指标（Beta Calculation）",
    calculation=Beta_Calculation,
    dependencies=['close'],
    parameters={'period': 10}
))

def Support_Resistance(df, period=20, threshold=0.05):
    """
    支撑阻力指标
    输入:
        df (pd.DataFrame): 包含high, low, close的DataFrame
        period (int): 回溯周期（默认20）
        threshold (float): 支撑阻力判定阈值（默认0.05）
    输出:
        pd.Series: 支撑阻力得分，正值表示阻力，负值表示支撑
    """
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    # 初始化输出序列
    sr_score = pd.Series(0.0, index=close.index)
    
    # 计算前一天的高低点并标记局部高点和低点
    high_prev = high.shift(1)
    low_prev = low.shift(1)
    high_points = high > high_prev
    low_points = low < low_prev
    
    # 预计算所有价格点的相对距离矩阵
    # 使用numpy广播来加速计算
    prices = close.values
    high_values = high.values
    low_values = low.values
    
    # 为了避免循环，使用rolling窗口操作
    for i in range(period, len(close)):
        current_price = prices[i]
        
        # 获取窗口数据
        window_start = max(0, i-period)
        window_high = high_values[window_start:i]
        window_low = low_values[window_start:i]
        window_high_points = high_points.iloc[window_start:i].values
        window_low_points = low_points.iloc[window_start:i].values
        
        # 筛选高点和低点
        valid_highs = window_high[window_high_points]
        valid_lows = window_low[window_low_points]
        
        # 如果没有有效点，继续
        if len(valid_highs) == 0 and len(valid_lows) == 0:
            continue
            
        # 计算相对距离并筛选阈值内的点
        min_high_dist = float('inf')
        if len(valid_highs) > 0:
            high_dists = np.abs((valid_highs - current_price) / current_price)
            near_highs = high_dists[high_dists < threshold]
            if len(near_highs) > 0:
                min_high_dist = np.min(near_highs)
        
        min_low_dist = float('inf')
        if len(valid_lows) > 0:
            low_dists = np.abs((valid_lows - current_price) / current_price)
            near_lows = low_dists[low_dists < threshold]
            if len(near_lows) > 0:
                min_low_dist = np.min(near_lows)
        
        # 计算支撑阻力得分
        if min_high_dist < min_low_dist:
            sr_score.iloc[i] = min_high_dist * 100  # 阻力得分（正值）
        elif min_low_dist < float('inf'):
            sr_score.iloc[i] = -min_low_dist * 100  # 支撑得分（负值）
    
    return sr_score

factor_manager.register_factor(Factor(
    name="Support_Resistance",
    category=FactorCategory.TECHNICAL,
    description="支撑阻力指标（Support Resistance）",
    calculation=Support_Resistance,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 20, 'threshold': 0.05}
))
