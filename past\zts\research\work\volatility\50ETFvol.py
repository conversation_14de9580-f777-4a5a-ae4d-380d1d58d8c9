# -*- coding:utf-8 -*-
from __future__ import print_function
import pyodbc
import pandas as pd
import numpy as np
from WindPy import w
from pandas import DataFrame
import math
from scipy.stats import norm
import xlwt

w.start()

wsd_data = w.wsd("SR.CZC", "trade_hiscode,close", "2017-04-19", "2017-11-21", "PriceAdj=F")

fm = pd.DataFrame(wsd_data.Data, index=wsd_data.Fields, columns=wsd_data.Times)
fm = fm.T  # 将矩阵转置


# print fm

def ImpVolCall(MktPrice, Strike, Expiry, Asset, IntRate, Dividend, Sigma, error):
    n = 1
    Volatility = Sigma  # 初始值
    dv = error + 1
    while abs(dv) > error:
        d1 = np.log(Asset / Strike) + (IntRate - Dividend + 0.5 * Volatility ** 2) * Expiry
        d1 = d1 / (Volatility * np.sqrt(Expiry))
        d2 = d1 - Volatility * np.sqrt(Expiry)
        PriceError = Asset * math.exp(-Dividend * Expiry) * norm.cdf(d1) - Strike * math.exp(
            -IntRate * Expiry) * norm.cdf(d2) - MktPrice
        Vega1 = Asset * np.sqrt(Expiry / 3.1415926 / 2) * math.exp(-0.5 * d1 ** 2)
        dv = PriceError / Vega1
        Volatility = Volatility - dv  # 修正隐含波动率
        n = n + 1

        if n > 300:  # 迭代次数过多的话
            ImpVolCall = 0.0
            break

        ImpVolCall2 = Volatility

    return ImpVolCall2


def BSOptionISDEstimate(iopt, S, X, r, q, tyr, optprice):
    Sq = S * math.exp(-q * tyr)
    pvX = X * math.exp(-r * tyr)
    p = 3.141592653
    calc0 = optprice - iopt * 0.5 * (Sq - pvX)
    calc1 = (calc0 ** 2) - ((Sq - pvX) ** 2) / p
    if calc1 < 0 :
        BSOptionISDEstimate = -1
    else:
        calc2 = calc0 + np.sqrt(calc1)
        BSOptionISDEstimate = np.sqrt(2 * p / tyr) * calc2 / (Sq + pvX)
    return BSOptionISDEstimate

def BSOptionISDGoalSeekNR(iopt, S, X, r, q, tyr, optprice):

    fval = 1
    atol = 0.01
    sigmanow = BSOptionISDEstimate(iopt, S, X, r, q, tyr, optprice)

    if sigmanow <= 0:
        sigmanow = np.sqrt(2 * abs(math.log(S / X) + (r - q) * tyr) / tyr)
    while abs(fval) > atol:
        fval = BSOptionValue(iopt, S, X, r, q, tyr, sigmanow) - optprice
        fdashval = BSOptionVega(S, X, r, q, tyr, sigmanow)
        sigmanow = sigmanow - (fval / fdashval)
    return sigmanow


def BSOptionValue(iopt, S, X, r, q, tyr, sigma):
    eqt = math.exp(-q * tyr)
    ert = math.exp(-r * tyr)
    if S > 0 and X > 0 and tyr > 0 and sigma > 0 :
        NDOne = norm.cdf(iopt * BSDOne(S, X, r, q, tyr, sigma))
        NDTwo = norm.cdf(iopt * BSDTwo(S, X, r, q, tyr, sigma))
        BSOptionValue = iopt * (S * eqt * NDOne - X * ert * NDTwo)
    else:
       BSOptionValue = -1

    return BSOptionValue

def BSDOne(S, X, r, q, tyr, sigma):
    BSDOne = (math.log(S / X) + (r - q + 0.5 * sigma ** 2) * tyr) / (sigma * math.sqrt(tyr))
    return BSDOne
def BSDTwo(S, X, r, q, tyr, sigma):
    BSDTwo = (math.log(S / X) + (r - q - 0.5 * sigma ** 2) * tyr) / (sigma * math.sqrt(tyr))
    return BSDTwo
def BSNdashDOne(S, X, r, q, tyr, sigma):
    BSNdashDOne = norm.cdf(BSDOne(S, X, r, q, tyr, sigma))
    return BSNdashDOne
def BSOptionVega(S, X, r, q, tyr, sigma):
    BSOptionVega = S * math.sqrt(tyr) * BSNdashDOne(S, X, r, q, tyr, sigma) * math.sqrt(-q * tyr)
    return BSOptionVega


sqllist2 = []
for index, row in fm.iterrows():
    time = index
    rate = w.wss("SHIBOR3M.IR", "close", "tradeDate=%s;priceAdj=U;cycle=D" % time).Data[0][0]
    print(time)
    code = row['TRADE_HISCODE']
    price = row['CLOSE']
    a = math.modf(price / 100)[0]
    if a == 0.5:
        price2 = int(price / 100) * 100
    else:
        price2 = round(price / 100) * 100

    code1 = code[:-4] + "C" + str(price2)[0:4] + code[-4:]
    code2 = code[:-4] + "P" + str(price2)[0:4] + code[-4:]
    # print code2
    wdata2 = w.wss("%s,%s" % (code1, code2), "close,ptmtradeday", "tradeDate=%s;priceAdj=F;cycle=D" % time)
    # print wdata2
    C = wdata2.Data[0][0]
    P = wdata2.Data[0][1]
    # print C,P
    Expiry = wdata2.Data[1][0]
    S = C - P + price2

    # imvol = ImpVolCall(C, price2, Expiry, S, rate / 100, 0, 1, 0.001)
    imvol = BSOptionISDGoalSeekNR(1, S, price2, rate / 100, 0, Expiry/252, C)
    sqllist = []

    sqllist.append(time)
    sqllist.append(code)
    sqllist.append(price)
    sqllist.append(price2)
    sqllist.append(C)
    sqllist.append(P)
    sqllist.append(S)
    sqllist.append(Expiry)
    sqllist.append(S - price)
    sqllist.append(imvol)

    sqltuple = tuple(sqllist)

    sqllist2.append(sqltuple)

print(sqllist2)

f = xlwt.Workbook()  # 创建工作簿
sheet1 = f.add_sheet(u'opt_fut', cell_overwrite_ok=True)  # 创建sheet
for i, row in enumerate(sqllist2):
    for j, col in enumerate(row):
        sheet1.write(i, j, col)
f.save('SR801vol2.xls')  # 保存文件
