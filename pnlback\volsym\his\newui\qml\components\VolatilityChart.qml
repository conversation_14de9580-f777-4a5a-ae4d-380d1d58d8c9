import QtQuick
import QtCharts
import QtQuick.Controls

ChartView {
    id: root
    antialiasing: true
    legend.visible: true
    animationOptions: ChartView.NoAnimation
    
    property bool showBarChart: true
    
    ValueAxis {
        id: axisX
        titleText: "行权价"
        labelFormat: "%.0f"
        tickCount: 5
    }
    
    ValueAxis {
        id: axisY
        titleText: "波动率"
        labelFormat: "%.1f%"
        tickCount: 5
    }
    
    ValueAxis {
        id: axisY2
        titleText: "持仓"
        visible: showBarChart
        labelsVisible: showBarChart
        gridVisible: false
        tickCount: 5
    }
    
    LineSeries {
        id: marketSeries
        name: "市场波动率"
        axisX: axisX
        axisY: axisY
        color: "blue"
        width: 2
    }
    
    LineSeries {
        id: sviSeries
        name: "SVI拟合"
        axisX: axisX
        axisY: axisY
        color: "red"
        width: 2
    }
    
    BarSeries {
        id: positionSeries
        name: "持仓"
        axisX: axisX
        axisY: axisY2
        visible: showBarChart
        
        BarSet {
            label: "多头"
            color: "#80c080"  // 半透明绿色
            borderColor: "green"
        }
        
        BarSet {
            label: "空头"
            color: "#c08080"  // 半透明红色
            borderColor: "red"
        }
    }
    
    // 测试数据
    Component.onCompleted: {
        // 添加一些测试数据点
        marketSeries.append(2800, 20)
        marketSeries.append(2900, 18)
        marketSeries.append(3000, 15)
        marketSeries.append(3100, 17)
        marketSeries.append(3200, 19)
        
        sviSeries.append(2800, 19.5)
        sviSeries.append(2900, 17.8)
        sviSeries.append(3000, 15.2)
        sviSeries.append(3100, 16.8)
        sviSeries.append(3200, 18.7)
        
        positionSeries.at(0).append([5, 8, 12, 8, 5])
        positionSeries.at(1).append([-3, -5, -8, -5, -3])
    }
    
    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        
        onWheel: {
            if (wheel.modifiers & Qt.ControlModifier) {
                var zoomFactor = wheel.angleDelta.y > 0 ? 0.9 : 1.1
                var mouseX = wheel.x
                var mouseY = wheel.y
                
                var xCenter = root.mapToValue(Qt.point(mouseX, mouseY), marketSeries).x
                var yCenter = root.mapToValue(Qt.point(mouseX, mouseY), marketSeries).y
                
                axisX.min = xCenter - (xCenter - axisX.min) * zoomFactor
                axisX.max = xCenter + (axisX.max - xCenter) * zoomFactor
                axisY.min = yCenter - (yCenter - axisY.min) * zoomFactor
                axisY.max = yCenter + (axisY.max - yCenter) * zoomFactor
            }
        }
        
        onDoubleClicked: {
            if (mouse.button === Qt.LeftButton) {
                // 重置缩放
                axisX.min = undefined
                axisX.max = undefined
                axisY.min = undefined
                axisY.max = undefined
            }
        }
    }
}