"""
时间序列因子模块
包含所有与时间序列相关的因子定义
-- author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory
from .basic_factors import calculate_imbalance

# 注册时间序列因子
for window in [5, 10, 20]:
    # 中间价收益率
    factor_manager.register_factor(Factor(
        name=f"mid_return_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期中间价收益率",
        calculation=lambda data, w=window: data['mid'].pct_change(w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining"
    ))

    # 成交价收益率
    factor_manager.register_factor(Factor(
        name=f"trade_price_return_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期成交价收益率",
        calculation=lambda data, w=window: data['LastPrice'].pct_change(w),
        dependencies=["LastPrice"],
        parameters={"window": window},
        source="lining"
    ))


    # 中间价移动平均
    def calculate_mid_ma(data: pd.DataFrame, window: int) -> pd.Series:
        """计算中间价移动平均
        公式: mid_ma = MA((AskPrice1 + BidPrice1)/2, window)
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        return mid.rolling(window=window).mean()


    factor_manager.register_factor(Factor(
        name=f"mid_ma_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期中间价移动平均",
        calculation=lambda data, w=window: calculate_mid_ma(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining"
    ))


    # 计算波动率
    def calculate_volatility(data: pd.DataFrame, window: int) -> pd.Series:
        """计算波动率
        公式: volatility = STD(mid_price.pct_change(), window)
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        return mid.pct_change().rolling(window=window).std()


    factor_manager.register_factor(Factor(
        name=f"volatility_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"中间价{window}周期收益率的滚动标准差",
        calculation=lambda data, w=window: calculate_volatility(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining"
    ))


    # 成交量移动平均
    def calculate_vol_ma(data: pd.DataFrame, window: int) -> pd.Series:
        """计算成交量移动平均
        公式: vol_ma = MA(tradedVol, window)
        """
        return data['tradedVol'].rolling(window=window).mean()


    factor_manager.register_factor(Factor(
        name=f"vol_ma_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期成交量移动平均",
        calculation=lambda data, w=window: calculate_vol_ma(data, w),
        dependencies=["tradedVol"],
        parameters={"window": window},
        source="lining"
    ))


    # 成交量标准差
    def calculate_vol_std(data: pd.DataFrame, window: int) -> pd.Series:
        """计算成交量标准差
        公式: vol_std = STD(tradedVol, window)
        """
        return data['tradedVol'].rolling(window=window).std()


    factor_manager.register_factor(Factor(
        name=f"vol_std_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期成交量标准差",
        calculation=lambda data, w=window: calculate_vol_std(data, w),
        dependencies=["tradedVol"],
        parameters={"window": window},
        source="lining"
    ))


    # 不平衡度移动平均
    def calculate_imbalance_ma(data: pd.DataFrame, window: int) -> pd.Series:
        """计算不平衡度移动平均
        公式: imbalance_ma = MA((BidVol1 - AskVol1)/(BidVol1 + AskVol1), window)
        """
        if 'imbalance_1' not in data.columns:
            imbalance = calculate_imbalance(data, 1)
        else:
            imbalance = data['imbalance_1']
        return imbalance.rolling(window=window).mean()


    factor_manager.register_factor(Factor(
        name=f"imbalance_ma_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期一档不平衡度移动平均",
        calculation=lambda data, w=window: calculate_imbalance_ma(data, w),
        dependencies=["BidVol1", "AskVol1"],
        parameters={"window": window},
        source="lining"
    ))


    # 实现绝对变异
    def calculate_realized_absvar(data: pd.DataFrame, window: int) -> pd.Series:
        """计算实现绝对变异
        公式: RAV = sqrt(π/(2*n)) * Σ|r_t|
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        # 使用向量化操作代替apply函数
        abs_returns = np.abs(log_return)
        sum_abs_returns = abs_returns.rolling(window=window).sum()
        count = log_return.rolling(window=window).count()
        
        # 向量化计算最终结果
        result = np.sqrt(np.pi / (2 * count)) * sum_abs_returns
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_absvar_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现绝对变异",
        calculation=lambda data, w=window: calculate_realized_absvar(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    # 实现双幂变异
    def calculate_realized_bipowvar(data: pd.DataFrame, window: int) -> pd.Series:
        """计算实现双幂变异
        公式: RBV = (π/2)*(n/(n-2))*Σ|r_t|*|r_{t-1}|
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        # 使用向量化操作代替apply函数
        abs_returns = np.abs(log_return)
        abs_returns_shifted = np.abs(log_return.shift(1))
        
        # 计算乘积的滚动和
        product_sum = (abs_returns * abs_returns_shifted).rolling(window=window).sum()
        
        # 计算有效样本数
        count = log_return.rolling(window=window).count()
        
        # 向量化计算最终结果
        result = (np.pi / 2) * (count / (count - 2)) * product_sum
        
        # 处理样本数不足的情况
        result = result.where(count >= 3, np.nan)
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_bipowvar_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现双幂变异",
        calculation=lambda data, w=window: calculate_realized_bipowvar(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    # 实现偏度
    def calculate_realized_skew(data: pd.DataFrame, window: int) -> pd.Series:
        """计算实现偏度
        公式: RS = (√n*Σr_t^3)/(RV^(3/2))
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        # 预计算立方和平方，避免在apply中重复计算
        log_return_squared = log_return ** 2
        log_return_cubed = log_return ** 3
        
        # 使用向量化操作代替apply函数
        rv = log_return_squared.rolling(window=window, min_periods=1).sum().pow(0.5)
        sum_cubed = log_return_cubed.rolling(window=window, min_periods=1).sum()
        count_sqrt = log_return.rolling(window=window, min_periods=1).count().pow(0.5)
        
        # 计算结果
        result = count_sqrt * sum_cubed / rv.pow(3)
        
        # 处理rv为0的情况
        result = result.where(rv != 0, np.nan)
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_skew_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现偏度",
        calculation=lambda data, w=window: calculate_realized_skew(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    # 实现峰度
    def calculate_realized_kurtosis(data: pd.DataFrame, window: int) -> pd.Series:
        """计算实现峰度
        公式: RK = (n*Σr_t^4)/(RV^2)
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        # 预计算平方和四次方，避免在apply中重复计算
        log_return_squared = log_return ** 2
        log_return_quad = log_return ** 4
        
        # 使用向量化操作代替apply函数
        rv_squared = log_return_squared.rolling(window=window, min_periods=1).sum().pow(2)
        sum_quad = log_return_quad.rolling(window=window, min_periods=1).sum()
        count = log_return.rolling(window=window, min_periods=1).count()
        
        # 计算结果
        result = count * sum_quad / rv_squared
        
        # 处理rv为0的情况
        result = result.where(rv_squared != 0, np.nan)
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_kurtosis_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现峰度",
        calculation=lambda data, w=window: calculate_realized_kurtosis(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    # 实现四次方
    def calculate_realized_quarticity(data: pd.DataFrame, window: int) -> pd.Series:
        """计算实现四次方
        公式: RQ = (n/3)*Σr_t^4
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        # 使用向量化操作代替apply函数
        log_return_quad = log_return ** 4
        sum_quad = log_return_quad.rolling(window=window, min_periods=1).sum()
        count = log_return.rolling(window=window, min_periods=1).count()
        
        # 计算结果
        result = (count / 3) * sum_quad
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_quarticity_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现四次方",
        calculation=lambda data, w=window: calculate_realized_quarticity(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    # 跳跃过程
    def calculate_bpv_jump(data: pd.DataFrame, window: int) -> pd.Series:
        """计算跳跃过程
        公式: BPV_jump = max(RV - RBV, 0)
        RBV: 实现双幂变异
        """
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        # 向量化计算RV
        log_return_squared = log_return ** 2
        rv = np.sqrt(log_return_squared.rolling(window=window).sum())
        
        # 向量化计算RBV
        abs_returns = np.abs(log_return)
        abs_returns_shifted = np.abs(log_return.shift(1))
        abs_product = abs_returns * abs_returns_shifted
        
        # 计算有效数据点数
        count = log_return.rolling(window=window).count()
        valid_count_mask = count >= 3
        
        # 计算RBV
        rbv = pd.Series(np.nan, index=log_return.index)
        rbv_values = (np.pi / 2) * (count / (count - 2)) * abs_product.rolling(window=window).sum()
        rbv.loc[valid_count_mask] = rbv_values.loc[valid_count_mask]
        
        # 计算跳跃过程
        return np.maximum(rv - rbv, 0)


    factor_manager.register_factor(Factor(
        name=f"BPV_jump_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期跳跃过程",
        calculation=lambda data, w=window: calculate_bpv_jump(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))

# 均线相交
def calculate_ma_cross(data: pd.DataFrame, short_window: int, long_window: int) -> pd.Series:
    """计算均线相交
    
    参数:
        data: 包含价格数据的DataFrame

    返回:
        均线相交序列
    """
    mid = (data['AskPrice1'] + data['BidPrice1']) / 2
    short_ma = mid.rolling(short_window).mean()
    long_ma = mid.rolling(long_window).mean()
    diff = short_ma - long_ma
    sign_change = np.sign(diff).diff()
    return np.where(sign_change != 0, diff, 0)


factor_manager.register_factor(Factor(
    name="ma_cross",
    category=FactorCategory.TIME_SERIES,
    description="均线相交",
    calculation=lambda data, short_window=5, long_window=20: calculate_ma_cross(data, short_window, long_window),
    dependencies=["AskPrice1", "BidPrice1"],
    parameters={"short_window": 5, "long_window": 20},
))

# 计算自相关
def calculate_autocorrelation(data: pd.DataFrame, window: int, lag: int) -> pd.Series:
    """计算自相关
    
    参数:
        data: 包含价格数据的DataFrame
        window: 计算窗口大小
        lag: 滞后阶数
        
    返回:
        自相关系数序列
    """
    mid = (data['AskPrice1'] + data['BidPrice1']) / 2
    returns = mid.pct_change()
    
    # 预先计算滞后序列，避免在rolling中重复计算
    returns_lag = returns.shift(lag)
    
    # 使用向量化操作代替apply函数
    # 计算协方差
    returns_mean = returns.rolling(window=window).mean()
    returns_lag_mean = returns_lag.rolling(window=window).mean()
    
    # (x - mean(x)) * (y - mean(y))的滚动和
    cov = (returns - returns_mean) * (returns_lag - returns_lag_mean)
    cov = cov.rolling(window=window).sum()
    
    # 计算标准差
    returns_std = returns.rolling(window=window).std()
    returns_lag_std = returns_lag.rolling(window=window).std()
    
    # 计算相关系数
    denominator = returns_std * returns_lag_std
    # 避免除零
    autocorr = cov / denominator.where(denominator != 0, np.nan)
    
    return autocorr


# 注册不同滞后阶数的自相关因子
for window in [5, 10, 20]:
    for lag in [1, 2, 3]:
        factor_manager.register_factor(Factor(
            name=f"autocorr_{window}_{lag}",
            category=FactorCategory.TIME_SERIES,
            description=f"{window}周期{lag}阶自相关",
            calculation=lambda data, w=window, l=lag: calculate_autocorrelation(data, w, l),
            dependencies=["AskPrice1", "BidPrice1"],
            parameters={"window": window, "lag": lag},
            source="lining-orderbook-dynamic-features v7"
        ))

# 价格和交易量导数 (时间导数)
# 使用差分近似导数
for side in ['Ask', 'Bid']: 
    for i in range(1, 6):
        factor_manager.register_factor(Factor(
            name=f"d{side}Price{i}_dt",
            category=FactorCategory.TIME_SERIES,
            description=f"{side}Price{i}的时间导数",
            calculation=lambda data, s=side, i=i: data[f'{s}Price{i}'].diff(),
            dependencies=[f"{side}Price{i}"],
            source="lining-orderbook-dynamic-features v6"
        ))
        factor_manager.register_factor(Factor(
            name=f"d{side}Vol{i}_dt",
            category=FactorCategory.TIME_SERIES,
            description=f"{side}Vol{i}的时间导数",
            calculation=lambda data, s=side, i=i: data[f'{s}Vol{i}'].diff(),
            dependencies=[f"{side}Vol{i}"],
            source="lining-orderbook-dynamic-features v6"
        ))


def calculate_avg_price_diff(data: pd.DataFrame, side: str, levels: list) -> pd.Series:
    """计算平均价格差异"""
    for j in levels:
        if f'd{side}Price{j}_dt' not in data.columns:
            data[f'd{side}Price{j}_dt'] = data[f'{side}Price{j}'].diff()
    return data[[f'd{side}Price{j}_dt' for j in levels]].mean(axis=1)

for side in ['Bid', 'Ask']:
    for i in range(1, 6):
        factor_manager.register_factor(Factor(
            name=f"avg{side}prcdt{i}",
            category=FactorCategory.TIME_SERIES,
            description=f"{side}档位{i}的平均价格差异",
            calculation=lambda data, s=side, l=i: calculate_avg_price_diff(data, s, range(1, l+1)),
            dependencies=[f"d{side}Price{i}_dt"],
        ))
