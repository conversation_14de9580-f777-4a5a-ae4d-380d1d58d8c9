#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('SHFE.dev')

""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""

#import vnpy.app.my_portfolio_strategy.backtesting, strategies.tick_resilience_portfolio  
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
#from strategies.tick_resilience_portfolio import TickWithRefer
from strategies.termStrategy import TermStrategy
import datetime
from vnpy.trader.constant import Interval

#%%


#maker='m2203.DCE'
#refer='m2201.DCE'"
commodity = 'hc'
maker1,maker2,maker3,maker4= [commodity+x for x in ['2111.SHFE','2112.SHFE','2202.SHFE','2203.SHFE']]
refer=commodity+'2201.SHFE'


engine = BacktestingEngine(alwaysInQueueHeadFlag=True)
engine.set_parameters(
    vt_symbols=[maker1,maker2,maker3,maker4,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime.datetime(2021, 8, 18,9, 0), # 开始时间
    end=datetime.datetime(2021, 8, 18, 15, 0), # 结束时间
    rates={maker1:0,maker2:0,maker3:0,maker4:0,refer: 0}, # 手续费率
    slippages={maker1:0,maker2:0,maker3:0,maker4:0,refer: 0}, # 滑点
    sizes={maker1:10,maker2:10,maker3:10,maker4:10,refer: 10}, # 合约规模
    priceticks={maker1:1,maker2:1,maker3:1,maker4:1,refer: 1}, # 一个tick大小
    capital=1_000_000, # 初始资金
)

# 添加回测策略，并修改内部参数
engine.clear_data()
#engine.add_strategy(ReverseStrategy, {'lots': 10,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':8,'edge':6,'eta':1,'gamma':1,'validVolume':50,'safeVolume':60,'maxPos':40,'loss':3000,
                                      #'referHedgeFlag':False,'neverStopFlag':False}) #pg2110
#engine.add_strategy(ReverseStrategy, {'lots': 10,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':1,'edge':3,'eta':1,'gamma':1,'validVolume':100,'safeVolume':150,'maxPos':50,'loss':3000,
                                      #'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':False,'useReverseOrderTargetPositionFlag':False,'reverseOrderTargetPosition':0}) #m2203
#engine.add_strategy(ReverseStrategy, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':1,'edge':4,'eta':0.5,'gamma':0.5,'validVolume':3,'safeVolume':5,'maxPos':6,'loss':5000}) #l2204
#engine.add_strategy(ReverseStrategy, {'lots': 25,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':9,'edge':9,'eta':0.6,'gamma':0.1,'validVolume':120,'safeVolume':150,'maxPos':50,'loss':5000,
                                      #'referHedgeFlag':False}) #hc2202
engine.add_strategy(TermStrategy, {'lots': 25,'maker':[maker1,maker2,maker3,maker4],'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      'minEdge':9,'edge':9,'eta':1,'gamma':0.1,'validVolume':100,'safeVolume':150,'maxPos':50,'loss':3000,
                                      'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':True,'useReverseOrderTargetPosition':True,'reverseOrderTargetPosition':0
                                      ,'adjust':1.5,'max_error':5}) #rb2202
#engine.add_strategy(ReverseStrategy, {'lots': 2,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':6,'edge':9,'eta':1,'gamma':0.3,'validVolume':12,'safeVolume':15,'maxPos':8,'loss':3000}) #jc2112
                                       

#%%
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
#duty = engine.duty_statistics()
#engine.show_tick_chart() # 显示图表
# engine.duty_statistics()

#%%
import pandas as pd
from datetime import timedelta
trades = pd.DataFrame([{'time':(engine.trades[x].datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':engine.trades[x].direction.value,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments
           } for x in engine.trades])
orders = pd.DataFrame([{'time':(x.datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
           } for x in engine.get_all_orders()])

#%%

mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
           'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
           'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
           'last_price','volume','turnover']]
mkt['datetime'] = mkt['datetime'].apply(lambda x : x+timedelta(hours=8))
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))

trade_split = pd.DataFrame(engine.trade_split,columns=['time','vt_symbol','trade']) #成交拆分
trade_split = trade_split[trade_split.vt_symbol==maker]

#%%
'''
shfe#%%

import pandas as pd
tradesDF = pd.DataFrame([])
stats=[]
date = '2021-08-05' #第一天晚上
start = datetime.datetime(2021, 8, 4) #第一天白天
end = datetime.datetime(2021, 8,5) #最后一天白天
delta = datetime.timedelta(days=1)
day = start
weekend=set([5,6])
date_formate = "%Y-%m-%d"
while day<=end:
    if day.weekday() in weekend:
        day+=delta
        continue
    else:
        beginStr = date+'T21:00:00.0Z'
        date = day.strftime(date_formate)
        endStr = date+'T15:00:00.0Z'
        
        day+=delta
        
        maker='m2203.DCE'
        refer='m2110.DCE'
        engine = BacktestingEngine()
        engine.set_parameters(
            vt_symbols=[maker,refer], # 回测品种
            interval=Interval.TICK, # 回测模式的数据间隔
            start = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ'), # 开始时间
            end= datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ'), # 结束时间
            rates={maker: 0,refer: 0}, # 手续费率
            slippages={maker: 0,refer: 0}, # 滑点
            sizes={maker: 10,refer: 10}, # 合约规模
            priceticks={maker: 1,refer: 1}, # 一个tick大小
            capital=1_000_000, # 初始资金
        )
        # 添加回测策略，并修改内部参数
        engine.clear_data()
        engine.add_strategy(ReverseStrategy, {'lots': 25,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      'minEdge':9,'edge':9,'eta':0.6,'gamma':0.1,'validVolume':120,'safeVolume':150,'maxPos':50,'loss':5000,
                                      'referHedgeFlag':True,'neverStopFlag':True}) #hc2202
      
        engine.load_data() # 加载历史数据
        engine.run_backtesting() # 进行回测
        df = engine.calculate_tick_result() # 计算逐日盯市盈亏
        stat = engine.calculate_tick_statistics() # 统计日度策略指标
        stats.append(stat)
        trades = pd.DataFrame([{'time':(engine.trades[x].datetime+datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':engine.trades[x].price,
           'direction':engine.trades[x].direction.value,'volume':engine.trades[x].volume} for x in engine.trades])
        tradesDF = pd.concat([tradesDF,trades])
        
dfStats = pd.DataFrame(stats)
dfStats.total_net_pnl.sum()
dfStats.total_trade_count.sum()
'''