#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
# ip.set_ip('SHFE.dev')
ip.set_ip('CZCE.prod')
import warnings
warnings.filterwarnings('ignore')

""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
    'CZCE.prod'代表大商所生产机
    'CZCE.dev'代表大商所生产机    
"""

"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from Chris.MMStrategy import MMStrategy
from vnpy_tools.PostTradeAnalysis import *
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
from datetime import timedelta
import PostTrade

#%%
maker='CF211.CZCE'
refer='CF211.CZCE'

multiplier=5

engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=False,save_result=False,refer_test=False,fast_join=True,refer_late=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime.datetime(2022,2,24 , 21, 0), # 开始时间
    end=datetime.datetime(2022, 2,25, 15, 0), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: 5,refer: 5}, # 一个tick大小
    capital=1_000_000, # 初始资金
)

# 添加回测策略，并修改内部参数
engine.clear_data()
engine.add_strategy(MMStrategy, {'lots': 1,'price_protect_tick':0,
                                 'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                 'edgetype':2,'edge':0,'fader':0.5,'fader_start_pos':5,'marketspread_indicator':0,
                                 'reverse_indicator':0,'reverse_tick':-1,'autohedgeclose':1}) 
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
# engine.duty_statistics()
#%%

# df_mkt = engine.get_risk(multiplier=multiplier)


# df_strategy = engine.get_strategy()

# engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name='MMStrategy_chris', parameters = parameters, save_risk=True, save_strategy=True, save_order=True, save_trade=True)



#%%
#盘后分析
# from datetime import datetime
import matplotlib.pyplot as plt
mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_5','bid_volume_5','bid_price_4','bid_volume_4','bid_price_3','bid_volume_3',
           'bid_price_2','bid_volume_2','bid_price_1','bid_volume_1','ask_price_1','ask_volume_1','ask_price_2',
           'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
           'last_price','volume','turnover']]
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dV'] = mkt_maker['dV'].fillna(0)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))
mkt_maker['mid'] = 0.5*(mkt_maker['bid_price_1']+mkt_maker['ask_price_1'])
trades = pd.DataFrame([{'datetime':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
            'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
            'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments} for x in engine.trades])
trades['net'] = (trades.volume*trades.direction).cumsum()
cash = (trades['direction']*(-1)*trades['volume']*trades['price'])*multiplier
cashCum = np.cumsum(cash)
trades['pnl'] = cashCum + trades.net*trades.price*multiplier
orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
            'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
            } for x in engine.get_all_orders()])

N = df.shape[0]
df['cum_pos'] = df['end_poses'].apply(lambda x: x[maker])
df['price'] = df['fair_prices'].apply(lambda x: x[maker])
# df['price'] = df['last_prices'].apply(lambda x: x[maker])
X_axis = range(N)
lw = 1
plt.plot(X_axis,df['total_pnl'].cumsum(),label='cum_pnl',linewidth=2*lw)
plt.title('PnL')
plt.show()

plt.plot(X_axis,df['cum_pos'],label='cum_pos',linewidth=1*lw)
plt.title('cum_pos')
plt.show()

plt.plot(X_axis,df['price'],label='price',linewidth=2*lw)
plt.title('price')
plt.show()


ts = engine.strategy.strategy_engine.priceticks[maker]
[df_market_impacts,df_realized_spreads] = PostTrade.calculate_market_impact(trades,mkt_maker,30,ts)
PostTrade.plot_Realized_Spread(df_market_impacts,df_realized_spreads)

#画一部分成交的market impact

# print(trades['datetime'].unique()) - len(trades['datetime'])

print('反手数量:',(trades['comments']=='reverse').sum())



# mkt_volume_dist = mkt_maker['dV'][mkt_maker['dV']>0]
# plt.figure(4,dpi=100)
# plt.hist(mkt_maker['dV'][mkt_maker['dV']>0],bins=10)
# plt.title('market_volume_distribution')

#%%
#check hedging on the close
test = trades.copy()
test['t'] = test['datetime'].apply(lambda x: datetime.datetime.strptime(x,'%Y-%m-%d %H:%M:%S.%f'))
test['t'] = test['t'].apply(lambda x: x.hour*100+x.minute)
df_test = test[(test['t']>1455) * (test['t']<1459)]
