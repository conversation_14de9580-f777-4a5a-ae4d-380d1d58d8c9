
"""
标签生成器
@author: lining
"""
import numpy as np
import pandas as pd
from utils.utils import log_print
from core import config

class LabelGenerator:
    

    def __init__(self, data, horizons=None):
        self.data = data
        self.horizons = horizons

    def generate_return_labels(self, ):
        

        for horizon in self.horizons:
            
            self.data[f'future_return_{horizon}'] = self.data['mid'].shift(-horizon) / self.data['mid'] - 1

            self.data[f'price_direction_{horizon}'] = np.sign(self.data[f'future_return_{horizon}'])

        log_print(f"已为 future_return_{horizon} 创建未来价格变动标签 数量: {self.data[f'future_return_{horizon}'].count()}")
    
    def generate_avg_return_labels(self, ):
        
        for horizon in self.horizons:
            
            future_avg_mid = self.data['mid'].rolling(window=horizon).mean().shift(-horizon)
            
            self.data[f'future_return_avg_mid_{horizon}'] = future_avg_mid / self.data['mid'] - 1


    def generate_return_vwap_labels(self, ):
        
        for horizon in self.horizons:
            
            self.data['vwap'] = self.data['TotalValueTraded'].diff(horizon) / self.data['Volume'].diff(horizon)/config.MULT
            self.data['vwap'] = self.data['vwap'].ffill()
            
            self.data[f'future_vwap_return_{horizon}'] = self.data['vwap'].shift(-horizon) / self.data['mid'] - 1
    
    def generate_return_class_labels(self, ):
        

        for horizon in self.horizons:
            
            quantiles = [0, 0.8, 0.95, 1.0]
            
            
            abs_returns = self.data[f'future_return_{horizon}'].abs()
            quantile_values = abs_returns.quantile(quantiles)
            
            
            labels = pd.cut(
                abs_returns,
                bins=quantile_values,
                labels=[0, 1, 2],
                include_lowest=True
            )
            
            
            labels = labels.fillna(0)  
            
            
            labels = labels.astype(int)
            
            
            self.data[f'return_class_{horizon}'] = labels
            self.data.loc[self.data[f'future_return_{horizon}'] < 0, f'return_class_{horizon}'] *= -1
            
        log_print(f"已为 future_return_{horizon} 创建5分类标签(-2,-1,0,1,2) 数量: {self.data[f'return_class_{horizon}'].count()}")
        

    def generate_volatility_labels(self, ):
        

        for horizon in self.horizons:
            
            future_std = self.data['mid'].rolling(window=horizon).std().shift(-horizon)
            self.data[f'future_volatility_{horizon}'] = future_std / self.data['mid']

        log_print(f"已为 future_return_{horizon} 创建未来波动率标签 数量: {self.data[f'future_volatility_{horizon}'].count()}")


    def generate_all_labels(self, ):
        
        
        self.generate_return_labels()

        
        self.generate_return_vwap_labels()

        
        self.generate_avg_return_labels()

        
        self.generate_return_class_labels()

        
        self.generate_volatility_labels()

        return self.data