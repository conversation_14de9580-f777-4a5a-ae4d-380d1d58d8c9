# -*- coding:utf-8 -*-
# Author:Lining
# Editdate:2019-6-20
import pyodbc

from WindPy import w
import pandas as pd
from os import path

w.start()

date1 = u'2019-07-08'
date2 = u'2019-07-09'

etfcode = u'510050.SH'

tradedates = w.tdays(date1, date2, "").Data[0]

d = path.dirname(__file__)
etf = pd.read_csv(open(d + '//db//sseETFmm.txt'), sep='\t',
                  names=['id', 'date', 'etf', 'com', 'sto', 'ot'])  # 加载papa.txt,指定它的分隔符是 \t
getf = etf.groupby('etf').size()
etfcodes = getf.index.values
etflist0 = None
nolist = []


def selecter(etfcode):
    if str(etfcode)[:2] == '50':
        print(etfcode)
        print('s50s')
        nolist.append(etfcode)
        return 0
    if str(etfcode)[:2] == '15':
        print(etfcode)
        print(u'深')
        nolist.append(etfcode)
        return 0
    if str(etfcode)[:3] == '511':
        print(u'债券')
        print(etfcode)
        nolist.append(etfcode)
        return 0
    if str(etfcode)[:3] == '518':
        print(u'黄金')
        print(etfcode)
        nolist.append(etfcode)
        return 0
    if str(etfcode)[:3] == '513' or str(etfcode) == '510901':
        print(u'跨境')
        print(etfcode)
        nolist.append(etfcode)
        return 0
    return 1


server = '10.36.18.54'
user = 'mktdtr'
password = '123678'
database = 'MktDataHis'
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE=database, PWD=password, UID=user)

sql0 = u"SELECT distinct [Symbol] FROM [XHLSHQ].[dbo].[ETF] where RecordTime>%s" % date1
dbetf = pd.read_sql(sql0, conn, index_col=None, coerce_float=True, params=None,
                    parse_dates=True, columns=None, chunksize=None)
etfcodes = dbetf['Symbol'].tolist()

for date0 in tradedates[::-1]:
    # ETF对应指数
    etfdata = w.wsd(pd.DataFrame(etfcodes)[0].apply(lambda x: str(x) + '.sh').tolist(), "fund_trackindexcode", date2,
                    date2, "")
    indexcodes = pd.DataFrame(etfdata.Data, index=None, columns=etfdata.Codes)
    for etfcode in etfcodes:
        if indexcodes[str(etfcode)+".SH"].str.contains('MI|CSI|XI')[0]:
            print(u'MI|CSI')
            print(etfcode)
            nolist.append(etfcode)
            continue
        if selecter(etfcode) == 0:
            continue
        wdata = w.wset("etfconstituent", "date=%s;windcode=%s.SH" % (date0, etfcode))
        if len(wdata.Data) == 0:
            print(etfcode)
            nolist.append(etfcode)
            continue
        etflist = pd.DataFrame(wdata.Data, index=wdata.Fields)
        etflist = etflist.T
        if len(etflist[etflist['wind_code'].str.contains('SH|SZ')])==0:
            print(u'境外2')
            print(etfcode)
            nolist.append(etfcode)
            continue
        if etflist0 is None:
            etflist0 = etflist['wind_code'].tolist()
        else:
            print(etfcode, len(set(etflist['wind_code'].tolist()) - set(etflist0)), )
            etflist0 = etflist0 + etflist['wind_code'].tolist()
            etflist0 = list(set(etflist0))
            print('tootal', len(etflist0))
    yelist = list(set(etfcodes) - set(nolist))
    etf2 = etf[etf['etf'].isin(yelist)]

    etf2.to_csv(d + "//db//sseETFmmclean.csv")
    pd.DataFrame(etfcodes).to_csv(d + "//db//ETFall.csv")
    pd.DataFrame(yelist).to_csv(d + "//db//ETFallclean.csv")
    break
