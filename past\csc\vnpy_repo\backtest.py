"""一般对价撮合模式"""
# from vnpy.app.cta_strategy.base import BacktestingMode
# from vnpy.app.cta_strategy.backtesting import BacktestingEngine, OptimizationSetting
# from strategies.tick_template import TickStrategy
# from datetime import datetime
# from vnpy.trader.constant import Interval

"""排队撮合"""
#from vnpy.app.my_tick_strategy.base import BacktestingMode
#from vnpy.app.my_tick_strategy.backtesting import BacktestingEngine, OptimizationSetting
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
import sys
from strategies.reverseStrategy import ReverseStrategy
from datetime import datetime
from vnpy.trader.constant import Interval
import pandas as pd

#%%
engine = BacktestingEngine()
maker = "eb2204.DCE"
refer = "eb2203.DCE"
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种 maker,refer
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime(2022, 2, 10, 21, 10), # 开始时间
    end=datetime(2022, 2, 11, 15, 00), # 结束时间
    capital=1_000_000, # 初始资金
    sizes={maker:5,refer:5}, # 合约规模
    priceticks={maker:1,refer:1}, # 一个tick大小
    rates={maker:0,refer:0}, #
    slippages = {maker:0,refer:0}
)
# 添加回测策略，并修改内部参数
engine.add_strategy(ReverseStrategy, {'lots': 5,'validVolume':10,'safeVolume':20,'edge':9,'minEdge':6,'gamma':1,'eta':1,'maxPos':20,'sizes':engine.sizes,'loss':3000, 'refer':refer, 'maker':maker, 'useSVMSingal':False, 'priceticks':engine.priceticks,'useReverseOrder':False, 'fixedFader':0, 'stop':2, 'neverStopFlag':False, 'referHedgeFlag':False, 'tradingOffsetSignal':False})
#engine.add_strategy(ReverseStrategy, {'lots': 5,'validVolume':20,'safeVolume':25,'edge':8,'minEdge':6,'gamma':0.5,'eta':1,'maxPos':50,'size':engine.size})

engine.load_data() # 加载历史数据
print('start: ', list(engine.history_data.items())[0][1].datetime)
print('end: ', list(engine.history_data.items())[-1][1].datetime)
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标

#%%
# engine.show_chart() # 显示图表1
get_all_trades = engine.get_all_trades()  # 获得所有成交
trades = pd.DataFrame([{'direction':x.direction.value,'price':x.price,'volume':x.volume,
                        'time': datetime.fromtimestamp(x.datetime.timestamp()).strftime('%Y-%m-%d %H:%M:%S.%f')} for x in get_all_trades])
get_all_orders = engine.get_all_orders() # 获得所有订单
orders = pd.DataFrame([{'direction':x.direction.value,'price':x.price,'volume':x.volume,'traded':x.traded,
                        'time':datetime.fromtimestamp(x.datetime.timestamp()).strftime('%Y-%m-%d %H:%M:%S.%f')} for x in get_all_orders])

#%%
date_list = [(datetime.datetime(2022,2, 7+i, 21, 10), datetime.datetime(2022,2, 8+i, 15, 00)) for i in range(4)]
result2 = {
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []
 }
for start, end in date_list:
    engine = BacktestingEngine()
    maker = "eb2204.DCE"
    refer = "eb2203.DCE"
    engine.set_parameters(
        vt_symbols=[maker,refer], # 回测品种 maker,refer
        interval=Interval.TICK, # 回测模式的数据间隔
        start=start, # 开始时间
        end=end, # 结束时间
        capital=1_000_000, # 初始资金
        sizes={maker:5,refer:5}, # 合约规模
        priceticks={maker:1,refer:1}, # 一个tick大小
        rates={maker:0,refer:0}, #
        slippages = {maker:0,refer:0}
    )
    # 添加回测策略，并修改内部参数
    engine.add_strategy(ReverseStrategy, {'lots': 5,'validVolume':10,'safeVolume':20,'edge':9,'minEdge':6,'gamma':1,'eta':1,'maxPos':20,'sizes':engine.sizes,'loss':3000, 'refer':refer, 'maker':maker, 'useSVMSingal':False, 'priceticks':engine.priceticks,'useReverseOrder':False, 'fixedFader':0, 'stop':2, 'neverStopFlag':False, 'referHedgeFlag':False, 'tradingOffsetSignal':False})
    #engine.add_strategy(ReverseStrategy, {'lots': 5,'validVolume':20,'safeVolume':25,'edge':8,'minEdge':6,'gamma':0.5,'eta':1,'maxPos':50,'size':engine.size})
    
    engine.load_data() # 加载历史数据
    print('start: ', list(engine.history_data.items())[0][1].datetime)
    print('end: ', list(engine.history_data.items())[-1][1].datetime)
    engine.run_backtesting() # 进行回测
    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
    stat = engine.calculate_tick_statistics() # 统计日度策略指标
    for key in engine.result.keys():
        result2[key].append(engine.result[key])  
result2 = pd.DataFrame(result2)


