#!/usr/bin/env python
# -*- coding:utf-8 -*-

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email.header import Header
from email import encoders
from email.utils import parseaddr, formataddr

from datetime import datetime

import os


class MailSender(object):
    _attachments = []

    def __init__(self, smtpSvr, port, user, pwd, usrname):
        self.smtp = smtplib.SMTP_SSL(smtpSvr, port)
        self._from = user
        self.pwd = pwd
        self.usrname = usrname
        print("connected!!!")

    def _format_addr(self, s):
        name, addr = parseaddr(s)
        # return formataddr(
        #     (Header(name, 'utf-8').encode(), addr.encode('utf-8') if isinstance(addr, str) else addr))
        return formataddr((Header(name, 'utf-8').encode(), addr))

    def login(self):
        self.smtp.login(self._from, self.pwd)
        print("login")

    def add_attachment(self, filepath):
        '''''
            添加附件
        '''
        att = MIMEBase('application', 'octet-stream')
        att.set_payload(open(filepath, 'rb').read())
        filename = os.path.split(filepath)[-1]
        if filename.split('.')[-1] in ['xlsx', 'xls']:
            att['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        att.add_header('Content-Disposition', 'attachment', filename=('gbk', '', filename))
        att.add_header('Content-ID', filename)
        encoders.encode_base64(att)

        self._attachments.append(att)

    def send(self, subject, content, to_addr):
        '''''
            发送邮件
        '''
        msg = MIMEMultipart()
        contents = MIMEText(content, "plain", _charset='utf-8')
        msg['Subject'] = subject
        msg['From'] = self._format_addr(u'%s<%s>' % (self.usrname, self._from))
        # msg['Cc'] = acc
        msg.attach(contents)
        for att in self._attachments:
            msg.attach(att)
        try:
            for i in range(len(to_addr)):
                to = to_addr[i]
                msg['To'] = to
            self.smtp.sendmail(self._from, to_addr, msg.as_string())
            return True
        except Exception as e:
            print(str(e))
            return False

    def close(self):
        self.smtp.quit()
        print("logout.")


if __name__ == '__main__':
    # send list
    to_addr = ["<EMAIL>", ]
    name = u"liningzts"

    import json
    data = open(u"D:/onedrive/文档/package.json", encoding='utf-8')
    strJson = json.load(data)
    users = strJson["email"]
    for user in users:
        if name == user["name"]:
            usr = user["user"]
            psw = user["password"]
            smtpSvr = user["smtpSvr"]
            break

    subject = u'中泰证券50ETF期权做市业务数据报送'  # input(u"{'请输入邮件主题：'}")
    content = u'Hello,\r\n\r\n\r\n    附件为中泰证券50ETF期权做市业务数据报送 \r\n\r\n\r\n李宁'  # input(u"{'请输入邮件主内容:'}")

    filepath = u'D:\\works\\中泰衍生\\做市组日常工作\\业务数据报送\\HIS\\中泰证券做市业务数据报送--%s.xlsx' \
               % datetime.strftime(datetime.now(), "%Y%m%d")

    mm = MailSender(smtpSvr, 465, usr, psw, name)
    mm.login()
    mm.add_attachment(filepath)
    res = mm.send(subject, content, to_addr)
    if res:
        print(datetime.now(), ':success')
    mm.close()
