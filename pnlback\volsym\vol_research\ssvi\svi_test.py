import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
from svi import SVI

def fit_svi_example():
    # 生成模拟数据
    K = np.linspace(80, 120, 20)  # 行权价范围
    F = 100  # 远期价格
    T = 1.0  # 到期时间
    
    # 生成真实波动率数据（模拟市场数据）
    k = np.log(K/F)  # 对数行权价
    true_vol = 0.2 + 0.1 * np.abs(k) + 0.02 * np.random.randn(len(k))  # 添加一些噪声
    
    # 计算市场数据的特征值
    atm_vol = true_vol[np.abs(k).argmin()]  # ATM波动率
    
    # 计算skew（使用±5%的点）
    idx_up = np.abs(k - 0.05).argmin()
    idx_down = np.abs(k + 0.05).argmin()
    skew = (true_vol[idx_up] - true_vol[idx_down]) / 0.1
    
    # 计算convexity（使用中心差分）
    convexity = (true_vol[idx_up] + true_vol[idx_down] - 2 * atm_vol) / (0.05**2)
    
    # 计算slopes
    cslope = (true_vol[idx_up] - atm_vol) / 0.05
    pslope = (atm_vol - true_vol[idx_down]) / 0.05
    
    # 目标特征值
    target_features = {
        'atm_vol': atm_vol,
        'skew': skew,
        'convexity': convexity,
        'cslope': cslope,
        'pslope': pslope
    }
    
    # 定义目标函数
    def objective(params):
        a, b, rho, m, sigma = params
        
        # 检查参数约束
        if (abs(rho) >= 1 or b <= 0 or sigma <= 0 or 
            a < 0 or a + b * sigma * np.sqrt(1 - rho**2) < 0 or
            b * (1 + rho) <= 0 or b * (rho - 1) >= 0):
            return 1e6
        
        # 计算SVI特征值
        svi = SVI(a, b, rho, m, sigma)
        svi.T = T  # 设置到期时间
        results = svi.get_results(0)  # 计算ATM点的特征值
        
        # 计算特征值的误差
        error = (
            ((results['atm_vol'] - target_features['atm_vol']) / target_features['atm_vol']) ** 2 +
            ((results['rawskew'] - target_features['skew']) / (abs(target_features['skew']) + 1e-6)) ** 2 +
            ((results['convexity'] - target_features['convexity']) / (abs(target_features['convexity']) + 1e-6)) ** 2 +
            ((results['cslope'] - target_features['cslope']) / (abs(target_features['cslope']) + 1e-6)) ** 2 +
            ((results['pslope'] - target_features['pslope']) / (abs(target_features['pslope']) + 1e-6)) ** 2
        )
        
        return error
    
    # 使用inverse_transform获取初始参数
    initial_params = SVI.inverse_transform(atm_vol, skew, convexity, cslope, pslope, T)
    
    # 参数优化
    bounds = [
        (0, None),      # a > 0
        (0, None),      # b > 0
        (-1, 1),        # -1 < rho < 1
        (None, None),   # m无限制
        (0, None)       # sigma > 0
    ]
    
    result = minimize(objective, initial_params, bounds=bounds, method='L-BFGS-B')
    
    if result.success:
        # 获取拟合参数
        a, b, rho, m, sigma = result.x
        svi = SVI(a, b, rho, m, sigma)
        svi.T = T
        
        # 生成拟合曲线
        k_fit = np.linspace(k.min(), k.max(), 100)
        v_fit = svi.variance(k_fit)
        sigma_fit = np.sqrt(v_fit/T)
        
        # 绘图
        plt.figure(figsize=(10, 6))
        plt.plot(k, true_vol, 'o', label='Market Data')
        plt.plot(k_fit, sigma_fit, '-', label='SVI Fit')
        plt.xlabel('Log-moneyness (k)')
        plt.ylabel('Implied Volatility')
        plt.title('SVI Volatility Surface Fitting')
        plt.legend()
        plt.grid(True)
        
        # 打印目标特征值
        print("\n目标特征值:")
        print(f"ATM Vol = {target_features['atm_vol']:.4f}")
        print(f"Skew = {target_features['skew']:.4f}")
        print(f"Convexity = {target_features['convexity']:.4f}")
        print(f"Call Slope = {target_features['cslope']:.4f}")
        print(f"Put Slope = {target_features['pslope']:.4f}")
        
        # 计算并打印拟合后的特征值
        results = svi.get_results(0)
        print("\n拟合后的特征值:")
        print(f"ATM Vol = {results['atm_vol']:.4f}")
        print(f"Skew = {results['rawskew']:.4f}")
        print(f"Convexity = {results['convexity']:.4f}")
        print(f"Call Slope = {results['cslope']:.4f}")
        print(f"Put Slope = {results['pslope']:.4f}")
        
        # 打印拟合参数
        print("\n拟合参数:")
        print(f"a = {a:.4f}")
        print(f"b = {b:.4f}")
        print(f"rho = {rho:.4f}")
        print(f"m = {m:.4f}")
        print(f"sigma = {sigma:.4f}")
        
        plt.show()
    else:
        print("拟合失败")

if __name__ == "__main__":
    fit_svi_example() 