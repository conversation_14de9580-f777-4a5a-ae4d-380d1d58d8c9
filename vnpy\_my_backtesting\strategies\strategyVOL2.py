# encoding: UTF-8


from datetime import datetime, time

import numpy as np
from vnpy.trader.constant import Status
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    TickArrayManager
)

from vnpy.my_backtesting.strategies.strategyIF import IFStrategy


########################################################################
class VOItrategy(CtaTemplate):
    """基于Tick的交易策略"""
    className = 'VOItrategy'

    # 策略参数
    initDays = 0
    onbook = 0
    N1 = 1
    N2 = 1
    Sigma = 15
    Weight = 1
    SWide = 0
    shiftThreshold1 = 0.75
    shiftThreshold2 = 0.5
    offsetThreshold = 0.3
    # DAY_START = time(8, 45)  # 日盘启动和停止时间
    # DAY_END = time(13, 45)
    # NIGHT_START = time(15, 00)  # 夜盘启动和停止时间
    # NIGHT_END = time(5, 00)
    maxpos = 3

    fixedSize = 1  # 下单数量
    maxorder = 3
    pidnum = 0
    size = 2  # 缓存大小

    # 策略变数
    posPrice = 0  # 持仓价格
    pos = 0  # 持仓数量
    avg_prc = 0
    max_prof = 0
    scorelist = dict(totalscore=0,
                     futscore=0,
                     stkscore=0,
                     stopscore=0)
    deltabalance = 0
    deltaswap = 0

    # 止盈止损参数
    stoppl = {'active': True,
              'stop_ratio': 0.1, 'track_threshold': 0.2, 'fallback_boundary': 0.02,
              'multiplier': 3, }

    # 参数列表，保存了参数的名称
    paramList = ['name',
                 'className',
                 'author',
                 'vtSymbol',
                 'initDays',
                 'Ticksize',
                 'fixedSize',
                 'N1',
                 'N2',
                 'Sigma1',
                 'Sigma2',
                 'Sigma3',
                 'Weight1',
                 'Weight2',
                 'Weight3',
                 'SWide',
                 'vtSymbol',
                 'refdealnum',
                 'wide',
                 'sig_thres',
                 'maxpos',
                 'mult1',
                 'mult2',
                 'sigmode',
                 'stoppl',
                 'max_sigadj',
                 ]

    # 变数清单，保存了变数的名称
    varList = ['inited',
               'trading',
               'pos',
               'posPrice'
               ]

    # 同步清单，保存了需要保存到资料库的变数名称
    syncList = ['pos',
                'posPrice',
                'intraTradeHigh',
                'intraTradeLow']

    # ----------------------------------------------------------------------
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """Constructor"""
        super(VOItrategy, self).__init__(
            cta_engine, strategy_name, vt_symbol, setting
        )

        # 创建Array伫列
        self.tickArray = TickArrayManager(self.size, self.N1, self.N2, self.cta_engine.pids)
        self.sig_set = np.zeros(self.size, dtype=object)

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        # self.load_bar(10)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    # ----------------------------------------------------------------------
    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        for key in self.scorelist:
            self.scorelist[key] = 0
        TA = self.tickArray
        TA.updateTradeBook(tick, 0, 'tradebook')
        # TA.updateRealBook(0)

        # TA.updateRealBook(tick)
        # TA.updateinnerTrade(tick)
        # TA.NewVOIIndex(0) 给纯成交用的
        TA.AdjNewVOIIndex080(0)
        shift = TA.AdjCDFShift(self.Sigma, self.Weight)

        pidnum = 0
        if shift and len(TA.AdjVOIArray) >= 2:
            # if TA.AdjVOIArray[-1] > self.shiftThreshold2:
            #     if TA.AdjVOIArray[-2] >= self.shiftThreshold1:
            #         self.scorelist['totalscore'] = 1
            # if TA.AdjVOIArray[-1] < -self.shiftThreshold2:
            #     if TA.AdjVOIArray[-2] <= self.shiftThreshold1:
            #         self.scorelist['totalscore'] = -1

            if self.pos < 0:
                if TA.AdjVOIArray[-1] >= self.shiftThreshold2:
                    self.scorelist['totalscore'] = 2
            elif self.pos > 0:
                if TA.AdjVOIArray[-1] <= -self.shiftThreshold2:
                    self.scorelist['totalscore'] = -2

            if TA.AdjVOIArray[-2] > self.shiftThreshold1:
                # if self.pos < 0:
                #     self.buy(TA.TickaskPrice1Array[-1], abs(self.pos), False)
                if TA.AdjVOIArray[-1] >= TA.AdjVOIArray[-2]:
                    self.scorelist['totalscore'] = 1

            if TA.AdjVOIArray[-2] < -self.shiftThreshold1:
                # if self.pos > 0:
                #     self.short(TA.TickbidPrice1Array[-1], abs(self.pos), False)
                if TA.AdjVOIArray[-1] <= TA.AdjVOIArray[-2]:
                    self.scorelist['totalscore'] = -1

            self.pos_manager()
            self.orderfilter(pidnum)

        TA.initVOI()

    # ----------------------------------------------------------------------

    def onBook(self, tick):
        pass

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.
        """

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status != Status.NOTTRADED:
            print('order:', order.orderid, order.datetime.time(), order.price, order.volume, order.direction,
                  self.tickArray.Tickset[0, -1]['BidPrice1'], self.tickArray.Tickset[0, -1]['AskPrice1'],
                  self.scorelist)

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        super().on_trade(trade)
        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """
        Callback of stop order update.
        """
        pass
