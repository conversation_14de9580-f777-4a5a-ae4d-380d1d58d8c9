import numpy as np
import pandas as pd
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('TkAgg')
warnings.filterwarnings("ignore")

def generate_sequence(min_val, max_val, step):
    # 计算需要的步数（确保包含最大值）
    n_steps = round((max_val - min_val) / step)
    # 生成等差数列，确保最后一个元素是最大值
    return [round(min_val + i * step,4) for i in range(n_steps + 1)]


def params_spread_esti_main():
    trade_path = 'E:\\Internship\\huatai_fin_inno\\Optimal_Market_Making\\data\\ETF_trade\\'
    trade_date = '20250702'
    etf_trade_data = pd.read_parquet(trade_path+'md_'+trade_date + '_udp_receiver_1_50072.parquet')
    etf_trade_data['timestamp_str'] = pd.to_datetime(etf_trade_data['timestamp_str'], format='%H:%M:%S.%f').dt.time
    etf_trade_data = etf_trade_data[((etf_trade_data['timestamp_str'] >= pd.to_datetime('09:30:00').time())&(etf_trade_data['timestamp_str'] <= pd.to_datetime('11:30:00').time()))|
                                    ((etf_trade_data['timestamp_str'] >= pd.to_datetime('13:00:00').time())&(etf_trade_data['timestamp_str'] <= pd.to_datetime('14:57:00').time()))]

    etf_trade_data['spread'] = etf_trade_data['AskPrice1']-etf_trade_data['BidPrice1']
    etf_trade_data['spread_prev'] = etf_trade_data['spread'].shift(1)
    etf_trade_data['spread_chg'] = etf_trade_data['spread'].diff()
    etf_trade_data = etf_trade_data.dropna(subset=['spread_chg', 'spread_prev'])
    etf_trade_data[['spread','spread_chg', 'spread_prev']] = etf_trade_data[['spread','spread_chg', 'spread_prev']].round(4)
    etf_trade_data = etf_trade_data.reset_index(drop=True)


    #print(round(etf_trade_data['spread'].min(),4), round(etf_trade_data['spread'].max(),4))
    plt.hist(etf_trade_data['spread'], bins=30, edgecolor='black')  # bins控制柱子数量
    plt.title('Spread Distribution Histogram')
    plt.xlabel('Spread ticks')
    plt.ylabel('Frequency')
    plt.grid(axis='y', alpha=0.5)  # 添加横向网格线
    plt.show()


    spread_sequence = generate_sequence(round(etf_trade_data['spread'].min(),4), round(etf_trade_data['spread'].max(),4), 0.001)
    stat_transfer_matrix = pd.DataFrame(columns=spread_sequence,index=spread_sequence)
    etf_trade_data_spread_chg = etf_trade_data[etf_trade_data['spread_chg'] != 0]
    #print(etf_trade_data_spread_chg[['spread','spread_chg','spread_prev']])

    for i in spread_sequence:
        stat_transfer_matrix.loc[i,i] = 0
        for j in spread_sequence:
            if j != i:
                down = len(etf_trade_data_spread_chg[etf_trade_data_spread_chg['spread_prev'] == i])
                up = len(etf_trade_data_spread_chg[(etf_trade_data_spread_chg['spread_prev'] == i) & (etf_trade_data_spread_chg['spread'] == j)])
                prob_ij = up/down
                #print(i, j, up, down)
                stat_transfer_matrix.loc[i,j] = prob_ij
    #print(stat_transfer_matrix)

    etf_trade_data_1 = etf_trade_data[(etf_trade_data['timestamp_str'] >= pd.to_datetime('09:30:00').time()) & (etf_trade_data['timestamp_str'] <= pd.to_datetime('10:30:00').time())]
    etf_trade_data_2 = etf_trade_data[(etf_trade_data['timestamp_str'] > pd.to_datetime('10:30:00').time()) & (etf_trade_data['timestamp_str'] <= pd.to_datetime('11:30:00').time())]
    etf_trade_data_3 = etf_trade_data[(etf_trade_data['timestamp_str'] >= pd.to_datetime('13:00:00').time()) & (etf_trade_data['timestamp_str'] <= pd.to_datetime('14:00:00').time())]
    etf_trade_data_4 = etf_trade_data[(etf_trade_data['timestamp_str'] > pd.to_datetime('14:00:00').time()) & (etf_trade_data['timestamp_str'] <= pd.to_datetime('14:57:00').time())]

    etf_trade_data_hour_lst = [etf_trade_data_1, etf_trade_data_2, etf_trade_data_3, etf_trade_data_4]
    spread_lambda_dict = {}
    spread_intensity_matrix_dict = {}

    for i in range(1,len(etf_trade_data_hour_lst)+1):
        #print(i)
        etf_trade_hour_data = etf_trade_data_hour_lst[i-1]
        etf_trade_hour_data_spread_chg = etf_trade_hour_data[etf_trade_hour_data['spread_chg'] != 0]
        if i != 4:
            spread_lambda = len(etf_trade_hour_data_spread_chg)/3600
        else:
            spread_lambda = len(etf_trade_hour_data_spread_chg)/3420
        spread_lambda_dict[i] = spread_lambda
        intensity_matrix = spread_lambda*stat_transfer_matrix
        spread_intensity_matrix_dict[i] = intensity_matrix
    print(spread_lambda_dict)
    print(spread_intensity_matrix_dict)
    return stat_transfer_matrix, spread_lambda_dict, spread_intensity_matrix_dict


if __name__ == '__main__':
    stat_transfer_matrix, spread_lambda_dict, spread_intensity_matrix_dict = params_spread_esti_main()

