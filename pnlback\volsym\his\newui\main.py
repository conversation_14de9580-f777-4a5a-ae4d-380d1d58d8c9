import os
import sys
from pathlib import Path
from PySide6.QtQml import QQmlApplicationEngine
from PySide6.QtGui import QGuiApplication
from PySide6.QtCore import QUrl, QDir
from backend.volatility_backend import VolatilityBackend

def main():
    # 启用调试输出
    os.environ["QT_DEBUG_PLUGINS"] = "1"
    
    app = QGuiApplication(sys.argv)
    
    # 创建QML引擎
    engine = QQmlApplicationEngine()
    
    # 注册后端
    backend = VolatilityBackend()
    engine.rootContext().setContextProperty("backend", backend)
    
    # 设置QML导入路径
    current_dir = Path(__file__).parent.absolute()
    qml_dir = current_dir / "qml"
    
    # 添加导入路径
    engine.addImportPath(str(qml_dir))
    
    # 设置当前工作目录
    QDir.setCurrent(str(current_dir))
    
    # 加载主QML文件
    qml_file = qml_dir / "main.qml"
    url = QUrl.fromLocalFile(str(qml_file))
    
    # 使用objectCreated信号来监控QML加载状态
    def handle_object_created(obj, url):
        if obj is None:
            print(f"Error loading QML file: {url.toString()}")
            sys.exit(-1)
        else:
            print(f"Successfully loaded QML file: {url.toString()}")
    
    engine.objectCreated.connect(handle_object_created)
    
    # 加载QML
    engine.load(url)
    
    # 检查是否成功加载
    if not engine.rootObjects():
        print("Failed to load QML")
        return -1
    
    return app.exec()

if __name__ == "__main__":
    ret = main()
    print(f"Application exited with code: {ret}")
    sys.exit(ret)