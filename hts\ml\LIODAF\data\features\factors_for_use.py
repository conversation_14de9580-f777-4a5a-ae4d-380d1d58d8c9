use_features=[
        # 基础数据列
        "mid_price",
        # "mid_price_level_2",
        # "mid_price_level_3",
        # "mid_price_level_4",
        # "mid_price_level_5",
        "tradedVol",
        
        # 基本特征 - 价差特征
        "spread_1", 
        # "spread_2",
        # "spread_3",
        # "spread_4",
        # "spread_5",
        "accumulated_price_spread",
        "accumulated_volume_spread",
        
        # 基本特征 - 订单簿不平衡特征
        # "imbalance_1", 
        # "imbalance_2", 
        # "imbalance_3", 
        # "imbalance_4", 
        # "imbalance_5",
        # "total_bid_vol", 
        # "total_ask_vol", 
        # #"vol_imbalance",
        
        # 基本特征 - 价格压力特征
        # "bid_price_diff_1", 
        # "bid_price_diff_2", 
        # "bid_price_diff_3", 
        # "bid_price_diff_4",
        "bid_price_diff_max",
        # "ask_price_diff_1", 
        # "ask_price_diff_2", 
        # "ask_price_diff_3", 
        # "ask_price_diff_4",
        "ask_price_diff_max",
        
        # 时间序列特征 - 价格动量特征
        "mid_return_5", 
        # "mid_return_10", 
        # "mid_return_20",
        # "mid_ma_5", 
        # "mid_ma_10", 
        "mid_ma_20",

        # 时间序列特征 - 交易量特征
        # "vol_ma_5", 
        # "vol_ma_10", 
        "vol_ma_20",
        # "vol_std_5", 
        # "vol_std_10", 
        # "vol_std_20",
        
        # 时间序列特征 - 不平衡滚动特征
        # "imbalance_ma_5", 
        # "imbalance_ma_10", 
        "imbalance_ma_20",
        
        # 高级特征 - 订单流特征
        # "OFI", 
        
        # 高级特征 - 交易强度特征
        # "volume_intensity",
        
        # 高级特征 - 波动率特征
        "volatility_5", 
        # "volatility_10",
        "volatility_20",
        # "realized_absvar_5",
        # "realized_absvar_10",
        # "realized_absvar_20", # volatility_20好用
        # "realized_bipowvar_5",
        # "realized_bipowvar_10",
        # "realized_bipowvar_20", # volatility_20好用
        # "realized_skew_5",
        # "realized_skew_10",
        "realized_skew_20",
        # "realized_kurtosis_5",
        # "realized_kurtosis_10",
        "realized_kurtosis_20",
        # "realized_quarticity_5",
        # "realized_quarticity_10",
        "realized_quarticity_20",
        # "BPV_jump_5", #volatility_5
        # "BPV_jump_10",
        # "BPV_jump_20",
        
        # 订单簿特征 - 深度和流动性
        # 'depth',        # 订单簿深度 IC 0.01 没有重要性
        'slope2',       # 订单簿斜率不平衡度
        'price_impact', # 价格冲击
        'ofi2',         # 订单流不平衡
        # 'wss12',        # 加权标准化价差,accumulated_price_spread 0.86
        
        # 时间序列特征 - 自相关
        # 'autocorr_10_1', 
        # 'autocorr_10_2', 
        # 'autocorr_10_3', # IC 0 没有重要性
        # 'autocorr_20_1', 
        # 'autocorr_20_2', 
        # 'autocorr_20_3'
    ]

# 添加微分特征
for level in [
    # 1,2,3,4, # 没有5好用
    5]:
    use_features.extend([
        f'dAskPrice{level}_dt',
        f'dBidPrice{level}_dt',
        # f'dAskVol{level}_dt',
        # f'dBidVol{level}_dt'
    ])

# 添加技术指标特征
use_features.extend([
    "ob_depth",
    "feature_rank_cov_2",
    "trade_impact",
    "trade_flow",
    "nettradeprice_mid",
    "mom_last",
    "up_pct",
    # "down_pct", # up_pct -1
    "trend_strength",
    # "alpha_49_enhanced", # IC 0.01
    "alpha_101_enhanced",
    # "feature_rising_n_falling_trends",
    "feature_long_short_mean_diff"
]+[f"last_out_ma_{window}" for window in 
   [
    10,
    30,
    # 60,
    # 240
    ]])

# 添加所有技术指标特征
technical_factors2cols = [
    # "volume_corr",
    "Chandelier_Exit",
    # "Fractals_buy",
    # "Fractals_sell",
    "Support_Resistance",
    # "Donchian_Channel_Middle",
    # "Donchian_Channel_Upper",
    # "Donchian_Channel_Lower",
    # "Bollinger_Bands_Upper",
    # "Bollinger_Bands_Middle",
    # "Bollinger_Bands_Lower",
    # "Bollinger_Bands_Signal",
    # "RSI",
    # "MACD",
    # "MACD_Signal",
    # "MACD_Histogram",
    # "Stochastic_Oscillator_K",
    # "Stochastic_Oscillator_D",
    # "ATR",
    # "ADX",
    # "Ichimoku_Cloud_Base",
    "Ichimoku_Cloud_Conversion",
    # "Ichimoku_Cloud_Leading_A",
    # "Ichimoku_Cloud_Leading_B",
    # "OBV",
    # "CCI",
    # "Williams_R",
    # "MFI",
    # "Parabolic_SAR",
    # "Linear_Regression_Line",
    # "Standard_Deviation",
    # "Deviation",
    # "Aroon_Up",
    # "Aroon_Down",
    # "Aroon_Oscillator",
    # "Chaikin_Money_Flow",
    # "Chaikin_Oscillator",
    # "Coppock_Curve",
    # "Detrended_Price_Oscillator",
    # "Elder_Ray_Bull_Power",
    # "Elder_Ray_Bear_Power",
    # "Force_Index",
    # "Keltner_Channel_Upper",
    # "Keltner_Channel_Middle",
    # "Keltner_Channel_Lower",
    # "Mass_Index",
    # "Momentum",
    # "Rate_of_Change",
    # "Relative_Vigor_Index",
    # "Stochastic_RSI",
    # "Triple_Exponential_Average",
    # "TRIX",
    # "Ultimate_Oscillator",
    # "Vortex_Indicator_Positive",
    # "Vortex_Indicator_Negative",
    # "Weighted_Moving_Average",
    # "Hull_Moving_Average",
    # "Volume_Weighted_Average_Price",
    # "Volume_Weighted_Moving_Average",
    # "Percentage_Price_Oscillator",
    # "Percentage_Volume_Oscillator",
    # "Price_Channel_Upper",
    # "Price_Channel_Lower",
    # "Price_Channel_Middle"
]
use_features.extend(technical_factors2cols)