"""
DeepLOB: 基于深度卷积神经网络的订单簿预测模型
@author: AI Assistant
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from utils.utils import log_print
from tqdm import tqdm

class InceptionModule(nn.Module):
    """Inception 模块，用于捕获多尺度特征"""
    
    def __init__(self, in_channels: int, out_channels: int):
        super(InceptionModule, self).__init__()
        
        # 1x1 卷积分支
        self.branch1x1 = nn.Conv2d(in_channels, out_channels//4, kernel_size=1)
        
        # 3x3 卷积分支
        self.branch3x3 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels//4, kernel_size=1),
            nn.Conv2d(out_channels//4, out_channels//4, kernel_size=3, padding=1)
        )
        
        # 5x5 卷积分支
        self.branch5x5 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels//4, kernel_size=1),
            nn.Conv2d(out_channels//4, out_channels//4, kernel_size=5, padding=2)
        )
        
        # 池化分支
        self.branch_pool = nn.Sequential(
            nn.MaxPool2d(kernel_size=3, stride=1, padding=1),
            nn.Conv2d(in_channels, out_channels//4, kernel_size=1)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        branch1x1 = self.branch1x1(x)
        branch3x3 = self.branch3x3(x)
        branch5x5 = self.branch5x5(x)
        branch_pool = self.branch_pool(x)
        
        return torch.cat([branch1x1, branch3x3, branch5x5, branch_pool], dim=1)

class AttentionModule(nn.Module):
    """注意力模块，用于关注重要特征"""
    
    def __init__(self, in_channels: int):
        super(AttentionModule, self).__init__()
        
        self.conv1 = nn.Conv2d(in_channels, in_channels//8, kernel_size=1)
        self.conv2 = nn.Conv2d(in_channels//8, 1, kernel_size=1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        attention = F.relu(self.conv1(x))
        attention = torch.sigmoid(self.conv2(attention))
        return x * attention

class DeepLOB(nn.Module):
    """DeepLOB 模型类"""
    
    def __init__(
        self,
        input_shape: Tuple[int, int, int],
        num_classes: int = 1,
        hidden_size: int = 32,
        num_layers: int = 2,
        dropout: float = 0.2
    ):
        """
        初始化 DeepLOB 模型
        
        参数:
            input_shape: 输入数据形状 (channels, height, width)
            num_classes: 输出类别数
            hidden_size: 隐藏层大小
            num_layers: 卷积层数量
            dropout: Dropout 比率
        """
        super(DeepLOB, self).__init__()
        
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.hidden_size = hidden_size
        
        # 特征提取层
        self.feature_extraction = nn.Sequential(
            nn.Conv2d(input_shape[0], hidden_size, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_size),
            nn.ReLU(),
            nn.Dropout2d(dropout)
        )
        
        # Inception 模块
        current_channels = hidden_size
        self.inception_layers = nn.ModuleList()
        for _ in range(num_layers):
            self.inception_layers.append(InceptionModule(current_channels, current_channels*2))
            current_channels *= 2
        
        # 注意力模块
        self.attention = AttentionModule(current_channels)
        
        # 计算全连接层的输入维度
        fc_input_dim = current_channels * input_shape[1] * input_shape[2]
        
        # 全连接层
        self.fc = nn.Sequential(
            nn.Linear(fc_input_dim, hidden_size*4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size*4, num_classes)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        参数:
            x: 输入张量，形状为 (batch_size, channels, height, width)
            
        返回:
            预测结果
        """
        # 特征提取
        x = self.feature_extraction(x)
        
        # Inception 模块
        for inception in self.inception_layers:
            x = inception(x)
            
        # 注意力机制
        x = self.attention(x)
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层
        x = self.fc(x)
        
        return x

class DeepLOBRegressor:
    """DeepLOB 回归器，用于与现有框架集成"""
    
    def __init__(
        self,
        input_shape: Tuple[int, int, int],
        num_classes: int = 1,
        hidden_size: int = 32,
        num_layers: int = 2,
        dropout: float = 0.2,
        learning_rate: float = 0.001,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        初始化 DeepLOB 回归器
        
        参数:
            input_shape: 输入数据形状
            num_classes: 输出类别数
            hidden_size: 隐藏层大小
            num_layers: 卷积层数量
            dropout: Dropout 比率
            learning_rate: 学习率
            device: 训练设备
        """
        self.device = device
        self.model = DeepLOB(
            input_shape=input_shape,
            num_classes=num_classes,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout
        ).to(device)
        
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate)
        self.criterion = nn.MSELoss()
        
    def fit(
        self,
        X: Union[np.ndarray, pd.DataFrame],
        y: Union[np.ndarray, pd.DataFrame],
        batch_size: int = 32,
        epochs: int = 5,
        validation_split: float = 0.2
    ) -> Dict[str, List[float]]:
        """
        训练模型
        
        参数:
            X: 训练数据，可以是 numpy 数组或 pandas DataFrame
            y: 目标值，可以是 numpy 数组或 pandas DataFrame
            batch_size: 批次大小
            epochs: 训练轮数
            validation_split: 验证集比例
            
        返回:
            训练历史
        """
        # 转换为 numpy 数组
        if isinstance(X, pd.DataFrame):
            X = X.values
        if isinstance(y, pd.DataFrame):
            y = y.values
            
        # 确保数据形状正确
        if len(X.shape) == 2:
            # 如果是 2D 数据，需要重塑为 4D (batch_size, channels, height, width)
            batch_size = X.shape[0]
            channels = self.model.input_shape[0]
            height = self.model.input_shape[1]
            width = self.model.input_shape[2]
            
            # 检查数据是否可以重塑
            if X.shape[1] != channels * height * width:
                raise ValueError(f"输入数据形状 {X.shape} 无法重塑为 (batch_size, {channels}, {height}, {width})")
                
            X = X.reshape(batch_size, channels, height, width)
        
        # 转换为 PyTorch 张量
        X = torch.FloatTensor(X).to(self.device)
        y = torch.FloatTensor(y).to(self.device)
        
        # 划分训练集和验证集
        val_size = int(len(X) * validation_split)
        train_X, val_X = X[:-val_size], X[-val_size:]
        train_y, val_y = y[:-val_size], y[-val_size:]
        
        history = {'train_loss': [], 'val_loss': []}
        
        for epoch in tqdm(range(epochs), desc='Training'):
            # 训练模式
            self.model.train()
            train_loss = 0
            
            # 批次训练
            for i in range(0, len(train_X), batch_size):
                batch_X = train_X[i:i+batch_size]
                batch_y = train_y[i:i+batch_size]
                
                # 前向传播
                pred = self.model(batch_X)
                loss = self.criterion(pred, batch_y)
                
                # 反向传播
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()
                
                train_loss += loss.item()
            
            # 验证模式
            self.model.eval()
            val_loss = 0
            
            with torch.no_grad():
                for i in range(0, len(val_X), batch_size):
                    batch_X = val_X[i:i+batch_size]
                    batch_y = val_y[i:i+batch_size]
                    
                    pred = self.model(batch_X)
                    val_loss += self.criterion(pred, batch_y).item()
            
            # 记录损失
            train_loss /= len(train_X) / batch_size
            val_loss /= len(val_X) / batch_size
            
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            
            if (epoch + 1) % 10 == 0:
                log_print(f'Epoch [{epoch+1}/{epochs}], '
                         f'Train Loss: {train_loss:.4f}, '
                         f'Val Loss: {val_loss:.4f}')
        
        return history
    
    def predict(self, X: Union[np.ndarray, pd.DataFrame]) -> np.ndarray:
        """
        预测
        
        参数:
            X: 输入数据，可以是 numpy 数组或 pandas DataFrame
            
        返回:
            预测结果，一维数组
        """
        # 转换为 numpy 数组
        if isinstance(X, pd.DataFrame):
            X = X.values
            
        # 确保数据形状正确
        if len(X.shape) == 2:
            # 如果是 2D 数据，需要重塑为 4D (batch_size, channels, height, width)
            batch_size = X.shape[0]
            channels = self.model.input_shape[0]
            height = self.model.input_shape[1]
            width = self.model.input_shape[2]
            
            # 检查数据是否可以重塑
            if X.shape[1] != channels * height * width:
                raise ValueError(f"输入数据形状 {X.shape} 无法重塑为 (batch_size, {channels}, {height}, {width})")
                
            X = X.reshape(batch_size, channels, height, width)
        
        self.model.eval()
        X = torch.FloatTensor(X).to(self.device)
        
        with torch.no_grad():
            pred = self.model(X)
        
        # 确保返回一维数组
        return pred.cpu().numpy().ravel()
    
    def save(self, path: str):
        """
        保存模型
        
        参数:
            path: 保存路径
        """
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict()
        }, path)
    
    def load(self, path: str):
        """
        加载模型
        
        参数:
            path: 模型路径
        """
        checkpoint = torch.load(path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
