# -*- coding: utf-8 -*-

import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random
import math


class FutureMMStrategy(StrategyTemplate):
    """"""

    author = "vnpy"

    buy_price = 0
    short_price = 0

    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'edge',
        'validVolume',
        'minEdge',
        'Trend_Flag'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]

    def __init__(
            self,
            strategy_engine: StrategyEngine,
            strategy_name: str,
            vt_symbol: str,
            setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        self.buy_refer_orderids = None
        self.short_refer_orderids = None
        self.buy_max_orderids = None
        self.short_max_orderids = None

    def getFairPrice(self, tick):
        last_price = tick.last_price
        bidP = tick.bid_price_1
        askP = tick.ask_price_1
        ts = self.priceticks[self.maker]
        fair = tick.last_price
        if askP > 0 and bidP > 0:
            if askP - bidP <= 2 * ts:  # 有流动性，中间价
                fair = 0.5 * (askP + bidP)
            elif fair > 0:  # 流动性不足, 不变
                if fair >= askP:
                    fair = askP - ts
                if fair <= bidP:
                    fair = bidP + ts
        return fair

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.avg_refer = 0
        self.avg_maker = 0
        self.avg_Basis = 0
        self.realPnl_refer = 0
        self.realPnl_maker = 0
        self.realPnl_basis = 0
        self.cumPnl_refer = 0
        self.cumPnl_maker = 0
        self.cumPnl_basis = 0
        self.net = 0
        self.maker_delta = 0
        self.refer_delta = 0
        self.basis_delta = 0
        self.fair_maker = 0
        self.fair_refer = 0
        self.rCount = 0
        self.mCount = 0
        self.pauseCount = 0
        self.lastRefer = {"net": 0, "tick": None, "timepoint": 0}
        self.lastMaker = {"pos": 0, "tick": None, "timepoint": 0}
        self.lastSp = {"pos": 0, "tick": None, "timepoint": 0}
        self.lastTicks = {self.maker: 0, self.refer: 0, self.strategy_engine.sp_contract: 0}
        self.comments = None
        self.last_bidP = 0
        self.last_askP = 0
        self.Basis = 0
        self.ValidCount = 0
        self.isQuoting = True
        self.offer_list = []
        self.pos_adjust = 0
        self.offer_list_head = []
        self.onHedgingVolume = 0
        self.hedgeInterval = 1
        self.lastDimmerTime = 0
        self.isValid = False

        self.credit = 0.5

        self.primer = 0
        self.second = 0
        self.lastBasis = []

        self.fair_refer_on_trade = False
        self.isValid = False
        self.makerTrend = 0
        self.makerTrendDt = 0

        self.pos_adjust_bid = 0
        self.pos_adjust_ask = 0

        self.predict_direction = 0

        length = 99999
        self.maker_list = np.zeros(length)  # maker fair price
        self.refer_list = np.zeros(length)  # refer fair price
        self.Basis_list = np.zeros(length)  # Basis

        self.trade_away = 0  #成交后5个tick内away
        self.init_price = 0
        self.target_pos = 0

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key] != self.lastTicks[key]:
                if key == self.maker:
                    self.on_tick_maker(ticks[key])
                if key == self.refer:
                    self.on_tick_refer(ticks[key])
                if key == self.strategy_engine.sp_contract:
                    self.on_tick_sp(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick

    def Trend_trade(self, tick):
        ts = self.priceticks[self.maker]
        if self.getFairPrice(tick) >= self.init_price + self.edge * ts:
            self.init_price == self.getFairPrice(tick)
            if self.target_pos < 3:
                self.target_pos += 1
                self.buy(self.refer, self.lastRefer['tick'].bid_price_1, 1, 'Trend')

        if self.getFairPrice(tick) <= self.init_price - self.edge * ts:
            self.init_price == self.getFairPrice(tick)
            if self.target_pos > -3:
                self.target_pos -= 1
                self.short(self.refer, self.lastRefer['tick'].ask_price_1, 1, 'Trend')

    def redifOrderBook(self, tick_maker, tick_sp, tick_refer):
        if not tick_sp:
            return tick_maker
        else:
            lots = self.lots
            sp_order = self.strategy_engine.sp_order

            # 推导订单
            tick = tick_refer
            ts = self.priceticks[self.maker]
            if sp_order == 1:  # maker & refer 的 sp
                Imply_Order = TickData(
                    bid_price_1=tick.bid_price_1 + tick_sp.bid_price_1,
                    bid_price_2=tick.bid_price_1 + tick_sp.bid_price_2,
                    bid_price_3=tick.bid_price_1 + tick_sp.bid_price_3,
                    bid_price_4=tick.bid_price_1 + tick_sp.bid_price_4,
                    bid_price_5=tick.bid_price_1 + tick_sp.bid_price_5,

                    bid_volume_1=min(tick.bid_volume_1, tick_sp.bid_volume_1),
                    bid_volume_2=min(tick.bid_volume_1, tick_sp.bid_volume_2),
                    bid_volume_3=min(tick.bid_volume_1, tick_sp.bid_volume_3),
                    bid_volume_4=min(tick.bid_volume_1, tick_sp.bid_volume_4),
                    bid_volume_5=min(tick.bid_volume_1, tick_sp.bid_volume_5),

                    ask_price_1=tick.ask_price_1 + tick_sp.ask_price_1,
                    ask_price_2=tick.ask_price_1 + tick_sp.ask_price_2,
                    ask_price_3=tick.ask_price_1 + tick_sp.ask_price_3,
                    ask_price_4=tick.ask_price_1 + tick_sp.ask_price_4,
                    ask_price_5=tick.ask_price_1 + tick_sp.ask_price_5,

                    ask_volume_1=min(tick.ask_volume_1, tick_sp.ask_volume_1),
                    ask_volume_2=min(tick.ask_volume_1, tick_sp.ask_volume_2),
                    ask_volume_3=min(tick.ask_volume_1, tick_sp.ask_volume_3),
                    ask_volume_4=min(tick.ask_volume_1, tick_sp.ask_volume_4),
                    ask_volume_5=min(tick.ask_volume_1, tick_sp.ask_volume_5),

                    gateway_name=self.strategy_engine.gateway_name,
                    symbol=tick_maker.symbol,
                    exchange=tick_refer.exchange,
                    datetime=tick_refer.datetime
                )

            if sp_order == -1:  # refer & maker 的 sp     
                Imply_Order = TickData(
                    bid_price_1=tick.bid_price_1 - tick_sp.ask_price_1,
                    bid_price_2=tick.bid_price_1 - tick_sp.ask_price_2,
                    bid_price_3=tick.bid_price_1 - tick_sp.ask_price_3,
                    bid_price_4=tick.bid_price_1 - tick_sp.ask_price_4,
                    bid_price_5=tick.bid_price_1 - tick_sp.ask_price_5,

                    bid_volume_1=min(tick.bid_volume_1, tick_sp.ask_volume_1),
                    bid_volume_2=min(tick.bid_volume_1, tick_sp.ask_volume_2),
                    bid_volume_3=min(tick.bid_volume_1, tick_sp.ask_volume_3),
                    bid_volume_4=min(tick.bid_volume_1, tick_sp.ask_volume_4),
                    bid_volume_5=min(tick.bid_volume_1, tick_sp.ask_volume_5),

                    ask_price_1=tick.ask_price_1 - tick_sp.bid_price_1,
                    ask_price_2=tick.ask_price_1 - tick_sp.bid_price_2,
                    ask_price_3=tick.ask_price_1 - tick_sp.bid_price_3,
                    ask_price_4=tick.ask_price_1 - tick_sp.bid_price_4,
                    ask_price_5=tick.ask_price_1 - tick_sp.bid_price_5,

                    ask_volume_1=min(tick.ask_volume_1, tick_sp.bid_volume_1),
                    ask_volume_2=min(tick.ask_volume_1, tick_sp.bid_volume_2),
                    ask_volume_3=min(tick.ask_volume_1, tick_sp.bid_volume_3),
                    ask_volume_4=min(tick.ask_volume_1, tick_sp.bid_volume_4),
                    ask_volume_5=min(tick.ask_volume_1, tick_sp.bid_volume_5),

                    gateway_name=self.strategy_engine.gateway_name,
                    symbol=tick_maker.symbol,
                    exchange=tick_refer.exchange,
                    datetime=tick_refer.datetime
                )

            bid_best = max(tick_maker.bid_price_1, Imply_Order.bid_price_1)
            ask_best = min(tick_maker.ask_price_1, Imply_Order.ask_price_1)

            orderbook = TickData(
                bid_price_1=bid_best - 0 * ts,
                bid_price_2=bid_best - 1 * ts,
                bid_price_3=bid_best - 2 * ts,
                bid_price_4=bid_best - 3 * ts,
                bid_price_5=bid_best - 4 * ts,

                bid_volume_1=0,
                bid_volume_2=0,
                bid_volume_3=0,
                bid_volume_4=0,
                bid_volume_5=0,

                ask_price_1=ask_best + 0 * ts,
                ask_price_2=ask_best + 1 * ts,
                ask_price_3=ask_best + 2 * ts,
                ask_price_4=ask_best + 3 * ts,
                ask_price_5=ask_best + 4 * ts,

                ask_volume_1=0,
                ask_volume_2=0,
                ask_volume_3=0,
                ask_volume_4=0,
                ask_volume_5=0,

                gateway_name=self.strategy_engine.gateway_name,
                symbol=tick_maker.symbol,
                exchange=tick_refer.exchange,
                datetime=tick_refer.datetime
            )

            maker_bid_price_list = [tick_maker.__getattribute__(f'bid_price_{level}') for level in range(1, 6)]
            maker_ask_price_list = [tick_maker.__getattribute__(f'ask_price_{level}') for level in range(1, 6)]
            Imply_bid_price_list = [Imply_Order.__getattribute__(f'bid_price_{level}') for level in range(1, 6)]
            Imply_ask_price_list = [Imply_Order.__getattribute__(f'ask_price_{level}') for level in range(1, 6)]

            for level in range(1, 6):  #bid修正
                p = orderbook.__getattribute__(f'bid_price_{level}')
                volume = 0
                if p in maker_bid_price_list:
                    i = maker_bid_price_list.index(p) + 1
                    volume += tick_maker.__getattribute__(f'bid_volume_{i}')
                if p in Imply_bid_price_list:
                    i = Imply_bid_price_list.index(p) + 1
                    volume += Imply_Order.__getattribute__(f'bid_volume_{i}')

                orderbook.__setattr__(f'bid_volume_{level}', volume)

            for level in range(1, 6):  #ask修正
                p = orderbook.__getattribute__(f'ask_price_{level}')
                volume = 0
                if p in maker_ask_price_list:
                    i = maker_ask_price_list.index(p) + 1
                    volume += tick_maker.__getattribute__(f'ask_volume_{i}')
                if p in Imply_ask_price_list:
                    i = Imply_ask_price_list.index(p) + 1
                    volume += Imply_Order.__getattribute__(f'ask_volume_{i}')

                orderbook.__setattr__(f'ask_volume_{level}', volume)

            return orderbook

    def getAsk5(self, tick, ask_volume, away_price):
        volume = 0
        for i in range(1, 6):
            try:
                volume += tick.__getattribute__('ask_volume_%d' % i)
            except:
                volume += 0
            if volume >= ask_volume:
                short_price = tick.__getattribute__('ask_price_%d' % i)
                break
        if volume < ask_volume:
            while i > 0:
                try:
                    short_price = tick.__getattribute__('ask_price_%d' % i) + away_price
                    break
                except:
                    i -= 1
        return short_price

    def getBid5(self, tick, buy_volume, away_price):
        volume = 0
        for i in range(1, 6):
            try:
                volume += tick.__getattribute__('bid_volume_%d' % i)
            except:
                volume += 0
            if volume >= buy_volume:
                buy_price = tick.__getattribute__('bid_price_%d' % i)
                break
        if volume < buy_volume:
            while i > 0:
                try:
                    buy_price = tick.__getattribute__('bid_price_%d' % i) - away_price
                    break
                except:
                    i -= 1
        return buy_price

    def Validtick(self, tick):  #无效的tick不计算ewma
        if (self.getAsk5(tick, 2 * self.lots, 10 * self.priceticks[self.maker]) - self.getBid5(tick, 2 * self.lots,
                                                                                               10 * self.priceticks[
                                                                                                   self.maker]) > self.edge *
                self.priceticks[self.maker]):
            return False
        return True

    def on_tick_sp(self, tick):
        self.lastSp["timepoint"] = tick.datetime
        self.lastSp["net"] = 0
        self.lastSp["tick"] = tick

    def on_pricing(self, tick_maker, tick_sp, tick_refer):

        ts = self.priceticks[self.refer]

        orderbook = self.redifOrderBook(tick_maker, tick_sp, tick_refer)

        validVolume = self.validVolume

        # bidP_best = self.getBid5(orderbook,validVolume,0)
        # askP_best = self.getAsk5(orderbook,validVolume,0)

        theo = (orderbook.bid_price_1 + orderbook.ask_price_1) / 2
        edge = (self.getAsk5(orderbook, validVolume, 0) - self.getBid5(orderbook, validVolume, 0)) / ts
        edge = max(self.minEdge, edge)

        bidP_best = theo - edge / 2 * ts
        askP_best = theo + edge / 2 * ts

        bidP_best = ts * np.floor(bidP_best / ts)
        askP_best = ts * np.ceil(askP_best / ts)

        #bidP
        if self.net >= 2 * self.lots:  #退
            bidP = bidP_best - ts
        elif self.net <= -3 * self.lots:  #进
            bidP = bidP_best + ts
        elif self.trade_away > 0:  #成交后5个tick内away
            bidP = bidP_best - ts
            self.trade_away -= 1
        else:
            bidP = bidP_best

        #askP
        if self.net <= -2 * self.lots:  #退
            askP = askP_best + ts
        elif self.net >= 3 * self.lots:  #进
            askP = askP_best - ts
        elif self.trade_away < 0:  #成交后5个tick内away
            askP = askP_best + ts
            self.trade_away += 1
        else:
            askP = askP_best

        return bidP, askP

    def on_tick_refer(self, tick):
        self.rCount += 1
        self.fair_refer = (tick.bid_price_1 + tick.ask_price_1) / 2
        self.refer_list[self.rCount] = self.fair_refer

        ts = self.priceticks[self.refer]
        net = self.net
        multiplier = self.sizes[self.maker]

        shortResendFlag = False
        buyResendFlag = False

        # 1. Filter ：非可控情景暂停
        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 

            if abs(net) > 1.5 * self.maxPos:
                self.isQuoting = False
                self.pauseCount = self.rCount + 10

            if self.rCount > 5 and (
                    tick.ask_price_1 < self.lastRefer["tick"].bid_price_1 - ts or tick.bid_price_1 > self.lastRefer[
                "tick"].ask_price_1 + ts):
                self.isQuoting = False
                self.pauseCount = self.rCount + 1
                print("refer gap limit pause", tick.datetime)  #

            if not self.isValid:  #maker非有效tick，暂停报价
                self.isQuoting = False
                self.pauseCount = self.rCount + 1
                print("maker invalid pause", tick.datetime)  #

            if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1 > 0 and tick.bid_volume_1 > 0) and (
                    tick.bid_price_1 > float(tick.limit_up) - 10 * ts or  # 涨停附近
                    tick.ask_price_1 < float(tick.limit_down) + 10 * ts):  # 跌停附近
                self.isQuoting = False
                self.pauseCount = self.rCount + 100
                print("price limit pause", tick.datetime)
        else:
            if self.pauseCount < self.rCount and abs(net) < 1.5 * self.maxPos:
                self.isQuoting = True

        if not self.isQuoting:
            if self.buy_vt_orderids:
                self.cancel_order(self.buy_vt_orderids[0])
            if self.short_vt_orderids:
                self.cancel_order(self.short_vt_orderids[0])

        # 2. Quote：定价报价

        if self.isValid:  # 记录Basis和ValidCount
            self.Basis = self.fair_maker - self.fair_refer
            self.Basis_list[self.rCount] = self.Basis
            self.ValidCount += 1

        if self.isQuoting and self.ValidCount > 20:  # 20个有效tick后开始定价报价

            tick_refer = tick
            tick_maker = self.lastMaker['tick']
            tick_sp = self.lastSp['tick']
            self.bidP, self.askP = self.on_pricing(tick_maker, tick_sp, tick_refer)

            if self.last_askP != self.askP:
                shortResendFlag = True
            if self.last_bidP != self.bidP:
                buyResendFlag = True

            self.comments = 'MM'

            if not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(self.maker, self.bidP, self.lots, self.comments)
            elif buyResendFlag and self.buy_vt_orderids[0]:
                self.cancel_order(self.buy_vt_orderids[0])
                self.buy_vt_orderids = self.buy(self.maker, self.bidP, self.lots, self.comments)

            if not self.short_vt_orderids:
                self.short_vt_orderids = self.short(self.maker, self.askP, self.lots, self.comments)
            elif shortResendFlag and self.short_vt_orderids[0]:
                self.cancel_order(self.short_vt_orderids[0])
                self.short_vt_orderids = self.short(self.maker, self.askP, self.lots, self.comments)

            self.last_bidP = self.bidP
            self.last_askP = self.askP

        # Save：数据存储                    
        self.lastRefer["timepoint"] = tick.datetime
        self.lastRefer["net"] = self.net
        self.lastRefer["tick"] = tick

        self.cumPnl_basis = round(self.basis_delta * self.Basis + self.realPnl_basis) * multiplier
        self.cumPnl_maker = round(self.maker_delta * self.fair_maker + self.realPnl_maker) * multiplier
        self.cumPnl_refer = round(self.refer_delta * self.fair_refer + self.realPnl_refer) * multiplier

    def on_tick_maker(self, tick):

        self.mCount += 1
        self.fair_maker = (tick.bid_price_1 + tick.ask_price_1) / 2
        self.maker_list[self.mCount] = self.fair_maker
        self.isValid = self.Validtick(tick)

        ts = self.priceticks[self.maker]
        multiplier = self.sizes[self.maker]
        net = self.net
        lots = self.lots

        shortResendFlag = False
        buyResendFlag = False

        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
            # 持仓超限暂停 、在途订单超限暂停、
            if abs(net) > 1.5 * self.maxPos:
                self.isQuoting = False
                self.pauseCount = self.rCount + 10
                # print("Net Position limit pause",tick.datetime)
            if self.rCount > 5 and self.lastMaker['tick'] and (
                    tick.ask_price_1 < self.lastMaker["tick"].bid_price_1 - ts or tick.bid_price_1 > self.lastMaker[
                "tick"].ask_price_1 + ts):
                self.isQuoting = False
                self.pauseCount = self.rCount + 1
                # print("maker gap limit pause",tick.datetime) #   

            if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1 > 0 and tick.bid_volume_1 > 0) and (
                    tick.bid_price_1 > float(tick.limit_up) - 10 * ts or  # 涨停附近
                    tick.ask_price_1 < float(tick.limit_down) + 10 * ts):  # 跌停附近
                self.isQuoting = False
                self.pauseCount = self.rCount + 100
                print("price limit pause", tick.datetime + timedelta(hours=8))
        else:
            if self.pauseCount < self.rCount and abs(net) < 1.5 * self.maxPos:
                self.isQuoting = True

        if not self.isQuoting:
            if self.buy_vt_orderids:
                self.cancel_order(self.buy_vt_orderids[0])
            if self.short_vt_orderids:
                self.cancel_order(self.short_vt_orderids[0])

        if self.isQuoting and self.ValidCount >= 20:  #20个有效tick后开始定价报价

            if self.ValidCount == 20:
                self.init_price = self.getFairPrice(self.lastMaker['tick'])

            if self.Trend_Flag:
                self.Trend_trade(tick)

            tick_refer = self.lastRefer['tick']
            tick_maker = tick
            tick_sp = self.lastSp['tick']
            self.bidP, self.askP = self.on_pricing(tick_maker, tick_sp, tick_refer)

            if self.last_askP != self.askP:
                shortResendFlag = True
            if self.last_bidP != self.bidP:
                buyResendFlag = True

            self.comments = 'MM'

            if not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(self.maker, self.bidP, self.lots, self.comments)
            elif buyResendFlag and self.buy_vt_orderids[0]:
                self.cancel_order(self.buy_vt_orderids[0])
                self.buy_vt_orderids = self.buy(self.maker, self.bidP, self.lots, self.comments)

            if not self.short_vt_orderids:
                self.short_vt_orderids = self.short(self.maker, self.askP, self.lots, self.comments)
            elif shortResendFlag and self.short_vt_orderids[0]:
                self.cancel_order(self.short_vt_orderids[0])
                self.short_vt_orderids = self.short(self.maker, self.askP, self.lots, self.comments)

            self.last_bidP = self.bidP
            self.last_askP = self.askP

        self.lastMaker["timepoint"] = tick.datetime
        self.lastMaker["pos"] = self.get_pos(self.maker)
        self.lastMaker["tick"] = tick

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume = trade.volume
        price = trade.price
        lots = self.lots

        if trade.symbol + '.' + trade.exchange.value == self.maker:
            self.onMMTrade(trade)
        if trade.symbol + '.' + trade.exchange.value == self.refer:
            self.onReferTrade(trade)

        if volume > 0:
            if trade.direction.value == '多':
                if self.net >= 0:
                    self.avg = (self.net * self.avg + volume * self.fair_refer) / (self.net + volume)
                elif volume + self.net > 0:  # net<0 # 平仓
                    self.avg = self.fair_refer
                self.net += volume
            elif trade.direction.value == '空':
                if self.net <= 0:
                    self.avg = (-self.net * self.avg + volume * self.fair_refer) / (-self.net + volume)
                elif volume - self.net > 0:  # net >0 # 平仓
                    self.avg = self.fair_refer
                self.net -= volume

    def onMMTrade(self, trade: TradeData):
        volume = trade.volume
        price = trade.price
        lots = self.lots

        if volume > 0:  #有成交则撤单，暂停一个tick再报价
            if self.buy_vt_orderids:
                self.cancel_order(self.buy_vt_orderids[0])
            if self.short_vt_orderids:
                self.cancel_order(self.short_vt_orderids[0])
            self.isQuoting = False
            self.pauseCount = self.rCount + 1

        if volume > 0:
            if trade.direction.value == '多':
                self.trade_away = 5
            if trade.direction.value == '空':
                self.trade_away = -5

    def onReferTrade(self, trade: TradeData):
        volume = trade.volume
        price = trade.price
        lots = self.lots
        tick = self.lastRefer["tick"]
        ts = self.priceticks[self.maker]

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids,
            self.buy_refer_orderids,
            self.short_refer_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """  # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if not (
                (dt.time() > dtime(21, 1) and dt.time() < dtime(22, 56))
                or (dt.time() > dtime(9, 1) and dt.time() < dtime(10, 14))
                or (dt.time() > dtime(10, 31) and dt.time() < dtime(11, 29))
                or (dt.time() > dtime(13, 31) and dt.time() < dtime(14, 56))
        ):
            if self.buy_vt_orderids:
                for i in self.buy_vt_orderids:
                    self.cancel_order(i)
            if self.short_vt_orderids:
                for i in self.short_vt_orderids:
                    self.cancel_order(i)
            if self.buy_hedge_orderids:
                for i in self.buy_hedge_orderids:
                    self.cancel_order(i)
            if self.short_hedge_orderids:
                for i in self.short_hedge_orderids:
                    self.cancel_order(i)
            if self.buy_refer_orderids:
                for i in self.buy_refer_orderids:
                    self.cancel_order(i)
            if self.short_refer_orderids:
                for i in self.short_refer_orderids:
                    self.cancel_order(i)
            # self.cancel_all()
            self.MMTrading = False
        else:
            self.MMTrading = True
