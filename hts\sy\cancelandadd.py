# -*- coding: utf-8 -*-
"""
Created on Thu Apr 17 16:24:19 2025

@author: admin
"""

import pandas as pd

def extract_book(snapshot, side="bid"):
    """提取某一快照中的买或卖五档为字典"""
    levels = {}
    for i in range(1, 6):
        price_key = f"{'Bid' if side=='bid' else 'Ask'}Price{i}"
        vol_key = f"{'Bid' if side=='bid' else 'Ask'}Vol{i}"
        if price_key in snapshot and vol_key in snapshot:
            price = snapshot[price_key]
            vol = snapshot[vol_key]
            levels[price] = vol
    return levels

def compute_add_cancel(row, snap_dict):
    """计算每一行 allocation 的五档新增挂单和撤单"""
    start = snap_dict.get(row["start_time_str"], {})
    end = snap_dict.get(row["end_time_str"], {})
    allocation_str = row["allocation"]

    # 解析成交价格和数量
    allocation = {}
    if isinstance(allocation_str, str) and ":" in allocation_str:
        for item in allocation_str.split("|"):
            try:
                price, qty = item.split(":")
                allocation[float(price)] = int(qty)
            except:
                continue

    result = {}

    for side in ["bid", "ask"]:
        book_start = extract_book(start, side)
        book_end = extract_book(end, side)
        all_prices = set(book_start.keys()).union(set(book_end.keys()))

        # 按价格从大到小排序 bid，ask 从小到大
        sorted_prices = sorted(all_prices, reverse=(side == "bid"))

        for i, price in enumerate(sorted_prices[:5]):
            vol_start = book_start.get(price, None)
            vol_end = book_end.get(price, None)
            trade_qty = allocation.get(price, 0)

            if vol_start is None or vol_end is None:
                result[f"{side}_add_{i+1}"] = 0
                result[f"{side}_cancel_{i+1}"] = 0
            else:
                result[f"{side}_add_{i+1}"] = max(0, vol_end - vol_start + trade_qty)
                result[f"{side}_cancel_{i+1}"] = max(0, vol_start - vol_end - trade_qty)
        market_bid = 0
        market_ask = 0
        for price, qty in allocation.items():
            if price >= start.get("AskPrice1", None):
                market_bid += qty
            elif price <= start.get("BidPrice1", None):
                market_ask += qty
        result["marketbid"] = market_bid
        result["marketask"] = market_ask
    return pd.Series(result)

def process_files(snap_file, alloc_file):
    """主函数，输入行情文件路径和成交分布文件路径"""
    snap_df = pd.read_csv(snap_file)
    alloc_df = pd.read_csv(alloc_file)

    # 清洗时间字段
    snap_df["time_str"] = pd.to_datetime(snap_df["time"]).dt.strftime("%Y-%m-%d %H:%M:%S.%f")
    alloc_df["start_time_str"] = pd.to_datetime(alloc_df["start_time"]).dt.strftime("%Y-%m-%d %H:%M:%S.%f")
    alloc_df["end_time_str"] = pd.to_datetime(alloc_df["end_time"]).dt.strftime("%Y-%m-%d %H:%M:%S.%f")

    # 建立快照字典用于快速索引
    snap_dict = snap_df.set_index("time_str").to_dict(orient="index")
    print("Snapshot keys example:", list(snap_dict.keys())[:5])
    print("First row of start_time_str / end_time_str in alloc:", alloc_df[['start_time_str', 'end_time_str']].head())
    # 计算每一行的挂单增撤信息
    book_changes = alloc_df.apply(lambda row: compute_add_cancel(row, snap_dict), axis=1)

    # 拼接结果
    result_df = pd.concat([alloc_df[["start_time", "end_time"]], book_changes], axis=1)
    return result_df

#用法示例（替换成你自己的文件名）：
snap_df = pd.read_csv("sample3.csv")
alloc_df = pd.read_csv("trade_allocation_result5.csv")


# 清洗时间字段
# snap_df["time_str"] = pd.to_datetime(snap_df["time"]).dt.strftime("%Y-%m-%d %H:%M:%S.%f")
# alloc_df["start_time_str"] = pd.to_datetime(alloc_df["start_time"]).dt.strftime("%Y-%m-%d %H:%M:%S.%f")
# alloc_df["end_time_str"] = pd.to_datetime(alloc_df["end_time"]).dt.strftime("%Y-%m-%d %H:%M:%S.%f")

# # 建立快照字典用于快速索引
# snap_dict = snap_df.set_index("time_str").to_dict(orient="index")


result = process_files("sample3.csv", "trade_allocation_result5.csv")
result.to_csv("output_book_changes.csv", index=False)