# coding=utf-8
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
from OmmDatabase import OmmDatabase
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

import sys
sys.path.append("./中信建投/做市/")
from Liquidity import processing,get_Depth,get_Breadth,get_Resilience,get_Tightness,get_Immediacy,get_Depth_Liquidity,get_divergence

_maker = 'hc2103'
_refer = 'hc2105'

date = '2021-01-07'
maker = processing(_maker,date)
refer = processing(_refer,date)

tickN = 5

maker_5 = get_divergence(maker)

# refer_5.reset_index(drop=True,inplace=True)
# fig,ax1=plt.subplots()
# ax1.plot(refer_5['fair'],'b',label='fair')
# plt.legend()
# ax2 = ax1.twinx()
# ax2.plot(refer_5['a-b'],'r')
# plt.show()

upper = 2.0
lower = -2.0
df1 = maker_5
m = 10

df = df1.copy()
df.reset_index(drop=True,inplace=True)

df['signal_1'] = 0
df['signal_2'] = 0


df['signal_1'] = 0
for i in df.index:
    if m<=i < df.shape[0]-m:
        temp = df.iloc[i-m:i+1]
        if (temp['a-b']>=upper).sum() > (temp['a-b']<=lower).sum():
            df.loc[i,'signal_1'] = -1
        if (temp['a-b']>=upper).sum() < (temp['a-b']<=lower).sum():
            df.loc[i,'signal_1'] = 1


df['signal_2'] = 0
for i in df.index:
    if m<=i < df.shape[0]-m:
        temp = df.iloc[i-m:i+1]
        if (temp['a+b']>=5).sum() >= m/2:
            df.loc[i,'signal_2'] = 1



fig,ax1=plt.subplots()
ax1.plot(df['fair'],'b',label='fair')
plt.legend()
ax2 = ax1.twinx()
ax2.plot(df['signal_1'],'r')
plt.show()

maxnums = df.shape[0] - m
m = 5

signal_1 = df[df.signal_1!=0]
signal_1['win'] = 0
for i in signal_1.index:
    if m <= i < maxnums:
        signal1 = df.loc[i, 'fair'] - df.loc[i - m, 'fair']
        signal2 = df.loc[i + m, 'fair'] - df.loc[i, 'fair']
        if signal1 == 0:
            signal_1.loc[i, 'win'] = 2
        elif signal1 != 0 and signal2 == 0:
            signal_1.loc[i, 'win'] = -2
        else:
            signal_1.loc[i, 'win'] = np.sign(signal1 * signal2)

signal_2 = df[df.signal_2 != 0]
signal_2['win'] = 0
for i in signal_2.index:
    if m <= i < maxnums:
        signal1 = df.loc[i, 'fair'] - df.loc[i - m, 'fair']
        signal2 = df.loc[i + m, 'fair'] - df.loc[i, 'fair']
        if signal1 == 0:
            signal_2.loc[i, 'win'] = 2
        elif signal1 != 0 and signal2 == 0:
            signal_2.loc[i, 'win'] = -2
        else:
            signal_2.loc[i, 'win'] = np.sign(signal1 * signal2)

maxnums = df.shape[0] - m

signal_1 = df[df.signal_1==-1]
for i in signal_1.index:
    if m <= i < maxnums:
        signal2 = df.loc[i + m, 'fair'] - df.loc[i, 'fair']
        if signal2 > 0:
            signal_1.loc[i, 'win'] = 1
        elif signal2 == 0:
            signal_1.loc[i, 'win'] = 0
        else:
            signal_1.loc[i, 'win'] = -1

