import numpy as np
from scipy.stats import norm


class OptionGreeks:
    """期权希腊字母计算类"""

    def __init__(self, S, K, T, r, option_price, isigma=0.3, option_type='call'):
        """
        初始化期权参数
        
        Args:
        S: 标的资产现价
        K: 行权价
        T: 到期时间（以年为单位）
        r: 无风险利率（年化）
        sigma: 波动率
        option_type: 期权类型 ('call' 或 'put')
        """
        self.S = S
        self.K = K
        self.T = T
        self.r = r
        self.option_price = option_price
        self.isigma = isigma
        self.option_type = option_type

        # 计算结果缓存
        self._iv = None
        self._d1 = None
        self._d2 = None

    def _calculate_d1d2(self, sigma):
        """计算d1和d2"""
        d1 = (np.log(self.S / self.K) + (self.r + 0.5 * sigma ** 2) * self.T) / (sigma * np.sqrt(self.T))
        d2 = d1 - sigma * np.sqrt(self.T)
        return d1, d2

    def _bs_price(self, sigma):
        """计算BS期权价格"""
        d1, d2 = self._calculate_d1d2(sigma)
        if self.option_type == 'call':
            return self.S * norm.cdf(d1) - self.K * np.exp(-self.r * self.T) * norm.cdf(d2)
        else:
            return self.K * np.exp(-self.r * self.T) * norm.cdf(-d2) - self.S * norm.cdf(-d1)

    def _vega(self, d1):
        """计算vega"""
        return self.S * np.sqrt(self.T) * norm.pdf(d1)

    def calculate_iv(self):
        """计算隐含波动率"""
        if self._iv is not None:
            return self._iv

        sigma = np.asarray(self.isigma)  # 确保是数组
        max_iter, tol = 100, 1e-8

        for _ in range(max_iter):
            price = self._bs_price(sigma)
            d1, _ = self._calculate_d1d2(sigma)
            vega = self._vega(d1)
            diff = self.option_price - price

            # 检查收敛
            if np.all(np.abs(diff) < tol):
                break

            # 更新sigma
            vega = np.where(np.abs(vega) < 1e-10, 1.0, vega)  # 避免除零
            sigma = np.clip(sigma + np.clip(diff / vega, -0.5, 0.5), 0.0001, 5.0)

        self._iv = sigma
        self._d1, self._d2 = self._calculate_d1d2(sigma)
        return self._iv

    @property
    def delta(self):
        """计算delta"""
        if self._d1 is None:
            self.calculate_iv()
        if self.option_type == 'call':
            return norm.cdf(self._d1)
        else:
            return -norm.cdf(-self._d1)

    @property
    def gamma(self):
        """计算gamma"""
        if self._d1 is None:
            self.calculate_iv()
        return norm.pdf(self._d1) / (self.S * self._iv * np.sqrt(self.T))

    @property
    def vega(self):
        """计算vega"""
        if self._d1 is None:
            self.calculate_iv()
        return self._vega(self._d1)

    @property
    def theta(self):
        """计算theta"""
        if self._d1 is None:
            self.calculate_iv()
        if self.option_type == 'call':
            return (-self.S * norm.pdf(self._d1) * self._iv / (2 * np.sqrt(self.T))
                    - self.r * self.K * np.exp(-self.r * self.T) * norm.cdf(self._d2))
        else:
            return (-self.S * norm.pdf(self._d1) * self._iv / (2 * np.sqrt(self.T))
                    + self.r * self.K * np.exp(-self.r * self.T) * norm.cdf(-self._d2))

    @property
    def rho(self):
        """计算rho"""
        if self._d1 is None:
            self.calculate_iv()
        if self.option_type == 'call':
            return self.K * self.T * np.exp(-self.r * self.T) * norm.cdf(self._d2)
        else:
            return -self.K * self.T * np.exp(-self.r * self.T) * norm.cdf(-self._d2)

    @property
    def vanna(self):
        """计算vanna (delta对波动率的导数)"""
        if self._d1 is None:
            self.calculate_iv()
        d1_pdf = norm.pdf(self._d1)
        return -d1_pdf * self._d2 / self._iv

    @property
    def volga(self):
        """计算volga/vomma (vega对波动率的导数)"""
        if self._d1 is None:
            self.calculate_iv()

        return self.vega * (self._d1 * self._d2) / self._iv

    @property
    def charm(self):
        """计算charm (delta对时间的导数)"""
        if self._d1 is None:
            self.calculate_iv()

        d1_pdf = norm.pdf(self._d1)
        if self.option_type == 'call':
            return -d1_pdf * (2 * (self.r * self.T - self._d2 * self._iv * np.sqrt(self.T)) /
                              (2 * self.T * self._iv * np.sqrt(self.T)))
        else:
            return d1_pdf * (2 * (self.r * self.T - self._d2 * self._iv * np.sqrt(self.T)) /
                             (2 * self.T * self._iv * np.sqrt(self.T)))

    @property
    def veta(self):
        """计算veta (vega对时间的导数)"""
        if self._d1 is None:
            self.calculate_iv()

        d1_pdf = norm.pdf(self._d1)
        return -self.S * d1_pdf * np.sqrt(self.T) * (
                self.r * self._d1 / (self._iv * np.sqrt(self.T)) -
                (1 + self._d1 * self._d2) / (2 * self.T)
        )

    @property
    def speed(self):
        """计算speed (gamma对标的价格的导数)"""
        if self._d1 is None:
            self.calculate_iv()

        return -self.gamma * (self._d1 / (self.S * self._iv * np.sqrt(self.T)) + 1 / self.S)

    @property
    def zomma(self):
        """计算zomma (gamma对波动率的导数)"""
        if self._d1 is None:
            self.calculate_iv()

        return self.gamma * (self._d1 * self._d2 - 1) / self._iv

    @property
    def color(self):
        """计算color (gamma对时间的导数)"""
        if self._d1 is None:
            self.calculate_iv()

        d1_pdf = norm.pdf(self._d1)
        return -d1_pdf / (2 * self.S * self.T * self._iv * np.sqrt(self.T)) * (
                2 * self.r * self.T + 1 +
                (2 * (self.r * self.T - self._d2 * self._iv * np.sqrt(self.T)) *
                 (self._d1 / (self._iv * np.sqrt(self.T))))
        )

    def get_all_greeks(self):
        """获取所有希腊字母值"""
        if self._iv is None:
            self.calculate_iv()

        return {
            # 隐含波动率
            'iv': self._iv,

            # 一阶希腊字母
            'delta': self.delta,
            'gamma': self.gamma,
            'vega': self.vega,
            'theta': self.theta,
            'rho': self.rho,

            # 二阶希腊字母
            'vanna': self.vanna,
            'volga': self.volga,
            'charm': self.charm,
            'veta': self.veta,
            'speed': self.speed,
            'zomma': self.zomma,
            'color': self.color,
        }


# 示例使用
if __name__ == '__main__':
    # 设置参数
    S = 100  # 标的价格
    K = 100  # 行权价
    T = 1.0  # 剩余期限（年）
    r = 0.05  # 无风险利率
    option_price = 10  # 期权价格

    # 创建实例
    option = OptionGreeks(S, K, T, r, option_price, isigma=0.3, option_type='call')

    # 获取所有希腊字母
    results = option.get_all_greeks()

    # 打印结果
    print("隐含波动率:")
    print(f"IV: {results['iv']:.4f}\n")

    print("一阶希腊字母:")
    for key in ['delta', 'gamma', 'vega', 'theta', 'rho']:
        print(f"{key.upper()}: {results[key]:.4f}")

    print("\n二阶希腊字母:")
    for key in ['vanna', 'volga', 'charm', 'veta', 'speed', 'zomma', 'color']:
        print(f"{key.upper()}: {results[key]:.4f}")
