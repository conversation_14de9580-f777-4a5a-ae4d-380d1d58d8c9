import pandas as pd
import datetime
import numpy as np

datetoday = '20221121'
str1 = u'vols_%s.csv' % datetoday
str2 = u'TradeRecords%s.csv' % datetoday
atm = 6.25

df = pd.read_csv(u'DATA/SZ500/VOL/' + str1, parse_dates=[0], index_col=0,
                 names=['time', 'spot', 'code', 'tv', 'bid', 'ask', 'sigma', 'a.1', 'a.2', 'a.3', 'a.4', 'a.5', 'delta',
                        'vega', 'a.6', 'a.7', 'rf', 'br', 'time2expiry', 'K', 'Z', 'Call', 'exp', 'a.8', 'a.9', 'a.10',
                        'a.11', 'a.12'], header=None,
                 error_bad_lines=False)
# df=df.set_index(2,append=True)
print(type(df.index[0]))
# df.index=pd.to_datetime(df.index)
# print(type(df.index[0]))

df = pd.concat([df.between_time('09:24:00', '11:30:01'), df.between_time('13:00:00', '15:00:01')])
df3 = df.groupby(df.index)
res = df.resample('20min', label='right').sum()
print(type(df.index[0]))

df2 = pd.read_csv(u'DATA/SZ500/' + str2, parse_dates=[1, 2], index_col=2, names=None, header=0,
                  error_bad_lines=False, encoding='utf-16', sep='\t')
# df2.index=pd.to_datetime(df2.index)

df2.index = pd.to_datetime(datetoday + ' ' + df2.index, format='%Y%m%d %H:%M:%S:%f')
trade = df2.resample('1s', label='right', closed='left').agg({'Delta': 'sum'})

data = df.reset_index()
data = data[data.delta > 0]
output = pd.DataFrame()
output['time2expiry'] = data.groupby(['exp', 'time']).apply(lambda z: z.time2expiry.iloc[0])
output['spot'] = data.groupby(['exp', 'time']).apply(lambda z: z.spot.iloc[0])
output['forward'] = data.groupby(['exp', 'time']).apply(
    lambda z: z.spot.iloc[0] * np.exp(z.rf.iloc[0] * z.time2expiry.iloc[0] - z.br.iloc[0] * z.time2expiry.iloc[0]))
# pnl2=df
# pnl3=

# trade['spot']=0
# trade['spot']=0
# trade['spot']=0

output = output.reset_index()
output['dt'] = output.groupby('time').time2expiry.diff(1)
output['atmvol'] = data.groupby('time').apply(lambda z: z.sigma.iloc[1])

output2 = output[output['exp'] == output['exp'].iloc[0]]

output2=output2.set_index('time')
output2.resample('1s').ffill()
output2 = pd.concat([output2.between_time('09:30:00', '11:30:01'), output2.between_time('13:00:00', '15:00:01')])
output2.to_csv(datetoday + 'vol2.csv')



# time0 = 0
# ii = 0
# for i in df3:
#     time1 = i[0]
#     voldata = i[1]
#     time0 = time0
#     ii = +1
#     print(i)
