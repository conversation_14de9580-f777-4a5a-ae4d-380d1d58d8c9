"""
工具函数
@author: lining
"""
import os
import time
import pandas as pd
import numpy as np
import datetime

from core import config

log_file = os.path.join(config.OUTDIR, 'log.log')


def clear_log():
    """清空日志文件"""
    if os.path.exists(log_file):
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write('')


def log_print(message, level='info', LOG_CONFIG=None, prefix=None):
    """统一的日志打印函数
    
    Args:
        message: 要打印的信息
        level: 日志级别，可选 'info', 'warning', 'error', 'debug'
        LOG_CONFIG: 日志配置字典,默认为None
        prefix: 自定义前缀，将覆盖默认级别前缀
    """
    # 默认配置
    default_config = {
        'enabled': True,
        'levels': {
            'debug': False,
            'info': True,
            'warning': True,
            'error': True
        },
        'use_color': True  # 是否使用彩色输出
    }

    # 使用默认配置或传入的配置
    config_to_use = LOG_CONFIG or default_config

    # 检查是否启用日志，确保level是字符串类型
    level_str = str(level) if not isinstance(level, str) else level
    if not config_to_use['enabled'] or level_str not in config_to_use['levels'] or not config_to_use['levels'][level_str]:
        write_log(message, level_str)
        return

    # 颜色映射（ANSI转义序列）
    color_map = {
        'debug': '\033[36m',    # 青色
        'info': '\033[32m',     # 绿色
        'warning': '\033[33m',  # 黄色
        'error': '\033[31m',    # 红色
        'success': '\033[32m',  # 绿色
        'reset': '\033[0m'      # 重置
    }
    
    # 根据级别选择颜色
    color = color_map.get(level_str, color_map[level]) if config_to_use.get('use_color', True) else ''
    reset = color_map['reset'] if config_to_use.get('use_color', True) else ''
    
    # 日志前缀
    if prefix:
        # 使用自定义前缀
        log_prefix = f"[{prefix}]"
    else:
        # 使用默认级别前缀
        prefix_map = {
            'debug': 'DEBUG', 
            'info': 'INFO', 
            'warning': 'WARN', 
            'error': 'ERROR',
            'success': 'SUCCESS'
        }
        log_prefix = f"[{prefix_map.get(level_str, 'INFO')}]"
    
    # 时间戳
    timestamp = datetime.datetime.now().strftime('%H:%M:%S')
    
    # 打印消息
    print(f"{color}{timestamp} {log_prefix} {message}{reset}")
    
    # 写入日志文件（不含颜色代码）
    write_log(f"{timestamp} {log_prefix} {message}", level_str)


def write_log(message, level):
    """写入日志文件"""
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    # 初始化清空日志文件
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"{timestamp} {level} {message}\n")


def calculate_win_rate_threshold(actual: pd.Series, pred: pd.Series, threshold_percent: float = None, set_threshold: float = None) -> float:
    """根据阈值计算因子胜率
    阈值为绝对值分位数
    """
    if set_threshold is None:
        if threshold_percent is not None:
            threshold = pred.abs().quantile(threshold_percent)
        else:
            threshold = 0
    else:
        threshold = set_threshold
    # 创建预测方向：1表示看涨，-1表示看跌，0表示中性
    pred_direction = pd.Series(0, index=pred.index)
    pred_direction[pred > threshold] = 1
    pred_direction[pred < -threshold] = -1

    # 计算准确率
    actual_threshold = config.ACTUAL_THRESHOLD
    actual_direction = pd.Series(0, index=actual.index)
    actual_direction[actual > actual_threshold] = 1
    actual_direction[actual < -actual_threshold] = -1

    # 计算预测正确的比例，只统计非零预测
    non_zero_predictions = pred_direction != 0
    correct_predictions = (pred_direction == actual_direction) & non_zero_predictions

    # 只计算非零预测的胜率
    if non_zero_predictions.sum() > 0:
        win_rate = float(format(correct_predictions.sum() / non_zero_predictions.sum(), '.4f')) # 胜率,非零预测中正确预测的比例
        signal_ratio = float(format(non_zero_predictions.sum() / len(pred_direction), '.4f')) # 信号比例,非零预测中信号的数量/总数量
        non_zero_count = float(format(non_zero_predictions.sum(), '.4f')) # 非零预测数量,非零预测的数量
        return win_rate, non_zero_count
    return -1, -1  # 如果没有非零预测，返回-1


def search_days(date_str, daysnum, data_path):
    # 加载过去15天的数据，不存在的自动使用前一天数据
    days = []
    date_str = datetime.datetime.strptime(date_str, '%Y%m%d')
    for i in range(0, 300):
        last_date_str = date_str + datetime.timedelta(days=-i)
        last_date_str = last_date_str.strftime('%Y%m%d')
        if not os.path.exists(data_path % last_date_str):
            log_print(f"数据不存在，使用前一天数据: {last_date_str}", 'debug')
        else:
            days.append(last_date_str)

        if len(days) == daysnum:
            days = sorted(days)
            break
    return days
