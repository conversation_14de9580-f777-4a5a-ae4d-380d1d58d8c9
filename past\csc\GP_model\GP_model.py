# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""

#%%
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
import scipy.stats as ss
from OmmDatabase import OmmDatabase
from datetime import datetime
import copy
import seaborn as sns
import numba 
import gc
import pickle
#%%


#%%


#%%


db_path = 'C:/Users/<USER>/Desktop/shfe/shfe_2021-01-04/'
testDB = OmmDatabase(db_path)
date='2021-01-04'
#df = testDB.read_file('./', date) # 期货
df = testDB.read_file('C:/Users/<USER>/Desktop/shfe/shfe_2021-01-04/MarketDataService/default_mkt/HC/', date) # 期货行情



#%%
date=['2021-01-0'+str(4+i) for i in range(5)]
print(date)
df0 = []
for i in range(5):
    db_path = 'C:/Users/<USER>/Desktop/shfe/shfe_' + date[i] +'/'
    testDB = OmmDatabase(db_path)
    df0.append(testDB.read_file('C:/Users/<USER>/Desktop/shfe/shfe_'+ date[i] +'/MarketDataService/default_mkt/HC/', date[i]))



#%%
df.columns

#%%



#%%
s = 100
t = 0
T = 1
sigma = 2
dt = 0.005
q = 0
gamma = 0.1
k = 1.5
A = 140
l = np.random.randn(int(T/dt))

#%%
def p_l(s, t, T, sigma, dt, q, gamma, k, A, l):
    u = []
    d = []
    S = []
    delta = 0.5*(gamma*sigma**2)*(T-t)+(1/gamma)*np.log(1+gamma/k)
    r = s-q*gamma*sigma**2*(T-t)
    u.append(r+delta)
    d.append(r-delta)
    S = [s]
    for i in range(int(T/dt)):     
        s += sigma*np.sqrt(dt)*l[i]
        r = s-q*r*sigma**2*(T-t+(i+1)*dt)
        delta = 0.5*(gamma*sigma**2)*(T-t+(i+1)*dt)+(1/gamma)*np.log(1+gamma/k)
        u.append(r+delta)
        d.append(r-delta)
        S.append(s)
    return (u, d, S)

#%%
a = p_l(s, t, T, sigma, dt, q, gamma, k, A, l)

#%%
plt.figure()
plt.plot([i for i in range(int((T-t)/dt)+1)], a[0])
plt.plot([i for i in range(int((T-t)/dt)+1)], a[1])
plt.plot([i for i in range(int((T-t)/dt)+1)], a[2])
plt.show()



#%%
df.index

#%%
len(df)
#%%
hc2103 = [[] for i in range(5)]
hc03 = []

for i in range(5):
    for j in df0[i].index:
        if df0[i]['instrumentId'][j] == 'hc2103':
            hc2103[i].append(j)

for i in range(5):
    hc03.append(df0[i].loc[hc2103[i]])

#%%
hc2105 = [[] for i in range(5)]
hc05 = []

for i in range(5):
    for j in df0[i].index:
        if df0[i]['instrumentId'][j] == 'hc2105':
            hc2105[i].append(j)

for i in range(5):
    hc05.append(df0[i].loc[hc2105[i]])

#%%


#%%
# hc03[1].columns

#%%





#%%


#%%
def SystemStamp_to_time(t):
    t0 = str(t)
    t1 = int(t0[8:10])
    t2 = int(t0[10:12])
    t3 = int(t0[12:])
    return (3600*t1+60*t2+t3/10**6)

print(SystemStamp_to_time(hc03[1]['SystemStamp'].iloc[1000]))
print(SystemStamp_to_time(hc03[1]['SystemStamp'].iloc[2000]))
#%%
# 时间节点, 只有上午和下午
real_timenode = [[[32700+300*i, 33000+300*i] for i in range(13)]+[[38100+300*i, 38400+300*i] for i in range(10)]+[[48900+300*i, 49200+300*i] for i in range(16)]][0]
print(real_timenode)

#%%
# hc2103的时间点
timenode = [[] for i in range(5)]
j = 0
for k in range(len(timenode)):
    for i in range(1, len(hc03[k]['SystemStamp'])):
        if SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[i-1]) < real_timenode[j][0] and SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[i]) >=  real_timenode[j][0]:
            timenode[k].append([i])
        if SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[i]) < real_timenode[j][1] and SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[i+1]) >=  real_timenode[j][1]:
            timenode[k][j].append(i)
            j += 1
            if j >= len(real_timenode):
                j = 0
                break
print('时间节点', timenode)

#%%
# hc2105的时间点
timenode1 = [[] for i in range(5)]
j = 0
for k in range(len(timenode1)):
    for i in range(1, len(hc05[k]['SystemStamp'])):
        if SystemStamp_to_time(hc05[k]['SystemStamp'].iloc[i-1]) < real_timenode[j][0] and SystemStamp_to_time(hc05[k]['SystemStamp'].iloc[i]) >=  real_timenode[j][0]:
            timenode1[k].append([i])
        if SystemStamp_to_time(hc05[k]['SystemStamp'].iloc[i]) < real_timenode[j][1] and SystemStamp_to_time(hc05[k]['SystemStamp'].iloc[i+1]) >=  real_timenode[j][1]:
            timenode1[k][j].append(i)
            j += 1
            if j >= len(real_timenode):
                j = 0
                break
print('时间节点', timenode1)

#%%
# 统计跳跃, 最大价差为4, 记为 n. 统计价差变化的转移矩阵
n = 4
spread03 = [[] for i in range(len(timenode))]
for k in range(len(timenode)):
    for j in range(len(timenode[k])):
        for i in range(timenode[k][j][0], timenode[k][j][1]):
            spread03[k].append(np.min([-hc03[k]['Bid1Price'].iloc[i]+hc03[k]['Ask1Price'].iloc[i], n]))

trans_p = [[[0 for i in range(n)] for i in range(n)] for i in range(len(timenode))]
for j in range(len(spread03)):
    for k in range(len(spread03[j])-1):
        for i in range(n):
            if spread03[j][k] == i+1:
                if spread03[j][k] != spread03[j][k+1]:
                    trans_p[j][int(spread03[j][k])-1][int(spread03[j][k+1])-1] += 1
for j in range(len(trans_p)):              
    for i in range(len(trans_p[j])):
        trans_p[j][i] = np.array(trans_p[j][i])/np.sum(trans_p[j][i])
trans_p = np.array(trans_p)
print(trans_p) 
#%%   
tr = pd.concat([pd.DataFrame(trans_p[i]) for i in range(len(trans_p))])
tr.columns = ([i+1 for i in range(4)])
tr.index = ([j+1 for i in range(len(trans_p)) for j in range(4)])
# tr.to_csv('C:/Users/<USER>/Desktop/中信建投/3/ppt/转移矩阵.csv')






#%%
# 计算价差变化的泊松参数
# 价差序列全部
n = 4

spread_all = [[] for i in range(len(timenode))]
for k in range(len(spread_all)):
    for i in range(len(hc03[k])):
        spread_all[k].append(np.min([-hc03[k]['Bid1Price'].iloc[i]+hc03[k]['Ask1Price'].iloc[i], n]))

# =============================================================================
# count = 0
# for k in range(len(spread_all)):
#     for i in range(len(spread_all[k])):
#         if not isinstance(spread_all[k][i], float):
#             count += 1
#         if spread_all[k][i] > 4 or spread_all[k][i] < 0:
#             count += 1
# print(count)
# =============================================================================

#%%

            



#%%
Lambda = [[] for i in range(len(timenode))] # 记录不同时间段的lambda
for k in range(len(timenode)):
    for i in range(len(timenode[k])):
        delta_ti = (SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[timenode[k][i][1]])-SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[timenode[k][i][0]]))/0.5
        Ni = 0
        for j in range(timenode[k][i][0], timenode[k][i][1]):
            if spread_all[k][j] !=  spread_all[k][j+1]:
                Ni += 1        
        Lambda[k].append(Ni/delta_ti)
print(Lambda)
#%%
Lam = []
for i in range(len(Lambda)):
    Lam += Lambda[i]
plt.figure()
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False
plt.title('泊松参数Lambda')
plt.plot(Lam)
# plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/Lambda/Lambda.png')
plt.show()
#%%
timenode[0]

#%%
# 各价差下的击中概率
tick = 1
Lambda_abi_ave = [[[0,0,0,0] for i in range(n)] for i in range(len(timenode))]
count = [[[0,0,0,0] for i in range(n)] for i in range(len(timenode))]
for k in range(len(timenode)):
    for i in range(1, n+1):
        na = 0
        na1 = 0
        nb = 0
        nb1 = 0
        n2 = 0 #n只考虑了击中次数
        n1 = 0 #n1 考虑了时间
        if i != 1:
            for l in timenode[k]:
                for j in range(l[0], l[1]):
                    if spread_all[k][j] == i:
                        Ba = hc03[k]['Ask1Price'].iloc[j]
                        Ba_minus = Ba-tick
                        Bb = hc03[k]['Bid1Price'].iloc[j]
                        Bb_plus = Bb+tick
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] < Bb and hc03[k]['tradedVolume'].iloc[j+1] > hc03[k]['tradedVolume'].iloc[j]:
                            nb += 1
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] < Bb_plus and hc03[k]['tradedVolume'].iloc[j+1] > hc03[k]['tradedVolume'].iloc[j]:
                            nb1 += 1
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] > Ba and hc03[k]['tradedVolume'].iloc[j+1] > hc03[k]['tradedVolume'].iloc[j]:
                            na += 1
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] > Ba_minus and hc03[k]['tradedVolume'].iloc[j+1] > hc03[k]['tradedVolume'].iloc[j]:
                            na1 += 1
                        n2 += 1
                        n1 += ((SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[j+1]) - SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[j])))/0.5     
            Lambda_abi_ave[k][i-1] = [na/n1, na1/n1, nb/n1, nb1/n1]
        else:
            for l in timenode[k]:
                for j in range(l[0], l[1]):
                    if spread_all[k][j] == i:
                        Ba = hc03[k]['Ask1Price'].iloc[j]
                        Ba_minus = Ba-tick
                        Bb = hc03[k]['Bid1Price'].iloc[j]
                        Bb_plus = Bb+tick
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] < Bb and hc03[k]['tradedVolume'].iloc[j+1] > hc03[k]['tradedVolume'].iloc[j]:
                            nb += 1
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] <= Bb_plus:
                            nb1 += 1
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] > Ba and hc03[k]['tradedVolume'].iloc[j+1] > hc03[k]['tradedVolume'].iloc[j]:
                            na += 1
                        if hc03[k]['lastPriceOnMarket'].iloc[j+1] >= Ba_minus:
                            na1 += 1
                        n2 += 1
                        n1 += ((SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[j+1]) - SystemStamp_to_time(hc03[k]['SystemStamp'].iloc[j])))/0.5 
            Lambda_abi_ave[k][i-1] = [na/n1, (na1+n1-n2)/n1, nb/n1, (nb1+n1-n2)/n1]            
# =============================================================================
#     if i != 1:       
#         Lambda_abi[i-1] = [na/n2, na1/n2, nb/n2, nb1/n2]
#     if i == 1:       
#         Lambda_abi[i-1] = [(na)/n2, na1/n2, (nb)/n2, nb1/n2]
# =============================================================================
print(Lambda_abi_ave)
#%%
Laa = pd.concat([pd.DataFrame(Lambda_abi_ave[i]) for i in range(len(Lambda_abi_ave))])
Laa.columns = ['Bid', 'Bid+tick', 'Ask', 'Ask-tick']
Laa.index = [j+1 for i in range(len(Lambda_abi_ave)) for j in range(4)]
# Laa.to_csv('C:/Users/<USER>/Desktop/中信建投/3/ppt/报价击穿概率.csv')
Laa

#%%
print(Lambda_abi_ave[0])

#%%
print(count[0])

#%%
hc03[0].columns


#%%
# 计算波动率, 用波动率对击穿概率进行修正
def volatility(data, time_slice): # data 是原始数据, time_slice 是时间段, 格式同timenode[0][0]
    EX2 = 0 # 二阶矩
    EX = 0 # 一阶矩
    for i in range(time_slice[0], time_slice[1]):
        if data['lastPriceOnMarket'].iloc[i]:
            EX2 += data['lastPriceOnMarket'].iloc[i]**2*(((SystemStamp_to_time(data['SystemStamp'].iloc[i+1]) - SystemStamp_to_time(data['SystemStamp'].iloc[i]))))
            EX += data['lastPriceOnMarket'].iloc[i]*(((SystemStamp_to_time(data['SystemStamp'].iloc[i+1]) - SystemStamp_to_time(data['SystemStamp'].iloc[i]))))
    EX2 /= (SystemStamp_to_time(data['SystemStamp'].iloc[time_slice[1]]) - SystemStamp_to_time(data['SystemStamp'].iloc[time_slice[0]]))
    EX /= (SystemStamp_to_time(data['SystemStamp'].iloc[time_slice[1]]) - SystemStamp_to_time(data['SystemStamp'].iloc[time_slice[0]]))
    return np.sqrt(EX2 - (EX)**2)

print(volatility(hc03[0], timenode[0][0]))

#%%
def Lambda_correct(Lambda, vol, Vol): # 通过波动率修正击中概率, 后两个参数分别代表某时间段的波动率和总时间段平均波动率
    return 1-(1-Lambda)**(vol/Vol)

Lambda_abi = [[ 0 for j in range(len(timenode[i]))] for i in range(len(timenode)-1)] # 第二天才开始有数据
v = [[ 0 for j in range(len(timenode[i]))] for i in range(len(timenode))]
Vol = [0 for i in range(len(timenode))]
for i in range(len(timenode)):    
    count = 0
    for j in range(len(timenode[i])):
        vol = volatility(hc03[i], timenode[i][j])
        Vol[i] += vol
        count += 1
        v[i][j] = vol
    Vol[i] /= count

#%%
for i in range(len(timenode)-1):   # 第二天开始才有数据 
    for j in range(len(timenode[i+1])):
        l = [[0 for x in range(4)] for x in range(n)] # n 为价差个数
        for k in range(n):
            for x in range(4):
                if j != 0:
                    l[k][x] = 1-(1-Lambda_abi_ave[i][k][x])**(v[i+1][j-1]/Vol[i])
                else:
                    l[k][x] = 1-(1-Lambda_abi_ave[i][k][x])**(v[i][-1]/Vol[i])
        Lambda_abi[i][j] = l
#%%
print(Lambda_abi[0][0])

#%%
#%%
def time_to_realtime(t): # 将浮点数重新转化为真实时间(字符串)
    hour = t // 3600
    minute = (t % 3600) // 60
    second = (t % 3600) % 60
    t0 = ''
    if hour < 10:
        t0 += '0'
    t0 += str(hour)+':'
    if minute < 10:
        t0 += '0'
    t0 += str(minute)+':'
    if second < 10:
        t0 += '0'
    t0 += str(second)
    return t0

print(time_to_realtime(28888))
#%%        
# 每日波动率图像
for i in range(len(timenode)):
    plt.figure()
    vi = []
    for j in v[i]:
        vi.append(j)
    vi = pd.DataFrame(vi)
    # vi.index = [time_to_realtime(real_timenode[i][0]) for i in range(len(timenode[i]))]
    plt.rcParams['font.sans-serif'] = ['KaiTi']
    plt.rcParams['font.serif'] = ['KaiTi']
    plt.rcParams['axes.unicode_minus'] = False
    plt.plot(vi)
    plt.title('第'+str(i+1)+'天'+'波动率曲线')
    # plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/vol/波动率曲线第'+str(i+1)+'天.png')
    plt.show()
# 总波动率图像
vi = []
for i in range(len(timenode)):
       
    for j in v[i]:
        vi.append(j)
vi = pd.DataFrame(vi)
# vi.index = [time_to_realtime(real_timenode[i][0]) for i in range(len(timenode[i]))]
plt.figure() 
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False
plt.plot(vi)
plt.title('波动率曲线')
# plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/vol/波动率曲线.png')
plt.show()


    
#%%
for i in range(len(timenode)):
    plt.figure()
    # vi.index = [time_to_realtime(real_timenode[i][0]) for i in range(len(timenode[i]))]
    plt.rcParams['font.sans-serif'] = ['KaiTi']
    plt.rcParams['font.serif'] = ['KaiTi']
    plt.rcParams['axes.unicode_minus'] = False
    plt.plot(Lambda[i])
    plt.title('第'+str(i+1)+'天'+'Lambda')
    # plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/Lambda/Lambda曲线第'+str(i+1)+'天.png')
    plt.show()

#%%
plt.figure()
plt.plot(Lambda[0])
plt.show()

#%%
plt.figure()
plt.plot(v[0])
plt.show()

#%%
print(Lambda[0])

#%%
i = 4
t = np.corrcoef(Lambda[i][:-1], Lambda[i][1:])
print(t)

                
            
#%%
hc03[0].iloc[1000]

#%%


#%%


#%%



#%%



#%%
len(hc03) #14151

#%%
hc03[0].head(20)
    



#%%
#%%

# 终值
m = 3 # 价差数目
y_max = 50 # 头寸量最大值
y_min = -50 # 头寸量最小值
delta = 0.01 # 最小跳价
epsilon = 0 # 固定费用
y = 1 #初始头寸
n1 = y_max - y_min #
def phi_T(i, y, delta, epsilon):
    return -np.abs(y)*i*delta/2-epsilon
terminal = [[phi_T(k+1, y, delta, epsilon)] for k in range(m)]

# 转移概率矩阵 m*m
m = 3
r1 = [[0.85, 0.1, 0.05], [0.5, 0.3, 0.2], [0.2, 0.6, 0.1]]

# 击中概率
lambda_b = [[0.2, 1], [0.15, 0.5], [0.1, 0.2]]
lambda_a = [[0.2, 1], [0.15, 0.5], [0.1, 0.2]]

# 挂单上下限, 平仓上下限
l_bar = [i for i in range(6)]
e_bar = [i for i in range(-5, 6)]

#%%

def GP(t = 0, T = 1, delta_t = 0.005, m = 3, y_max = 50, y_min = -50, delta = 0.01, epsilon = 0, gamma = 0.001, r = r1, lambda_b = [[0.2, 1], [0.15, 0.5], [0.1, 0.2]], lambda_a = [[0.2, 1], [0.15, 0.5], [0.1, 0.2]], l_bar = [i for i in range(6)], e_bar = [i for i in range(-5, 6)], market_order = False):
    def phi_T(i, y, delta, epsilon): # 记录终值
        return -np.abs(y)*i*delta/2-epsilon
    n1 = y_max-y_min
    n2 = int((T-t)/delta_t)
    U = [[[0 for i in range(n1+1)] for i in range(m)] for i in range(n2+1)]
    U[-1] = [[phi_T(i+1, k+y_min, delta, epsilon) for k in range(n1+1)] for i in range(m)]
    stratege = [[[[0,0,0,0,0,j+y_min] for j in range(n1+1)] for i in range(m)] for i in range(n2+1)]
    for i in range(2, n2+2):
        for j in range(m):
            for k in range(n1+1):
                record = [0,0,0,0,0,k+y_min] #第一项为买单数量, 第二项为买单价格(Bb/Bb+), 第三, 第四为卖单, 第五项为市价单数量.
                sup_b = -np.inf
                for x in range(2):
                    for l_b in l_bar:
                        if k+l_b <= n1:
                            if x == 0:
                                sup = lambda_b[j][x]*(U[-i+1][j][k+l_b]-U[-i+1][j][k]+l_b*(j+1)*delta/2)
                                if sup > sup_b:
                                    sup_b = sup
                                    record[0] = x
                                    record[1] = l_b
                            if x == 1:
                                sup = lambda_b[j][x]*(U[-i+1][j][k+l_b]-U[-i+1][j][k]+l_b*((j+1)*delta/2-delta))
                                if sup > sup_b:
                                    sup_b = sup
                                    record[0] = x
                                    record[1] = l_b
                sup_a = -np.inf
                for x in range(2):
                    for l_a in l_bar:
                        if k-l_a >= 0:
                            if x == 0:
                                sup = lambda_a[j][x]*(U[-i+1][j][k-l_a]-U[-i+1][j][k]+l_a*(j+1)*delta/2)
                                if sup > sup_a:
                                    sup_a = sup
                                    record[2] = x
                                    record[3] = l_a
                            if x == 1:
                                sup = lambda_a[j][x]*(U[-i+1][j][k-l_a]-U[-i+1][j][k]+l_a*((j+1)*delta/2-delta))
                                if sup > sup_a:
                                    sup_a = sup
                                    record[2] = x
                                    record[3] = l_a
                jump = 0
                for j2 in range(m):
                    if j2 != j:
                        jump += r[j][j2]*(U[-i+1][j2][k]-U[-i+1][j][k])
                U[-i][j][k] = U[-i+1][j][k]+(jump+sup_a+sup_b)-gamma*(k+y_min)**2

# 市价单限价单二选一
# =============================================================================
#                 sup_M = U[-i][j][k]
#                 for e in e_bar:
#                     if k+e <= n1 and k+e >= 0:
#                         sup = -np.abs(e)*(j+1)*delta/2-epsilon+U[-i+1][j][k+e]-gamma*(k+y_min)**2
#                         if sup > sup_M:
#                             record[4] = e # 1记录是否执行市价策略
#                             sup_M = sup
#                 U[-i][j][k] = sup_M
# =============================================================================
                stratege[-i][j][k] = tuple(record)
# 市价单限价单同时进行
        if market_order:
            V = np.array(U[-i]).copy()
            for j in range(m):
                for k in range(n1+1):        
                    sup_M = V[j][k]
                    record = list(stratege[-i][j][k])
                    for e in e_bar:
                        if k+e <= n1 and k+e >= 0:
                            sup = -np.abs(e)*(j+1)*delta/2-epsilon+V[j][k+e]
                            if sup > sup_M:
                                record = list(stratege[-i][j][k+e])
                                record[4] = e 
                                record[5] = k+y_min
                                sup_M = sup
                    U[-i][j][k] = sup_M
                    stratege[-i][j][k] = tuple(record)
    return (U, stratege)

#%%

#%%
# =============================================================================
# if True:
#     print(1)
# 
# =============================================================================


#%%
# =============================================================================
# a = np.array([[[1,2,3] for i in range(3)] for i in range(3)])
# b = a.copy()
# a[0][0][0] = 2
# print(b, type(b))
# =============================================================================

#%%
m = 4 # 价差个数
M = len(timenode) # 时间段的个数

# 从第二天开始才有数据
lambda_b = [[[[(Lambda_abi[k][l][i][0]+Lambda_abi[k][l][i][2])/2, (Lambda_abi[k][l][i][1]+Lambda_abi[k][l][i][3])/2] for i in range(m)] for l in range(len(timenode[k+1]))] for k in range(len(timenode)-1)]
lambda_a = copy.deepcopy(lambda_b)

#%%



#%%
# =============================================================================
# l = pd.DataFrame(lambda_b[0][0])
# l.columns = ['bid', 'bid+tick']
# l.to_csv('C:/Users/<USER>/Desktop/中信建投/3/ppt/lambda_b[0][0].csv')
# 
# =============================================================================



#%%
# 价差的转移概率矩阵, 前一个时刻的lambda和前一天的转移矩阵
r = [[[[0 for i in range(m)] for i in range(m)] for i in range(len(timenode[j]))] for j in range(1, len(timenode))]
for l in range(len(timenode)-1):
    for k in range(len(timenode[l+1])):
        for i in range(m):
            for j in range(m):
                if j != i:
                    if k != 0:
                        r[l][k][i][j] = trans_p[l+1][i][j]*Lambda[l+1][k-1]
                    else:
                        r[l][k][i][j] = trans_p[l+1][i][j]*Lambda[l][-1]
                    r[l][k][i][i] -= r[l][k][i][j]                        
            r[l][k][i][i] += 1


#%%
print(r[3][0])
# pd.DataFrame(r[0][0]).to_csv('C:/Users/<USER>/Desktop/中信建投/3/ppt/转移概率矩阵.csv')
                 

#%%
t = time.time()
test1 = GP(t = 0, T = 330, delta_t = 0.5, m = 4, y_max = 30, y_min = -30, delta = 1, epsilon = 0, gamma = 0.0004, r = r[0][0], lambda_b = lambda_b[0][0], lambda_a = lambda_a[0][0], l_bar = [i for i in range(6)], e_bar = [i for i in range(-5, 6)], market_order = False)
print('运行时间为', time.time()-t)

#%%
test1[1][-10][0]



           
#%%
# 记录策略
# =============================================================================
# print('记录策略')
# t = time.time()
# for i in range(len(timenode)):
#     L1=[GP(t = 0, T = 330, delta_t = 0.5, m = 4, y_max = 30, y_min = -30, delta = 1, epsilon = 0, gamma = 0.001, r = r[i][j], lambda_b = lambda_b[i][j], lambda_a = lambda_a[i][j], l_bar = [k for k in range(6)], e_bar = [k for k in range(-5, 6)], market_order = False) for j in range(len(timenode[i]))]
#     print('策略运行时间为', time.time()-t)
#     t = time.time()
#     f = open('C:/Users/<USER>/Desktop/中信建投/3/save/limit_order'+str(i+1)+'.txt', 'wb')
#     pickle.dump(L1, f)
#     f.close()
#     print('策略保存时间为', time.time()-t)
#     t = time.time()
#     del L1
# =============================================================================

#%%
# =============================================================================
# f = open('C:/Users/<USER>/Desktop/中信建投/3/save/limit_order1.txt', 'wb')
# pickle.dump(L1, f)
# f.close()
# =============================================================================

#%%
# =============================================================================
# f1 = open('C:/Users/<USER>/Desktop/中信建投/3/save/limit_order1.txt', 'rb')
# L1 = pickle.load(f1)
# f1.close()
# =============================================================================
gc.collect()
#%%
# L1==============================================================================================================
# =============================================================================
# L1=[]
# for i in range(len(timenode)):
#     t = time.time()
#     f1 = open('C:/Users/<USER>/Desktop/中信建投/3/save/limit_order'+str(1+i)+'.txt', 'rb')
#     L1.append(pickle.load(f1))
#     f1.close()
#     print('所花时间为', time.time()-t)
# 
# =============================================================================
#%%
# =============================================================================
# L1[0][38][1][-30][0]
# =============================================================================

#%%
# =============================================================================
# print('记录策略2')
# t = time.time()
# for i in range(len(timenode)-1):
#     L2=[GP(t = 0, T = 320, delta_t = 0.5, m = 4, y_max = 30, y_min = -30, delta = 1, epsilon = 0, gamma = 0.0004, r = r[i][j], lambda_b = lambda_b[i][j], lambda_a = lambda_a[i][j], l_bar = [k for k in range(6)], e_bar = [k for k in range(-5, 6)], market_order = False) for j in range(len(timenode[i]))]
#     print('策略运行时间为', time.time()-t)
#     t = time.time()
#     f = open('C:/Users/<USER>/Desktop/中信建投/3/save/limit_order stratege'+str(i+2)+'.txt', 'wb')
#     pickle.dump(L2, f)
#     f.close()
#     print('策略保存时间为', time.time()-t)
#     t = time.time()
#     del L2
# =============================================================================
    

#%%

L2=[]
for i in range(len(timenode)-1):
    t = time.time()
    f1 = open('C:/Users/<USER>/Desktop/中信建投/3/save/limit_order stratege'+str(i+2)+'.txt', 'rb')
    L2.append(pickle.load(f1))
    f1.close()
    print('所花时间为', time.time()-t)

#%%
len(L2)

#%%


#%%
# =============================================================================
# f = open('C:/Users/<USER>/Desktop/中信建投/3/save.txt', 'wb')
# pickle.dump(B, f)
# f.close()
# =============================================================================

#%%
# =============================================================================
# f1 = open('C:/Users/<USER>/Desktop/中信建投/3/save.txt', 'rb')
# data1 = pickle.load(f1)
# data1[0][1][0][1]
# =============================================================================


#%%
# =============================================================================
# f1 = open('C:/Users/<USER>/Desktop/中信建投/3/save.txt', 'rb')
# B = pickle.load(f1)
# f1.close()
# 
# #%%
# 
# 
# 
# #%%
# 
# #%%
# B[0][1][0][0]
# 
# 
# #%%
# def GP1(t = 0, T = 600, delta_t = 0.5, m = 4, y_max = 75, y_min = -75, delta = 0.01, epsilon = 0, gamma = 0.001, unit = 5, r = r1, lambda_b = [[0.2, 1], [0.15, 0.5], [0.1, 0.2]], lambda_a = [[0.2, 1], [0.15, 0.5], [0.1, 0.2]], l_bar = [i for i in range(6)], e_bar = [i for i in range(-5, 6)], market_order = False):
#     n1 = int((y_max-y_min)/unit)
#     n2 = int((T-t)/delta_t)
#     U = [[[0 for i in range(n1+1)] for i in range(m)] for i in range(n2+1)]
#     U[-1] = [[phi_T(i+1, k*unit+y_min, delta, epsilon) for k in range(n1+1)] for i in range(m)]
#     stratege = [[[[0,0,0,0,0,j*unit+y_min] for j in range(n1+1)] for i in range(m)] for i in range(n2+1)]
#     for i in range(2, n2+2):
#         for j in range(m):
#             for k in range(n1+1):
#                 record = [0,0,0,0,0,0, k*unit+y_min]
#                 sup_b = -np.inf
#                 for x in range(2):
#                     for l_b in l_bar:
#                         if k+l_b <= n1:
#                             if x == 0:
#                                 sup = lambda_b[j][x]*(U[-i+1][j][k+l_b]-U[-i+1][j][k]+l_b*unit*(j+1)*delta/2)
#                                 if sup > sup_b:
#                                     sup_b = sup
#                                     record[0] = x
#                                     record[1] = l_b*unit
#                             if x == 1:
#                                 sup = lambda_b[j][x]*(U[-i+1][j][k+l_b]-U[-i+1][j][k]+l_b*unit*((j+1)*delta/2-delta))
#                                 if sup > sup_b:
#                                     sup_b = sup
#                                     record[0] = x
#                                     record[1] = l_b*unit
#                 sup_a = -np.inf
#                 for x in range(2):
#                     for l_a in l_bar:
#                         if k-l_a >= 0:
#                             if x == 0:
#                                 sup = lambda_a[j][x]*(U[-i+1][j][k-l_a]-U[-i+1][j][k]+l_a*unit*(j+1)*delta/2)
#                                 if sup > sup_a:
#                                     sup_a = sup
#                                     record[2] = x
#                                     record[3] = l_a*unit
#                             if x == 1:
#                                 sup = lambda_a[j][x]*(U[-i+1][j][k-l_a]-U[-i+1][j][k]+l_a*unit*((j+1)*delta/2-delta))
#                                 if sup > sup_a:
#                                     sup_a = sup
#                                     record[2] = x
#                                     record[3] = l_a*unit
#                 jump = 0
#                 for j2 in range(m):
#                     if j2 != j:
#                         jump += r[j][j2]*(U[-i+1][j2][k]-U[-i+1][j][k])
#                 U[-i][j][k] = U[-i+1][j][k]+(jump+sup_a+sup_b)-gamma*(k*unit+y_min)**2
#                 stratege[-i][j][k] = tuple(record)
#         if market_order:        
#             V = np.array(U[-i]).copy()
#             for j in range(m):
#                 for k in range(n1+1):
#                     sup_M = V[j][k]
#                     record = list(stratege[-i][j][k])
#                     for e in e_bar:
#                         if k+e <= n1 and k+e >= 0:
#                             sup = -np.abs(e*unit)*(j+1)*delta/2-epsilon+V[j][k+e]
#                             if sup > sup_M:
#                                 record = list(stratege[-i][j][k])
#                                 record[4], record[5] = e*unit, k*unit+e
#                                 sup_M = sup
#                     U[-i][j][k] = sup_M
#                     stratege[-i][j][k] = tuple(record)
#     return (U, stratege)
# 
# 
# #%%
# t = time.time()
# test2 = GP1(t = 0, T = 600, delta_t = 0.5, m = 4, y_max = 75, y_min = -75, delta = 1, epsilon = 0, gamma = 0.0004, unit = 5, r = r[0], lambda_b = lambda_b, lambda_a = lambda_a, l_bar = [i for i in range(2, 7)], e_bar = [i for i in range(-5, 6)], market_order = False) 
# print('运行时间为', time.time()-t)
# #%%
# test2[1][0][1]
# #%%
# 
# #%%
# # =============================================================================
# # t = time.time()
# # B1 = [GP1(t = 0, T = 3600, delta_t = 0.5, m = 4, y_max = 75, y_min = -75, delta = 1, epsilon = 0, gamma = 0.0001, unit = 5, r = r[i], lambda_b = lambda_b, lambda_a = lambda_a, l_bar = [i for i in range(2, 7)], e_bar = [i for i in range(-6, 7)]) for i in range(M)]
# # print('时间', time.time()-t)
# # t = time.time()
# # =============================================================================
# 
# #%%
# # =============================================================================
# # f = open('C:/Users/<USER>/Desktop/中信建投/3/save1.txt', 'wb')
# # pickle.dump(B1, f)
# # f.close()
# # print('时间', time.time()-t)
# # =============================================================================
# 
# #%%
# f2 = open('C:/Users/<USER>/Desktop/中信建投/3/save1.txt', 'rb')
# B1 = pickle.load(f2)
# f2.close()
# 
# #%%
# B1[0][1][0][0]
# 
# #%%
# print((1,1)==(1,2))
# 
# #%%
# 
# def stratege_search(B, inventory, n, m, unit): # n为第n个时刻, m为价差
#     y_min = B[1][0][0][0][-1]
#     inv = (inventory-y_min) // unit
#     rem = (inventory-y_min) % unit    
#     if rem == 0:
#         stratege = B[1][n][m-1][inv]
#     if rem != 0:
#         if rem < unit/2:
#             stratege = B[1][n][m-1][inv]
#         else:
#             stratege = B[1][n][m-1][inv+1]       
#     return stratege
# 
# #%%
# t = time.time()
# for i in range(151):
#     print(stratege_search(B=B1[0], inventory = -75+i, n=0, m=1, unit=5))
# print((time.time()-t))
# 
# #%%
# -7%5
# 
# #%%
# hc03.iloc[0]
# 
# #%%
# (1,2,3)[:2]
# =============================================================================

#%%
hc03[0].columns

#%%
np.random.random()

#%%
# 下面开始回测
def back_test(inventory = 0, DATA = L2, timenode = timenode, typ = hc03[0], tick = 1, hit = 0): # hit 取 0, 1, 2分别代表击中, 击穿, 综合考虑
    tradevolume = 0
    Bidamount = 0
    Askamount = 0
    pnl = [0]
    y_min = DATA[0][1][0][0][0][-1]
    m = len(DATA[0][1][0]) # 价差
    inventory_list = [inventory]
    for i in range(len(timenode)):
        d = int((SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][1]])-SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]]))/(0.5)) # 记录间隔数
        t0 = SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]]) # 记录起始时间
        k = 1
        spr = np.min([typ['Ask1Price'].iloc[timenode[i][0]] - typ['Bid1Price'].iloc[timenode[i][0]], m]) # 价差, 最大为m
        # mid = (typ['Ask1Price'].iloc[timenode[i][0]] + typ['Bid1Price'].iloc[timenode[i][0]])/2
        bid = typ['Bid1Price'].iloc[timenode[i][0]]
        ask = typ['Ask1Price'].iloc[timenode[i][0]]
        bid1volume = typ['Bid1Volume'].iloc[timenode[i][0]]
        ask1volume = typ['Ask1Volume'].iloc[timenode[i][0]]
        stratege = DATA[i][1][0][int(spr-1)][inventory-y_min]
        for j in range(d+1):
            update = 0 #记录是否更新
            if j*0.5+t0 > SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]+k]):
                k += 1
                update = 1
            if j*0.5+t0 > SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]+k]):
                continue
            if stratege[-2] != 0: #平仓一定被触发
                inventory += stratege[-2]
                if stratege[0] < 0:
                    Askamount += -stratege[0]*bid
                if stratege[0] > 0:
                    Bidamount += stratege[0]*ask
            if update == 1:
                last = typ['lastPriceOnMarket'].iloc[timenode[i][0]+k]
                if typ['tradedVolume'].iloc[timenode[i][0]+k] > typ['tradedVolume'].iloc[timenode[i][0]+k-1]:
                    if hit == 0:
                        if stratege[1] > 0:
                            if stratege[0] == 0:
                                if last <= bid:
                                    Bidamount += stratege[1]*bid
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                            if stratege[0] == 1:
                                if last <= bid+tick:
                                    Bidamount += stratege[1]*(bid+tick)
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                        if stratege[3] > 0:
                            if stratege[2] == 0:
                                if last >= ask:
                                    Askamount += stratege[3]*ask
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                            if stratege[2] == 1:
                                if last >= ask-tick:
                                    Askamount += stratege[3]*(ask-tick)
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                    if hit == 1:
                        if stratege[1] > 0:
                            if stratege[0] == 0:
                                if last < bid:
                                    Bidamount += stratege[1]*bid
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                            if stratege[0] == 1:
                                if last < bid+tick:
                                    Bidamount += stratege[1]*(bid+tick)
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                        if stratege[3] > 0:
                            if stratege[2] == 0:
                                if last > ask:
                                    Askamount += stratege[3]*ask
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                            if stratege[2] == 1:
                                if last > ask-tick:
                                    Askamount += stratege[3]*(ask-tick)
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                    if hit == 2:
                        if stratege[1] > 0:
                            if stratege[0] == 0:
                                if last < bid:
                                    Bidamount += stratege[1]*bid
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                                if last == bid:
                                    p = stratege[1]/(stratege[1]+bid1volume)
                                    if np.random.random() < p:
                                        Bidamount += stratege[1]*bid
                                        inventory += stratege[1]
                                        tradevolume += stratege[1]                                         
                            if stratege[0] == 1:
                                if last <= bid+tick:
                                    Bidamount += stratege[1]*(bid+tick)
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                        if stratege[3] > 0:
                            if stratege[2] == 0:
                                if last > ask:
                                    Askamount += stratege[3]*ask
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                                if last == ask:
                                    p = stratege[3]/(stratege[3]+ask1volume)
                                    if np.random.random() < p:
                                        Askamount += stratege[3]*ask
                                        inventory -= stratege[3]
                                        tradevolume += stratege[1]                    
                            if stratege[2] == 1:
                                if last >= ask-tick:
                                    Askamount += stratege[3]*(ask-tick)
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3] 

                bid = typ['Bid1Price'].iloc[timenode[i][0]+k]
                ask = typ['Ask1Price'].iloc[timenode[i][0]+k]
                bid1volume = typ['Bid1Volume'].iloc[timenode[i][0]+k]
                ask1volume = typ['Ask1Volume'].iloc[timenode[i][0]+k]
                spr = np.min([ask-bid, m])
            stratege = DATA[i][1][j][int(spr-1)][inventory-y_min]
            if update == 0:
                pnl.append(pnl[-1])
            else:
                if inventory > 0:
                    pnl.append(Askamount-Bidamount+bid*inventory)
                else:
                    pnl.append(Askamount-Bidamount+ask*inventory)
            if (j == d and i == len(timenode)-1):
                if inventory > 0:
                    Askamount += bid*inventory
                else:
                    Bidamount -= ask*inventory      
                inventory = 0
            inventory_list.append(inventory)
    
    # =============================================================================
    # for i in range(len(time_node2)-1):
    #     d = int((SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i+1]])-SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]]))/(0.5))
    #     t0 = SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]])
    #     k = 1
    #     spr = np.min([hc03['Ask1Price'].iloc[time_node2[i]] - hc03['Bid1Price'].iloc[time_node2[i]], m])
    #     mid = (hc03['Ask1Price'].iloc[time_node2[i]] + hc03['Bid1Price'].iloc[time_node2[i]])/2
    #     bid = hc03['Bid1Price'].iloc[time_node2[i]]
    #     ask = hc03['Ask1Price'].iloc[time_node2[i]]
    #     stratege = DATA[i+len(time_node1)-1][1][0][int(spr-1)][inventory-y_min]
    #     for j in range(d+1):
    #         update = 0 #记录是否更新
    #         if j*0.5+t0 > SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]+k]):
    #             k += 1
    #             update = 1
    #         if stratege[-2] != 0: #平仓一定被触发
    #             inventory += stratege[-2]
    #             if stratege[0] < 0:
    #                 Askamount += -stratege[0]*bid
    #             if stratege[0] > 0:
    #                 Bidamount += stratege[0]*ask
    #         if update == 1:
    #             last = hc03['lastPriceOnMarket'].iloc[time_node2[i]+k]
    #             if stratege[1] > 0:
    #                 if stratege[0] == 0:
    #                     if last <= bid:
    #                         Bidamount += stratege[1]*bid
    #                         inventory += stratege[1]
    #                 if stratege[0] == 1:
    #                     if last <= bid+tick:
    #                         Bidamount += stratege[1]*(bid+tick)
    #                         inventory += stratege[1]
    #             if stratege[3] > 0:
    #                 if stratege[2] == 0:
    #                     if last >= ask:
    #                         Askamount += stratege[3]*ask
    #                         inventory -= stratege[3]
    #                 if stratege[2] == 1:
    #                     if last >= ask-tick:
    #                         Askamount += stratege[3]*(ask-tick)
    #                         inventory -= stratege[3]
    #             bid = hc03['Bid1Price'].iloc[time_node2[i]+k]
    #             ask = hc03['Ask1Price'].iloc[time_node2[i]+k]
    #             spr = np.min([ask-bid, m])
    #         stratege = DATA[i][1][j][int(spr-1)][inventory-y_min]
    #         if update == 0:
    #             pnl.append(pnl[-1])
    #         else:
    #             if inventory > 0:
    #                 pnl.append(Askamount-Bidamount+bid*inventory)
    #             else:
    #                 pnl.append(Askamount-Bidamount+ask*inventory)
    #         inventory_list.append(inventory)
    # =============================================================================
            
    
    if inventory > 0:
        Askamount += bid*inventory
        inventory = 0
    if inventory < 0:
        Bidamount -= ask*inventory
        inventory = 0         
    return (tradevolume, Askamount, Bidamount, pnl, inventory_list)

#%%

#%%
DATA = L2

#%%
# 保存累计收益率序列
pnl_ave = [[] for i in range(len(DATA))]
for day in range(len(DATA)):
    for hit in range(3):
        tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test(inventory = 0, DATA = DATA[day], timenode = timenode[day], typ = hc03[day], tick = tick, hit = hit)
        plt.rcParams['font.sans-serif'] = ['KaiTi']
        plt.rcParams['font.serif'] = ['KaiTi']
        plt.rcParams['axes.unicode_minus'] = False        
        plt.figure()
        plt.title('pnl, Askamount='+str(Askamount)+', Bidamount='+str(Bidamount)+', hit='+str(hit))
        plt.plot(pnl[:])
        # plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/pnl/pnl'+str(day+2)+' hit='+str(hit)+'.png')
        print((Askamount-Bidamount)/tradevolume)
        pnl_ave[day].append((Askamount-Bidamount)/tradevolume)
        plt.show()   

#%%
pnl_ave = pd.DataFrame(pnl_ave)
pnl_ave.columns = ['击中', '击穿', '综合']
pnl_ave.index = [2+i for i in range(len(DATA))]
# pnl_ave.to_csv('C:/Users/<USER>/Desktop/中信建投/3/ppt/平均收益.csv', encoding = 'gbk')
print(pnl_ave)

#%%
# 买卖报价走势序列
for day in range(len(DATA)):
    tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test(inventory = 0, DATA = DATA[day], timenode = timenode[day+1], typ = hc03[day+1], tick = tick, hit = hit)
    plt.rcParams['font.sans-serif'] = ['KaiTi']
    plt.rcParams['font.serif'] = ['KaiTi']
    plt.rcParams['axes.unicode_minus'] = False        
    plt.figure()
    plt.title('第'+str(day+1)+'天买卖报价走势')
    plt.plot([i for i in range(timenode[day][0][0], timenode[day][-1][1])], hc03[day]['Ask1Price'].iloc[timenode[day][0][0]: timenode[day][-1][1]], label = 'Ask')
    plt.plot([i for i in range(timenode[day][0][0], timenode[day][-1][1])], hc03[day]['Bid1Price'].iloc[timenode[day][0][0]: timenode[day][-1][1]], label = 'Bid')
    plt.legend()
    # plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/price/走势'+str(day+1)+'.png')
    plt.show()        

#%%

# 持仓序列
for day in range(len(DATA)):
    for hit in range(3):
        tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test(inventory = 0, DATA = DATA[day], timenode = timenode[day+1], typ = hc03[day+1], tick = tick, hit = hit)
        plt.rcParams['font.sans-serif'] = ['KaiTi']
        plt.rcParams['font.serif'] = ['KaiTi']
        plt.rcParams['axes.unicode_minus'] = False        
        plt.figure()
        plt.title('持仓'+str(day+1)+', hit='+str(hit))
        plt.plot(inventory_list[:])
        # plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/inventory/持仓'+str(day+2)+' hit='+str(hit)+'.png')
        plt.show()   

#%%
c0 = []
c1 = []
for day in range(len(DATA)):
    for hit in range(2):
        tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test(inventory = 0, DATA = DATA[day], timenode = timenode[day+1], typ = hc03[day+1], tick = tick, hit = hit)
        count = 0
        for i in inventory_list:
            if np.abs(i) > 20:
                count += 1
        count /= len(inventory_list)
        count = round(count, 4)
        if hit == 0:
            c0.append(count)
        else:
            c1.append(count)
#%%
print(c0, c1)

#%%
hit = 0
day = 0        
DATA = L2  

tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test(inventory = 0, DATA = DATA[day], timenode = timenode[day+1], typ = hc03[day+1], tick = tick, hit = hit)
#%%
DATA = L2
volume = []
for day in range(len(DATA)):
    for hit in range(2):
        tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test(inventory = 0, DATA = DATA[day], timenode = timenode[day+1], typ = hc03[day+1], tick = tick, hit = hit)
        volume.append((Askamount-Bidamount)/tradevolume)
print(volume)
        

#%%
len(pnl)

#%%
#len(L1[0][1][i][j][k])

    
#%%
    
print(Askamount-Bidamount)

#%%
# 累计收益序列
plt.figure()
plt.title('Askamount='+str(Askamount)+', Bidamount='+str(Bidamount))
plt.plot(pnl[:])

plt.show()
        

#%%
# 走势序列
plt.figure()
plt.plot([i for i in range(timenode[day][0][0], timenode[day][-1][1])], hc03[day]['Ask1Price'].iloc[timenode[day][0][0]: timenode[day][-1][1]])
plt.plot([i for i in range(timenode[day][0][0], timenode[day][-1][1])], hc03[day]['Bid1Price'].iloc[timenode[day][0][0]: timenode[day][-1][1]])
plt.show()

#%%

#%%
#%% 
# 持仓情况
plt.figure()
plt.plot(inventory_list[:])
plt.show()

#%%


#%%
plt.figure()
plt.plot([i for i in range(timenode[day][0][0], timenode[day][-1][1])], hc03[day]['Ask1Price'].iloc[timenode[day][0][0]: timenode[day][-1][1]]-hc03[day]['Bid1Price'].iloc[timenode[day][0][0]: timenode[day][-1][1]])
plt.show()

#%%
s = [] # 记录时间段内的全部价差
for i in range(len(timenode)):
    for j in range(len(timenode[i])):
        for k in range(timenode[i][j][0], timenode[i][j][1]):
            s.append(np.min([hc03[i]['Ask1Price'].iloc[k]-hc03[i]['Bid1Price'].iloc[k], 5]))



#%%
a = plt.hist(s, bins = 5)
#plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/价差.png')
plt.show()
a



#%%



#%%
# =============================================================================
# count = 0
# for i in range(timenode[0][0], timenode[-1][1]):
#     if hc03['lastPriceOnMarket'].iloc[i+1] < hc03['Bid1Price'].iloc[i]:
#         count += 1
#     if hc03['lastPriceOnMarket'].iloc[i+1] > hc03['Ask1Price'].iloc[i]:
#         count += 1
# print(count)
# 
# =============================================================================
#%%
def time_to_realtime(t): # 将浮点数重新转化为真实时间(字符串)
    hour = t // 3600
    minute = (t % 3600) // 60
    second = (t % 3600) % 60
    t0 = ''
    if hour < 10:
        t0 += '0'
    t0 += str(hour)+':'
    if minute < 10:
        t0 += '0'
    t0 += str(minute)+':'
    if second < 10:
        t0 += '0'
    t0 += str(second)
    return t0

print(time_to_realtime(28888))
    
            


#%%
# 挂单的热力图, 挂单位置图
m = 3 # 价差-1
n = -40 # 第n个时刻
day = 0 # 第几天
M = len(timenode[day]) # M个时间段
for m in [0, 3]:
    for n in [-600]:
        a = []
        bid = []
        ask = []
        for i in range(61):
            a.append([DATA[day][j][1][n][m][i][1]+5*L2[day][j][1][n][m][i][0] for j in range(M)])
            bid.append(DATA[day][0][1][n][m][i][0] if DATA[day][0][1][n][m][i][1] > 0 else None)
            ask.append(-DATA[day][0][1][n][m][i][2]+m+1 if DATA[day][0][1][n][m][i][3] > 0 else None)
        a = pd.DataFrame(a)
        a.index = [-30+i for i in range(61)]
        a.columns = [time_to_realtime(real_timenode[i][0]) for i in range(M)]
        # =============================================================================
        # plt.xlabel("I am x")
        # plt.ylabel("I am y")
        # plt.legend()
        # =============================================================================
        sns.set()
        plt.rcParams['font.sans-serif'] = ['KaiTi']
        plt.rcParams['font.serif'] = ['KaiTi']
        plt.rcParams['axes.unicode_minus'] = False
        plt.title('价差'+str(m+1)+', 距离到期'+str(-n) +'个tick' )
        sns.heatmap(a, vmin=-2, vmax=12)
        # plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/stratege/策略'+str(day+1)+' 价差'+str(m+1)+' 距离到期'+str(-n)+'tick.png')
        plt.figure()
        bid = pd.DataFrame(bid)
        bid.index = [-30+i for i in range(61)]
        ask = pd.DataFrame(ask)
        ask.index = [-30+i for i in range(61)]
        plt.plot(bid, label = 'bid')
        plt.plot(ask, label = 'ask')
        plt.title('价差为'+str(m+1)+'时的买卖报价')
        plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/savefig/stratege/价差为'+str(m+1)+'时的买卖报价.png')
        plt.legend()        
        plt.show()

#%%
#gc.collect()

#%%


#%%
      


#%%
# =============================================================================
# i= 2
# print(timenode)
# d = int((SystemStamp_to_time(hc03['SystemStamp'].iloc[timenode[i][1]])-SystemStamp_to_time(hc03['SystemStamp'].iloc[timenode[i][0]]))/(0.5))
# print(hc03['SystemStamp'].iloc[timenode[i][1]])
# print(hc03['SystemStamp'].iloc[timenode[i][0]])
# print(hc03['SystemStamp'].iloc[timenode[i][1]]-hc03['SystemStamp'].iloc[timenode[i][0]])
# print(d)
# =============================================================================



#%%
# 回测2, 加上交易所限制后的回测
# =============================================================================
# Bidamount = 0
# Askamount = 0
# inventory = 0
# tick = 1
# pnl = [0]
# inventory_list = [inventory]
# for i in range(len(time_node1)-1):
#     d = int((SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node1[i+1]])-SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node1[i]]))/(0.5))
#     t0 = SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node1[i]])
#     k = 1
#     spr = np.min([hc03['Ask1Price'].iloc[time_node1[i]] - hc03['Bid1Price'].iloc[time_node1[i]], m])
#     mid = (hc03['Ask1Price'].iloc[time_node1[i]] + hc03['Bid1Price'].iloc[time_node1[i]])/2
#     bid = hc03['Bid1Price'].iloc[time_node1[i]]
#     ask = hc03['Ask1Price'].iloc[time_node1[i]]
#     stratege = stratege_search(B1[i], inventory, 0, int(spr), 5)  #B[i][1][0][int(spr-1)][inventory-y_min]
#     for j in range(d+1):
#         update = 0 #记录是否更新
#         if j*0.5+t0 > SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node1[i]+k]):
#             k += 1
#             update = 1
#         if stratege[-2] == 1: #平仓一定被触发
#             inventory += stratege[0]
#             if stratege[0] < 0:
#                 Askamount += -stratege[0]*bid
#             if stratege[0] > 0:
#                 Bidamount += stratege[0]*ask
#         if update == 0:
#             pass
#         if update == 1:
#             last = hc03['lastPriceOnMarket'].iloc[time_node1[i]+k]
#             if stratege[-2] == 0:
#                 if stratege[1] > 0:
#                     if stratege[0] == 0:
#                         if last < bid:
#                             Bidamount += stratege[1]*bid
#                             inventory += stratege[1]
#                     if stratege[0] == 1:
#                         if last < bid+tick:
#                             Bidamount += stratege[1]*(bid+tick)
#                             inventory += stratege[1]
#                 if stratege[3] > 0:
#                     if stratege[2] == 0:
#                         if last > ask:
#                             Askamount += stratege[3]*ask
#                             inventory -= stratege[3]
#                     if stratege[2] == 1:
#                         if last > ask+tick:
#                             Askamount += stratege[3]*(ask+tick)
#                             inventory -= stratege[3]
#             bid = hc03['Bid1Price'].iloc[time_node1[i]+k]
#             ask = hc03['Ask1Price'].iloc[time_node1[i]+k]
#             spr = np.min([ask-bid, m])
#         stratege = stratege_search(B1[i], inventory, j, int(spr), 5)
#         if update == 0:
#             pnl.append(pnl[-1])
#         else:
#             if inventory > 0:
#                 pnl.append(Askamount-Bidamount+bid*inventory)
#             else:
#                 pnl.append(Askamount-Bidamount+ask*inventory)
#         inventory_list.append(inventory)
# 
# for i in range(len(time_node2)-1):
#     d = int((SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i+1]])-SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]]))/(0.5))
#     t0 = SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]])
#     k = 1
#     spr = np.min([hc03['Ask1Price'].iloc[time_node2[i]] - hc03['Bid1Price'].iloc[time_node2[i]], m])
#     mid = (hc03['Ask1Price'].iloc[time_node2[i]] + hc03['Bid1Price'].iloc[time_node2[i]])/2
#     bid = hc03['Bid1Price'].iloc[time_node2[i]]
#     ask = hc03['Ask1Price'].iloc[time_node2[i]]
#     stratege = stratege = stratege_search(B1[i], inventory, 0, int(spr), 5)
#     for j in range(d+1):
#         update = 0 #记录是否更新
#         if j*0.5+t0 > SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]+k]):
#             k += 1
#             update = 1
#         if stratege[-2] == 1: #平仓一定被触发
#             inventory += stratege[0]
#             if stratege[0] < 0:
#                 Askamount += -stratege[0]*bid
#             if stratege[0] > 0:
#                 Bidamount += stratege[0]*ask
#         if update == 0:
#             pass
#         if update == 1:
#             last = hc03['lastPriceOnMarket'].iloc[time_node2[i]+k]
#             if stratege[-2] == 0:
#                 if stratege[1] > 0:
#                     if stratege[0] == 0:
#                         if last < bid:
#                             Bidamount += stratege[1]*bid
#                             inventory += stratege[1]
#                     if stratege[0] == 1:
#                         if last < bid+tick:
#                             Bidamount += stratege[1]*(bid+tick)
#                             inventory += stratege[1]
#                 if stratege[3] >= 0:
#                     if stratege[2] == 0:
#                         if last > ask:
#                             Askamount += stratege[3]*ask
#                             inventory -= stratege[3]
#                     if stratege[2] == 1:
#                         if last > ask-tick:
#                             Askamount += stratege[3]*(ask-tick)
#                             inventory -= stratege[3]
#             bid = hc03['Bid1Price'].iloc[time_node2[i]+k]
#             ask = hc03['Ask1Price'].iloc[time_node2[i]+k]
#             spr = np.min([ask-bid, m])
#         stratege = stratege_search(B1[i], inventory, j, int(spr), 5)
#         inventory_list.append(inventory)
#         if update == 0:
#             pnl.append(pnl[-1])
#         else:
#             if inventory > 0:
#                 pnl.append(Askamount-Bidamount+bid*inventory)
#             else:
#                 pnl.append(Askamount-Bidamount+ask*inventory)        
# 
# if inventory > 0:
#     Askamount += bid*inventory
#     inventory = 0
# if inventory < 0:
#     Bidamount -= ask*inventory
#     inventory = 0         
# =============================================================================
  
#%%
    
print(Askamount-Bidamount)

#%%
# =============================================================================
# plt.figure()
# plt.plot(pnl)
# plt.title('Askamount='+str(Askamount)+', Bidamount='+str(Bidamount))
# # plt.savefig('C:/Users/<USER>/Desktop/中信建投/3/击穿.png')
# plt.show()
# 
# 
# 
# #%%
# plt.figure()
# plt.plot(inventory_list[23000:24000])
# plt.show()
#         
# 
# 
# #%%    
# a = [i for i in range(10)]
# print(a[-10])
# 
# 
# #%%
# a = tuple([i for i in range(10)])
# a
# 
# =============================================================================

#%%
str(datetime.now())

#%%
t = str(datetime.now())
print(t)

#%%
def trans_t(t):
    t = str(t)
    hour = float(t[11:13])
    minute = float(t[14:16])
    second = float(t[-9:])
    return (3600*hour+60*minute+second)

print(t, trans_t(t))

#%%
#sns.heatmap(L1[0][1][0][0])

#%%
#L1[0][1][-101][2]

#%%
def time_to_realtime(t): # 将浮点数重新转化为真实时间(字符串)
    hour = t // 3600
    minute = (t % 3600) // 60
    second = (t % 3600) % 60
    t0 = ''
    if hour < 10:
        t0 += '0'
    t0 += str(hour)+':'
    if minute < 10:
        t0 += '0'
    t0 += str(minute)+':'
    if second < 10:
        t0 += '0'
    t0 += str(second)
    return t0

print(time_to_realtime(28888))
    
            

#%%
i = 100
j = -101


plt.figure()
plt.plot(hc03[0]['lastPriceOnMarket'][j:j+i], label = '成交价')
plt.plot(hc03[0]['Ask1Price'][j:j+i], label = '卖一')
plt.plot(hc03[0]['Bid1Price'][j:j+i], label = '买一')
plt.legend()
plt.show()

#%%
# 统计涨跌
n = 4 # 第几天
record = []
i = 0
typ = hc03
while True:
    j = i+1
    while True:
        if j >= len(typ[n]['lastPriceOnMarket']):
            break
        if typ[n]['lastPriceOnMarket'].iloc[i] == typ[n]['lastPriceOnMarket'].iloc[j]:
            j += 1          
        else:
            record.append(typ[n]['lastPriceOnMarket'].iloc[i]-typ[n]['lastPriceOnMarket'].iloc[j])
            break
    i = j   
    if j >= len(typ[n]['lastPriceOnMarket']):
        break
print(len(record))    
up = 0
upup = 0
down = 0
downdown = 0
for i in range(len(record)-1):
    if record[i] > 0:
        up += 1
        if record[i+1] > 0:
            upup += 1
    if record[i] < 0:
        down += 1
        if record[i+1] < 0:
            downdown += 1
print(up, upup, upup/up)
print(down, downdown, downdown/down)
t = np.corrcoef(record[:-1], record[1:])
print(t)
#%%
print(len(record))
    


    
#%%
hc03[0].columns

#%%
DATA[0][0][1][0][0][0]
#%%
# 直接挂单的回测
def back_test0(inventory = 0, DATA = L2, timenode = timenode, typ = hc03[0], tick = 1, hit = 0): # hit 取 0, 1, 2分别代表击中, 击穿, 综合考虑
    tradevolume = 0
    Bidamount = 0
    Askamount = 0
    pnl = [0]
    y_min = DATA[0][1][0][0][0][-1]
    m = len(DATA[0][1][0]) # 价差
    inventory_list = [inventory]
    for i in range(len(timenode)):
        d = int((SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][1]])-SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]]))/(0.5)) # 记录间隔数
        t0 = SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]]) # 记录起始时间
        k = 1
        spr = np.min([typ['Ask1Price'].iloc[timenode[i][0]] - typ['Bid1Price'].iloc[timenode[i][0]], m]) # 价差, 最大为m
        # mid = (typ['Ask1Price'].iloc[timenode[i][0]] + typ['Bid1Price'].iloc[timenode[i][0]])/2
        bid = typ['Bid1Price'].iloc[timenode[i][0]]
        ask = typ['Ask1Price'].iloc[timenode[i][0]]
        bid1volume = typ['Bid1Volume'].iloc[timenode[i][0]]
        ask1volume = typ['Ask1Volume'].iloc[timenode[i][0]]
        stratege = (0,5,0,5,0,0)
        for j in range(d+1):
            update = 0 #记录是否更新
            if j*0.5+t0 > SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]+k]):
                k += 1
                update = 1
            if j*0.5+t0 > SystemStamp_to_time(typ['SystemStamp'].iloc[timenode[i][0]+k]):
                continue
            if stratege[-2] != 0: #平仓一定被触发
                inventory += stratege[-2]
                if stratege[0] < 0:
                    Askamount += -stratege[0]*bid
                if stratege[0] > 0:
                    Bidamount += stratege[0]*ask
            if update == 1:
                last = typ['lastPriceOnMarket'].iloc[timenode[i][0]+k]
                if typ['tradedVolume'].iloc[timenode[i][0]+k] > typ['tradedVolume'].iloc[timenode[i][0]+k-1]:
                    if hit == 0:
                        if stratege[1] > 0:
                            if stratege[0] == 0:
                                if last <= bid:
                                    Bidamount += stratege[1]*bid
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                            if stratege[0] == 1:
                                if last <= bid+tick:
                                    Bidamount += stratege[1]*(bid+tick)
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                        if stratege[3] > 0:
                            if stratege[2] == 0:
                                if last >= ask:
                                    Askamount += stratege[3]*ask
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                            if stratege[2] == 1:
                                if last >= ask-tick:
                                    Askamount += stratege[3]*(ask-tick)
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                    if hit == 1:
                        if stratege[1] > 0:
                            if stratege[0] == 0:
                                if last < bid:
                                    Bidamount += stratege[1]*bid
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                            if stratege[0] == 1:
                                if last < bid+tick:
                                    Bidamount += stratege[1]*(bid+tick)
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                        if stratege[3] > 0:
                            if stratege[2] == 0:
                                if last > ask:
                                    Askamount += stratege[3]*ask
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                            if stratege[2] == 1:
                                if last > ask-tick:
                                    Askamount += stratege[3]*(ask-tick)
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                    if hit == 2:
                        if stratege[1] > 0:
                            if stratege[0] == 0:
                                if last < bid:
                                    Bidamount += stratege[1]*bid
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                                if last == bid:
                                    p = stratege[1]/(stratege[1]+bid1volume)
                                    if np.random.random() < p:
                                        Bidamount += stratege[1]*bid
                                        inventory += stratege[1]
                                        tradevolume += stratege[1]                                         
                            if stratege[0] == 1:
                                if last <= bid+tick:
                                    Bidamount += stratege[1]*(bid+tick)
                                    inventory += stratege[1]
                                    tradevolume += stratege[1]
                        if stratege[3] > 0:
                            if stratege[2] == 0:
                                if last > ask:
                                    Askamount += stratege[3]*ask
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3]
                                if last == ask:
                                    p = stratege[3]/(stratege[3]+ask1volume)
                                    if np.random.random() < p:
                                        Askamount += stratege[3]*ask
                                        inventory -= stratege[3]
                                        tradevolume += stratege[1]                    
                            if stratege[2] == 1:
                                if last >= ask-tick:
                                    Askamount += stratege[3]*(ask-tick)
                                    inventory -= stratege[3]
                                    tradevolume += stratege[3] 

                bid = typ['Bid1Price'].iloc[timenode[i][0]+k]
                ask = typ['Ask1Price'].iloc[timenode[i][0]+k]
                bid1volume = typ['Bid1Volume'].iloc[timenode[i][0]+k]
                ask1volume = typ['Ask1Volume'].iloc[timenode[i][0]+k]
                spr = np.min([ask-bid, m])
            stratege = (0,5,0,5,0,0)
            if update == 0:
                pnl.append(pnl[-1])
            else:
                if inventory > 0:
                    pnl.append(Askamount-Bidamount+bid*inventory)
                else:
                    pnl.append(Askamount-Bidamount+ask*inventory)
            if (j == d and i == len(timenode)-1):
                if inventory > 0:
                    Askamount += bid*inventory
                else:
                    Bidamount -= ask*inventory      
                inventory = 0
            inventory_list.append(inventory)
    
    # =============================================================================
    # for i in range(len(time_node2)-1):
    #     d = int((SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i+1]])-SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]]))/(0.5))
    #     t0 = SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]])
    #     k = 1
    #     spr = np.min([hc03['Ask1Price'].iloc[time_node2[i]] - hc03['Bid1Price'].iloc[time_node2[i]], m])
    #     mid = (hc03['Ask1Price'].iloc[time_node2[i]] + hc03['Bid1Price'].iloc[time_node2[i]])/2
    #     bid = hc03['Bid1Price'].iloc[time_node2[i]]
    #     ask = hc03['Ask1Price'].iloc[time_node2[i]]
    #     stratege = DATA[i+len(time_node1)-1][1][0][int(spr-1)][inventory-y_min]
    #     for j in range(d+1):
    #         update = 0 #记录是否更新
    #         if j*0.5+t0 > SystemStamp_to_time(hc03['SystemStamp'].iloc[time_node2[i]+k]):
    #             k += 1
    #             update = 1
    #         if stratege[-2] != 0: #平仓一定被触发
    #             inventory += stratege[-2]
    #             if stratege[0] < 0:
    #                 Askamount += -stratege[0]*bid
    #             if stratege[0] > 0:
    #                 Bidamount += stratege[0]*ask
    #         if update == 1:
    #             last = hc03['lastPriceOnMarket'].iloc[time_node2[i]+k]
    #             if stratege[1] > 0:
    #                 if stratege[0] == 0:
    #                     if last <= bid:
    #                         Bidamount += stratege[1]*bid
    #                         inventory += stratege[1]
    #                 if stratege[0] == 1:
    #                     if last <= bid+tick:
    #                         Bidamount += stratege[1]*(bid+tick)
    #                         inventory += stratege[1]
    #             if stratege[3] > 0:
    #                 if stratege[2] == 0:
    #                     if last >= ask:
    #                         Askamount += stratege[3]*ask
    #                         inventory -= stratege[3]
    #                 if stratege[2] == 1:
    #                     if last >= ask-tick:
    #                         Askamount += stratege[3]*(ask-tick)
    #                         inventory -= stratege[3]
    #             bid = hc03['Bid1Price'].iloc[time_node2[i]+k]
    #             ask = hc03['Ask1Price'].iloc[time_node2[i]+k]
    #             spr = np.min([ask-bid, m])
    #         stratege = DATA[i][1][j][int(spr-1)][inventory-y_min]
    #         if update == 0:
    #             pnl.append(pnl[-1])
    #         else:
    #             if inventory > 0:
    #                 pnl.append(Askamount-Bidamount+bid*inventory)
    #             else:
    #                 pnl.append(Askamount-Bidamount+ask*inventory)
    #         inventory_list.append(inventory)
    # =============================================================================
            
    
    if inventory > 0:
        Askamount += bid*inventory
        inventory = 0
    if inventory < 0:
        Bidamount -= ask*inventory
        inventory = 0         
    return (tradevolume, Askamount, Bidamount, pnl, inventory_list)

#%%
#%%
# 保存累计收益率序列
DATA = L2
pnl_ave = [[] for i in range(len(DATA))]
for day in range(len(DATA)):
    for hit in range(3):
        tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test0(inventory = 0, DATA = DATA[day], timenode = timenode[day], typ = hc03[day], tick = tick, hit = hit)
        plt.rcParams['font.sans-serif'] = ['KaiTi']
        plt.rcParams['font.serif'] = ['KaiTi']
        plt.rcParams['axes.unicode_minus'] = False        
        plt.figure()
        plt.title('pnl, Askamount='+str(Askamount)+', Bidamount='+str(Bidamount)+', hit='+str(hit))
        plt.plot(pnl[:])
        print((Askamount-Bidamount)/tradevolume)
        pnl_ave[day].append((Askamount-Bidamount)/tradevolume)
        plt.show()   


#%%

# 持仓序列
for day in range(len(DATA)):
    for hit in range(3):
        tradevolume, Askamount, Bidamount, pnl, inventory_list = back_test0(inventory = 0, DATA = DATA[day], timenode = timenode[day+1], typ = hc03[day+1], tick = tick, hit = hit)
        plt.rcParams['font.sans-serif'] = ['KaiTi']
        plt.rcParams['font.serif'] = ['KaiTi']
        plt.rcParams['axes.unicode_minus'] = False        
        plt.figure()
        plt.title('持仓'+str(day+1)+', hit='+str(hit))
        plt.plot(inventory_list[:])
        plt.show()   






