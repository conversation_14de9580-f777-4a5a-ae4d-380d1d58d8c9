# -*- coding: utf-8 -*-
"""
Get Market Data from InfluxDB
@author: <PERSON>
"""
from influxdb import InfluxDBClient
import pandas as pd
import numpy as np

def df_convert(result):
    points = result.get_points()
    l = []
    for d in points:
        l.append(d)
    return pd.DataFrame(l)

def getmarketdata(mode,exchange,contract,beginT,endT):
    # client = InfluxDBClient('192.168.203.11',8989,'reader','iamreader','testbase') #上期所prod (只储存3天数据)
    # client = InfluxDBClient('202.0.3.209',8989,'reader','reader','testbase') #大商所prod
    # client = InfluxDBClient('202.0.3.200',8989,'reader','reader','testbase') #郑商所prod
    # client = InfluxDBClient('10.17.30.134',9001,'reader','iamreader','testbase') #历史数据（所有交易所）
    # client.query("show measurements")
    
    
    #client名字
    if mode == 'dev':
        client = InfluxDBClient('10.17.30.134',9001,'reader','iamreader','testbase') #历史数据（所有交易所）
        # client = InfluxDBClient('10.17.30.134',9001,'reader','iamreader','omm') #历史数据（所有交易所）
    elif mode == 'prod' and (exchange == 'SHF' or exchange == 'shf' or exchange == 'SHFE' or exchange == 'INE'or exchange == 'ine'):
        client = InfluxDBClient('192.168.203.11',8989,'reader','iamreader','testbase') #上期所prod (只储存3天数据)      
    elif mode == 'prod' and (exchange == 'CZC' or exchange == 'CZCE' or exchange == 'czc'):
        client = InfluxDBClient('202.0.3.200',8989,'reader','iamreader','testbase') #郑商所prod
    elif mode == 'prod' and (exchange == 'DCE' or exchange == 'dce'):
        client = InfluxDBClient('202.0.3.209',8989,'reader','iamreader','testbase') #大商所prod
        
    #表名
    table_dict = {'SHF':'test10',
                  'shf':'test10',
                  'SHFE':'test10',
                  'DCE':'testdalian',
                  'dce':'testdalian',
                  'czc':'zce_md',
                  'CZC':'zce_md',
                  'CZCE':'zce_md',
                  'INE':'test10',
                  'ine':'test10'}
    
    df = df_convert(client.query("select * from %s where time >= %d and time <= %d and insid_md='%s';"%(table_dict[exchange],beginT,endT,contract)))
    # print ("select * from %s where time >= %d and time <= %d and insid_md='%s';"%(table_dict[exchange],beginT,endT,contract))
    return df