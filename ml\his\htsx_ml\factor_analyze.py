"""
单因子分析脚本

描述：
    该脚本用于对单因子进行筛选分析，计算因子在不同时间窗口下的 IC 值（Pearson IC 和 Rank IC）。
    通过调用因子计算函数，对每日数据进行处理，并输出因子在不同时间窗口的表现。
    可以直接运行该脚本，测试指定的因子的ic，一般建议因子5s ic值大于0.01可以被认为有效的

作者: [cyr]
日期: [250117]
版本: 1.0

依赖库：
    - pandas
    - numpy
    - factors.ob_std (自定义模块)
    - data_process (自定义模块)

"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from data_process import SingleFactorAnalyzeDataGenerator
from factors.ob_std import *
from factors.ob_extra import *

class FactorAnalyzer:
    """
    单因子分析类。

    职责：
        对因子数据进行分析，计算 IC 值（Pearson IC 和 Rank IC）。

    方法：
        - calculate_ic: 计算 IC 值。
        - single_factor_analyze: 对单个因子进行分析。
        - factor_analyze: 对多个因子进行分析。
    """

    @staticmethod
    def calculate_ic(factor: pd.Series, target: pd.Series) -> tuple:
        """
        计算因子值与目标变量之间的 Pearson IC 和 Rank IC。

        Args:
            factor (pd.Series): 因子值序列。
            target (pd.Series): 目标变量序列。

        Returns:
            tuple: 包含 Pearson IC 和 Rank IC 的元组。
        """
        pearson_ic = np.corrcoef(factor, target)[0, 1]
        rank_ic = np.corrcoef(factor.rank(), target.rank())[0, 1]
        return pearson_ic, rank_ic
    
    @staticmethod   
    def analyze_factor_distribution(factor_series):
        """
        分析因子分布，绘制直方图、KDE图、箱线图，并输出统计指标。
        
        参数:
            factor_series (pd.Series): 待分析的因子值序列
        
        返回:
            fig (matplotlib.figure.Figure): 绘图对象
        """
        # 预处理,移除无穷值和缺失值
        factor = factor_series.replace([np.inf, -np.inf], np.nan).dropna()
        print(f'无效值比例: {(len(factor_series) - len(factor))/len(factor_series)}')

        # 创建画布
        fig = plt.figure(figsize=(15, 10))
        plt.suptitle(f'factor distribution (N={len(factor)})', y=1.02, fontsize=14)
        
       
        # 子图1: 直方图 + KDE + 正态分布参考线
        ax1 = plt.subplot2grid((2, 2), (0, 0), colspan=2)
        sns.histplot(factor, kde=True, stat='density', color='skyblue', edgecolor='white', bins=50)
        
        xmin, xmax = factor.min(), factor.max()
        x = np.linspace(xmin, xmax, 100)
        normal_pdf = stats.norm.pdf(x, loc=factor.mean(), scale=factor.std())
        ax1.plot(x, normal_pdf, 'r--', label='normal', alpha=0.7)
        
        ax1.set_xlabel('factor', fontsize=10)
        ax1.set_ylabel('density', fontsize=10)
        ax1.legend()
        
        # 子图2: 箱线图（检测异常值）
        ax2 = plt.subplot2grid((2, 2), (1, 0))
        sns.boxplot(x=factor, color='lightgreen', flierprops={'marker': 'o', 'markersize': 4})
        ax2.set_xlabel('factor value', fontsize=10)
        
        # 子图3: Q-Q图（检验正态性）
        ax3 = plt.subplot2grid((2, 2), (1, 1))
        stats.probplot(factor, dist='norm', plot=ax3)
        ax3.get_lines()[0].set_markersize(4)
        ax3.get_lines()[1].set_color('r')
        ax3.set_title('Q-Q figure', fontsize=10)
        
        plt.tight_layout()
        
        # 输出统计指标
        stats_dict = {
            '观测数': len(factor),
            '均值': factor.mean(),
            '标准差': factor.std(),
            '偏度': factor.skew(),
            '峰度': factor.kurtosis(),
            'Jarque-Bera检验': stats.jarque_bera(factor)[0],
            'JB检验P值': stats.jarque_bera(factor)[1],
        }
        
        print("因子分布统计指标:")
        for key, value in stats_dict.items():
            print(f"- {key}: {value:.4f}")

        return fig

    # # 示例调用
    # factor = pd.Series(np.random.normal(0, 1, 1000))  
    # fig = analyze_factor_distribution(factor)
    # plt.show()
    
    @staticmethod
    def single_factor_analyze(daily_factors: pd.Series, daily_targets: pd.DataFrame) -> None:
        """
        计算单个因子在不同时间窗口的 IC 值或 Rank IC 值。

        Args:
            daily_factors (pd.Series): 因子值序列。
            daily_targets (pd.DataFrame): 目标变量序列，包含多个不同的时间窗口。

        Returns:
            None: 直接打印每个时间窗口的 IC 值或 Rank IC 值。
        """
        col_name = daily_targets.columns
        results = {}
        FactorAnalyzer.analyze_factor_distribution(daily_factors)
        plt.savefig('C:/Users/<USER>/Desktop/data/fig/factor_distribution.png')

        for col in col_name:
            # 计算滚动窗口的因子值和目标值
            target_col = daily_targets[col]

            # 去除空值
            valid_idx = target_col.notna() & daily_factors.notna()
            target = target_col[valid_idx]
            factor = daily_factors[valid_idx]

            # 计算 IC
            pearson_ic, rank_ic = FactorAnalyzer.calculate_ic(factor, target)
            results[f'{col}s'] = {'Pearson IC': pearson_ic, 'Rank IC': rank_ic}

        # 输出结果
        print(f"因子在不同时间窗口的 IC 值：")
        for window, ic_values in results.items():
            print(f"{window}: Pearson IC = {ic_values['Pearson IC']:.4f}, Rank IC = {ic_values['Rank IC']:.4f}")

    @staticmethod
    def factor_analyze(func: callable, daily_data: list, windows: list = [5, 10, 30, 60], **kwargs) -> None:
        """
        对给定的因子函数和每日数据进行因子分析，计算不同时间窗口的 IC 值。

        Args:
            func (callable): 因子计算函数，接受一个 DataFrame 和可选参数，返回因子值。
            daily_data (list of pd.DataFrame): 包含每日数据的 DataFrame 列表。
            type_name (str): 用于筛选数据的类型名称（如 'IC2412'）。
            windows (list of int): 时间窗口列表，单位为秒。默认为 [5, 10, 30, 60]。
            **kwargs: 传递给因子计算函数 `func` 的可选参数。

        Returns:
            None: 直接调用 `single_factor_analyze` 函数并输出结果。
        """
        daily_target = []
        daily_factor = []

        for daily_dt in daily_data:
            target_mat = pd.DataFrame()

            # 计算不同窗口的特征
            mid = (daily_dt['AskPrice1'] + daily_dt['BidPrice1']) / 2

            for window in windows:
                window_size = "window" + str(window)
                target_mat[window_size] = (mid.rolling(window=window * 2).mean().shift(-window * 2 + 1) - mid) / mid

            daily_target.append(target_mat)
            daily_factor.append(pd.Series(func(daily_dt, **kwargs)))

        daily_targets = pd.concat(daily_target, axis=0)
        daily_factors = pd.concat(daily_factor, axis=0)

        # 检查 daily_factors 和 daily_targets 的长度是否一致
        if len(daily_factors) != len(daily_targets):
            raise ValueError("daily_factors 和 daily_targets 的长度不一致，请检查数据。")

        FactorAnalyzer.single_factor_analyze(daily_factors, daily_targets)

# 示例用法, 调用因子计算函数生成分析数据
if __name__ == "__main__":
    generator = SingleFactorAnalyzeDataGenerator(data_dir='C:/Users/<USER>/Desktop/data', future='IC')
    generator.load_data()
    # 生成用于分析的data
    generator.generate_analyze_data()
    # 返回data
    daily_data = generator.data_for_analyze
    # 示例：检测reversal_last因子
    FactorAnalyzer.factor_analyze(reversal_last, daily_data)   