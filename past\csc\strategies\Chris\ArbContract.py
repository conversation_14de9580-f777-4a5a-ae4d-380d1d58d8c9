# -*- coding: utf-8 -*-
"""
Created on Fri Oct 15 18:07:27 2021
#套利单行情统计
@author: zhanghc
"""

#import python pacakges
import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
import math
from datetime import timedelta
import datetime
import GetMarketData
import PostTrade
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings("ignore")

def get_product_code(contract):
    exchange = contract[-3:]
    if exchange =='CZC':
        text_start = '-'
    else:
        text_start = '+'
    start = contract.find(text_start)
    end = re.search(r'\d',contract).start()
    product_code = contract[start+1:end].upper()
    product_code = product_code + '.' + exchange
    return product_code

def spread(df_mkt,ts):
    s = (df_mkt['a_p1'] - df_mkt['b_p1'] )/ ts
    result = [np.mean(s)] #Avg Spread
    n = len(s)
    for i in range(1,6):
        result.append("{:.2%}".format(len(s[s==i])/n))
    col = ['AvgSpread','1','2','3','4','5']
    result = pd.DataFrame(result,index=col,columns=['spread_statistics'])
    return result
    
if __name__ == '__main__':

    beginStr = '2022-8-1T21:00:00.0Z'
    endStr = '2022-8-2T15:00:00.0Z'
    # mode = 'dev'
    mode ='prod'
    df = pd.read_csv("contract_config.csv",index_col=0,error_bad_lines=False)
    config = df.to_dict()
    
    #CZC Arb Contract Format 'SPD-AP210%2FAP211'
    contract = 'SP+eg2209%26eg2301.DCE'
    # contract = 'SPD-TA209%2FTA301.CZC'
    product_code = get_product_code(contract)
    multiplier = config['size'][product_code]
    ts = config['pricetick'][product_code] 
    
    
    beginT = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    endT = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    df_mkt = GetMarketData.getmarketdata(mode,contract[-3:],contract[:-4],beginT,endT)
    orderbook_shape = df_mkt[['b_v5','b_v4','b_v3','b_v2','b_v1','a_v1','a_v2','a_v3','a_v4','a_v5']].mean()
    plt.bar(orderbook_shape.index,orderbook_shape)
    plt.title('orderbookShape')
    plt.show()
    
    print(contract)
    print('切片数量',df_mkt.shape[0])
    average_quote_size = (df_mkt[['b_v1','a_v1']].mean()).mean()
    print('平均盘口挂单量',int(average_quote_size))
    
    spread_table = spread(df_mkt,ts)
    print (spread_table)
    df_mkt['spread'] = (df_mkt['a_p1'] - df_mkt['b_p1'])/ts
    
    
    df_mkt['t'] = df_mkt['local_t'].diff().shift(-1)  
    df1 = df_mkt[df_mkt['spread']==2]
    df1['t']/=(10**6)
    df1 = df1[df1['t']<=5000]
    average_t = df1['t'].mean()
    print('SelfTrade Time Window',average_t)
    plt.hist(df1['t'],bins=20)
    
    
    
    
    # start_list = list(df1.index)
    # end_list = [i+1 for i in start_list]
    # df_local_t = df_mkt['local_t']
    # # df_time = pd.DataFrame(df_local_t.diff())
    # # a = df_time.iloc[[end_list]]
    # ls1 = df_local_t.loc[start_list].values.tolist()
    # ls2 = df_local_t.loc[end_list].values.tolist()
    # ls = [ls2[i] - ls1[i] for i in range(len(ls1))]
    # ls = [ls[i]/(10**9) for i in range(len(ls))]
    # ls
    
    
    