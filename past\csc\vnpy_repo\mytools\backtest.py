"""一般对价撮合模式"""
# from vnpy.app.cta_strategy.base import BacktestingMode
# from vnpy.app.cta_strategy.backtesting import BacktestingEngine, OptimizationSetting
# from strategies.tick_template import TickStrategy
# from datetime import datetime
# from vnpy.trader.constant import Interval

"""排队撮合"""
from vnpy.app.my_tick_strategy.base import BacktestingMode
from vnpy.app.my_tick_strategy.backtesting import BacktestingEngine, OptimizationSetting
import sys
sys.path.append("C:/Users/<USER>")
from strategies.new_tick import TickStrategy
from datetime import datetime
from vnpy.trader.constant import Interval
from OmmDatabase import OmmDatabase
import pandas as pd

#%%
engine = BacktestingEngine()
engine.set_parameters(
    vt_symbol="m2203.DCE", # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime(2021, 5, 20, 21, 0), # 开始时间
    end=datetime(2021, 5, 21, 15, 0), # 结束时间
    rate=0, # 手续费率
    slippage=0, # 滑点
    size=10, # 合约规模
    pricetick=1, # 一个tick大小
    capital=1_000_000, # 初始资金
    mode=BacktestingMode.TICK # 回测模式
)
# 添加回测策略，并修改内部参数
engine.add_strategy(TickStrategy, {'fixed_size': 10})

#%%
engine.load_data() # 加载历史数据
print('start: ', engine.history_data[0].datetime)
print('end: ', engine.history_data[-1].datetime)
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
# engine.show_chart() # 显示图表

#%%
def down_from_omm(DBPATH,date,commodity,main):
    path = DBPATH + 'MarketDataService/default_mkt/{}/'.format(commodity)
    testDB = OmmDatabase(path)
    df = testDB.read_file(path, date)
    df['SystemStamp'] = pd.to_datetime(df['SystemStamp'] * 10, format='%Y%m%d%H%M%S%f')
    df = df[df['instrumentId'] == main]
    df['dV'] = df['tradedVolume'] - df['tradedVolume'].shift(1)
    df['dV'].fillna(0,inplace=True)
    return df

date = '2021-05-21'
commodity = 'M'
exchange = 'dce'
DBPATH = f'C:/Users/<USER>/Desktop/Commodity/{date}/{exchange}_future/'
main = 'm2203'
df = down_from_omm(DBPATH,date,commodity,main)


#%%
a = pd.DataFrame.from_dict(engine.limit_orders,orient='index')

#%%
a = ((df.Bid1Price >= df.Ask1Price.shift(1)) | (df.Ask1Price <= df.Bid1Price.shift(1))) .sum()   #上一刻的买一价高于下一刻的卖一价
b = ((df.Bid1Price == df.Bid1Price.shift(1)) & (df.Ask1Price == df.Ask1Price.shift(1))).sum()
c = ((df.lastPriceOnMarket < df.Bid1Price.shift(1)) | (df.lastPriceOnMarket > df.Ask1Price.shift(1)))
