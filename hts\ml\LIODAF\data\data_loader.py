
"""
数据加载模块
@author: lining
"""
import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from utils.utils import log_print
from core import config

def split_normalize_data(data, feature_cols, scaler, test_size=0.2, random_state=42):
    
    target_cols = config.TARGET_COLS
    backmode = config.BACKMODE
    
    data_clean = data.dropna(subset=feature_cols + target_cols)
    log_print(f"删除NaN后剩余 {len(data_clean)} 条记录")

    
    log_print(f"分割数据: {backmode}")
    X = data_clean[feature_cols] 
    y_dict = {target: data_clean[target] for target in target_cols} 

    X_train, X_test, y_train_dict, y_test_dict, scalers = {}, {}, {}, {}, {}

    
    for target in target_cols:
        if backmode == 't':
            X_train[target], X_test[target], y_train_dict[target], y_test_dict[target] = train_test_split(
                X, y_dict[target], test_size=test_size, random_state=random_state
            )
        elif backmode == 'normal':
            train_data, valid_data, test_data = split_data(X, train_ratio=0.7, valid_ratio=0.0, test_ratio=0.3, by_date=False)
            X_train[target] = train_data
            X_test[target] = test_data
            y_train_dict[target] = y_dict[target][train_data.index]
            y_test_dict[target] = y_dict[target][test_data.index]
        elif backmode == 'rolling':
            X_train[target] = X[:-1]
            X_test[target] = X[-1:]
            y_train_dict[target] = y_dict[target][:-1]
            y_test_dict[target] = y_dict[target][-1:]
        elif backmode == 'days':
            
            train_data, valid_data, test_data = split_data(X, train_ratio=0.7, valid_ratio=0.0, test_ratio=0.3, by_date=True)
            X_train[target] = train_data
            X_test[target] = test_data
            y_train_dict[target] = y_dict[target][train_data.index]
            y_test_dict[target] = y_dict[target][test_data.index]

        
        X_train[target], X_test[target], scalers[target] = normalize_data(X_train[target], X_test[target], scaler)

        log_print(f"目标 {target}: 训练集 {len(X_train[target])} 条记录, 测试集 {len(X_test[target])} 条记录")

    return X_train, X_test, y_train_dict, y_test_dict, scalers


def normalize_data(X_train, X_test, scaler):
    
    X_train = pd.DataFrame(scaler.fit_transform(X_train),
                          columns=X_train.columns,
                          index=X_train.index)
    X_test = pd.DataFrame(scaler.transform(X_test),
                          columns=X_test.columns,
                          index=X_test.index)
    return X_train, X_test, scaler


def split_data(data, train_ratio=0.7, valid_ratio=0.0, test_ratio=0.3, by_date=True, is_random=False):
    
    
    if abs(train_ratio + valid_ratio + test_ratio - 1.0) > 1e-10:
        raise ValueError("训练集、验证集和测试集的比例之和必须为1")
    
    
    datecol = pd.to_datetime(data.index.date)

    
    if by_date:
        
        unique_dates = sorted(datecol.unique())
        n_dates = len(unique_dates)

        if n_dates == 2:
            n_train = 1
            n_valid = 0
            n_test = 1
        else:
            
            n_test = max(1, int(n_dates * test_ratio)) 
            n_valid = int(n_dates * valid_ratio) 
            n_train = n_dates - n_test - n_valid 

        
        train_dates = unique_dates[:n_train] 
        valid_dates = unique_dates[n_train:n_train + n_valid] 
        test_dates = unique_dates[n_train + n_valid:] 

        
        train_data = data[datecol.isin(train_dates)] 
        valid_data = data[datecol.isin(valid_dates)] 
        test_data = data[datecol.isin(test_dates)] 

        print(
            f"按日期分割数据: 训练集 {len(train_data)} 条记录, 验证集 {len(valid_data)} 条记录, 测试集 {len(test_data)} 条记录")
    else:
        
        if is_random:
            indices = np.random.permutation(len(data)) 
        else:
            indices = np.arange(len(data)) 
        n_train = int(len(data) * train_ratio) 
        n_valid = int(len(data) * valid_ratio) 

        train_idx = indices[:n_train] 
        valid_idx = indices[n_train:n_train + n_valid] 
        test_idx = indices[n_train + n_valid:] 

        train_data = data.iloc[train_idx]
        valid_data = data.iloc[valid_idx]
        test_data = data.iloc[test_idx]

        print(
            f"随机分割数据: 训练集 {len(train_data)} 条记录, 验证集 {len(valid_data)} 条记录, 测试集 {len(test_data)} 条记录")

    return train_data, valid_data, test_data


class OrderBookDataLoader:
    

    def __init__(self, data_path=None, code_list=None, date_str=None, time_range=None):
        
        self.data_path = data_path
        self.code_list = code_list
        self.date_str = date_str
        self.time_range = time_range
        self.mult = 200

    def load_data(self, ):
        

        
        file_ext = os.path.splitext(self.data_path)[1].lower()

        
        if file_ext == '.csv':
            data = self._load_csv()
        elif file_ext == '.parquet':
            data = self._load_parquet()
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

        return data

    def _load_csv(self):
        
        try:
            data = pd.read_csv(self.data_path)
            log_print(f"从CSV加载了 {len(data)} 条记录", 'debug')
            return data
        except Exception as e:
            log_print(f"加载CSV文件时出错: {e}", 'debug')
            return pd.DataFrame()

    def _load_parquet(self):
        
        actual_path = self.data_path

        log_print(f'加载数据文件: {actual_path}', 'debug')

        
        if '%s' in actual_path and self.date_str:
            actual_path = actual_path % self.date_str

        log_print(f'实际文件路径: {actual_path}', 'debug')

        try:
            data_md = pd.read_parquet(actual_path, engine='pyarrow', memory_map=True, use_threads=True)
            log_print(f'成功读取数据，记录数: {len(data_md)}', 'debug')
            log_print(f'数据列: {data_md.columns.tolist()}', 'debug')
        except FileNotFoundError:
            log_print(f"错误：文件 {actual_path} 不存在", 'debug')
            return None
        except Exception as e:
            log_print(f"读取文件时发生错误: {e}", 'debug')
            return None

        self.data_md = data_md

    def filtor(self, codeslist, date_md, tradetime_range, source=None, 
            mid=0.5, last=0.5, mult=config.MULT, sig=None):
        sig = [''] if sig is None else sig

        data_md = self.data_md

        
        try:
            data_md['timestamp_str'] = pd.to_datetime(date_md + ' ' + data_md['timestamp_str'], format='%Y%m%d %H:%M:%S.%f')
            data_md.index = data_md['timestamp_str']
        except Exception as e:
            log_print(f"转换时间戳时出错: {e}", 'debug')
            
            try:
                log_print("尝试不同的时间戳格式...", 'debug')
                data_md['timestamp_str'] = pd.to_datetime(data_md['timestamp_str'])
                data_md.index = data_md['timestamp_str']
            except Exception as e2:
                log_print(f"再次转换时间戳时出错: {e2}", 'debug')
                return None

        
            data_md = pd.concat([data_md.between_time(tradetime_range[0][0], tradetime_range[0][1]),
                                data_md.between_time(tradetime_range[1][0], tradetime_range[1][1])])
            log_print(f'过滤交易时间后的记录数: {len(data_md)}', 'debug')


        data_md['Symbol'] = data_md['Symbol'].astype(str)
        if source:
            data_md = data_md[data_md['Source'] == source]
            log_print(f'过滤Source后的记录数: {len(data_md)}', 'debug')

        
        if codeslist and codeslist[0] is not None:
            available_symbols = data_md['Symbol'].unique()
            log_print(f'可用的Symbol: {available_symbols}', 'debug')
            log_print(f'请求的Symbol: {codeslist}', 'debug')

            matching_symbols = [code for code in codeslist if code in available_symbols]
            if not matching_symbols:
                log_print('错误：没有找到匹配的Symbol', 'debug')
                return None
            else:
                log_print(f'找到匹配的Symbol: {matching_symbols}', 'debug')

        
            data_md['mid'] = data_md['AskPrice1'] / 2 + data_md['BidPrice1'] / 2
            data_md['mixfut'] = (data_md['mid'] * mid + data_md['LastPrice'] * last)
            data_md['edge'] = (data_md['AskPrice1'] - data_md['BidPrice1']) / 2


        if codeslist:
            data_md = data_md[data_md['Symbol'].isin(codeslist)].drop_duplicates(subset=['timestamp_str', 'Symbol'])
            log_print(f'过滤Symbol后的记录数: {len(data_md)}', 'debug')

            
            if 'Volume' in data_md.columns:
                data_md['tradedVol'] = data_md['Volume'].diff(1)
                data_md.loc[data_md['tradedVol'] < 0, 'tradedVol'] = 0  

            if 'TotalValueTraded' in data_md.columns:
                data_md['tradedValue'] = data_md['TotalValueTraded'].diff(1)
                data_md.loc[data_md['tradedValue'] < 0, 'tradedValue'] = 0  

        
        for col in ['tradedVol', 'tradedValue']:
            if col in data_md.columns:
                data_md[col] = data_md[col].fillna(0)

        data_md['avg_prc'] = (data_md['tradedValue'] / data_md['tradedVol'] / mult).fillna(0).round(5)

        addcol = ['mid', 'mixfut', 'edge', 'tradedVol', 'avg_prc']

        all_col = [
            'Symbol', 'timestamp_str', 'Volume',
            'BidPrice5', 'BidPrice4', 'BidPrice3', 'BidPrice2', 'BidPrice1',
            'AskPrice1', 'AskPrice2', 'AskPrice3', 'AskPrice4', 'AskPrice5',
            'BidVol5', 'BidVol4', 'BidVol3', 'BidVol2', 'BidVol1',
            'AskVol1', 'AskVol2', 'AskVol3', 'AskVol4', 'AskVol5',
            'LastPrice', 'High', 'Low', 'TotalValueTraded', 'ForQuoteSysID', 'Source', 'State',
        ]

        all_col = [x for x in all_col if x in data_md.columns]
        data_md = data_md[all_col + addcol]
        log_print(list(filter(lambda x: x not in data_md.columns, all_col)), 'debug')

        log_print(self.data_path + ' md done', 'debug')
        data_md = data_md.sort_index()

        if codeslist:
            data_md['dsmid'] = data_md['mid'].diff(1)
            data_md['dsema'] = np.round(data_md['dsmid'].ewm(alpha=0.1).mean(), 2)

        return data_md.dropna(axis=1, how='all')