from PySide6.QtCore import QObject, Slot, Signal, Property
from PySide6.QtQml import QQmlApplicationEngine
from PySide6.QtWidgets import QApplication
import sys
import pandas as pd
import numpy as np
from temple import BaseVolatilityCurveUI

class VolatilityCurveBackend(QObject):
    dataUpdated = Signal(dict)
    configChanged = Signal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self._base = BaseVolatilityCurveUI()
        self._config = {
            'minVol': 0.1,
            'maxVol': 1.0
        }

    @Slot(str)
    def loadData(self, file_path):
        try:
            # Load and process data using existing logic
            data = pd.read_csv(file_path)
            self._base.data_manager.load_data(data)
            
            # Convert data for QML
            vol_data = {
                'volCurve': self._prepare_vol_curve_data(),
                'positions': self._prepare_position_data()
            }
            self.dataUpdated.emit(vol_data)
        except Exception as e:
            print(f"Error loading data: {e}")

    @Slot(dict)
    def updateConfig(self, config):
        self._config.update(config)
        self.configChanged.emit(self._config)
        self.updatePlot()

    def _prepare_vol_curve_data(self):
        if self._base.data_manager.current_data is None:
            return []
        
        data = self._base.data_manager.current_data
        # Convert volatility curve data to list of points
        points = []
        for idx, row in data.iterrows():
            points.append({'x': float(row['K']), 'y': float(row['vol'])})
        return points

    def _prepare_position_data(self):
        if self._base.data_manager.current_data is None:
            return []
        
        data = self._base.data_manager.current_data
        # Convert position data based on display option
        positions = []
        grouped = data.groupby('K')['posi'].sum()
        for k, v in grouped.items():
            positions.append(float(v))
        return positions

def main():
    app = QApplication(sys.argv)
    engine = QQmlApplicationEngine()
    
    # Create and register backend
    backend = VolatilityCurveBackend()
    engine.rootContext().setContextProperty("backend", backend)
    
    # Load QML
    engine.load("VolatilityCurve.qml")
    
    if not engine.rootObjects():
        return -1
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
