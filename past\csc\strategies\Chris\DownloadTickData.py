# -*- coding: utf-8 -*-
"""
Created on Fri Oct 15 18:07:27 2021
#下载tick数据并保存到csv
@author: zhanghc
"""
#import python pacakges
import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
import math
from datetime import timedelta
import datetime

import GetMarketData


'''
SP+j2209%26j2301
SP+i2207%26i2208

SPD-RM209/RM301.CZC(Backtest)
SPD-AP210%2FAP211 (Download Data)

'''


# result = client.query("select * from zce_md where insid_md=~/SP/ and time >= %d and time <= %d;"%(begin,end)) 


if __name__ == '__main__':
    # contracts = ['c2205.dce','SR205.CZC','au2202.shf','au2206.shf','CF205.CZC','rr2202.dce','rr2203.dce','rr2205.dce']
    # contracts = ['c2207.dce']
    
    contracts = ['i2205.DCE']
    beginStr = '2022-04-1T21:00:00.0Z'
    endStr = '2022-04-30T15:00:00.0Z'
    mode = 'dev'
    # mode = 'prod'
    beginT = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    endT = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    for contract in contracts:
        exchange = contract[-3:]
        contract = contract[:-4]
        df = GetMarketData.getmarketdata(mode,exchange,contract,beginT,endT)
        df['v'] = df['v'].diff().fillna(0)
        df.to_csv(contract+'.csv')
        
   
    