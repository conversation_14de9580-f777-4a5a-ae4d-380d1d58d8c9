"""
SVM one-versus-all implementation for orderbook dynamics.
This module provides implementation for multi-class classification using SVM.
"""
from typing import Dict, Set, List
import numpy as np
from sklearn.svm import LinearSVC
from sklearn.preprocessing import StandardScaler


class SVMModel:
    """
    Support Vector Machine model for binary classification.
    
    Similar to Spark's SVMModel.
    """
    def __init__(self, weights: np.ndarray, intercept: float, threshold: float = 0.0):
        """
        Initialize an SVM model.
        
        Args:
            weights: Weight vector for the linear model
            intercept: Intercept term
            threshold: Classification threshold
        """
        self.weights = weights
        self.intercept = intercept
        self.threshold = threshold
        
    def predict(self, features: np.ndarray) -> float:
        """
        Predict using the SVM model.
        
        Args:
            features: Feature vector
            
        Returns:
            Predicted label (0.0 or 1.0)
        """
        margin = np.dot(self.weights, features) + self.intercept
        if self.threshold == 0.0:
            return margin
        return 1.0 if margin > self.threshold else 0.0
    
    def clear_threshold(self):
        """Clear the classification threshold, returning margin instead."""
        self.threshold = 0.0


class SVMWithSGD:
    """
    SVM with Stochastic Gradient Descent.
    
    Similar to Spark's SVMWithSGD.
    """
    def __init__(self, num_iterations: int = 100, step_size: float = 1.0, 
                 reg_param: float = 0.01, mini_batch_fraction: float = 1.0,
                 initial_weights: np.ndarray = None):
        """
        Initialize an SVM with SGD.
        
        Args:
            num_iterations: Number of iterations
            step_size: Step size for SGD
            reg_param: Regularization parameter
            mini_batch_fraction: Fraction of data to use for each mini-batch
            initial_weights: Initial weights for the model
        """
        self.num_iterations = num_iterations
        self.step_size = step_size
        self.reg_param = reg_param
        self.mini_batch_fraction = mini_batch_fraction
        self.initial_weights = initial_weights
        
    def run(self, labeled_points: List) -> SVMModel:
        """
        Train an SVM model with SGD.
        
        Args:
            labeled_points: List of LabeledPoint
            
        Returns:
            Trained SVMModel
        """
        # Extract features and labels
        X = np.array([lp.features for lp in labeled_points])
        y = np.array([lp.label for lp in labeled_points])
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Train SVM
        svm = LinearSVC(
            max_iter=self.num_iterations,
            C=1.0/self.reg_param,
            loss='hinge',
            random_state=42
        )
        svm.fit(X_scaled, y)
        
        # Extract model parameters
        return SVMModel(svm.coef_[0], svm.intercept_[0])


class SVMOneVersusAllModel:
    """
    SVM model for multi-class classification using one-versus-all approach.
    """
    def __init__(self, models: Dict[float, SVMModel]):
        """
        Initialize an SVM one-versus-all model.
        
        Args:
            models: Dictionary mapping class labels to SVM models
        """
        self.models = models
        
    def predict(self, features: np.ndarray) -> Dict[float, float]:
        """
        Predict using all SVM models.
        
        Args:
            features: Feature vector
            
        Returns:
            Dictionary mapping class labels to prediction scores
        """
        return {label: model.predict(features) for label, model in self.models.items()}


class SVMOneVersusAll:
    """
    SVM for multi-class classification using one-versus-all approach.
    """
    def __init__(self, svm: SVMWithSGD):
        """
        Initialize an SVM one-versus-all classifier.
        
        Args:
            svm: SVM with SGD instance
        """
        self.svm = svm
        
    def run(self, labeled_points: List) -> SVMOneVersusAllModel:
        """
        Train an SVM one-versus-all model.
        
        Args:
            labeled_points: List of LabeledPoint
            
        Returns:
            Trained SVMOneVersusAllModel
        """
        # Get all unique labels
        labels = set(lp.label for lp in labeled_points)
        
        # Train one SVM per label
        models = {}
        for label in labels:
            # Relabel data for binary classification (1.0 for target label, 0.0 for others)
            relabeled_points = [
                type(lp)(1.0 if lp.label == label else 0.0, lp.features)
                for lp in labeled_points
            ]
            
            # Train model for this label
            model = self.svm.run(relabeled_points)
            model.clear_threshold()
            models[label] = model
            
        return SVMOneVersusAllModel(models)


def train(labeled_points: List, num_iterations: int = 100) -> SVMOneVersusAllModel:
    """
    Train an SVM one-versus-all model.
    
    Args:
        labeled_points: List of LabeledPoint
        num_iterations: Number of iterations
        
    Returns:
        Trained SVMOneVersusAllModel
    """
    svm = SVMWithSGD(num_iterations=num_iterations)
    return SVMOneVersusAll(svm).run(labeled_points) 