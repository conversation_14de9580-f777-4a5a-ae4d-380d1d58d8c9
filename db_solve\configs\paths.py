import os


class Paths(object):
    def __init__(self, under, app_name=""):
        self.dirs = u'G:\\DATA\\' + under + '\\'
        self.outdirs = self.dirs + 'output\\' + os.path.basename(app_name).split('.')[0] + '\\'
        self.str1 = self.dirs + 'md_%s_cffex_multicast.parquet'
        self.str11 = self.dirs + 'md_%s_exanic_gfex.parquet'
        self.str2 = self.dirs + 'md_%s_tcp_receiver_1_3276.parquet'
        self.str22 = self.dirs + 'md_%s_shm_receiver.parquet'
        self.str23 = self.dirs + 'md_%s_udp_receiver_2_25103.parquet'
        self.str233 = self.dirs + 'md_%s_udp_receiver_1_25103.parquet'
        self.str_trade = self.dirs + 'TradeRecords%s.parquet'
        self.str_ord = self.dirs + '%s_OrderDetails.parquet'
        self.str_optmd = self.dirs + 'md_%s_sse_mdgw.parquet'
        self.str_vol = self.dirs + 'vols_%s.parquet'

        self.str_com = self.dirs + 'md_%s_exanic.parquet'

        if not os.path.exists(self.outdirs):
            os.makedirs(self.outdirs)


class Ftpserver(object):
    SH500_VOLSERVER = dict(Host="************", username="admin", password="Htsc_Qqzs_sc_153"),
    SZ100_VOLSERVER = dict(Host="***********", username="admin", password="Qqzs_sc_0941"),
    SH50_VOLSERVER = dict(Host="************", username="admin", password="Htsc_Qqzs_sc_153"),
    SH300_VOLSERVER = dict(Host="***********", username="admin", password="Htsc_Qqzs_sc_0861"),
    SZ300_VOLSERVER = dict(Host="************", username="admin", password="Htsc_Qqzs_sc_ip151"),
    SZcyb_VOLSERVER = dict(Host="************", username="admin", password="Htsc_Qqzs_sc_153"),
    guitai = dict(Host="**********", username="admin", password="Htsc_Qqzs_sc_130"),
    ftp = dict(Host="***********", username="jcsh", password="9L6q#BJ23-dx")


gui_dirs = dict(SH500=r"D:\\Desktop\\",
                SH300=r"D:\\Desktop\\",
                SH50=r"D:\\Desktop\\",
                SHkcb=r"D:\\Desktop\\",
                SZ300500=r"D:\\Desktop\\",
                SZ100=r"D:\\Desktop\\",
                SZcyb=r"D:\\Desktop\\",

                DCE=r"D:\\Desktop\\Sepro_COM\\",
                CZCE=r"D:\\Desktop\\Sepro_COM\\",
                SHFE_aucu=r"D:\\Desktop\\Sepro_COM\\",
                SHFE_rbsc=r"D:\\Desktop\\Sepro_COM\\")

UNDERCONFIG = dict(
    SH500=dict(spot='IC', spot2='IM', out_fut='CN', futmult=200, optmult1=10000, optmult2=10160, s_mult=1000),
    SH300=dict(spot='IF', spot2='IC', out_fut='CN', futmult=300, optmult1=10000, optmult2=10000, s_mult=1000),
    SH50=dict(spot='IH', spot2='IF', out_fut='CN', futmult=300, optmult1=10000, optmult2=10000, s_mult=1000),
    TA=dict(spot='TA', futmult=5, optmult1=5),
    sc=dict(spot='sc', futmult=100, optmult1=100),
    au=dict(spot='au', futmult=100, optmult1=100),
    cu=dict(spot='cu', futmult=100, optmult1=100),
)

FUTCONFIG = dict(
    IC=dict(futmult=200, mintick=0.2),
    IF=dict(futmult=300, mintick=0.2),
    IH=dict(futmult=300, mintick=0.2),
    IM=dict(futmult=200, mintick=0.2),
    CN=dict(futmult=1, mintick=1),
    si=dict(futmult=1, mintick=5)
)
