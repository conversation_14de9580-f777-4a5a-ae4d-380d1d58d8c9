# -*- coding:utf-8 -*-
import matplotlib.pyplot as plt
from matplotlib.pylab import date2num
import matplotlib.finance as mpf
import matplotlib.dates as mdates
import datetime
import pandas as pd
import pymssql
import talib
# import numpy as np

import matplotlib.font_manager as fm
myFont = fm.FontProperties(fname=r'C:/Windows/Fonts/msyh.ttf')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = datetime.datetime.now()
# beginDate = "2017-06-24"

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')

sql = "SELECT  datetime,stkid,openprice,high,low,closeprice,volume,amount FROM Alex.dbo.test300 \
        WHERE datetime BETWEEN '2015-6-10' AND '2016-7-15' "

pf = pd.read_sql(sql, conn, index_col=['datetime'], coerce_float=True, params=None, parse_dates=None, columns=None,
                 chunksize=None)

conn.commit()

conn.close()

print(pf)
pf2 = pf = pf[pf['stkid'] == '000001.SZ']
print("a")
print(pf2)
# 对tushare获取到的数据转换成candlestick_ohlc()方法可读取的格式
data_list = []

for dates, row in pf2.iterrows():
    # 将时间转换为数字
    date_time = datetime.datetime.strptime(dates, '%Y-%m-%d')
    t = date2num(date_time)
    open2, high, low, close = row[1:5]
    datas = (t, open2, high, low, close)
    data_list.append(datas)

# 创建子图
# fig, ax = plt.subplots(figsize=(15,7))
fig = plt.figure(figsize=(15, 9))
ax = fig.add_axes([0.1, 0.46, 0.8, 0.5])
fig.subplots_adjust(bottom=0.2)
# 设置X轴刻度为日期时间
plt.xticks(rotation=45)
plt.yticks()
plt.title(u"K线", fontproperties=myFont)
# plt.xlabel(u"时间",fontproperties=myfont)
plt.ylabel(u"股价（元）", fontproperties=myFont)
mpf.candlestick_ohlc(ax, data_list, width=1.5, colorup='r', colordown='green')
ax.xaxis.set_major_locator(mdates.MonthLocator())  # DayLocator(bymonthday=range(1,32), interval=15))
ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
plt.grid()
ax.autoscale_view()

close2 = pf2['closeprice'].values
dates = pd.to_datetime(pf2.index)

sma5 = talib.SMA(close2, timeperiod=5)
ax.plot(dates, sma5, label='MA5', linewidth=1.0, linestyle="-")
sma10 = talib.SMA(close2, timeperiod=10)
ax.plot(dates, sma10, label='MA10', linewidth=1.0, linestyle="-")
sma60 = talib.SMA(close2, timeperiod=60)
ax.plot(dates, sma60, label='MA60', linewidth=1.0, linestyle="-")
plt.legend(loc='best')


def myMACD(price, fastperiod=12, slowperiod=26, signalperiod=9):
    ewma12 = price.ewm(span=fastperiod).mean()
    ewma60 = price.ewm(span=slowperiod).mean()
    # ewma12 = pd.ewma(price,span=fastperiod)
    # ewma60 = pd.ewma(price,span=slowperiod)
    dif = ewma12 - ewma60
    # dea = pd.ewma(dif,span=signalperiod)
    dea = dif.ewm(span=signalperiod).mean()
    bar = (dif - dea) * 2  # 有些地方的bar = (dif-dea)*2，但是talib中MACD的计算是bar = (dif-dea)*1
    return dif, dea, bar


macd, signal, hist = talib.MACD(close2, fastperiod=12, slowperiod=26, signalperiod=9)

mydif, mydea, mybar = myMACD(pf2['closeprice'], fastperiod=12, slowperiod=26, signalperiod=9)

# fig = plt.figure(figsize=[15,9])
# ax1 = fig.add_subplot(2, 1, 2, frameon=False)
ax1 = fig.add_axes([0.1, 0.12, 0.8, 0.2])
plt.plot(dates, macd, label='macd dif', linewidth=1.0, linestyle="-")
plt.plot(dates, signal, label='signal dea', linewidth=1.0, linestyle="-")
plt.plot(dates, hist, label='hist bar', linewidth=1.0, linestyle="-")
plt.plot(dates, mydif, label='my dif', linewidth=1.0, linestyle="-")
plt.plot(dates, mydea, label='my dea', linewidth=1.0, linestyle="-")
plt.plot(dates, mybar, label='my bar', linewidth=1.0, linestyle="-")
ax1.xaxis.set_major_locator(mdates.MonthLocator())  # DayLocator(bymonthday=range(1,32), interval=15))
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
plt.grid()
plt.legend(loc='best')
plt.title(u"MACD", fontproperties=myFont)
plt.xlabel(u"时间", fontproperties=myFont)
fig.subplots_adjust(bottom=0.3)
plt.xticks(rotation=45)
ax1.autoscale_view()

plt.show()
