
use_features=[
        # 基础数据列
        "mid_price",
        # "mid_price_level_2",
        # "mid_price_level_3",
        # "mid_price_level_4",
        # "mid_price_level_5",
        "tradedVol",
        "mid_return",
        # "mid5_return",
        # "tradedValue",
        # "avg_prc",
        
        # 基本特征 - 价差特征
        "spread_1", 
        # "spread_2",
        # "spread_3",
        # "spread_4",
        # "spread_5",
        # "mean_ask_price",
        # "mean_bid_price",
        "mean_ask_vol",
        "mean_bid_vol",
        "accumulated_price_spread",
        "accumulated_volume_diff",
        
        # 基本特征 - 订单簿不平衡特征
        # "imbalance_1", 
        # "imbalance_2", 
        # "imbalance_3", 
        # "imbalance_4", 
        # "imbalance_5",
        # "total_bid_vol", 
        # "total_ask_vol", 
        # #"vol_imbalance",
        
        # 基本特征 - 价格压力特征
        # "bid_price_diff_1",  midprc_diff_2_1 0.72
        # "bid_price_diff_2", 
        # "bid_price_diff_3", 
        # "bid_price_diff_4",
        "bid_price_diff_max", # vol_weighted_spread_bid 0.86
        # "ask_price_diff_1", 
        # "ask_price_diff_2", 
        # "ask_price_diff_3", 
        # "ask_price_diff_4",
        "ask_price_diff_max", # vol_weighted_spread_ask 0.86
        
        # 时间序列特征 - 价格动量特征
        "mid_return_5", 
        # "mid_return_10", 
        # "mid_return_20",
        # "mid_ma_5", 
        # "mid_ma_10", 
        # "mid_ma_20",  # midprice corr=1
        "ma_cross",

        # 时间序列特征 - 交易量特征
        # "vol_ma_5", 
        # "vol_ma_10", 
        "vol_ma_20",
        # "vol_std_5", 
        # "vol_std_10", 
        # "vol_std_20",
        
        # 时间序列特征 - 不平衡滚动特征
        # "imbalance_ma_5", 
        # "imbalance_ma_10", 
        # "imbalance_ma_20",
        
        # 高级特征 - 订单流特征
        # "OFI", 
        
        # 高级特征 - 交易强度特征
        # "volume_intensity",
        
        # 订单簿特征 - 深度和流动性
        # 'depth',        # 订单簿深度 IC 0.01 没有重要性
        'slope2',       # 订单簿斜率不平衡度
        'ofi2',         # 订单流不平衡
        # 'wss12',        # 加权标准化价差,accumulated_price_spread 0.86

        # 时间序列特征 - 自相关
        # 'autocorr_10_1', 
        # 'autocorr_10_2', 
        # 'autocorr_10_3', # IC 0 没有重要性
        # 'autocorr_20_1', 
        # 'autocorr_20_2', 
        # 'autocorr_20_3'
    ]

time_series_features = [
        # 高级特征 - 波动率特征
        "volatility_5", 
        # "volatility_10",
        "volatility_20",
        # "realized_absvar_5",
        # "realized_absvar_10",
        # "realized_absvar_20", # volatility_20好用
        # "realized_bipowvar_5",
        # "realized_bipowvar_10",
        # "realized_bipowvar_20", # volatility_20好用
        # "realized_skew_5",
        # "realized_skew_10",
        "realized_skew_20",
        # "realized_kurtosis_5",
        # "realized_kurtosis_10",
        "realized_kurtosis_20",
        # "realized_quarticity_5",
        # "realized_quarticity_10",
        "realized_quarticity_20",
        # "BPV_jump_5", #volatility_5
        # "BPV_jump_10",
        # "BPV_jump_20",

]
# 添加微分特征
for level in [
    # 1,2,3,4, # 没有5好用
    5]:
    time_series_features.extend([
        f'dAskPrice{level}_dt',
        f'dBidPrice{level}_dt',
        # f'dAskVol{level}_dt',
        # f'dBidVol{level}_dt'

        # f'avgAskprcdt{level}', 和dAskPrice{level}_dt 0.96
        # f'avgBidprcdt{level}', 和dBidPrice{level}_dt 0.96
    ])
use_features.extend(time_series_features)


diy_features = [
            # 订单簿特征 - 买卖失衡指标
        'im5',          # 5档买卖失衡指标
        'im5vol',       # 5档成交量失衡指标
        'mid_minnum',   # 基于最小成交量的中间价格偏离
        # 'mid_minnum2',  # 基于加权平均价格的中间价格偏离
        # 'mid_level',    # 中间价格档位
        'turnover_mid', # 成交均价与中间价格的偏离
        # 'press',        # 价格压力指标
        # 'im2mult',      # 2档加权买卖失衡指标
        'voi',          # 订单失衡量指标
        ]
# use_features.extend(diy_features)

# 添加技术指标特征
use_features.extend([
    "corr_rank",
    "trdshock",
    "trdvol_with_dir",
    # "trdvol_with_dir_ewm",
    "trd_offset",
    "last_return_10",
    "dir_ratio",
    # "turnover_mid_corr",
    "mid_persistent",
    # "accel_reversal",
    "volatility_adjusted_return",
    # "trend_flip",
    "mean_gap_relative"
]+[f"last_out_ma_{window}" for window in 
   [
    10,
    # 30,
    # 60,
    # 240
    ]])

# 添加所有技术指标特征
technical_factors2cols = [
    # "Accumulation_Distribution",
    "Awesome_Oscillator",
    # "ADX",
    "ADX_DI_Plus",
    # "ADX_DI_Minus",
    # "ADX_DX",
    "ADXR",
    # "ATR",
    # "Alligator",
    # "Alligator_teeth",
    # "Alligator_lips",
    # "APO",
    # "Aroon_Indicator",
    "Aroon_Oscillator",
    # "Bollinger_Bands",
    # "Bollinger_Bands_Upper",
    # "Bollinger_Bands_Lower",
    # "Bollinger_Bands_Signal",
    # "Ichimoku_Cloud",
    "Ichimoku_Cloud_Conversion", #o
    # "Ichimoku_Cloud_Base",
    # "Ichimoku_Cloud_Leading_B",
    "CMO",
    # "Chaikin_Oscillator",
    # "Chandelier_Exit",
    # "Chandelier_Exit_Short",
    # "Donchian_Channels_Upper",
    # "Donchian_Channels_Middle",
    # "Donchian_Channels_Lower",
    # "COG",
    # "DEMA",
    # "DPO",
    # "Heikin_Ashi",
    # "Highest_High",
    # "Lowest_Low",
    # "HMA",
    # "IBS",
    # "Keltner_Channels_Upper",
    # "Keltner_Channels_Middle",
    # "Keltner_Channels_Lower",
    # "MACD_Oscillator",
    # "Median_Price",
    "Momentum",
    # "VMA",
    "NATR",
    "PPO",
    "ROC",
    "RSI",
    "Stochastic_RSI",
    # "Rising_SAR",
    # "Falling_SAR",
    "Deviation", 
    "Standard_Deviation",
    # "Fractals",
    # "Fractals_sell",
    # "Linear_Regression_Line",
    # "Rational_Transfer_Filter",
    # "Savitzky_Golay_Filter",
    # "Zero_Phase_Filter",
    "Remove_Offset", #ic 0.07 
    # "Detrend_Least_Squares",
    # "Beta_Calculation",
    # "Support_Resistance" #o
]
# use_features.extend(technical_factors2cols)

microstructure_factors = [
    "ordflowbid", "ordflowask", 
    "ordimb", 
    # "outbidlevel", "outasklevel",
    # "outlevel_sum",
    # 'vol_weighted_spread_ask', 'vol_weighted_spread_bid', 
    'vol_weighted_spread_diff',
    "midprcdiff_2_1", 
    # "midprcdiff_3_2", "midprcdiff_4_2", "midprcdiff_5_1", 
    "trdflowsign", 
    # "trdflowva", "trdflowvb", 
    "trdimb", 
    "toflowBid", "toflowAsk", 
    # "toflow",
]
use_features.extend(microstructure_factors)

# 添加DEAP生成的因子
deap_factors = [
    # "complex_spread_pressure_factor",
]
use_features.extend(deap_factors)

