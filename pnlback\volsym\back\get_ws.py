# Author : gp
# Date : 20240319

import requests
import websocket
import json
import pandas as pd
import threading
import time
import datetime
from PySide6.QtCore import QObject, Signal

import sys
import os
sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])
from pnlback.volsym.ui.config import UNDER

if UNDER in ['510500','510050','510300',]:
    ws_addr = 'ws://168.231.2.88:22929'  # ETF生产环境地址
    http_addr = 'http://168.231.2.88:22928'
elif UNDER in ['i',]:
    ws_addr = 'ws://168.231.2.227:22909'  # 商品生产环境地址
    http_addr = 'ws://168.231.2.227:22908'


class md_config:
    # ws_addr = 'ws://160.14.228.64:5557'  # 开发环境地址
    # http_addr = 'http://160.14.228.64:5555'
    ws_addr = ws_addr
    http_addr = http_addr
    user_name = 'ln'
    password = 'ln'
    msg_name = ['volGreeks', 'option_rs', ][0]
    filter_key = ['underlyer', ][0]
    filter_value_list = UNDER
    token = ''
    disable_snap = 0  # 0是包括历史，1是实时的


class MarketDataManager:  # 基础版本，不依赖 Qt
    debug = False

    def __init__(self, keep_history=False, data_updated_callback=None):
        self.keep_history = keep_history
        self.options_latest = {}  # {optioncode: latest_data}
        self.options_history = {}  # {optioncode: [historical_data]} - only used if keep_history=True
        self.ws = None
        self.ws_thread = None
        self.is_connected = False
        self.data_updated_callback = data_updated_callback  # 接收回调函数
        self.last_message_time = time.time()  # 记录最后接收到消息的时间
        self.callback_interval = 0.001  # 10毫秒
        self.fitvol_interval = 1  # 1秒

    def start_websocket(self):
        if self.ws_thread and self.ws_thread.is_alive():
            return

        self.ws_thread = threading.Thread(target=self._run_websocket)
        self.ws_thread.daemon = True  # 设置为守护线程
        self.ws_thread.start()

        # 启动一个定时器线程来检查超时
        if self.data_updated_callback:  # 如果回调函数不为空，则启动定时器线程
            threading.Thread(target=self._check_timeout_thread, daemon=True).start()

    def _run_websocket(self):
        # 登录并获取token
        login_data = {
            "type": "login_req",
            "data": {
                "user_name": md_config.user_name,
                "password": md_config.password
            }
        }
        r = requests.post(f"{md_config.http_addr}/auth/login", json=login_data)
        md_config.token = r.cookies.get("token", "")

        if not md_config.token:
            print("ERROR: Failed to get token", r.text)
            return

        # 建立websocket连接
        self.ws = websocket.WebSocketApp(
            md_config.ws_addr,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            on_open=self.on_open
        )
        self.ws.run_forever()

    def on_open(self, ws):
        # 发送认证请求
        init_str = json.loads('{"type":"token_sub","data":{"token":0,"disable_sub_all":1}}')
        init_str["data"]["token"] = md_config.token
        print("send init req : ", init_str)
        ws.send(json.dumps(init_str))
        # 发送订阅请求
        subscribe_json = json.loads(
            '{"seqno":1,"type":"table_action","data":{"msg_name":"","action":"sub","interval_ms":0,"disable_snap":0,"filter_key":"","filter_value_list":""}}')
        subscribe_json["seqno"] = 1
        subscribe_json["data"]["msg_name"] = md_config.msg_name
        subscribe_json["data"]["disable_snap"] = md_config.disable_snap
        subscribe_json["data"]["filter_key"] = md_config.filter_key
        subscribe_json["data"]["filter_value_list"] = md_config.filter_value_list
        print("send subscribe req : ", subscribe_json)
        ws.send(json.dumps(subscribe_json))

    def on_message(self, ws, message):
        # print("Received message:", message)
        json_data = json.loads(message)

        if json_data['type'] == "volGreeks":
            self.add_data(json_data['data'])

        elif json_data['type'] == "fi_msg_rsp":
            if json_data['seqno'] == 1:
                print("Subscription confirmed")

        # 更新最后接收到消息的时间
        self.last_message_time = time.time()

    def on_error(self, ws, error):
        print("Error:", error)

    def on_close(self, ws, a, b):
        print("Connection closed")

    def add_data(self, data):
        """处理volGreeks消息数据"""
        option_code = data.get("optioncode")

        # 构建数据记录
        record = {
            'timestamp': pd.Timestamp.now(),
            **{k: data.get(k) for k in data.keys()},
        }

        # 更新期权数据
        self.options_latest[option_code] = record
        if self.keep_history:
            if option_code not in self.options_history:
                self.options_history[option_code] = []
            self.options_history[option_code].append(record)

    def get_option_latest(self, option_code):
        """获取期权最新数据"""
        return self.options_latest.get(option_code)

    def get_option_history(self, option_code):
        """获取期权历史数据"""
        return self.options_history.get(option_code, [])

    def get_options_by_key(self, key, value):
        """获取某个标的物的所有期权"""
        result = {}
        for code, data in self.options_latest.items():
            if data and key in data and data[key] == value:
                result[code] = data
        return result

    def clear_data(self):
        """清空数据"""
        self.options_latest = {}
        self.options_history = {}

    def stop(self):
        if self.ws:
            self.ws.close()
        if self.ws_thread:
            self.ws_thread.join(timeout=1)

    def _check_timeout_thread(self):
        while True:
            """检查是否超时并调用回调函数"""
            if time.time() - self.last_message_time > self.callback_interval:
                if self.data_updated_callback:
                    print(f'ws call at {datetime.datetime.fromtimestamp(time.time())} of check_timeout_thread {round(time.time() - self.last_message_time, 6)}') if self.debug else ''
                    # 在新线程中执行回调
                    threading.Thread(target=self.data_updated_callback, daemon=True).start()
                    time.sleep(self.fitvol_interval)


def login(data_updated_callback=None):
    """非阻塞的登录函数"""
    manager = MarketDataManager(keep_history=False,
                                data_updated_callback=data_updated_callback)  # 创建MarketDataManager实例
    manager.start_websocket()
    return manager  # 返回实例


def logout(manager):
    """非阻塞的登出函数"""
    manager.stop()
    # 清空数据
    manager.clear_data()  # 使用局部MarketDataManager实例


class QtMarketDataManager(MarketDataManager, QObject):  # Qt 版本
    def __init__(self, keep_history=False, data_updated_callback=None):
        QObject.__init__(self)
        MarketDataManager.__init__(self, keep_history, data_updated_callback)


def login_qt(data_updated_callback=None):
    """Qt 版本的登录函数"""
    manager = QtMarketDataManager(keep_history=False, data_updated_callback=data_updated_callback)
    manager.start_websocket()
    return manager


if __name__ == "__main__":
    manager = login()  # 获取实例
    logout(manager)  # 传递实例
