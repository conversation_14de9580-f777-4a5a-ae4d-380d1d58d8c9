import pandas as pd
import numpy as np
import datetime
import time
import os

def to_filter_files(x):
    filter_files = list()
    for i in x:
        if i[-4:] == ".csv" and i[:4] == "vols":
            print(i)
            filter_files.append(i)
    filter_files.sort()
    return filter_files

def get_atm_vol_df(file_name):
    vol = pd.read_csv(path + "/" + file_name, header=None, names=col, index_col=False,
                      usecols=range(19))
    vol = vol[(np.abs(vol["delta"]) >= 0.25) & (np.abs(vol["delta"]) <= 0.75)]

    vol["underlying"] = vol.contract.apply(lambda x: x[:6])  # 不够鲁棒

    vol = vol[vol["underlying"] == vol.underlying.min()]
    try:
        # call_list = vol.iloc[vol.groupby("time").apply(lambda x: np.argmin(np.abs(x.delta - 0.25)))]["contract"]
        # put_list = vol.iloc[vol.groupby("time").apply(lambda x: np.argmin(np.abs(x.delta + 0.25)))]["contract"]
        # call_list.index = vol["time"].unique()
        # put_list.index = vol["time"].unique()
        # common_index = np.intersect1d(call_list.index, put_list.index)
        # call_lst = call_list[common_index]
        # put_lst = put_list[common_index]
        # call_df = call_lst.reset_index()
        # put_df = put_lst.reset_index()


        # call_vol = pd.merge(vol, call_df, how='inner', left_on=["time", "contract"], right_on=["index", "contract"])[
        #     ["time", "contract", "spot", "vol"]]
        # put_vol = pd.merge(vol, put_df, how='inner', left_on=["time", "contract"], right_on=["index", "contract"])[
        #     ["time", "contract", "spot", "vol"]]
        #
        # total_vol = pd.merge(call_vol, put_vol, how='inner', left_on=["time"], right_on=["time"])
        # total_vol["skew"] = total_vol["vol_y"] - total_vol["vol_x"]
        vol = vol[vol["underlying"] == vol.underlying.min()]
        vol.time = vol.time.apply(lambda x: datetime.datetime.strptime(x, "%Y-%m-%d %H:%M:%S"))
        #降频至1min
        vol["time_stamp"] = vol.time.apply(lambda x: x.timestamp() // 60)
        x = vol.groupby("time_stamp").apply(lambda x: x.time.max())
        vol = vol[vol.time.isin(x)]

        #生成当时的0.5 delta call/put vol
        atm_vol = vol.groupby("time").apply(lambda x: x.iloc[np.argmin(np.abs(x.delta - 0.5))]).resample("1min",
                                                                                                            closed="right").last().fillna(
            method="ffill")


        atm_vol.rename(columns={"time": "original_time"}, inplace=True)



    except:
        atm_vol = pd.DataFrame([], columns= ['time', 'spot', 'contract', 'tv', 'bid', 'ask', 'vol', 'bidvol', 'askvol', 'delta', 'vega', 'r', 'q', 'T',
           'strike', 'x1', 'x2', 'enddate', 'basis'])

    return atm_vol

if __name__ == '__main__':
    # import time
    ####参数列######
    t1 = time.perf_counter()
    col = ['time', 'spot', 'contract', 'tv', 'bid', 'ask', 'vol', 'bidvol', 'askvol', 'delta', 'vega', 'r', 'q', 'T',
           'strike', 'x1', 'x2', 'enddate', 'basis']
    instrument = 'sc'
    # underlying = 'sc2212'

    ###############
    path = "D:/zzq/策略详细/skew/vol_log/sc"
    files = os.listdir(path)
    filter_files = to_filter_files(files)

    #print(get_skew_df(filter_files[1]))
    sum_final_vol = pd.DataFrame([], columns=col)
    for i in filter_files:
        x  = get_atm_vol_df(i)
        sum_final_vol = pd.concat([sum_final_vol, x], axis=0)

    # sum_final_skew = sum_final_skew.set_index(["time"])
    # sum_final_skew = sum_final_skew.resample("5min").last()
    sum_final_vol.to_csv("sum_atm_vol.csv")

    t2 = time.perf_counter()

    print('程序运行时间:%s秒' % ((t2 - t1) ))
