# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['D:/code/pythonworld/pnlback/volsym/main.py'],
    pathex=['D:/code/pythonworld/'],
    binaries=[],
    datas=[],
    hiddenimports=['pnlback.volsym.vol_cal_ui','pnlback.volsym.temple', 'pnlback.volsym.custom_widgets', 'pnlback.volsym.vol_cal'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['PyQt5'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)

exe = EXE(pyz,
          a.scripts, 
          [],
          exclude_binaries=True,
          name='TEngine',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          console=False,
          disable_windowed_traceback=False,
          target_arch=None,
          codesign_identity=None,
          entitlements_file=None )
coll = COLLECT(exe,
               a.binaries,
               a.zipfiles,
               a.datas, 
               strip=False,
               upx=True,
               upx_exclude=[],
               name='TEngine')