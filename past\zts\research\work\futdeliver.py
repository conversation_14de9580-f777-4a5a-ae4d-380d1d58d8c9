# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-11-29

import pandas as pd
import numpy as np
import pyodbc
from datetime import datetime
import math
import xlwt

from WindPy import w

w.start()

server = '10.25.18.36'
user = 'Alex'
password = '789456'

# Specifying the ODBC driver, server name, database, etc. directly
cnxn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user, charset="utf8")

# Create a cursor from the connection
# cursor = cnxn.cursor()


sql2 = "INSERT INTO [Alex].[dbo].[IndexFuture_min] VALUES (?,?,?,?,?,?,?,?,?)"

cursor = cnxn.cursor()

cursor.execute("""
 IF OBJECT_ID('IndexFuture_min', 'U') IS NOT NULL
    DROP TABLE IndexFuture_min
 CREATE TABLE IndexFuture_min (
    DateTime DATETIME NOT NULL,
    Code VARCHAR(20) NOT NULL,
    OpenPrice numeric(15, 2),
    High numeric(15, 2),
    Low numeric(15, 2),
    ClosePrice numeric(15, 2),
    Volume BIGINT,
    Amount BIGINT,
    oi BIGINT
    )
 """)
cnxn.commit()


def main(code0):
    if code0 == 'IF.CFE':
        codei = '000300.SH'
    elif code0 == 'IH.CFE':
        codei = '000016.SH'
    elif code0 == 'IC.CFE':
        codei = '000905.SH'
    wdata1 = w.wset("futurecc", "wind_code=%s" % code0)

    fm = pd.DataFrame(wdata1.Data, index=wdata1.Fields, columns=wdata1.Data[8])
    fm = fm.T  # 将矩阵转置
    # print(fm)

    fm = fm['2017-01':'2017-11']
    print(fm)

    for i, row in fm.iterrows():
        date1 = str(i)[:-9]
        # i = datetime.strptime(str(i)[:-9], "%Y-%m-%d")
        code = row['wind_code']
        wdatamin = w.wsi(code, "open,high,low,close,volume,amt,oi", "%s 00:00:00" % date1, "%s 23:59:59" % date1,
                         "PriceAdj=F")
        save(wdatamin, code)

        wdatamin2 = w.wsi(codei, "open,high,low,close,volume,amt,oi", "%s 00:00:00" % date1, "%s 23:59:59" % date1,
                          "PriceAdj=F")
        save(wdatamin2, codei)


def save(wdatamin, code):
    sqllist2 = []
    for j in range(0, len(wdatamin.Times)):
        sqllist = []
        sqllist.append(wdatamin.Times[j].strftime('%Y-%m-%d %H:%M:%S'))

        sqllist.append(str(code))

        for k in range(0, len(wdatamin.Fields)):
            sqllist.append(wdatamin.Data[k][j])

        # print(sqllist)

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)
        try:
            if math.isnan(sqltuple[-1]):
                sqllist[-1]=-1
                sqltuple = tuple(sqllist)
            cursor.execute(sql2, sqltuple)
        except Exception as e:
            print(sqllist)
            print(str(e))
        cnxn.commit()


main('IF.CFE')
main('IH.CFE')
main('IC.CFE')

cnxn.close()
