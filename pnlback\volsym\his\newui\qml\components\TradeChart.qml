import QtQuick
import QtCharts
import QtQuick.Controls
import QtQuick.Layouts

Item {
    id: root
    
    property bool normalizeByEdgePnl: false
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 5
        
        // 控制栏
        RowLayout {
            Layout.fillWidth: true
            
            CheckBox {
                text: "前后10分钟"
                onCheckedChanged: backend.setShowRecent(checked)
            }
            
            CheckBox {
                text: "NormaPNL"
                checked: root.normalizeByEdgePnl
                onCheckedChanged: backend.setNormalizeByEdgePnl(checked)
            }
        }
        
        // 图表
        ChartView {
            id: tradeChart
            Layout.fillWidth: true
            Layout.fillHeight: true
            antialiasing: true
            
            ValueAxis {
                id: tradeAxisX
                titleText: "Time"
            }
            
            ValueAxis {
                id: tradeAxisY
                titleText: normalizeByEdgePnl ? "Normalized PNL" : "PNL"
            }
            
            LineSeries {
                id: pnlSeries
                name: "PNL"
                axisX: tradeAxisX
                axisY: tradeAxisY
            }
        }
    }
}