
"""
配置文件，用于存储订单簿分析框架的配置参数
@author: lining
"""
from sklearn.preprocessing import RobustScaler

FACTOR_TEST = True
SCALER = RobustScaler()

DATA_PATH = "G:\\DATA\SH500\\md_%s_cffex_multicast.parquet"
DATE_STR = "20250425"
CODE_LIST = ['IM2506', ] 
MULT=200
TIME_RANGE = [('09:15:00', '11:29:00'), ('13:00:00', '14:57:00')]

BACKMODE = ['rolling', 'normal', 'days'][2]  

OUTDIR="output/"


WINDOW_SIZES = [5, 10, 20, 30]
PREDICTION_HORIZONS = [10, ]
EVALUATE_HORIZONS = [2,5,10,20,]
ENABLE_SHAP = True

TARGET_LABEL = "future_vwap_return"
TARGET_COLS = [f"{TARGET_LABEL}_{period}" for period in PREDICTION_HORIZONS]

MODEL_TYPE = {0:'lightgbm',1:'linear', 2:'xgboost', 3:'gbdt', 4:'random_forest', 5:'svm', 6:'mlp', 7:'knn', 8:'decision_tree', 9:'extra_trees', 10:'lstm'}[2]
MODEL_PARAMETER_SEARCH = False

CV_N_SPLITS = 3  

TRAIN_TEST_RATIO = 0.8
NUM_BOOST_ROUND = 200
EARLY_STOPPING_ROUNDS = 20
ROLLING_WINDOW_SIZE = 3000

VISUALIZE_TOP_N = 200 



QUANTILE_LIST = [0.5, 0.75, 0.9, 0.95]  
THRESHOLD_PERCENT = QUANTILE_LIST[-2]  
HOLDING_PERIOD = 10  
COMMISSION = 0.000023  
MAX_POSITION = 5.0  
STOP_LOSS = 0.001  
TAKE_PROFIT = 0.0002  
MIN_PROFIT = 0.002  
USE_MID_PRICE = True  
ORDER_SIZE = 1  
SPREAD_THRESHOLD = 0.00002  


LOG_CONFIG = {
    'enabled': True,  
    'levels': {
        'info': True,    
        'warning': True, 
        'error': True    
    }
}


MODEL_SETTING = {
    'linear': {'params': {}},
    'xgboost': {
        'params': {
            'learning_rate': 0.05,
            'max_depth': 6,
            'min_child_weight': 2,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'gamma': 0,
            'objective': 'reg:squarederror',
            'eval_metric': 'rmse',
            'nthread': 4,
            'seed': 42
        },
        'param_grid': {
            'learning_rate': [0.01, 0.05, 0.1],
            'max_depth': [3, 6, 9],
            'min_child_weight': [1, 2, 3],
            'subsample': [0.7, 0.8, 0.9],
        },
    },
    'lightgbm': {
        'params': {
            'learning_rate': 0.05,
            'max_depth': 6,
            'min_child_weight': 2,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
        },
        'param_grid': {
            'learning_rate': [0.01, 0.05, 0.1],
            'max_depth': [3, 6, 9],
            'min_child_weight': [1, 2, 3],
            'subsample': [0.7, 0.8, 0.9],
        }
    },
    'gbdt': {
        'params': {
            'n_estimators': 100,
            'max_depth': 5,
            'learning_rate': 0.1,
            'random_state': 42
        },
        'param_grid': {
            'n_estimators': [50, 100, 200],
            'max_depth': [3, 5, 7],
            'learning_rate': [0.01, 0.05, 0.1]
        }
    },
    'random_forest': {
        'params': {
            'n_estimators': 100,
            'max_depth': 10,
            'random_state': 42
        },
        'param_grid': {
            'n_estimators': [10, 20, 40],
            'max_depth': [None, 10, 20],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
    },
    'svm': {
        'params': {
            'kernel': 'rbf',
            'C': 1.0,
            'epsilon': 0.1,
            'gamma': 'scale'
        },
        'param_grid': {
            'C': [0.1, 1, 10],
            'epsilon': [0.01, 0.1, 0.2],
            'gamma': ['scale', 'auto']
        }
    },
    'mlp': {
        'params': {
            'hidden_layer_sizes': (100, 50),
            'activation': 'relu',
            'solver': 'adam',
        }
    },
    'knn': {
        'params': {
            'n_neighbors': 5,
            'weights': 'uniform',
            'algorithm': 'auto'
        }
    },
    'decision_tree': {
        'params': {
            'max_depth': 5,
            'min_samples_split': 2,
            'min_samples_leaf': 1
        }
    },
    'extra_trees': {
        'params': {
            'n_estimators': 100,
            'max_depth': 5,
            'min_samples_split': 2,
            'min_samples_leaf': 1
        },
        'param_grid': { 
            'n_estimators': [50, 100, 200],
            'max_depth': [3, 5, 7],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
    },
    'cnn': {
        'params': {
            'input_size': None,  
            'hidden_size': 64,   
            'output_size': 1,    
            'learning_rate': 0.001,
            'batch_size': 32,
            'n_epochs': 100,
            'device': 'cuda'      
        },
        'param_grid': {
            'hidden_size': [32, 64, 128],
            'learning_rate': [0.0001, 0.001, 0.01],
            'batch_size': [16, 32, 64]
        }
    },
    'lstm': {
        'params': {
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2
        },
        'param_grid': {
            'hidden_size': [32, 64, 128],
            'num_layers': [1, 2, 3],
            'dropout': [0.1, 0.2, 0.3]
        }
    }
}