""""""
from typing import Any
from vnpy.app.cta_strategy import (
    CtaTemplate,
    BarGenerator,
    ArrayManager,
    TickData,
    BarData,
    OrderData,
    TradeData
)

from vnpy.mytools.utility import SecondBarGenerator
from vnpy.trader.constant import Status

import numpy as np
from datetime import datetime, time as dtime


class TickStrategy(CtaTemplate):
    """"""

    author = "sincerefall"

    boll_window = 20
    boll_dev = 2
    trailing_long = 0.5
    trailing_short = 0.5
    fixed_size = 10

    boll_up = 0
    boll_down = 0
    trading_size = 0
    intra_trade_high = 0
    intra_trade_low = 0
    long_stop = 0
    short_stop = 0

    buy_price = 0
    short_price = 0

    mCount = 0
    last_bid = None
    last_ask = None
    lastP1 = None
    midP1 = None
    signal = None
    bidP1 = None
    askP1 = None
    bidV1 = None
    askV1 = None
    lastP = None

    parameters = [
        'boll_window',
        'boll_dev',
        'trailing_long',
        'trailing_short',
        'fixed_size',
    ]
    variables = [
        'boll_up',
        'boll_down',
        'trading_size',
        'intra_trade_high',
        'intra_trade_low',
        'long_stop',
        'short_stop',
        'buy_price',
        'short_price',
        'mCount',
        'last_bid',
        'last_ask',
        'lastP1',
        'midP1',
        'signal',
        'bidP1',
        'askP1',
        'bidV1',
        'askV1',
        'lastP'

    ]

    def __init__(
        self,
        cta_engine: Any,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
    
        self.sbg = SecondBarGenerator(self.on_bar)
        self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        self.am = ArrayManager(size=300)

        

        self.buy_vt_orderids = None
        self.sell_vt_orderids = None

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_tick(0)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """
        if (
            (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 55))
            or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 10))
            or (dt.time() > dtime(10, 35) and dt.time() < dtime(11, 25))
            or (dt.time() > dtime(13, 35) and dt.time() < dtime(14, 55))
        ):
            self.trading = True
        else:
            self.trading = False

    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.sbg.update_tick(tick)
        ts = 1
        self.bidP1 = tick.bid_price_1
        self.askP1 = tick.ask_price_1
        self.bidV1 = tick.bid_volume_1
        self.askV1 = tick.ask_volume_1
        self.lastP = tick.last_price
        spread = self.askP1 - self.bidP1
        midP = (self.bidP1+self.askP1)/2
        net = self.pos
        bidV = 0
        askV = 0
        bidP = self.bidP1 - ts
        askP = self.askP1 + ts

        if self.midP1 and self.lastP1:
            if spread <= 2 and np.sign(midP - self.lastP) == np.sign(self.midP1-self.lastP1):
                self.signal = midP - self.lastP
                bidV = self.fixed_size
                askV = self.fixed_size
                if self.signal > 0:  # 预计未来价格上涨
                    if spread == 1:
                        bidP = self.bidP1  # 针对lastP在bidP1和askP1之外的情况
                        askP = self.askP1 + ts
                    if spread == 2:
                        bidP = self.bidP1
                        askP = self.askP1
                    if net > 0:  # 我们不希望裸露过多的多头头寸
                        bidP = bidP - ts

                elif self.signal < 0:   # 预计未来价格下跌
                    if spread == 1:
                        bidP = self.bidP1 - ts
                        askP = self.askP1
                    if spread == 2:
                        bidP = self.bidP1
                        askP = self.askP1
                    if net < 0:  # 我们不希望裸露过多的空头头寸
                        askP = askP + ts

                else:  # 预计未来价格变动不大,指spread==2的情况
                    bidP = self.bidP1
                    askP = self.askP1
                    if net > 0:  # 平仓
                        askP = self.askP1 - ts
                    if net < 0:
                        bidP = self.bidP1 + ts

            else:  # 无信号2种情况 1:无连续信号 2：流动性较差
                self.signal = None  # 无信号的情况
                if spread <= 2:  # 只对流动性好的时候报价，价差放大，停止报价
                    bidP = self.bidP1 - ts
                    askP = self.askP1 + ts
                    bidV = self.fixed_size
                    askV = self.fixed_size

                    if net > 0:
                        askP = self.bidP1 + ts
                    if net < 0:
                        bidP = self.askP1 - ts

                else:  # 流动性较差停止报价，但如果存在净持仓，则朝持仓为0的方向激进报价
                    bidV = 0
                    askV = 0

                    if net > 0:
                        askP = max(self.askP1 - ts, self.bidP1 + 2*ts)

                    if net < 0:
                        bidP = min(self.bidP1 + ts, self.askP1 - 2*ts)

        if self.bidP1 > 0 and self.askP1 > 0:
            self.mCount += 1

        if self.buy_vt_orderids and (self.last_bid != bidP or bidV == 0):
            for orderID in self.buy_vt_orderids:
                self.cancel_order(orderID)
            self.buy_vt_orderids = None
        if self.sell_vt_orderids and (self.last_ask != askP or askV == 0):
            for orderID in self.sell_vt_orderids:
                self.cancel_order(orderID)
            self.sell_vt_orderids = None

        # 交易时间段内检查之前委托都已经结束
        if self.trading:
            if not self.buy_vt_orderids:
                if bidV > 0 and askV > 0:
                    self.buy_vt_orderids = self.buy(bidP, bidV)
                    self.last_bid = bidP

            if not self.sell_vt_orderids:
                if bidV > 0 and askV > 0:
                    self.sell_vt_orderids = self.short(askP, askV)
                    self.last_ask = askP

        # 记录上一批的lastP和midP
        self.lastP1 = self.lastP
        self.midP1 = midP

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.(second)
        """
        self.sbg5.update_bar(bar)

        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return

    def on_5s_bar(self, bar: BarData):
        """"""
        pass

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        pass

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        pass
