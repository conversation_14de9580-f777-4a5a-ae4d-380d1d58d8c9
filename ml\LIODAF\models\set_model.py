"""
模型训练模块
@author: lining
"""
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import ExtraTreesRegressor
from sklearn.neural_network import MLPRegressor
import xgboost as xgb
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from utils.utils import log_print
from models.lstm_regressor import LSTMRegressor
from models.deeplob import DeepLOBRegressor
from models.autogluon import AutoGluonRegressor

def set_model(model_type='xgboost', params=None):
    """
    设置模型
    """
    # 设置默认参数
    if params is None:
        log_print("未模型设置参数")

    if model_type == 'xgboost':
        # XGBoost：基于梯度提升的高效集成学习算法，适合处理高维特征和大规模数据
        # 常用参数：learning_rate, max_depth, n_estimators, subsample
        # 适用场景：金融时序预测、特征重要性分析
        # 数据注意事项：需要处理缺失值，对特征缩放不敏感，适合处理不平衡数据
        model = xgb.XGBRegressor(**params)
    elif model_type == 'lightgbm':
        # LightGBM：基于梯度提升的高效集成学习算法，适合处理高维特征和大规模数据
        # 常用参数：learning_rate, max_depth, n_estimators, subsample
        # 适用场景：金融时序预测、特征重要性分析
        # 数据注意事项：需要处理缺失值，对特征缩放不敏感，适合处理不平衡数据
        model = lgb.LGBMRegressor(**params)
    elif model_type == 'random_forest':
        # 随机森林：基于决策树的集成学习方法，通过多棵树的投票减少过拟合
        # 常用参数：n_estimators, max_depth, min_samples_split, max_features
        # 适用场景：处理高维数据，对异常值不敏感
        # 数据注意事项：不需要特征缩放，能自动处理缺失值和类别特征，但对高度相关特征敏感
        model = RandomForestRegressor(**params)
    elif model_type == 'gbdt':
        # 梯度提升决策树：通过顺序构建弱学习器来提高性能的集成方法
        # 常用参数：learning_rate, n_estimators, max_depth, subsample
        # 适用场景：回归问题，特征重要性分析
        # 数据注意事项：对异常值敏感，需要预处理，对特征缩放不敏感
        model = GradientBoostingRegressor(**params)
    elif model_type == 'linear':
        # 线性回归：简单且可解释性强的线性模型
        # 常用参数：fit_intercept, normalize
        # 适用场景：简单关系建模，特征重要性分析
        # 数据注意事项：需要特征缩放，对异常值敏感，需要线性关系假设
        model = LinearRegression(**params)
    elif model_type == 'svm':
        # 支持向量机：基于最大间隔的非线性模型
        # 常用参数：C, kernel, gamma, epsilon
        # 适用场景：高维特征空间，复杂决策边界
        # 数据注意事项：需要特征缩放，对异常值敏感，计算复杂度高
        model = SVR(**params)
    elif model_type == 'mlp':
        # 多层感知机：用于建模复杂非线性关系的神经网络
        # 常用参数：hidden_layer_sizes, activation, alpha, learning_rate
        # 适用场景：复杂模式识别，高维数据建模
        # 数据注意事项：需要特征缩放，对异常值敏感，需要足够的训练数据
        model = MLPRegressor(**params)
    elif model_type == 'knn':
        # K近邻：基于相似度的非参数化学习方法
        # 常用参数：n_neighbors, weights, p
        # 适用场景：简单模式识别，基于距离的预测
        # 数据注意事项：需要特征缩放，对噪声和异常值敏感，维度灾难问题
        model = KNeighborsRegressor(**params)
    elif model_type == 'decision_tree':
        # 决策树：基于规则的可解释模型
        # 常用参数：max_depth, min_samples_split, criterion
        # 适用场景：决策规则提取，特征重要性分析
        # 数据注意事项：不需要特征缩放，对噪声敏感，容易过拟合
        model = DecisionTreeRegressor(**params)
    elif model_type == 'extra_trees':
        # 极端随机树：类似随机森林但分裂更随机的集成方法
        # 常用参数：n_estimators, max_depth, min_samples_split
        # 适用场景：降低方差，提高泛化能力
        # 数据注意事项：不需要特征缩放，对噪声不敏感，计算效率高
        model = ExtraTreesRegressor(**params)
    elif model_type == 'lstm':
        # LSTM：长短期记忆网络，处理序列数据的深度学习模型
        # 常用参数：hidden_size, num_layers, batch_size, learning_rate
        # 适用场景：时间序列预测，序列建模
        # 数据注意事项：需要序列化数据，需要特征缩放，需要足够的训练数据
        model = LSTMRegressor(**params)
    elif model_type == 'deeplob':
        # DeepLOB：专门用于订单簿建模的深度学习模型
        # 常用参数：input_shape, hidden_size, num_layers, dropout
        # 适用场景：高频订单簿数据分析，市场微观结构建模
        # 数据注意事项：需要订单簿数据张量，需要特定数据预处理
        model = DeepLOBRegressor(**params)
    elif model_type == 'autogluon':
        # AutoGluon：自动机器学习库，可以自动构建和优化多种模型的集成
        # 常用参数：time_limit, presets, eval_metric
        # 适用场景：快速模型开发，多模型集成，自动调参
        # 数据注意事项：能自动处理各种类型的数据，包括缺失值、类别特征等
        model = AutoGluonRegressor(**params)
    else:
        model = None
        log_print(f"未知模型类型: {model_type}", "error")

    return model






