import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
#%%%
from typing import Any,List, Dict
from datetime import datetime, time as dtime

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

class TickWithRefer(StrategyTemplate):

    author = "sincerefall"

    boll_window = 1200
    boll_dev = 2
    fixed_size = 10
    leg1_ratio = 1
    leg2_ratio = 1

    maker_symbol = ""
    refer_symbol = ""
    current_spread = 0.0
    boll_mid = 0.0
    boll_down = 0.0
    boll_up = 0.0

    parameters = [
        "price_add",
        "boll_window",
        "boll_dev",
        "fixed_size",
        "leg1_ratio",
        "leg2_ratio",
    ]
    variables = [
        "maker_symbol",
        "refer_symbol",
        "current_spread",
        "boll_mid",
        "boll_down",
        "boll_up",
    ]

    def __init__(
        self,
        strategy_engine: Any,
        strategy_name: str,
        vt_symbols: List[str],
        setting: dict
    ):
        super().__init__(strategy_engine, strategy_name, vt_symbols, setting)

        self.targets: Dict[str, int] = {}
        self.spread_count: int = 0
        self.spread_data: np.array = np.zeros(3600)

        # 拆分目标单和主力单
        self.maker_symbol, self.refer_symbol = vt_symbols

        for vt_symbol in self.vt_symbols:
            self.targets[vt_symbol] = 0

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        #self.load_ticks(3600)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_ticks(self, ticks: Dict[str, TickData]):

        self.cancel_all()

        # Return if one leg data is missing
        if self.maker_symbol not in ticks or self.refer_symbol not in ticks:
            return

        # Calculate current spread
        leg1_tick = ticks[self.maker_symbol]
        leg2_tick = ticks[self.refer_symbol]

        self.current_spread = (
            leg1_tick.ask_price_1 * self.leg1_ratio - leg2_tick.ask_price_1 * self.leg2_ratio
        )

        # Update to spread array
        self.spread_data[:-1] = self.spread_data[1:]
        self.spread_data[-1] = self.current_spread

        self.spread_count += 1
        if self.spread_count <= self.boll_window:
            return

        # Calculate boll value
        buf: np.array = self.spread_data[-self.boll_window:]

        std = buf.std()
        self.boll_mid = buf.mean()
        self.boll_up = self.boll_mid + self.boll_dev * std
        self.boll_down = self.boll_mid - self.boll_dev * std

        # Calculate new target position
        leg1_pos = self.get_pos(self.maker_symbol)

        if not leg1_pos:
            if self.current_spread >= self.boll_up:
                self.targets[self.maker_symbol] = -1*self.fixed_size
                self.targets[self.refer_symbol] = 1*self.fixed_size
            elif self.current_spread <= self.boll_down:
                self.targets[self.maker_symbol] = 1*self.fixed_size
                self.targets[self.refer_symbol] = -1*self.fixed_size
        elif leg1_pos > 0:
            if self.current_spread >= self.boll_mid:
                self.targets[self.maker_symbol] = 0
                self.targets[self.refer_symbol] = 0
        else:
            if self.current_spread <= self.boll_mid:
                self.targets[self.maker_symbol] = 0
                self.targets[self.refer_symbol] = 0

        # Execute orders
        for vt_symbol in self.vt_symbols:
            target_pos = self.targets[vt_symbol]
            current_pos = self.get_pos(vt_symbol)

            pos_diff = target_pos - current_pos
            tick=ticks[vt_symbol]
            volume = abs(pos_diff)

            if pos_diff > 0:
                if current_pos < 0:
                    # self.cover(vt_symbol, tick.ask_price_1, volume)
                    self.buy(vt_symbol, tick.ask_price_1, volume)
                else:
                    self.buy(vt_symbol, tick.ask_price_1, volume)
                #print('buy',vt_symbol,current_pos,tick.datetime)
            elif pos_diff < 0:
                if current_pos > 0:
                    # self.sell(vt_symbol, tick.bid_price_1, volume)
                    self.short(vt_symbol, tick.bid_price_1, volume)
                else:
                    self.short(vt_symbol, tick.bid_price_1, volume)
                #print('short',vt_symbol,current_pos,tick.datetime)

        # self.put_event()

    def on_trade(self, trade: TradeData) -> None:
        """
        Callback of new trade data update.
        """
        pass

    def on_order(self, order: OrderData) -> None:
        """
        Callback of new order data update.
        """
        pass