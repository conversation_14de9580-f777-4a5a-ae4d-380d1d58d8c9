import matplotlib.pyplot as plt
import seaborn as sns
from factors.ob_std import *

# 构建训练和测试数据
def data_generate(daily_data, type_name):
    # 示例：type_name = 'IC2412'
    data_type = daily_data[daily_data['Symbol'].str.startswith(type_name)].reset_index(drop=True)

    # 剔除不需要的列
    data_type_filter = data_type.iloc[:,:35]
    # 剔除全都是0的列
    data_type_filter = data_type_filter.loc[:, (data_type_filter!= 0).any(axis=0)]
    # # 计算特征 mid未来5s的pctchange
    # mid = (data_type_filter['AskPrice1'] + data_type_filter['BidPrice1'])/2
    # target = (mid.shift(-10) - mid) / mid

    # 计算平均涨幅
    # 未来5s mid价格的平均
    mid = (data_type_filter['AskPrice1'] + data_type_filter['BidPrice1'])/2
    forward_rolling_mid = mid.rolling(window=10).mean().shift(-9)
    target = (forward_rolling_mid - mid)/mid


    data_stand = pd.DataFrame({'TimeStamp':data_type_filter['TimeStamp'],
                          'Feature1':order_imb(data_type_filter),
                          'Feature1-2':order_imb(data_type_filter,level='2'),
                          'Feature1-3':order_imb(data_type_filter,level='3'),
                          'Feature2':order_price_mid(data_type_filter),
                          'Feature2-2':order_price_mid(data_type_filter, level='2'),
                          'Feature2-3':order_price_mid(data_type_filter, level='3'),
                          'Feature3':nettradeprice_mid(data_type_filter),
                          'Feature4':mom_last(data_type_filter),
                          'Feature5':reversal_last(data_type_filter),
                          'Feature4-1':mom_last(data_type_filter, window=10),
                          'Feature5-1':reversal_last(data_type_filter, window=60),
                          'Feature6':order_imb_diff(data_type_filter),
                          'Feature7':ob_depth(data_type_filter),
                          'Feature8':trade_flow(data_type_filter, contract_mul=200, window=1),
                          'Feature8-1':trade_flow(data_type_filter, contract_mul=200, window=60),
                          'Feature9':order_flow_imb(data_type_filter),
                          'Feature9-1':order_flow_imb(data_type_filter, window=60),
                          'target':target})
    return data_stand
# 示例：对IC2412合约进行预测 进行数据构造

# 数据导入
df1 = pd.read_csv('./data/md_20241128_exanic_cffex.csv')
df2 = pd.read_csv('./data/md_20241129_exanic_cffex.csv')

print("generate dataset 1")
df1_stand = data_generate(df1, 'IC2412')
print("generate dataset 2")
df2_stand = data_generate(df2, 'IC2412')

# 由于数据量足够，故暂时考虑剔除掉包含na的数据样本对
df1_stand.dropna(inplace=True)
df2_stand.dropna(inplace=True)

df_all = pd.concat([df1_stand, df2_stand]).reset_index(drop=True)
print(df_all.head())
df_all.info()
df_all.to_csv('./data/temp.csv',index=False)

feature = df_all.loc[:,df_all.columns.str.startswith('Feature')]
correlation_matrix = feature.dropna().corr()

print("Correlation matrix:\n", correlation_matrix)

# 绘制相关系数矩阵的热力图
plt.figure(figsize=(8, 6))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)
plt.title('Correlation Matrix')
plt.show()

