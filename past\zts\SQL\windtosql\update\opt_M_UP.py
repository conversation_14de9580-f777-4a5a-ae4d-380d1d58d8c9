# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-10-20
from WindPy import w
import pyodbc
from datetime import datetime
from datetime import timedelta
import pandas as pd

#import sys
#reload(sys)
#sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
table="[Alex].[dbo].[Opt_M]"

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
cursor = conn.cursor()

import sys
sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')
from SQL.windtosql.update import datemakerDay
indexnum = 100
dt, beginDate, lastday = datemakerDay.datemaker(table, conn, cursor, indexnum)
w.start()

sqllist2 = []


def updatefrwind(cpcode, date):
        wcodes = w.wset("sectorconstituent", "date=%s;sectorid=%s" % (date, cpcode))
        if wcodes.ErrorCode != 0:
            print("空")
        codes3 = []
        for iii in range(0, len(wcodes.Data[1])):
            codes3.append(wcodes.Data[1][iii])
        print(codes3)
        windtolist(codes3, date)


def windtolist(codes2, date):
    wdata = w.wss(codes2, "open,high,low,close,settle,pre_close,pre_settle,chg,chg_settlement,volume,oi,oi_chg,amt,delta,us_impliedvol,exe_price,lasttradingdate","tradeDate= %s;priceAdj=U;cycle=D;unit=1" % date)

    if wdata.ErrorCode != 0:
        import time
        print(len(wdata.Data[1]))
        print(u"暂停提取数据30秒..请稍等")
        time.sleep(30)
        return windtolist(codes2, date)

    #pf2 = pf.ix['2017-04-24', 'SR707C6200']
#print(pf2)
    print(codes2)
    print(len(wdata.Data[1]))

    for ii in range(0, len(wdata.Data[0])):
        sqllist = []

        date1 = date
        sqllist.append(date1.strftime('%Y-%m-%d'))
        sqllist.append(wdata.Codes[ii])

        for k in range(0, len(wdata.Fields)):
            sqllist.append(wdata.Data[k][ii])

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)


datelist=w.tdays(beginDate, dt, "").Data[0]
for i in datelist:
    date = i
    print(date)
    updatefrwind(1000015593000000, date)
    updatefrwind(1000015594000000, date)

sql2 = "INSERT INTO %s VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" % table

cursor.executemany(sql2, sqllist2)
conn.commit()
