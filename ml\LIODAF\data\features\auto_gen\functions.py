"""The functions used to create programs.

The :mod:`gplearn.functions` module contains all of the functions used by
gplearn programs. It also contains helper methods for a user to define their
own custom functions.
"""

# Author: <PERSON> <trevorstephens.com>
#
# License: BSD 3 clause

import numpy as np
import pandas as pd
from joblib import Parallel, delayed
from scipy.stats import kurtosis
from scipy.stats import skew
import statsmodels.api as sm
import warnings

from hquant.basic_model.util.global_util import get_global

warnings.filterwarnings('ignore', category=RuntimeWarning)

__all__ = ['make_function']


class _Function(object):
    """A representation of a mathematical relationship, a node in a program.

    This object is able to be called with NumPy vectorized arguments and return
    a resulting vector based on a mathematical relationship.

    Parameters
    ----------
    function : callable
        A function with signature function(x1, *args) that returns a Numpy
        array of the same shape as its arguments.

    name : str
        The name for the function as it should be represented in the program
        and its visualizations.

    arity : int
        The number of arguments that the ``function`` takes.

    type : int
        The type of function
    """

    def __init__(self, function, name, arity, _type):
        self.function = function
        self.name = name
        self.arity = arity
        self.type = _type

    def __call__(self, *args):
        return self.function(*args)


def make_function(function, name, arity, _type='normal'):
    """Make a function node, a representation of a mathematical relationship.

    This factory function creates a function node, one of the core nodes in any
    program. The resulting object is able to be called with NumPy vectorized
    arguments and return a resulting vector based on a mathematical
    relationship.

    Parameters
    ----------
    function : callable
        A function with signature `function(x1, *args)` that returns a Numpy
        array of the same shape as its arguments.

    name : str
        The name for the function as it should be represented in the program
        and its visualizations.

    arity : int
        The number of arguments that the `function` takes.

    """
    '''
    if not isinstance(arity, int):
        raise ValueError('arity must be an int, got %s' % type(arity))
    if not isinstance(function, np.ufunc):
        if function.__code__.co_argcount != arity:
            raise ValueError('arity %d does not match required number of '
                             'function arguments of %d.'
                             % (arity, function.__code__.co_argcount))
    if not isinstance(name, str):
        raise ValueError('name must be a string, got %s' % type(name))

    # Check output shape
    args = [np.ones(10) for _ in range(arity)]
    try:
        function(*args)
    except ValueError:
        raise ValueError('supplied function %s does not support arity of %d.'
                         % (name, arity))
    if not hasattr(function(*args), 'shape'):
        raise ValueError('supplied function %s does not return a numpy array.'
                         % name)
    if function(*args).shape != (10,):
        raise ValueError('supplied function %s does not return same shape as '
                         'input vectors.' % name)

    # Check closure for zero & negative input arguments
    args = [np.zeros(10) for _ in range(arity)]
    if not np.all(np.isfinite(function(*args))):
        raise ValueError('supplied function %s does not have closure against '
                         'zeros in argument vectors.' % name)
    args = [-1 * np.ones(10) for _ in range(arity)]
    if not np.all(np.isfinite(function(*args))):
        raise ValueError('supplied function %s does not have closure against '
                         'negatives in argument vectors.' % name)
    '''
    return _Function(function, name, arity, _type)


def _ts_argmax(x1, window):
    interval = x1.shape[1]

    def _df_ts_argmax(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanargmax(x1[:, k:window + k], axis=1) + 1.0

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_argmax)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_argmin(x1, window):
    interval = x1.shape[1]

    def _df_ts_argmax(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanargmin(x1[:, k:window + k], axis=1) + 1.0

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_argmax)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _delay(x1, window):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    result = x1[:, :-window + 1]
    fill = np.full([x1.shape[0], window - 1], np.nan)
    result = np.column_stack([fill, result])
    return result


def _delta(x1, window):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    result = x1[:, window - 1:] / x1[:, :-window + 1] - 1
    fill = np.full([x1.shape[0], window - 1], np.nan)
    result = np.column_stack([fill, result])
    return result


def _ts_stddev(x1, window):
    interval = x1.shape[1]

    def _df_ts_stddev(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanstd(x1[:, k:window + k], axis=1, ddof=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_stddev)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_sum(x1, window):
    interval = x1.shape[1]

    def _df_ts_sum(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nansum(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_sum)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    result[np.isnan(x1)] = np.nan
    return result


def _ts_max(x1, window):
    interval = x1.shape[1]

    def _df_ts_max(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanmax(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_max)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_min(x1, window):
    interval = x1.shape[1]

    def _df_ts_min(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanmin(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_min)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_nanmean(x1, window):
    interval = x1.shape[1]

    def _df_ts_nanmean(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanmean(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_nanmean)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_prod(x1, window):
    interval = x1.shape[1]

    def _df_ts_prod(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanprod(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_prod)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    result[np.isnan(x1)] = np.nan
    return result


def _ts_rank(x1, window):
    interval = x1.shape[1]

    def _df_ts_rank(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        data = pd.DataFrame(x1[:, k:window + k])
        return data.rank(axis=1).iloc[:, -1].values / window

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_rank)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _rank(x1):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    data = pd.DataFrame(x1)
    rank = data.rank().values
    return rank / np.nanmax(rank, axis=0)


def _ts_covariance(x1, x2, window):
    interval = x1.shape[1]

    def _df_ts_cov(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        data1 = pd.DataFrame(x1[:, k:window + k].T)
        data2 = pd.DataFrame(x2[:, k:window + k].T)
        tmp = data1.covwith(data2).values
        return tmp

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_cov)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    data = pd.DataFrame(result)
    rank = data.rank().values
    return rank / np.nanmax(rank, axis=0)


def _ts_correlation(x1, x2, window):
    interval = x1.shape[1]

    def _df_ts_corr(k=0):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        data1 = pd.DataFrame(x1[:, k:window + k].T)
        data2 = pd.DataFrame(x2[:, k:window + k].T)
        tmp = data1.corrwith(data2).values
        return tmp

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_corr)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    data = pd.DataFrame(result)
    rank = data.rank().values
    return rank / np.nanmax(rank, axis=0)


def _protected_rank_add(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    data = pd.DataFrame(x1)
    rank = data.rank().values
    rank1 = rank / np.nanmax(rank, axis=0)
    data = pd.DataFrame(x2)
    rank = data.rank().values
    rank2 = rank / np.nanmax(rank, axis=0)

    return np.add(rank1, rank2)


def _protected_rank_sub(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    data = pd.DataFrame(x1)
    rank = data.rank().values
    rank1 = rank / np.nanmax(rank, axis=0)
    data = pd.DataFrame(x2)
    rank = data.rank().values
    rank2 = rank / np.nanmax(rank, axis=0)

    return np.subtract(rank1, rank2)


def _protected_rank_mul(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    data = pd.DataFrame(x1)
    rank = data.rank().values
    rank1 = rank / np.nanmax(rank, axis=0)
    data = pd.DataFrame(x2)
    rank = data.rank().values
    rank2 = rank / np.nanmax(rank, axis=0)

    return np.multiply(rank1, rank2)


def _protected_rank_division(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    data = pd.DataFrame(x1)
    rank = data.rank().values
    rank1 = rank / np.nanmax(rank, axis=0)
    data = pd.DataFrame(x2)
    rank = data.rank().values
    rank2 = rank / np.nanmax(rank, axis=0)

    with np.errstate(divide='ignore', invalid='ignore'):
        return np.where(np.abs(rank2) > 0.001, np.divide(rank1, rank2), 1.)


def _protected_sqrt(x1):
    """Closure of square root for negative arguments."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    return np.sqrt(np.abs(x1))


def _protected_log(x1):
    """Closure of log for zero arguments."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    with np.errstate(divide='ignore', invalid='ignore'):
        return np.where(np.abs(x1) > 0.001, np.log(np.abs(x1)), 0.)


def _protected_inverse(x1):
    """Closure of log for zero arguments."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    with np.errstate(divide='ignore', invalid='ignore'):
        return np.where(np.abs(x1) > 0.001, 1. / x1, 0.)


def _sigmoid(x1):
    """Special case of logistic function to transform to probabilities."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    with np.errstate(over='ignore', under='ignore'):
        return 1 / (1 + np.exp(-x1))


def _protected_add(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    return np.add(x1, x2)


def _protected_division(x1, x2):
    """Closure of division (x1/x2) for zero denominator."""
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    with np.errstate(divide='ignore', invalid='ignore'):
        return np.where(np.abs(x2) > 0.001, np.divide(x1, x2), 1.)


def _decay_linear(x1, window):
    interval = x1.shape[1]
    num = np.array(list(range(window))) + 1.0
    coe = np.tile(num, (x1.shape[0], 1))

    def _sub_decay_linear(k, coe):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        data = x1[:, k:window + k]
        isnan = np.isnan(data)
        coe[isnan] = np.nan
        sum_days = np.nansum(coe, axis=1)
        sum_days = np.tile(sum_days, (window, 1)).T
        coe = coe / sum_days
        decay = np.nansum(coe * data, axis=1)
        decay[isnan[:, -1]] = np.nan
        return decay

    tmparray = np.array(
        Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_sub_decay_linear)(k + 1, coe) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _signedpower(x1, power):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    return np.sign(x1) * np.power(np.abs(x1), power)


# =================================================================================

def _ts_skewness(x1, window):
    interval = x1.shape[1]

    def _df_ts_skewness(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return skew(x1[:, k:window + k], axis=1, bias=False)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_skewness)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_kurtosis(x1, window):
    interval = x1.shape[1]

    def _df_ts_kurtosis(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return kurtosis(x1[:, k:window + k], axis=1, bias=False)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_kurtosis)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_max_diff(x1, window):
    interval = x1.shape[1]

    def _df_ts_max_diff(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return x1[:, window + k - 1] - np.nanmax(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_max_diff)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_min_diff(x1, window):
    interval = x1.shape[1]

    def _df_ts_min_diff(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return x1[:, window + k - 1] - np.nanmin(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_min_diff)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_return(x1, window):
    interval = x1.shape[1]

    def _df_ts_return(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return (x1[:, window + k - 1] - x1[:, k]) / x1[:, k]

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_return)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_zscore(x1, window):
    interval = x1.shape[1]

    def _df_ts_zscore(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return ((x1[:, window + k - 1]).T - np.nanmean(x1[:, k:window + k], axis=1)) / np.nanstd(x1[:, k:window + k],
                                                                                                 axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_zscore)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_scale(x1, window):
    interval = x1.shape[1]

    def _df_ts_scale(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return (x1[:, window + k - 1] - np.nanmin(x1[:, k:window + k], axis=1)) / (
                np.nanmax(x1[:, k:window + k], axis=1) - np.nanmin(x1[:, k:window + k], axis=1))

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_scale)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_min_max_cps(x1, window, f=2):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    interval = x1.shape[1]

    def _df_ts_cps(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanmin(x1[:, k:window + k], axis=1) + np.nanmax(x1[:, k:window + k], axis=1) - f * x1[:,
                                                                                                     window + k - 1]

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_cps)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_ir(x1, window):
    interval = x1.shape[1]

    def _df_ts_ir(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanmean(x1[:, k:window + k], axis=1) / np.nanstd(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_ir)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _ts_median(x1, window):
    interval = x1.shape[1]

    def _df_ts_median(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return np.nanmedian(x1[:, k:window + k], axis=1)

    tmparray = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_df_ts_median)(k + 1) for k in range(0, interval - window))).T
    result = np.full([x1.shape[0], window], np.nan)
    result = np.column_stack([result, tmparray])
    return result


def _sign(x1):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    return np.sign(x1)


# =======================================================================
# 2019-07-11 新增

def _zscore(x1):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    zs = (x1 - np.nanmean(x1, axis=0)) / np.nanstd(x1, axis=0)
    return zs


def _zscore_square(x1):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    zs = (x1 - np.nanmean(x1, axis=0)) / np.nanstd(x1, axis=0)
    return zs ** 2


def _winsorize(x1):
    interval = x1.shape[1]

    def _is_dummy(x):
        # return x in [0,1,np.nan]
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        return (x == 0) or (x == 1) or (x == np.nan)

    def _winsorize_by_col(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        col = x1[:, k].copy()
        m = np.nanmedian(col)
        mm = np.nanmedian(np.abs(col - m))
        if mm == 0:
            m_mask = (col == m)
            col[m_mask] = np.nan
            mm = np.abs(col - m)
            try:
                col[col > m + 5 * mm] = m + 5 * mm
                col[col < m - 5 * mm] = m - 5 * mm
            except ValueError:
                pass
            col[m_mask] = m
        else:
            try:
                col[col > m + 5 * mm] = m + 5 * mm
                col[col < m - 5 * mm] = m - 5 * mm
            except ValueError:
                pass
        return col

    result = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_winsorize_by_col)(k) for k in range(0, interval))).T

    return result


def _regress_resid(y1, x1):
    interval = x1.shape[1]

    def _regress_resid_cs(k):
        warnings.filterwarnings('ignore', category=RuntimeWarning)
        y_c = y1[:, k]
        x_c = x1[:, k]
        resid = np.full(x1.shape[0], np.nan)
        try:
            model = sm.OLS(y_c, x_c, missing='drop')
            results = model.fit()
            nanmask = np.isnan(x_c)
            nanmask[np.isnan(y_c)] = True
            resid[~nanmask] = y_c[~nanmask] - results.fittedvalues
        except:
            resid[:] = np.nan
        return resid

    result = np.array(Parallel(n_jobs=get_global('n_jobs', 1))(delayed(_regress_resid_cs)(k) for k in range(0, interval))).T

    return result


def _non_linear(x1):
    warnings.filterwarnings('ignore', category=RuntimeWarning)
    x1 = _zscore(_winsorize(x1)) + 1
    nl = _regress_resid(x1 ** 3, x1)
    # nl = _winsorize(x1)
    return nl


# ========================================================================

add2 = make_function(function=_protected_add, name='add', arity=2)
sub2 = make_function(function=np.subtract, name='sub', arity=2)
mul2 = make_function(function=np.multiply, name='mul', arity=2)
div2 = make_function(function=_protected_division, name='div', arity=2)
rank_add2 = make_function(function=_protected_rank_add, name='rank_add', arity=2)
rank_sub2 = make_function(function=_protected_rank_sub, name='rank_sub', arity=2)
rank_mul2 = make_function(function=_protected_rank_mul, name='rank_mul', arity=2)
rank_div2 = make_function(function=_protected_rank_division, name='rank_div', arity=2)
sqrt1 = make_function(function=_protected_sqrt, name='sqrt', arity=1)
log1 = make_function(function=_protected_log, name='log', arity=1)
neg1 = make_function(function=np.negative, name='neg', arity=1)
inv1 = make_function(function=_protected_inverse, name='inv', arity=1)
abs1 = make_function(function=np.abs, name='abs', arity=1)
max2 = make_function(function=np.maximum, name='max', arity=2)
min2 = make_function(function=np.minimum, name='min', arity=2)
sin1 = make_function(function=np.sin, name='sin', arity=1)
cos1 = make_function(function=np.cos, name='cos', arity=1)
tan1 = make_function(function=np.tan, name='tan', arity=1)
sig1 = make_function(function=_sigmoid, name='sig', arity=1)
ts_corr3 = make_function(function=_ts_correlation, name='ts_corr', arity=3, _type='ts')
ts_cov3 = make_function(function=_ts_covariance, name='ts_cov', arity=3, _type='ts')
ts_sum2 = make_function(function=_ts_sum, name='ts_sum', arity=2, _type='ts')
ts_stddev2 = make_function(function=_ts_stddev, name='ts_stddev', arity=2, _type='ts')
ts_max2 = make_function(function=_ts_max, name='ts_max', arity=2, _type='ts')
ts_min2 = make_function(function=_ts_min, name='ts_min', arity=2, _type='ts')
ts_nanmean2 = make_function(function=_ts_nanmean, name='ts_nanmean', arity=2, _type='ts')
ts_prod2 = make_function(function=_ts_prod, name='ts_prod', arity=2, _type='ts')
ts_rank2 = make_function(function=_ts_rank, name='ts_rank', arity=2, _type='ts')
rank1 = make_function(function=_rank, name='rank', arity=1)

delay2 = make_function(function=_delay, name='delay', arity=2, _type='ts')
delta2 = make_function(function=_delta, name='delta', arity=2, _type='ts')
ts_argmax2 = make_function(function=_ts_argmax, name='ts_argmax', arity=2, _type='ts')
ts_argmin2 = make_function(function=_ts_argmin, name='ts_argmin', arity=2, _type='ts')
decay_linear2 = make_function(function=_decay_linear, name='decay_linear', arity=2, _type='ts')
signedpower1 = make_function(function=_signedpower, name='signedpower', arity=1)

sign1 = make_function(function=_sign, name='sign', arity=1)
sigmoid1 = make_function(function=_sigmoid, name='sigmoid', arity=1)
ts_skew2 = make_function(function=_ts_skewness, name='ts_skewness', arity=2, _type='ts')
ts_kurt2 = make_function(function=_ts_kurtosis, name='ts_kurtosis', arity=2, _type='ts')
ts_max_diff2 = make_function(function=_ts_max_diff, name='ts_max_diff', arity=2, _type='ts')
ts_min_diff2 = make_function(function=_ts_min_diff, name='ts_min_diff', arity=2, _type='ts')
ts_return2 = make_function(function=_ts_return, name='ts_return', arity=2, _type='ts')
ts_zscore2 = make_function(function=_ts_zscore, name='ts_zscore', arity=2, _type='ts')
ts_scale2 = make_function(function=_ts_scale, name='ts_scale', arity=2, _type='ts')
ts_min_max_cps2 = make_function(function=_ts_min_max_cps, name='ts_min_max_cps', arity=2, _type='ts')
ts_ir2 = make_function(function=_ts_ir, name='ts_ir', arity=2, _type='ts')
ts_median2 = make_function(function=_ts_median, name='ts_median', arity=2, _type='ts')

winsorize = make_function(function=_winsorize, name='winsorize', arity=1)
zscore = make_function(function=_zscore, name='zscore', arity=1)
zscore_square = make_function(function=_zscore_square, name='zscore_square', arity=1)
regress_resid = make_function(function=_regress_resid, name='regress_resid', arity=2)
non_linear = make_function(function=_non_linear, name='non_linear', arity=1)

_function_map = {'add': add2,
                 'sub': sub2,
                 'mul': mul2,
                 'div': div2,
                 'rank_add': rank_add2,
                 'rank_sub': rank_sub2,
                 'rank_mul': rank_mul2,
                 'rank_div': rank_div2,
                 'sqrt': sqrt1,
                 'log': log1,
                 'abs': abs1,
                 'neg': neg1,
                 'inv': inv1,
                 'max': max2,
                 'min': min2,
                 'sin': sin1,
                 'cos': cos1,
                 'tan': tan1,
                 'ts_corr': ts_corr3,
                 'ts_cov': ts_cov3,
                 'ts_stddev': ts_stddev2,
                 'ts_sum': ts_sum2,
                 'ts_max': ts_max2,
                 'ts_min': ts_min2,
                 'ts_nanmean': ts_nanmean2,
                 'ts_prod': ts_prod2,
                 'ts_rank': ts_rank2,
                 'rank': rank1,
                 'delay': delay2,
                 'delta': delta2,
                 'ts_argmax': ts_argmax2,
                 'ts_argmin': ts_argmin2,
                 'decay_linear': decay_linear2,
                 'signedpower': signedpower1,
                 'sigmoid': sigmoid1,
                 'sign': sign1,
                 'ts_skewness': ts_skew2,
                 'ts_kurtosis': ts_kurt2,
                 'ts_max_diff': ts_max_diff2,
                 'ts_min_diff': ts_min_diff2,
                 'ts_return': ts_return2,
                 'ts_zscore': ts_zscore2,
                 'ts_scale': ts_scale2,
                 'ts_min_max_cps': ts_min_max_cps2,
                 'ts_ir': ts_ir2,
                 'ts_median': ts_median2,
                 'winsorize': winsorize,
                 'zscore': zscore,
                 'zscore_square': zscore_square,
                 'regress_resid': regress_resid,
                 'non_linear': non_linear
                 }
