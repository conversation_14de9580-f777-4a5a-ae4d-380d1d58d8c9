#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('CZCE.dev')
import warnings
warnings.filterwarnings('ignore')

""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.reverseStrategy import ReverseStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np

#%%
maker='CF211.CZCE'
refer='CF205.CZCE'

multiplier=5

engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=True,duty=False,save_result=False,refer_test=False,fast_join=True,refer_late=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔

    start=datetime.datetime(2022,2, 10, 21, 0), # 开始时间
    end=datetime.datetime(2022, 2,10, 23, 0), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: 5,refer: 5}, # 一个tick大小
    capital=1_000_000, # 初始资金
)

# 添加回测策略，并修改内部参数
engine.clear_data()

engine.clear_data()
engine.add_strategy(ReverseStrategy, {'lots': 10,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      'minEdge':3,'edge':3,'eta':1,'gamma':1,'validVolume':10,'safeVolume':20,'maxPos':50,'loss':5000,
                                      'referHedgeFlag':False,'neverStopFlag':False,'useSVMSingal':False,'useReverseOrder':False,'fixedFader':0,'stop':1,'tradingOffsetSignal':False}) #hc2202
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
# engine.duty_statistics()