import matplotlib.pyplot as plt
import pandas as pd
import datetime
import numpy as np
import matplotlib.pyplot as plt

def flatten_multi_index(multi_index, join_str='_'):
    label0 = multi_index.get_level_values(0)
    label1 = multi_index.get_level_values(1)
    index = [str(i) + join_str + str(int(j)) for i, j in zip(label0, label1)]
    return pd.Index(index)


datetoday = '20230314'
under = 'SH300'
str1 = u'vols_%s.csv' % datetoday
str2 = u'TradeRecords%s.csv' % datetoday
atm = 4.0
multi = 10000
multi2 = 10157

tradetime00 = [['09:30:00', '11:30:00'], ['13:00:00', '15:00:00']]
# tradetime00 = [['14:55:00', '14:59:00'], ['14:59:00', '15:00:00']]

print('load trade')
tradeData = pd.read_csv(u'DATA/%s/%s' % (under, str2), parse_dates=[1, 2], index_col=2, names=None, header=0,
                        encoding='utf-16', sep='\t')
print('done load trade')
expTrade = tradeData['Expiry'].unique()
tradeData.index = pd.to_datetime(datetoday + ' ' + tradeData.index, format='%Y%m%d %H:%M:%S:%f')
tradeData = tradeData.reset_index()
tradeData.rename(columns={u'成交时间': 'tradetime'}, inplace=True)
tradeData = tradeData.set_index('tradetime')
tradeAll = tradeData.groupby(['Expiry', u'tradetime']).agg({'Delta': 'sum', 'Vega': 'sum'}).fillna(0)
tradeAll = pd.DataFrame(tradeAll, columns=['Delta', 'Vega']).unstack(level=0)
tradeAll.columns = flatten_multi_index(tradeAll.columns)
tradeAll[['deltaall', 'vegaall', 'PNL', 'TradePNL']] = tradeData.groupby([u'tradetime']).agg(
    {'Delta': 'sum', 'Vega': 'sum', 'PNL': 'last', 'TradePNL': 'last'}).fillna(0)
tradeAll = pd.concat(
    [tradeAll.between_time(tradetime00[0][0], tradetime00[0][1]),
     tradeAll.between_time(tradetime00[1][0], tradetime00[1][1])]).fillna(0)
trade0 = tradeAll.resample('1000l', label='right', closed='left').sum().fillna(0)
trade0[['PNL', 'TradePNL']] = tradeAll.resample('1000l', label='right', closed='left').agg(
    {'PNL': 'last', 'TradePNL': 'last'}).ffill()
trade0 = pd.concat(
    [trade0.between_time(tradetime00[0][0], tradetime00[0][1]),
     trade0.between_time(tradetime00[1][0], tradetime00[1][1])]).fillna(0)
trade0.to_csv(u'DATA/%s/output/%s' % (under, datetoday + 'trade2.csv'))

# ---------------------
print('load vol')
voldata = pd.read_csv(u'DATA/%s/%s' % (under, str1), parse_dates=[0], index_col=0,
                      names=['time', 'spot', 'code', 'tv', 'bid', 'ask', 'sigma', 'a.1', 'a.2', 'a.3', 'a.4', 'a.5',
                             'delta', 'vega', 'a.6', 'a.7', 'rf', 'br', 'time2expiry', 'K', 'Z', 'Call', 'exp', 'a.8',
                             'a.9', 'a.10', 'a.11', 'a.12'], header=None)
print('done load vol')

voldata = voldata.drop(['a.1', 'a.2', 'a.3', 'a.4', 'a.5', 'a.6', 'a.7', 'Call', 'a.8', 'a.9',
                        'a.10', 'a.11', 'a.12'], axis=1)
expVol = voldata['exp'].unique()
expTrade = sorted(list(set(expVol) & set(expTrade)))
data = voldata[voldata.delta > 0]

output = data.groupby(['exp']).apply(lambda z: z[z.K == atm])
output['forward'] = data.groupby(['exp', 'time']).apply(
    lambda z: z.spot.iloc[0] * np.exp(z.rf.iloc[0] * z.time2expiry.iloc[0] - z.br.iloc[0] * z.time2expiry.iloc[0]))
output['basis'] = output['forward'] - output['spot']
volCurveTimeline = output[['forward', 'basis', 'sigma', 'code']].unstack(0)
volCurveTimeline.columns = flatten_multi_index(volCurveTimeline.columns)
volCurveTimeline = pd.concat(
    [output[output['exp'] == output['exp'].iloc[0]].reset_index(level='exp', drop=True), volCurveTimeline], axis=1)

# ---------------------
volCurveTimeline['dt'] = volCurveTimeline.index
volCurveTimeline['dt'] = volCurveTimeline['dt'].diff(1)
volCurveTimeline['ds'] = volCurveTimeline.spot.diff(1)
volCurveTimeline['ds2'] = volCurveTimeline['ds'].shift(1)
for exp in expTrade:
    volCurveTimeline['dbasis_' + str(int(exp))] = volCurveTimeline['basis_' + str(int(exp))].diff(1)
    volCurveTimeline['dvol_' + str(int(exp))] = volCurveTimeline['sigma_' + str(int(exp))].diff(1)

volCurveTimeline = volCurveTimeline.resample('1000l').ffill()

volCurveTimeline = pd.concat([volCurveTimeline.between_time(tradetime00[0][0], tradetime00[0][1]),
                              volCurveTimeline.between_time(tradetime00[1][0], tradetime00[1][1])])
volCurveTimeline.to_csv(u'DATA/%s/output/%s' % (under, datetoday + 'vol2.csv'))

tradeMixVol = volCurveTimeline.merge(trade0, how='left', left_index=True, right_index=True)
tradeMixVol['dspnl'] = 0
tradeMixVol['basispnl'] = 0
tradeMixVol['vegapnl'] = 0
for exp in expTrade:
    tradeMixVol['dspnl' + str(int(exp))] = tradeMixVol['Delta_' + str(int(exp))] * tradeMixVol['ds'] / tradeMixVol[
        'spot']
    tradeMixVol['basispnl_' + str(int(exp))] = tradeMixVol['Delta_' + str(int(exp))] * tradeMixVol[
        'dbasis_' + str(int(exp))] / tradeMixVol['spot']
    tradeMixVol['vegapnl_' + str(int(exp))] = tradeMixVol['Vega_' + str(int(exp))] * tradeMixVol[
        'dvol_' + str(int(exp))] * 100

    tradeMixVol['dspnl'] += tradeMixVol['dspnl_' + str(int(exp))]
    tradeMixVol['basispnl'] += tradeMixVol['basispnl_' + str(int(exp))]
    tradeMixVol['vegapnl'] += tradeMixVol['vegapnl_' + str(int(exp))]

tradeMixVol = tradeMixVol.replace(np.nan, 0)
tradeMixVol.to_csv(u'DATA/%s/output/%s' % (under, datetoday + 'mix2.csv'))

# ---------------------
df3 = voldata.groupby(['code']).resample('1000l').ffill()
df3 = df3.drop('code', axis=1)
df3 = df3.reset_index()
df3 = df3.set_index('time')
df3 = pd.concat([df3.between_time(tradetime00[0][0], tradetime00[0][1]),
                 df3.between_time(tradetime00[1][0], tradetime00[1][1])])
df3 = df3.groupby('time')

o = 0
ii = 0
code = 0
optdata = []
time2 = []
volDataNow = []
tradeMixVol[
    ['edgePNL', 'adjPNL', 'DeltaPNL', 'VegaPNL', 'Rsd', 'TPNL']
] = 0
for i in df3:
    time1 = time2
    volDataBefore = volDataNow
    time2 = i[0]
    volDataNow = i[1].set_index('code')
    # print(time2)

    if o == 0:
        o += 1
        optdata = i[1].set_index('code')
        optdata[
            ['TPos', 'fwd', 'deltaWap', 'deltaBalance', 'basisWap', 'basisBalance', 'basis2Wap', 'basis2Balance',
             'vegaWap', 'vegaBalance', 'edgePNL', 'adjPNL', 'DeltaPNL', 'BasisPNL', 'Basis2PNL',
             'VegaPNL', 'Rsd', 'tBalance', 'TPNL']
        ] = 0
        continue

    for tradetime in tradeData[ii:].index:
        if tradetime < time1:
            ii += 1
            print(tradetime)
            print('before trading')
            continue
        try:
            if time1 <= tradetime < time2:
                trade = tradeData.iloc[ii]
                code = int(trade['Code'])
                print(code)

                if int(trade['Strike']*100)%10 == 0 :
                    multi=multi
                else:
                    multi=multi2

                volbefore = volDataBefore.loc[code]
                volnow = volDataNow.loc[code]
                tpos = trade[u'数量']
                optdata.loc[code, 'TPos'] += tpos

                fwd = volbefore.spot * np.exp(
                    volbefore.rf * volbefore.time2expiry - volbefore.br * volbefore.time2expiry)
                optdata.loc[code, 'deltaWap'] += volbefore[u'delta'] * tpos
                optdata.loc[code, 'deltaBalance'] += volbefore[u'delta'] * tpos * fwd

                optdata.loc[code, 'basisWap'] += volbefore[u'delta'] * tpos
                optdata.loc[code, 'basisBalance'] += volbefore[u'delta'] * tpos * (fwd - volbefore.spot)

                optdata.loc[code, 'basisWap'] += volbefore[u'delta'] * tpos
                optdata.loc[code, 'basisBalance'] += volbefore[u'delta'] * tpos * fwd

                optdata.loc[code, 'vegaWap'] += volbefore[u'vega'] * tpos
                optdata.loc[code, 'vegaBalance'] += volbefore[u'vega'] * tpos * volbefore[u'sigma']

                optdata.loc[code, 'edgePNL'] += (trade['AdjTv'] - trade[u'价格']) * tpos * multi
                optdata.loc[code, 'adjPNL'] += (volbefore['tv'] - trade['AdjTv']) * tpos * multi

                optdata.loc[code, 'tBalance'] += trade[u'价格'] * tpos

                ii += 1

            else:
                optdata.iloc[:, 0:12] = i[1].set_index('code').iloc[:, 0:12]
                optdata['fwd'] = optdata.spot * np.exp(
                    optdata.rf * optdata.time2expiry - optdata.br * optdata.time2expiry)
                optdata['DeltaPNL'] = (optdata['deltaWap'] * optdata['fwd'] - optdata['deltaBalance']) * multi
                optdata['BasisPNL'] = (optdata['basisWap'] * (optdata['fwd'] - optdata['spot']) - optdata[
                    'basisBalance']) * multi
                optdata['Basis2PNL'] = (optdata['basis2Wap'] * optdata['fwd'] - optdata['basis2Balance']) * multi
                optdata['VegaPNL'] = (optdata['vegaWap'] * optdata[u'sigma'] - optdata['vegaBalance']) * multi * 100
                optdata['TPNL'] = (optdata['TPos'] * optdata['tv'] - optdata['tBalance']) * multi
                optdata['Rsd'] = optdata['TPNL'] - optdata['edgePNL'] - optdata['adjPNL'] - optdata['DeltaPNL'] - \
                                 optdata['VegaPNL']

                tradeMixVol.loc[time2, 'edgePNL'] = optdata.loc[:, 'edgePNL'].sum()
                tradeMixVol.loc[time2, 'adjPNL'] = optdata.loc[:, 'adjPNL'].sum()
                tradeMixVol.loc[time2, 'DeltaPNL'] = optdata.loc[:, 'DeltaPNL'].sum()
                tradeMixVol.loc[time2, 'BasisPNL'] = optdata.loc[:, 'DeltaPNL'].sum()
                tradeMixVol.loc[time2, 'VegaPNL'] = optdata.loc[:, 'VegaPNL'].sum()
                tradeMixVol.loc[time2, 'Rsd'] = optdata.loc[:, 'Rsd'].sum()
                tradeMixVol.loc[time2, 'TPNL'] = optdata.loc[:, 'TPNL'].sum()
                break
        except Exception:
            print('error')
            print(time2)
            print(tradeData.iloc[ii].head(3))

tradeMixVol.to_csv(u'DATA/%s/output/%s' % (under, datetoday + 'mix3.xlsx'))
# tradeMixVol[['deltaall2', 'vegaall2']] = tradeMixVol[['deltaall', 'vegaall']].comsum(0)
# tradeMixVol[
#     ['time', 'spot', 'exp',
#      'forward', 'basis_%s' % expTrade[0], 'dbasis_%s' % expTrade[0], 'basis_%s' % expTrade[2],
#      'dbasis_%s' % expTrade[2], 'sigma_%s' % expTrade[0],
#      'sigma_%s' % expTrade[2], 'dvol_%s' % expTrade[0],'dvol_%s' % expTrade[0],'dt', 'ds', 'ddpnl',
#      'deltaall','delta_pnl', 'vegaall', 'dvpnl', 'vega_pnl', 'PNL', 'TradePNL', 'edgePNL', 'adjPNL', 'DeltaPNL', 'BasisPNL',
#      'VegaPNL','Rsd', 'TPNL']].to_csv(u'DATA/%s/output/%s' % (under, datetoday + 'mix4.xlsx'))

print('all done')
