# -*- coding:utf-8 -*-
import pyodbc
import pandas as pd
from pandas import DataFrame
import numpy as np
from WindPy import w
import datetime

import sys

reload(sys)
sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'

# Specifying the ODBC driver, server name, database, etc. directly
cnxn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)

# Create a cursor from the connection
# cursor = cnxn.cursor()

w.start()

futuredata = w.wsd("SR.CZC", "trade_hiscode", "2010-01-01", "2017-10-28", "PriceAdj=F")
df = DataFrame({"times": futuredata.Times, "codes": futuredata.Data[0]})

# sql = "SELECT  codes,exe_price,exe_date  FROM [Alex].[dbo].[SR2] "

# pf = pd.read_sql(sql, cnxn, index_col=["codes"], coerce_float=True, params=None, parse_dates=None,
#                 columns=None, chunksize=None)
sqllist2 = []

print(futuredata)
print(df)
codesdict = {}
for i in range(1, len(df)):
    if df.values[i][0] != df.values[i - 1][0]:
        codesdict[df.values[i][0]] = df.values[i - 1][1]

for k, v in codesdict.iteritems():
    date1 = v
    fcode = k
    wsddata1 = w.wsd(fcode, "open,high,low,close,volume,amt,dealnum,oi,lasttrade_date,dlmonth,lastdelivery_date",
                  "ED-100TD", date1, "PriceAdj=F")
    for i in range(0, len(wsddata1.Data[1])):
        sqllist = []

        sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))

        sqllist.append(str(fcode))

        for k in range(0, len(wsddata1.Fields)):
            sqllist.append(wsddata1.Data[k][i])

        sqllist[10] = sqllist[10].strftime('%Y-%m-%d')
        sqllist[12] = sqllist[12].strftime('%Y-%m-%d')
        sqllist.append(0)
        print(sqllist)

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)


# for index, row in df.iterrows():
#    if df.loc[index].values[0:-1] !=

def update():
    # codes=pf.index.values.tolist()
    for i in range(0, len(futuredata.Data[0])):
        date1 = futuredata.Times[i]
        fcode = futuredata.Data[0][i]
        wdata = w.wss(fcode, "open,high,low,close,volume,amt,dealnum,oi,lasttrade_date,dlmonth,lastdelivery_date",
                      "tradeDate=%s;priceAdj=U;cycle=D" % date1)

        sqllist = []

        sqllist.append(date1.strftime('%Y-%m-%d'))

        sqllist.append(str(fcode))

        for k in range(0, len(wdata.Fields)):
            sqllist.append(wdata.Data[k][0])

        sqllist[10] = sqllist[10].strftime('%Y-%m-%d')
        sqllist[12] = sqllist[12].strftime('%Y-%m-%d')
        sqllist.append(1)
        print(sqllist)

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)

update()

sql2 = "INSERT INTO [Alex].[dbo].[SR_Future] VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

cursor = cnxn.cursor()

cursor.execute("""
 IF OBJECT_ID('SR_Future', 'U') IS NOT NULL
    DROP TABLE SR_Future
 CREATE TABLE SR_Future (
    DateTime DATE NOT NULL,
    Code VARCHAR(20) NOT NULL,
    OpenPrice numeric(15, 2),
    High numeric(15, 2),
    Low numeric(15, 2),
    ClosePrice numeric(15, 2),
    Volume BIGINT,
    Amount numeric(15, 2),
    dealnum BIGINT,
    oi BIGINT,
    lasttrade_date DATE,
    dlmonth VARCHAR(20),
    lastdelivery_date DATE,
    is_prime VARCHAR(20)
    )
 """)

cursor.executemany(sql2, sqllist2)

cnxn.commit()

cnxn.close()
