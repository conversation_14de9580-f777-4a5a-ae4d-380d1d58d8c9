import os
import re
import time

import matplotlib.pyplot as plt
import pandas as pd
import datetime
import numpy as np
import matplotlib.pyplot as plt


def siltor(x, col):
    if x[col] < x['BidPrice5']:
        return -6
    elif x[col] < x['BidPrice4']:
        return -5
    elif x[col] < x['BidPrice3']:
        return -4
    elif x[col] < x['BidPrice2']:
        return -3
    elif x[col] < x['BidPrice1']:
        return -2
    elif x[col] == x['BidPrice1']:
        return -1
    elif x[col] < x['AskPrice1']:
        return 0
    elif x[col] < x['AskPrice2']:
        return 1
    elif x[col] < x['AskPrice3']:
        return 2
    elif x[col] < x['AskPrice4']:
        return 3
    elif x[col] < x['AskPrice5']:
        return 4
    elif x[col] == x['AskPrice5']:
        return 5
    elif x[col] > x['AskPrice5']:
        return 6
    else:
        return 100


# mdmix1['bid'] = mdmix1.apply(
#     lambda x: 6 if x['price_x'] < x['BidPrice5']
#     else 5 if x['price_x'] < x['BidPrice4']
#     else 4 if x['price_x'] < x['BidPrice3']
#     else 3 if x['price_x'] < x['BidPrice2']
#     else 2 if x['price_x'] < x['BidPrice1']
#     else 1 if x['price_x'] == x['BidPrice1']
#     else 0 if x['price_x'] < x['AskPrice1']
# else -1 if x['price_x'] < x['AskPrice2']
# else -2 if x['price_x'] < x['AskPrice3']
# else -3 if x['price_x'] < x['AskPrice4']
# else -4 if x['price_x'] < x['AskPrice5']
# else -5 if x['price_x'] == x['AskPrice5']
# else -6 if x['price_x'] > x['AskPrice5']
#     else 100, axis=1)

def eachFile(filepath):
    pathDir = os.listdir(filepath)
    file = []
    dates = []
    for allDir in pathDir:
        file.append(os.path.join('%s\\%s' % (filepath, allDir)))
        try:
            dates.append(re.findall(r'20\d{6}', allDir)[0])
        except:
            continue
    dates = list(set(dates))
    dates.sort()
    return dates


def main(add):
    dates = eachFile(dirs)
    if add == 1:
        dates2 = eachFile(outdirs)
        dates = list(set(dates) - set(dates2))
        dates.sort()
    print(dates[0] + '--' + dates[-1])
    print(str(len(dates)) + ' dates')
    for datetoday in dates:
        0

    # cor.to_csv(u'DATA/%s/output/%s' % (under, dates[-1] + 'cor.csv'))
    print(dates[0] + '--' + dates[-1])
    print('success')


def flatten_multi_index(multi_index, join_str='_'):
    label0 = multi_index.get_level_values(0)
    label1 = multi_index.get_level_values(1)
    index = [str(i) + join_str + str(int(j)) for i, j in zip(label0, label1)]
    return pd.Index(index)


datetoday = '20230713'
under = 'SH300'
str1 = u'vols_%s.csv' % datetoday
str2 = u'TradeRecords%s.csv' % datetoday
str3 = u'md_%s_unicast.csv' % datetoday
fut = 'IF2307'
optcodes = [90005422]
futfreq = '500l'
volfreq = '1000l'
atm = 4.0
multi = 10000
multi2 = 10148

tradetime00 = [['09:30:00', '11:30:00'], ['13:00:00', '14:56:00']]
dirs = 'DATA\\%s\\' % under
outdirs = '%s\\' % under + 'output3'

str4 = u'%s_OrderDetails.csv' % datetoday
str5 = u'md_%s_sse_mdgw.csv' % datetoday

longdir = ['OpenLong', 'CloseShort']
shortdir = ['OpenShort', 'CloseLong']

print('load orders')
md_reader = pd.read_csv(dirs + str4, names=None, header=0, chunksize=1000000, low_memory=False)
size = 1
mdData1 = pd.DataFrame()
for chunk in md_reader:
    mdData1 = pd.concat([mdData1, chunk])
    size += 1
    print(size)

mdData1 = mdData1[mdData1['symbol'].isin(optcodes)]
mdData1['updateTime'] = pd.to_datetime(datetoday + ' ' + mdData1['updateTime'].str.replace('.', ':'),
                                       format='%Y%m%d %H:%M:%S:%f')
mdData1['insertTime'] = pd.to_datetime(datetoday + ' ' + mdData1['insertTime'].str.replace('.', ':'),
                                       format='%Y%m%d %H:%M:%S:%f')

mdData1 = pd.merge_asof(mdData1[(mdData1['rspSrc'] == 2)],
                        mdData1[(mdData1['rspSrc'] == 1)][['updateTime', 'OrderId']], on='updateTime',
                        by='OrderId',
                        direction='nearest',
                        tolerance=pd.Timedelta('100ms'), allow_exact_matches=True)

mdlong121 = mdData1[mdData1['directionStr'].isin(longdir)]
mdshort121 = mdData1[mdData1['directionStr'].isin(shortdir)]

mdData1 = pd.merge_asof(mdlong121,
                        mdshort121[['price', 'updateTime']],
                        on='updateTime', direction='nearest',
                        tolerance=pd.Timedelta('20ms'), allow_exact_matches=True)

mdData1.index = mdData1['updateTime']
print('md12 done')
mdData1 = mdData1.sort_index()

md121 = mdData1[(mdData1['stateStr'] == 'Accepted')]
md122 = mdData1[(mdData1['stateStr'] == 'FullCanceled')]

# md123 = pd.concat([md121, md122])

print('load md2')
md_reader = pd.read_csv(dirs + str5, names=None, header=0, chunksize=500000, low_memory=False)
size = 1
mdData2 = pd.DataFrame()
for chunk in md_reader:
    mdData2 = pd.concat([mdData2, chunk])
    size += 1
    print(size)
mdData2['timestamp_str'] = pd.to_datetime(datetoday + ' ' + mdData2['timestamp_str'], format='%Y%m%d %H:%M:%S.%f')
mdData2.index = mdData2['timestamp_str']
mdData2 = mdData2[[
    'Symbol', 'exchange_time_str', 'timestamp_str', 'Volume',
    'BidPrice5', 'BidPrice4', 'BidPrice3', 'BidPrice2', 'BidPrice1',
    'AskPrice1', 'AskPrice2', 'AskPrice3', 'AskPrice4', 'AskPrice5',
    'BidVol5', 'BidVol4', 'BidVol3', 'BidVol2', 'BidVol1',
    'AskVol1', 'AskVol2', 'AskVol3', 'AskVol4', 'AskVol5',
    'LastPrice'
]]
print('md2 done')
mdData2 = mdData2.sort_index()

# mdData2['Symbol'] = mdData2['Symbol'].apply(str)
md21 = mdData2[mdData2['Symbol'].isin(optcodes)]
md21 = pd.concat([md21.between_time(tradetime00[0][0], tradetime00[0][1]),
                  md21.between_time(tradetime00[1][0], tradetime00[1][1])])

mdmix1 = pd.merge_asof(md21, md121,
                       left_index=True, right_index=True, direction='backward',
                       tolerance=pd.Timedelta('10000ms'), allow_exact_matches=True)

mdmix2 = pd.merge_asof(md21, md121,
                       left_index=True, right_index=True, direction='forward',
                       tolerance=pd.Timedelta('500ms'), allow_exact_matches=True)

mdmix1['bid1'] = mdmix1.apply(siltor, axis=1, args=('price_x',))
print(1)
mdmix1['ask1'] = mdmix1.apply(siltor, axis=1, args=('price_y',))
print(1)
mdmix2['bid2'] = mdmix2.apply(siltor, axis=1, args=('price_x',))
print(1)
mdmix2['ask2'] = mdmix2.apply(siltor, axis=1, args=('price_y',))
print(1)
mdmix1['lastseq1'] = mdmix1.apply(siltor, axis=1, args=('LastPrice',))
print(1)

mdmix1['mkttrd1'] = mdmix1['Volume'].diff(1)
mdmix2['mkttrd2'] = mdmix2['Volume'].diff(1)

mdmix1['own_lasttrd1'] = mdmix1.apply(lambda x: -1 if x['price_x'] >= x['LastPrice']
else 0 if (x['price_x'] < x['LastPrice']) & (x['price_y'] > x['LastPrice'])
else 1 if x['price_y'] <= x['LastPrice']
else 100, axis=1)
# mdmix2['lasttrd2'] = mdmix2.apply(lambda x: -1 if x['price_x'] >= x['LastPrice']
# else 0 if (x['price_x'] < x['LastPrice']) & (x['price_y'] > x['LastPrice'])
# else 1 if x['price_y'] <= x['LastPrice']
# else 100, axis=1)

mdmix1 = pd.merge_asof(mdmix1,
                       mdmix2[['bid2', 'ask2', 'mkttrd2', 'exchange_time_str']],
                       left_index=True, right_index=True, direction='nearest',
                       tolerance=pd.Timedelta('10ms'), allow_exact_matches=True)

mdmix1 = pd.merge_asof(mdmix1,
                       md122[['updateTime', 'stateStr']],
                       left_index=True, right_index=True, direction='backward',
                       tolerance=pd.Timedelta('1000000000ms'), allow_exact_matches=True)

mdmix1['order_time'] = (mdmix1['updateTime_x'] - mdmix1['insertTime']) / np.timedelta64(1, 's')
mdmix1['order_md_time'] = (mdmix1['updateTime_x'] - mdmix1['timestamp_str']) / np.timedelta64(1, 's')
mdmix1['cancel_mkt_t'] = (mdmix1['timestamp_str'] - mdmix1['updateTime_y']) / np.timedelta64(1, 's')
mdmix1['cancel_trade'] = mdmix1.apply(
    lambda x: 0 if ((x['cancel_mkt_t'] >= 0.5) & (x['updateTime_x'] < x['updateTime_y'])) else 1, axis=1)
mdmix1['cancel_order'] = mdmix1.apply(lambda x: 0 if x['updateTime_x'] < x['updateTime_y'] else 1, axis=1)
# mdmix1['cancel_order2'] = mdmix1.apply(lambda x: 0 if x['updateTime_x'] > x['updateTime_y'] else 1, axis=1)

mdmix1['bid1'] = mdmix1['bid1'] * mdmix1['cancel_order']
mdmix1['ask1'] = mdmix1['ask1'] * mdmix1['cancel_order']
# mdmix1['bid2'] = mdmix1['bid2'] * mdmix1['cancel_order']
# mdmix1['ask2'] = mdmix1['ask2'] * mdmix1['cancel_order']
# mdmix1['lastseq1']=mdmix1['lastseq1']*mdmix1['cancel_trade']
mdmix1['own_lasttrd1'] = mdmix1['own_lasttrd1'] * mdmix1['cancel_trade']

mdmix1.to_excel(u'DATA/%s/output/%s' % (under, datetoday + 'ordermdmix_%s.xlsx' % optcodes[0]))
# mdmix1.to_excel(u'DATA/%s/output/%s' % (under, datetoday + 'ordermdmix2.xlsx'))

print('done')

print('load md')
mdData = pd.read_csv(u'DATA/%s/%s' % (under, str3), names=None, header=0)
mdData = mdData[mdData['Symbol'] == fut].drop_duplicates(subset=['timestamp_str', 'Symbol'])
mdData['futtime'] = pd.to_datetime(datetoday + ' ' + mdData['timestamp_str'], format='%Y%m%d %H:%M:%S.%f')
mdData.index = mdData['futtime']
mdData = mdData.sort_index()
mdData['fut'] = mdData['AskPrice1'] / 2 + mdData['BidPrice1'] / 2

print('md done')

mdData['dsf_10'] = -mdData['fut'].diff(10)
mdData['dsf_4'] = -mdData['fut'].diff(4)
mdData['dsf_2'] = -mdData['fut'].diff(2)

mdData['dsf0'] = mdData['fut'].diff(1)
mdData['dsf1'] = -mdData['fut'].diff(-1)
mdData['dsf2'] = -mdData['fut'].diff(-2)
mdData['dsf4'] = -mdData['fut'].diff(-4)
mdData['dsf10'] = -mdData['fut'].diff(-10)

print('load trade')
tradeData = pd.read_csv(u'DATA/%s/%s' % (under, str2), parse_dates=[1, 2], index_col=2, names=None, header=0,
                        encoding='utf-16', sep='\t')
print('done load trade')
expTrade = tradeData['Expiry'].unique()
tradeData.index = pd.to_datetime(datetoday + ' ' + tradeData.index, format='%Y%m%d %H:%M:%S:%f')
tradeData['委托时间'] = pd.to_datetime(datetoday + ' ' + tradeData['委托时间'], format='%Y%m%d %H:%M:%S:%f')
tradeData = tradeData.reset_index()
tradeData.rename(columns={u'成交时间': 'tradetime'}, inplace=True)
tradeData = tradeData.set_index('tradetime')
tradeData = tradeData.sort_index().dropna(how='all')
tradeData[u'multi'] = tradeData['Strike'].apply(lambda x: multi if int(x * 100) % 10 == 0 else multi2)
tradeData['edgepnl'] = (tradeData['Tv'] - tradeData[u'价格']) * tradeData[u'数量'] * tradeData[u'multi']

tradeData = pd.merge_asof(tradeData, mdData[['fut', 'timestamp_str']], left_index=True, right_index=True,
                          direction='backward',
                          tolerance=pd.Timedelta('1000000000ms'), allow_exact_matches=True)

tradeData = tradeData.reset_index()
print('mixorder')
ordertrade = tradeData.groupby(['OrderId'], as_index=False).agg(
    {'OrderId': 'last', u'数量': 'sum', u'Delta': 'sum', u'Vega': 'sum', u'tradetime': 'last'}) \
    .fillna(0).sort_values(u'tradetime')
ordertrade = pd.merge_asof(ordertrade,
                           tradeData, on=u'tradetime',
                           by=u'OrderId',
                           direction='nearest',
                           tolerance=pd.Timedelta('100ms'), allow_exact_matches=True)

mdmix1['OrderId'] = mdmix1['OrderId'].fillna(0).astype('int64')
mdmixorder = pd.merge(mdmix1,
                      ordertrade.sort_values('OrderId'),
                      on='OrderId')

mdmixorder.index = mdmixorder['insertTime']
mdmixorder = mdmixorder.sort_index()

mdmixorder = pd.merge_asof(mdmixorder, mdData[
    ['fut', 'timestamp_str', 'dsf0', 'dsf1', 'dsf2', 'dsf4', 'dsf10']],
                           left_index=True, right_index=True, direction='backward',
                           tolerance=pd.Timedelta('500ms'), allow_exact_matches=True)


mdmixorder.to_excel(u'DATA/%s/output/%s' % (under, datetoday + 'mdmixorder.xlsx'))

print('all done')
