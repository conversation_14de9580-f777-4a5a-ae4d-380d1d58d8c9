import time
from datetime import datetime
import numpy as np
from utils.utils import log_print
from core import config
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV, RandomizedSearchCV
from skopt import BayesSearchCV
from skopt.space import Real, Categorical, Integer
from skopt.plots import plot_convergence, plot_objective
import matplotlib.pyplot as plt

def _get_param_space(method='grid'):
    """
    获取参数空间配置

    参数:
        method: 搜索方法

    返回:
        dict: 参数空间配置
    """
    if method in ['grid', 'random']:
        return {
            'n_estimators': (10, 1000),
            'learning_rate': (0.01, 1.0),
            'max_depth': (3, 10),
            'subsample': (0.5, 1.0),
            'colsample_bytree': (0.5, 1.0),
            'gamma': (0.0, 1.0),
            'reg_alpha': (0.0, 1.0),
            'reg_lambda': (0.0, 1.0),
            'min_child_weight': (1, 10)
        }
    elif method == 'bayesian':
        from skopt.space import Real, Integer
        return {
            'n_estimators': Integer(10, 1000),
            'learning_rate': Real(0.01, 1.0, prior='log-uniform'),
            'max_depth': Integer(3, 10),
            'subsample': Real(0.5, 1.0),
            'colsample_bytree': Real(0.5, 1.0),
            'gamma': Real(0.0, 1.0),
            'reg_alpha': Real(0.0, 1.0),
            'reg_lambda': Real(0.0, 1.0),
            'min_child_weight': Integer(1, 10)
        }
    elif method == 'optuna':
        return {
            'n_estimators': (10, 1000),
            'learning_rate': (0.01, 1.0),
            'max_depth': (3, 10),
            'subsample': (0.5, 1.0),
            'colsample_bytree': (0.5, 1.0),
            'gamma': (0.0, 1.0),
            'reg_alpha': (0.0, 1.0),
            'reg_lambda': (0.0, 1.0),
            'min_child_weight': (1, 10)
        }
    elif method == 'hyperopt':
        from hyperopt import hp
        import numpy as np
        return {
            'n_estimators': hp.choice('n_estimators', range(10, 1001)),
            'learning_rate': hp.loguniform('learning_rate', np.log(0.01), np.log(1.0)),
            'max_depth': hp.choice('max_depth', range(3, 11)),
            'subsample': hp.uniform('subsample', 0.5, 1.0),
            'colsample_bytree': hp.uniform('colsample_bytree', 0.5, 1.0),
            'gamma': hp.uniform('gamma', 0.0, 1.0),
            'reg_alpha': hp.uniform('reg_alpha', 0.0, 1.0),
            'reg_lambda': hp.uniform('reg_lambda', 0.0, 1.0),
            'min_child_weight': hp.choice('min_child_weight', range(1, 11))
        }
    return None

def _create_visualization(method, search_results, save_path):
    """
    创建优化过程可视化

    参数:
        method: 搜索方法
        search_results: 搜索结果
        save_path: 保存路径

    返回:
        str: 可视化文件路径
    """
    import matplotlib.pyplot as plt
    from skopt.plots import plot_convergence, plot_objective

    try:
        fig, (ax1, ax2) = plt.subplots(1, 2)

        if method == 'optuna':
            import optuna
            optuna.visualization.matplotlib.plot_optimization_history(search_results['study'], ax=ax1)
            optuna.visualization.matplotlib.plot_param_importances(search_results['study'], ax=ax2)
        elif method == 'hyperopt':
            losses = [t['result']['loss'] for t in search_results['trials'].trials]
            ax1.plot(losses)
            ax1.set_title('优化历史')
            ax1.set_xlabel('试验次数')
            ax1.set_ylabel('损失值')
        elif method == 'bayesian':
            plot_convergence(search_results['optimizer'].optimizer_results_[0], ax=ax1)
            plot_objective(search_results['optimizer'].optimizer_results_[0], ax=ax2)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        return save_path
    except Exception as e:
        log_print(f"生成可视化时出错: {str(e)}", "warning")
        return None

def parameter_search(x_train, y_train, model, method='grid', n_trials=50, cv=3, 
                    visualization=True, save_path=config.OUTDIR, param_grid=None):
    """
    统一的超参数搜索方法，支持多种优化算法

    参数:
        x_train: 训练特征数据
        y_train: 训练目标数据
        method: 搜索方法，可选值：
            - 'grid': 网格搜索 (GridSearchCV)
            - 'random': 随机搜索 (RandomizedSearchCV)
            - 'bayesian': 贝叶斯优化 (BayesSearchCV)
            - 'optuna': Optuna优化
            - 'hyperopt': Hyperopt优化
            - 'tpot': TPOT自动化机器学习
        n_trials: 优化试验次数
        cv: 交叉验证折数
        visualization: 是否生成可视化图表
        save_path: 可视化结果保存路径
        param_grid: 参数网格（仅用于grid和random方法）

    返回:
        dict: 包含优化结果的字典
    """
    from sklearn.model_selection import GridSearchCV, RandomizedSearchCV, TimeSeriesSplit
    from skopt import BayesSearchCV
    import optuna
    from hyperopt import fmin, tpe, STATUS_OK, Trials
    from tpot import TPOTRegressor

    # 获取参数空间
    param_space = param_grid if param_grid is not None else _get_param_space(method)
    if param_space is None:
        raise ValueError(f"不支持的优化方法: {method}")

    # 使用 TimeSeriesSplit 进行时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=cv)

    results = {
        'best_model': None,
        'best_params': None,
        'best_score': None,
        'optimization_history': [],
        'visualization_path': None
    }

    try:
        if method == 'grid':
            # 网格搜索
            param_grid = {k: [v[0], v[1]] for k, v in param_space.items()}
            search = GridSearchCV(
                estimator=model,
                param_grid=param_grid,
                cv=tscv,
                scoring='r2',
                n_jobs=-1,
                verbose=1
            )
            
        elif method == 'random':
            # 随机搜索
            search = RandomizedSearchCV(
                estimator=model,
                param_distributions=param_space,
                n_iter=n_trials,
                cv=tscv,
                scoring='r2',
                n_jobs=-1,
                random_state=42
            )
            
        elif method == 'bayesian':
            # 贝叶斯优化
            search = BayesSearchCV(
                estimator=model,
                search_spaces=param_space,
                n_iter=n_trials,
                cv=tscv,
                scoring='r2',
                n_jobs=-1,
                verbose=1,
                random_state=42
            )
            
        elif method == 'optuna':
            # Optuna优化
            def objective(trial):
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', *param_space['n_estimators']),
                    'learning_rate': trial.suggest_float('learning_rate', *param_space['learning_rate'], log=True),
                    'max_depth': trial.suggest_int('max_depth', *param_space['max_depth']),
                    'subsample': trial.suggest_float('subsample', *param_space['subsample']),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', *param_space['colsample_bytree']),
                    'gamma': trial.suggest_float('gamma', *param_space['gamma']),
                    'reg_alpha': trial.suggest_float('reg_alpha', *param_space['reg_alpha']),
                    'reg_lambda': trial.suggest_float('reg_lambda', *param_space['reg_lambda']),
                    'min_child_weight': trial.suggest_int('min_child_weight', *param_space['min_child_weight'])
                }
                
                model.set_params(**params)
                scores = []
                for train_idx, val_idx in tscv.split(x_train):
                    X_train_fold = x_train.iloc[train_idx]
                    y_train_fold = y_train.iloc[train_idx]
                    X_val_fold = x_train.iloc[val_idx]
                    y_val_fold = y_train.iloc[val_idx]
                    
                    model.fit(X_train_fold, y_train_fold)
                    score = model.score(X_val_fold, y_val_fold)
                    scores.append(score)
                
                return np.mean(scores)

            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=n_trials)
            
            results['best_params'] = study.best_params
            results['best_score'] = study.best_value
            model.set_params(**study.best_params)
            results['best_model'] = model
            results['study'] = study
            
        elif method == 'hyperopt':
            # Hyperopt优化
            def objective(space):
                model.set_params(**space)
                scores = []
                for train_idx, val_idx in tscv.split(x_train):
                    X_train_fold = x_train.iloc[train_idx]
                    y_train_fold = y_train.iloc[train_idx]
                    X_val_fold = x_train.iloc[val_idx]
                    y_val_fold = y_train.iloc[val_idx]
                    
                    model.fit(X_train_fold, y_train_fold)
                    score = model.score(X_val_fold, y_val_fold)
                    scores.append(score)
                
                return {'loss': -np.mean(scores), 'status': STATUS_OK}

            trials = Trials()
            best = fmin(fn=objective, space=param_space, algo=tpe.suggest, 
                        max_evals=n_trials, trials=trials)
            
            results['best_params'] = best
            model.set_params(**best)
            results['best_model'] = model
            results['best_score'] = -trials.best_trial['result']['loss']
            results['trials'] = trials
            
        elif method == 'tpot':
            # TPOT自动化机器学习
            tpot = TPOTRegressor(
                generations=5,
                population_size=20,
                cv=tscv,
                scoring='r2',
                verbosity=2,
                random_state=42,
                n_jobs=-1
            )
            
            log_print("开始TPOT优化...", "info")
            tpot.fit(x_train, y_train)
            
            results['best_model'] = tpot.fitted_pipeline_
            results['best_params'] = tpot.fitted_pipeline_.get_params()
            results['best_score'] = tpot.score(x_train, y_train)

        # 执行搜索（对于非TPOT方法）
        if method not in ['optuna', 'hyperopt', 'tpot']:
            log_print(f"开始{method}搜索...", "info")
            start_time = time.time()
            search.fit(x_train, y_train)
            search_time = time.time() - start_time
            log_print(f"搜索完成，耗时: {search_time:.2f}秒", "info")
            
            results['best_model'] = search.best_estimator_
            results['best_params'] = search.best_params_
            results['best_score'] = search.best_score_
            if method == 'bayesian':
                results['optimizer'] = search

        # 生成可视化
        if visualization and method in ['optuna', 'hyperopt', 'bayesian']:
            if save_path is None:
                save_path = f"{config.OUTDIR}/plots/paras_search_{method}_{datetime.now().strftime('%Y%m%d_%H')}.png"
            results['visualization_path'] = _create_visualization(method, results, save_path)

        # 输出最佳结果
        log_print(f"最佳参数: {results['best_params']}", "info")
        log_print(f"最佳得分: {results['best_score']:.4f}", "info")

    except Exception as e:
        log_print(f"参数搜索过程中出错: {str(e)}", "error")
        return None

    return results


def model_parameter_search(
    X_train_sd, y_train, model, param_grid, n_splits=3
):
    """
    训练模型并进行参数搜索。

    参数:
        X_train_sd: 训练特征数据
        y_train: 训练目标数据
        model: 初始模型实例
        param_grid: 参数网格
        n_splits: 交叉验证折数，默认为3
                    - 较小的折数(如3折)：训练速度更快，但可能评估不够全面
                    - 较大的折数(如5折)：评估更全面，但训练时间更长
                    - 对于时间序列数据，通常3-5折为宜，视数据量大小而定

    返回:
        model: 使用最佳参数的模型
    """
    # 使用 TimeSeriesSplit 进行时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=n_splits)  # 时间序列分割

    # 可选评分指标
    scoring_metrics = {
        "r2": "r2",  # R²分数
        "neg_mse": "neg_mean_squared_error",  # 负均方误差
        "neg_rmse": "neg_root_mean_squared_error",  # 负均方根误差
        "neg_mae": "neg_mean_absolute_error",  # 负平均绝对误差
    }

    # 默认使用R²作为评分指标
    scoring = scoring_metrics["r2"]

    # 日志输出参数搜索信息
    log_print(f"开始参数搜索，模型类型: {type(model).__name__}", "info")
    log_print(f"参数网格: {param_grid}", "debug")
    log_print(f"评分指标: {scoring}", "debug")
    log_print(f"交叉验证: 时间序列分割(n_splits={n_splits})", "debug")

    # 配置GridSearchCV，添加n_jobs=-1启用并行计算加速搜索
    grid_search = GridSearchCV(
        estimator=model,
        param_grid=param_grid,
        cv=tscv,
        scoring=scoring,
        n_jobs=-1,
        return_train_score=True,  # 同时返回训练集得分
        verbose=1,  # 输出进度 [0,1,2,3]
    )

    # 执行网格搜索
    log_print("开始执行参数网格搜索...", "info")
    start_time = time.time()
    grid_search.fit(X_train_sd, y_train)
    search_time = time.time() - start_time
    log_print(f"参数搜索完成，耗时: {search_time:.2f}秒", "info")

    try:
        # 输出搜索结果
        best_score = grid_search.best_score_
        best_params = grid_search.best_params_
        log_print(f"最佳参数: {best_params}", "info")
        log_print(f"最佳得分: {best_score:.4f}", "info")

        # 输出前3名的参数组合
        cv_results = grid_search.cv_results_
        top_indices = np.argsort(-cv_results["mean_test_score"])[:3]

        log_print("前3名参数组合:", "info")
        for i, idx in enumerate(top_indices):
            mean_score = cv_results["mean_test_score"][idx]
            std_score = cv_results["std_test_score"][idx]
            params = cv_results["params"][idx]
            log_print(
                f"#{i + 1} 得分: {mean_score:.4f} (±{std_score:.4f}), 参数: {params}",
                "info",
            )

        # 使用最佳参数的模型
        model = grid_search.best_estimator_
    except Exception as e:
        log_print(f"获取最佳参数时出错: {e}", "error")
        log_print("使用原始模型参数", "warning")
        return model

    return model


def bayesian_optimization_search(x_train, y_train, model, model_type=config.MODEL_TYPE, n_iter=50, cv=3, 
                                visualization=True,  param_grid=None,method='bayesian'):
    """
    使用贝叶斯优化进行超参数搜索，支持可视化分析

    参数:
        x_train: 训练特征数据
        y_train: 训练目标数据
        model_type: 模型类型，如果为None则使用当前模型
        n_iter: 优化迭代次数
        cv: 交叉验证折数
        visualization: 是否生成可视化图表
        save_path: 可视化结果保存路径

    返回:
        dict: 包含优化结果的字典，包括：
            - best_model: 最佳模型
            - best_params: 最佳参数
            - best_score: 最佳得分
            - optimization_history: 优化历史
            - visualization_path: 可视化文件路径（如果生成）
    """

    # 定义参数空间
    if model_type == 'lightgbm':
        param_space = {
            'n_estimators': Integer(10, 1000),
            'learning_rate': Real(0.01, 1.0, prior='log-uniform'),
        'max_depth': Integer(3, 10),
        'subsample': Real(0.5, 1.0),
        'colsample_bytree': Real(0.5, 1.0),
        'gamma': Real(0.0, 1.0),
        'reg_alpha': Real(0.0, 1.0),
        'reg_lambda': Real(0.0, 1.0),
            'min_child_weight': Integer(1, 10)
        }
    elif model_type == 'xgboost':
        param_space = {
            'n_estimators': Integer(10, 1000),
            'learning_rate': Real(0.01, 1.0, prior='log-uniform'),
            'max_depth': Integer(3, 10),
            'subsample': Real(0.5, 1.0),
            'colsample_bytree': Real(0.5, 1.0),
            'gamma': Real(0.0, 1.0),
            'reg_alpha': Real(0.0, 1.0),
            'reg_lambda': Real(0.0, 1.0),
            'min_child_weight': Integer(1, 10)
        }

    # 使用 TimeSeriesSplit 进行时间序列交叉验证
    tscv = TimeSeriesSplit(n_splits=cv)

    # 配置贝叶斯优化
    bayes_search = BayesSearchCV(
        estimator=model,
        search_spaces=param_space,
        n_iter=n_iter,
        cv=tscv,
        scoring='r2',
        n_jobs=-1,
        verbose=1,
        random_state=42
    )

    # 执行贝叶斯优化
    log_print("开始贝叶斯优化参数搜索...", "info")
    start_time = time.time()
    bayes_search.fit(x_train, y_train)
    search_time = time.time() - start_time
    log_print(f"贝叶斯优化完成，耗时: {search_time:.2f}秒", "info")

    # 收集优化结果
    results = {
        'best_model': bayes_search.best_estimator_,
        'best_params': bayes_search.best_params_,
        'best_score': bayes_search.best_score_,
        'optimization_history': []
    }

    # 记录优化历史
    for i, (params, score) in enumerate(zip(bayes_search.cv_results_['params'], 
                                            bayes_search.cv_results_['mean_test_score'])):
        results['optimization_history'].append({
            'iteration': i + 1,
            'params': params,
            'score': score
        })
        log_print(f"迭代 {i+1}: 得分 = {score:.4f}, 参数 = {params}", "debug")

    # 生成可视化
    if visualization:
        try:
            # 创建图形
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

            # 绘制收敛曲线
            plot_convergence(bayes_search.optimizer_results_[0], ax=ax1)
            ax1.set_title('优化收敛曲线')

            # 绘制目标函数
            plot_objective(bayes_search.optimizer_results_[0], ax=ax2)
            ax2.set_title('目标函数分布')

            # 保存图形
            save_path = f"{config.OUTDIR}/models/optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            
            plt.tight_layout()
            plt.savefig(save_path)
            plt.close()

            results['visualization_path'] = save_path
            log_print(f"优化可视化结果已保存至: {save_path}", "info")

        except Exception as e:
            log_print(f"生成可视化时出错: {str(e)}", "warning")
            results['visualization_path'] = None

    # 输出最佳结果
    log_print(f"最佳参数: {results['best_params']}", "info")
    log_print(f"最佳得分: {results['best_score']:.4f}", "info")

    return results