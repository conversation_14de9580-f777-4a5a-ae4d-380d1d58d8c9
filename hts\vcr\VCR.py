import pandas as pd
# Author : gp
# Date : 20230808

import requests
import websocket
import json
import threading
import time
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from datetime import timedelta
import datetime


class md_config:
    #    ws_addr = 'ws://160.14.228.64:5557'                 # 开发环境地址
    #    http_addr = 'http://160.14.228.64:5555'
    # ws_addr = 'ws://168.231.2.88:22919'  # 生产环境地址
    # http_addr = 'http://168.231.2.88:22918'
    ws_addr = 'ws://168.231.2.88:22929'              # 生产环境地址
    http_addr = 'http://168.231.2.88:22928'
    user_name = 'ln'
    password = 'ln'
    msg_name = 'time_curve_minute'
    filter_key = 'curve_name'
    filter_value_list = '510300_20241105_atmvol'
    token = ''


def create_on_open(underlying):
    def on_open(ws):
        # 发送认证请求
        init_str = json.loads('{"type":"token_sub","data":{"token":0,"disable_sub_all":1}}')
        init_str["data"]["token"] = md_config.token;
        print("send init req : ", init_str)
        ws.send(json.dumps(init_str))
        # 发送订阅请求
        subscribe_json = json.loads(
            '{"seqno":1,"type":"table_action",'
            '"data":{"msg_name":"","action":"sub","interval_ms":0,"disable_snap":0,"filter_key":"","filter_value_list":""}'
            '}')
        subscribe_json["seqno"] = 1
        subscribe_json["data"]["msg_name"] = md_config.msg_name
        subscribe_json["data"]["filter_key"] = md_config.filter_key
        subscribe_json["data"]["filter_value_list"] = md_config.filter_value_list
        print("send subscribe req : ", subscribe_json)
        ws.send(json.dumps(subscribe_json))

    return on_open


def on_message(ws, message):
    print("Received message:", message)
    json_data = json.loads(message)
    if (json_data['type'] == 'time_curve_minute'):
        md_json = json_data['data']
        print("receive data ", md_json)


def on_error(ws, error):
    print("Error:", error)


def on_close(ws):
    print("Connection closed")


def monitor_connection(ws, interval=30):
    global last_message_time
    while True:
        time.sleep(1)
        if time.time() - last_message_time > interval:
            ws.close()
            break


def main(underlying, date):
    # 需要保存的数组
    vol_list = []
    price_list = []
    time_list = []
    global last_message_time
    date = pd.to_datetime(date)
    timestamp = int(time.mktime(date.timetuple()) * 1000)

    # print(timestamp,11111)
    def on_message(ws, message):
        print("Received message:", message)
        global last_message_time
        last_message_time = time.time()
        json_data = json.loads(message)
        if json_data['type'] == 'time_curve_minute':
            md_json = json_data['data']
            print("receive data ", md_json, 1111)

        # Assuming md_json is a dictionary and you want to check some conditions
        if float(md_json["timestamp"]) >= timestamp:
            # print(md_json["timestamp"],22)
            if md_json["curve_name"] == underlying + "_forwardprice" and md_json["value"] != "NaN":
                price_list.append(md_json['value'])
                # print(price_list)
            if md_json["curve_name"] == underlying + "_atmvol" and md_json["value"] != "NaN":
                vol_list.append(md_json['value'])

                time_list.append(md_json["timestamp"])

    # 发送http登陆请求
    url = md_config.http_addr + "/auth/login"
    login_req_json = json.loads('{"type":"login_req","data":{"user_name":"","password":""}}')
    login_req_json["data"]["user_name"] = md_config.user_name
    login_req_json["data"]["password"] = md_config.password
    r = requests.post(url, json=login_req_json)
    for cookie in r.cookies.keys():
        if cookie == "token":
            md_config.token = r.cookies.get(cookie)
    if (md_config.token == ''):
        print("ERROR get token ", r.text, r.cookies)
        return
    print("token ", md_config.token, r.text, r.cookies)

    ws = websocket.WebSocketApp(md_config.ws_addr, on_message=on_message, on_error=on_error, on_close=on_close)
    my_on_open = create_on_open(underlying)
    ws.on_open = my_on_open
    monitor_thread = threading.Thread(target=monitor_connection, args=(ws, 10))
    monitor_thread.start()
    ws.run_forever()

    time_list_day = [pd.to_datetime(i, unit="ms") + timedelta(hours=8) for i in time_list]
    vol_list = list(map(float, vol_list))
    price_list = list(map(float, price_list))
    l = pd.DataFrame()
    l["time"] = time_list_day
    l["vol"] = vol_list
    l["price"] = price_list
    l.to_csv("/DATA" + underlying + str(date)[0:10] + ".csv")
    # vol_dif = vol_list
    # vol_list = np.array(vol_list).reshape(-1,1)
    # price_list = np.array(price_list).reshape(-1,1)
    # print(vol_list)
    price1 = price_list[0]
    return_list = [i / price1 - 1 for i in price_list]
    vol1 = vol_list[0]
    vol_dif_list = [i - vol1 for i in vol_list]
    vol_dif_list = np.array(vol_dif_list).reshape(-1, 1)
    return_list = np.array(return_list).reshape(-1, 1)
    model = LinearRegression()
    model.fit(return_list, vol_dif_list)
    y_pred = model.predict(return_list)
    print(f"模型斜率（权重）: {model.coef_}")
    print(f"模型截距: {model.intercept_}")
    print(f"均方误差 (MSE): {mean_squared_error(vol_dif_list, y_pred)}")
    # print(f"均方误差 (MSE): {mean_squared_error(y_test, y_pred)}")
    print(f"R^2得分: {r2_score(vol_dif_list, y_pred)}")

    y_pred = model.predict(return_list)
    plt.scatter(return_list, vol_dif_list, color='blue', label='true')
    plt.plot(return_list, y_pred, color='red', label='fit')
    plt.xlabel('return_list')
    plt.ylabel('vol')
    plt.title('VcR')
    plt.legend()
    plt.show()


if __name__ == "__main__":
    main("510300_20241105", "2024-11-05 09:30:00")

#
