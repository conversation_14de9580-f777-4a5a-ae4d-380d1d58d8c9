import os
import re
import shutil
import sys
import zipfile
import subprocess
import polars as pl
from tqdm import tqdm

adpath = os.path.abspath(__file__) if '__file__' in globals() else os.getcwd()
sys.path.extend([os.path.abspath(os.path.join(adpath, *(['..'] * i))) for i in range(3)])

from db_solve import try_encodings
from db_solve.configs import paths


def load_parquet_file_path(parquet_file_path):
    # 读取 Parquet 文件
    df = pl.read_parquet(parquet_file_path)

    # 显示前几行数据
    print(df.head())


def trans_parquet(dir_path, filename):
    if filename.endswith('.csv'):
        files = [filename, ]
    else:
        files = [f for f in os.listdir(dir_path) if f.endswith('.csv')]

    for file in files:
        # print(f'Converting {file} to Parquet...')
        # 构建 CSV 文件的完整路径
        csv_file_path = os.path.join(dir_path, file)

        # 构建 Parquet 文件的名称和路径
        parquet_file_name = os.path.splitext(file)[0] + '.parquet'
        parquet_file_path = os.path.join(dir_path, parquet_file_name)

        encoding = try_encodings(csv_file_path)
        separator = '\t' if "TradeRecords" in csv_file_path else ','

        my_csv = pl.read_csv(csv_file_path, infer_schema_length=10_000_000, encoding=encoding, separator=separator)
        # my_csv = my_csv[[s.name for s in my_csv if not (s.null_count() == my_csv.height)]]
        my_csv.write_parquet(parquet_file_path, compression="zstd", )
        # print(f'Successfully converted to {parquet_file_name}')
        os.remove(csv_file_path)


def clean_oldest_files(folder_path: str = '\\zips\\', max_size=0.1 * 1024 * 1024):
    from pathlib import Path
    # 将文件夹路径转换为Path对象
    folder = Path(os.path.dirname(__file__) + folder_path)

    # 获取文件夹当前的总大小
    current_size = sum(f.stat().st_size for f in folder.iterdir() if f.is_file())

    # 如果当前大小小于或等于最大限制，则不需要清理
    if current_size <= max_size:
        return

    # 获取文件夹中所有文件，按修改时间排序（最旧的文件在前）
    files = sorted(folder.iterdir(), key=lambda f: f.stat().st_mtime)

    # 循环删除文件，直到文件夹大小低于限制
    for file in files:
        if current_size <= max_size:
            break
        current_size -= file.stat().st_size  # 更新当前大小
        file.unlink()  # 删除文件


def compress_csv(exchange, datetoday):
    pattern = rf"%s" % datetoday
    dir_path = paths.Paths(exchange).dirs
    outpath = os.path.join('H:', '')
    if not os.path.exists(outpath):
        print('no ' + outpath)
        outpath = os.path.join('G:', '')

    zip_filename = os.path.join(outpath, exchange + datetoday + 'today.zip')
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file in os.listdir(dir_path):
                if re.search(pattern, file):
                    print('zip   ' + file)
                    file_path = os.path.join(dir_path, file)
                    zip_file.write(file_path, arcname=file)
    except Exception as e:
        print(f"Error: {e}")


def copy_csv(exchange, datetoday):
    pattern = re.escape(str(datetoday))
    dir_path = paths.Paths(exchange).dirs
    outpath = f'/DATA/{exchange}/'

    try:
        from db_solve.fip_trans import ftp_upload_files
        ftp_host = 'nux.pp.ua'
        port=21111
        ftp_user = 'lucfor'
        ftp_pass = 'LINING0418'
        ftp_upload_files(ftp_host, port, ftp_user, ftp_pass, dir_path, outpath, pattern)

        # if not os.path.exists(outpath):
        #     os.mkdir(outpath)
        # for file in os.listdir(dir_path):
        #     if re.search(pattern, file):
        #         print(outpath + ' --------   ' + file)
        #         shutil.copy2(os.path.join(dir_path, file), os.path.join(outpath, file))
    except Exception as e:
        print(f"Error: {e}")


def split_zip(exchange, datetoday):
    import win32com.client
    pattern = rf"%s" % datetoday
    dir_path = paths.Paths(exchange).dirs
    i = 0
    volume_size = '200'
    for file in os.listdir(dir_path):
        if re.search(pattern, file):
            print('zip   ' + file)
            i += 1
            shell = win32com.client.Dispatch("WScript.Shell")
            winrar_path = os.path.dirname(shell.RegRead("HKEY_LOCAL_MACHINE\\SOFTWARE\\WinRAR\\exe64"))

            os.chdir(winrar_path)
            cmd_command = r'%s a -v%sm %s %s' % (
                'Rar.exe', volume_size, os.path.dirname(__file__) + f"\\zips\\{exchange}_{i}.rar", dir_path + file)
            os.system(cmd_command)  # 执行压缩命令
            print('done')


def decompress_csv(exchange):
    with zipfile.ZipFile('zips\\' + exchange + '.zip', 'r') as file:
        file.extractall(paths.Paths(exchange).dirs)


def auto_git():
    from git import Repo
    # pip3 install gitpython
    local_path = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
    os.chdir(local_path)
    repo = Repo(local_path)
    repo.git.pull()

    # 检查状态
    status = repo.git.status()
    if 'nothing to commit, working tree clean' not in status:
        print('----------commit pushed-------------\n', status)
        # 添加所有修改的文件
        repo.git.add('.')
        # 提交到本地仓库
        repo.git.commit('-m', 'auto message')
        # 推送到远程仓库
        repo.remotes.origin.push()
        print('commit pushed')
    else:
        print("Nothing to commit.")


def delete_log_files(directory,):
    """
    删除指定目录下所有包含'log'字样的文件。

    参数:
    directory (str): 需要清理的目录路径。
    """
    keywords = ["VolData", "TradeRecords", "TimeSeries", ]
    suffixes = ('.log', '.tmp', '.bak')
    # 构建正则表达式模式
    pattern = '|'.join(keywords)
    regex = re.compile(pattern, re.IGNORECASE)  # 忽略大小写
    for root, dirs, files in os.walk(directory):
        for file in files:
            if (regex.search(file) and file.endswith(".csv")) or file.endswith(suffixes):
                file_path = os.path.join(root, file)
                print(f"Deleting {file_path}")
                try:
                    os.remove(file_path)
                except OSError:
                    print(f"Error deleting {file_path}")
    print("delete all")


def trans_one(dirs, file):
    """
    转换单个文件为 Parquet 格式。

    参数:
    dirs (str): 包含要转换的文件的目录路径。
    file (str): 要转换的文件名。
    """
    try:
        if file.endswith('.csv'):
            file_co = (re.split(r'[.\-]', file))[0] + '.csv'
            if file_co != file:
                shutil.copy(os.path.join(dirs, file), os.path.join(dirs, file_co))
            trans_parquet(dirs, file_co)    
            print(f"\nConverted {file} to Parquet format.\n")
    except Exception as e:
        print(f"\nError processing {file}: {e}")

def trans_all(dirs):
    for file in tqdm(os.listdir(dirs)):
        if file.endswith('.csv'):
            trans_one(dirs, file)
        elif os.path.isdir(os.path.join(dirs, file)):
            print(f"\nProcessing directory: {file}\n")
            trans_all(os.path.join(dirs, file))


if __name__ == '__main__':
    dirs = r'G:\\DATA'
    # dirs = r'Y:\\DATA'
    trans_all(dirs,)
    print("All CSV files have been converted to Parquet format.")
    # delete_log_files(dirs)

    sys.exit()
