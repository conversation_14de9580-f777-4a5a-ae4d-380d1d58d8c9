# -*- coding:utf-8 -*-
from __future__ import print_function
from datetime import datetime
from WindPy import w

# import os
import time
import sys
import logging

# reload(sys)
# sys.setdefaultencoding('utf-8')



import subprocess

# import traceback
# import tempfile


def subexe(cmd):
    # this method is used for monitoring

    # import time
    import subprocess
    # import locale
    # import codecs

    mylist = []
    ps = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    while True:
        out = ps.stderr.read(1)
        if out == '' and ps.poll() != None:
            break
        if out != '':
            # sys.stdout.write(out)
            # sys.stdout.flush()
            data = ps.stdout.readline()
            mylist.append(data)
            print(u"%s" % data, end='')
        # if data == b'':
        #     if ps.poll() is not None:
        #         break
        # else:
        #     mylist.append(data)
        #     newlist = []
        #     for i in mylist:
        #         if i.find('192.168') > 0:
        #             newlist.append(i)
        #     newlist.sort()
        #     print('Sum of requests from LAN:', len(newlist))

    return mylist


def exeCmd(path2):
    # r = os.popen(path2)
    # text = r.read()
    # r.close()
    try:
        obj = subprocess.Popen(path2, shell=True, stdin=subprocess.PIPE,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        text = obj.stdout.read()
        try:
            text.decode('GBK').encode('GBK')
            writeFile('GBK\n')
            text = text.decode('GBK')
        except:
            writeFile('UTF-8\n')
            text = text.decode('UTF-8')
        obj.stdin.close()
        obj.stdout.close()
    except Exception as e:
        text = e
        print(e)
    return text


def whichEncode(text):
    '''
    获取编码格式
    '''
    if isinstance(text, str):
        return "unicode"
    try:
        text.decode("utf8")
        return 'utf8'
    except:
        pass
    try:
        text.decode("gbk")
        return 'gbk'
    except:
        pass


def writeFile(data):
    print(u"%s" % data, end='')
    logging.info(data)
    print('logging successful')


def auto_exit(timer, tt):
    for _ in range(0, timer, tt):
        print("\r", end="")
        print(u"\r程序将在 %d秒 内自动关闭" % timer, end="")
        time.sleep(tt)
        timer -= tt


def linetime(timer, tt):
    lineLength = timer
    delaySeconds = tt
    frontSymbol = '='
    frontSymbol2 = ['—', '\\', '|', '/']
    backSymbol = ' '

    lineTmpla = u"{:%s<%s} {} {:<10}" % (backSymbol, lineLength)
    print(u"本次更新将在 %d秒 内自动退出" % timer, end="")
    for _ in range(0, timer, delaySeconds):
        tmpSymbol = frontSymbol2[timer % (len(frontSymbol2))]
        sys.stdout.write("\r")
        # print(lineTmpla.format(frontSymbol * timer, tmpSymbol, str(timer) + u"秒后自动关闭"), end='')
        sys.stdout.write(lineTmpla.format(frontSymbol * timer, tmpSymbol, str(timer) + "秒后自动关闭"))
        sys.stdout.flush()
        time.sleep(delaySeconds)
        timer -= delaySeconds


if __name__ == "__main__":
    i = 0
    sqlpath = u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\SQL\\windtosql\\update'
    wordpath = u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\optionweek\\update.py'
    logpath = u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\daily'
    # encode('gbk', 'ignore').decode('gbk') #.encode('gbk')

    logging.basicConfig(handlers=[logging.FileHandler('%s\\myapp.log' % logpath, 'a', 'utf-8')],
                        level=logging.DEBUG,
                        format='%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s',
                        datefmt='%Y-%m-%d %a %H:%M:%S'
                        )

    now = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
    writeFile(now)

    auto_exit(150, 1)
