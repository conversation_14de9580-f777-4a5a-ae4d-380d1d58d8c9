# -*- coding: utf-8 -*-
import re
import pandas as pd
import datetime
import numpy as np
from datetime import time as dtime
import codecs
import csv
import os

from cqtrade.strategy.template import BaseStrategy, EXCHANGE_NAME_TO_ENUM
from cqtrade.trade.model import SendOrderData, OrderData, SendOrderResponse
from cqtrade.trade.constant import OrderDirection


class VolumeStrategy3(BaseStrategy):
    """"""

    author = "ChrisZ"

    #策略账号(每天会改变，需要手动修改)
    ice_algoid = 1206160023
    refer = 'i2209.DCE'
    maker = 'i2301.DCE'
    lots = 1
    hold = 800
    skew_pos = 5
    basis_limit = 30
    ioc_flag_refer = False
    ioc_flag_maker = False
    ioc_price_refer = 1
    ioc_price_maker = 1
    refer_hedge_mode = False
    refer_hedge_type = 'on_trade'
    lower = 0.8
    Maker_minsize = 10
    Refer_minsize = 100
    Maker_Grid_Start = 2  #从四档开始铺单
    Maker_Grid_Interval = 2  #每2个tick挂一个单
    Maker_Grid_Layer = 3  #铺几层单
    Refer_Grid_Start = 3  #从四档开始铺单
    Refer_Grid_Interval = 1  #每2个tick挂一个单
    Refer_Grid_Layer = 3  #铺几层单
    net_init = 0
    net = net_init
    position_long = 0
    position_short = 0
    pos = position_long - position_short
    max_cancel_count = 400
    totalPnl = 0
    cancel_layer = 2
    Refer_OC_Flag = True
    cancel_count = (0, 0)

    parameters = [
        'refer',
        'maker',
        'lots',
        'lower',
        'net_init',
        'Maker_minsize',
        'Refer_minsize',
        'skew_pos',
        'basis_limit',
        'ioc_flag_refer',
        'ioc_flag_maker',
        'ioc_price_refer',
        'ioc_price_maker',
        'refer_hedge_mode',
        'refer_hedge_type',  #1 on_tick 2 on_trade 3 both
        'Maker_Grid_Start',
        'Maker_Grid_Interval',
        'Maker_Grid_Layer',
        'Refer_Grid_Start',
        'Refer_Grid_Interval',
        'Refer_Grid_Layer',
        'cancel_layer',
        'position_long',
        'position_short',
        'hold',  #最大单边持仓
        'ice_algoid',
        'max_cancel_count',
        'Refer_OC_Flag'
    ]

    variables = [
        'cancel_count',
        'net',
        'pos',
        'totalPnl'
    ]

    def process_tick(self, tick):  #处理非五档行情的tick数据
        ts = self.pricetick

        if len(tick.bid_prices) != 5 or tick.bid_prices[1] == 0:  #非五档行情
            bid_price_1 = tick.bid_prices[0]
            bid_price_2 = tick.bid_prices[0] - ts
            bid_price_3 = tick.bid_prices[0] - 2 * ts
            bid_price_4 = tick.bid_prices[0] - 3 * ts
            bid_price_5 = tick.bid_prices[0] - 4 * ts
            ask_price_1 = tick.ask_prices[0]
            ask_price_2 = tick.ask_prices[0] + ts
            ask_price_3 = tick.ask_prices[0] + 2 * ts
            ask_price_4 = tick.ask_prices[0] + 3 * ts
            ask_price_5 = tick.ask_prices[0] + 4 * ts
            tick.bid_prices = [bid_price_1, bid_price_2, bid_price_3, bid_price_4, bid_price_5]
            tick.ask_prices = [ask_price_1, ask_price_2, ask_price_3, ask_price_4, ask_price_5]

        tick.bid_price_1, tick.bid_price_2, tick.bid_price_3, tick.bid_price_4, tick.bid_price_5 = tick.bid_prices
        tick.ask_price_1, tick.ask_price_2, tick.ask_price_3, tick.ask_price_4, tick.ask_price_5 = tick.ask_prices
        tick.bid_volume_1, tick.bid_volume_2, tick.bid_volume_3, tick.bid_volume_4, tick.bid_volume_5 = tick.bid_volumes
        tick.ask_volume_1, tick.ask_volume_2, tick.ask_volume_3, tick.ask_volume_4, tick.ask_volume_5 = tick.ask_volumes
        tick.last_price = tick.last
        return tick

    def sendorder(self, ice_algoid, order, comments):
        self.send_order(ice_algoid, order, comments)
        self.send_orders.append(order)  #本地订单维护
        # self.log_info(f"发出下单指令，方向:{order.direction.value}, 价格:{order.price},数量:{order.quantity},发单时间：{datetime.datetime.now()}")

    def buy(self, vt_symbol, price, qty, comments):  #买
        symbol, exchange = vt_symbol.split('.')
        exchange = EXCHANGE_NAME_TO_ENUM[exchange]
        order = SendOrderData(
            symbol=symbol,
            exchange=exchange,
            direction=OrderDirection.BUY_OPEN if ((self.position_short <= self.hold and vt_symbol == self.maker) or (
                        vt_symbol == self.refer and self.Refer_OC_Flag)) else OrderDirection.BUY_CLOSE,
            quantity=qty,
            price=price
        )
        return self.sendorder(self.ice_algoid, order, comments)

    def short(self, vt_symbol, price, qty, comments):  #卖
        symbol, exchange = vt_symbol.split('.')
        exchange = EXCHANGE_NAME_TO_ENUM[exchange]
        order = SendOrderData(
            symbol=symbol,
            exchange=exchange,
            direction=OrderDirection.SELL_OPEN if ((self.position_long <= self.hold and vt_symbol == self.maker) or (
                        vt_symbol == self.refer and self.Refer_OC_Flag)) else OrderDirection.SELL_CLOSE,
            quantity=qty,
            price=price
        )
        return self.sendorder(self.ice_algoid, order, comments)

    def cancel_order(self, orderid):
        self.cancel_one_order_id(self.ice_algoid, orderid)

    def cancel_signal(self, signal):  #暂时先不要用
        self.cancel_one_signal_id(self.ice_algoid, signal)

    def cancel_all(self):
        orderids = list(self.active_limit_orders.keys())
        for orderid in orderids:
            self.cancel_order(orderid)

    def cancel_symbol(self, symbol):
        for orderid, order in self.active_limit_orders.items():
            if order.symbol == symbol:
                self.cancel_order(orderid)

    def on_init(self):
        """
        Callback when strategy is inited.
        """

        # self.subscribe_tick(self.vt_symbols) 

        df = pd.read_csv('Y:/YSP-NFS/cscquant-pythonplugin/cscquant/zhanghongchi/.trader/config.csv', index_col=0,
                         error_bad_lines=False)
        config = df.to_dict()
        symbol, exchange = self.refer.split('.')
        contract = symbol[:re.search(r'\d', symbol).start()].upper()
        exchange = exchange.upper()
        if exchange == 'ZCE':
            exchange = 'CZC'
        if exchange == 'SHFE':
            exchange = 'SHF'
        Ticker = contract + '.' + exchange
        self.pricetick = config['pricetick'][Ticker]
        self.size = config['size'][Ticker]
        self.log_info('策略初始化')

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.log_info("策略启动")
        self.vt_symbols = [self.refer, self.maker]
        self.subscribe_tick(self.vt_symbols)
        self.mCount = 0
        self.rCount = 0
        self.active_limit_orders = {}  #系统返回发单成功的orders
        self.send_orders = []  #自身维护的orders
        self.fairPrice = 0
        self.last_net = 0
        self.last_pos = 0
        self.pos_chg = 0
        self.buy_price = 0
        self.short_price = 0
        self.MMTrading = False
        self.buy_price = 0
        self.short_price = 0
        self.last_cancel_count = 0
        self.pos = self.position_long - self.position_short
        self.net = self.net_init
        self.end_flag = False
        self.lastMaker = None
        self.lastRefer = None
        self.cancel_count = {}
        for i in [self.maker, self.refer]:
            self.cancel_count[i] = 0
        self.net = self.net_init
        self.pos = self.position_long - self.position_short
        self.log_info(f"初始持仓信息：多头{self.position_long},空头{self.position_short}")
        self.update_data()

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.log_info("策略停止！")

    def on_pause(self):
        self.log_info("策略暂停，撤单！")
        self.cancel_all()
        self.send_orders = []  #自身维护的orders

    def on_end(self, tick):  #收盘清仓,只发一次对价清仓指令
        dt = datetime.datetime.now()
        if ((dt.time() > dtime(22, 55) and dt.time() < dtime(23, 00)) \
                or (dt.time() > dtime(11, 28) and dt.time() < dtime(11, 30)) \
                or (dt.time() > dtime(14, 55) and dt.time() < dtime(15, 00))):
            if not self.end_flag:
                self.log_info('time to clear net')
                self.log_info(f'remaining net:{self.net}')
                self.cancel_all()
                if self.net > 0:
                    self.short(self.refer, tick.bid_price_1, abs(self.net), 'end')
                if self.net < 0:
                    self.buy(self.refer, tick.ask_price_1, abs(self.net), 'end')
            self.end_flag = True
        else:
            self.end_flag = False

    def on_time(self):
        dt = datetime.datetime.now()
        if not (
                (dtime(21, 5) < dt.time() < dtime(22, 55))
                or (dtime(9, 5) < dt.time() < dtime(10, 15))
                or (dtime(10, 30) < dt.time() < dtime(11, 25))
                or (dtime(13, 32) < dt.time() < dtime(14, 55))
        ):
            self.MMTrading = False
        else:
            self.MMTrading = True
        return self.MMTrading

    def edge_b(self, tick):  #计算量比
        if tick.bid_volume_1 / tick.ask_volume_1 > 1.5:
            edge = 1
        elif tick.bid_volume_1 / tick.ask_volume_1 < self.lower:
            edge = -1
        else:
            edge = 0
        return edge

    def edge_s(self, tick):  #计算量比
        if tick.ask_volume_1 / tick.bid_volume_1 > 1.5:
            edge = 1
        elif tick.ask_volume_1 / tick.bid_volume_1 < self.lower:
            edge = -1
        else:
            edge = 0
        return edge

    def getFairPrice(self, lastP, askP, bidP, edgeP):
        fair = lastP
        if askP > 0 and bidP > 0:
            if askP - bidP <= edgeP * self.pricetick:  # 有流动性，中间价
                fair = 0.5 * (askP + bidP)
            else:  # 流动性不足, 不变
                if lastP > askP:
                    fair = askP
                if lastP < bidP:
                    fair = bidP
        return fair

    def getPosPnL(self, pos):
        totalPnl = 0  # tick 数   更改为总盈亏
        price = self.fairPrice
        current_volume_long = pos.today_amount_long_open + pos.today_amount_long_close  #多头交易量
        current_volume_short = pos.today_amount_short_open + pos.today_amount_short_close  #
        current_value_long = pos.today_value_long_open
        current_value_short = pos.today_value_short_open
        if pos.current_amount_long > 0 and current_volume_long > 0:  # 多头持仓手数大于0
            position_long_avg = current_value_long / current_volume_long / self.size
            totalPnl += current_volume_long * (price - position_long_avg) * self.size
        if pos.current_amount_short > 0 and current_volume_short > 0:  # 空头持仓手数
            position_short_avg = current_value_short / current_volume_short / self.size
            totalPnl += current_volume_short * (position_short_avg - price) * self.size
        return totalPnl

    def on_tick(self, tick):
        if tick.symbol == self.maker.split('.')[0]:
            self.on_tick_maker(tick)
        if tick.symbol == self.refer.split('.')[0]:
            self.on_tick_refer(tick)
        self.on_end(tick)

    def on_tick_refer(self, tick):
        self.rCount += 1
        tick = self.process_tick(tick)
        ts = self.pricetick
        lots = self.lots
        self.MMTrading = self.on_time()
        orders = list(self.active_limit_orders.items())
        refer_orders = np.sum([order.symbol == tick.symbol for order in self.send_orders])
        cancel_count = self.cancel_count[self.refer] + refer_orders
        minsize = self.Refer_minsize

        if cancel_count > self.max_cancel_count:
            self.cancel_symbol(tick.symbol)
            self.log_info("撤单次数超限，停止自营！")
            return

        if self.rCount > 1:

            for vt_orderid, order in orders:
                if order.symbol != tick.symbol:
                    continue

                #触发主力对冲盘口反向订单撤
                if self.refer_hedge_mode and self.skew_pos >= self.net >= -self.skew_pos:
                    if order.price == tick.bid_price_1 and self.pos_chg > 0 and self.net >= 0:
                        self.cancel_order(vt_orderid)
                    if order.price == tick.ask_price_1 and self.pos_chg < 0 and self.net <= 0:
                        self.cancel_order(vt_orderid)
                        #skew_pos内反edge增加持仓,则盘口撤单
                if self.net <= self.skew_pos and self.net >= - self.skew_pos:
                    if order.price == tick.bid_price_1 and self.edge_b(tick) == -1 and self.net >= 0:
                        self.cancel_order(vt_orderid)
                        # print(f"反edge撤单,净持仓：{self.net},订单：{order}")
                    if order.price == tick.ask_price_1 and self.edge_s(tick) == -1 and self.net <= 0:
                        self.cancel_order(vt_orderid)
                        #超过skewpos撤单
                if self.net > self.skew_pos:
                    if order.price >= tick.__getattribute__(
                            f'bid_price_{self.cancel_layer}') and order.direction.value in ['BuyOpen', 'BuyClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过skewpos撤单,净持仓：{self.net},订单：{order}")
                if self.net < -self.skew_pos:
                    if order.price <= tick.__getattribute__(
                            f'ask_price_{self.cancel_layer}') and order.direction.value in ['SellOpen', 'SellClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过skewpos撤单,净持仓：{self.net},订单：{order}")
                # 增加持仓 edge=0 唯一单撤单
                if order.price == tick.bid_price_1 and self.edge_b(
                        tick) == 0 and self.net > 0 and tick.bid_volume_1 <= minsize:
                    self.cancel_order(vt_orderid)
                    # print(f"唯一单撤单,净持仓：{self.net},订单：{order}")
                if order.price == tick.ask_price_1 and self.edge_s(
                        tick) == 0 and self.net < 0 and tick.ask_volume_1 <= minsize:
                    self.cancel_order(vt_orderid)
                    # print(f"唯一单撤单,净持仓：{self.net},订单：{order}")

            if self.MMTrading:  #规定报价时间才报价
                # 先判断是否需要balance
                bid1_orders = np.sum(
                    [order.price == tick.bid_price_1 for order in self.send_orders if order.symbol == tick.symbol])
                ask1_orders = np.sum(
                    [order.price == tick.ask_price_1 for order in self.send_orders if order.symbol == tick.symbol])
                if self.net > self.skew_pos and ask1_orders == 0:
                    self.short(self.refer, tick.ask_price_1, lots, 'balance')
                #                    ask1_orders+=1
                if self.net < -self.skew_pos and bid1_orders == 0:
                    self.buy(self.refer, tick.bid_price_1, lots, 'balance')
                #                    bid1_orders+=1
                # 判断主力对冲
                if self.refer_hedge_mode and (
                        self.refer_hedge_type == 'on_tick' or self.refer_hedge_type == 'both') and self.skew_pos >= self.net >= -self.skew_pos:
                    if self.pos_chg > 0 and self.net > 0 and ask1_orders == 0:
                        self.short(self.refer, tick.ask_price_1, lots, 'hedge')
                        self.pos_chg = 0
                    if self.pos_chg < 0 and self.net < 0 and bid1_orders == 0:
                        self.buy(self.refer, tick.bid_price_1, lots, 'hedge')
                        self.pos_chg = 0

                #                bid1_orders = np.sum([order.price == tick.bid_price_1 for order in self.send_orders if order.symbol == tick.symbol])
                #                ask1_orders = np.sum([order.price == tick.ask_price_1 for order in self.send_orders if order.symbol == tick.symbol])

                quote_prices = [order.price for order in self.send_orders if order.symbol == tick.symbol]
                # 挂单
                Grid_Start = self.Refer_Grid_Start
                Grid_Interval = self.Refer_Grid_Interval
                Grid_Layer = self.Refer_Grid_Layer
                my_bid_1 = np.floor(
                    (tick.bid_price_1 - (Grid_Start - 1) * ts) / Grid_Interval / ts) * Grid_Interval * ts
                my_ask_1 = np.ceil((tick.ask_price_1 + (Grid_Start - 1) * ts) / Grid_Interval / ts) * Grid_Interval * ts
                for i in range(Grid_Layer):
                    my_bid = my_bid_1 - i * Grid_Interval * ts
                    my_ask = my_ask_1 + i * Grid_Interval * ts
                    if my_bid not in quote_prices and self.net <= self.skew_pos:
                        self.buy(self.refer, my_bid, lots, 'refer')
                    if my_ask not in quote_prices and self.net >= -self.skew_pos:
                        self.short(self.refer, my_ask, lots, 'refer')
        self.lastRefer = tick

    def on_tick_maker(self, tick):
        self.mCount += 1
        tick = self.process_tick(tick)
        ts = self.pricetick
        lots = self.lots
        self.MMTrading = self.on_time()
        orders = list(self.active_limit_orders.items())
        maker_orders = np.sum([order.symbol == tick.symbol for order in self.send_orders])
        cancel_count = self.cancel_count[self.maker] + maker_orders
        minsize = self.Maker_minsize

        if cancel_count > self.max_cancel_count:
            self.cancel_symbol(tick.symbol)
            self.log_info("撤单次数超限，停止自营！")
            return

        if self.mCount > 1:

            for vt_orderid, order in orders:
                if order.symbol != tick.symbol:
                    continue

                #skew_pos内反edge则盘口撤单
                if self.pos <= self.basis_limit and self.pos >= - self.basis_limit:
                    if order.price == tick.bid_price_1 and self.edge_b(tick) == -1 and self.pos >= 0:
                        self.cancel_order(vt_orderid)
                        # print(f"反edge撤单,净持仓：{self.net},订单：{order}")
                    if order.price == tick.ask_price_1 and self.edge_s(tick) == -1 and self.pos <= 0:
                        self.cancel_order(vt_orderid)
                        #Net超过skewpos,不继续增加net敞口,撤单
                if self.net > self.skew_pos:
                    if order.price >= tick.__getattribute__(
                            f'bid_price_{self.cancel_layer}') and order.direction.value in ['BuyOpen', 'BuyClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过skewpos撤单,净持仓：{self.net},订单：{order}")
                if self.net < -self.skew_pos:
                    if order.price <= tick.__getattribute__(
                            f'ask_price_{self.cancel_layer}') and order.direction.value in ['SellOpen', 'SellClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过skewpos撤单,净持仓：{self.net},订单：{order}")
                #超过basislimit撤单
                if self.pos > self.basis_limit:
                    if order.price >= tick.__getattribute__(
                            f'bid_price_{self.cancel_layer}') and order.direction.value in ['BuyOpen', 'BuyClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过basis_limit撤单,净持仓：{self.net},订单：{order}")
                if self.pos < -self.basis_limit:
                    if order.price <= tick.__getattribute__(
                            f'ask_price_{self.cancel_layer}') and order.direction.value in ['SellOpen', 'SellClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过basis_limit撤单,净持仓：{self.net},订单：{order}")                
                # 增加持仓 edge=0 唯一单撤单
                if order.price == tick.bid_price_1 and self.edge_b(
                        tick) == 0 and self.pos > 0 and tick.bid_volume_1 <= minsize:
                    self.cancel_order(vt_orderid)
                    # print(f"唯一单撤单,净持仓：{self.net},订单：{order}")
                if order.price == tick.ask_price_1 and self.edge_s(
                        tick) == 0 and self.pos < 0 and tick.ask_volume_1 <= minsize:
                    self.cancel_order(vt_orderid)
                    # print(f"唯一单撤单,净持仓：{self.net},订单：{order}")

            if self.MMTrading:  #规定报价时间才报价
                # 先判断是否需要balance
                bid1_orders = np.sum(
                    [order.price == tick.bid_price_1 for order in self.send_orders if order.symbol == tick.symbol])
                ask1_orders = np.sum(
                    [order.price == tick.ask_price_1 for order in self.send_orders if order.symbol == tick.symbol])
                if self.pos > self.basis_limit and ask1_orders == 0:
                    self.short(self.maker, tick.ask_price_1, lots, 'balance')
                if self.pos < -self.basis_limit and bid1_orders == 0:
                    self.buy(self.maker, tick.bid_price_1, lots, 'balance')

                    # 判断反手
                bid1_orders = np.sum(
                    [order.price == tick.bid_price_1 for order in self.send_orders if order.symbol == tick.symbol])
                ask1_orders = np.sum(
                    [order.price == tick.ask_price_1 for order in self.send_orders if order.symbol == tick.symbol])

                quote_prices = [order.price for order in self.send_orders if order.symbol == tick.symbol]
                # 挂单
                Grid_Start = self.Maker_Grid_Start
                Grid_Interval = self.Maker_Grid_Interval
                Grid_Layer = self.Maker_Grid_Layer
                my_bid_1 = np.floor(
                    (tick.bid_price_1 - (Grid_Start - 1) * ts) / Grid_Interval / ts) * Grid_Interval * ts
                my_ask_1 = np.ceil((tick.ask_price_1 + (Grid_Start - 1) * ts) / Grid_Interval / ts) * Grid_Interval * ts
                for i in range(Grid_Layer):
                    my_bid = my_bid_1 - i * Grid_Interval * ts
                    my_ask = my_ask_1 + i * Grid_Interval * ts
                    if my_bid not in quote_prices and self.net <= self.skew_pos and self.pos <= self.basis_limit:
                        self.buy(self.maker, my_bid, lots, 'maker')
                    if my_ask not in quote_prices and self.net >= -self.skew_pos and self.pos >= -self.basis_limit:
                        self.short(self.maker, my_ask, lots, 'maker')
        self.lastMaker = tick

    def on_trade(self, order):
        volume_traded = order.filled_qty - order.original_filled_qty
        price_traded = (order.filled_price - order.original_filled_price) / self.size / volume_traded
        if order.direction.value in ['BuyOpen', 'BuyClose']:
            self.net += volume_traded
        if order.direction.value in ['SellOpen', 'SellClose']:
            self.net -= volume_traded
        if order.symbol == self.refer.split('.')[0]:
            self.onReferTrade(order)
        if order.symbol == self.maker.split('.')[0]:
            self.onMMTrade(order)
        self.last_net = self.net
        self.last_pos = self.pos
        self.log_info(
            f"收到成交回报, 代码：{order.symbol},方向:{order.direction.value}, 价格:{price_traded},数量:{volume_traded}, 信息:{order.order_id},成交时间：{datetime.datetime.now()}")
        self.update_data()

    def onMMTrade(self, order):
        volume_traded = order.filled_qty - order.original_filled_qty
        price_traded = (order.filled_price - order.original_filled_price) / self.size / volume_traded
        trade_price = price_traded
        last_tick = self.lastMaker
        if order.direction.value in ['BuyOpen']:
            self.position_long += volume_traded
        if order.direction.value in ['SellOpen']:
            self.position_short += volume_traded
        if order.direction.value in ['BuyClose']:
            self.position_short -= volume_traded
        if order.direction.value in ['SellClose']:
            self.position_long -= volume_traded
        self.pos = self.position_long - self.position_short
        self.pos_chg = self.pos - self.last_pos
        lots = self.lots
        # bid1_orders = np.sum([_order.price == last_tick.bid_price_1 for _order in self.send_orders if _order.symbol == order.symbol]) 
        # ask1_orders = np.sum([_order.price == last_tick.ask_price_1 for _order in self.send_orders if _order.symbol == order.symbol]) 
        if self.ioc_flag_maker:
            if self.net <= self.skew_pos and self.net >= - self.skew_pos:
                if self.pos_chg > 0 and self.net > 0 and self.pos > 0 and trade_price != 0:
                    self.short(self.maker, trade_price + self.ioc_price_maker * self.pricetick, volume_traded,
                               'reverse')
                if self.pos_chg < 0 and self.net < 0 and self.pos < 0 and trade_price != 0:
                    self.buy(self.maker, trade_price - self.ioc_price_maker * self.pricetick, volume_traded, 'reverse')

                    # 判断主力对冲
        if self.refer_hedge_mode and (
                self.refer_hedge_type == 'on_trade' or self.refer_hedge_type == 'both') and self.net < self.skew_pos and self.net > -self.skew_pos:
            bid1_orders = np.sum([_order.price == self.lastRefer.bid_price_1 for _order in self.send_orders if
                                  _order.symbol == order.symbol])
            ask1_orders = np.sum([_order.price == self.lastRefer.ask_price_1 for _order in self.send_orders if
                                  _order.symbol == order.symbol])

            if self.pos_chg > 0 and self.net > 0 and ask1_orders == 0:
                self.short(self.refer, self.lastRefer.ask_price_1, lots, 'hedge')
                if self.refer_hedge_type != 'both':
                    self.pos_chg = 0
            if self.pos_chg < 0 and self.net < 0 and bid1_orders == 0:
                self.buy(self.refer, self.lastRefer.bid_price_1, lots, 'hedge')
                if self.refer_hedge_type != 'both':
                    self.pos_chg = 0

    def onReferTrade(self, order):
        volume_traded = order.filled_qty - order.original_filled_qty
        price_traded = (order.filled_price - order.original_filled_price) / self.size / volume_traded
        trade_price = price_traded
        last_tick = self.lastRefer
        lots = self.lots
        bid1_orders = np.sum(
            [_order.price == last_tick.bid_price_1 for _order in self.send_orders if _order.symbol == order.symbol])
        ask1_orders = np.sum(
            [_order.price == last_tick.ask_price_1 for _order in self.send_orders if _order.symbol == order.symbol])
        if self.ioc_flag_refer:
            if self.net <= self.skew_pos and self.net >= - self.skew_pos:
                net_chg = self.net - self.last_net
                if net_chg > 0 and self.net > 0 and trade_price != 0:
                    self.short(self.refer, trade_price + self.ioc_price_refer * self.pricetick, volume_traded,
                               'reverse')
                if net_chg < 0 and self.net < 0 and trade_price != 0:
                    self.buy(self.refer, trade_price - self.ioc_price_refer * self.pricetick, volume_traded, 'reverse')

    def on_order(self, order: OrderData):  #订单状态发生变化，则调用一次on_order,不记录首次发单
        vt_symbol = f"{order.symbol}.{order.exchange.value}"
        current_sid = order.sid.split('.')[-1]  # order.sid形如cq094551636018.AT0708.AuctionTrade

        if current_sid.isdigit():
            return  # 过滤算法单回报
        if order.order_status.value in ['Filled', 'Cancelled', 'Invalid', 'InvalidCancel', 'LocalCancelled', 'Unsend']:
            # self.log_info(f"订单状态变化，订单状态：{order.order_status.value}, 信息:{order.order_id},错误代码:{order.error_code},错误信息：{order.error_message}")
            orderid = order.order_id
            if orderid in self.active_limit_orders:
                self.active_limit_orders.pop(orderid)  #全成、撤单或发单失败则移除
            for send_order in self.send_orders:
                if send_order.price == order.order_price:
                    self.send_orders.remove(send_order)
        if order.order_status.value in ['Filled', 'PartialFilled']:
            self.on_trade(order)
        if order.order_status.value in ['Invalid']:
            self.log_info(
                f"废单，订单状态：{order.order_status.value}, 信息:{order.order_id},错误代码:{order.error_code},错误信息：{order.error_message}")
        # if order.order_status.value in ['Cancelled']:
        #     self.log_info(f"收到撤单回报, 方向:{order.direction.value}, 价格:{order.order_price},数量:{order.order_qty},信息:{order.order_id},撤单时间：{datetime.datetime.now()}")
        if order.order_status.value in ['Cancelled', 'PartialCancelled']:
            self.cancel_count[vt_symbol] += 1
            # if order.order_status.value in ['Open']:
        #     self.log_info(f"收到下单回报, 方向:{order.direction.value}, 价格:{order.order_price}, 数量:{order.order_qty}, 信息:{order.order_id},下单时间：{datetime.datetime.now()}")

    def on_send_order(self, response: SendOrderResponse):  #记录首次发单返回的数据（包括是否发单成功）
        if not response.success:
            self.log_info(f"下单失败:{response}")
        else:
            current_sid = response.sid.split('.')[-1]
            if current_sid.isdigit():
                return  # 过滤算法单回报
            for orderid, order in zip(response.orderid_list, response.order_list):
                cq_symbol = f"{order.symbol}.{order.exchange.value}"
                if isinstance(orderid, str):  # 失败
                    self.log_info(
                        f"下单标的{cq_symbol}失败, 方向:{order.direction.value}, 价格:{order.price}, 数量:{order.quantity}, 信息:{orderid},下单时间：{datetime.datetime.now()}")
                else:
                    # self.log_info(f"收到下单回报, 方向:{order.direction.value}, 价格:{order.price}, 数量:{order.quantity}, 信息:{orderid},下单时间：{datetime.datetime.now()}")
                    self.active_limit_orders[str(orderid)] = order  #记录成功发单的订单
