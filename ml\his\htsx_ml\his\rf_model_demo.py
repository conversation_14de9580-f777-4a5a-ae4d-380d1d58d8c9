import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, TimeSeriesSplit, GridSearchCV
from sklearn.preprocessing import StandardScaler  
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, mean_absolute_percentage_error

from utils import normalize_columns

# 1. 加载数据
# 假设数据存储在一个CSV文件中，包含特征和目标列, 其中目标列须命名为'target',可以包含一个时间列
# 可选包含日期列，用于训练集和测试集的划分
data = pd.read_csv('../data/temp.csv')

# 2. 数据预处理，提取特征
X = data.drop(['target'], axis=1)
y = data['target']
if 'TimeStamp' in X.columns:
    X.drop(['TimeStamp'], axis=1, inplace=True)

# 3. 划分训练集和测试集: 如果包含label列，则有限按照label进行划分，标签0为训练集，1为测试集
if 'label' in X.columns:
    X.drop(['TimeStamp'], axis=1, inplace=True)
    X_train, y_train = X[X.label == 0], y[X.label == 0]
    X_test, y_test = X[X.label == 1], y[X.label == 1]
else: 
    # 默认方法
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, shuffle=False)

# 使用训练集的均值和标准差对训练集和测试集进行归一化，可以选择制定的列不需要标准化操作 
# eg：[1,2,3]则表示1-3列不需要归一化操作
X_train, X_test = normalize_columns(X_train, X_test, exclude_columns=None)

print(f"特征个数：{X.shape[1]}, 数据数量：{X.shape[0]}")
print(f"训练集大小：{X_train.shape[0]}, 测试集大小：{X_test.shape[0]}")

# 4. 定义参数网格
param_grid = {
    'n_estimators': [10, 20, 40],  # 树的数量
    'max_depth': [None, 10, 20],     # 树的最大深度
    'min_samples_split': [2, 5, 10], # 分裂内部节点所需的最小样本数
    'min_samples_leaf': [1, 2, 4]    # 叶节点所需的最小样本数
}

# 5. 构建随机森林模型
model = RandomForestRegressor(random_state=42)

# 6. 使用 GridSearchCV 进行参数搜索
#    使用 TimeSeriesSplit 进行时间序列分割

tscv = TimeSeriesSplit(n_splits=5)  # 5 个时间序列分割

print("start params search")
grid_search = GridSearchCV(estimator=model, param_grid=param_grid, cv=tscv, scoring='neg_mean_squared_error', n_jobs=-1)
print("start fit")
grid_search.fit(X_train, y_train)
print("params search done")

# 7. 输出最佳参数
print("Best Parameters:", grid_search.best_params_)

# 8. 使用最佳参数的模型进行预测
best_model = grid_search.best_estimator_
y_train_pred = best_model.predict(X_train)  # 训练集预测
y_test_pred = best_model.predict(X_test)    # 测试集预测

# 9. 定义评价函数
def evaluate_model(y_true, y_pred, set_name):
    mse = mean_squared_error(y_true, y_pred)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    # mape = mean_absolute_percentage_error(y_true, y_pred)
    
    # 计算准确率: 方向预测正确的概率
    accuracy = np.mean(np.sign(y_true) == np.sign(y_pred) )
    
    # 输出评价结果
    # MSE（均方误差）表示模型预测值与实际值之间差异的平方的平均值，值越小说明预测误差越小
    # MAE（平均绝对误差）表示预测值与实际值之间绝对差异的平均值，值越小说明预测误差越小
    # R²（决定系数）表示模型对目标变量方差的解释能力，取值范围在0到1之间，越接近1说明模型的解释能力越强
    # Accuracy表示方向预测正确的比例
    print(f'Evaluation on {set_name}:')
    print(f'MSE: {mse}')
    print(f'MAE: {mae}')
    print(f'R²: {r2}')
    print(f'Accuracy: {accuracy * 100:.2f}%')
    # 不同涨跌幅大小的具体预测准确率
    big_up = np.mean(np.sign(y_pred[y_true > 8e-5]) == np.sign(y_true[y_true > 8e-5]))
    big_down = np.mean(np.sign(y_pred[y_true < -8e-5]) == np.sign(y_true[y_true < -8e-5]))
    print(f'较大涨幅正确率:{big_up}')
    print(f'较大跌幅正确率:{big_down}')
    print('-' * 40)

# 10. 在训练集和测试集上评估模型
evaluate_model(y_train, y_train_pred, 'Training Set')  # 训练集评价
evaluate_model(y_test, y_test_pred, 'Test Set')        # 测试集评价
