# -*- coding: utf-8 -*-
"""
Created on Sat Dec 19 11:13:34 2020

@author: 017701
"""

import matplotlib.pyplot as plt
import numpy as np
from scipy import interpolate
import pandas as pd
import datetime
import os


def eachFile(filepath):
    pathDir = os.listdir(filepath)
    file = []
    for allDir in pathDir:
        file.append(os.path.join('%s\\%s' % (filepath, allDir)))
    return (file)


filelist = eachFile('D:\\python\\pnlback\\DATA\\SZ500\\')

def cubicspline(z, target):
    z = z.sort_values(by=['delta'])
    z1 = z.loc[z.delta > target].head(2)
    z2 = z.loc[z.delta < target].tail(2)
    z = pd.concat([z2, z1])
    x = z.delta
    y = z.sigma
    try:
        s = interpolate.CubicSpline(x, y)
        return (s(target))
    except:
        return (-99)


#    arr=np.arange(np.amin(x), np.amax(x), 0.01)
#   fig,ax = plt.subplots(1, 1)
#  ax.plot(x, y, 'bo', label='Data Point')

# ax.plot(arr, s(arr), 'k-', label='Cubic Spline', lw=1)


def vol_cal(path):
    data = pd.read_csv(path, header=None)
    data.columns = ['time', 'spot', 'code', 'tv', 'bid', 'ask', 'sigma', 'a.1', 'a.2', 'a.3', 'a.4', 'a.5', 'delta',
                    'vega', 'a.6', 'a.7', 'rf', 'br', 'time2expiry', 'K', 'Z', 'Call', 'exp', 'a.8', 'a.9', 'a.10',
                    'a.11', 'a.12']
    data.time = pd.to_datetime(data.time)
    data = data.set_index(data.time)
    data = data.drop('time', axis=1)

    data = data.groupby('code').resample('1T').last()
    data = data.drop('code', axis=1)
    data = data.reset_index()
    data = data[data.delta > 0]
    output = pd.DataFrame()
    output['Delta50'] = data.groupby(['exp', 'time']).apply(lambda z: cubicspline(z, 0.50))
    #  output['Delta25']=data.groupby(['exp','time']).apply(lambda z : cubicspline(z,0.25))
    # output['Delta75']=data.groupby(['exp','time']).apply(lambda z : cubicspline(z,0.75))

    output['time2expiry'] = data.groupby(['exp', 'time']).apply(lambda z: z.time2expiry.iloc[0])
    output['spot'] = data.groupby(['exp', 'time']).apply(lambda z: z.spot.iloc[0])
    output['forward'] = data.groupby(['exp', 'time']).apply(
        lambda z: z.spot.iloc[0] * np.exp(z.rf.iloc[0] * z.time2expiry.iloc[0] - z.br.iloc[0] * z.time2expiry.iloc[0]))

    output['forward_vol'] = output.Delta50
    output = output.reset_index()
    output['dt'] = output.groupby('time').time2expiry.diff(1)
    output['dvol'] = output.groupby('time').forward_vol.diff(1)
    #    output['forward_vol']=output.forward_vol/output.dt
    #    output['forward_vol']=output.forward_vol.apply(lambda x :np.sqrt(x))
    return (output)


os.chdir('D:\\python\\pnlback\\DATA\\SZ500\\output')
for i in range(0, len(filelist)):
    # output = vol_cal(filelist[i])
    output = vol_cal(u'D:\\python\\pnlback\\DATA\\SZ500\\vols_20221116.csv')
    output.to_csv(filelist[i][-12:])
