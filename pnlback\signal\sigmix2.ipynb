{"cells": [{"cell_type": "code", "execution_count": 2, "id": "1e8d04e366920ed8", "metadata": {"ExecuteTime": {"end_time": "2024-09-07T17:16:25.800688Z", "start_time": "2024-09-07T17:16:09.159601Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'custom_progress_bar' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 139\u001b[0m\n\u001b[0;32m    137\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdb_solve\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfigs\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m paths\n\u001b[0;32m    138\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdb_solve\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconfigs\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpaths\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FUTCONFIG\n\u001b[1;32m--> 139\u001b[0m engine\u001b[38;5;241m=\u001b[39m\u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[2], line 123\u001b[0m, in \u001b[0;36mmain\u001b[1;34m(datetoday, trade)\u001b[0m\n\u001b[0;32m    120\u001b[0m sys\u001b[38;5;241m.\u001b[39mstdout \u001b[38;5;241m=\u001b[39m Logger(engine\u001b[38;5;241m.\u001b[39mpaths\u001b[38;5;241m.\u001b[39moutdirs \u001b[38;5;241m+\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mout_file\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.txt\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    122\u001b[0m engine\u001b[38;5;241m.\u001b[39mload_data()\n\u001b[1;32m--> 123\u001b[0m \u001b[43mengine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstart_strategy\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    124\u001b[0m \u001b[38;5;28mprint\u001b[39m(mode_set[setting[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmode\u001b[39m\u001b[38;5;124m'\u001b[39m]])\n\u001b[0;32m    126\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m engine\n", "File \u001b[1;32md:\\code\\pythonworld\\pnlback\\signal\\app\\backengie.py:53\u001b[0m, in \u001b[0;36mEngine.start_strategy\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     52\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mstart_strategy\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[1;32m---> 53\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstrategy\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgo_it\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\code\\pythonworld\\pnlback\\signal\\stragtegies\\futone.py:126\u001b[0m, in \u001b[0;36mfutone.go_it\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    122\u001b[0m siglast \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m    124\u001b[0m reset_count \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m--> 126\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m m, row \u001b[38;5;129;01min\u001b[39;00m \u001b[43mcustom_progress_bar\u001b[49m(md_mix\u001b[38;5;241m.\u001b[39miterrows(), total\u001b[38;5;241m=\u001b[39mmd_mix\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m], desc\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mReading DF\u001b[39m\u001b[38;5;124m'\u001b[39m, ncols\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m80\u001b[39m):\n\u001b[0;32m    127\u001b[0m     time1 \u001b[38;5;241m=\u001b[39m time2\n\u001b[0;32m    128\u001b[0m     time2 \u001b[38;5;241m=\u001b[39m m\n", "\u001b[1;31mNameError\u001b[0m: name 'custom_progress_bar' is not defined"]}], "source": ["import os\n", "import sys\n", "import pandas as pd\n", "import datetime\n", "\n", "sys.path.extend([os.path.abspath(os.path.join(os.path.abspath(__file__) if '__file__' in globals() \n", "                else os.getcwd(), *(['..'] * i))) for i in range(5)])\n", "\n", "# 设置 Pandas 显示选项\n", "pd.set_option('display.max_columns', None)  # 显示所有列\n", "pd.set_option('display.max_rows', None)  # 显示所有行\n", "pd.set_option('display.width', None)  # 自动调整列宽\n", "pd.set_option('display.max_colwidth', None)  # 显示所有单元格的内容\n", "pd.set_option('display.expand_frame_repr', False)  # 设置Pandas选项以打印不换行的DataFrame\n", "\n", "today = datetime.datetime.now().strftime(\"Y%m%d\")\n", "\n", "\n", "def main(datetoday='20240904', trade=True):\n", "    setting = dict(datetoday=datetoday, under='SH500', spot_month='2409', out_fut_month='2409', optcodes=['10007488', ],\n", "               mode=['a50', 'one', 'fut', 'etffast'][1],\n", "               etf=dict(SH300='16', SH500='159915', SH50='16'),\n", "               # tradetime00=[['09:30:00', '09:55:00'], ['10:00:00', '10:00:00']],\n", "               silmode=['all', 'onlytrade', 'onlymid'][0], )\n", "\n", "    engine = Engine()\n", "    engine.paths = paths.Paths(setting[\"under\"], os.getcwd())\n", "\n", "    setting.update(paths.UNDERCONFIG[setting[\"under\"]])\n", "\n", "    key_set = {0: 'LastPrice', 1: 'mid', 2: 'ForQuoteSysID', 1.1: 'dsema', 1.2: 'BASIS', 1.3: 'basisema',\n", "            3: 'im1',\n", "            5: 'im5', 6: 'im5vol', 7: 'im2mult', 8: 'mid_minnum', 8.2: 'mid_minnum2', 9: 'press', 10: 'voi',\n", "            11: 'turnover_mid',\n", "            12: 'mid_level', 13: 'mixedge_minnum'}\n", "\n", "    mult_dict = {'im5': 2, 'im5vol': 3, 'im2mult': 2, 'mid_minnum': 2, 'mid_minnum2': 2, 'press': 1, 'voi': 1,\n", "                'turnover_mid': 1}\n", "\n", "    mode_set = (\n", "        {'a50': dict(drvmode=Stray, sigmode='cum', mixmode='backward', key=['LastPrice', ],\n", "                    fut1=setting['spot'] + setting['spot_month'],\n", "                    str1=engine.paths.str1,\n", "                    fut2='CN' + setting['out_fut_month'], futmult2=1, str2=engine.paths.str2,\n", "                    # fut2='si2411', futmult2=5, str2=engine.paths.str22,\n", "                    start=20, end=4900,\n", "                    minpx=1,  # cum\n", "                    minpx2=1,  # fast\n", "                    resetpxchg=1,  # if>minpx,minpx会继续累计\n", "                    minvol=10, rrratio=0, mult=1,\n", "                    lasttimelimit=100,  # 0表示不跨期货行情\n", "                    groupList=['dtmdmix_type', '_dtfut_int', '_signal', '_vol_type']\n", "                    ),\n", "        'one': dict(drvmode=futone, sigmode='chg', mixmode='backward', key=[key_set[key] for key in (8,)],\n", "                    fut1=setting['spot'] + setting['spot_month'],\n", "                    str1=engine.paths.str1,\n", "                    fut2=setting['spot'] + setting['spot_month'], futmult2=FUTCONFIG[setting['spot']]['futmult'],\n", "                    str2=engine.paths.str1,\n", "                    minpx=0.6, mult1=mult_dict, sweight=0.5,\n", "                    \n", "                    ),\n", "        'one2': dict(drvmode=futone, sigmode='chg', mixmode='backward', key=[key_set[key] for key in (8,)],\n", "                    fut1='10007380',\n", "                    str1=engine.paths.str_optmd,\n", "                    fut2='10007380', futmult2=10000,\n", "                    str2=engine.paths.str_optmd,\n", "                    minpx=0.00000, mult1=mult_dict, sweight=0.5,\n", "                    ),\n", "        'etffast': dict(drvmode=Stray, sigmode='cum', mixmode='backward', key=['LastPrice'],\n", "                        fut1=setting['spot'] + setting['spot_month'],\n", "                        str1=engine.paths.str1,\n", "                        fut2='510500_tick', futmult2=1,\n", "                        str2=engine.paths.str23,\n", "                        start=20, end=4900,\n", "                        minpx=0.0001,  # cum\n", "                        minpx2=0.0001,  # fast\n", "                        resetpxchg=0.0001,  # if>minpx,minpx会继续累计\n", "                        minvol=100000, rrratio=0, mult=1, lasttimelimit=100,  # 0表示不跨期货行情\n", "                        groupList=['dtmdmix_type', '_dtfut_int', '_signal', '_vol_type']\n", "                        ),\n", "        'etf': dict(drvmode=Stray, sigmode='fast', mixmode='backward', key=['ForQuoteSysID'],\n", "                    fut1=setting['spot'] + setting['spot_month'],\n", "                    fut2=setting['etf'][setting['under']],\n", "                    str2=engine.paths.str23,\n", "                    start=20, end=4900,\n", "                    minpx=0.0002,  # cum\n", "                    minpx2=0.0002,  # fast\n", "                    resetpxchg=0.0002,  # if>minpx,minpx会继续累计\n", "                    minvol=500000, rrratio=0, mult=1, lasttimelimit=100,  # 0表示不跨期货行情\n", "                    ),\n", "        'fut': dict(drvmode=Stray, sigmode='one', mixmode='backward', key=[key_set[key] for key in (1,)],\n", "                    fut1=setting['spot'] + setting['spot_month'],\n", "                    fut2=setting['spot2'] + setting['spot_month'], futmult2=FUTCONFIG[setting['spot2']]['futmult'],\n", "                    str1=engine.paths.str1,\n", "                    str2=engine.paths.str1,\n", "                    start=-100, end=4900,\n", "                    minpx=0.1,  # cum\n", "                    minpx2=0.1,  # fast\n", "                    resetpxchg=0,  # if>minpx,minpx会继续累计\n", "                    minvol=1, rrratio=0,  # 上一次跳动\n", "                    lasttimelimit=10000000,  # 0表示不跨期货行情\n", "                    groupList=['_signal', 'dsf-1_y', 'dt_mdmix', ]\n", "                    ),\n", "        'mix': dict(drvmode=Stray, sigmode='fast', mixmode='forward', key='mid',\n", "                    fut2='IM2401', str2=engine.paths.str1,\n", "                    fut_ad='IC2401', key_ad=None, str_ad=engine.paths.str1,\n", "                    start=-100, end=4900,\n", "                    minpx=0.4,  # cum\n", "                    minpx2=0.4,  # fast\n", "                    resetpxchg=0,  # if>minpx,minpx会继续累计\n", "                    minvol=0, rrratio=0, lasttimelimit=100,  # 0表示不跨期货行情\n", "                    syn=0, mult=1, mult2=1)})\n", "\n", "\n", "    setting.update(mode_set[setting['mode']])\n", "    engine.add_strategy(setting['drvmode'], setting)\n", "    origin = sys.stdout\n", "    out_file = '-'.join(\n", "        [str(setting[key]) for key in ['datetoday', 'under', 'mode', 'sigmode', 'key', 'minpx']] +\n", "        [datetime.datetime.now().strftime(\"%m%d_%H%M%S\")])\n", "    sys.stdout = Logger(engine.paths.outdirs + f\"\\\\{out_file}.txt\")\n", "\n", "    engine.load_data()\n", "    engine.start_strategy()\n", "    print(mode_set[setting['mode']])\n", "\n", "    return engine\n", "\n", "\n", "\n", "\n", "if __name__ == '__main__':\n", "    from pnlback.signal.stragtegies.futone import futone\n", "    from pnlback.signal.stragtegies.stray import Stray\n", "    from pnlback.signal.stragtegies.func4 import fut4\n", "    from app.backengie import Engine\n", "    from db_solve import Logger\n", "    from db_solve.configs import paths\n", "    from db_solve.configs.paths import FUTCONFIG\n", "    engine=main()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "348df0a7", "metadata": {}, "outputs": [], "source": ["group_results=engine.stat_func()"]}, {"cell_type": "code", "execution_count": 6, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-09-07T17:16:34.262321Z", "start_time": "2024-09-07T17:16:25.805350Z"}, "collapsed": true}, "outputs": [], "source": ["engine.plot_summary(group_results)\n", "engine.plot_surface(group_results)\n", "engine.plot_time(group_results)\n", "engine.plot_pivot(group_results)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9de6dbd58804401f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}