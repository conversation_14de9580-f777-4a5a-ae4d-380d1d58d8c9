# -*- coding: utf-8 -*-
"""
Created on Mon Mar 13 16:50:45 2023

@author: Lenovo
"""

from matplotlib import pyplot
import matplotlib.pyplot as plt
import numpy as np
from scipy import optimize as op
import pandas as pd
import datetime
# from pulp import LpProblem,LpVariable,LpMinimize,LpInteger,LpContinuous
from scipy import interpolate
import cx_Oracle
import os
import scipy.stats as st
from scipy.optimize import fsolve

dangyue=20230322
ciyue=20230628
rf=0.025
ATMprice=4
vvK=[4,3.9,4.1]



def vanna(z):
    S=z.SPOT_REFF
    br=z.br
    K=z.K
    sigma=z.GEN2FITVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    vanna=st.norm.pdf(d1,loc=0,scale=1)*(-d2/sigma)*np.exp(-br*t)
    return (vanna)
def cgamma(z):
    S=z.SPOT_REFF
    br=z.br
    K=z.K
    sigma=z.GEN2FITVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    gamma=st.norm.pdf(d1,loc=0,scale=1)/(S*np.exp(br*t)*sigma*np.sqrt(t))
    return (gamma)

def dvanna(z):
    S=z.SPOT_REFF
    K=z.K
    sigma=z.GEN2FITVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    br=z.br
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    dvanna=st.norm.pdf(d1,loc=0,scale=1)*(-1+d1*d2)*np.exp(-br*t)/(S*sigma*sigma*np.sqrt(t))
    return (dvanna)

    
def volga(z):
    S=z.SPOT_REFF
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    volga=z.GEN2VEGA/sigma*d1*d2*100
    return (volga)

def thetac(z):
    t=z.TIME2EXPIRY
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    S=z.SPOT_REFF*np.exp((rf-br)*t)
    #vega=0.00214585
    
    d2= np.log(S/K)+(-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    if z.Call==1:
        theta=S*st.norm.pdf(d1,loc=0,scale=1)*z.GEN2FITVOL/(-2*np.sqrt(t))-rf*K*np.exp(-rf*t)*st.norm.cdf(d2,loc=0,scale=1)
    else:
        theta=S*st.norm.pdf(d1,loc=0,scale=1)*z.GEN2FITVOL/(-2*np.sqrt(t))+rf*K*np.exp(-rf*t)*st.norm.cdf(-d2,loc=0,scale=1)

    return (theta)



def dvolga(z):
    S=z.SPOT_REFF
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    dvolga=z.GEN2VEGA*100*(d1+d2-d1*d2*d2)/(sigma*sigma*S*np.sqrt(t))

    return (dvolga)

def ATMdelta(z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=z.GEN2ATMVOL
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)

    if z.Call==1:
        return(np.exp(-br*t)*Nd1)
    else:
        return(np.exp(-br*t)*(Nd1-1))

def ATMvega(z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=z.GEN2ATMVOL
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    vega=st.norm.pdf(d1,loc=0,scale=1)*S*np.sqrt(t)*np.exp(-br*t)*100
    return vega


def charm(z):
    t=z.TIME2EXPIRY
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    S=z.SPOT_REFF*np.exp((rf-br)*t)
    #vega=0.00214585
    
    d2= np.log(S/K)+(-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    charm=np.exp(-rf*t)*(-rf*Nd1+st.norm.pdf(d1,loc=0,scale=1)*(np.log(S/K)/-2/sigma*np.power(t,-1.5)+sigma/4/np.sqrt(t)))

    if z.Call==1:
        return(charm)
    else:
        return(charm+rf*np.exp(-rf*t))







def ATMvolprice(z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=z.GEN2ATMVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.Call==1:
        return(Call)
    else:
        return(Put)


def bs_cal(est_sigma,z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=est_sigma
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.Call==1:
        return(Call)
    else:
        return(Put)
def bs_diff(est_sigma,z):
    est_price=bs_cal(est_sigma,z)
    return est_price-z.vvprice

def bs_diff2(est_sigma,z):
    est_price=bs_cal(est_sigma,z)
    return est_price-z.vvprice2
    
def vv(z):
    test=z.copy()
    b=test.tv-test.ATMvolprice
    test.loc[:,'vega']=test.loc[:,'vega']*100
    A=test[['vega','vanna','volga']].values
    try:
        x=np.linalg.inv(A)@b
    except:
        x=[None,None,None]
    return(x)


def cubicspline(z,target):
    z=z.sort_values(by=['GEN2DELTA'])
    z1=z.loc[z.GEN2DELTA>target].head(3)
    z2=z.loc[z.GEN2DELTA<target].tail(1)
    z=pd.concat([z2,z1])
 #   x=z.GEN2DELTA
  #  y=z.GEN2FITVOL
    x=z.GEN2DELTA.append(pd.Series(0.5*np.sign(target)))
    y=z.GEN2FITVOL.append(pd.Series(z.GEN2ATMVOL.iloc[0]))
    z=pd.concat([x,y],axis=1).sort_values(by=[0])
    s=interpolate.CubicSpline(z.iloc[:,0], z.iloc[:,1])
    """      
    arr=np.arange(np.amin(x), np.amax(x), 0.01)    
    fig,ax = plt.subplots(1, 1)
    ax.plot(x, y, 'bo', label='Data Point')

    ax.plot(arr, s(arr), 'k-', label='Cubic Spline', lw=1)
    
   """  
    return(s(target))

def br_cal(z):
    time2expiry=z.TIME2EXPIRY.iloc[0]
    exp0=z.ID.iloc[0]
    E=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==1)].code.iloc[0]
    F=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==0)].code.iloc[0]
    f=data[data.OPTIONCODE==E].GEN2TV.iloc[0]-data[data.OPTIONCODE==F].GEN2TV.iloc[0]+ATMprice*np.exp(-rf*time2expiry)
    s=data[data.OPTIONCODE==E].SPOT_REFF.iloc[0]
    br=-np.log(f/s)/time2expiry
    return(br)


def yparams_cal(z,vvK):
    test=z[z.K.isin(vvK)]
    test=test[test.GEN2TV.isin(test.groupby('K').GEN2TV.min().values)]
    b=test.GEN2TV-test.ATMvolprice

    A=test[['GEN2VEGA','vanna','volga']].values
    x=np.linalg.inv(A)@b
    
    x[0]=x[0]/100
    return(x)
def t_cal(t_est,z,vvK):
    #vega=0.00214585
    time2expiry=t_est
    exp0=z.exp.iloc[0]
    E=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==1)].code.iloc[0]
    F=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==0)].code.iloc[0]
    f=data[data.OPTIONCODE==E].GEN2TV.iloc[0]-data[data.OPTIONCODE==F].GEN2TV.iloc[0]+ATMprice*np.exp(-rf*time2expiry)
    s=data[data.OPTIONCODE==E].SPOT_REFF.iloc[0]
    br=-np.log(f/s)/time2expiry
    
    
    
    test=z.copy()
    test=test[test.GEN2TV.isin(test.groupby('K').GEN2TV.min().values)]
    test=test[test.GEN2TV==test.GEN2TV.max()]
    z=test.iloc[0,:]
    S=z.SPOT_REFF
    K=z.K
    
    sigma=z.GEN2FITVOL
    
    t=t_est
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.Call==1:
        res=Call
    else:
        res=Put    
    return(res-z.GEN2TV)

def rescal():
    global trading,data
    os.environ['path']=r'D:\Oracle\Instant Client\bin'

    oracle_tns=cx_Oracle.makedsn('168.70.16.21',1521,'prdthetf')
    con=cx_Oracle.connect('hs_asset','hundsun',oracle_tns)
    sql_cmd='SELECT * FROM HS_USER.HT_EXCALIBUR'
    data=pd.read_sql(sql_cmd,con)

    oracle_tns=cx_Oracle.makedsn('168.70.16.21',1521,'prdthetf')
    con=cx_Oracle.connect('hs_asset','hundsun',oracle_tns)
    sql_cmd='SELECT * FROM HS_ASSET.qqzs_position_info'
    position=pd.read_sql(sql_cmd,con)


    
    trading=pd.read_csv('D:\\data\\trading300.csv')
    data.OPTIONCODE=data.OPTIONCODE.astype('int')
    data.SPOT_REFF=data.SPOT_REFF.astype('float')
    data=pd.merge(data, trading,left_on='OPTIONCODE',right_on='code')
    data['ID']=data.exp



    data.loc[:,'OPTIONCODE']=pd.to_numeric(data.OPTIONCODE)


    atmvol=data[data.ID==dangyue].GEN2ATMVOL.iloc[0]

    time2expiry=data.groupby('exp').apply(lambda z: fsolve(t_cal,0.007,args=(z,vvK))[0])
    data=data.set_index('exp')
    data['TIME2EXPIRY']=time2expiry
    br=data.groupby('exp').apply(lambda z: br_cal(z))
    data['br']=br
    data=data.reset_index()

    data['vanna']=data.apply(lambda z: vanna(z),axis=1)
    data['theta']=data.apply(lambda z: thetac(z),axis=1)
    data['gamma']=data.apply(lambda z: cgamma(z),axis=1)
    data['charm']=data.apply(lambda z: charm(z),axis=1)
    data['dvanna']=data.apply(lambda z: dvanna(z),axis=1)
    data['volga']=data.apply(lambda z: volga(z),axis=1)
    data['dvolga']=data.apply(lambda z: dvolga(z),axis=1)
    data['ATMvolprice']=data.apply(lambda z: ATMvolprice(z),axis=1)
    data['ATMdelta']=data.apply(lambda z: ATMdelta(z),axis=1)
    data['ATMvega']=data.apply(lambda z: ATMvega(z),axis=1)







    dat=pd.DataFrame(data.groupby('exp').apply(lambda z: yparams_cal(z,vvK)).rename('yparams'))
    dat['y1']=dat.apply(lambda z: z.yparams[0],axis=1)
    dat['y2']=dat.apply(lambda z: z.yparams[1],axis=1)
    dat['y3']=dat.apply(lambda z: z.yparams[2],axis=1)
    dat=dat.drop('yparams',axis=1)
    data=pd.merge(data,dat,left_on='exp',right_index=True)
    print(dat)

    data['adjustdelta']=data['ATMdelta']+data.vanna*data.y1+data.dvanna*data.y2+data.dvolga*data.y3
    data['vvprice']=data.GEN2VEGA*data.y1*100+data.vanna*data.y2+data.volga*data.y3+data.ATMvolprice
    res=[data[data.exp==dangyue].GEN2ATMVOL.iloc[0],data[data.exp==ciyue].GEN2ATMVOL.iloc[0],dat[dat.index==dangyue].y2.iloc[0]]

    return (res)
    
