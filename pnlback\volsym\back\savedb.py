from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS, ASYNCHRONOUS
from typing import List, Optional, Union
import time
import pandas as pd
import threading


class InfluxManager:
    """InfluxDB data management class"""

    def __init__(self, url: str = "http://168.231.2.162:8086",
                 token: str = "WyNHAbrV5RbC4niczqc5NGXPxhtO0T9hfMuz_Lsl44pYHHmtz8kOzC-mv9GGsFgyMy7ocRq_TugR7wjonn9T6Q==",
                 org: str = "nino",
                 bucket: str = "VOLS",
                 debug: bool = False):
        """Initialize InfluxDB manager"""
        self.url = url
        self.token = token
        self.org = org
        self.bucket = bucket
        self.debug = debug
        self._active_threads = []

        # Initialize InfluxDB client
        self.client = InfluxDBClient(url=self.url, token=self.token, org=self.org)
        # 创建异步 write_api
        self.write_api = self.client.write_api(write_options=ASYNCHRONOUS)

    def save_data(self, data: pd.DataFrame, measurement: str,
                  taglist: List[str] = [], fieldlist: List[str] = [], timelist: List[str] = [],
                  bucket: Optional[str] = None) -> None:
        """异步数据保存实现"""
        if data is None or data.empty:
            return

        points_start = time.time() if self.debug else 0
        target_bucket = bucket or self.bucket

        try:
            # 1. 预先过滤列并优化数据转换
            numeric_cols = fieldlist if fieldlist else data.select_dtypes(include=['int64', 'float64']).columns
            field_cols = [col for col in numeric_cols if col not in taglist and col != 'time']

            # 预先处理时间转换
            time_adjustment = None
            if timelist:
                time_adjustment = data[timelist[0]] - pd.Timedelta(hours=8)
                time_adjustment = time_adjustment.astype('int64')  # 直接使用纳秒时间戳

            # 使用 itertuples() 替代 to_dict('records')，提高性能
            points = []
            for row in data.itertuples():
                point = Point(measurement)

                if timelist:
                    point = point.time(int(time_adjustment[row.Index]))

                # 添加标签
                for t in taglist:
                    if t == 'index':
                        point = point.tag(t, str(row.Index))
                    else:
                        val = getattr(row, t, None)
                        if pd.notna(val):
                            point = point.tag(t, str(val))

                # 添加字段
                for f in field_cols:
                    val = getattr(row, f, None)
                    if pd.notna(val):
                        point = point.field(f, float(val))

                points.append(point)
            points_time = time.time() - points_start if self.debug else 0

            # 4. 异步写入点
            write_start = time.time() if self.debug else 0
            self._save_async(points, target_bucket, measurement)

            if self.debug:
                write_time = time.time() - write_start
                print(f"{measurement} InfluxDB Timing - Creation: {points_time * 1000:.2f}ms, Write: {write_time * 1000:.2f}ms, Total: {(points_time + write_time) * 1000:.2f}ms")

        except Exception as e:
            print(f"Error saving to InfluxDB: {e}")

    def _save_async(self, points, bucket, measurement):
        """异步写入实现"""

        def save_thread():
            try:
                self.write_api.write(
                    bucket=bucket,
                    org=self.org,
                    record=points,
                    write_precision='ns'
                )
            except Exception as e:
                print(f"Error in async write for {measurement}: {e}")
            finally:
                if threading.current_thread() in self._active_threads:
                    self._active_threads.remove(threading.current_thread())

        thread = threading.Thread(target=save_thread)
        thread.daemon = True
        self._active_threads.append(thread)
        thread.start()

    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """Wait for all async operations to complete"""
        for thread in self._active_threads[:]:
            thread.join(timeout=timeout)
            if thread.is_alive():
                return False
        return True

    def close(self):
        """Close InfluxDB connection"""
        try:
            self.wait_for_completion()
            if self.client:
                if hasattr(self, 'write_api'):
                    self.write_api.close()
                self.client.close()
                self.client = None
                self.write_api = None
        except Exception as e:
            print(f"Error closing InfluxDB connection: {e}")