from pykalman import <PERSON><PERSON><PERSON><PERSON><PERSON>
import numpy as np
import pandas as pd


class VolFilter(object):
    def __init__(self):
        self.kf = KalmanFilter(
            em_vars=[
                'transition_matrices',
                'transition_offsets',
                'transition_covariance',
                'observation_covariance',
                'initial_state_mean',
                'initial_state_covariance'
            ],
            n_dim_state=3
        )

    def fit_parameters(self, m, sigma, df_vols, initial_state_mean=None, n_iter=1000):
        if initial_state_mean is None:
            initial_state_mean = [0, 3, 5]
        strikes = df_vols['行权价'].unique()

        strikes = sorted(strikes)
        self.kf.n_dim_obs = len(strikes)
        self.m = m
        self.sigma = sigma

        Y = []
        observation_matrices = []
        for time, dfx in df_vols.groupby('时间'):
            dfy = dfx[dfx['行权价'] == strikes]
            Y.append((dfy['隐含波动率'] ** 2).values)

            k_m = np.log(dfy['行权价'] / dfy['远期价']).values - m
            Z = []
            Z.append(np.ones_like(k_m))
            Z.append(k_m)
            Z.append(np.sqrt(k_m ** 2 + sigma ** 2))
            Z = np.array(Z).T
            observation_matrices.append(Z)

        Y = np.array(Y)
        self.train_obs = Y

        observation_matrices = np.array(observation_matrices)

        self.kf.transition_matrices = 0.8 * np.eye(self.kf.n_dim_state, self.kf.n_dim_state)
        self.kf.transition_covariance = 0.01 * np.eye(self.kf.n_dim_state)
        self.kf.transition_offsets = np.zeros(self.kf.n_dim_state)
        self.kf.observation_matrices = observation_matrices
        self.kf.observation_covariance = 1.0 - 4 * np.eye(self.kf.n_dim_obs)
        self.kf.observation_offsets = np.zeros(self.kf.n_dim_obs)
        self.kf.initial_state_mean = np.array(initial_state_mean)
        self.kf.initial_state_covariance = 0.01 * np.eye(self.kf.n_dim_state)
        self.kf.random_state = 1

        self.kf.em(Y, n_iter=n_iter)

    def filter_states(self, df_vols):
        strikes = df_vols['行权价'].unique()
        strikes = sorted(strikes)

        Y = []
        observation_matrices = []
        for time, dfx in df_vols.groupby('时间'):
            dfy = dfx[dfx['行权价'] == strikes]
            Y.append((dfy['隐含波动率'] ** 2).values)

            k_m = np.log(dfy['行权价'] / dfy['远期价']).values - self.m
            Z = []
            Z.append(np.ones_like(k_m))
            Z.append(k_m)
            Z.append(np.sqrt(k_m ** 2 + self.sigma ** 2))
            Z = np.array(Z).T
            observation_matrices.append(Z)

        Y = np.array(Y)
        self.filter_obs = Y

        observation_matrices = np.array(observation_matrices)
        
        self.kf.observation_matrices = observation_matrices
        filteres_states = self.kf.filter(Y)[0]

        return filteres_states

if __name__ == '__main__':
    df_params = pd.read_csv('data/svi_parameters_20241220.csv', index_col=0)
    m, sigma = df_params[['m', 'sigma']].median()