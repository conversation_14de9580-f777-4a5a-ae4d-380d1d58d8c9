import numpy as np
from scipy.optimize import minimize

class VolatilityModel:
    """波动率计算模型"""
    
    def __init__(self, rd=1.0, wd=1.3):
        """初始化模型参数"""
        self.rd = rd
        self.wd = wd
        self.params = None

    def calculate(self, spot, strike):
        """计算给定参数下的波动率"""
        if self.params is None:
            raise ValueError("模型参数未设置")
            
        base_vol, wing, rr = self.params
        
        spot = np.asarray(spot)
        strike = np.asarray(strike)
        
        temp = np.abs(spot - strike) / (spot * 0.05)
        wing_adj = np.power(temp, self.wd) * wing * 0.01
        rr_adj = np.power(temp, self.rd) * rr * 0.01
        
        vol = np.where(
            spot > strike,
            base_vol + wing_adj - rr_adj,
            base_vol + wing_adj + rr_adj
        )
        
        return float(vol) if vol.size == 1 else vol

def fit_svi_model(config, exp_data, exp, last_exp_data=None):
    """拟合波动率模型并计算特征值"""
    # 准备数据
    spot = exp_data['forward'].values
    strike = exp_data['K'].values
    market_sigma = exp_data['market_sigma'].values
    
    # 修改权重计算，bid=ask时权重设为0
    weights = exp_data['vega'].values
    weights = np.where(exp_data['bid'] == exp_data['ask'], 0, weights)
    weights /= weights.sum()
    
    # 创建模型
    model = VolatilityModel(rd=config.get('rd', 1.0), wd=config.get('wd', 1.3))
    
    # 拟合参数设置
    # 设置参数优化边界
    bounds = [
        (0.01, 2),  # base_vol: 基础波动率范围 0.1-0.5 (10%-50%)
        (0.0, 5000.0),  # wing: 翼展参数范围 0-5 (0%-500%)
        (-2000.0, 2000.0)  # rr: 风险逆转参数范围 -2-2 (-200%-200%)
    ]
    initial_params = ([last_exp_data[exp].get('base_vol', 0.2),
                      last_exp_data[exp].get('wing', 1.0),
                      last_exp_data[exp].get('rr', 0.0)]
                     if last_exp_data is not None and exp in last_exp_data 
                     else [0.2, 1.0, 0.0])

    # 定义目标函数
    def objective(params):
        model.params = params
        return np.sum((weights * (model.calculate(spot, strike) - market_sigma)) ** 2)

    # 执行拟合
    result = minimize(
        objective,
        initial_params,
        bounds=bounds,
        method='L-BFGS-B'
    )
    
    if not result.success:
        return None, None, None
        
    model.params = result.x
    
    # 计算拟合结果
    sigma_fit = model.calculate(spot, strike)
    rmse_error = np.sqrt(np.mean((weights * (sigma_fit - market_sigma)) ** 2))
    r2 = 1 - np.sum((market_sigma - sigma_fit) ** 2) / np.sum((market_sigma - np.mean(market_sigma)) ** 2)
    
    # 计算ATM特征值
    idx_atm = np.abs(np.log(strike/spot)).argmin()
    k_atm = strike[idx_atm]
    vol_atm = model.calculate(spot[idx_atm:idx_atm+1], k_atm)
    
    # 计算偏斜度和凸度
    dk = 0.05 * spot[idx_atm]
    vol_up = model.calculate(spot[idx_atm:idx_atm+1], k_atm + dk)
    vol_down = model.calculate(spot[idx_atm:idx_atm+1], k_atm - dk)
    
    skew = (vol_up - vol_down) / (2 * dk)
    convexity = (vol_up + vol_down - 2 * vol_atm) / (dk * dk)
    
    # 返回结果
    voltime = {
        **dict(zip(['base_vol', 'wing', 'rr'], model.params)),
        'wd': model.wd, 'rd': model.rd,
        'atm_vol': vol_atm, 'skew': skew, 'convexity': convexity,
        'rmse_error': rmse_error, 'r2': r2
    }
    
    features = {
        'atm_vol': vol_atm, 'skew': skew, 'convexity': convexity,
        'otm_slope': (vol_up - vol_atm) / dk,
        'itm_slope': (vol_atm - vol_down) / dk
    }
    
    return sigma_fit, voltime, features
