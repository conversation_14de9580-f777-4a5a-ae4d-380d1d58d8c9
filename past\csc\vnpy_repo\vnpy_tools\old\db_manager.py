#%%
"""Manager tick date"""
import datetime
import tqdm
import pandas as pd

from OmmDatabase import OmmDatabase

from vnpy.trader.constant import Exchange
from vnpy.trader.database import database_manager
from vnpy.trader.object import TickData

#%%
EXCHANGE_MAP = {
    'shfe': Exchange.SHFE,
    'dce': Exchange.DCE,
    'zce': Exchange.CZCE
}


class DBTickManager(object):
    """"""

    def __init__():
        """"""
        pass


    def down_from_OMM(DBPATH,date,commodity,main):
        path = DBPATH + 'MarketDataService/default_mkt/{}/'.format(commodity)
        testDB = OmmDatabase(path)
        df = testDB.read_file(path, date)
        df['SystemStamp'] = pd.to_datetime(
            df['SystemStamp'] * 10, format='%Y%m%d%H%M%S%f')
        df = df[df['instrumentId'] == main]
        df['dV'] = df['tradedVolume'] - df['tradedVolume'].shift(1)
        df['dV'].fillna(0,inplace=True)
        return df

    @classmethod
    def save_from_server(cls,DBPATH, date, commodity, exchange,main):
        """从OMM服务器存储数据
            DBPATH: 最顶层文件夹, 下一层就是exchange_future/
        """
        df = cls.down_from_OMM(DBPATH,date,commodity,main).copy()
        time_index = pd.Series(df.SystemStamp, index=df.index).dt.time

        mask = (
            (time_index >= datetime.time(21, 0)) & (time_index <= datetime.time(23, 0))
            | (time_index >= datetime.time(9, 0)) & (time_index <= datetime.time(10, 15))
            | (time_index >= datetime.time(10, 30)) & (time_index <= datetime.time(11, 30))
            | (time_index >= datetime.time(13, 30)) & (time_index <= datetime.time(15, 0))
        )
        df = df[mask].copy()
        vt_exchange = EXCHANGE_MAP[exchange]

        # insert to db
        ticks = []
        start = None
        count = 0

        for _, x in tqdm.tqdm(df.iterrows(), total=df.shape[0]):
            tick = TickData(
                gateway_name='ommdb',
                symbol=x['instrumentId'],
                exchange=vt_exchange,
                datetime=x['SystemStamp'].to_pydatetime(),

                volume=x['tradedVolume'],
                turnover=x['turnover'],
                last_price=x['lastPriceOnMarket'],
                limit_up=x['upperLimit'],
                limit_down=x['lowerLimit'],

                bid_price_1=x['Bid1Price'],
                bid_price_2=x['Bid2Price'],
                bid_price_3=x['Bid3Price'],
                bid_price_4=x['Bid4Price'],
                bid_price_5=x['Bid5Price'],

                ask_price_1=x['Ask1Price'],
                ask_price_2=x['Ask2Price'],
                ask_price_3=x['Ask3Price'],
                ask_price_4=x['Ask4Price'],
                ask_price_5=x['Ask5Price'],

                bid_volume_1=x['Bid1Volume'],
                bid_volume_2=x['Bid2Volume'],
                bid_volume_3=x['Bid3Volume'],
                bid_volume_4=x['Bid4Volume'],
                bid_volume_5=x['Bid5Volume'],

                ask_volume_1=x['Ask1Volume'],
                ask_volume_2=x['Ask2Volume'],
                ask_volume_3=x['Ask3Volume'],
                ask_volume_4=x['Ask4Volume'],
                ask_volume_5=x['Ask5Volume'],
            )
            ticks.append(tick)
            count += 1
            if not start:
                start = tick.datetime
        end = tick.datetime
        print('Saving...')
        database_manager.save_tick_data(ticks)
        print(
            f'Finished insert, start:{start}, end:{end}, total ticks:{count}')
        
#%%

if __name__ == '__main__':
    date = '2021-06-10'
    commodity = 'RB'
    exchange = 'shfe'
    DBPATH = f'D:/Commodity/{date}/{exchange}_future/'
    main = 'rb2109'
    DBTickManager.save_from_server(DBPATH, date, commodity, exchange,main)

    date = '2021-06-10'
    commodity = 'HC'
    exchange = 'shfe'
    DBPATH = f'D:/Commodity/{date}/{exchange}_future/'
    main = 'hc2109'
    DBTickManager.save_from_server(DBPATH, date, commodity, exchange,main)
    



# %%
