{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeepLOB: 基于深度卷积神经网络的限价订单簿预测\n", "\n", "### 作者: <PERSON><PERSON><PERSON>, <PERSON> 和 <PERSON>\n", "牛津大学牛津-曼彻斯特定量金融研究所, 工程科学系\n", "\n", "本 Jupyter notebook 用于演示我们最近发表在 IEEE Transactions on Signal Processing 的论文[2]。我们使用 FI-2010[1] 数据集并在此展示模型架构的构建过程。\n", "\n", "### 数据:\n", "FI-2010 数据集是公开可用的,感兴趣的读者可以查看他们的论文[1]。数据集可以从以下地址下载: https://etsin.fairdata.fi/dataset/73eb48d7-4dbc-4a10-a52a-da745b47a649\n", "\n", "或者,本 notebook 会自动下载数据,也可以从以下地址获取:\n", "\n", "https://drive.google.com/drive/folders/1Xen3aRid9ZZhFqJRgEMyETNazk02cNmv?usp=sharing\n", "\n", "### 参考文献:\n", "[1] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>chmark dataset for mid‐price forecasting of limit order book data with machine learning methods. Journal of Forecasting. 2018 Dec;37(8):852-66. https://arxiv.org/abs/1705.03233\n", "\n", "[2] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>: Deep convolutional neural networks for limit order books. IEEE Transactions on Signal Processing. 2019 Mar 25;67(11):3001-12. https://arxiv.org/abs/1808.03668\n", "\n", "### 本 notebook 运行于 PyTorch 1.9.0。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据已存在。\n"]}], "source": ["import os \n", "if not os.path.isfile('data.zip'):\n", "    !wget https://raw.githubusercontent.com/zcakhaa/DeepLOB-Deep-Convolutional-Neural-Networks-for-Limit-Order-Books/master/data/data.zip\n", "    !unzip -n data.zip\n", "    print('数据已下载。')\n", "else:\n", "    print('数据已存在。')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 加载包\n", "import pandas as pd\n", "import pickle\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "from tqdm import tqdm \n", "from sklearn.metrics import accuracy_score, classification_report\n", "\n", "import torch\n", "import torch.nn.functional as F\n", "from torch.utils import data\n", "from torchinfo import summary\n", "import torch.nn as nn\n", "import torch.optim as optim"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cpu\n"]}], "source": ["device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "print(device)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def prepare_x(data):\n", "    df1 = data[:40, :].T\n", "    return np.array(df1)\n", "\n", "def get_label(data):\n", "    lob = data[-5:, :].T\n", "    return lob\n", "\n", "def data_classification(X, Y, T):\n", "    [N, D] = X.shape\n", "    df = np.array(X)\n", "\n", "    dY = np.array(Y)\n", "\n", "    dataY = dY[T - 1:N]\n", "\n", "    dataX = np.zeros((N - T + 1, T, D))\n", "    for i in range(T, N + 1):\n", "        dataX[i - T] = df[i - T:i, :]\n", "\n", "    return dataX, dataY\n", "\n", "def torch_data(x, y):\n", "    x = torch.from_numpy(x)\n", "    x = torch.unsqueeze(x, 1)\n", "    y = torch.from_numpy(y)\n", "    y = F.one_hot(y, num_classes=3)\n", "    return x, y"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["class Dataset(data.Dataset):\n", "    \"\"\"定义数据集类\"\"\"\n", "    def __init__(self, data, k, num_classes, T):\n", "        \"\"\"初始化\"\"\"\n", "        self.k = k\n", "        self.num_classes = num_classes\n", "        self.T = T\n", "            \n", "        x = prepare_x(data)\n", "        y = get_label(data)\n", "        x, y = data_classification(x, y, self.T)\n", "        y = y[:,self.k] - 1\n", "        self.length = len(x)\n", "\n", "        x = torch.from_numpy(x)\n", "        self.x = torch.unsqueeze(x, 1)\n", "        self.y = torch.from_numpy(y)\n", "\n", "    def __len__(self):\n", "        \"\"\"返回样本总数\"\"\"\n", "        return self.length\n", "\n", "    def __getitem__(self, index):\n", "        \"\"\"生成数据样本\"\"\"\n", "        return self.x[index], self.y[index]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 数据准备\n", "\n", "我们使用了他们工作中通过小数精度方法标准化的无拍卖数据集。前七天作为训练数据,最后三天作为测试数据。从训练集中抽取20%作为验证集,用于监控过拟合行为。\n", "\n", "FI-2010数据集的前40列是限价订单簿的10个层级的买卖信息,我们在网络中只使用这40个特征。数据集的最后5列是不同预测时间范围的标签。"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(149, 203800) (149, 50950) (149, 139587)\n"]}], "source": ["# 请将 data_path 改为您的本地路径\n", "# data_path = '/nfs/home/<USER>/limit_order_book/data'\n", "\n", "dec_data = np.loadtxt('Train_Dst_NoAuction_DecPre_CF_7.txt')\n", "dec_train = dec_data[:, :int(np.floor(dec_data.shape[1] * 0.8))]\n", "dec_val = dec_data[:, int(np.floor(dec_data.shape[1] * 0.8)):]\n", "\n", "dec_test1 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_7.txt')\n", "dec_test2 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_8.txt')\n", "dec_test3 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_9.txt')\n", "dec_test = np.hstack((dec_test1, dec_test2, dec_test3))\n", "\n", "print(dec_train.shape, dec_val.shape, dec_test.shape)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([203701, 1, 100, 40]) torch.<PERSON><PERSON>([203701])\n"]}], "source": ["batch_size = 64\n", "\n", "dataset_train = Dataset(data=dec_train, k=4, num_classes=3, T=100)\n", "dataset_val = Dataset(data=dec_val, k=4, num_classes=3, T=100)\n", "dataset_test = Dataset(data=dec_test, k=4, num_classes=3, T=100)\n", "\n", "train_loader = torch.utils.data.DataLoader(dataset=dataset_train, batch_size=batch_size, shuffle=True)\n", "val_loader = torch.utils.data.DataLoader(dataset=dataset_val, batch_size=batch_size, shuffle=False)\n", "test_loader = torch.utils.data.DataLoader(dataset=dataset_test, batch_size=batch_size, shuffle=False)\n", "\n", "print(dataset_train.x.shape, dataset_train.y.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型架构\n", "\n", "请在我们的论文中查看模型架构的详细讨论。"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["class deeplob(nn.Mo<PERSON>le):\n", "    def __init__(self, y_len):\n", "        super().__init__()\n", "        self.y_len = y_len\n", "        \n", "        # 卷积块\n", "        self.conv1 = nn.Sequential(\n", "            nn.Conv2d(in_channels=1, out_channels=32, kernel_size=(1,2), stride=(1,2)),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "        )\n", "        self.conv2 = nn.Sequential(\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(1,2), stride=(1,2)),\n", "            nn.<PERSON>(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),\n", "            nn.<PERSON>(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),\n", "            nn.<PERSON>(),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "        )\n", "        self.conv3 = nn.Sequential(\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(1,10)),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.Conv2d(in_channels=32, out_channels=32, kernel_size=(4,1)),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "        )\n", "        \n", "        # Inception 模块\n", "        self.inp1 = nn.Sequential(\n", "            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=(1,1), padding='same'),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=(3,1), padding='same'),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "        )\n", "        self.inp2 = nn.Sequential(\n", "            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=(1,1), padding='same'),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "            nn.Conv2d(in_channels=64, out_channels=64, kernel_size=(5,1), padding='same'),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "        )\n", "        self.inp3 = nn.Sequential(\n", "            nn.MaxPool2d((3, 1), stride=(1, 1), padding=(1, 0)),\n", "            nn.Conv2d(in_channels=32, out_channels=64, kernel_size=(1,1), padding='same'),\n", "            nn.LeakyReLU(negative_slope=0.01),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "        )\n", "        \n", "        # LSTM 层\n", "        self.lstm = nn.LSTM(input_size=192, hidden_size=64, num_layers=1, batch_first=True)\n", "        self.fc1 = nn.Linear(64, self.y_len)\n", "\n", "    def forward(self, x):\n", "        # h0: (隐藏层数量, batch size, 隐藏层大小)\n", "        h0 = torch.zeros(1, x.size(0), 64).to(device)\n", "        c0 = torch.zeros(1, x.size(0), 64).to(device)\n", "    \n", "        x = self.conv1(x)\n", "        x = self.conv2(x)\n", "        x = self.conv3(x)\n", "        \n", "        x_inp1 = self.inp1(x)\n", "        x_inp2 = self.inp2(x)\n", "        x_inp3 = self.inp3(x)  \n", "        \n", "        x = torch.cat((x_inp1, x_inp2, x_inp3), dim=1)\n", "        \n", "        x = x.permute(0, 2, 1, 3)\n", "        x = torch.reshape(x, (-1, x.shape[1], x.shape[2]))\n", "        \n", "        x, _ = self.lstm(x, (h0, c0))\n", "        x = x[:, -1, :]\n", "        x = self.fc1(x)\n", "        forecast_y = torch.softmax(x, dim=1)\n", "        \n", "        return forecast_y"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["deeplob(\n", "  (conv1): Sequential(\n", "    (0): Conv2d(1, 32, kernel_size=(1, 2), stride=(1, 2))\n", "    (1): LeakyReLU(negative_slope=0.01)\n", "    (2): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (3): Conv2d(32, 32, kernel_size=(4, 1), stride=(1, 1))\n", "    (4): LeakyReLU(negative_slope=0.01)\n", "    (5): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (6): Conv2d(32, 32, kernel_size=(4, 1), stride=(1, 1))\n", "    (7): LeakyReLU(negative_slope=0.01)\n", "    (8): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "  )\n", "  (conv2): Sequential(\n", "    (0): Conv2d(32, 32, kernel_size=(1, 2), stride=(1, 2))\n", "    (1): <PERSON><PERSON>()\n", "    (2): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (3): Conv2d(32, 32, kernel_size=(4, 1), stride=(1, 1))\n", "    (4): <PERSON><PERSON>()\n", "    (5): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (6): Conv2d(32, 32, kernel_size=(4, 1), stride=(1, 1))\n", "    (7): <PERSON><PERSON>()\n", "    (8): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "  )\n", "  (conv3): Sequential(\n", "    (0): Conv2d(32, 32, kernel_size=(1, 10), stride=(1, 1))\n", "    (1): LeakyReLU(negative_slope=0.01)\n", "    (2): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (3): Conv2d(32, 32, kernel_size=(4, 1), stride=(1, 1))\n", "    (4): LeakyReLU(negative_slope=0.01)\n", "    (5): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (6): Conv2d(32, 32, kernel_size=(4, 1), stride=(1, 1))\n", "    (7): LeakyReLU(negative_slope=0.01)\n", "    (8): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "  )\n", "  (inp1): Sequential(\n", "    (0): Conv2d(32, 64, kernel_size=(1, 1), stride=(1, 1), padding=same)\n", "    (1): LeakyReLU(negative_slope=0.01)\n", "    (2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (3): Conv2d(64, 64, kernel_size=(3, 1), stride=(1, 1), padding=same)\n", "    (4): LeakyReLU(negative_slope=0.01)\n", "    (5): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "  )\n", "  (inp2): Sequential(\n", "    (0): Conv2d(32, 64, kernel_size=(1, 1), stride=(1, 1), padding=same)\n", "    (1): LeakyReLU(negative_slope=0.01)\n", "    (2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (3): Conv2d(64, 64, kernel_size=(5, 1), stride=(1, 1), padding=same)\n", "    (4): LeakyReLU(negative_slope=0.01)\n", "    (5): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "  )\n", "  (inp3): Sequential(\n", "    (0): MaxPool2d(kernel_size=(3, 1), stride=(1, 1), padding=(1, 0), dilation=1, ceil_mode=False)\n", "    (1): Conv2d(32, 64, kernel_size=(1, 1), stride=(1, 1), padding=same)\n", "    (2): LeakyReLU(negative_slope=0.01)\n", "    (3): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "  )\n", "  (lstm): LSTM(192, 64, batch_first=True)\n", "  (fc1): Linear(in_features=64, out_features=3, bias=True)\n", ")"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["model = deeplob(y_len = dataset_train.num_classes)\n", "model.to(device)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["criterion = nn.CrossEntropyLoss()\n", "optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型训练"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# 封装训练循环的函数\n", "def batch_gd(model, criterion, optimizer, train_loader, test_loader, epochs):\n", "    \n", "    train_losses = np.zeros(epochs)\n", "    test_losses = np.zeros(epochs)\n", "    best_test_loss = np.inf\n", "    best_test_epoch = 0\n", "\n", "    for it in tqdm(range(epochs)):\n", "        \n", "        model.train()\n", "        t0 = datetime.now()\n", "        train_loss = []\n", "        for inputs, targets in train_loader:\n", "            # 移动数据到GPU\n", "            inputs, targets = inputs.to(device, dtype=torch.float), targets.to(device, dtype=torch.int64)\n", "            # 梯度清零\n", "            optimizer.zero_grad()\n", "            # 前向传播\n", "            outputs = model(inputs)\n", "            loss = criterion(outputs, targets)\n", "            # 反向传播和优化\n", "            loss.backward()\n", "            optimizer.step()\n", "            train_loss.append(loss.item())\n", "        # 获取训练损失和测试损失\n", "        train_loss = np.mean(train_loss)\n", "    \n", "        model.eval()\n", "        test_loss = []\n", "        for inputs, targets in test_loader:\n", "            inputs, targets = inputs.to(device, dtype=torch.float), targets.to(device, dtype=torch.int64)      \n", "            outputs = model(inputs)\n", "            loss = criterion(outputs, targets)\n", "            test_loss.append(loss.item())\n", "        test_loss = np.mean(test_loss)\n", "\n", "        # 保存损失\n", "        train_losses[it] = train_loss\n", "        test_losses[it] = test_loss\n", "        \n", "        if test_loss < best_test_loss:\n", "            torch.save(model, './best_val_model_pytorch')\n", "            best_test_loss = test_loss\n", "            best_test_epoch = it\n", "            print('模型已保存')\n", "\n", "        dt = datetime.now() - t0\n", "        print(f'轮次 {it+1}/{epochs}, 训练损失: {train_loss:.4f}, \\\n", "          验证损失: {test_loss:.4f}, 用时: {dt}, 最佳验证轮次: {best_test_epoch}')\n", "\n", "    return train_losses, test_losses"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  2%|▏         | 1/50 [10:30<8:34:58, 630.58s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["模型已保存\n", "轮次 1/50, 训练损失: 0.9319,           验证损失: 0.9983, 用时: 0:10:30.580247, 最佳验证轮次: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  2%|▏         | 1/50 [13:33<11:04:41, 813.91s/it]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[20]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m train_losses, val_losses = \u001b[43mbatch_gd\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcriterion\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptimizer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[32m      2\u001b[39m \u001b[43m                                    \u001b[49m\u001b[43mtrain_loader\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mval_loader\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mepochs\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m50\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[19]\u001b[39m\u001b[32m, line 20\u001b[39m, in \u001b[36mbatch_gd\u001b[39m\u001b[34m(model, criterion, optimizer, train_loader, test_loader, epochs)\u001b[39m\n\u001b[32m     18\u001b[39m optimizer.zero_grad()\n\u001b[32m     19\u001b[39m \u001b[38;5;66;03m# 前向传播\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m outputs = \u001b[43mmodel\u001b[49m\u001b[43m(\u001b[49m\u001b[43minputs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     21\u001b[39m loss = criterion(outputs, targets)\n\u001b[32m     22\u001b[39m \u001b[38;5;66;03m# 反向传播和优化\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1739\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1737\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1738\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1739\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1750\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1745\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1746\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1747\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1748\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1749\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1750\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1752\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1753\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[16]\u001b[39m\u001b[32m, line 76\u001b[39m, in \u001b[36mdeeplob.forward\u001b[39m\u001b[34m(self, x)\u001b[39m\n\u001b[32m     74\u001b[39m x = \u001b[38;5;28mself\u001b[39m.conv1(x)\n\u001b[32m     75\u001b[39m x = \u001b[38;5;28mself\u001b[39m.conv2(x)\n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m x = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconv3\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     78\u001b[39m x_inp1 = \u001b[38;5;28mself\u001b[39m.inp1(x)\n\u001b[32m     79\u001b[39m x_inp2 = \u001b[38;5;28mself\u001b[39m.inp2(x)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1739\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1737\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1738\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1739\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1750\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1745\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1746\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1747\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1748\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1749\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1750\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1752\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1753\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\container.py:250\u001b[39m, in \u001b[36mSequential.forward\u001b[39m\u001b[34m(self, input)\u001b[39m\n\u001b[32m    248\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m):\n\u001b[32m    249\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m module \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m         \u001b[38;5;28minput\u001b[39m = \u001b[43mmodule\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    251\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28minput\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1739\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1737\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1738\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1739\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1750\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1745\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1746\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1747\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1748\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1749\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1750\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1752\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1753\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\conv.py:554\u001b[39m, in \u001b[36mConv2d.forward\u001b[39m\u001b[34m(self, input)\u001b[39m\n\u001b[32m    553\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;28minput\u001b[39m: Tensor) -> Tensor:\n\u001b[32m--> \u001b[39m\u001b[32m554\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_conv_forward\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mweight\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mbias\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\veighna_studio\\Lib\\site-packages\\torch\\nn\\modules\\conv.py:549\u001b[39m, in \u001b[36mConv2d._conv_forward\u001b[39m\u001b[34m(self, input, weight, bias)\u001b[39m\n\u001b[32m    537\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.padding_mode != \u001b[33m\"\u001b[39m\u001b[33mzeros\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    538\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m F.conv2d(\n\u001b[32m    539\u001b[39m         F.pad(\n\u001b[32m    540\u001b[39m             \u001b[38;5;28minput\u001b[39m, \u001b[38;5;28mself\u001b[39m._reversed_padding_repeated_twice, mode=\u001b[38;5;28mself\u001b[39m.padding_mode\n\u001b[32m   (...)\u001b[39m\u001b[32m    547\u001b[39m         \u001b[38;5;28mself\u001b[39m.groups,\n\u001b[32m    548\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m549\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mF\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconv2d\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    550\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mweight\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbias\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mstride\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpadding\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mdilation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgroups\u001b[49m\n\u001b[32m    551\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["train_losses, val_losses = batch_gd(model, criterion, optimizer, \n", "                                    train_loader, val_loader, epochs=50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(15,6))\n", "plt.plot(train_losses, label='训练损失')\n", "plt.plot(val_losses, label='验证损失')\n", "plt.legend()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = torch.load('best_val_model_pytorch')\n", "\n", "n_correct = 0.\n", "n_total = 0.\n", "for inputs, targets in test_loader:\n", "    # 移动到GPU\n", "    inputs, targets = inputs.to(device, dtype=torch.float), targets.to(device, dtype=torch.int64)\n", "\n", "    # 前向传播\n", "    outputs = model(inputs)\n", "    \n", "    # 获取预测结果\n", "    # torch.max 返回最大值和对应的索引\n", "    _, predictions = torch.max(outputs, 1)\n", "\n", "    # 更新计数\n", "    n_correct += (predictions == targets).sum().item()\n", "    n_total += targets.shape[0]\n", "\n", "test_acc = n_correct / n_total\n", "print(f\"测试准确率: {test_acc:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_targets = []\n", "all_predictions = []\n", "\n", "for inputs, targets in test_loader:\n", "    # 移动到GPU\n", "    inputs, targets = inputs.to(device, dtype=torch.float), targets.to(device, dtype=torch.int64)\n", "\n", "    # 前向传播\n", "    outputs = model(inputs)\n", "    \n", "    # 获取预测结果\n", "    _, predictions = torch.max(outputs, 1)\n", "\n", "    all_targets.append(targets.cpu().numpy())\n", "    all_predictions.append(predictions.cpu().numpy())\n", "\n", "all_targets = np.concatenate(all_targets)    \n", "all_predictions = np.concatenate(all_predictions)    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('准确率:', accuracy_score(all_targets, all_predictions))\n", "print(classification_report(all_targets, all_predictions, digits=4))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}