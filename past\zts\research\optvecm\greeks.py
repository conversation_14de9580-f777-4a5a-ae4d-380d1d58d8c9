# -*- coding:utf-8 -*-
from __future__ import print_function
import numpy as np
import math
from scipy.stats import norm


def imvol1(S0, X, r, q, t, mktprice):
    sigma = 0.1  # initial volatility
    C = P = 0
    upper = 1
    lower = 0
    n = 0
    while abs(C - mktprice) > 1e-6:
        n = n + 1
        d1 = (np.log(S0 / X) + (r - q + sigma ** 2 / 2) * t) / (sigma * np.sqrt(t))
        d2 = d1 - sigma * np.sqrt(t)
        C = S0 * np.exp(-q * t) * norm.cdf(d1) - X * np.exp(-r * t) * norm.cdf(d2)
        P = X * np.exp(-r * t) * norm.cdf(-d2) - S0 * np.exp(-q * t) * norm.cdf(-d1)
        if C - mktprice > 0:
            upper = sigma
            sigma = (sigma + lower) / 2
        else:
            lower = sigma
            sigma = (sigma + upper) / 2
        if n > 300:  # 迭代次数过多的话
            sigma = 0.0
            break
    return sigma


def ImpVolCaller(MktPrice, Strike, Expiry, Asset, IntRate, Dividend, Sigma, error):
    n = 1
    Volatility = Sigma  # 初始值
    dv = error + 1
    while abs(dv) > error:
        d1 = np.log(Asset / Strike) + (IntRate - Dividend + 0.5 * Volatility ** 2) * Expiry
        d1 = d1 / (Volatility * np.sqrt(Expiry))
        d2 = d1 - Volatility * np.sqrt(Expiry)
        PriceError = Asset * math.exp(-Dividend * Expiry) * norm.cdf(d1) - Strike * math.exp(
            -IntRate * Expiry) * norm.cdf(d2) - MktPrice
        Vega1 = Asset * np.sqrt(Expiry / 3.1415926 / 2) * math.exp(-0.5 * d1 ** 2)
        dv = PriceError / Vega1
        Volatility = Volatility - dv  # 修正隐含波动率
        n = n + 1

        if n > 300:  # 迭代次数过多的话
            ImpVolCall22 = 0.0
            break

        ImpVolCall = Volatility

    return ImpVolCall


def BSOptionISDEstimate(iopt, S, X, r, q, tyr, optprice):
    Sq = S * math.exp(-q * tyr)
    pvX = X * math.exp(-r * tyr)
    p = 3.141592653
    calc0 = optprice - iopt * 0.5 * (Sq - pvX)
    calc1 = (calc0 ** 2) - ((Sq - pvX) ** 2) / p
    if calc1 < 0:
        BSOptionISDEstimate = -1
    else:
        calc2 = calc0 + np.sqrt(calc1)
        BSOptionISDEstimate = np.sqrt(2 * p / tyr) * calc2 / (Sq + pvX)
    return BSOptionISDEstimate


def BSOptionISDGoalSeekNR(iopt, S, X, r, q, tyr, optprice):
    fval = 1
    atol = 0.001
    n = 0
    sigmanow = BSOptionISDEstimate(iopt, S, X, r, q, tyr, optprice)

    if sigmanow <= 0:
        sigmanow = np.sqrt(2 * abs(math.log(S / X) + (r - q) * tyr) / tyr)
    while abs(fval) > atol and n<5000:
        n=n+1
        fval = BSOptionValue(iopt, S, X, r, q, tyr, sigmanow) - optprice
        fdashval = BSOptionVega(S, X, r, q, tyr, sigmanow)
        if fdashval == 0:
            sigmanow = 0
        else:
            sigmanow = sigmanow - (fval / fdashval)
    if sigmanow < 0 or sigmanow > 1:
        sigmanow = 0
    return sigmanow


def BSOptionValue(iopt, S, X, r, q, tyr, sigma):
    eqt = math.exp(-q * tyr)
    ert = math.exp(-r * tyr)
    if S > 0 and X > 0 and tyr > 0 and sigma > 0:
        NDOne = norm.cdf(iopt * BSDOne(S, X, r, q, tyr, sigma))
        NDTwo = norm.cdf(iopt * BSDTwo(S, X, r, q, tyr, sigma))
        BSOptionValue = iopt * (S * eqt * NDOne - X * ert * NDTwo)
    else:
        BSOptionValue = -1

    return BSOptionValue


def BSDOne(S, X, r, q, tyr, sigma):
    BSDOne = (math.log(S / X) + (r - q + 0.5 * sigma ** 2) * tyr) / (sigma * math.sqrt(tyr))
    return BSDOne


def BSDTwo(S, X, r, q, tyr, sigma):
    BSDTwo = (math.log(S / X) + (r - q - 0.5 * sigma ** 2) * tyr) / (sigma * math.sqrt(tyr))
    return BSDTwo


def BSNdashDOne(S, X, r, q, tyr, sigma):
    BSNdashDOne = norm.cdf(BSDOne(S, X, r, q, tyr, sigma))
    return BSNdashDOne


def BSOptionVega(S, X, r, q, tyr, sigma):
    BSOptionVega = S * math.sqrt(tyr) * BSNdashDOne(S, X, r, q, tyr, sigma) * math.exp(-q * tyr)
    return BSOptionVega
