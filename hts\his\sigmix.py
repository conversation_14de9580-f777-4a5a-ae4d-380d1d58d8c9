import os
import sys

import pandas as pd
import numpy as np
import datetime

from db_solve.csvreader import readmd, loadtrade
from db_solve import eachFile, dttype, mddiff, mddiffpnl, drop_col_nan


def mixdata(str1, fut1, str2, fut2, datetoday, key, mult, silmode, str3=None, fut3=None, key3=None):
    # a50
    mdData2 = readmd(str2, [fut2, ], datetoday, tradetime00, )
    if key == 'ForQuoteSysID':
        mdData2 = mdData2[mdData2['Source'] == 7]
    elif mode == 'etffast':
        mdData2 = mdData2[mdData2['Source'] == 0]
    elif fut2[0:2] == 'SH':
        mdData2 = mdData2[mdData2['Source'] == 4]

    if silmode == 'onlytrade':
        mdData2 = mdData2[mdData2['tradedVol'] != 0]
        print('md only trade')
    elif silmode == 'onlymid':
        mdData2 = mdData2[mdData2['tradedVol'] == 0]
        print('md only trade')
    mdData2['fut'] = mdData2['mid']
    mdData2[key] = mdData2[key].mask(mdData2[key] == 0).ffill(downcast='infer')
    mdData2['dslast'] = mdData2[key].diff(1)
    mdData2['dsf-1'] = mdData2['dslast']
    mdData2 = mddiff(mdData2, key, 'dsf', dsflist)
    mdData2['dsmixfut'] = mdData2['mixfut'].diff(1)
    # mdData2 = mdData2[(mdData2['dsf-1'] != 0) | (mdData2['dslast'] != 0)]
    mdData2['dt-1'] = mdData2['timestamp_str'].diff(1) / np.timedelta64(1, 's')
    mdData2 = mdData2.add_suffix('_x')

    mdData1 = readmd(str1, [fut1, ], datetoday, tradetime00, )
    mdData1['fut'] = mdData1['mid']
    if under[0:2] == 'SH':
        mdData1 = mdData1[mdData1['Source'] == 4]
    mdData1 = mddiff(mdData1, 'fut', 'dsf', dsflist, abs=True)
    mdData1['dt-1'] = mdData1['timestamp_str'].diff(1) / np.timedelta64(1, 's')
    mdData1['mean20'] = mdData1['fut'].rolling(window=20).mean().fillna(mdData1['fut'][0])
    mdData1['dma'] = mdData1['mean20'].diff(1)
    mdData1 = mdData1.add_suffix('_y')

    mixData = pd.merge_asof(mdData2, mdData1, left_index=True, right_index=True,
                            direction=mixmode,
                            tolerance=pd.Timedelta('1000000000ms'), allow_exact_matches=True)
    mixData = pd.merge_asof(mixData, mdData1[['timestamp_str_y']].rename(columns={'timestamp_str_y': 'timestamp2near'}),
                            left_index=True, right_index=True,
                            direction='nearest',
                            tolerance=pd.Timedelta('1000000000ms'), allow_exact_matches=True)

    mixData['dt_mdmix'] = (mixData['timestamp_str_x'] - mixData['timestamp_str_y']) / np.timedelta64(1, 's')
    mixData = drop_col_nan(mixData, 'dt_mdmix', is_zero=False)
    mixData = dttype(mixData, 'dt_mdmix', 'dtmdmix_type')

    mixData['dt_mdmix2near'] = (mixData['timestamp_str_x'] - mixData['timestamp2near']) / np.timedelta64(1, 's')
    mixData = drop_col_nan(mixData, 'dt_mdmix2near', is_zero=False)

    mixData['BASIS'] = -(mixData['mid_y'] - mixData[key + '_x'] * mult)
    mixData = mddiff(mixData, 'BASIS', 'dsf', dsflist)

    print('mix done')

    if str3 != '':
        mdData3 = readmd(str3, [fut3, ], datetoday, tradetime00, )
        if key3 == 'ForQuoteSysID':
            mdData3 = mdData3[mdData3['Source'] == 7]
        elif mode == 'fut':
            mdData2 = mdData2[mdData2['Source'] == 4]
        mdData3['dslast'] = mdData3[key3].diff(1)
        mdData3 = mdData3[['timestamp_str', key3, 'dslast']]
        mdData3 = mdData3.add_suffix('_ad')
        mixData = pd.merge_asof(mixData, mdData3, left_index=True, right_index=True,
                                direction=mixmode,
                                tolerance=pd.Timedelta('1000000000ms'), allow_exact_matches=True)
        mixData['BASIS_ad'] = -(mixData['mid_y'] - mixData[key3 + '_ad'] * mult)

    return mixData, mdData1, mdData2


def comparedata(mixData, stat=False):
    for i in dsflist:
        mixData['dsfcommid' + str(i)] = mixData['dsf' + '-1_x'] * mixData['dsf' + str(i) + '_y']

    for i in dsflist:
        mixData['dsfcomlast' + str(i)] = mixData['ds' + 'last'] * mixData['dsf' + str(i) + '_y']

    for i in dsflist:
        mixData['dsfcommix' + str(i)] = (mixData['dsmixfut']) * mixData['dsf' + str(i) + '_y']

    col1 = 'dsfcommid-1'
    mixData = winlos(mixData, col1, 'dsf1_x')
    mixData.to_csv(outdirs + u'\%s' % (datetoday + 'mdmix' + now + '.csv'))

    if stat:
        groupList = ['dtmdmix_type', 'dsf-1_x']
        for col2 in groupList:
            mixData2 = stat_group(mixData, col1, col2)
            mixData2.to_csv(outdirs + u'\%s' % (under + datetoday + 'mdmix_STAT_' + col2 + '.csv'))
    return mixData


def winlos(mixData, col1, col2):
    mixData['win_' + col1] = mixData.apply(
        lambda x: 1 if x[col1] > 0
        else 0
        , axis=1)
    mixData['wins_' + col1] = mixData['win_' + col1] * abs(mixData[col2])

    mixData['lo_' + col1] = mixData.apply(
        lambda x: 1 if x[col1] < 0
        else 0
        , axis=1)
    mixData['los_' + col1] = mixData['lo_' + col1] * abs(mixData[col2])

    return mixData


def stat_group(mixData, col1, col2):
    mixData2 = mixData.groupby([col2]).agg(
        {col1: 'sum', 'win_' + col1: 'sum', 'lo_' + col1: 'sum',
         'wins_' + col1: 'sum', 'los_' + col1: 'sum', 'Symbol_x': 'count'}).ffill()
    mixData2['%win' + col1] = mixData2['win_' + col1] / mixData2['Symbol_x']
    mixData2['%lo' + col1] = mixData2['lo_' + col1] / mixData2['Symbol_x']
    return mixData2


def trademixmd(tradeData, sigdata, mdData1, mdData2, stat=False):
    print('---------------------------------------start trade mix md2')
    tradeData = pd.merge_asof(tradeData, mdData2, left_index=True, right_index=True,
                              direction='backward',
                              tolerance=pd.Timedelta('10000000ms'), allow_exact_matches=True)
    tradeData = pd.merge_asof(tradeData, mdData1, left_index=True, right_index=True,
                              direction='backward',
                              tolerance=pd.Timedelta('10000000ms'), allow_exact_matches=True)
    tradeData['dt_trade_1'] = tradeData.index
    tradeData['dttrd_new'] = tradeData.apply(
        lambda x: -1 if x[u'类型'] == 2008
        else (x['dt_trade_1'] - x[u'timestamp_str_x']) / np.timedelta64(1, 's')
        , axis=1)
    tradeData = drop_col_nan(tradeData, 'dttrd_new')
    tradeData = dttype(tradeData, 'dttrd_new', 'dttrd_new_type')
    tradeData['dt_trade_1'] = tradeData['dt_trade_1'].diff(1) / np.timedelta64(1, 's')

    tradeData['dt_mdmix'] = (tradeData['timestamp_str_y'] - tradeData['timestamp_str_x']) / np.timedelta64(1, 's')
    tradeData = drop_col_nan(tradeData, 'dt_mdmix', is_zero=False)
    tradeData = dttype(tradeData, 'dt_mdmix', 'dtmdmix_type')

    tradeData['totaldelta'] = tradeData['Delta'].cumsum(0)
    tradeData['totalvega'] = tradeData['Vega'].cumsum(0)
    tradeData['dt_x_y'] = (tradeData['timestamp_str_x'] - tradeData.index) / np.timedelta64(1, 's')
    # tradeData = mddiffpnl(dsflist, tradeData, 'Delta', 'fut_x', 'dsf%s_x')
    tradeData['dsf-1_xpnl_Delta'] = tradeData['dsf-1_x'] * tradeData['Delta'] / tradeData['fut_x']
    tradeData = mddiffpnl(dsflist, tradeData, 'Delta', 'fut_y', 'dsf%s_y')

    col1 = 'dsf-1_x'
    tradeData = winlos(tradeData, col1, 'dsf1_y')
    col1 = 'dsf-1_xpnl_Delta'
    tradeData = winlos(tradeData, col1, 'dsf-1_x')
    col1 = 'dsf1_ypnl_Delta'
    tradeData = winlos(tradeData, col1, 'dsf1_y')
    tradeData.to_csv(outdirs + u'\%s' % (datetoday + 'trades_fut' + now + '.csv'))

    if stat:
        tradeData = drop_col_nan(tradeData, 'dsf-1_x')
        groupList = ['dttrd_ord_type', 'dsf-1_x']
        for col2 in groupList:
            mixData2 = stat_group(tradeData, col1, col2)
            mixData2.to_csv(outdirs + u'\%s' % (datetoday + 'tradesmd_STAT_' + col2 + '.csv'))

    print('done trade mix md2')
    print('start ordertime mix md2')
    tradeData = tradeData.reset_index()
    tradeData = tradeData.set_index('ordertime')
    tradeData = tradeData.sort_index()
    tradeData['ordertime'] = tradeData.index

    tradeData = pd.merge_asof(tradeData, mdData1[['timestamp_str_y', 'fut_y', 'dsf-1_y', 'dsf1_y']].rename(
        columns={'timestamp_str_y': 'orderfuttime', u'fut_y': 'fut_ord', u'dsf-1_y': 'dsf-1_ord',
                 u'dsf1_y': 'dsf1_ord'}), left_index=True,
                              right_index=True,
                              direction='backward',
                              tolerance=pd.Timedelta('10000000ms'), allow_exact_matches=True)
    tradeData['dtord_fut'] = tradeData.apply(
        lambda x: -1 if x[u'类型'] == 2008
        else (x[u'ordertime'] - x[u'orderfuttime']) / np.timedelta64(1, 's')
        , axis=1)

    tradeData = tradeData[tradeData['Strike'] > 0]
    tradeData = drop_col_nan(tradeData, 'dtord_fut')
    tradeData = dttype(tradeData, 'dtord_fut', 'dtord_fut_type')

    tradeData = pd.merge_asof(tradeData, mdData2[['timestamp_str_x', 'fut_x', 'dsf-1_x', 'dsf1_x']].rename(
        columns={u'timestamp_str_x': 'ordercntime', u'fut': 'futcn_ord_x', u'dsf-1_x': 'dsf-1cn_ord',
                 u'dsf1_x': 'dsf1cn_ord'}), left_index=True,
                              right_index=True,
                              direction='backward',
                              tolerance=pd.Timedelta('10000000ms'), allow_exact_matches=True)
    tradeData['dtord_cn'] = tradeData.apply(
        lambda x: -1 if x[u'类型'] == 2008
        else (x[u'ordertime'] - x[u'ordercntime']) / np.timedelta64(1, 's')
        , axis=1)
    tradeData = drop_col_nan(tradeData, 'dtord_cn')
    tradeData = dttype(tradeData, 'dtord_cn', 'dtord_cn_type')

    tradeData[u'Edge'] = (tradeData['AdjTv'] - tradeData[u'价格']) * tradeData[u'数量'] / tradeData['absnum']
    tradeData['edgepnl_adj'] = abs(tradeData[u'Edge'] / tradeData['per_delta']) * 1000
    tradeData['sg_adj'] = abs(tradeData[u'Spot'] * 1000 - tradeData[u'fut_ord'])
    tradeData['res_adj'] = tradeData['edgepnl_adj'] - tradeData['sg_adj']
    tradeData['sg_adj_pnl'] = abs(tradeData['sg_adj']) * abs(tradeData[u'Delta']) / tradeData[u'Spot'] / 1000
    tradeData['res_adj_pnl'] = tradeData['edgepnl'] - tradeData['sg_adj_pnl']
    tradeData['sig_delta_pnl1'] = tradeData['Delta'] * tradeData['dsf1_ord'] / tradeData['fut_ord']
    tradeData['trd_sig_yn'] = tradeData.apply(
        lambda x: 1 if x[u'dsf-1_xpnl_Delta'] > 0
        else -1 if (x[u'dsf-1_xpnl_Delta'] < 0)
        else 0
        , axis=1)
    tradeData = pd.merge_asof(tradeData, sigdata[['timestamp_str_x', pricecol + '_signal']].rename(
        columns={u'timestamp_str_x': 'sigtime', pricecol + '_signal': 'signal'}), left_index=True,
                              right_index=True,
                              direction='forward',
                              tolerance=pd.Timedelta('500ms'), allow_exact_matches=True)
    if mode == 'fut':
        tradeData['ord_type'] = tradeData.apply(
            lambda x: 0 if abs(x[u'signal']) == 0
            else 1 if (x[u'dttrd_ord'] < 0.08)
            else 2 if (x[u'dttrd_ord'] < 0.150)
            else 3 if (x[u'dttrd_ord'] < 0.250)
            else 4 if (x[u'dttrd_ord'] < 0.500)
            else 5
            , axis=1)
    else:
        tradeData['ord_type'] = tradeData.apply(
            lambda x: 0 if x[u'dtord_fut'] < 0.01
            else -1 if (x[u'dtord_cn_type'] > 0.03)
            else 1 if (x[u'dttrd_ord'] < 0.08)
            else 2 if (x[u'dttrd_ord'] < 0.150)
            else 3 if (x[u'dttrd_ord'] < 0.250)
            else 4 if (x[u'dttrd_ord'] < 0.500)
            else 5
            , axis=1)

    tradeData = pd.concat(
        [tradeData.between_time(tradetime00[0][0], tradetime00[0][1]),
         tradeData.between_time(tradetime00[1][0], tradetime00[1][1])]).fillna(0)

    tradeData.to_csv(outdirs + u'\%s' % (under + datetoday + 'order_trades_fut' + now + '.csv'))

    for ordt in ['all', 69]:
        print('-----------------------------------------------------------trade records stat describe')
        print(':::::::::::::::::::::::::::::::::::::::::::::::::::::%s' % ordt)
        for ll in [[1, ], [2, 3, ], [4, ]]:
            print(ll)
            p = tradeData[tradeData['ord_type'].isin(ll)]
            if ordt == 69:
                p = p[p[u'类型'].isin([ordt, ])]
            print('absnum', p['absnum'].sum().round(2))
            print('trd_sig_yn error: times ', (p['trd_sig_yn'] < 0).sum().round(2), 'sumabsnum',
                  p[p['trd_sig_yn'] < 0]['absnum'].sum().round(2))
            # print(p[p['trd_sig_yn'] < 0].index)
            print('edgepnl', p['edgepnl'].sum().round(2), 'sg_adj_pnl', p['sg_adj_pnl'].sum().round(2), 'res_adj_pnl',
                  p['res_adj_pnl'].sum().round(2))
            for i in dsflist:
                print('dsf%s_ypnl : ' % str(i), p['dsf%s_ypnl_Delta' % str(i)].sum().round(2))
            print(p[['absdelta', 'edgepnl_adj', 'sg_adj', 'res_adj', ]].describe().round(2))

    print('done ordertime mix md2')

    return tradeData


def mixtrade_sig(tradeData, sigdata, pricecol):
    if mode == 'fut':
        tradeData = tradeData[(tradeData['signal'] != 0) & (tradeData['dttrd_ord'] <= 0.150)]
    else:
        tradeData = tradeData[(tradeData['dttrd_ord'] <= 0.150) & (tradeData['dtord_fut'] > 0.020)]
    tradeData = tradeData.groupby([u'ordercntime']).agg(
        {'ordertime': 'last', 'tradetime': 'last', 'Delta': 'sum', 'Vega': 'sum', 'Spot': 'last', 'absnum': 'sum',
         'absdelta': 'sum', 'Tv': 'last', 'edgepnl_adj': 'mean', 'sg_adj': 'mean', 'res_adj': 'mean',
         'edgepnl': 'sum', 'sg_adj_pnl': 'sum', 'res_adj_pnl': 'sum',
         'PNL': 'last', 'TradePNL': 'last', 'livePdeltaU': 'last', 'orderfuttime': 'last', 'dSpot-1': 'first',
         'dSpot1': 'first', 'fut_ord': 'last',
         'dsf-1_ord': 'last', 'dsf1_ord': 'last', 'sig_delta_pnl1': 'sum', 'trd_sig_yn': 'sum'}
    ).ffill()
    print('----------------------------------------------traderecords group by sig describe')
    print(tradeData[['absdelta', 'edgepnl_adj', 'sg_adj', 'res_adj', ]].describe().round(2))
    sigdata = sigdata[sigdata[pricecol + '_signal'] != 0]
    tradeData = pd.merge_asof(sigdata, tradeData, left_index=True,
                              right_index=True,
                              direction='forward',
                              tolerance=pd.Timedelta('1ms'), allow_exact_matches=True)
    print(sigdata[['dsf%s_abs_y' % x for x in dsflist]].describe().round(2))
    tradeData['trd_sig_yn2'] = tradeData.apply(
        lambda x: 1 if x[u'Delta'] * x[pricecol + '_signal'] > 0
        else -1 if x[u'Delta'] * x[pricecol + '_signal'] < 0
        else -0 if x[u'Delta'] * x[pricecol + '_signal'] == 0
        else None
        , axis=1)
    print('trd_sig group wrong dir:', (tradeData['trd_sig_yn2'] < 0).sum().round(2),
          'times , sumabsnums: ', tradeData[tradeData['trd_sig_yn2'] < 0]['absnum'].sum().round(2))
    # print(tradeData[tradeData['trd_sig_yn2'] < 0].index)
    tradeData = mddiffpnl(dsflist, tradeData, 'Delta', 'fut_ord', 'dsf%s_y')
    # tradeData = mddiffpnl(dsflist,tradeData, pricecol + '_signal', 'fut_y', 'dsf%s_y')
    # for i in dsflist:
    #     tradeData['dsf%s_y' % str(i) + 'pnl_' + pricecol + '_signal'] \
    #         = tradeData['dsf%s_y' % str(i) + 'pnl_' + pricecol + '_signal'] * 100000
    tradeData.to_csv(outdirs + u'/%s' % (under + datetoday + 'sig_trade' + now + '.csv'))
    print(tradeData.resample('1800s', label='right').agg(
        {'Symbol_x': 'count', 'tradetime': 'count', 'absdelta': 'mean', 'absnum': 'sum',
         'edgepnl_adj': 'mean', 'res_adj': 'mean', 'res_adj_pnl': 'sum',
         'dsf1_ypnl_Delta': 'sum',
         'dsf2_ypnl_Delta': 'sum', '1_pnl': 'sum',
         '2_pnl': 'sum', 'win_1_pnl': 'sum',
         'lo_1_pnl': 'sum',
         }).round(2))


def func2(x, pricecol, volumecol, start, end,
          minpx, resetpxchg, minpx2, minvol, rrratio,
          ord_start, ord_limit, lasttimelimit,
          sigmode,
          stat=False):
    global x3
    time2 = x.index[0]
    time1 = time2
    lastprice = x.loc[time2, pricecol]
    lastvolume = x.loc[time2, volumecol]
    lasttime = x.index[0]
    ordcount = 0
    quotepxchg = 0
    deltaswap = 0
    deltabalance = 0

    reset_count = 0

    for m in x.index:
        time0 = time1
        time1 = time2
        time2 = m
        # print(m)

        diff = (x.loc[m, 'timestamp_str_x'] - x.loc[m, 'timestamp_str_y']) / np.timedelta64(1, 's')
        lasttimediff = (x.loc[m, 'timestamp_str_y'] - lasttime) / np.timedelta64(1, 's')
        if ((start / 1000)>= diff >= (end / 1000)) | (not time1) | (lasttimediff >= lasttimelimit):
            reset_count += 1
            lastprice = x.loc[m, pricecol]
            lastvolume = x.loc[m, volumecol]
            quotepxchg = 0
            ordcount = 0
            lasttime = m
            #############################################################
            x.loc[m, 'reset_count'] = 1
            print('-----------------', x.loc[m, 'timestamp_str_x'], x.loc[m, 'timestamp_str_y'],
                  str(reset_count) + ' pass reset diff:', round(diff, 3), 'lasttimediff', round(lasttimediff, 3))
            #############################################################
        else:
            diffreset = diff

            pxchgcum = x.loc[m, pricecol] - lastprice
            pxchgfast = x.loc[m, pricecol] - x.loc[time1, pricecol] + rrratio * (
                    x.loc[time0, pricecol] - x.loc[time1, pricecol])
            volchg = x.loc[m, volumecol] - lastvolume

            cumsig = (abs(pxchgcum) > minpx)
            fastsig = (abs(pxchgfast) > minpx2)
            recumsig = (abs(pxchgcum) > resetpxchg)
            refastsig = True
            if sigmode == 'cum':
                sigflag = cumsig
                resigflag = recumsig
                pxchg = pxchgcum
            elif sigmode == 'fast':
                sigflag = fastsig
                resigflag = refastsig
                pxchg = pxchgfast
                pxchgcum = pxchgfast
                quotepxchg = 0
            else:
                sigflag = fastsig and cumsig
                resigflag = recumsig
                pxchg = pxchgfast
                quotepxchg = 0

            if (sigflag) & (volchg > minvol) & (pxchgfast * pxchgcum >= 0) & (pxchg != quotepxchg):
                if ordcount < ord_limit:
                    x.loc[m, pricecol + '_signal'] = pxchg
                    diffreset = 0

                    if resigflag:
                        lastprice = x.loc[m, pricecol]
                        lastvolume = x.loc[m, volumecol]
                        lasttime = m
                        print('reset pxchg', pxchg)
                    else:
                        print('no reset pxchg', pxchg)

                    ordcount += 1

                    quotetime = m
                    if resetpxchg > minpx:
                        quotepxchg = pxchg
                    else:
                        quotepxchg = 0

                    ##############################################################################
                    if x.loc[m, 'timestamp_str_y'] < quotetime:  # 在同一个期货行情内
                        if quotepxchg == pxchg:
                            x.loc[m, 'reordcount'] = 1  # 同一个期货行情内，pxchg相同
                        else:
                            x.loc[m, 'reordcount'] = 2  # 同一个期货行情内，adj变大
                    elif ordcount > 1:
                        if quotepxchg == pxchg:
                            x.loc[m, 'reordcount'] = 11  # 不同一个期货行情内，pxchg相同
                        else:
                            x.loc[m, 'reordcount'] = 22  # 不同一个期货行情内，adj变大

                    ##############################################################################
                    deltaswap += pxchg
                    deltabalance += pxchg * x.loc[m, pricecol]

                    x.loc[m, pricecol + '_dtfut'] = lasttimediff
                    x.loc[m, pricecol + '_dtfut_int'] = min(round((lasttimediff - 0.25) / 0.5) * 0.5, 5)
                    x.loc[m, pricecol + '_vol_type'] = min(round(volchg / minvol) * minvol, 15 * minvol)
                    if x.loc[m, 'tradedVol_x'] > 0:
                        x.loc[m, pricecol + '_SGTYPE'] = 1
                        print(m, 'tradevol chg sig')
                    else:
                        x.loc[m, pricecol + '_SGTYPE'] = 0
                        print(m, 'mid chg')

                    print('pxchg', round(pxchg, 5), 'volchg', volchg, 'timediff', round(lasttimediff, 3), 'ordcount',
                          ordcount)
                    print('dsf1_y', (x.loc[m, 'dsf1_y']).round(5), 'dsf2_y',
                          (x.loc[m, 'dsf2_y']).round(5),
                          'dsf10_y', (x.loc[m, 'dsf10_y']).round(5),
                          '------------pnl', (x.loc[m, 'dsf1_y']).round(5) * round(pxchg, 5))
                    ##############################################################################
                else:
                    print('order limit ' + str(ordcount))
            else:
                if pxchg > minpx:
                    print('-----------------', x.loc[m, 'timestamp_str_x'], 'sigflag', sigflag,
                          'pxchg', round(pxchg, 5), 'volchg', volchg, 'pxchgfast', round(pxchgfast, 3),
                          'pxchgcum', pxchgcum, 'quotepxchg', quotepxchg)

            x.loc[m, pricecol + '_fchg'] = pxchgfast
            x.loc[m, pricecol + '_cumchg'] = pxchgcum
            x.loc[m, volumecol + '_chg'] = volchg
            x.loc[m, pricecol + '_chg'] = pxchg

        x.loc[m, 'ord_count'] = ordcount
        x.loc[m, 'totalpnls'] = deltaswap * x.loc[m, pricecol] - deltabalance
    if stat:
        print('func1')
        x = statfunc(x, pricecol, groupList=['dtmdmix_type', pricecol + '_dtfut_int',
                                             pricecol + '_signal', pricecol + '_vol_type'])
    x.to_csv(outdirs + u'/%s' % (under + datetoday + 'signal_' + pricecol + now + '.csv'))

    return x


def func3(x, pricecol, volumecol, start, end, minpx, minvol, ord_start, ord_limit, lasttimelimit, key, stat=False):
    global x3
    time2 = x.index[0]
    lastprice = x.loc[time2, pricecol]
    lastvolume = x.loc[time2, volumecol]
    pxchg = 0
    volchg = 0
    ordcount = 0
    deltaswap = 0
    deltabalance = 0

    x.loc[:, pricecol + '_dtfut'] = 0
    x.loc[:, pricecol + '_dtfut_int'] = 0
    x.loc[:, pricecol + '_SGTYPE'] = 0

    reset_count = 0

    for m in x.index:
        time1 = time2
        time2 = m
        # print(m)

        if (not time1):
            lastvolume = x.loc[m, volumecol]
            lastprice = x.loc[m, pricecol]
            ordcount = 0

            reset_count += 1
            x.loc[m, 'reset_count'] = 1
            print('-----------------', x.loc[m, 'timestamp_str_x'], x.loc[m, 'timestamp_str_y'],
                  str(reset_count))
        else:

            pxchg = x.loc[m, pricecol]-lastprice
            volchg = x.loc[m, volumecol] - lastvolume

            lastprice = x.loc[m, pricecol]

            if (abs(pxchg) > minpx):
                if ordcount < ord_limit:
                    x.loc[m, pricecol + '_signal'] = pxchg
                    lastvolume = x.loc[m, volumecol]

                    ordcount += 1

                    ##############################################################################
                    deltaswap += pxchg
                    deltabalance += pxchg * x.loc[m, pricecol]

                    typenum = 0.2
                    x.loc[m, pricecol + '_sig_type'] = max(
                        min(round(x.loc[m, pricecol + '_signal'] / typenum) * typenum, typenum * 5), -typenum * 5)

                    print(m, 'pxchg', round(pxchg, 5), 'volchg', volchg, 'ordcount',
                          ordcount)
                    print('dsf1_y', (x.loc[m, 'dsf1_y']).round(5), 'dsf2_y',
                          (x.loc[m, 'dsf2_y']).round(5),
                          'dsf10_y', (x.loc[m, 'dsf10_y']).round(5),
                          '------------pnl', (x.loc[m, 'dsf1_y']).round(5) * round(pxchg, 5))
                    ##############################################################################
                else:
                    print('order limit ' + str(ordcount))

                x.loc[m, pricecol + '_chg'] = pxchg
                x.loc[m, volumecol + '_chg'] = volchg

        x.loc[m, 'ord_count'] = ordcount
        x.loc[m, 'totalpnls'] = deltaswap * x.loc[m, pricecol] - deltabalance
    if stat:
        x = statfunc(x, pricecol, groupList=[pricecol + '_sig_type'])
    x.to_csv(outdirs + u'/%s' % (under + datetoday + 'signal_' + pricecol + now + '.csv'))

    return x


def func4(x, pricecol, volumecol, start, end, minpx, minvol, ord_start, ord_limit, lasttimelimit, key, stat=False):
    global x3
    time2 = x.index[0]
    lastprice = x.loc[time2, pricecol]
    lastvolume = x.loc[time2, volumecol]
    basis2 = 0
    pxchg = 0
    volchg = 0
    lasttime = 0
    lasttimediff = x.index[0]
    ordcount = 0
    diffreset = 0
    deltaswap = 0
    deltabalance = 0

    reset_count = 0

    for m in x.index:
        time1 = time2
        time2 = m
        # print(m)

        diff = (x.loc[m, 'timestamp_str_x'] - x.loc[m, 'timestamp_str_y']) / np.timedelta64(1, 's')
        if (not time1):

            reset_count += 1
            x.loc[m, 'reset_count'] = 1
            print('-----------------', x.loc[m, 'timestamp_str_x'], x.loc[m, 'timestamp_str_y'],
                  str(reset_count) + ' pass reset diff:', round(diff, 3), 'lasttimediff', round(lasttimediff, 3))

            lastprice = x.loc[m, pricecol]
            lastvolume = x.loc[m, volumecol]
            ordcount = 0
            lasttime = m
            lasttimediff = 0
            basis2 = 0
        else:
            diffreset = diff
            # pass

            # lasttimediff = (x.loc[m, 'timestamp_str_y'] - lasttime) / np.timedelta64(1, 's')

            pxchg = x.loc[m, pricecol] - x.loc[time1, pricecol]
            pxchg2 = x.loc[m, key] - x.loc[time1, key]
            volchg = x.loc[m, volumecol] - lastvolume
            x.loc[m, pricecol + '_chg'] = pxchg
            x.loc[m, volumecol + '_chg'] = volchg

            basis2 = 0.2 * basis2 + 0.8 * pxchg
            x.loc[m, 'basis2'] = basis2

            # if (abs(pxchg) > minpx) & (volchg > minvol*(diffreset+1)):
            # if (abs(pxchg) > minpx) & (volchg > minvol):
            # if (abs(pxchg) > minpx) & (volchg > minvol) & (diff > ord_start) & (lasttimediff >= 0.5):
            if (abs(pxchg) > minpx) & (pxchg * basis2 > 0) & (abs(basis2) > 0.1) & (diff > ord_start):
                if ordcount < ord_limit:
                    x.loc[m, pricecol + '_signal'] = pxchg
                    diffreset = 0

                    lastprice = x.loc[m, pricecol]
                    lastvolume = x.loc[m, volumecol]
                    lasttime = m

                    ordcount += 1

                    x.loc[m, pricecol + '_dtfut'] = lasttimediff
                    x.loc[m, pricecol + '_dtfut_int'] = int(lasttimediff)
                    if x.loc[m, 'tradedVol_x'] > 0:
                        x.loc[m, pricecol + '_SGTYPE'] = 1
                        print(m, 'tradevol chg sig')
                    else:
                        x.loc[m, pricecol + '_SGTYPE'] = 0
                        print(m, 'mid chg')
                    deltaswap += pxchg
                    deltabalance += pxchg * x.loc[m, pricecol]

                    print('pxchg', round(pxchg, 5), 'volchg', volchg, 'timediff', round(lasttimediff, 3), 'ordcount',
                          ordcount)
                    print('dsf1_y', (x.loc[m, 'dsf1_y']).round(5), 'dsf2_y',
                          (x.loc[m, 'dsf2_y']).round(5),
                          'dsf10_y', (x.loc[m, 'dsf10_y']).round(5),
                          '------------pnl', (x.loc[m, 'dsf1_y']).round(5) * round(pxchg, 5))
                else:
                    print('order limit ' + str(ordcount))

        x.loc[m, 'ord_count'] = ordcount
        x.loc[m, 'totalpnls'] = deltaswap * x.loc[m, pricecol] - deltabalance
    if stat:
        x = statfunc(x, pricecol, groupList=['dtmdmix_type', pricecol + '_dtfut_int'])
    x.to_csv(outdirs + u'/%s' % (under + datetoday + 'signal_' + pricecol + now + '.csv'))

    return x


def mixfunc(x, pricecol, volumecol, start, end, minpx, minvol, ord_start, ord_limit, lasttimelimit, key3,
            syn, mult, mult2,
            stat=False):
    global x3
    time2 = x.index[0]
    lastprice = x.loc[time2, pricecol]
    lastvolume = x.loc[time2, volumecol]
    basis2 = 0
    pxchg = 0
    volchg = 0
    lasttime = x.index[0]
    lasttimediff = 0
    ordcount = 0
    diffreset = 0
    deltaswap = 0
    deltabalance = 0

    reset_count = 0

    for m in x.index:
        time1 = time2
        time2 = m
        # print(m)

        diff = (x.loc[m, 'timestamp_str_x'] - x.loc[m, 'timestamp_str_y']) / np.timedelta64(1, 's')
        if (diff <= start / 1000) | (diff >= end / 1000) | \
                (not time1) | (lasttimediff >= lasttimelimit):

            reset_count += 1
            x.loc[m, 'reset_count'] = 1
            print('-----------------', x.loc[m, 'timestamp_str_x'], x.loc[m, 'timestamp_str_y'],
                  str(reset_count) + ' pass reset diff:', round(diff, 3), 'lasttimediff', round(lasttimediff, 3))

            lastprice = x.loc[m, pricecol]
            lastvolume = x.loc[m, volumecol]
            ordcount = 0
            lasttime = m
            lasttimediff = 0
        else:
            diffreset = diff
            # pass

            lasttimediff = (x.loc[m, 'timestamp_str_y'] - lasttime) / np.timedelta64(1, 's')

            pxchg = x.loc[m, pricecol] - lastprice
            pxchg = x.loc[m, pricecol] - x.loc[time1, pricecol]
            volchg = x.loc[m, volumecol] - lastvolume
            pxchg_ad = x.loc[m, key3 + '_ad'] - x.loc[time1, key3 + '_ad']
            pxchg_ad2 = x.loc[m, key3 + '_ad'] - x.loc[lasttime, key3 + '_ad']

            x.loc[m, pricecol + '_chg'] = pxchg
            x.loc[m, volumecol + '_chg'] = volchg

            # if (abs(pxchg) > minpx) & (volchg > minvol*(diffreset+1)):
            # if (abs(pxchg) > minpx) & (volchg > minvol):
            # if (abs(pxchg) > minpx) & (volchg > minvol) & (diff > ord_start) & (lasttimediff >= 0.5):
            if (abs(pxchg * mult + pxchg_ad * mult2) > minpx) & (pxchg * pxchg_ad >= syn - 100) \
                    & (volchg > minvol) & (diff > ord_start):
                if ordcount < ord_limit:
                    x.loc[m, pricecol + '_signal'] = pxchg
                    diffreset = 0

                    lastprice = x.loc[m, pricecol]
                    lastvolume = x.loc[m, volumecol]
                    lasttime = m

                    ordcount += 1

                    x.loc[m, pricecol + '_dtfut'] = lasttimediff
                    x.loc[m, pricecol + '_dtfut_int'] = int(lasttimediff)
                    if x.loc[m, 'tradedVol_x'] > 0:
                        x.loc[m, pricecol + '_SGTYPE'] = 1
                        print(m, 'tradevol chg sig')
                    else:
                        x.loc[m, pricecol + '_SGTYPE'] = 0
                        print(m, 'mid chg')
                    deltaswap += pxchg
                    deltabalance += pxchg * x.loc[m, pricecol]

                    print('pxchg', round(pxchg, 5), 'volchg', volchg, 'timediff', round(lasttimediff, 3), 'ordcount',
                          ordcount)
                    print('dsf1_y', (x.loc[m, 'dsf1_y']).round(5), 'dsf2_y',
                          (x.loc[m, 'dsf2_y']).round(5),
                          'dsf10_y', (x.loc[m, 'dsf10_y']).round(5),
                          '------------pnl', (x.loc[m, 'dsf1_y']).round(5) * round(pxchg, 5))
                else:
                    print('order limit ' + str(ordcount))

        x.loc[m, 'ord_count'] = ordcount
        x.loc[m, 'totalpnls'] = deltaswap * x.loc[m, pricecol] - deltabalance
    if stat:
        x = statfunc(x, pricecol, groupList=['dtmdmix_type', pricecol + '_dtfut_int'])
    x.to_csv(outdirs + u'/%s' % (under + datetoday + 'signal_' + pricecol + now + '.csv'))

    return x


def statfunc(x, pricecol, groupList):
    print('result statistics')
    print(under, fut2, 'to', fut1, mixmode, sigmode, 'mode', mode, 'drvmode', drvmode, datetoday)
    signalnum = (x[pricecol + '_signal'].dropna() != 0).sum()
    print('total signals:', signalnum)

    col1 = '%s_pnl'

    for i in dsflist:
        x[col1 % str(i)] = x[pricecol + '_signal'] * x['dsf%s_y' % str(i)] / x['fut_y'] * 100000
        winrate = (x[col1 % str(i)] > 0).sum() / signalnum
        losrate = (x[col1 % str(i)] < 0).sum() / signalnum
        totalpnl = x.loc[:, col1 % str(i)].sum()

        print(str(i) + ' fut pnl:', 'win%', int(winrate * 100) / 100, int(losrate * 100) / 100,
              'totalpnl', int(totalpnl),
              'perpnl', int(totalpnl / signalnum * 100) / 100,
              )

    for i in dsflist[0:2]:
        x = winlos(x, col1 % str(i), 'dsf%s_y' % str(i))

    x.loc[:, 'toltal_nums'] = x.loc[:, pricecol + '_signal'].cumsum(0)

    print(u'参数:', start, end, minpx, resetpxchg, pricecol, lasttimelimit)
    print('per sig diff', int(x.loc[:, pricecol + '_dtfut'].sum() / signalnum * 100) / 100)
    print('per md diff', int(x.loc[:, 'dt_mdmix'].sum() / signalnum * 100) / 100)
    print('sgtype tradechg%', int(x.loc[:, pricecol + '_SGTYPE'].sum() / signalnum * 100) / 100)

    x3 = drop_col_nan(x, pricecol + '_signal')

    for i in dsflist[2:]:
        x3 = winlos(x3, col1 % str(i), 'dsf%s_y' % str(i))
    for col2 in groupList:
        mixData2 = stat_group(x3, col1 % str(1), col2)
        for i in dsflist[2:]:
            mixData2 = pd.concat([mixData2, stat_group(x3, col1 % str(i), col2).iloc[:, -2:]], axis=1)
        print(col2)
        print(mixData2.iloc[:, -(len(dsflist) - 1) * 2 - 1:].reset_index(drop=False).T.round(2))
        mixData2.to_csv(outdirs + u'/%s' % (
                datetoday + 'signal_STAT_' + col2 +
                now + '.csv'))

    return x


def main(datetoday, key,
         start, end, minpx, minpx2, minvol, ord_start, ord_limit, lasttimelimit,
         drvmode, pricecol,
         str_ad, fut_ad, key_ad):
    mixData, mdData1, mdData2 = mixdata(str1, fut1, str2, fut2, datetoday, key, mult, mdsilemode, str_ad, fut_ad,
                                        key_ad)
    # mixData = comparedata(mixData)
    if (drvmode == 'price') or (drvmode == 'basis'):
        x = func2(mixData, pricecol, 'Volume_x',
                  start, end,
                  minpx, resetpxchg, minpx2, minvol, rrratio,
                  ord_start,
                  ord_limit,
                  lasttimelimit,
                  sigmode,
                  stat=True)
    elif drvmode == 'sigone':
        x = func3(mixData, pricecol, 'Volume_x',
                  start, end,
                  minpx, minvol,
                  ord_start,
                  ord_limit,
                  lasttimelimit,
                  key + '_x',
                  stat=True)
    elif drvmode == 'basis2':
        x = func4(mixData, pricecol, 'Volume_x',
                  start, end,
                  minpx, minvol,
                  ord_start,
                  ord_limit,
                  lasttimelimit,
                  key + '_x',
                  stat=True)
    else:
        x = mixfunc(mixData, pricecol, 'Volume_x',
                    start, end,
                    minpx, minvol,
                    ord_start,
                    ord_limit,
                    lasttimelimit,
                    key_ad,
                    syn,
                    mult, mult2,
                    stat=True)

    # func(mixData, 'mixfut', 'Volume_x', 'dtmdmix_type', 0, 9, 2, 30,4)
    tradedata = loadtrade(datetoday, str_trade, tradetime00)
    tradeData = trademixmd(tradedata, x, mdData1, mdData2)
    mixtrade_sig(tradeData, x, pricecol)
    # mixtrade_sig2(tradeData, x, key + '_x')

    print(datetoday)


if __name__ == '__main__':
    dates = ['20240426']
    under = 'SH500'
    fut1 = 'IC2405'
    fut2 = 'CN2404'
    optcodes = ['10005956', ]

    # mode = ['a50', 'fut', 'etf', 'mix', 'mixFUT'][0]
    # drvmode = ['price', 'basis', 'basis2', 'mix'][0]
    # sigmode = ['cum', 'fast', 'mix'][0]
    # mixmode = ['backward', 'forward'][0]
    # key = ['LastPrice', 'mid', 'mixfut'][0]

    [mode, drvmode, sigmode, mixmode, key] = ['a50', 'price', 'cum', 'backward', 'LastPrice']
    # [mode, drvmode, sigmode, mixmode, key] = ['etffast', 'price', 'cum', 'backward', 'LastPrice']
    # [mode, drvmode, sigmode, mixmode, key] = ['etf', 'price', 'fast', 'backward', 'ForQuoteSysID']
    # [mode, drvmode, sigmode, mixmode, key] = ['fut', 'price', 'fast', 'backward', 'mid']
    # [mode, drvmode, sigmode, mixmode, key] = ['futone', 'sigone', 'fast', 'backward', 'mid']
    # [mode, drvmode, sigmode, mixmode, key] = ['mixFUT', 'mix', 'fast', 'forward', 'mid']

    mdsilemode = ['all', 'onlytrade', 'onlymid'][0]

    fut22 = ['16', '159915', '16']
    key22 = 'ForQuoteSysID'

    start = -100
    end = 1000000
    minpx = 0  # cum
    minpx2 = minpx  # fast
    resetpxchg = 0  # if>minpx,minpx会继续累计
    minvol = 0
    rrratio = 0
    ord_start = 0
    ord_limit = 30000
    lasttimelimit = 100  # 0表示不跨期货行情
    mult = 1

    str_ad = ''
    fut_ad = ''
    key_ad = ''

    # optcodes = ['10005666', '10005667', '10005675', '10005676']
    tradetime00 = [['09:30:00', '11:30:00'], ['13:00:00', '14:54:30']]
    # tradetime00 = [['09:30:00', '10:00:30'], ['13:00:00', '13:00:00']]
    # tradetime00 = [['09:30:00', '09:30:00'], ['13:00:00', '14:54:30']]

    cwd = os.getcwd()
    cwd = os.path.dirname(cwd)
    dirs = u'E:\\DATA\\' + under + '\\'
    outdirs = dirs + 'output\\sigmix'

    str1 = dirs + 'md_%s_cffex_multicast.csv'
    str2 = dirs + 'md_%s_tcp_receiver_sub.csv'
    str22 = dirs + 'md_%s_shm_receiver.csv'
    str23 = dirs + 'md_%s_sse_fast_fpga.csv'

    str_trade = dirs + 'TradeRecords%s.csv'

    str_ord = dirs + '%s_OrderDetails.csv'
    str_optmd = dirs + 'md_%s_sse_mdgw.csv'

    if mode == 'a50':
        fut2 = fut2
        key = key
        str2 = str2
        start = 20
        end = 4900
        minpx = 1  # cum
        minpx2 = minpx  # fast
        resetpxchg = 3  # if>minpx,minpx会继续累计
        minvol = 3
        rrratio = 0
        ord_start = 0
        ord_limit = 30000
        lasttimelimit = 100  # 0表示不跨期货行情
        mult = 1
    elif mode == 'etffast':
        if under == 'SH300':
            fut2 = fut22[0]
        elif under == 'SH500':
            fut2 = fut22[1]
        elif under == 'SH50':
            fut2 = fut22[2]
        key = key
        str2 = str23
        start = 20
        end = 4900
        minpx = 0.0002  # cum
        minpx2 = minpx  # fast
        resetpxchg = 0.002  # if>minpx,minpx会继续累计
        minvol = 500000
        rrratio = 0
        ord_start = 0
        ord_limit = 30000
        lasttimelimit = 100  # 0表示不跨期货行情
        mult = 1
    elif mode == 'fut':
        fut2 = 'IC2404'
        key = key
        str2 = str1
        start = -100
        end = 4900
        minpx = 0.45  # cum
        minpx2 = minpx  # fast
        resetpxchg = 0  # if>minpx,minpx会继续累计
        minvol = 1
        rrratio = 0  # 上一次跳动
        ord_start = 0
        ord_limit = 30000
        lasttimelimit = 100  # 0表示不跨期货行情
    elif mode == 'futone':
        fut2 = 'IC2404'
        key = 'im2'
        str2 = str1
        start = -100
        end = 4900
        minpx = 0.45  # cum
        minpx2 = minpx  # fast
        resetpxchg = 0  # if>minpx,minpx会继续累计
        minvol = 1
        rrratio = 0  # 上一次跳动
        ord_start = 0
        ord_limit = 30000
        lasttimelimit = 100  # 0表示不跨期货行情
    elif mode == 'etf':
        if under == 'SH300':
            fut2 = fut22[0]
        elif under == 'SH500':
            fut2 = fut22[1]
        elif under == 'SH50':
            fut2 = fut22[2]
        fut2 = '159915'
        key = key
        str2 = str22
        start = 5
        end = 4950
        minpx = 0.0010
        resetpxchg = minpx
        minvol = -1000000000
        lasttimelimit = 100
        mult = 1
    elif mode == 'mix':
        if under == 'SH300':
            fut_ad = fut22[0]
        elif under == 'SH500':
            fut_ad = fut22[1]
        elif under == 'SH50':
            fut_ad = fut22[2]
        key_ad = key22
        str_ad = str22
        mult = 1
        syn = 0
    elif mode == 'mixFUT':
        fut2 = 'IM2401'
        key = key
        str2 = str1

        fut_ad = 'IC2401'
        key_ad = key
        str_ad = str1

        start = -100
        end = 4900
        minpx = 0.4  # cum
        minpx2 = minpx  # fast
        resetpxchg = 0  # if>minpx,minpx会继续累计
        minvol = 0
        rrratio = 0
        ord_start = 0
        ord_limit = 30000
        lasttimelimit = 100  # 0表示不跨期货行情
        syn = 0
        mult = 1
        mult2 = 1

    if drvmode == 'basis':
        pricecol = 'BASIS'
    elif drvmode == 'basis2':
        pricecol = 'BASIS'
    else:
        pricecol = key + '_x'

    # dsflist = [-20, -10, -5, -1, 1, 4, 10, 20, 60]
    dsflist = [-1, 1, 2, 3, 4, 5, 7, 10, 20]

    now = datetime.datetime.now()
    now = datetime.datetime.strftime(now, "_%m%d_%H%M%S")

    if not os.path.exists(outdirs):
        os.makedirs(outdirs)

    # 设置 Pandas 显示选项
    pd.set_option('display.max_columns', None)  # 显示所有列
    pd.set_option('display.max_rows', None)  # 显示所有行
    pd.set_option('display.width', None)  # 自动调整列宽
    pd.set_option('display.max_colwidth', None)  # 显示所有单元格的内容
    if dates == ['all']:
        dates = eachFile(dirs)
        for datetoday in dates:
            main(datetoday, key, start, end, minpx, minpx2, minvol, ord_start, ord_limit, lasttimelimit,
                 drvmode, pricecol,
                 str_ad, fut_ad, key_ad)
    else:
        for datetoday in dates:
            main(datetoday, key, start, end, minpx, minpx2, minvol, ord_start, ord_limit, lasttimelimit,
                 drvmode, pricecol,
                 str_ad, fut_ad, key_ad)
    print(datetime.datetime.now())
    print('all done')
    os.startfile(outdirs)
    sys.exit()
