# -*- coding: utf-8 -*-
"""
Created on Mon Mar 13 16:50:45 2023

@author: Lenovo
"""

from matplotlib import pyplot
import matplotlib.pyplot as plt
import numpy as np
from scipy import optimize as op
import pandas as pd
import datetime
# from pulp import LpProblem,LpVariable,LpMinimize,LpInteger,LpContinuous
from scipy import interpolate
import cx_Oracle
import os
import scipy.stats as st
from scipy.optimize import fsolve




def vanna(z):
    S=z.spotPrice
    br=z.BR
    K=z.STRIKEPRICE
    sigma=z.fitvol
    rf=z.RF
    #vega=0.00214585
    t=z.t
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    vanna=st.norm.pdf(d1,loc=0,scale=1)*(-d2/sigma)*np.exp(-br*t)
    return (vanna)
def cgamma(z):
    S=z.spotPrice
    br=z.BR
    K=z.STRIKEPRICE
    sigma=z.fitvol
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    gamma=st.norm.pdf(d1,loc=0,scale=1)/(S*np.exp(br*t)*sigma*np.sqrt(t))
    return (gamma)

def dvanna(z):
    S=z.spotPrice
    K=z.STRIKEPRICE
    sigma=z.fitvol
    rf=z.RF
    #vega=0.00214585
    t=z.t
    br=z.BR
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    dvanna=st.norm.pdf(d1,loc=0,scale=1)*(-1+d1*d2)*np.exp(-br*t)/(S*sigma*sigma*np.sqrt(t))
    return (dvanna)

    
def volga(z):
    S=z.spotPrice
    K=z.STRIKEPRICE
    sigma=z.fitvol
    br=z.BR
    #vega=0.00214585
    t=z.t
    rf=z.RF
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    volga=z.vega/sigma*d1*d2*100
    return (volga)

def thetac(z):
    t=z.TIME2EXPIRY
    K=z.STRIKEPRICE
    sigma=z.fitvol
    br=z.BR
    rf=z.RF
    S=z.spotPrice*np.exp((rf-br)*t)
    #vega=0.00214585
    
    d2= np.log(S/K)+(-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    if z.Call==1:
        theta=S*st.norm.pdf(d1,loc=0,scale=1)*z.fitvol/(-2*np.sqrt(t))-rf*K*np.exp(-rf*t)*st.norm.cdf(d2,loc=0,scale=1)
    else:
        theta=S*st.norm.pdf(d1,loc=0,scale=1)*z.fitvol/(-2*np.sqrt(t))+rf*K*np.exp(-rf*t)*st.norm.cdf(-d2,loc=0,scale=1)

    return (theta)



def dvolga(z):
    S=z.spotPrice
    K=z.STRIKEPRICE
    sigma=z.fitvol
    br=z.BR
    #vega=0.00214585
    t=z.t
    rf=z.RF
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    dvolga=z.vega*100*(d1+d2-d1*d2*d2)/(sigma*sigma*S*np.sqrt(t))

    return (dvolga)

def ATMdelta(z):
    S=z.spotPrice
    K=z.STRIKEPRICE
    br=z.BR
    sigma=z.atmVol
    t=z.t
    rf=z.RF
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)

    if z.OPTIONTYPE=='C':
        return(np.exp(-br*t)*Nd1)
    else:
        return(np.exp(-br*t)*(Nd1-1))

def ATMvega(z):
    S=z.spotPrice
    K=z.STRIKEPRICE
    br=z.BR
    sigma=z.atmVol
    t=z.t
    rf=z.RF
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    vega=st.norm.pdf(d1,loc=0,scale=1)*S*np.sqrt(t)*np.exp(-br*t)*100
    return vega


def charm(z):
    t=z.t
    K=z.STRIKEPRICE
    sigma=z.fitvol
    br=z.BR
    rf=z.RF
    S=z.spotPrice*np.exp((rf-br)*t)
    #vega=0.00214585
    
    d2= np.log(S/K)+(-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    charm=np.exp(-rf*t)*(-rf*Nd1+st.norm.pdf(d1,loc=0,scale=1)*(np.log(S/K)/-2/sigma*np.power(t,-1.5)+sigma/4/np.sqrt(t)))

    if z.OPTIONTYPE=='C':
        return(charm)
    else:
        return(charm+rf*np.exp(-rf*t))







def ATMvolprice(z):
    S=z.spotPrice
    K=z.STRIKEPRICE
    br=z.BR
    rf=z.RF
    sigma=z.atmVol
    #vega=0.00214585
    t=z.t
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.OPTIONTYPE=='C':
        return(Call)
    else:
        return(Put)


def bs_cal(est_sigma,z):
    S=z.spotPrice
    K=z.STRIKEPRICE
    br=z.BR
    sigma=est_sigma
    #vega=0.00214585
    t=z.t
    rf=z.RF
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.OPTIONTYPE=='C':
        return(Call)
    else:
        return(Put)
def bs_diff(est_sigma,z):
    est_price=bs_cal(est_sigma,z)
    return est_price-z.vvprice

def bs_diff2(est_sigma,z):
    est_price=bs_cal(est_sigma,z)
    return est_price-z.vvprice2
    
def vv(z):
    test=z.copy()
    b=test.tv-test.ATMvolprice
    test.loc[:,'vega']=test.loc[:,'vega']*100
    A=test[['vega','vanna','volga']].values
    try:
        x=np.linalg.inv(A)@b
    except:
        x=[None,None,None]
    return(x)


def cubicspline(z,target):
    z=z.sort_values(by=['GEN2DELTA'])
    z1=z.loc[z.GEN2DELTA>target].head(3)
    z2=z.loc[z.GEN2DELTA<target].tail(1)
    z=pd.concat([z2,z1])
 #   x=z.GEN2DELTA
  #  y=z.GEN2FITVOL
    x=z.GEN2DELTA.append(pd.Series(0.5*np.sign(target)))
    y=z.GEN2FITVOL.append(pd.Series(z.GEN2ATMVOL.iloc[0]))
    z=pd.concat([x,y],axis=1).sort_values(by=[0])
    s=interpolate.CubicSpline(z.iloc[:,0], z.iloc[:,1])
    """      
    arr=np.arange(np.amin(x), np.amax(x), 0.01)    
    fig,ax = plt.subplots(1, 1)
    ax.plot(x, y, 'bo', label='Data Point')

    ax.plot(arr, s(arr), 'k-', label='Cubic Spline', lw=1)
    
   """  
    return(s(target))



def yparams_cal(z):
    K1=z.sort_values('vanna').STRIKEPRICE.iloc[0]
    K2=z.sort_values('vanna').STRIKEPRICE.iloc[-1]
    z['absz']=np.abs(z.z)
    K3=z.sort_values('absz').STRIKEPRICE.iloc[0]

    test=z[z.STRIKEPRICE.isin([K1,K2,K3])]
    test=test[test.tv.isin(test.groupby('STRIKEPRICE').tv.min().values)]
    if len(test)<3:
        return ([0,0,0])
    b=test.tv-test.ATMvolprice

    A=test[['vega','vanna','volga']].values
    
    x=np.linalg.inv(A)@b
    
    
    x[0]=x[0]/100
    return(x)


def rescal(data1,data2,product):
    
    """
    data1.clumns=['expiry', 'underlyer', 'optioncode', 'vollasttime', 'fitvol', 'bidvol',
           'askvol', 'delta', 'gamma', 'vega', 'theta', 'spotprice', 'tv',
           'theoedge', 'dtv', 'vegaDtv', 'deltaDtv', 'strike', 'z', 'callPut',
           'skdelta', 'speed', 'charm', 'color']
    data2.columns=['expiry', 'underlyer', 'timestamp', 'atmVol', 'atmSlope', 'BR', 'RF',
           'forwardVol', 'spotPrice', 'forwardPrice', 'vg', 'exptime', 'lBR',
           'atmWing']
    product.columns=['SECURITYID', 'UNDERLYING', 'PRODUCTID', 'STARTDATE', 'ENDDATE',
           'PRODUCTCLASS', 'EXCHANGETYPE', 'MULTIPLER', 'UNDERLYINGMULTIPLER',
           'STRIKEPRICE', 'PRECLOSE', 'PRESETTLE', 'UNDERLYINGPRECLOSE', 'UPLIMIT',
           'DOWNLIMIT', 'MARGINRATIOPARAM1', 'MARGINRATIOPARAM2',
           'LONGMARGINRATIO', 'SHORTMARGINRATIO', 'ROUNDLOT', 'MINLIMITORDERVOL',
           'MAXLIMITORDERVOL', 'MINMARKETORDERVOL', 'MAXMARKETORDERVOL',
           'TICKSIZE', 'MAXMARGINSIDEALGO', 'ISNEWCONTRACT', 'ISTRADING',
           'OPTIONTYPE', 'COMBINATIONTYPE', 'SECURITYNAME', 'MODIFYDATE']
    """
    
    
    data3=data1.copy()
    data4=data2.copy()
    product=product.copy()

    
    
    newdata=pd.merge(data3,product,left_on='optioncode',right_on='SECURITYID',how='inner')
    newdata=pd.merge(newdata,data4,on='expiry')
    newdata.loc[:,'STRIKEPRICE']=pd.to_numeric(newdata.STRIKEPRICE)
    newdata.loc[:,'z']=pd.to_numeric(newdata.z)
    newdata.loc[:,'forwardPrice']=pd.to_numeric(newdata.forwardPrice)
    newdata.loc[:,'spotPrice']=pd.to_numeric(newdata.spotPrice)
    newdata.loc[:,'fitvol']=pd.to_numeric(newdata.fitvol)
    newdata.loc[:,'vega']=pd.to_numeric(newdata.vega)
    newdata.loc[:,'RF']=pd.to_numeric(newdata.RF)
    newdata.loc[:,'tv']=pd.to_numeric(newdata.tv)
    newdata.loc[:,'atmVol']=pd.to_numeric(newdata.atmVol)
    
    
    
    newdata=newdata.set_index('expiry')
    newdata['t']=newdata.groupby('expiry').apply(lambda z: np.log(z.STRIKEPRICE.max()/z.STRIKEPRICE.min())/(z.z.max()-z.z.min()))
    newdata.loc[:,'t']=np.power(newdata.t,2)
    newdata.loc[:,'BR']=newdata.RF-np.log(newdata.forwardPrice/newdata.spotPrice)/newdata.t
    

    newdata['vanna']=newdata.apply(lambda z: vanna(z),axis=1)
 #   data['theta']=data.apply(lambda z: thetac(z),axis=1)
  #  data['gamma']=data.apply(lambda z: cgamma(z),axis=1)
    newdata['charm2']=newdata.apply(lambda z: charm(z),axis=1)
    newdata['dvanna']=newdata.apply(lambda z: dvanna(z),axis=1)
    newdata['volga']=newdata.apply(lambda z: volga(z),axis=1)
    newdata['dvolga']=newdata.apply(lambda z: dvolga(z),axis=1)
    newdata['ATMvolprice']=newdata.apply(lambda z: ATMvolprice(z),axis=1)
    newdata['ATMdelta']=newdata.apply(lambda z: ATMdelta(z),axis=1)
    newdata['ATMvega']=newdata.apply(lambda z: ATMvega(z),axis=1)







    dat=pd.DataFrame(newdata.groupby('expiry').apply(lambda z: yparams_cal(z)).rename('yparams'))
    dat['y1']=dat.apply(lambda z: z.yparams[0],axis=1)
    dat['y2']=dat.apply(lambda z: z.yparams[1],axis=1)
    dat['y3']=dat.apply(lambda z: z.yparams[2],axis=1)
    dat=dat.drop('yparams',axis=1)
    newdata=pd.merge(newdata,dat,left_on='expiry',right_index=True)
   # print(dat)

    newdata['adjustdelta']=newdata['ATMdelta']+newdata.vanna*newdata.y1+newdata.dvanna*newdata.y2+newdata.dvolga*newdata.y3
    newdata['vvprice']=newdata.vega*newdata.y1*100+newdata.vanna*newdata.y2+newdata.volga*newdata.y3+newdata.ATMvolprice
    return (newdata)
    
