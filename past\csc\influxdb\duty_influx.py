# -*- coding: utf-8 -*-
"""
Created on Thu Sep 23 14:13:31 2021

@author: yihw
"""

from influxdb import InfluxDBClient
import datetime
import time
import numpy as np
import pandas as pd
from OmmDatabase import OmmDatabase

import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False    
# import re
from enum import Enum
#%%
class order_status(Enum):
    pending_add = 0
    pending_delete = 1
    exchange_order = 2
    partial_traded = 3
    all_traded = 4
    deleted = 5
    over_flow = 8
    cancelled = 9
#%%
# client = InfluxDBClient('10.17.88.168',9001,'reader','reader','testbase') # 东坝机房
client = InfluxDBClient('10.17.88.168',9001,'reader','reader','omm') # 东坝机房测试
#%%
beginStr = '2021-09-13T09:10:00.0Z'
endStr = '2021-09-13T15:10:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000

exchange = 'zce'

result_order = client.query("select * from "+exchange+"_future_order where time >= %d and time <= %d;"%(begin,end)) 
# result_quote = client.query("select * from "+exchange+"_future_quote where time >= %d and time <= %d;"%(begin,end))
# result_trade = client.query("select * from "+exchange+"_future_trade where time >= %d and time <= %d;"%(begin,end))
#%%
# iid_list = ['hc2111', 'hc2112', 'hc2201', 'hc2202', 'hc2203', 'rb2111', 'rb2112', 'rb2201', 'rb2202', 'rb2203']
iid_list = ['CF111', 'RM111', 'SR111']
try:
    points1 = result_order.get_points()
    l=[]
    for d in points1:
        l.append(d)
    print(len(l))
    df_order = pd.DataFrame(l)
except:
    df_order = pd.DataFrame()

try:
    points2 = result_quote.get_points()
    l=[]
    for d in points2:
        l.append(d)
    print(len(l))
    df_quote = pd.DataFrame(l)
except:
    df_quote = pd.DataFrame()

try:
    l=[]
    if exchange == 'shfe':
        measurement = 'test10'
    elif exchange == 'zce':
        measurement = 'testzhengzhou'
    elif exchange == 'dce':
        measurement = 'testdalian'
    else:
        print('你这交易所有问题啊')
    for iid in iid_list:
        result_mkt = client.query("select * from "+measurement+" where time >= %d and time <= %d and insid_md='{}';".format(iid)%(begin,end)) 
        points3 = result_mkt.get_points()
        for d in points3:
            l.append(d)
    print(len(l))
    df_mkt = pd.DataFrame(l)
except:
    df_mkt = pd.DataFrame()

try:
    points4 = result_trade.get_points()
    l=[]
    for d in points4:
        l.append(d)
    print(len(l))
    df_trade = pd.DataFrame(l)
except:
    df_trade = pd.DataFrame()

#%%
try:
    df_order['UnixStamp'] = df_order['local_time']/10**9
    df_order.index = df_order['UnixStamp']
    df_order = df_order.sort_index()
except:
    df_order.columns = ['time', 'base_price', 'comments', 'error_id', 'error_message', 'insid',
       'internal_order_id', 'last_traded_time', 'last_update_time', 'level',
       'local_time', 'long_short', 'match_condition', 'note', 'open_close',
       'order_id', 'order_price', 'order_status', 'portfolio_id',
       'strategy_id', 'traded_price', 'volume_original_total', 'volume_total',
       'volume_traded', 'UnixStamp']

try:
    df_quote['UnixStamp'] = df_quote['local_time']/10**9
    df_quote.index = df_quote['UnixStamp']
    df_quote = df_quote.sort_index()
except:
    df_quote = pd.DataFrame(columns=['time', 'ask_error_id', 'ask_open_close', 'ask_order_price',
       'ask_order_status', 'ask_volume_original_total', 'ask_volume_total',
       'ask_volume_traded', 'base_ask_price', 'base_bid_price', 'bid_error_id',
       'bid_open_close', 'bid_order_price', 'bid_order_status',
       'bid_volume_original_total', 'bid_volume_total', 'bid_volume_traded',
       'cancel_time', 'error_id', 'error_message', 'insert_time', 'insid',
       'internal_quote_id', 'last_traded_time', 'level', 'local_time', 'note',
       'portfolio_id', 'prevInternal_quote_id', 'quote_id', 'quote_request_id',
       'quote_status', 'strategy_id', 'UnixStamp'])   
    
def str_to_timestamp(x):
    try:
        t = (datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ')+datetime.timedelta(hours=8)).timestamp()
    except:
        t = (datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%SZ')+datetime.timedelta(hours=8)).timestamp()
    return t

try:
    df_mkt['UnixStamp'] = df_mkt['time'].apply(str_to_timestamp) # 此处加了8小时, 只能运行一次, 否则时间戳会出错
    df_mkt.index = df_mkt['UnixStamp']
    df_mkt = df_mkt.sort_index()      
except:
    df_mkt = pd.DataFrame(columns=['time', 'a_p1', 'a_p2', 'a_p3', 'a_p4', 'a_p5', 'a_v1', 'a_v2', 'a_v3',
       'a_v4', 'a_v5', 'b_p1', 'b_p2', 'b_p3', 'b_p4', 'b_p5', 'b_v1', 'b_v2',
       'b_v3', 'b_v4', 'b_v5', 'exchange_t', 'insid_md', 'last_p', 'local_t',
       'lower_limit_p', 'preclose_p', 'presettle_p', 'turnover',
       'upper_limit_p', 'v', 'UnixStamp'])

try:
    df_trade['UnixStamp'] = df_trade['local_time']/10**9
    df_trade.index = df_trade['UnixStamp']
    df_trade = df_trade.sort_index()      
except:
    df_trade = pd.DataFrame(columns=['time', 'base_price', 'comments', 'delta', 'insid', 'internal_order_id',
       'is_deleted', 'local_time', 'long_short', 'note', 'open_close',
       'order_id', 'portfolio_id', 'strategy_id', 'trade_base_price',
       'trade_id', 'trade_price', 'traded_time', 'volatility',
       'volume_traded', 'UnixStamp'])
#%%
# 义务统计
def duty(bid_status, ask_status, tick, spr, vol, exchange = 'dce'): #'dce', 'zce', 'shfe'
    if bid_status and ask_status:    
        bid_list = []
        ask_list = []
        bidP_list = []
        askP_list = []
        for i in bid_status.keys():
            bid_list.append(bid_status[i])
            bidP_list.append(bid_status[i]['price'])
        for i in ask_status.keys():
            ask_list.append(ask_status[i])
            askP_list.append(ask_status[i]['price'])    
        
        if exchange == 'dce':             
            bid_order = bid_list[np.argmax(bidP_list)]
            ask_order = ask_list[np.argmin(askP_list)]
                            
            bidP = bid_order['price']
            bidV = bid_order['volume_total']
            askP = ask_order['price']
            askV = ask_order['volume_total']
            spread = np.max([askP - bidP, 0])/tick
            volume = np.min([bidV, askV])
        elif exchange == 'zce':
            for i in np.argsort(bidP_list[::-1]):
                bidV = bid_list[i]['volume_total']
                bidP = bid_list[i]['price']
                if bidV >= vol:
                    break
            for i in np.argsort(askP_list):
                askV = ask_list[i]['volume_total']
                askP = ask_list[i]['price']
                if askV >= vol:
                    break                         
            spread = np.max([askP - bidP, 0])/tick   
            volume = np.min([bidV, askV])
        else: # shfe
            k = 0 # 记录是否是最内层
            bidV = 0
            askV = 0
            for i in np.argsort(bidP_list[::-1]):
                bidV += bid_list[i]['volume_total']
                bidP = bid_list[i]['price']
                if k == 0:
                    bidP_in = bidP
                    k += 1
                if bidV >= vol:
                    break 
            k = 0
            for i in np.argsort(askP_list):
                askV += ask_list[i]['volume_total']
                askP = ask_list[i]['price']
                if k == 0:
                    askP_in = askP
                    k += 1
                if askV >= vol:
                    break                         
            spread = np.max([askP - bidP, 0])/tick   
            volume = np.min([bidV, askV])        
    else:
        spread = np.inf
        volume = 0
    
    if spread <= spr and volume >= vol:
        is_duty = True
        if exchange == 'shfe':
            spread = np.max([askP_in - bidP_in, 0])/tick
    else:
        is_duty = False
    return (spread, volume, is_duty)

#%%
def duty_accumulate(df, spread = 9, vol = 15, tick = 1, break_t = np.inf, exchange = 'shfe'):
    
    if len(df) == 0:
        return (0, 0)

    error_id = []
    for k in range(2): # 纠错, 应对omm漏记数据的情况    
        bid_status = {} # 字典中的元素为以订单号internal_order_id为索引, 包含 long_short, price, volume_total 三个元素的字典 
        ask_status = {}
        
        t_total = 0
        spread_total = 0
        spread_split = [0 for i in range(int(spread))]

        
        row = df[0]
        t_now = row['UnixStamp']
        # 初始准备    
        if row['order_status'] == order_status.exchange_order.value: # typ 为 'trade', 'all_trade', 'order', 'quote_order', 'cancel' 之一
            insid = row['insid']
            long_short = row['long_short']
            internal_order_id = row['internal_order_id']
            price = row['order_price']
            volume_total = row['volume_total']
            if not internal_order_id in error_id:
                if long_short == 0:
                    bid_status[internal_order_id] = {'price': price, 'volume_total': volume_total}
                elif long_short == 1:
                    ask_status[internal_order_id] = {'price': price, 'volume_total': volume_total}
        
        for row in df[1:]:
            t_last = t_now
            t_now = row['UnixStamp']
            if t_now >= break_t:
                break
            spread_last, vol_last, is_duty = duty(bid_status, ask_status, tick, spread, vol, exchange)
            if is_duty:
                # if t_now - t_last < 300: # 相隔太远可能为收盘
                    t_total += t_now - t_last
                    spread_total += (t_now - t_last)*spread_last
                    spread_split[int(spread_last)-1] += (t_now - t_last)
            internal_order_id = row['internal_order_id']
            
            if internal_order_id in error_id: # 不记录错误信息
                continue
                        
            if row['order_status'] == order_status.partial_traded.value or row['order_status'] == order_status.all_traded.value:
                internal_order_id = row['internal_order_id']
                long_short = row['long_short']
                if long_short == 0:
                    if internal_order_id in bid_status.keys():
                        bid_status[internal_order_id]['volume_total'] = row['volume_total']
                        if bid_status[internal_order_id]['volume_total'] == 0:
                            bid_status.pop(internal_order_id)
                if long_short == 1:
                    if internal_order_id in ask_status.keys():
                        ask_status[internal_order_id]['volume_total'] = row['volume_total']
                        if ask_status[internal_order_id]['volume_total'] == 0:
                            ask_status.pop(internal_order_id)   
            if row['order_status'] == order_status.deleted.value:
                internal_order_id = row['internal_order_id']
                long_short = row['long_short']
                if long_short == 0:
                    if internal_order_id in bid_status.keys():
                        bid_status.pop(internal_order_id)
                if long_short == 1:
                    if internal_order_id in ask_status.keys():
                        ask_status.pop(internal_order_id)  
            if row['order_status'] == order_status.exchange_order.value:
                internal_order_id = row['internal_order_id']
                long_short = row['long_short'] 
                price = row['order_price']
                volume_total = row['volume_total']
                if volume_total > 0:
                    if long_short == 0:
                        if not internal_order_id in bid_status.keys():
                            bid_status[internal_order_id] = {'price': price, 'volume_total': volume_total}
                    if long_short == 1:
                        if not internal_order_id in ask_status.keys():
                            ask_status[internal_order_id] = {'price': price, 'volume_total': volume_total}   
                        
        # 纠错
        if bid_status:
            for i in bid_status.keys():
                error_id.append(i)
        if ask_status:
            for i in ask_status.keys():
                error_id.append(i) 
        if not error_id:
            break
    # print(error_id)
    
    return (spread_total/t_total if t_total > 0 else 0, t_total/(345*60), list(np.array(spread_split)/(345*60)))

#%%
df = df_order[df_order.insid == 'CF111']
df = df.to_dict('records')

result_duty = duty_accumulate(df, spread = 5, vol = 10, tick = 5, break_t = np.inf, exchange = 'zce')

#%%
# 计算市场的平均价宽, 义务时间
# 获取有效价格
def duty_market(dfMkt, spr = 5, vol = 10, tick = 1, exchange = 'zce'):    
    def get_askP(row, v):
        askP = np.inf
        v_total = 0
        askP_total = 0
        for i in range(1, 6):
            if row['a_p'+str(i)] == -1:
                askP = np.inf
                break
            v0 = v-v_total
            v_total += np.min([row['a_v'+str(i)], v-v_total])
            if v_total >= v:
                askP_total += row['a_p'+str(i)] * v0
                break
            else:
                askP_total += row['a_p'+str(i)] * row['a_v'+str(i)]
        if v_total < v:
            askP = np.inf
        else:
            askP = askP_total/v
        return askP
        
    def get_bidP(row, v):
        bidP = 0
        v_total = 0
        bidP_total = 0
        for i in range(1, 6):
            if row['b_p'+str(i)] == -1:
                bidP = 0
                break
            v0 = v-v_total
            v_total += np.min([row['b_v'+str(i)], v-v_total])
            if v_total >= v:
                bidP_total += row['b_p'+str(i)] * v0
                break
            else:
                bidP_total += row['b_p'+str(i)] * row['b_v'+str(i)]
        if v_total < v:
            bidP = 0
        else:
            bidP = bidP_total/v
        return bidP
    
    
    def get_spread(row, v):
        return get_askP(row, v) - get_bidP(row, v)
    
    total_time_m = 0
    total_spread_m = 0
    Spread_m = []
    t1 = 0
    row = dfMkt[0]
    spread = get_spread(row, vol)/tick
    
    
    
    # for i, row in dfMkt.iterrows():
    for row in dfMkt:
        t2 = row['UnixStamp']
        if t2 - t1 < 300:
            if spread <= spr:
                total_time_m += t2-t1
                total_spread_m += (t2-t1)*spread
            Spread_m.append(spread)
        spread = get_spread(row, vol)/tick    
        t1 = t2
    spread_ave_m = total_spread_m/total_time_m
    t_ave_m = total_time_m/(345*60)
    return (spread_ave_m, t_ave_m)


#%%
dfMkt = df_mkt[df_mkt.insid_md=='CF111']
dfMkt = dfMkt.to_dict('records')
result_duty_mkt = duty_market(dfMkt, spr = 5, vol = 10, tick = 5, exchange = 'zce')



















