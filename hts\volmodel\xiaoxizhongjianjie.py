# -*- coding: utf-8 -*-
"""
Created on Tue Mar 14 16:58:26 2023

@author: Lenovo
"""
import pika
import sys
import pandas as pd
import numpy as np
import cx_Oracle
import os
import threading
import time
import copy
import cal300_xiaoxi
import pyqtgraph as pg
from pyqtgraph.Qt import QtCore

import warnings

warnings.filterwarnings(('ignore'))

os.environ['path'] = r'D:\Oracle\Instant Client\bin'
# 读取合约
# 300
oracle_tns = cx_Oracle.makedsn('************', 1521, 'prdthetf')
con = cx_Oracle.connect('hs_asset', 'hundsun', oracle_tns)
sql_cmd = 'SELECT * FROM HS_USER.PRODUCTS'
product300 = pd.read_sql(sql_cmd, con)

# #50
# oracle_tns=cx_Oracle.makedsn('************',1521,'prdqqzs')
# con=cx_Oracle.connect('hs_asset','hundsun',oracle_tns)
# sql_cmd='SELECT * FROM HS_USER.PRODUCTS'
# product50=pd.read_sql(sql_cmd,con)

# 500
oracle_tns = cx_Oracle.makedsn('************', 1521, 'stbqqzs3')
con = cx_Oracle.connect('hs_asset', 'hundsun', oracle_tns)
sql_cmd = 'SELECT * FROM HS_USER.PRODUCTS'
product50 = pd.read_sql(sql_cmd, con)


# 300消息中间件接收
def threading300_xiaoxi():
    global data1, data2
    host = '*************'
    exchange = 'vol.sh'
    routing_key = 'vol.510300'
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(host=host))
    channel = connection.channel()
    channel.exchange_declare(exchange=exchange, exchange_type='topic')
    result = channel.queue_declare('', exclusive=True)
    queue_name = result.method.queue
    channel.queue_bind(
        exchange=exchange, queue=queue_name, routing_key=routing_key)
    print(' [*] Waiting for logs. To exit press CTRL+C')

    def callback(ch, method, properties, body):
        #     print(" [x] %r:%r" % (method.routing_key, body))
        global data1, data2
        if body[0:4] == b'volG':
            data1 = pd.DataFrame(body.split(b'\n'))

            data1 = data1.apply(lambda x: x[0].decode('utf-8').split('|'), axis=1)
            data1 = pd.DataFrame(list(data1))
            columns = data1.iloc[0, 1:]
            data1 = data1.iloc[1:-1, :-1]
            data1.columns = columns
        if body[0:4] == b'time':
            data2 = pd.DataFrame(body.split(b'\n'))

            data2 = data2.apply(lambda x: x[0].decode('utf-8').split('|'), axis=1)
            data2 = pd.DataFrame(list(data2))
            columns = data2.iloc[0, 1:]
            data2 = data2.iloc[1:-1, :-1]
            data2.columns = columns

    channel.basic_consume(
        queue=queue_name, on_message_callback=callback, auto_ack=True)

    channel.start_consuming()


global data1, data2
data1 = pd.DataFrame()
th1 = threading.Thread(target=threading300_xiaoxi)
th1.start()


# 50消息中间件接收
def threading50_xiaoxi():
    global data3, data4
    host = '*************'
    exchange = 'vol.sh'
    routing_key = 'vol.510500'
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(host=host))
    channel = connection.channel()
    channel.exchange_declare(exchange=exchange, exchange_type='topic')
    result = channel.queue_declare('', exclusive=True)
    queue_name = result.method.queue
    channel.queue_bind(
        exchange=exchange, queue=queue_name, routing_key=routing_key)
    print(' [*] Waiting for logs. To exit press CTRL+C')

    def callback(ch, method, properties, body):
        #     print(" [x] %r:%r" % (method.routing_key, body))
        global data3, data4

        if body[0:4] == b'volG':
            data3 = pd.DataFrame(body.split(b'\n'))

            data3 = data3.apply(lambda x: x[0].decode('utf-8').split('|'), axis=1)
            data3 = pd.DataFrame(list(data3))
            columns = data3.iloc[0, 1:]
            data3 = data3.iloc[1:-1, :-1]
            data3.columns = columns
        if body[0:4] == b'time':
            data4 = pd.DataFrame(body.split(b'\n'))

            data4 = data4.apply(lambda x: x[0].decode('utf-8').split('|'), axis=1)
            data4 = pd.DataFrame(list(data4))
            columns = data4.iloc[0, 1:]
            data4 = data4.iloc[1:-1, :-1]
            data4.columns = columns

    channel.basic_consume(
        queue=queue_name, on_message_callback=callback, auto_ack=True)

    channel.start_consuming()


global data3, data4
data3 = pd.DataFrame()
th3 = threading.Thread(target=threading50_xiaoxi)
th3.start()

dangyue = ********
ciyue = ********

time.sleep(5)
# 300信号计算
plotdata300 = pd.DataFrame()


def threading300_cal():
    global plotdata300, dangyue, ciyue
    num = 0

    while True:
        t = time.localtime().tm_hour * 100 + time.localtime().tm_min
        if t > 1501:
            break
        elif (t > 1131 and t < 1300):
            time.sleep(60)
        else:
            time.sleep(1)
            try:

                newdata300 = cal300_xiaoxi.rescal(data1, data2, product300)
                iv0 = newdata300[newdata300.index.astype('int') == dangyue].atmVol.iloc[0]
                iv1 = newdata300[newdata300.index.astype('int') == ciyue].atmVol.iloc[0]
                y21 = newdata300[newdata300.index.astype('int') == dangyue].y2.iloc[0]
                y22 = newdata300[newdata300.index.astype('int') == ciyue].y2.iloc[0]
                calendar = iv0 * np.sqrt(
                    newdata300[newdata300.index.astype('int') == dangyue].t.iloc[0]) - iv1 * np.sqrt(
                    newdata300[newdata300.index.astype('int') == ciyue].t.iloc[0])
                plotdata300 = plotdata300.append(pd.DataFrame([iv0, iv1, y21, y22, calendar]).T)
                num = num + 1
            except Exception as e:
                print(e, '300')
            if num == 15000:
                break


th2 = threading.Thread(target=threading300_cal)
th2.start()

# 50 信号计算
plotdata50 = pd.DataFrame()


def threading50_cal():
    global plotdata50, dangyue, ciyue
    num = 0
    while True:
        t = time.localtime().tm_hour * 100 + time.localtime().tm_min
        if t > 1501:
            break
        elif (t > 1131 and t < 1300):
            time.sleep(60)
        else:
            time.sleep(1)
            try:

                newdata50 = cal300_xiaoxi.rescal(data3, data4, product50)
                iv0 = newdata50[newdata50.index.astype('int') == dangyue].atmVol.iloc[0]
                iv1 = newdata50[newdata50.index.astype('int') == ciyue].atmVol.iloc[0]
                y21 = newdata50[newdata50.index.astype('int') == dangyue].y2.iloc[0]
                y22 = newdata50[newdata50.index.astype('int') == ciyue].y2.iloc[0]
                calendar = iv0 * np.sqrt(newdata50[newdata50.index.astype('int') == dangyue].t.iloc[0]) - iv1 * np.sqrt(
                    newdata50[newdata50.index.astype('int') == ciyue].t.iloc[0])
                plotdata50 = plotdata50.append(pd.DataFrame([iv0, iv1, y21, y22, calendar]).T)
                num = num + 1
            except Exception as e:
                print(e, '50')

            if num == 15000:
                break


th4 = threading.Thread(target=threading50_cal)
th4.start()

app = pg.mkQApp("Plotting Example")
# mw = QtWidgets.QMainWindow()
# mw.resize(800,800)

win = pg.GraphicsLayoutWidget(show=True, title="signal")
win.resize(1000, 600)
win.setWindowTitle('signal_version_1.0')

# Enable antialiasing for prettier plots
pg.setConfigOptions(antialias=True)
pg.setConfigOption('background', 'w')
pg.setConfigOption('foreground', 'w')

p1 = win.addPlot(title="vol_510300")
curve11 = p1.plot(pen=pg.mkPen('w', width=3))
curve12 = p1.plot(pen=pg.mkPen('y', width=3))

p2 = win.addPlot(title="y2_510300")
curve21 = p2.plot(pen=pg.mkPen('w', width=3))
curve22 = p2.plot(pen=pg.mkPen('y', width=3))

p3 = win.addPlot(title="calendar300")
curve3 = p3.plot(pen=pg.mkPen('w', width=3))

win.nextRow()

p4 = win.addPlot(title="vol_510050")
curve41 = p4.plot(pen=pg.mkPen('w', width=3))
curve42 = p4.plot(pen=pg.mkPen('y', width=3))

p5 = win.addPlot(title="y2_510050")
curve51 = p5.plot(pen=pg.mkPen('w', width=3))
curve52 = p5.plot(pen=pg.mkPen('y', width=3))

p6 = win.addPlot(title="calendar50")
curve6 = p6.plot(pen=pg.mkPen('w', width=3))

win.nextRow()
# p7 = win.addPlot(title="vol50-vol300")
# curve7 = p7.plot(pen=pg.mkPen('w',width=3))

p1.enableAutoRange('xy', True, 1.05)  ## stop auto-scaling after the first data set is plotted
p2.enableAutoRange('xy', True, 1.05)
p3.enableAutoRange('xy', True, 1.05)
p4.enableAutoRange('xy', True, 1.05)
p5.enableAutoRange('xy', True, 1.05)
p6.enableAutoRange('xy', True, 1.05)

p1.setDownsampling(mode='peak')
p2.setDownsampling(mode='peak')
p3.setDownsampling(mode='peak')
p4.setDownsampling(mode='peak')
p5.setDownsampling(mode='peak')
p6.setDownsampling(mode='peak')


# p7.enableAutoRange('xy', True)

def update1():
    global curve11, p1
    curve11.setData(np.array(plotdata300.iloc[:, 0]))
    curve12.setData(np.array(plotdata300.iloc[:, 1]))
    curve21.setData(np.array(plotdata300.iloc[:, 2]))
    curve22.setData(np.array(plotdata300.iloc[:, 3]))
    curve3.setData(np.array(plotdata300.iloc[:, 4]))
    curve41.setData(np.array(plotdata50.iloc[:, 0]))
    curve42.setData(np.array(plotdata50.iloc[:, 1]))
    curve51.setData(np.array(plotdata50.iloc[:, 2]))
    curve52.setData(np.array(plotdata50.iloc[:, 3]))
    curve6.setData(np.array(plotdata50.iloc[:, 4]))


#   curve7.setData(np.array(plotdata50.iloc[:,0]-plotdata300.iloc[:,0]))
"""

"""

timer = QtCore.QTimer()
timer.timeout.connect(update1)
timer.start(500)

x2 = np.linspace(-100, 100, 1000)
data22 = np.sin(x2) / x2
p8 = win.addPlot(title="Region Selection")
p8.plot(data22, pen=(255, 255, 255, 200))
lr = pg.LinearRegionItem([400, 700])
lr.setZValue(-10)
p8.addItem(lr)

p9 = win.addPlot(title="Zoom on selected region")
p9.plot(data22)


def updatePlot():
    p9.setXRange(*lr.getRegion(), padding=0)


def updateRegion():
    lr.setRegion(p9.getViewBox().viewRange()[0])


lr.sigRegionChanged.connect(updatePlot)
p9.sigXRangeChanged.connect(updateRegion)
updatePlot()

if __name__ == '__main__':
    pg.exec()

