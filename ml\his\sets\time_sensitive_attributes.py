#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Dict, Callable, Optional, Any, TypeVar, Union
from dataclasses import dataclass
from enum import Enum
import time
from datetime import timedelta

# 从time_insensitive_attributes.py导入Cell类
from time_insensitive_attributes import Cell, OrderBook


class Side(Enum):
    """订单方向枚举"""
    BUY = "Buy"
    SELL = "Sell"


@dataclass
class OpenBookMsg:
    """订单消息"""
    symbol: str
    price: int
    size: int
    side: Side
    source_time: int  # 时间戳（毫秒）
    
    @property
    def is_buy(self) -> bool:
        """是否为买单"""
        return self.side == Side.BUY
    
    @property
    def is_sell(self) -> bool:
        """是否为卖单"""
        return self.side == Side.SELL


T = TypeVar('T')
T2 = TypeVar('T2')


class TimeSensitiveAttribute:
    """时间敏感属性特征"""
    
    def __init__(self, func):
        """
        初始化时间敏感属性
        
        Args:
            func: 从订单消息列表提取特征的函数
        """
        self._func = func
    
    def __call__(self, orders_trail):
        """
        应用特征提取函数
        
        Args:
            orders_trail: 订单消息列表
            
        Returns:
            Cell: 提取的特征值
        """
        return self._func(orders_trail)
    
    def map(self, f):
        """
        映射特征值
        
        Args:
            f: 映射函数
            
        Returns:
            TimeSensitiveAttribute: 新的时间敏感属性
        """
        def new_func(orders_trail):
            return self(orders_trail).map(f)
        
        return TimeSensitiveAttribute(new_func)


class TimeSensitiveSet:
    """时间敏感特征集合"""
    
    def __init__(self, duration_ms: int = 1000, order_book_depth: int = 10):
        """
        初始化时间敏感特征集
        
        Args:
            duration_ms: 时间窗口大小（毫秒），默认为1秒
            order_book_depth: 订单簿深度，默认为10
        """
        self.duration_ms = duration_ms
        self.order_book_depth = order_book_depth
    
    def trail(self, current, order):
        """
        计算时间窗口内的订单消息
        
        Args:
            current: 当前时间窗口内的订单消息列表
            order: 新到达的订单消息
            
        Returns:
            List[OpenBookMsg]: 更新后的时间窗口内的订单消息列表
        """
        cut_off_time = order.source_time - self.duration_ms
        # 移除窗口外的订单消息
        filtered = [o for o in current if o.source_time >= cut_off_time]
        # 添加新的订单消息
        return filtered + [order]
    
    def _zip_map(self, cell1, cell2, func):
        """合并两个Cell并应用函数"""
        if cell1.is_na or cell2.is_na:
            return Cell.na()
        return Cell.from_value(func(cell1.get(), cell2.get()))
    
    def _from_option(self, opt):
        """从可选值创建Cell"""
        return Cell.from_value(opt) if opt is not None else Cell.na()
    
    def arrival_rate(self, predicate):
        """
        计算消息到达率
        
        Args:
            predicate: 消息筛选条件
            
        Returns:
            函数: 计算消息到达率的函数
        """
        def calculate(orders_trail):
            if not orders_trail:
                return Cell.na()
            
            head_opt = orders_trail[0] if orders_trail else None
            last_opt = orders_trail[-1] if orders_trail else None
            
            head_cell = self._from_option(head_opt)
            last_cell = self._from_option(last_opt)
            
            def calc_rate(head, last):
                diff = last.source_time - head.source_time
                if diff == 0:
                    return None
                
                # 计算满足条件的消息数量
                count = sum(1 for msg in orders_trail if predicate(msg))
                return count / diff
            
            result = self._zip_map(
                head_cell, 
                last_cell,
                lambda head, last: calc_rate(head, last)
            )
            
            # 如果结果为None，返回NA
            if result.is_value and result.get() is None:
                return Cell.na()
                
            return result
        
        return calculate
    
    def _attribute(self, f):
        """创建时间敏感属性"""
        return TimeSensitiveAttribute(f)
    
    def bid_arrival_rate(self):
        """
        计算买单到达率（每毫秒）
        
        Returns:
            TimeSensitiveAttribute: 买单到达率
        """
        return self._attribute(self.arrival_rate(lambda msg: msg.side == Side.BUY))
    
    def ask_arrival_rate(self):
        """
        计算卖单到达率（每毫秒）
        
        Returns:
            TimeSensitiveAttribute: 卖单到达率
        """
        return self._attribute(self.arrival_rate(lambda msg: msg.side == Side.SELL))
    
    def order_intensity(self):
        """
        计算订单强度（总订单到达率，每毫秒）
        
        Returns:
            TimeSensitiveAttribute: 订单强度
        """
        return self._attribute(self.arrival_rate(lambda _: True))
    
    def buy_sell_ratio(self):
        """
        计算买卖比率
        
        Returns:
            TimeSensitiveAttribute: 买卖比率
        """
        def calculate(orders_trail):
            buy_count = sum(1 for msg in orders_trail if msg.side == Side.BUY)
            sell_count = len(orders_trail) - buy_count
            
            if sell_count == 0:
                return Cell.na()
                
            return Cell.from_value(buy_count / sell_count)
        
        return self._attribute(calculate)
    
    def order_imbalance_ratio(self):
        """
        计算订单不平衡比率
        
        Returns:
            TimeSensitiveAttribute: 订单不平衡比率 ([-1, 1]范围)
        """
        def calculate(orders_trail):
            if not orders_trail:
                return Cell.na()
                
            buy_count = sum(1 for msg in orders_trail if msg.side == Side.BUY)
            sell_count = len(orders_trail) - buy_count
            total_count = len(orders_trail)
            
            if total_count == 0:
                return Cell.na()
                
            return Cell.from_value((buy_count - sell_count) / total_count)
        
        return self._attribute(calculate)
    
    def price_impact(self):
        """
        计算价格影响（最后价格与第一个价格的比率）
        
        Returns:
            TimeSensitiveAttribute: 价格影响
        """
        def calculate(orders_trail):
            if not orders_trail or len(orders_trail) < 2:
                return Cell.na()
                
            first_price = orders_trail[0].price
            last_price = orders_trail[-1].price
            
            if first_price == 0:
                return Cell.na()
                
            return Cell.from_value((last_price - first_price) / first_price)
        
        return self._attribute(calculate)
    
    def price_volatility(self):
        """
        计算价格波动率（标准差）
        
        Returns:
            TimeSensitiveAttribute: 价格波动率
        """
        def calculate(orders_trail):
            if not orders_trail or len(orders_trail) < 2:
                return Cell.na()
            
            prices = [msg.price for msg in orders_trail]
            mean_price = sum(prices) / len(prices)
            variance = sum((price - mean_price) ** 2 for price in prices) / len(prices)
            
            return Cell.from_value(variance ** 0.5)  # 标准差是方差的平方根
        
        return self._attribute(calculate)
    
    def time_weighted_average_price(self):
        """
        计算时间加权平均价格
        
        Returns:
            TimeSensitiveAttribute: 时间加权平均价格
        """
        def calculate(orders_trail):
            if not orders_trail:
                return Cell.na()
                
            total_time = 0
            weighted_sum = 0
            
            for i in range(len(orders_trail) - 1):
                current_msg = orders_trail[i]
                next_msg = orders_trail[i + 1]
                time_diff = next_msg.source_time - current_msg.source_time
                
                weighted_sum += current_msg.price * time_diff
                total_time += time_diff
            
            if total_time == 0:
                return Cell.na()
                
            return Cell.from_value(weighted_sum / total_time)
        
        return self._attribute(calculate)


# 示例用法
def example_usage():
    # 创建模拟订单消息
    current_time_ms = int(time.time() * 1000)
    orders = [
        OpenBookMsg("AAPL", 150, 100, Side.BUY, current_time_ms - 900),
        OpenBookMsg("AAPL", 151, 200, Side.SELL, current_time_ms - 800),
        OpenBookMsg("AAPL", 150, 150, Side.BUY, current_time_ms - 700),
        OpenBookMsg("AAPL", 152, 100, Side.SELL, current_time_ms - 600),
        OpenBookMsg("AAPL", 149, 200, Side.BUY, current_time_ms - 500),
        OpenBookMsg("AAPL", 153, 100, Side.SELL, current_time_ms - 400),
        OpenBookMsg("AAPL", 148, 300, Side.BUY, current_time_ms - 300),
        OpenBookMsg("AAPL", 154, 100, Side.SELL, current_time_ms - 200),
        OpenBookMsg("AAPL", 147, 200, Side.BUY, current_time_ms - 100),
        OpenBookMsg("AAPL", 155, 100, Side.SELL, current_time_ms)
    ]
    
    # 创建时间敏感特征集
    ts_set = TimeSensitiveSet(duration_ms=1000)  # 1秒窗口
    
    # 计算买单到达率
    bid_rate = ts_set.bid_arrival_rate()(orders)
    print(f"买单到达率: {bid_rate.get() if bid_rate.is_value else 'NA'} 每毫秒")
    
    # 计算卖单到达率
    ask_rate = ts_set.ask_arrival_rate()(orders)
    print(f"卖单到达率: {ask_rate.get() if ask_rate.is_value else 'NA'} 每毫秒")
    
    # 计算订单强度
    intensity = ts_set.order_intensity()(orders)
    print(f"订单强度: {intensity.get() if intensity.is_value else 'NA'} 每毫秒")
    
    # 计算买卖比率
    ratio = ts_set.buy_sell_ratio()(orders)
    print(f"买卖比率: {ratio.get() if ratio.is_value else 'NA'}")
    
    # 计算订单不平衡比率
    imb_ratio = ts_set.order_imbalance_ratio()(orders)
    print(f"订单不平衡比率: {imb_ratio.get() if imb_ratio.is_value else 'NA'}")
    
    # 计算价格影响
    impact = ts_set.price_impact()(orders)
    print(f"价格影响: {impact.get() if impact.is_value else 'NA'}")
    
    # 计算价格波动率
    volatility = ts_set.price_volatility()(orders)
    print(f"价格波动率: {volatility.get() if volatility.is_value else 'NA'}")
    
    # 计算时间加权平均价格
    twap = ts_set.time_weighted_average_price()(orders)
    print(f"时间加权平均价格: {twap.get() if twap.is_value else 'NA'}")


if __name__ == "__main__":
    example_usage() 