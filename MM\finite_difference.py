import numpy as np
import pandas as pd
from scipy.optimize import minimize_scalar
from params_esti_spread import params_spread_esti_main  # 从价差参数估计代码导入
from params_esti_trade_hawkes_1d_time_variant import params_trade_esti_main  # 从成交参数估计代码导入
#from params_esti_trade_hawkes_1d_totally_time_variant import params_trade_esti_main  # 从成交参数估计代码导入
import datetime
from scipy.interpolate import interp1d
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('TkAgg')


class MarketMakerStrategy:
    def __init__(self):
        # 基本参数设置
        self.delta = 0.001  # 最小价格变动单位（0.001元）
        self.h = 15  # 时间步长(秒)
        self.gamma = 10  # 存货惩罚系数
        self.max_inventory = 1000  # 最大库存限制，影响库存网格的边界
        self.y_grid = np.arange(-1*self.max_inventory, 1+self.max_inventory, 1)  # 存货网格(-1000到1000，步长1)
        self.max_trade_limit = 100  # 一笔限价单交易中的最大张数
        self.max_trade_market = 100  # 一笔市价单交易中的最大张数

        # 计算时间网格（3小时57分钟 = 14220秒，每15秒一个点）
        self.t_grid = np.arange(int(14220 / self.h))  # 948个时间点

        # 价差状态(0.001, 0.002, 0.003, 0.004)
        self.spreads = [1, 2, 3, 4]  # 对应0.001的倍数

        # 初始化策略矩阵
        n_y = len(self.y_grid)  # 201
        n_t = len(self.t_grid)  # 948
        n_s = len(self.spreads)  # 4

        self.lb = np.zeros((n_y, n_t, n_s))  # 买单量
        self.qb = np.zeros((n_y, n_t, n_s))  # 买单价格类型(0=Bb, 1=Bb+)
        self.la = np.zeros((n_y, n_t, n_s))  # 卖单量
        self.qa = np.zeros((n_y, n_t, n_s))  # 卖单价格类型(0=Ba, 1=Ba-)
        self.phi = np.zeros((n_y, n_t, n_s))  # 值函数
        self.ea = np.zeros((n_y, n_t, n_s))  # 市价单量

        # 交易时间段定义
        self.spread_periods = [  # 价差参数时间段（4段）
            (datetime.time(9, 30), datetime.time(10, 30)),
            (datetime.time(10, 30), datetime.time(11, 30)),
            (datetime.time(13, 0), datetime.time(14, 0)),
            (datetime.time(14, 0), datetime.time(14, 57))
        ]

        self.trade_periods = [  # 成交参数时间段（8段）
            (datetime.time(9, 30), datetime.time(10, 0)),
            (datetime.time(10, 0), datetime.time(10, 30)),
            (datetime.time(10, 30), datetime.time(11, 0)),
            (datetime.time(11, 0), datetime.time(11, 30)),
            (datetime.time(13, 0), datetime.time(13, 30)),
            (datetime.time(13, 30), datetime.time(14, 0)),
            (datetime.time(14, 0), datetime.time(14, 30)),
            (datetime.time(14, 30), datetime.time(14, 57))
        ]

        # 加载参数估计结果
        self.load_parameters()
        # 创建插值函数
        self.phi_interp = [None] * n_s
    def load_parameters(self):
        """加载价差转移和成交强度参数"""
        # 1. 调用价差参数估计代码
        print("Estimating spread parameters...")
        # 这里需要替换为实际从params_spread_esti.py获取的结果
        self.stat_trans_matrix,self.spread_lambda_dict,self.intensity_matrix_dict = params_spread_esti_main()  # 状态转移概率矩阵
        self.stat_trans_matrix = self.stat_trans_matrix.loc[0.001:0.004,0.001:0.004] #只取有效价差部分(1-4)
        self.stat_trans_matrix = self.stat_trans_matrix.values
        for spread_period_number in range(1,5):
            self.intensity_matrix_dict[spread_period_number] = self.intensity_matrix_dict[spread_period_number].loc[0.001:0.004,0.001:0.004]
        # 2. 调用成交参数估计代码
        print("Estimating trade parameters...")
        self.trade_params = params_trade_esti_main()  # 获取Hawkes过程参数

    def time_to_spread_period(self, t_index):
        """将时间索引转换为价差参数时间段(1-4)"""
        t_seconds = t_index * self.h
        t_time = (datetime.datetime.combine(datetime.date.today(), datetime.time(9, 30)) +
                  datetime.timedelta(seconds=t_seconds)).time()

        for i, (start, end) in enumerate(self.spread_periods):
            if start <= t_time < end:
                return i+1
        return 4  # 默认为最后一个时间段

    def time_to_trade_period(self, t_index):
        """将时间索引转换为成交参数时间段(1-8)"""
        t_seconds = t_index * self.h
        t_time = (datetime.datetime.combine(datetime.date.today(), datetime.time(9, 30)) +
                  datetime.timedelta(seconds=t_seconds)).time()

        for i, (start, end) in enumerate(self.trade_periods):
            if start <= t_time < end:
                return i+1
        return 8  # 默认为最后一个时间段

    def update_interp(self, t):
        """更新值函数插值器"""
        for s_idx in range(len(self.spreads)):
            self.phi_interp[s_idx] = interp1d(
                self.y_grid, self.phi[:, t, s_idx],
                kind='linear', fill_value="extrapolate"
            )

    def solve(self):
        """使用有限差分法求解最优策略"""
        n_y, n_t, n_s = len(self.y_grid), len(self.t_grid), len(self.spreads)
        t_end = n_t - 1  # 最后一个时间点索引

        # 设置终端条件
        for i in range(n_s):  # 价差状态
            spread_value = (i + 1) * self.delta  # 实际价差值
            for j in range(n_y):  # 存货
                y = self.y_grid[j]
                self.phi[j, t_end, i] = -abs(y) * spread_value / 2

        # 倒向递推（从倒数第二个时间点到正数第一个）
        for t in range(t_end - 1, -1, -1):
            spread_period = self.time_to_spread_period(t)  # 价差参数时间段
            trade_period = self.time_to_trade_period(t)  # 成交参数时间段
            self.update_interp(t + 1)  # 更新下一时间步的插值器

            for i in range(n_s):  # 当前价差状态
                spread_value = (i + 1) * self.delta  # 实际价差值

                for j in range(n_y):  # 当前存货
                    y = self.y_grid[j] # 当前存货

                    # ================= 1. 限价买单策略优化 =================
                    best_bid_value = -np.inf
                    best_bid_q = None
                    best_bid_lb = 0

                    # 尝试两种买单类型
                    for q_type in ['bid_best', 'bid_higher']:  # 0=Bb, 1=Bb+
                        # 获取成交强度
                        lambda_b = self.trade_params[(spread_value, q_type)]['lambda_vals'][t]

                        # 计算每单位收益
                        if q_type == 'bid_best':  # Bb
                            profit_per_unit = spread_value / 2
                        else: # Bb+ (高于最优买价一个单位)
                            profit_per_unit = spread_value / 2 - self.delta

                        # 定义目标函数（最大化值函数增量）
                        def bid_obj(lb):
                            """限价买单目标函数"""
                            # 计算新存货位置
                            new_y = y + lb * lambda_b * 4
                            new_j = np.argmin(np.abs(self.y_grid - new_y))

                            # 计算值函数增量 值函数增量 = 收益 + 未来值函数
                            profit = profit_per_unit * lb * lambda_b * 4
                            value_increase = profit + self.phi[new_j, t + 1, i]
                            return -value_increase  # 最小化负值函数

                        # 优化委托量
                        res = minimize_scalar(
                            bid_obj,
                            bounds=(0, self.max_trade_limit),
                            method='bounded'
                        )

                        # 更新最佳买单策略
                        if -res.fun > best_bid_value:
                            best_bid_value = -res.fun
                            if q_type == 'bid_best':
                                best_bid_q = 0
                            else:
                                best_bid_q = 1
                            best_bid_lb = res.x

                    # ================= 2. 限价卖单策略优化 =================
                    best_ask_value = -np.inf
                    best_ask_q = None
                    best_ask_la = 0

                    # 尝试两种卖单类型
                    for q_type in ['ask_best', 'ask_lower']:  # 0=Ba, 1=Ba-
                        # 获取成交强度（ask_best和ask_lower对应索引2和3）
                        lambda_a = self.trade_params[(spread_value, q_type)]['lambda_vals'][t]

                        # 计算每单位收益
                        if q_type == 'ask_best':  # Ba
                            profit_per_unit = spread_value / 2
                        else: # Ba- (低于最优卖价一个单位)
                            profit_per_unit = spread_value / 2 - self.delta

                        def ask_obj(la):
                            """限价卖单目标函数"""
                            # 计算新存货位置
                            new_y = y - la * lambda_a * 4
                            new_j = np.argmin(np.abs(self.y_grid - new_y))

                            # 计算值函数增量 值函数增量 = 收益 + 未来值函数
                            profit = profit_per_unit * la * lambda_a * 4
                            value_increase = profit + self.phi[new_j, t + 1, i]
                            return -value_increase

                        # 优化委托量
                        res = minimize_scalar(
                            ask_obj,
                            bounds=(0, self.max_trade_limit),
                            method='bounded'
                        )

                        # 更新最佳卖单策略
                        if -res.fun > best_ask_value:
                            best_ask_value = -res.fun
                            if q_type == 'ask_best':
                                best_ask_q = 0
                            else:
                                best_ask_q = 1
                            best_ask_la = res.x

                    # 保存限价单策略
                    self.qb[j, t, i] = best_bid_q
                    self.lb[j, t, i] = best_bid_lb
                    self.qa[j, t, i] = best_ask_q
                    self.la[j, t, i] = best_ask_la

                    # ================= 3. 计算限价策略下的值函数 =================
                    # 价差转移期望项
                    price_expect = self.phi[j, t + 1, i]

                    # 价差转移期望项
                    phi_next = np.zeros(n_s)  # 列向量
                    # 填充值函数
                    for s_idx_next in range(n_s):
                        phi_next[s_idx_next] = self.phi[j, t + 1, s_idx_next]
                    # 计算dd项
                    current_spread_sum = 0
                    for b in range(n_s):
                        # self.intensity_matrix_dict[spread_period].loc[b] 是1xn_s向量
                        # phi_next是n_sx1向量
                        current_spread = (b+1)*self.delta
                        current_spread_unit = np.dot(np.array(self.intensity_matrix_dict[spread_period].loc[current_spread]), phi_next)
                        current_spread_sum += current_spread_unit
                    # 计算ee项
                    next_spread_sum = 0
                    for c in range(n_s):
                        # phi_next是1xn_s向量
                        # self.intensity_matrix_dict[spread_period][next_spread] 是n_sx1向量
                        next_spread = (c+1)*self.delta
                        next_spread_unit = np.dot(phi_next, np.array(self.intensity_matrix_dict[spread_period][next_spread]))
                        next_spread_sum += next_spread_unit

                    transfer_term = current_spread_sum - next_spread_sum

                    # 存货惩罚项
                    # max_inventory实际上是能接受的一笔交易的最大张数（max_inventory代表的是一次成交所能接受的最大张数），这里我们认为它就等于库存的最大限制。
                    # 如果库存最大限制比max_inventory更大，可以设计库存最大限制参数，并添加在惩罚项里边, 如果改了惩罚函数的形式，似乎也不需要动终端条件或差分格式
                    penalty = -self.h * self.gamma * y**2

                    # 限价策略值函数
                    phi0 = 0.25 * (price_expect + transfer_term + penalty + best_bid_value + best_ask_value)

                    # ================= 4. 市价单策略优化 =================
                    def market_obj(e):
                        """市价单目标函数"""
                        # 计算成本
                        cost = spread_value / 2 * abs(e)
                        # 计算新存货位置
                        new_y = y + e
                        new_y_idx = np.argmin(np.abs(self.y_grid - new_y))
                        # 值函数 = 成本 - 未来值函数
                        future_value = self.phi[new_y_idx, t + 1, i]
                        return cost-future_value

                    # 优化委托量
                    res_mkt = minimize_scalar(
                        market_obj,
                        bounds=(-1*self.max_trade_market, self.max_trade_market),
                        method='bounded'
                    )

                    market_value = -res_mkt.fun
                    self.ea[j, t, i] = res_mkt.x

                    # ================= 5. 选择最优策略 =================
                    if phi0 >= market_value:  # 比较限价策略值 vs 市价策略净效应
                        self.phi[j, t, i] = phi0  # 选择限价
                        # 如果选择限价单，则取消市价单
                        self.ea[j, t, i] = 0
                    else:
                        self.phi[j, t, i] = market_value  # 选择市价
                        #### 如果选择市价单，也不取消限价单
                        ####self.lb[j, t, i] = 0
                        ####self.la[j, t, i] = 0

            # 打印进度
            if t % 50 == 0:
                print(
                    f"Progress: t={t}/{t_end}, time={self.t_grid[t] * self.h} sec, phi_min={np.min(self.phi[:, t, :]):.2f}, phi_max={np.max(self.phi[:, t, :]):.2f}")

    def visualize_strategy(self, y_level=50, s_level=0.001, t_start=0, t_end=10000):
        """可视化策略结果"""
        # 找到指定存货水平和价差状态的索引
        y_idx = np.argmin(np.abs(self.y_grid - y_level))
        s_idx = np.argmin(np.abs(np.array(self.spreads) * self.delta - s_level))

        # 创建时间轴
        times = [f"{(t * self.h) // 3600 + 9}:{(t * self.h % 3600) // 60:02d}"
                 for t in range(t_start, min(t_end, len(self.t_grid)))]

        plt.figure(figsize=(15, 12))
        # 1. 市价单策略
        plt.subplot(3, 1, 1)
        plt.plot(times, self.ea[y_idx, t_start:t_end, s_idx], 'b-o')
        plt.title(f"Market Order Strategy (Spread={s_level:.4f}, Inventory={y_level})")
        plt.ylabel("Order Size")
        plt.grid(True)

        # 2. 限价买单策略
        plt.subplot(3, 1, 2)
        plt.plot(times, self.lb[y_idx, t_start:t_end, s_idx], 'r-o', label='Bid Volume')
        plt.plot(times, self.qb[y_idx, t_start:t_end, s_idx], 'g--', label='Bid Type (0=Bb, 1=Bb+)')
        plt.title(f"Limit Buy Strategy (Spread={s_level:.4f}, Inventory={y_level})")
        plt.ylabel("Volume / Type")
        plt.legend()
        plt.grid(True)

        # 3. 限价卖单策略
        plt.subplot(3, 1, 3)
        plt.plot(times, self.la[y_idx, t_start:t_end, s_idx], 'r-o', label='Ask Volume')
        plt.plot(times, self.qa[y_idx, t_start:t_end, s_idx], 'g--', label='Ask Type (0=Ba, 1=Ba-)')
        plt.title(f"Limit Sell Strategy (Spread={s_level:.4f}, Inventory={y_level})")
        plt.ylabel("Volume / Type")
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        plt.savefig('market_making_strategy.png', dpi=300)
        plt.show()

    def save_results(self):
        """保存策略结果"""
        np.savez('results\\market_making_strategy_20250702.npz',
                 y_grid=self.y_grid,
                 t_grid=self.t_grid,
                 spreads=self.spreads,
                 lb=self.lb,
                 qb=self.qb,
                 la=self.la,
                 qa=self.qa,
                 ea=self.ea,
                 phi=self.phi)


def plot_strategy_results(file_path, y_level=50, s_level=0.001):
    """
    绘制市场做市策略结果的图像。

    :param file_path: npz文件的路径
    :param y_level: 存货水平
    :param s_level: 价差状态
    """
    # 加载npz文件
    results = np.load(file_path)

    # 获取数据
    y_grid = results['y_grid']
    t_grid = results['t_grid']
    spreads = results['spreads']
    lb = results['lb']
    qb = results['qb']
    la = results['la']
    qa = results['qa']
    ea = results['ea']

    # 找到指定存货水平和价差状态的索引
    y_idx = np.argmin(np.abs(y_grid - y_level))
    s_idx = np.argmin(np.abs(np.array(spreads) * 0.001 - s_level))

    # 创建时间轴
    times = [f"{(t * 15) // 3600 + 9}:{(t * 15 % 3600) // 60:02d}" for t in t_grid]

    # 定义绘图函数
    def plot_single_graph(data, title, ylabel, color):
        plt.figure(figsize=(15, 6))
        # plt.plot(times, data[y_idx, :, s_idx], f'{color}-o')
        plt.plot(t_grid, data[y_idx, :, s_idx], f'{color}-o')
        plt.title(title)
        plt.xlabel("Time")
        plt.ylabel(ylabel)
        plt.grid(True)
        plt.tight_layout()
        plt.show()

    # 1. 绘制lb图像
    plot_single_graph(lb, f"Limit Buy Volume (Spread={s_level:.4f}, Inventory={y_level})", "Volume", 'r')

    # 2. 绘制qb图像
    plot_single_graph(qb, f"Limit Buy Type (Spread={s_level:.4f}, Inventory={y_level})", "Type (0=Bb, 1=Bb+)", 'g')

    # 3. 绘制la图像
    plot_single_graph(la, f"Limit Sell Volume (Spread={s_level:.4f}, Inventory={y_level})", "Volume", 'b')

    # 4. 绘制qa图像
    plot_single_graph(qa, f"Limit Sell Type (Spread={s_level:.4f}, Inventory={y_level})", "Type (0=Ba, 1=Ba-)", 'm')

    # 5. 绘制ea图像
    plot_single_graph(ea, f"Market Order Size (Spread={s_level:.4f}, Inventory={y_level})", "Order Size", 'c')


if __name__ == "__main__":

    print("Initializing Market Making Strategy Solver...")
    mm = MarketMakerStrategy()

    print("Starting Strategy Optimization...")
    mm.solve()

    #print("Visualizing Results...")
    #mm.visualize_strategy(y_level=5000, s_level=0.002, t_start=100, t_end=200)

    print("Saving Results...")
    mm.save_results()

    print("Optimization Complete!")

    '''
    file_path = 'results\\market_making_strategy_20250702.npz'
    #results = np.load(file_path)
    plot_strategy_results(file_path,y_level=0, s_level=0.001)
    '''
