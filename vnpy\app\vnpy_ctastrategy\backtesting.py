from collections import defaultdict
from datetime import (
    date as Date,
    datetime,
    timedelta
)
from typing import Callable
from functools import lru_cache, partial, reduce
import traceback

import pandas as pd
import numpy as np
import plotly
from pandas import DataFrame, Series
from pandas.core.window import ExponentialMovingWindow
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from vnpy.trader.constant import (
    Direction,
    Offset,
    Exchange,
    Interval,
    Status
)
from vnpy.trader.database import get_database, BaseDatabase
from vnpy.trader.object import OrderData, TradeData, BarData, TickData
from vnpy.trader.utility import round_to, extract_vt_symbol
from vnpy.trader.optimize import (
    OptimizationSetting,
    check_optimization_setting,
    run_bf_optimization,
    run_ga_optimization
)

from .base import (
    BacktestingMode,
    EngineType,
    STOPORDER_PREFIX,
    StopOrder,
    StopOrderStatus,
    INTERVAL_DELTA_MAP,
    DataMode
)
from .template import CtaTemplate
from .locale import _
import time
from tqdm import tqdm


class BacktestingEngine:
    """"""

    engine_type: EngineType = EngineType.BACKTESTING
    gateway_name: str = "BACKTESTING"

    def __init__(self) -> None:
        """"""
        self.vt_symbol: str = ""
        self.symbol: str = ""
        self.exchange: Exchange
        self.start: datetime
        self.end: datetime
        self.rate: float = 0
        self.slippage: float = 0
        self.size: float = 1
        self.pricetick: float = 0
        self.capital: int = 1_000_000
        self.risk_free: float = 0
        self.annual_days: int = 240
        self.half_life: int = 120
        self.mode: BacktestingMode = BacktestingMode.BAR
        self.datamode: DataMode = DataMode.SNAPSHOT_MODE

        self.strategy_class: type[CtaTemplate]
        self.strategy: CtaTemplate
        self.tick: TickData
        self.bar: BarData
        self.datetime: datetime = datetime(1970, 1, 1)

        self.interval: Interval
        self.days: int = 0
        self.callback: Callable
        self.history_data: list = []

        self.stop_order_count: int = 0
        self.stop_orders: dict[str, StopOrder] = {}
        self.active_stop_orders: dict[str, StopOrder] = {}

        self.limit_order_count: int = 0
        self.limit_orders: dict[str, OrderData] = {}
        self.active_limit_orders: dict[str, OrderData] = {}

        self.trade_count: int = 0
        self.trades: dict[str, TradeData] = {}

        self.logs: list = []

        self.daily_results: dict[Date, DailyResult] = {}
        self.daily_df: DataFrame = DataFrame()
        # -----------------------------------------
        self.realtime_result = {}
        self.realtime_df = None

        self.tickmaxnum = None
        self.pids = None
        self.fixrate = 0

        self.capitalList = []  # 盈亏汇总
        self.timeList = []  # 成交价时间序列
        self.priceList = []

    def clear_data(self) -> None:
        """
        Clear all data of last backtesting.
        """
        self.strategy = None
        self.tick = None
        self.bar = None
        self.datetime = None

        self.stop_order_count = 0
        self.stop_orders.clear()
        self.active_stop_orders.clear()

        self.limit_order_count = 0
        self.limit_orders.clear()
        self.active_limit_orders.clear()

        self.trade_count = 0
        self.trades.clear()

        self.logs.clear()
        self.daily_results.clear()

    def set_parameters(
            self,
            vt_symbol: str,
            interval: Interval,
            start: datetime,
            rate: float,
            slippage: float,
            size: float,
            pricetick: float,
            pids: list = None,
            capital: int = 0,
            tickmaxnum: int = 1,
            end: datetime = None,
            mode: BacktestingMode = BacktestingMode.TICK,
            datamode: DataMode = DataMode.SNAPSHOT_MODE,
            risk_free: float = 0,
            annual_days: int = 240,
            half_life: int = 120
    ) -> None:
        """"""
        self.mode = mode
        self.datamode = datamode
        self.vt_symbol = vt_symbol
        self.pids = pids
        self.interval = Interval(interval)
        self.rate = rate
        self.slippage = slippage
        self.size = size
        self.pricetick = pricetick
        self.start = start

        self.symbol, exchange_str = self.vt_symbol.split(".")
        self.exchange = Exchange(exchange_str)

        self.capital = capital
        self.tickmaxnum = tickmaxnum

        self.end = end if end else datetime.now().replace(hour=23, minute=59, second=59)

        self.mode = mode
        self.risk_free = risk_free
        self.annual_days = annual_days
        self.half_life = half_life

    def add_strategy(self, strategy_class: type[CtaTemplate], setting: dict) -> None:
        """"""
        self.strategy_class = strategy_class
        self.strategy = strategy_class(
            self, strategy_class.__name__, self.vt_symbol, setting
        )

    def loadHistoryDataCsv(self, csv_path, date):
        """载入历史数据"""
        df = pd.read_parquet(csv_path)

        colCF = {
            "timestamp_str": "time", "Symbol": "symbol", "LastPrice": "last_price", "Volume": "volume",
            "BidPrice1": "bid_price_1", "BidPrice2": "bid_price_2", "BidPrice3": "bid_price_3",
            "BidPrice4": "bid_price_4", "BidPrice5": "bid_price_5",
            "AskPrice1": "ask_price_1", "AskPrice2": "ask_price_2", "AskPrice3": "ask_price_3",
            "AskPrice4": "ask_price_4", "AskPrice5": "ask_price_5",
            "BidVol1": "bid_volume_1", "BidVol2": "bid_volume_2", "BidVol3": "bid_volume_3",
            "BidVol4": "bid_volume_4", "BidVol5": "bid_volume_5",
            "AskVol1": "ask_volume_1", "AskVol2": "ask_volume_2", "AskVol3": "ask_volume_3",
            "AskVol4": "ask_volume_4", "AskVol5": "ask_volume_5"
        }
        colCXFD = {
            "txDate": "date", "txTime": "time", "Pid": "symbol", "Dp": "last_price", "Dv": "last_volume",
            "Bp1": "bid_price_1", "Bp2": "bid_price_1", "Bp3": "bid_price_1", "Bp4": "bid_price_1",
            "Bp5": "bid_price_1",
            "Sp1": "askPrice1", "Sp2": "askPrice2", "Sp3": "askPrice3", "Sp4": "askPrice4", "Sp5": "askPrice5",
            "Bv1": "bidVolume1", "Bv2": "bidVolume2", "Bv3": "bidVolume3", "Bv4": "bidVolume4",
            "Bv5": "bidVolume5",
            "Sv1": "askVolume1", "Sv2": "askVolume2", "Sv3": "askVolume3", "Sv4": "askVolume4",
            "Sv5": "askVolume5",
            "Tvolume": "volume"
        }

        df = df.rename(columns={**colCF, **colCXFD})
        self.output(df.symbol.unique())
        df = df[df.symbol.isin(self.pids)] if df.symbol.isin(self.pids).any() else self.output(('no ', self.pids))
        self.output(df.symbol.unique())

        df['datetime'] = date + " " + df['time'].astype(str)
        if self.exchange == Exchange.CFFEX:
            df = df[df['bid_price_1'] != 0]
            df['datetime'] = pd.to_datetime(df['datetime'], format='%Y%m%d %H:%M:%S.%f')
        elif self.pids[0] == 'CXFD8':
            df['datetime'] = df['date'].astype(str) + " " + df['time'].astype(str)
            df['datetime'] = pd.to_datetime(df['datetime'], format='%Y/%m/%d %H%M%S%f')

        df["symbol"] = df["symbol"].apply(lambda x: x.strip())

        row = df.to_dict('records')

        self.output(u'开始载入资料')

        if self.mode == BacktestingMode.BAR:
            dataClass = BarData
        else:
            dataClass = TickData

        data = None
        for d in row:
            data = dataClass(gateway_name='CSV', symbol=self.symbol, exchange=self.exchange,
                             datetime=d['datetime'])
            data.__dict__ = d
            data.exchange = self.exchange
            data.gateway_name = 'CSV'
            self.history_data.append(data)

        self.output(f"{data.datetime}历史数据加载完成，数据量：{len(self.history_data)}")

    def load_data(self) -> None:
        """"""
        self.output(_("开始加载历史数据"))

        if not self.end:
            self.end = datetime.now()

        if self.start >= self.end:
            self.output(_("起始日期必须小于结束日期"))
            return

        self.history_data.clear()       # Clear previously loaded history data

        # Load 30 days of data each time and allow for progress update
        total_days: int = (self.end - self.start).days
        progress_days: int = max(int(total_days / 10), 1)
        progress_delta: timedelta = timedelta(days=progress_days)
        interval_delta: timedelta = INTERVAL_DELTA_MAP[self.interval]

        start: datetime = self.start
        end: datetime = self.start + progress_delta
        progress: float = 0

        while start < self.end:
            progress_bar: str = "#" * int(progress * 10 + 1)
            self.output(_("加载进度：{} [{:.0%}]").format(progress_bar, progress))

            end = min(end, self.end)  # Make sure end time stays within set range

            if self.mode == BacktestingMode.BAR:
                data: list[BarData] = load_bar_data(
                    self.symbol,
                    self.exchange,
                    self.interval,
                    start,
                    end
                )
            else:
                data = load_tick_data(
                    self.symbol,
                    self.exchange,
                    start,
                    end
                )

            self.history_data.extend(data)

            progress += progress_days / total_days
            progress = min(progress, 1)

            start = end + interval_delta
            end += progress_delta

        self.output(_("历史数据加载完成，数据量：{}").format(len(self.history_data)))

# -------------------------------------------------------------------------------------
    def run_backtesting(self) -> None:
        """"""
        func = None
        if len(self.pids) == 1:
            mode_func_map = {
                BacktestingMode.BAR: self.new_bar,
                BacktestingMode.TRADE: self.newTrade,
                BacktestingMode.BOOK: self.newBook,
                BacktestingMode.TICK: self.newTradeBook
            }
            func = mode_func_map.get(self.mode)
        elif len(self.pids) == 2 and self.mode == BacktestingMode.REAL:
            func = self.multinewTradeBook

        self.strategy.on_init()
        self.strategy.inited = True
        self.output(_("策略初始化完成"))

        self.strategy.on_start()
        self.strategy.trading = True
        self.output(_("开始回放历史数据"))

        total_size: int = len(self.history_data)

        IR = IndiTradingResult(self.rate, self.fixrate, self.slippage, self.size)
        it = 0

        self.capitalList = np.empty(total_size, dtype=np.float64)
        self.timeList = np.empty(total_size, dtype=object)
        self.priceList = np.empty(total_size, dtype=np.float64)
        try:
            start_time = time.time()
            for idx, data in enumerate(tqdm(self.history_data)):
                func(data)
                trades = list(self.trades.values())[it:]
                for trade in trades:
                    IR.calculate(trade.price, trade.volume, trade.direction)
                    it += 1
                if len(self.trades) == it:
                    IR.calculate(data.last_price, 0)

                self.capitalList[idx] = IR.net_pnl
                self.timeList[idx] = data.datetime
                self.priceList[idx] = data.last_price

        except Exception:
            self.output("触发异常，回测终止")
            self.output(traceback.format_exc())
        finally:
            self.strategy.on_stop()
            self.output(f"回放时间: {time.time() - start_time:.2f}秒")
            self.output("历史数据回放结束")

    def calculate_result(self) -> DataFrame:
        """"""
        self.output(_("开始计算逐日盯市盈亏"))

        if not self.trades:
            self.output(_("回测成交记录为空"))

        # Add trade data into daily reuslt.
        for trade in self.trades.values():
            d: Date = trade.datetime.date()
            daily_result: DailyResult = self.daily_results[d]
            daily_result.add_trade(trade)

        # Calculate daily result by iteration.
        pre_close: float = 0
        start_pos: float = 0

        for daily_result in self.daily_results.values():
            daily_result.calculate_pnl(
                pre_close,
                start_pos,
                self.size,
                self.rate,
                self.slippage
            )

            pre_close = daily_result.close_price
            start_pos = daily_result.end_pos

        # Generate dataframe
        results: defaultdict = defaultdict(list)

        for daily_result in self.daily_results.values():
            for key, value in daily_result.__dict__.items():
                results[key].append(value)

        if results:
            self.daily_df = DataFrame.from_dict(results).set_index("date")

        self.output(_("逐日盯市盈亏计算完成"))
        return self.daily_df

    def calculate_statistics(
        self,
        df: DataFrame | None = None,
        output: bool = True
    ) -> dict:
        """"""
        self.output(_("开始计算策略统计指标"))

        # Check DataFrame input exterior
        if df is None:
            if self.daily_df.empty:
                self.output(_("回测结果为空，无法计算绩效统计指标"))
                return {}

            df = self.daily_df

        # Init all statistics default value
        start_date: str = ""
        end_date: str = ""
        total_days: int = 0
        profit_days: int = 0
        loss_days: int = 0
        end_balance: float = 0
        max_drawdown: float = 0
        max_ddpercent: float = 0
        max_drawdown_duration: int = 0
        total_net_pnl: float = 0
        daily_net_pnl: float = 0
        total_commission: float = 0
        daily_commission: float = 0
        total_slippage: float = 0
        daily_slippage: float = 0
        total_turnover: float = 0
        daily_turnover: float = 0
        total_trade_count: int = 0
        daily_trade_count: float = 0
        total_return: float = 0
        annual_return: float = 0
        daily_return: float = 0
        return_std: float = 0
        sharpe_ratio: float = 0
        ewm_sharpe: float = 0
        return_drawdown_ratio: float = 0

        # Check if balance is always positive
        positive_balance: bool = False

        if df is not None:
            # Calculate balance related time series data
            df["balance"] = df["net_pnl"].cumsum() + self.capital

            # When balance falls below 0, set daily return to 0
            pre_balance: Series = df["balance"].shift(1)
            pre_balance.iloc[0] = self.capital
            x = df["balance"] / pre_balance
            x[x <= 0] = np.nan
            df["return"] = np.log(x).fillna(0)

            df["highlevel"] = df["balance"].rolling(min_periods=1, window=len(df), center=False).max()
            df["drawdown"] = df["balance"] - df["highlevel"]
            df["ddpercent"] = df["drawdown"] / df["highlevel"] * 100

            # All balance value needs to be positive
            positive_balance = (df["balance"] > 0).all()
            if not positive_balance:
                self.output(_("回测中出现爆仓（资金小于等于0），无法计算策略统计指标"))

        # Calculate statistics value
        if positive_balance:
            # Calculate statistics value
            start_date = df.index[0]
            end_date = df.index[-1]

            total_days = len(df)
            profit_days = len(df[df["net_pnl"] > 0])
            loss_days = len(df[df["net_pnl"] < 0])

            end_balance = df["balance"].iloc[-1]
            max_drawdown = df["drawdown"].min()
            max_ddpercent = df["ddpercent"].min()
            max_drawdown_end = df["drawdown"].idxmin()

            if isinstance(max_drawdown_end, Date):
                max_drawdown_start = df["balance"][:max_drawdown_end].idxmax()      # type: ignore
                max_drawdown_duration = (max_drawdown_end - max_drawdown_start).days
            else:
                max_drawdown_duration = 0

            total_net_pnl = df["net_pnl"].sum()
            daily_net_pnl = total_net_pnl / total_days

            total_commission = df["commission"].sum()
            daily_commission = total_commission / total_days

            total_slippage = df["slippage"].sum()
            daily_slippage = total_slippage / total_days

            total_turnover = df["turnover"].sum()
            daily_turnover = total_turnover / total_days

            total_trade_count = df["trade_count"].sum()
            daily_trade_count = total_trade_count / total_days

            total_return = (end_balance / self.capital - 1) * 100
            annual_return = total_return / total_days * self.annual_days
            daily_return = df["return"].mean() * 100
            return_std = df["return"].std() * 100

            if return_std:
                daily_risk_free: float = self.risk_free / np.sqrt(self.annual_days)
                sharpe_ratio = (daily_return - daily_risk_free) / return_std * np.sqrt(self.annual_days)

                ewm_window: ExponentialMovingWindow = df["return"].ewm(halflife=self.half_life)
                ewm_mean: Series = ewm_window.mean() * 100
                ewm_std: Series = ewm_window.std() * 100
                ewm_sharpe = ((ewm_mean - daily_risk_free) / ewm_std).iloc[-1] * np.sqrt(self.annual_days)
            else:
                sharpe_ratio = 0
                ewm_sharpe = 0

            if max_ddpercent:
                return_drawdown_ratio = -total_return / max_ddpercent
            else:
                return_drawdown_ratio = 0

        # Output
        if output:
            self.output("-" * 30)
            self.output(_("首个交易日：\t{}").format(start_date))
            self.output(_("最后交易日：\t{}").format(end_date))

            self.output(_("总交易日：\t{}").format(total_days))
            self.output(_("盈利交易日：\t{}").format(profit_days))
            self.output(_("亏损交易日：\t{}").format(loss_days))

            self.output(_("起始资金：\t{:,.2f}").format(self.capital))
            self.output(_("结束资金：\t{:,.2f}").format(end_balance))

            self.output(_("总收益率：\t{:,.2f}%").format(total_return))
            self.output(_("年化收益：\t{:,.2f}%").format(annual_return))
            self.output(_("最大回撤: \t{:,.2f}").format(max_drawdown))
            self.output(_("百分比最大回撤: {:,.2f}%").format(max_ddpercent))
            self.output(_("最大回撤天数: \t{}").format(max_drawdown_duration))

            self.output(_("总盈亏：\t{:,.2f}").format(total_net_pnl))
            self.output(_("总手续费：\t{:,.2f}").format(total_commission))
            self.output(_("总滑点：\t{:,.2f}").format(total_slippage))
            self.output(_("总成交金额：\t{:,.2f}").format(total_turnover))
            self.output(_("总成交笔数：\t{}").format(total_trade_count))

            self.output(_("日均盈亏：\t{:,.2f}").format(daily_net_pnl))
            self.output(_("日均手续费：\t{:,.2f}").format(daily_commission))
            self.output(_("日均滑点：\t{:,.2f}").format(daily_slippage))
            self.output(_("日均成交金额：\t{:,.2f}").format(daily_turnover))
            self.output(_("日均成交笔数：\t{}").format(daily_trade_count))

            self.output(_("日均收益率：\t{:,.2f}%").format(daily_return))
            self.output(_("收益标准差：\t{:,.2f}%").format(return_std))
            self.output(f"Sharpe Ratio：\t{sharpe_ratio:,.2f}")
            self.output(f"EWM Sharpe：\t{ewm_sharpe:,.2f}")
            self.output(_("收益回撤比：\t{:,.2f}").format(return_drawdown_ratio))

        statistics: dict = {
            "start_date": start_date,
            "end_date": end_date,
            "total_days": total_days,
            "profit_days": profit_days,
            "loss_days": loss_days,
            "capital": self.capital,
            "end_balance": end_balance,
            "max_drawdown": max_drawdown,
            "max_ddpercent": max_ddpercent,
            "max_drawdown_duration": max_drawdown_duration,
            "total_net_pnl": total_net_pnl,
            "daily_net_pnl": daily_net_pnl,
            "total_commission": total_commission,
            "daily_commission": daily_commission,
            "total_slippage": total_slippage,
            "daily_slippage": daily_slippage,
            "total_turnover": total_turnover,
            "daily_turnover": daily_turnover,
            "total_trade_count": total_trade_count,
            "daily_trade_count": daily_trade_count,
            "total_return": total_return,
            "annual_return": annual_return,
            "daily_return": daily_return,
            "return_std": return_std,
            "sharpe_ratio": sharpe_ratio,
            "ewm_sharpe": ewm_sharpe,
            "return_drawdown_ratio": return_drawdown_ratio,
        }

        # Filter potential error infinite value
        for key, value in statistics.items():
            if value in (np.inf, -np.inf):
                value = 0
            statistics[key] = np.nan_to_num(value)

        self.output(_("策略统计指标计算完成"))
        return statistics

    def show_chart(self, df: DataFrame | None = None) -> go.Figure:
        """"""
        # Check DataFrame input exterior
        if df is None:
            df = self.daily_df

        # Check for init DataFrame
        if df.empty:
            return

        fig = make_subplots(
            rows=4,
            cols=1,
            subplot_titles=["Balance", "Drawdown", "Daily Pnl", "Pnl Distribution"],
            vertical_spacing=0.06
        )

        balance_line = go.Scatter(
            x=df.index,
            y=df["balance"],
            mode="lines",
            name="Balance"
        )

        drawdown_scatter = go.Scatter(
            x=df.index,
            y=df["drawdown"],
            fillcolor="red",
            fill='tozeroy',
            mode="lines",
            name="Drawdown"
        )
        pnl_bar = go.Bar(y=df["net_pnl"], name="Daily Pnl")
        pnl_histogram = go.Histogram(x=df["net_pnl"], nbinsx=100, name="Days")

        fig.add_trace(balance_line, row=1, col=1)
        fig.add_trace(drawdown_scatter, row=2, col=1)
        fig.add_trace(pnl_bar, row=3, col=1)
        fig.add_trace(pnl_histogram, row=4, col=1)

        fig.update_layout(height=1000, width=1000)
        fig.show()

    def calculateBacktestingTradeResult(self):
        """
        计算回测结果
        """
        self.output(u'计算回测结果')
        # 检查成交记录

        if not self.trade_count:
            self.output(u'成交记录为空，无法计算回测结果')
            return {}

        # 首先基于回测后的成交记录，计算每笔交易的盈亏

        tradeTimeList = []  # 每笔成交时间戳记
        posList = []  # 每笔成交后的持仓情况

        IR = IndiTradingResult(self.rate, self.fixrate, self.slippage, self.size)
        capitalList = []  # 盈亏汇总的时间序列

        BuyDeal = []  # 记录买成交
        BuyCapital = []
        BuyDealTime = []
        SellDeal = []  # 记录卖成交
        SellCapital = []
        SellDealTime = []
        BuyOrder = []  # 记录委託买
        BuyOrderTime = []
        SellOrder = []  # 记录委託卖
        SellOrderTime = []

        for trade in self.trades.values():
            IR.calculate(trade.price, trade.volume, trade.direction)
            if trade.direction == Direction.SHORT:
                SellDeal.append(trade.price)
                SellDealTime.append(trade.datetime)
                SellCapital.append(IR.net_pnl)
            else:
                BuyDeal.append(trade.price)
                BuyDealTime.append(trade.datetime)
                BuyCapital.append(IR.net_pnl)
            capitalList.append(IR.net_pnl)
            tradeTimeList.append(trade.datetime)
            posList.append(IR.end_pos)

        for order in self.limit_orders.values():
            if order.direction == Direction.SHORT:
                SellOrder.append(order.price)
                SellOrderTime.append(order.datetime)
            else:
                BuyOrder.append(order.price)
                BuyOrderTime.append(order.datetime)

        # 然后基于每笔交易的结果，我们可以计算具体的盈亏曲线和最大回撤等
        capital = IR.net_pnl  # 资金
        maxCapital = max(capital, 0)  # 资金最高淨值
        drawdown = capital - maxCapital  # 回撤

        totalResult = IR.totalVolume  # 总成交数量
        totalTurnover = IR.Capital  # 总成交金额（合约面值）
        totalRate = IR.commission  # 百分比手续费
        totalFixRate = IR.fixrate  # 固定手续费
        totalSlippage = IR.slippages  # 总滑点

        pnlList = []  # 每笔盈亏序列
        drawdownList = []  # 回撤的时间序列

        pnlList.append(IR.net_pnl)

        drawdownList.append(drawdown)

        # 返回回测结果
        d = {
            'capital': capital,
            'maxCapital': maxCapital,
            'drawdown': drawdown,
            'totalResult': totalResult,
            'totalTurnover': totalTurnover,
            'totalCommission': totalRate + totalFixRate,
            'totalSlippage': totalSlippage,
            'timeList': tradeTimeList,
            'pnlList': pnlList,
            'capitalList': capitalList,
            'drawdownList': drawdownList,
            'posList': posList,
            'tradeTimeList': tradeTimeList,

            'BuyDeal': BuyDeal,
            'BuyCapital': BuyCapital,
            'BuyDealTime': BuyDealTime,
            'SellDeal': SellDeal,
            'SellCapital': SellCapital,
            'SellDealTime': SellDealTime,
            'BuyOrder': BuyOrder,
            'BuyOrderTime': BuyOrderTime,
            'SellOrder': SellOrder,
            'SellOrderTime': SellOrderTime
        }

        return d

    # ----------------------------------------------------------------------
    def showBacktestingResult(self, output=True):
        """显示回测结果"""
        self.realtime_result = self.calculateBacktestingTradeResult()

        self.output("逐日盯市盈亏计算完成")

        d = self.realtime_result
        # 输出
        if output:
            self.output('-' * 30)
            # self.output(u'第一笔交易平仓：\t%s' % d['timeList'][0])
            self.output(f"最后一笔交易平仓：\t{d['timeList'][-1]}")

            self.output(f"总交易次数：\t{d['totalResult']:,.2f}")
            self.output(f"总成交金额：\t{d['totalTurnover']:,.2f}")
            self.output(f"总盈亏：\t{d['capital']:,.2f}")
            self.output(f"最大回撤：\t{min(d['drawdownList']):,.2f}")

            self.output(f"平均每笔盈利：\t{d['capital'] / d['totalResult']:,.2f}")
            self.output(f"平均每笔滑点：\t{d['totalSlippage'] / d['totalResult']:,.2f}")
            self.output(f"平均每笔佣金：\t{d['totalCommission'] / d['totalResult']:,.2f}")

            # self.output(u'胜率\t\t%s%%' % formatNumber(self.strategy.bingo/self.strategy.round*100))

        return self.realtime_df

    # ----------------------------------------------------------------------
    def plotResultWithTime(self, logpath, save=False) -> None:
        """"""
        import plotly.io as pio
        pio.renderers.default = "browser"
        d = self.realtime_result
        df1 = pd.DataFrame({'S': self.priceList, 'capital_all': self.capitalList}, index=self.timeList)
        dft = pd.DataFrame({'capital_trade': d['capitalList'], 'posList': d['posList']}, index=d['timeList'])
        dfb = pd.DataFrame({'capital_buy': d['BuyCapital']}, index=d['BuyDealTime'])
        dfs = pd.DataFrame({'capital_sell': d['SellCapital']}, index=d['SellDealTime'])
        df2 = pd.DataFrame({'BuyOrder': d['BuyOrder']}, index=d['BuyOrderTime'])
        df3 = pd.DataFrame({'SellOrder': d['SellOrder']}, index=d['SellOrderTime'])

        df = reduce(lambda left, right: pd.merge(left, right, how='left', left_index=True, right_index=True),
                    [df1, df2, df3, dft, dfb, dfs])

        for date0 in self.daily_results.keys():
            fig = make_subplots(rows=3, cols=1, subplot_titles=["capital", self.symbol, "position"],
                                row_heights=[0.3, 0.5, 0.2], vertical_spacing=0.10, shared_xaxes=True)

            fig.add_trace(go.Scatter(x=df.index, y=df["capital_all"], mode='lines', name='capital_trade',
                                     line=dict(color='lightblue', dash='solid', width=1)), row=1, col=1)
            fig.add_trace(go.Scatter(x=df.index, y=df["capital_buy"], mode='markers',
                                     marker=dict(color='lightcoral', size=10, opacity=0.8, symbol='arrow-up', ),
                                     name='capital_buy', line=dict(color='cyan')), row=1, col=1)
            fig.add_trace(go.Scatter(x=df.index, y=df["capital_sell"], mode='markers',
                                     marker=dict(color='lightgreen', size=10, opacity=0.8, symbol='arrow-down', ),
                                     name='capital_sell', line=dict(color='cyan')), row=1, col=1)

            fig.add_trace(
                go.Scatter(x=df.index, y=df["S"], line=dict(color='cyan', dash='solid', width=1), mode='lines',
                           name="s"), row=2, col=1)
            fig.add_trace(
                go.Scatter(x=df.index, y=df["BuyOrder"], line=dict(color='red'), mode='markers', name="BuyOrder",
                           marker=dict(color='pink', size=10, opacity=0.8, symbol='arrow-up', ),
                           ),
                row=2, col=1)
            fig.add_trace(
                go.Scatter(x=df.index, y=df["SellOrder"], line=dict(color='green'), mode='markers', name="SellOrder",
                           marker=dict(color='forestgreen', size=10, opacity=0.8, symbol='arrow-down', ), ), row=2,
                col=1)

            fig.add_trace(
                go.Scatter(x=df.index, y=df["posList"].ffill(), name="posList", fill='tozerox', fillcolor='white',
                           mode="lines",
                           # fillpattern=dict(type='horizontal',
                           #                   colorscale=[(0.0, 'darkblue'), (0.5, 'royalblue'), (1.0, 'cyan')]),
                           line=dict(color='rgba(255,255,255,0)', dash='dash', width=0.1, ), ), row=3, col=1)

            fig.update_xaxes(
                # rangeslider_visible=True,# 优化1： 区间滑块【默认】关闭滑动区间
                rangeslider=dict(visible=True, thickness=0.01,  # 设置滑轨的厚度为轴域的5%
                                 bgcolor='black'  # 设置滑轨的背景颜色
                                 ), )

            fig.update_xaxes(
                rangeselector=dict(buttons=list([  # 优化2：时间按钮
                    dict(count=1, label="1h", step="hour", stepmode="backward"),  # 往前推1h
                    dict(count=2, label="2h", step="hour", stepmode="backward"),  # 往前推2h
                    dict(count=1, label="TD", step="day", stepmode="todate"),  # 只显示今天数据
                    dict(count=1, label="1m", step="month", stepmode="backward"),  # 显示过去一月的数据
                    dict(step="all")  # 显示全部数据
                ]),
                    font=dict(size=10, color='white'),  # 按钮文本的字体大小
                    bgcolor="black",  # 按钮的背景颜色
                    bordercolor="black",  # 按钮的边框颜色
                    borderwidth=1,  # 按钮的边框宽度
                )
            )
            fig.update_layout(
                height=1000, width=1000,
                title=date0.strftime('%Y%m%d') + ' PNL RESULT', title_font=dict(size=20, color='white'),
                plot_bgcolor='black', paper_bgcolor='black', font_color='white',
                xaxis=dict(showline=True, showgrid=True, zeroline=False, ), yaxis=dict(showgrid=True, ),
                # xaxis_title='Year',yaxis_title='Amount',
                legend=dict(bgcolor='black', bordercolor='black', font=dict(color='white'))
            )
            fig.update_xaxes(rangebreaks=[dict(bounds=["sat", "mon"]), ])
            fig.update_xaxes(rangebreaks=[dict(bounds=[11.5, 13], pattern="hour")])
            fig.show()

            if save:
                plotly.offline.plot(fig, filename=logpath + '\%s_%s.html' % (self.pids[0], date0.strftime('%Y%m%d')))
            # pio.write_image(fig,logpath + '\%s_%s.png' % (self.pids[0], date0.strftime('%Y%m%d')))

    def run_bf_optimization(
        self,
        optimization_setting: OptimizationSetting,
        output: bool = True,
        max_workers: int | None = None
    ) -> list:
        """"""
        if not check_optimization_setting(optimization_setting):
            return []

        evaluate_func: Callable = wrap_evaluate(self, optimization_setting.target_name)
        results: list = run_bf_optimization(
            evaluate_func,
            optimization_setting,
            get_target_value,
            max_workers=max_workers,
            output=self.output
        )

        if output:
            for result in results:
                msg: str = _("参数：{}, 目标：{}").format(result[0], result[1])
                self.output(msg)

        return results

    run_optimization = run_bf_optimization

    def run_ga_optimization(
        self,
        optimization_setting: OptimizationSetting,
        output: bool = True,
        max_workers: int | None = None,
        pop_size: int = 100,
        ngen: int = 30,
        mu: int | None = None,
        lambda_: int | None = None,
        cxpb: float = 0.95,
        mutpb: float | None = None,
        indpb: float = 1.0
    ) -> list:
        """"""
        if not check_optimization_setting(optimization_setting):
            return []

        evaluate_func: Callable = wrap_evaluate(self, optimization_setting.target_name)
        results: list = run_ga_optimization(
            evaluate_func,
            optimization_setting,
            get_target_value,
            max_workers=max_workers,
            pop_size=pop_size,
            ngen=ngen,
            mu=mu,
            lambda_=lambda_,
            cxpb=cxpb,
            mutpb=mutpb,
            indpb=indpb,
            output=self.output
        )

        if output:
            for result in results:
                msg: str = _("参数：{}, 目标：{}").format(result[0], result[1])
                self.output(msg)

        return results

    def update_daily_close(self, price: float) -> None:
        """"""
        d: Date = self.datetime.date()

        daily_result: DailyResult | None = self.daily_results.get(d, None)
        if daily_result:
            daily_result.close_price = price
        else:
            self.daily_results[d] = DailyResult(d, price)

    def new_bar(self, bar: BarData) -> None:
        """"""
        self.bar = bar
        self.datetime = bar.datetime

        self.cross_limit_order()
        self.cross_stop_order()
        self.strategy.on_bar(bar)

        self.update_daily_close(bar.close_price)

    def new_tick(self, tick: TickData) -> None:
        """"""
        self.tick = tick
        self.datetime = tick.datetime

        self.cross_limit_order()
        self.cross_stop_order()
        self.strategy.on_tick(tick)

        self.update_daily_close(tick.last_price)

    # ----------------------------------------------------------------------
    def newTradeBook(self, tick):
        if self.datamode == DataMode.SNAPSHOT_MODE:
            self.newSnapShotTrade(tick)
        else:
            if tick.lastPrice > 0:
                self.newTrade(tick)
            else:
                self.newBook(tick)

    # ----------------------------------------------------------------------
    def multinewTradeBook(self, tick):
        if tick.symbol == self.pids[0]:
            if tick.lastPrice > 0:
                self.newTrade(tick)
            else:
                self.newBook(tick)
        elif tick.symbol == self.pids[1]:
            if tick.lastPrice > 0:
                self.refnewTrade(tick)
            else:
                self.refnewBook(tick)

    # ----------------------------------------------------------------------
    def newSnapShotTrade(self, tick):
        """新的Tick"""
        self.tick = tick
        self.datetime = tick.datetime

        self.strategy.on_tick(tick)

        self.cross_limit_order()
        # self.workingRODLimitOrderDict = {}
        # self.workingIOCLimitOrderDict = {}
        # self.crossStopOrder()

        self.update_daily_close(tick.last_price)

    # ----------------------------------------------------------------------
    def newTrade(self, tick):
        """新的Tick"""
        self.tick = tick
        self.datetime = tick.datetime

        self.strategy.onTick(tick)

        self.cross_limit_order()

        self.update_daily_close(tick.last_price)

    # ----------------------------------------------------------------------
    def newBook(self, tick):
        """新的Trade"""
        self.tick = tick
        self.datetime = tick.datetime

        self.cross_limit_order()

        self.strategy.onBook(tick)

        # self.updateDailyClose(tick.datetime, tick.last_price)

    # ----------------------------------------------------------------------
    def refnewTrade(self, tick):
        """新的Tick"""
        self.tick = tick
        self.datetime = tick.datetime

        self.strategy.onTick(tick)

    # ----------------------------------------------------------------------
    def refnewBook(self, tick):
        """新的Trade"""
        self.tick = tick
        self.datetime = tick.datetime

        self.strategy.onBook(tick)

    # ----------------------------------------------------------------------
    def cross_limit_order(self, output=True):
        """
        Cross limit order with last bar/tick data.
        """
        if self.mode == BacktestingMode.BAR:
            long_cross_price = self.bar.low_price
            short_cross_price = self.bar.high_price
            long_best_price = self.bar.open_price
            short_best_price = self.bar.open_price
        else:
            long_cross_prices = [self.tick.ask_price_1,
                                 self.tick.ask_price_2,
                                 self.tick.ask_price_3,
                                 self.tick.ask_price_4,
                                 self.tick.ask_price_5]
            short_cross_prices = [self.tick.bid_price_1,
                                  self.tick.bid_price_2,
                                  self.tick.bid_price_3,
                                  self.tick.bid_price_4,
                                  self.tick.bid_price_5]
            long_best_prices = long_cross_prices
            short_best_prices = short_cross_prices
            long_cross_vol = [self.tick.ask_volume_1,
                              self.tick.ask_volume_2,
                              self.tick.ask_volume_3,
                              self.tick.ask_volume_4,
                              self.tick.ask_volume_5]
            short_cross_vol = [self.tick.bid_volume_1,
                               self.tick.bid_volume_2,
                               self.tick.bid_volume_3,
                               self.tick.bid_volume_4,
                               self.tick.bid_volume_5]

        for order in list(self.active_limit_orders.values()):
            for ticknum in range(self.tickmaxnum):
                # Push order update with status "not traded" (pending).
                # self.output("*** cross_limit_order orderID: " + str(order.orderid) + " cross " + str(self.datetime)+"  ***")
                if order.status == Status.SUBMITTING:
                    order.status = Status.NOTTRADED
                    self.strategy.on_order(order)

                # Check whether limit orders can be filled.
                long_cross = (
                        order.direction == Direction.LONG
                        and order.price >= long_cross_prices[ticknum] > 0
                        and long_cross_vol[ticknum] > 0
                )

                short_cross = (
                        order.direction == Direction.SHORT
                        and order.price <= short_cross_prices[ticknum]
                        and short_cross_prices[ticknum] > 0
                        and short_cross_vol[ticknum] > 0
                )

                if not long_cross and not short_cross:
                    continue

                # Push order udpate with status "all traded" (filled).
                order.traded = order.volume
                order.status = Status.ALLTRADED
                self.strategy.on_order(order)

                if order.vt_orderid in self.active_limit_orders:
                    self.active_limit_orders.pop(order.vt_orderid)

                # Push trade update
                self.trade_count += 1

                if long_cross:
                    trade_price = min(order.price, long_best_prices[ticknum])
                    crossvol = min(long_cross_vol[ticknum], order.volume)
                    pos_change = crossvol
                else:
                    trade_price = max(order.price, short_best_prices[ticknum])
                    crossvol = min(short_cross_vol[ticknum], order.volume)
                    pos_change = -crossvol
                order.volume -= crossvol


                trade: TradeData = TradeData(
                    symbol=order.symbol,
                    exchange=order.exchange,
                    orderid=order.orderid,
                    tradeid=str(self.trade_count),
                    direction=order.direction,
                    offset=order.offset,
                    price=trade_price,
                    volume=crossvol,
                    pos=self.strategy.pos,
                    datetime=self.datetime.strftime("%H:%M:%S"),
                    gateway_name=self.gateway_name,
                )
                trade.datetime = self.datetime

                self.strategy.pos += pos_change
                self.strategy.on_trade(trade)

                self.trades[trade.vt_tradeid] = trade

                if order.vt_orderid in self.active_limit_orders:
                    if order.volume == 0:
                        self.active_limit_orders.pop(order.vt_orderid)
                        # self.output("**********      order " + str(order.vt_orderid) + " all traded    ***********")
                        break  # 不计算下一tick
                    else:
                        self.active_limit_orders[order.vt_orderid].volume = order.volume
                        # self.output("***      order " + str(order.vt_orderid) + " next tick cross    ***")

            if order.vt_orderid in self.active_limit_orders:
                self.active_limit_orders.pop(order.vt_orderid)

    def cross_stop_order(self) -> None:
        """
        Cross stop order with last bar/tick data.
        """
        if self.mode == BacktestingMode.BAR:
            long_cross_price = self.bar.high_price
            short_cross_price = self.bar.low_price
            long_best_price = self.bar.open_price
            short_best_price = self.bar.open_price
        else:
            long_cross_price = self.tick.last_price
            short_cross_price = self.tick.last_price
            long_best_price = long_cross_price
            short_best_price = short_cross_price

        for stop_order in list(self.active_stop_orders.values()):
            # Check whether stop order can be triggered.
            long_cross: bool = (
                stop_order.direction == Direction.LONG
                and stop_order.price <= long_cross_price
            )

            short_cross: bool = (
                stop_order.direction == Direction.SHORT
                and stop_order.price >= short_cross_price
            )

            if not long_cross and not short_cross:
                continue

            # Create order data.
            self.limit_order_count += 1

            order: OrderData = OrderData(
                symbol=self.symbol,
                exchange=self.exchange,
                orderid=str(self.limit_order_count),
                direction=stop_order.direction,
                offset=stop_order.offset,
                price=stop_order.price,
                volume=stop_order.volume,
                traded=stop_order.volume,
                status=Status.ALLTRADED,
                gateway_name=self.gateway_name,
                datetime=self.datetime
            )

            self.limit_orders[order.vt_orderid] = order

            # Create trade data.
            if long_cross:
                trade_price = max(stop_order.price, long_best_price)
                pos_change = order.volume
            else:
                trade_price = min(stop_order.price, short_best_price)
                pos_change = -order.volume

            self.trade_count += 1

            trade: TradeData = TradeData(
                symbol=order.symbol,
                exchange=order.exchange,
                orderid=order.orderid,
                tradeid=str(self.trade_count),
                direction=order.direction,
                offset=order.offset,
                price=trade_price,
                volume=order.volume,
                pos=self.strategy.pos,
                datetime=self.datetime,
                gateway_name=self.gateway_name,
            )

            self.trades[trade.vt_tradeid] = trade

            # Update stop order.
            stop_order.vt_orderids.append(order.vt_orderid)
            stop_order.status = StopOrderStatus.TRIGGERED

            if stop_order.stop_orderid in self.active_stop_orders:
                self.active_stop_orders.pop(stop_order.stop_orderid)

            # Push update to strategy.
            self.strategy.on_stop_order(stop_order)
            self.strategy.on_order(order)

            self.strategy.pos += pos_change
            self.strategy.on_trade(trade)

    def load_bar(
        self,
        vt_symbol: str,
        days: int,
        interval: Interval,
        callback: Callable,
        use_database: bool
    ) -> list[BarData]:
        """"""
        self.callback = callback

        init_end = self.start - INTERVAL_DELTA_MAP[interval]
        init_start = self.start - timedelta(days=days)

        symbol, exchange = extract_vt_symbol(vt_symbol)

        bars: list[BarData] = load_bar_data(
            symbol,
            exchange,
            interval,
            init_start,
            init_end
        )

        return bars

    def load_tick(self, vt_symbol: str, days: int, callback: Callable) -> list[TickData]:
        """"""
        self.callback = callback

        init_end = self.start - timedelta(seconds=1)
        init_start = self.start - timedelta(days=days)

        symbol, exchange = extract_vt_symbol(vt_symbol)

        ticks: list[TickData] = load_tick_data(
            symbol,
            exchange,
            init_start,
            init_end
        )

        return ticks

    def send_order(
        self,
        strategy: CtaTemplate,
        direction: Direction,
        offset: Offset,
        price: float,
        volume: float,
        stop: bool,
        lock: bool,
        net: bool
    ) -> list:
        """"""
        price = round_to(price, self.pricetick)
        if stop:
            vt_orderid: str = self.send_stop_order(direction, offset, price, volume)
        else:
            vt_orderid = self.send_limit_order(direction, offset, price, volume)
        return [vt_orderid]

    def send_stop_order(
        self,
        direction: Direction,
        offset: Offset,
        price: float,
        volume: float
    ) -> str:
        """"""
        self.stop_order_count += 1

        stop_order: StopOrder = StopOrder(
            vt_symbol=self.vt_symbol,
            direction=direction,
            offset=offset,
            price=price,
            volume=volume,
            datetime=self.datetime,
            stop_orderid=f"{STOPORDER_PREFIX}.{self.stop_order_count}",
            strategy_name=self.strategy.strategy_name,
        )

        self.active_stop_orders[stop_order.stop_orderid] = stop_order
        self.stop_orders[stop_order.stop_orderid] = stop_order

        return stop_order.stop_orderid

    def send_limit_order(
        self,
        direction: Direction,
        offset: Offset,
        price: float,
        volume: float
    ) -> str:
        """"""
        self.limit_order_count += 1

        order: OrderData = OrderData(
            symbol=self.symbol,
            exchange=self.exchange,
            orderid=str(self.limit_order_count),
            direction=direction,
            offset=offset,
            price=price,
            volume=volume,
            status=Status.SUBMITTING,
            gateway_name=self.gateway_name,
            datetime=self.datetime
        )

        self.active_limit_orders[order.vt_orderid] = order
        self.limit_orders[order.vt_orderid] = order

        return order.vt_orderid     # type: ignore

    def cancel_order(self, strategy: CtaTemplate, vt_orderid: str) -> None:
        """
        Cancel order by vt_orderid.
        """
        if vt_orderid.startswith(STOPORDER_PREFIX):
            self.cancel_stop_order(strategy, vt_orderid)
        else:
            self.cancel_limit_order(strategy, vt_orderid)

    def cancel_stop_order(self, strategy: CtaTemplate, vt_orderid: str) -> None:
        """"""
        if vt_orderid not in self.active_stop_orders:
            return
        stop_order: StopOrder = self.active_stop_orders.pop(vt_orderid)

        stop_order.status = StopOrderStatus.CANCELLED
        self.strategy.on_stop_order(stop_order)

    def cancel_limit_order(self, strategy: CtaTemplate, vt_orderid: str) -> None:
        """"""
        if vt_orderid not in self.active_limit_orders:
            return
        order: OrderData = self.active_limit_orders.pop(vt_orderid)

        order.status = Status.CANCELLED
        self.strategy.on_order(order)

    def cancel_all(self, strategy: CtaTemplate) -> None:
        """
        Cancel all orders, both limit and stop.
        """
        vt_orderids: list = list(self.active_limit_orders.keys())
        for vt_orderid in vt_orderids:
            self.cancel_limit_order(strategy, vt_orderid)

        stop_orderids: list = list(self.active_stop_orders.keys())
        for vt_orderid in stop_orderids:
            self.cancel_stop_order(strategy, vt_orderid)

    def write_log(self, msg: str, strategy: CtaTemplate | None = None) -> None:
        """
        Write log message.
        """
        msg = f"{self.datetime}\t{msg}"
        self.logs.append(msg)

    def send_email(self, msg: str, strategy: CtaTemplate | None = None) -> None:
        """
        Send email to default receiver.
        """
        pass

    def sync_strategy_data(self, strategy: CtaTemplate) -> None:
        """
        Sync strategy data into json file.
        """
        pass

    def get_engine_type(self) -> EngineType:
        """
        Return engine type.
        """
        return self.engine_type

    def get_pricetick(self, strategy: CtaTemplate) -> float:
        """
        Return contract pricetick data.
        """
        return self.pricetick

    def get_size(self, strategy: CtaTemplate) -> float:
        """
        Return contract size data.
        """
        return self.size

    def put_strategy_event(self, strategy: CtaTemplate) -> None:
        """
        Put an event to update strategy status.
        """
        pass

    def output(self, msg: str) -> None:
        """
        Output message of backtesting engine.
        """
        print(f"{datetime.now()}\t{msg}")

    def get_all_trades(self) -> list:
        """
        Return all trade data of current backtesting result.
        """
        return list(self.trades.values())

    def get_all_orders(self) -> list:
        """
        Return all limit order data of current backtesting result.
        """
        return list(self.limit_orders.values())

    def get_all_daily_results(self) -> list:
        """
        Return all daily result data.
        """
        return list(self.daily_results.values())


class DailyResult:
    """"""

    def __init__(self, date: Date, close_price: float) -> None:
        """"""
        self.date: date = date
        self.close_price: float = close_price
        self.pre_close: float = 0

        self.trades: list[TradeData] = []
        self.trade_count: int = 0

        self.start_pos: float = 0
        self.end_pos: float = 0

        self.turnover: float = 0
        self.commission: float = 0
        self.slippage: float = 0

        self.trading_pnl: float = 0
        self.holding_pnl: float = 0
        self.total_pnl: float = 0
        self.net_pnl: float = 0

    def add_trade(self, trade: TradeData) -> None:
        """"""
        self.trades.append(trade)

    def calculate_pnl(
        self,
        pre_close: float,
        start_pos: float,
        size: float,
        rate: float,
        slippage: float
    ) -> None:
        """"""
        # If no pre_close provided on the first day,
        # use value 1 to avoid zero division error
        if pre_close:
            self.pre_close = pre_close
        else:
            self.pre_close = 1

        # Holding pnl is the pnl from holding position at day start
        self.start_pos = start_pos
        self.end_pos = start_pos

        self.holding_pnl = self.start_pos * (self.close_price - self.pre_close) * size

        # Trading pnl is the pnl from new trade during the day
        self.trade_count = len(self.trades)

        for trade in self.trades:
            if trade.direction == Direction.LONG:
                pos_change = trade.volume
            else:
                pos_change = -trade.volume

            self.end_pos += pos_change

            turnover: float = trade.volume * size * trade.price
            self.trading_pnl += pos_change * \
                (self.close_price - trade.price) * size
            self.slippage += trade.volume * size * slippage

            self.turnover += turnover
            self.commission += turnover * rate

        # Net pnl takes account of commission and slippage cost
        self.total_pnl = self.trading_pnl + self.holding_pnl
        self.net_pnl = self.total_pnl - self.commission - self.slippage


########################################################################
class IndiTradingResult(object):
    """每笔交易的结果"""

    # ----------------------------------------------------------------------
    def __init__(self, rate, fixrate, slippage, size):
        """Constructor"""

        self.Capital = 0
        self.turnover = 0
        self.totalVolume = 0
        self.end_pos = 0

        self.rate = rate
        self.fixrate = fixrate
        self.slippage = slippage
        self.commission = 0
        self.slippages = 0

        self.size = size
        self.net_pnl = 0

    # ----------------------------------------------------------------------
    def calculate(self, newPrice, volume, direction=None):
        if direction == Direction.SHORT:
            volume = -volume
        self.totalVolume += abs(volume)
        turnover = newPrice * abs(volume) * self.size
        self.turnover += turnover
        self.end_pos += volume
        self.Capital += newPrice * volume * self.size

        self.commission += turnover * self.rate  # 税费
        self.slippages += self.slippage * self.size * abs(volume)  # 滑点成本

        self.net_pnl = self.end_pos * newPrice * self.size - self.Capital - (
                self.commission + self.slippages)


@lru_cache(maxsize=999)
def load_bar_data(
    symbol: str,
    exchange: Exchange,
    interval: Interval,
    start: datetime,
    end: datetime
) -> list[BarData]:
    """"""
    database: BaseDatabase = get_database()

    return database.load_bar_data(symbol, exchange, interval, start, end)       # type: ignore


@lru_cache(maxsize=999)
def load_tick_data(
    symbol: str,
    exchange: Exchange,
    start: datetime,
    end: datetime
) -> list[TickData]:
    """"""
    database: BaseDatabase = get_database()

    return database.load_tick_data(symbol, exchange, start, end)       # type: ignore


def evaluate(
    target_name: str,
    strategy_class: type[CtaTemplate],
    vt_symbol: str,
    interval: Interval,
    start: datetime,
    rate: float,
    slippage: float,
    size: float,
    pricetick: float,
    capital: int,
    end: datetime,
    mode: BacktestingMode,
    setting: dict
) -> tuple:
    """
    Function for running in multiprocessing.pool
    """
    engine: BacktestingEngine = BacktestingEngine()

    engine.set_parameters(
        vt_symbol=vt_symbol,
        interval=interval,
        start=start,
        rate=rate,
        slippage=slippage,
        size=size,
        pricetick=pricetick,
        capital=capital,
        end=end,
        mode=mode
    )

    engine.add_strategy(strategy_class, setting)
    engine.load_data()
    engine.run_backtesting()
    engine.calculate_result()
    statistics: dict = engine.calculate_statistics(output=False)

    target_value: float = statistics.get(target_name, 0)
    return (setting, target_value, statistics)


def wrap_evaluate(engine: BacktestingEngine, target_name: str) -> Callable:
    """
    Wrap evaluate function with given setting from backtesting engine.
    """
    func: Callable = partial(
        evaluate,
        target_name,
        engine.strategy_class,
        engine.vt_symbol,
        engine.interval,
        engine.start,
        engine.rate,
        engine.slippage,
        engine.size,
        engine.pricetick,
        engine.capital,
        engine.end,
        engine.mode
    )
    return func


def get_target_value(result: list) -> float:
    """
    Get target value for sorting optimization results.
    """
    return cast(float, result[1])
