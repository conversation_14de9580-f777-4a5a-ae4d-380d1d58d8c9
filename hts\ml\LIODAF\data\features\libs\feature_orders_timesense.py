
"""
订单时间敏感特征
@author: lining
"""
import pandas as pd
import numpy as np
from factor_manager import factor_manager, Factor, FactorCategory





def calculate_order_arrival_rate(data: pd.DataFrame, side: str = None) -> pd.Series:
    
    
    
    
    if 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    if side and 'order_side' not in data.columns:
        return pd.Series(0, index=data.index)
    
    
    window_ms = 1000
    
    
    result = pd.Series(index=data.index)
    for idx in data.index:
        
        current_time = data.loc[idx, 'timestamp']
        
        
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        
        if side:
            filtered_orders = window_data[window_data['order_side'] == side].shape[0]
        else:
            filtered_orders = window_data.shape[0]
        
        
        if not window_data.empty:
            time_span = window_data['timestamp'].max() - window_data['timestamp'].min()
            if time_span > 0:
                result.loc[idx] = filtered_orders / time_span
            else:
                result.loc[idx] = 0
        else:
            result.loc[idx] = 0
            
    return result


factor_manager.register_factor(Factor(
    name="bid_arrival_rate",
    category=FactorCategory.TIME_SENSITIVE,
    description="买单到达率（每毫秒）",
    calculation=lambda data: calculate_order_arrival_rate(data, 'BUY'),
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))


factor_manager.register_factor(Factor(
    name="ask_arrival_rate",
    category=FactorCategory.TIME_SENSITIVE,
    description="卖单到达率（每毫秒）",
    calculation=lambda data: calculate_order_arrival_rate(data, 'SELL'),
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))


def calculate_order_intensity(data: pd.DataFrame) -> pd.Series:
    
    
    
    if 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    
    window_ms = 1000
    
    
    result = pd.Series(index=data.index)
    for idx in data.index:
        
        current_time = data.loc[idx, 'timestamp']
        
        
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        
        total_orders = window_data.shape[0]
        
        
        if not window_data.empty:
            time_span = window_data['timestamp'].max() - window_data['timestamp'].min()
            if time_span > 0:
                result.loc[idx] = total_orders / time_span
            else:
                result.loc[idx] = 0
        else:
            result.loc[idx] = 0
            
    return result


def calculate_buy_sell_ratio(data: pd.DataFrame) -> pd.Series:
    
    
    
    
    if 'order_side' not in data.columns or 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    
    window_ms = 1000
    
    
    result = pd.Series(index=data.index)
    for idx in data.index:
        
        current_time = data.loc[idx, 'timestamp']
        
        
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        
        buy_orders = window_data[window_data['order_side'] == 'BUY'].shape[0]
        sell_orders = window_data[window_data['order_side'] == 'SELL'].shape[0]
        
        
        if sell_orders > 0:
            result.loc[idx] = buy_orders / sell_orders
        else:
            result.loc[idx] = float('nan')  
            
    return result


def calculate_order_imbalance_ratio(data: pd.DataFrame) -> pd.Series:
    
    
    
    
    if 'order_side' not in data.columns or 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    
    window_ms = 1000
    
    
    result = pd.Series(index=data.index)
    for idx in data.index:
        
        current_time = data.loc[idx, 'timestamp']
        
        
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        
        buy_orders = window_data[window_data['order_side'] == 'BUY'].shape[0]
        sell_orders = window_data[window_data['order_side'] == 'SELL'].shape[0]
        total_orders = buy_orders + sell_orders
        
        
        if total_orders > 0:
            result.loc[idx] = (buy_orders - sell_orders) / total_orders
        else:
            result.loc[idx] = 0
            
    return result




def calculate_time_weighted_average_price(data: pd.DataFrame) -> pd.Series:
    
    
    
    
    if 'price' not in data.columns or 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    
    window_ms = 1000
    
    
    result = pd.Series(index=data.index)
    for idx in data.index:
        
        current_time = data.loc[idx, 'timestamp']
        
        
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        
        if len(window_data) >= 2:
            
            window_data = window_data.sort_values('timestamp')
            
            
            timestamps = window_data['timestamp'].values
            prices = window_data['price'].values
            
            
            time_diffs = np.diff(timestamps)
            
            
            weighted_sum = np.sum(prices[:-1] * time_diffs)
            total_time = np.sum(time_diffs)
            
            if total_time > 0:
                result.loc[idx] = weighted_sum / total_time
            else:
                result.loc[idx] = prices.mean()  
        else:
            
            if len(window_data) == 1:
                result.loc[idx] = window_data['price'].iloc[0]
            else:
                result.loc[idx] = 0
            
    return result






factor_manager.register_factor(Factor(
    name="order_intensity",
    category=FactorCategory.TIME_SENSITIVE,
    description="订单强度（总订单到达率，每毫秒）",
    calculation=calculate_order_intensity,
    dependencies=["timestamp"],
    source="lining-orderbook-dynamic-features v6"
))


factor_manager.register_factor(Factor(
    name="buy_sell_ratio",
    category=FactorCategory.TIME_SENSITIVE,
    description="买卖比率",
    calculation=calculate_buy_sell_ratio,
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))


factor_manager.register_factor(Factor(
    name="order_imbalance_ratio",
    category=FactorCategory.TIME_SENSITIVE,
    description="订单不平衡比率 ([-1, 1]范围)",
    calculation=calculate_order_imbalance_ratio,
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))



factor_manager.register_factor(Factor(
    name="time_weighted_average_price",
    category=FactorCategory.TIME_SENSITIVE,
    description="时间加权平均价格",
    calculation=calculate_time_weighted_average_price,
    dependencies=["price", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))