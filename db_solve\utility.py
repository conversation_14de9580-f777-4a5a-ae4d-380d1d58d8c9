import copy
import os
import re
import sys
from colorsys import hsv_to_rgb

import numpy as np
import pandas as pd
from scipy.stats import norm


def eachFile(filepath):
    pathDir = os.listdir(filepath)
    file = []
    dates = []
    for allDir in pathDir:
        file.append(os.path.join('%s\\%s' % (filepath, allDir)))
        try:
            dates.append(re.findall(r'20\d{6}', allDir)[0])
        except:
            continue
    dates = list(set(dates))
    dates.sort()
    return dates


def mddiff(mdData, col, newcol, dsflist, abs=False):
    for i in dsflist:
        if i < 0:
            mdData[newcol + str(i)] = mdData[col].diff(-i)
        else:
            mdData[newcol + str(i)] = -mdData[col].diff(-i)
    if abs:
        for i in dsflist:
            mdData[newcol + str(i) + '_abs'] = (mdData[newcol + str(i)]).abs()
    return mdData


def mddiffpnl(dsflist, mdmixorder, col, col2, newcol):
    for i in dsflist:
        mdmixorder[newcol % str(i) + 'pnl_' + col] = (
            (mdmixorder[col] * mdmixorder[newcol % str(i)] / mdmixorder[col2]).round(2))
    return mdmixorder


def voldiffpnl(dvlist, mdmixorder, col, newcol):
    for i in dvlist:
        mdmixorder[newcol + str(i) + 'pnl'] = (mdmixorder[col] * mdmixorder[newcol + str(i)] * 100).round(2)
    return mdmixorder


def flatten_multi_index(multi_index, join_str='_'):
    label0 = multi_index.get_level_values(0)
    label1 = multi_index.get_level_values(1)
    index = [str(i) + join_str + str(int(j)) for i, j in zip(label0, label1)]
    return pd.Index(index)


def drop_col_nan(data, col, is_zero=True):
    drops = data[data[col].isna()].index
    if is_zero:
        drops = drops.append(data[data[col] == 0].index)
        print(str(len(data[col])), '删除%s条为0及nan的数据' % str(len(drops)))
    else:
        print(str(len(data[col])), col, '删除%s条数据' % str(len(drops)))
    return data.drop(drops)


def dttype(mixData, str1, out):
    mixData[out] = mixData.apply(
        lambda x: -1 if x[str1] < 0
        else 0 if x[str1] < 0.01
        else 0.02 if x[str1] < 0.02
        else 0.05 if x[str1] < 0.05
        else 0.08 if x[str1] < 0.08
        else 1 if x[str1] < 0.1
        else 21 if x[str1] > 0.6
        else round((x[str1] - 0.025) / 0.05)
        , axis=1)
    return mixData


def siltor2(x, col, dir):
    if dir == -1:
        for i in range(-5, 6, 1):
            i = -i
            if x[col] <= abs(x['BidPrice%s' % abs(5)]):
                x['BidPrice%s' % abs(i)] = -x['BidPrice%s' % abs(i)]
                x['BidVol%s' % abs(5)] = -x['BidVol%s' % abs(5)]
                return x
            if i > 0:
                if x[col] >= abs(x['AskPrice%s' % abs(i)]):
                    x['AskPrice%s' % abs(i)] = -x['AskPrice%s' % abs(i)]
                    x['AskVol%s' % abs(i)] = -x['AskVol%s' % abs(i)]
                    return x
            elif i == 0:
                if x[col] > abs(x['BidPrice%s' % abs(1)]):
                    for i in range(-5, 0, 1):
                        x['BidPrice%s' % abs(i)] = -x['BidPrice%s' % abs(i)]
                        x['BidVol%s' % abs(i)] = -x['BidVol%s' % abs(i)]
                    return x
            else:
                if x[col] >= abs(x['BidPrice%s' % abs(i)]):
                    x['BidPrice%s' % abs(i)] = -x['BidPrice%s' % abs(i)]
                    x['BidVol%s' % abs(i)] = -x['BidVol%s' % abs(i)]
                    return x

    else:
        for i in range(-5, 6, 1):
            if x[col] >= abs(x['AskPrice%s' % abs(5)]):
                x['AskPrice%s' % abs(i)] = -x['AskPrice%s' % abs(i)]
                x['AskVol%s' % abs(i)] = -x['AskVol%s' % abs(i)]
                return x
            if i < 0:
                if x[col] <= abs(x['BidPrice%s' % abs(i)]):
                    x['BidPrice%s' % abs(i)] = -x['BidPrice%s' % abs(i)]
                    x['BidVol%s' % abs(i)] = -x['BidVol%s' % abs(i)]
                    return x
            elif i == 0:
                if x[col] < abs(x['AskPrice%s' % abs(1)]):
                    for i in range(-5, 0, 1):
                        x['AskPrice%s' % abs(i)] = -x['AskPrice%s' % abs(i)]
                        x['AskVol%s' % abs(i)] = -x['AskVol%s' % abs(i)]
                    return x
            else:
                if x[col] <= abs(x['AskPrice%s' % abs(i)]):
                    x['AskPrice%s' % abs(i)] = -x['AskPrice%s' % abs(i)]
                    x['AskVol%s' % abs(i)] = -x['AskVol%s' % abs(i)]
                    return x


def siltor(x, col):
    for i in range(-5, 6, 1):
        if abs(x[col]) >= abs(x['AskPrice%s' % abs(5)]):
            return 6
        if i < 0:
            if abs(x[col]) <= abs(x['BidPrice%s' % abs(i)]):
                return i
        elif i == 0:
            if abs(x[col]) < abs(x['AskPrice%s' % abs(1)]):
                return i
        else:
            if abs(x[col]) <= abs(x['AskPrice%s' % abs(i)]):
                return i


class Logger(object):
    def __init__(self, filename):
        self.terminal = sys.stdout
        self.log = open(filename, 'a', encoding='utf-8')

    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()

    def flush(self):
        self.terminal.flush()
        self.log.flush()



def time_resh(data_pre):
    data = copy.deepcopy(data_pre)
    data.index = r"'" + data.index.strftime(f"%H:%M:%S.%f")
    pattern = rf"Time|time"
    for col in data.columns:
        if re.search(pattern, col):
            try:
                data[col] = r"'" + data[col].dt.strftime('%S.%f')
                data[col] = data[col].apply(lambda x: str(x)[:-3])
            except Exception as e:
                print(str(e), col, pattern, data[col].iloc[1])
    print('time_resh done')

    return data


def try_encodings(file_path):
    encodings = ['utf-8', 'utf-16', 'gbk', 'gb2312', 'latin1']
    # 尝试不同的编码方式读取文件
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                f.read(1000)
            # print(f"文件以 {encoding} 编码成功读取。")
            return encoding
        except UnicodeDecodeError:
            continue  # 忽略UnicodeDecodeError，继续尝试下一个编码
        except FileNotFoundError:
            print(f"文件 {file_path} 不存在。")
            return None
        except PermissionError:
            print(f"没有权限读取文件 {file_path}。")
            return None
        except Exception as e:
            print(f"读取文件时发生未知错误: {e}")
            return None

    # 如果所有编码都尝试失败
    print(f"无法用任何提供的编码格式读取文件。已尝试的编码有：{encodings}")
    return None


# 生成随机颜色的函数
def generate_random_color(brightness=1.0, alpha=None):
    return (f'rgba({int(np.random.randint(0, 256) * brightness)}, {int(np.random.randint(0, 256) * brightness)}, '
            f'{int(np.random.randint(0, 256) * brightness)}, {alpha if alpha else np.random.uniform(0.3, 0.7)})')


# 定义函数，生成同一色系的渐变色
def generate_gradient_colors(num_colors, ):
    colors = []
    for sat in np.linspace(0.4, 0.9, num_colors):
        rgb = hsv_to_rgb(sat, sat, 1)
        rgba = f'rgba({int(rgb[0]*255)}, {int(rgb[1]*255)}, {int(rgb[2]*255)}, 0.7)'
        colors.append(rgba)
    return colors


def output_settings(setting, indent=0):
    indent_str = ' ' * (indent * 4)
    if isinstance(setting, dict):
        result = '{\n'
        for key, value in setting.items():
            result += f'{indent_str}    {key:<20} : '
            result += output_settings(value, indent + 1)
            result += ',\n'
        result += f'{indent_str}}}'
        return result
    elif isinstance(setting, list):
        result = '[\n'
        for item in setting:
            result += f'{indent_str}    {output_settings(item, indent + 1)},\n'
        result += f'{indent_str}]'
        return result
    else:
        return repr(setting)



