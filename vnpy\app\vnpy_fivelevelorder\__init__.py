from vnpy.trader.app import BaseApp
from vnpy.trader.constant import Direction
from vnpy.trader.object import TickData, SubscribeRequest
from vnpy.event import EventEngine, Event
from PySide6 import QtWidgets, QtCore

from .ui.widget import FiveLevelOrderWidget

class FiveLevelOrderApp(BaseApp):
    app_name = "FiveLevelOrder"
    app_module = __module__
    app_path = __file__
    display_name = "五档下单"
    engine_class = None
    widget_name = "FiveLevelOrderWidget"
    icon_name = "fivelevel.ico"

    def __init__(self, main_engine, event_engine):
        super().__init__(main_engine, event_engine)
        self.widget = None

    def create_widget(self):
        """创建五档下单界面"""
        self.widget = FiveLevelOrderWidget(self.main_engine, self.event_engine)
        return self.widget

    def init_engine(self):
        """初始化引擎"""
        pass

    def start(self):
        """启动应用"""
        pass

    def stop(self):
        """停止应用"""
        pass

    def close(self):
        """关闭应用"""
        pass

    def show(self):
        """显示应用"""
        self.widget.show()

    def hide(self):
        """隐藏应用"""
        self.widget.hide()

# 确保 FiveLevelOrderApp 可以被直接从 vnpy_fivelevelorder 导入
__all__ = ['FiveLevelOrderWidget']
