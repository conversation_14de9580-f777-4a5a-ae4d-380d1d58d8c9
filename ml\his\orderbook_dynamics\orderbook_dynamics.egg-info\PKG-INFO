Metadata-Version: 2.4
Name: orderbook_dynamics
Version: 0.1.0
Summary: A library for analyzing high-frequency limit order book dynamics
Home-page: https://github.com/yourusername/orderbook-dynamics
Author: Python Port Author
Author-email: <EMAIL>
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Topic :: Office/Business :: Financial
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: numpy>=1.19.0
Requires-Dist: pandas>=1.0.0
Requires-Dist: scikit-learn>=0.24.0
Requires-Dist: matplotlib>=3.0.0
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license-file
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# OrderBook Dynamics

Python implementation of the paper [Modeling high-frequency limit order book dynamics with support vector machines](https://raw.github.com/ezhulenev/scala-openbook/master/assets/Modeling-high-frequency-limit-order-book-dynamics-with-support-vector-machines.pdf).

This library provides tools for analyzing high-frequency limit order book data and building predictive models for financial market movements.

## Features

- Comprehensive set of time-insensitive and time-sensitive order book features
- Support for different machine learning models (Decision Trees, SVM)
- Flexible feature extraction pipeline
- Label generation for price movement prediction

## Order Book Features

The library extracts various features from order book data, including:

### Time-Insensitive Features

These features don't depend on the time sequence of updates:

- Price spreads and steps between different levels
- Mean prices and volumes
- Accumulated price and volume spreads

### Time-Sensitive Features

These features depend on the time sequence of updates:

- Order arrival rates
- Buy/sell ratios
- Order intensity
- Price impact
- Time-weighted average price (TWAP)
- Order imbalance ratios
- Price volatility

## Installation

```bash
git clone https://github.com/yourusername/orderbook-dynamics.git
cd orderbook-dynamics
pip install -e .
```

## Usage

### Basic Example

```python
from orderbook_dynamics.src.models import OrderBook, OpenBookMsg, Side
from orderbook_dynamics.src.attribute.time_insensitive_attribute import TimeInsensitiveSet

# Create an order book
order_book = OrderBook("AAPL")

# Add some orders
order_book.update(OpenBookMsg("AAPL", 1000, 0, 150.0, 100, Side.BID))
order_book.update(OpenBookMsg("AAPL", 1001, 0, 150.5, 200, Side.ASK))
order_book.update(OpenBookMsg("AAPL", 1002, 0, 149.5, 150, Side.BID))
order_book.update(OpenBookMsg("AAPL", 1003, 0, 151.0, 100, Side.ASK))

# Create a time-insensitive feature set
feature_set = TimeInsensitiveSet()

# Calculate some features
price_spread = feature_set.price_spread()(order_book)
mid_price = feature_set.mid_price()(order_book)
mean_ask = feature_set.mean_ask()(order_book)

print(f"Price spread: {price_spread.get()}")
print(f"Mid price: {mid_price.get()}")
print(f"Mean ask: {mean_ask.get()}")
```

### Training a Model

```python
from orderbook_dynamics.src.decision_tree_dynamics import DecisionTreeDynamics
from orderbook_dynamics.src.open_book import OpenBook

# Load data files
training_files = OpenBook.open_book_files("path/to/training/data")
validation_files = OpenBook.open_book_files("path/to/validation/data")

# Create and train model
dynamics = DecisionTreeDynamics(max_depth=5, max_level=5, time_window_ms=1000)
models = dynamics.train(training_files, validation_files, filter_symbol="AAPL")

# Use model for prediction
# ...
```

## Command-line Interface

The library includes a command-line tool for training and evaluating models:

```bash
python -m orderbook_dynamics.src.decision_tree_dynamics \
    --training path/to/training/data \
    --validation path/to/validation/data \
    --symbol AAPL \
    --max-depth 5 \
    --max-level 5 \
    --time-window 1000
```

## Requirements

- Python 3.7+
- NumPy
- pandas
- scikit-learn

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Based on the original Scala implementation by [ezhulenev](https://github.com/ezhulenev/orderbook-dynamics)
- Inspired by the paper "Modeling high-frequency limit order book dynamics with support vector machines" 
