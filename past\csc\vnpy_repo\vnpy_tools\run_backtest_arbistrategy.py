#%% 
'''IP SETTING FOR INFLUXDB'''
from vnpy.database.ip.ip_setting import IPSETTING

ip = IPSETTING()
ip.set_ip('DCE.prod')
import warnings

warnings.filterwarnings('ignore')
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机""
    'DCE.dev'代表大商所生产机    
"""

"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
# from strategies.ArbiStrategy import ArbiStrategy
from strategies.ArbiStrategy_V3 import ArbiStrategyV3
from strategies.ArbiStrategy import ArbiStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
import re

maker = 'eb2210.DCE'
refer = 'eb2209.DCE'
symbol, exchange = maker.split('.')
contract = symbol[:re.search(r'\d', symbol).start()].upper()
exchange = exchange.upper()
df = pd.read_csv("contract_config.csv", index_col=0, error_bad_lines=False)
config = df.to_dict()
if exchange == 'CZCE':
    exchange = 'CZC'
if exchange == 'SHFE':
    exchange = 'SHF'
Ticker = contract + '.' + exchange
multiplier = config['size'][Ticker]
ts = config['pricetick'][Ticker]

engine = BacktestingEngine(alwaysInQueueHeadFlag=False, cancelFailProba=0, onlyCrossFlag=False, duty=False,
                           fast_join=True, refer_late=True, refer_test=False)  #Counter代表是否只统计对价成交，duty代表是否统计义务

engine.set_parameters(
    vt_symbols=[maker, refer],  # 回测品种
    interval=Interval.TICK,  # 回测模式的数据间隔
    start=datetime.datetime(2022, 7, 22, 21, 5),  # 开始时间
    end=datetime.datetime(2022, 7, 25, 15, 0),  # 结束时间
    rates={maker: 0, refer: 0},  # 手续费率
    slippages={maker: 0, refer: 0},  # 滑点
    sizes={maker: multiplier, refer: multiplier},  # 合约规模
    priceticks={maker: ts, refer: ts},  # 一个tick大小
    capital=1_000_000,  # 初始资金
)
# 添加回测策略，并修改内部参数

engine.clear_data()
# engine.add_strategy(ArbiStrategy, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
#                                    'skew_pos':15,'path':10,'ioc_flag':True})
engine.add_strategy(ArbiStrategyV3,
                    {'lots': 1, 'maker': maker, 'refer': refer, 'priceticks': engine.priceticks, 'sizes': engine.sizes,
                     'maxPos': 15, 'path': 50, 'min_std': 1, 'open_std': 1, 'close_std': 0,
                     'grid_start': 2, 'grid_interval': 1, 'grid_layer': 5})
engine.load_data()  # 加载历史数据
engine.run_backtesting()  # 进行回测
df = engine.calculate_tick_result()  # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics()  # 统计日度策略指标
engine.show_tick_chart()  # 显示图表

#%% 买卖点分析
from vnpy_tools.PostTradeAnalysis import PostTradeAnalysis

Analysis = PostTradeAnalysis(engine, multiplier)
_df = Analysis.plot_equity_sp()
