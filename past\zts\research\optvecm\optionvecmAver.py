# 模型相关包
import pyodbc

from datetime import datetime, timedelta

import statsmodels.api as sm
import statsmodels.stats.diagnostic
from statsmodels.tsa.api import VAR, DynamicVAR
from statsmodels.tsa.vector_ar.vecm import coint_joh<PERSON>en, VECM

from scipy.linalg import solve

from WindPy import w

# 画图包
import matplotlib.pyplot as plt
# 其他包
import pandas as pd
import numpy as np
import matplotlib as mpl

import sys
import os

sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from optvecm import greeks


def main(forward1, datett, date1, date2, result2, times2):
    sql = u"SELECT [Symbol],[Buy1Px],[Sell1Px],[Price],[Qty],[Volume],[RecordTime] FROM [MktDataHis].[dbo].[" \
          u"FutMktDataAdj] where RecordTime>'%s' and RecordTime<'%s' and Symbol='%s' and price <>0 order by RecordTime" \
          % (date1, date2, forward1)
    pf0 = pd.read_sql(sql, conn, index_col='RecordTime', coerce_float=True, params=None, parse_dates=True, columns=None,
                      chunksize=None)
    pf0['pankou'] = (pf0['Buy1Px'] + pf0['Sell1Px']) / 2
    pf0[['pankou', 'Price']] = pf0[['pankou', 'Price']] / 1000
    pf0 = pf0[['pankou', 'Price', 'Volume']]
    pf0.rename(columns={'pankou': 'pankou0', 'Price': 'Price0'}, inplace=True)
    print(u'fut base done ' + forward1)

    optcp1 = getOptCP(datett)

    price0 = pf0['pankou0'][0]

    sdmin = 10
    sdindex = 0
    for i in range(0, len(optcp1['strike_price'])):
        sd = abs(optcp1['strike_price'][i] - price0)
        if sd < sdmin:
            sdmin = sd
            sdindex = i
    optionnames = []
    result = pf0
    for i in range(-2, 3):  # no 3
        optionnames.append([optcp1['strike_price'][sdindex + i], optcp1['option_code_x'][sdindex + i],
                            optcp1['option_code_y'][sdindex + i]])
        pf11 = optionprice(conn, optcp1['strike_price'][sdindex + i], optcp1['option_code_x'][sdindex + i][:8],
                           optcp1['option_code_y'][sdindex + i][:8], i, date1, date2)  # 期权合成期货
        result = pd.merge(result, pf11, how='inner', left_index=True, right_index=True)

    data = result[['pankou0', 'Price0', 'pankou11', 'price11', 'pankou12', 'price12', 'pankou13', 'price13',
                   'pankou14', 'price14', 'pankou15', 'price15']]
    data['pankou22'] = (data['pankou11'] + data['pankou12'] + data['pankou13'] + data['pankou14'] + data[
        'pankou15']) / 5
    data['price22'] = (data['price11'] + data['price12'] + data['price13'] + data['price14'] + data['price15']) / 5
    data = np.log(data[['pankou0', 'Price0', 'pankou22', 'price22']]).dropna()

    date00 = datetime.strptime("%s  9:30:00" % datetime.strftime(datett, "%Y-%m-%d"), "%Y-%m-%d %H:%M:%S")

    date01 = datetime.strptime("%s  10:30:00" % datetime.strftime(datett, "%Y-%m-%d"), "%Y-%m-%d %H:%M:%S")

    date02 = datetime.strptime("%s  13:00:00" % datetime.strftime(datett, "%Y-%m-%d"), "%Y-%m-%d %H:%M:%S")

    date03 = datetime.strptime("%s  14:00:00" % datetime.strftime(datett, "%Y-%m-%d"), "%Y-%m-%d %H:%M:%S")
    date04 = datetime.strptime("%s  15:00:00" % datetime.strftime(datett, "%Y-%m-%d"), "%Y-%m-%d %H:%M:%S")

    times1 = [date00, date01, date02, date03, date04]

    # volume=result['volume'].resample('50T',label='left').sum().dropna()

    tradedate1 = w.wss(optcp1['option_code_x'][sdindex], "ptmtradeday", "tradeDate=%s" % datett).Data[0][0]

    for i in range(0, len(times1) - 1):
        data2 = data[times1[i]:times1[i + 1]]
        gis = vecmgis(data2)  # 计算信息贡献度
        if i == 0:
            gis = np.append(gis, result.loc[np.datetime64(times1[i + 1]), 'Volume'] - 0)
        else:
            gis = np.append(gis, result.loc[np.datetime64(times1[i + 1]), 'Volume'] - result.loc[
                np.datetime64(times1[i]), 'Volume'])
        try:
            gis = np.append(gis,
                            greeks.BSOptionISDGoalSeekNR(1, np.exp(data2['price13'][-1]),
                                                         optcp1['strike_price'][sdindex],
                                                         0 / 100, 0, (tradedate1 - (i + 1) / 8 * 0.8) / 252,
                                                         result.loc[np.datetime64(times1[i + 1]), 'price03']))
        except:
            gis = np.append(gis, 0)
        gis = np.append(gis, optcp1['expiredate'][0])
        result2.append(gis.tolist())
        print(times1[i])

    gis = vecmgis(data)  # 计算信息贡献度
    gis = np.append(gis, result.loc[times1[-1], 'Volume'])  # 区间成交量
    gis = np.append(gis, result2[-1][-2])
    gis = np.append(gis, result2[-1][-1])

    print(times1[-1])
    result2.append(gis.tolist())
    times2.extend(times1)


def getOptCP(datenow):
    wdata = w.wset("optionchain",
                   u"date=%s;us_code=510050.SH;option_var=全部;call_put=全部;"
                   u"field=option_var,option_code,option_name,strike_price,month,call_put,last_tradedate,expiredate"
                   % datenow)

    opt_list = pd.DataFrame(wdata.Data, index=wdata.Fields, columns=None)
    opt_list = opt_list.T  # 将矩阵转置

    date = []
    month = opt_list['month'][0]
    date.append(month)
    for i in opt_list['month']:
        if i != month:
            month = i
            date.append(month)

    optcp = dfmaker(date, opt_list)

    if date[0] > 0:  # 最后月份转为次月
        optcp1 = optcp[optcp['month'] == date[0]]
    else:
        optcp1 = optcp[optcp['month'] == date[1]]

    return optcp1


def dfmaker(date, opt_listall):
    # 去除非标准合约
    try:
        opt_list0 = opt_listall[~opt_listall.option_name.str.contains('A')]  # 去除分红加上的合约
        df1 = opt_list0[opt_list0['month'] == date[0]]
        df2 = opt_list0[opt_list0['month'] == date[1]]
        T1 = float(df1['expiredate'].values[0])
        # if df1.empty or df2.empty:
        #     print('仅有非标准合约')
        #     df0 = dfmaker(opt_listall)
        #     df1 = df0[df0['DLMONTH'] == str(date[0])]
        #     df2 = df0[df0['DLMONTH'] == str(date[1])]
    except:
        print('近月仅有非标准合约')
        opt_list0 = opt_listall
        df1 = opt_list0[opt_list0['month'] == str(date[0])]
        df2 = opt_list0[opt_list0['month'] == str(date[1])]

    if df1.empty or df2.empty:
        print('次月仅有非标准合约')
        opt_list0 = opt_listall

    optcall = opt_list0[opt_list0['call_put'] == u'认购']
    optput = opt_list0[opt_list0['call_put'] == u'认沽']
    optcall = optcall.reset_index(drop=True)  # 不进行reset_index(drop=True)，index会变得不连续
    optput = optput.reset_index(drop=True)

    optcp = pd.merge(optcall, optput, on=['month', 'strike_price', 'last_tradedate', 'expiredate', 'option_var'])

    return optcp


def optionprice(conn, strike1, option1, option2, num, date1, date2):
    sql1 = u"SELECT [Symbol],[Buy1Px],[Sell1Px],[Price],[Qty],[Volume],[RecordTime] FROM [MktDataHis].[dbo].[" \
           u"OptMktDataAdj] where RecordTime>'%s' and RecordTime<'%s' and Symbol='%s' order by RecordTime" % (
               date1, date2, option1)
    pf1 = pd.read_sql(sql1, conn, index_col='RecordTime', coerce_float=True, params=None, parse_dates=True,
                      columns=None,
                      chunksize=None)
    pf1 = pf1[['Buy1Px', 'Sell1Px', 'Price']]
    pf1['pankou'] = (pf1['Buy1Px'] + pf1['Sell1Px']) / 2

    sql2 = u"SELECT [Symbol],[Buy1Px],[Sell1Px],[Price],[Qty],[Volume],[RecordTime] FROM [MktDataHis].[dbo].[" \
           u"OptMktDataAdj] where RecordTime>'%s' and RecordTime<'%s' and Symbol='%s' order by RecordTime" % (
               date1, date2, option2)
    pf2 = pd.read_sql(sql2, conn, index_col='RecordTime', coerce_float=True, params=None, parse_dates=True,
                      columns=None,
                      chunksize=None)
    pf2 = pf2[['Buy1Px', 'Sell1Px', 'Price']]
    pf2['pankou'] = (pf2['Buy1Px'] + pf2['Sell1Px']) / 2

    pf11 = pd.DataFrame(columns=['pankou1' + str(num + 3), 'price1' + str(num + 3)])
    pf11['pankou1' + str(num + 3)] = pf1['pankou'] - pf2['pankou'] + strike1
    pf11['price1' + str(num + 3)] = pf1['Price'] - pf2['Price'] + strike1
    if num == 0:
        pf11['price0' + str(num + 3)] = pf1['Price']

    print('opt base done ' + str(num) + ' ' + str(strike1))

    return pf11


def vecmgis(data):
    # fig = plt.figure(figsize=(12, 8))
    # plt.plot(pf0['Buy1Px'], 'r', label='Buy1Px')
    # plt.plot(pf0['Price'], 'g', label='Price')
    # plt.title('Correlation: ' + str(forward1))
    # plt.grid(True)
    # plt.axis('tight')
    # plt.legend(loc=0)
    # plt.ylabel('Price')
    # plt.show()

    # maxlags = 100

    # adf 检验
    # adfResult = sm.tsa.stattools.adfuller(pf0['Price'], maxlags)
    # output = pd.DataFrame(index=['Test Statistic Value', "p-value", "Lags Used", "Number of Observations Used",
    #                              "Critical Value(1%)", "Critical Value(5%)", "Critical Value(10%)"],
    #                       columns=['value'])
    # output['value']['Test Statistic Value'] = adfResult[0]
    # output['value']['p-value'] = adfResult[1]
    # output['value']['Lags Used'] = adfResult[2]
    # output['value']['Number of Observations Used'] = adfResult[3]
    # output['value']['Critical Value(1per)'] = adfResult[4]['1%']
    # output['value']['Critical Value(5per)'] = adfResult[4]['5%']
    # output['value']['Critical Value(10per)'] = adfResult[4]['10%']

    # result = sm.tsa.stattools.coint(pf0['Price'], pf0['Buy1Px'])

    # varLagNum = 100
    # # 建立对象，dataframe就是前面的data，varLagNum就是你自己定的滞后阶数
    # rgMod = sm.tsa.VARMAX(dataframe, order=(varLagNum, 0), trend='nc', exog=None)
    # # 估计：就是模型
    # fitMod = orgMod.fit(maxiter=1000, disp=False)
    # # 打印统计结果
    # print(fitMod.summary())
    # # 获得模型残差
    # resid = fitMod.resid
    # result = {'fitMod': fitMod, 'resid': resid}
    if data.shape[0] > 20000:
        data = data.resample('S').asfreq().dropna()
    model = VAR(data)
    # results = model.fit(2)
    a = model.select_order(50)  # 滞后阶数
    # results2 = model.fit(maxlags=50, ic='aic')
    # print(results2.summary())
    # results.plot()
    print(a.aic)

    model2 = VECM(data, k_ar_diff=a.aic - 1, coint_rank=data.shape[1] - 1, deterministic='nc')
    results3 = model2.fit()
    resid = results3.resid.T
    # sigma = results3.sigma_u  残差的协方差
    alpha = results3.alpha
    cov_r = np.cov(resid)  # 残差的协方差
    corr_r = np.corrcoef(resid)  # 残差的相关系数

    eigenvalue, featurevector = np.linalg.eig(corr_r)  # 特征向量,特征根

    # alpha2 = np.r_[alpha, np.array([[0, 0, 0]])].T
    alpha2 = np.r_[alpha.T, np.ones((1, alpha.shape[0]))]

    psi = solve(alpha2, np.c_[np.zeros((1, alpha.shape[0] - 1)), [1]].T)
    V = np.diag(np.sqrt(np.diag(cov_r)))

    F = np.linalg.inv(
        (np.dot(np.dot(np.dot(featurevector, np.diag(eigenvalue ** (-1 / 2))), featurevector.T), np.linalg.inv(V))))

    psi2 = (np.square(np.dot(psi.T, F))) / np.dot(np.dot(psi.T, cov_r), psi)

    gis = np.append(psi.T, psi2, axis=1)
    gis = np.append(gis, a.aic)  # 之后阶数
    gis = np.append(gis, np.exp(data.iloc[-1, 0]))  # 区间最后价格

    return gis


if __name__ == "__main__":
    # 结果储存数据库
    server2 = '10.25.18.73'
    user2 = 'lining'
    password2 = 'lining'
    database2 = 'NINO'
    table = 'LeaderAver33'

    # 数据库中最大日期
    conn2 = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server2, DATABASE=database2, PWD=password2, UID=user2)
    cursor = conn2.cursor()
    sql1 = u"SELECT max([index]) FROM [NINO].[dbo].[%s]" % table
    try:
        cursor.execute(sql1)
        dbdate = cursor.fetchone()[0]
        date1 = dbdate + timedelta(days=1)
        date2 = datetime.now()
        # date2 = u"2019-04-18"
    except:  # 新建数据库
        date1 = u"2019-04-19"
        date2 = u"2019-04-19"  # 包括第一天，包括最后一天

    conn2.commit()
    conn2.close()

    w.start()
    datetrades = w.tdays(date1, date2, "").Data[0]
    trade_hiscode = w.wsd("IH.CFE", "trade_hiscode", date1, date2, "").Data[0]

    # tick行情
    server = '10.36.18.54'
    user = 'mktdtr'
    password = '123678'
    database = 'MktDataHis'
    conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE=database, PWD=password, UID=user)

    result2 = []
    times2 = []
    # 每日提取一次数据
    futcodes = w.wset("futurecc", "startdate=%s;enddate=%s;wind_code=IH.CFE" % (date1, date2)).Data[2]
    lasttrade = w.wss(futcodes, "lasttrade_date").Data[0]
    for i in range(0, len(datetrades)):
        # 期货最后交易日转为次月合约
        lastdayt = trade_hiscode[0]
        futnum = 0
        for ii in range(0, len(futcodes)):
            if trade_hiscode[i] == futcodes[ii]:
                lastdayt = lasttrade[ii]
                futnum = ii
                break
        if datetrades[i] == lastdayt:
            forward1 = futcodes[futnum + 1]
        else:
            forward1 = trade_hiscode[i]
        # 计算贡献度
        print(str(datetrades[i]) + ' begin')
        main(forward1[:6], datetrades[i], datetrades[i], datetrades[i] + timedelta(days=1), result2,
             times2)

    conn.commit()
    conn.close()

    pand = pd.DataFrame(result2, index=times2,
                        columns=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])

    from sqlalchemy import create_engine

    engine = create_engine('mssql+pyodbc://%s:%s@%s:1433/%s?driver=SQL+Server' % (user2, password2, server2, database2))

    # pand = pand.reset_index()
    # pand.rename(columns={'index': 'date'}, inplace=True)
    pand.to_sql(name=table, con=engine, if_exists='append', index=True)

    print('all done')
