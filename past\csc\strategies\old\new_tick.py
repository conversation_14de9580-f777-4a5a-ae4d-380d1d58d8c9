""""""
from typing import Any
from vnpy.app.cta_strategy import (
    CtaTemplate,
    BarGenerator,
    ArrayManager,
    TickData,
    BarData,
    OrderData,
    TradeData
)

from vnpy.mytools.utility import SecondBarGenerator
from vnpy.trader.constant import Status


class TickStrategy(CtaTemplate):
    """"""

    author = "vnpy"

    boll_window = 20
    boll_dev = 2
    trailing_long = 0.5
    trailing_short = 0.5
    fixed_size = 1

    boll_up = 0
    boll_down = 0
    trading_size = 0
    intra_trade_high = 0
    intra_trade_low = 0
    long_stop = 0
    short_stop = 0

    buy_price = 0
    short_price = 0

    parameters = [
        'boll_window',
        'boll_dev',
        'trailing_long',
        'trailing_short',
        'fixed_size',
    ]
    variables = [
        'boll_up',
        'boll_down',
        'trading_size',
        'intra_trade_high',
        'intra_trade_low',
        'long_stop',
        'short_stop',
        'buy_price',
        'short_price'
    ]

    def __init__(
        self,
        cta_engine: Any,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        self.sbg = SecondBarGenerator(self.on_bar)
        self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        self.am = ArrayManager(size=300)

        self.buy_vt_orderids = []
        self.short_vt_orderids = []

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_tick(0)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.cancel_all()
    


        self.buy(self.buy_price, self.fixed_size)

        self.short(self.short_price, self.fixed_size)

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.(second)
        """
        self.sbg5.update_bar(bar)

        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return

    def on_5s_bar(self, bar: BarData):
        """"""
        pass


