#%% 
'''IP SETTING FOR INFLUXDB'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('CZCE.dev')
import warnings
warnings.filterwarnings('ignore')
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机""
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.VWAP import VWAP
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
 
#%%
maker='RM207.CZCE'
refer='RM207.CZCE'

multiplier=10

engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=False,fast_join=True,refer_late = True,refer_test=False) #Counter代表是否只统计对价成交，duty代表是否统计义务

engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime.datetime(2022, 2,17, 21, 0), # 开始时间
    end=datetime.datetime(2022, 2,18,15,0), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: 1,refer: 1}, # 一个tick大小
    capital=1_000_000, # 初始资金
)
# 添加回测策略，并修改内部参数

#%%
engine.clear_data()
# engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})

engine.add_strategy(VWAP, {'lots': 10,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':5,'stop':1,'path':120})

engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
duty = engine.duty_statistics()
#engine.show_tick_chart() # 显示图表

#%%
import pandas as pd
import numpy as np
from datetime import timedelta

mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
           'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
           'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
           'last_price','volume','turnover']]
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))

mkt_main = mkt[mkt.symbol==engine.main_symbol]
mkt_main['dV'] = mkt_main.volume - mkt_main.volume.shift(1)
mkt_main['dT'] = (mkt_main.turnover - mkt_main.turnover.shift(1)).apply(lambda x:str(x))
 
trade_split = pd.DataFrame(engine.trade_split,columns=['time','vt_symbol','trade']) #成交拆分
trade_split = trade_split[trade_split.vt_symbol==maker]

trades = pd.DataFrame([{'datetime':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
           } for x in engine.trades])
trades['net'] = (trades.volume*trades.direction).cumsum()
cash = (trades['direction']*(-1)*trades['volume']*trades['price'])*multiplier
cashCum = np.cumsum(cash)
trades['pnl'] = cashCum + trades.net*trades.price*multiplier
orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
           } for x in engine.get_all_orders()])

#%%
import pandas as pd
import numpy as np
execution_pnl_actual = 0
execution_pnl_next = 0
for i in trades.index:
    if trades.loc[i,'symbol'] == maker.split('.')[0]:
        execution_pnl_actual += (trades.loc[i,'price'] - trades.loc[i,'midPrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier    
        execution_pnl_next += (trades.loc[i,'price'] - trades.loc[i,'midPrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier  
    if trades.loc[i,'symbol'] == refer.split('.')[0]:
        execution_pnl_actual += (trades.loc[i,'price'] - trades.loc[i,'basePrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier   
        execution_pnl_next += (trades.loc[i,'midPrice_next'] - trades.loc[i,'basePrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier

total_pnl = df["net_pnl"].sum()
print('总盈亏:',total_pnl)
print('实际执行盈亏:',execution_pnl_actual)
print('中价执行盈亏:',execution_pnl_next)
print('理论盈亏:',total_pnl - execution_pnl_next)

#%%
def run_backtest(startdate, enddate, maker, refer, maxPos = 20, save_flag = False, name = 'basis_trading', number = 0,path = 500,eta = 1, onlyCrossFlag=False):
    save_strategy=True
    save_risk = True
    save_order = True
    save_trade = True
    onlyCrossFlag=False
    number=number
    tick_size = 1
    duty=False

    lots=1
    redifEWMAFlag=False
    
    parameters = {'path':path, 'eta':eta, 'maxPos':maxPos}
    
    engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=onlyCrossFlag,duty=duty,save_result=False,refer_test=False,rule_out = False,fast_join=True,refer_late = True) #Counter代表是否只统计对价成交，duty代表是否统计义务
    start=datetime.datetime(startdate[0], startdate[1], startdate[2], 21, 0) # 开始时间
    end=datetime.datetime(enddate[0], enddate[1],enddate[2], 15, 0) # 结束时间
    
    engine.set_parameters(
        vt_symbols=[maker,refer], # 回测品种
        interval=Interval.TICK, # 回测模式的数据间隔
    
        start=start, # 开始时间
        end=end, # 结束时间
        rates={maker: 0,refer: 0}, # 手续费率
        slippages={maker: 0,refer: 0}, # 滑点
        sizes={maker: multiplier,refer: multiplier}, # 合约规模
        priceticks={maker: tick_size,refer: tick_size}, # 一个tick大小
        capital=1_000_000, # 初始资金
    )
    
    # 添加回测策略，并修改内部参数
    engine.clear_data()

    engine.add_strategy(BasisTrading, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'maxPos':maxPos,'path':path,'eta':eta})
    
    engine.load_data() # 加载历史数据
    engine.run_backtesting() # 进行回测
    
    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
    stat = engine.calculate_tick_statistics() # 统计日度策略指标
    df_mkt = pd.DataFrame()
    if save_flag:
        if save_risk:
            df_mkt = engine.get_risk(multiplier=multiplier)
        else:
            df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
        if save_strategy:
            df_strategy = engine.get_strategy()
        else:
            df_strategy = pd.DataFrame([])
        engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade)
    
    
    # print(engine.result)
    # engine.duty_statistics()
    # from vnpy.trader.analysis import Analysis
    
    # analysis = Analysis(engine)
    # df = analysis.pnl_plot() 
    return engine, df_mkt #, df
# # =======
# # time_list = [7,8,11,12,13,14,15]
# time_list = [7,8]
#%%

multiplier=10

startdate, enddate, maker, refer, maxPos,path ,eta, number = ([2022, 2, 16], [2022, 2, 17], 'rb2209.SHFE', 'rb2210.SHFE', 30,500,1,0)
engine, df_mkt = run_backtest(startdate, enddate, maker, refer, maxPos,save_flag=True, number=number,path=path,eta=eta)

df_offer_list = pd.DataFrame(engine.strategy.offer_list)
df_offer_list.columns = engine.strategy.offer_list_head
df_mkt = engine.get_risk(multiplier=multiplier)
# df_test = df_mkt[df_mkt.symbol=='rb2202']
#%%
df_strategy = engine.get_strategy()
