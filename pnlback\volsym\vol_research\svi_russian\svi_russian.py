#!/usr/bin/env python 
# encoding: utf-8 
""" 
@version: ?? 
@author: laofish 
@contact: <EMAIL> 
@site: http://www.laofish.com 
@file: fitSVI.py 
@time: 2018-09-07 23:48 
 
拟合SVI曲线 
 
""" 
 
import traceback 
 
from scipy.optimize import lsq_linear 
from scipy.optimize import fmin 
 
from lsqlin import * 
 
from scipy.optimize import least_squares 

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
 
# 中文和负号的正常显示 
import socket 
 
hostName = socket.gethostname() 
 
if hostName == 'laofish-home-PC': 
    plt.rcParams['font.sans-serif'] = ['Yahei Mono']  # mat 2.1 
else: 
    plt.rcParams['font.sans-serif'] = ['Microsoft Yahei Mono'] 
 
plt.rcParams['axes.unicode_minus'] = False 
 
# 设置图形的显示风格 
plt.style.use('ggplot') 
 
 
def neicengOptimization(guess, myargs): 
    '''内侧优化使用''' 
    # SVI模型内层优化 
    # m,sigma 为给定值，直接从参数里面得到 
    # x 为自变量,即最优化问题里面的x 
    # a d c 为系数，即最优化问题里面的c 
 
    m, sigma = guess 
 
    x = myargs[0] 
 
    omg_i = myargs[1] 
 
    # x = lsqlin(C,d,A,b) solves the linear system C*x = d 
    # in the least-squares sense subject to A*x ≤ b, where C is m-by-n. 
 
    yx = (x - m) / sigma 
    zx = np.sqrt(yx ** 2 + 1) 
 
    omega = max(omg_i) 
 
    # xz = np.array([np.ones([len(x), 1]).T, yx, zx]) 
    xz = np.array([np.array(np.ones([len(x), 1]).T.tolist()[0]), yx, zx]) 
 
    # 实际上可能可以用cvxpy这个凸优化包的使用更为简单 
    A = [[0, 0, -1], [0, 0, 1], [0, -1, -1], [0, 1, -1], [0, 1, 1], [0, -1, 1], [-1, 0, 0], [1, 0, 0]] 
    # 约束边界b 
 
    b = [0, 4 * sigma, 0, 0, 4 * sigma, 4 * sigma, 0, omega] 
    # lsqlin(C, d, reg=0, A=None） 
    xmatric = lsqlin(xz.T, omg_i, 0, np.array(A), np.array(b)) 
    acap = np.array(xmatric['x'])[0][0] 
    d = np.array(xmatric['x'])[1][0] 
    c = np.array(xmatric['x'])[2][0] 
    # [acap, d, c]=lsqlin(xz.T, omg_i, 0, np.array(A), np.array(b)) 
 
    # [acap,d,c] = lsqlin(xz, omg_i,A,b) 
 
    a = acap 
    b = c / sigma 
    rho = d / c 
 
    # 目标函数 
    sigma2 = np.sum(np.array(acap + d * yx + c * zx - omg_i) ** 2) 
 
    # return [a,b,rho,sigma2] 
 
    return sigma2 
 
 
def neicengOptimization2(guess, myargs): 
    '''返回a d c ''' 
    m, sigma = guess 
    x = myargs[0] 
    omg_i = myargs[1] 
 
    yx = (x - m) / sigma 
    zx = np.sqrt(yx ** 2 + 1) 
 
    omega = max(omg_i) 
 
    # xz = np.array([np.ones([len(x), 1]).T, yx, zx]) 
    xz = np.array([np.array(np.ones([len(x), 1]).T.tolist()[0]), yx, zx]) 
 
    A = [[0, 0, -1], [0, 0, 1], [0, -1, -1], [0, 1, -1], [0, 1, 1], [0, -1, 1], [-1, 0, 0], [1, 0, 0]] 
    # 约束边界b 
 
    b = [0, 4 * sigma, 0, 0, 4 * sigma, 4 * sigma, 0, omega] 
    # lsqlin(C, d, reg=0, A=None） 
    xmatric = lsqlin(xz.T, omg_i, 0, np.array(A), np.array(b)) 
    acap = np.array(xmatric['x'])[0][0] 
    d = np.array(xmatric['x'])[1][0] 
    c = np.array(xmatric['x'])[2][0] 
    # [acap, d, c]=lsqlin(xz.T, omg_i, 0, np.array(A), np.array(b)) 
 
    # [acap,d,c] = lsqlin(xz, omg_i,A,b) 
 
    a = acap 
    b = c / sigma 
    rho = d / c 
 
    # 目标函数 
    # sigma2 = np.sum(np.array(acap + d * yx + c * zx - omg_i) ** 2) 
    return [a, b, rho] 
 
 
def getfitsiv(x, t2M, a_star, b_star, rho_star, m_star, sigma_star): 
    '''生成拟合后的SVI波动率''' 
 
    # 计算拟合值 
    fit_omg = a_star + b_star * (rho_star * (x - m_star) + np.sqrt((x - m_star) ** 2 + sigma_star ** 2)) 
 
    fsigma = np.sqrt(fit_omg / t2M) 
 
    return fsigma 
 
 
if __name__ == '__main__': 
 
    optionData = pd.read_excel('D:/code/pythonworld/pnlback/volsym/vol_research/svi_russian/rawOptionData.xlsx') 
 
    strikeArr = pd.unique(optionData['期权行权价']) 
    time2Mature = pd.unique(optionData['剩余到期时间(日历日)']) 
    ulprice = pd.unique(optionData['标的收盘价']) 
 
    r = 0.04 
    S = ulprice[0] 
    q = 0 
 
    # 鉴于国内特色，只计算call 
    oD = optionData 
    vArr = [] 
    strikeArrE = [] 
    time2MatureE = [] 
    cData = pd.DataFrame() 
    try: 
        # 处理错误 
 
        # step 1 
        # 计算期权市场的隐含波动率 
        for strike in strikeArr: 
            for t2M in time2Mature: 
                oprice = oD[(oD['期权行权价'] == strike) & (oD['期权类型'] == 'C') 
                            & (oD['剩余到期时间(日历日)'] == t2M)]['收盘中间价'] 
                if oprice.empty == False: 
                    cimpv = bsmImpVol(S, strike, t2M, r, q, oprice.tolist()[0], 'C') 
                else: 
                    cimpv = 0 
 
                # 这个就是隐含方差 
                # total implied variance 
                v = cimpv ** 2 * t2M 
 
                vArr.append(v) 
                strikeArrE.append(strike) 
                time2MatureE.append(t2M) 
 
        cData['K'] = strikeArrE 
        cData['t2M'] = time2MatureE 
        cData['v'] = vArr 
 
        # 去掉v等于0的元素 
        cData2 = cData[cData['v'] != 0] 
 
        # 论文中没提到的是，这里应该一个到期时间一个到期时间的循环 
        for t2M in cData2['t2M'].unique(): 
            # 扣除所需数据 
            thisData = cData2[cData2['t2M'] == t2M] 
            x = np.log(thisData['K'] / (S * np.exp(r * t2M))) 
 
            omg_i = thisData['v'].values 
 
            # %para为m和sigma的初值 
            m = 0.1 
            sigma = 0.2 
 
            myargs = np.array([x, omg_i])  # 需要传进去的参数 
 
            # arg_out= fmin(neicengOptimization, para) 
            # neicengOptimization(m, sigma, myargs) 
            # arg_out = fmin(neicengOptimization, x0=[m, sigma],args=(myargs,)) 
            guess = [m, sigma] 
            arg_out = fmin(neicengOptimization, guess, args=(myargs,)) 
 
            # m sigma 
            m_star, sigma_star = arg_out 
            # 获取a,b,rho 
            a_star, b_star, rho_star = neicengOptimization2(arg_out.tolist(), myargs) 
 
            # =================================== 
            # 绘制拟合和真是曲线 
            k1 = thisData['K'].tolist() 
 
            # 换成隐含波动率比较 
            tsigma = np.sqrt(omg_i / t2M) 
 
            fig = plt.figure() 
            ax1 = fig.add_subplot(111)  # 在图表1中创建子图1 
 
            ax1.scatter(k1, tsigma, color='.25', label='Value') 
 
            # 生成拟合后的曲线 
            # xaxis = np.linspace(min(k1), max(k1), 100) 
            xaxis = np.linspace(min(k1), max(k1), 100) 
            # np.log(thisData['K'] / (S * np.exp(r * t2M))) 
            # 行权价转化为实际计算用x 
            xbx=[np.log(x / (S * np.exp(r * t2M))) for x in xaxis] 
 
            yaxis = [getfitsiv(xk, t2M, a_star, b_star, rho_star, m_star, sigma_star) for xk in xbx] 
 
            # ax1.plot(xaxis.tolist(), yaxis, color='r--',label='fitValue') 
            ax1.plot(xaxis.tolist(), yaxis, 'r--', label='fitValue') 
 
            # 去除图形顶部边界和右边界的刻度 
            # ax1.tick_params(top='off', right='off') mat 2.1 
            ax1.tick_params(top=False, right=False) 
 
            ax1.set_title('svi模型拟合对比') 
 
            ax1.legend() 
 
            plt.savefig('fitsvi_t2m=' + str(round(t2M, 3)) + '.png') 
 
            plt.close() 
            # plt.show() 
 
        # 检测是否在要求阈值内 
 
        print(cData) 
    except:  # 处理异常 
 
        traceback.print_exc() 
        pass 
 
    print(optionData) 