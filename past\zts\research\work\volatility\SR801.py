# -*- coding:utf-8 -*-
from __future__ import print_function
import pyodbc
import pandas as pd
import numpy as np
from WindPy import w
from pandas import DataFrame
import math
import xlwt

w.start()

wsd_data = w.wsd("SR801.CZC", "trade_hiscode,close", "2017-07-17", "2017-11-20", "PriceAdj=F")

fm = pd.DataFrame(wsd_data.Data, index=wsd_data.Fields, columns=wsd_data.Times)
fm = fm.T  # 将矩阵转置
# print fm


sqllist2 = []
for index, row in fm.iterrows():
    time = index
    print(time)
    code = row['TRADE_HISCODE']
    price = row['CLOSE']
    a = math.modf(price / 100)[0]
    if a == 0.5:
        price2 = int(price / 100) * 100
    else:
        price2 = round(price / 100) * 100

    code1 = code[:-4] + "C" + str(price2)[0:4] + code[-4:]
    code2 = code[:-4] + "P" + str(price2)[0:4] + code[-4:]
    # print code2
    wdata2 = w.wss("%s,%s" % (code1, code2), "close", "tradeDate=%s;priceAdj=F;cycle=D" % time)
    # print wdata2
    C = wdata2.Data[0][0]
    P = wdata2.Data[0][1]
    # print C,P
    S = C - P + price2

    sqllist = []

    sqllist.append(time)
    sqllist.append(code)
    sqllist.append(price)
    sqllist.append(price2)
    sqllist.append(C)
    sqllist.append(P)
    sqllist.append(S)
    sqllist.append(S - price)

    sqltuple = tuple(sqllist)

    sqllist2.append(sqltuple)

print(sqllist2)



f = xlwt.Workbook()  # 创建工作簿
sheet1 = f.add_sheet(u'opt_fut', cell_overwrite_ok=True)  # 创建sheet
for i, row in enumerate(sqllist2):
    for j, col in enumerate(row):
        sheet1.write(i, j, col)
f.save('SR801.xls')  # 保存文件
