pragma Singleton
import QtQuick

QtObject {
    // 颜色
    readonly property color primaryColor: "#007AFF"
    readonly property color secondaryColor: "#5856D6"
    readonly property color backgroundColor: "#F2F2F7"
    readonly property color textColor: "#000000"
    readonly property color borderColor: "#C7C7CC"
    
    // 字体
    readonly property int smallFontSize: 12
    readonly property int normalFontSize: 14
    readonly property int largeFontSize: 16
    
    // 间距
    readonly property int smallSpacing: 5
    readonly property int normalSpacing: 10
    readonly property int largeSpacing: 20
    
    // 边框
    readonly property int borderWidth: 1
    readonly property int cornerRadius: 4
    
    // 动画持续时间
    readonly property int shortAnimation: 150
    readonly property int normalAnimation: 250
    readonly property int longAnimation: 350
}