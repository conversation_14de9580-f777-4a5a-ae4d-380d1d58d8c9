# -*- coding: utf-8 -*-
"""
Created on Thu Aug 19 13:40:45 2021

@author: csctest
"""
from influxdb import InfluxDBClient
from OmmDatabase import OmmDatabase
import datetime
import time
import numpy as np
import pandas as pd

#%%

client = InfluxDBClient('10.17.88.168',9001,'reader','reader','testbase') # 东坝机房
#%%
date_list = ['2021-06-25', '2021-06-28', '2021-06-29', '2021-06-30', '2021-07-01', '2021-07-02', '2021-07-05', '2021-07-06', \
             '2021-07-07', '2021-07-08', '2021-07-09', '2021-07-12', '2021-07-13', '2021-07-14', '2021-07-15', '2021-07-16', \
             '2021-07-19', '2021-07-20', '2021-07-21', '2021-07-22', '2021-07-23', '2021-07-26', '2021-07-27', '2021-07-28', \
             '2021-07-29', '2021-07-30', '2021-08-02', '2021-08-03', '2021-08-04', '2021-08-05', '2021-08-06', '2021-08-09', \
             '2021-08-10', '2021-08-11', '2021-08-12', '2021-08-13', '2021-08-16']
# date = '2021-08-16'
underlying_list = ['hc2111', 'hc2112', 'hc2202', 'hc2203', 'rb2111', 'rb2112', 'rb2202', 'rb2203']
# underlying = 'RB'
for date in date_list:
    print(date)
    df_summary = []
    t = time.time()
    for underlying in ['HC', 'RB']:
        
        db_path = 'C:/Users/<USER>/Desktop/data/shfe_'+date+'/shfe/'
        testDB = OmmDatabase(db_path)
        
        dfOC = testDB.read_file(db_path + 'OrderService/cancel/{}/'.format(underlying), date) 
        dfOO = testDB.read_file(db_path + 'OrderService/order/{}/'.format(underlying), date) 
        dfOT = testDB.read_file(db_path + 'OrderService/trade/{}/'.format(underlying), date) 
        dfOPO = testDB.read_file(db_path + 'OrderService/prepare_order/{}/'.format(underlying), date) 
        dfOPC = testDB.read_file(db_path + 'OrderService/prepare_cancel/{}/'.format(underlying), date) 
        
        dfOC['UnixStamp'] = dfOC['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOO['UnixStamp'] = dfOO['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOT['UnixStamp'] = dfOT['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOPO['UnixStamp'] = dfOPO['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        dfOPC['UnixStamp'] = dfOPC['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
        
        dfOC_dict = dfOC.to_dict('records')
        dfOO_dict = dfOO.to_dict('records')
        dfOT_dict = dfOT.to_dict('records')
        dfOPO_dict = dfOPO.to_dict('records')
        dfOPC_dict = dfOPC.to_dict('records')
        
        df_summary.append(dfOC_dict + dfOO_dict + dfOT_dict + dfOPO_dict + dfOPC_dict)
    
    
    print('文件读取时间：',  time.time() - t)
    df_summary = df_summary[0] + df_summary[1]        
    t = time.time()
    json_body = []
    
    for row in df_summary:
        
        current_time = int(row['UnixStamp']*10**9)
        
        measurement = 'shfe_future_order'
        
        base_price = row['BasePrice']
        comments = str(row['Comments'])
        error_id = 0
        error_message = str(row['ErrorMessage'])
        insid = str(row['InstrumentId'])
        internal_order_id = int(row['InternalOrderId'])
        last_traded_time = -1
        last_update_time = -1
        level = int(row['Level'])
        local_time = current_time
        long_short = int(row['LongShort'])
        match_condition = int(row['MatchCondition'])
        note = 'OS准备撤单' if row['OrderStatus'] == 1 else 'OS准备下单' if row['OrderStatus'] == 0 else 'OS收到Order回报'
        open_close = row['OpenClose']
        order_id = str(row['OrderID'])
        
        order_price = float(row['OrderPrice'])
        order_status = int(row['OrderStatus'])
        portfolio_id = int(row['PortfolioID'])
        stratege_id = int(row['StrategyID'])
        traded_price = float(row['TradedPrice'])
        volume_origin_total = int(row['Quantity'])
        volume_total = int(row['VolumeTotal'])
        volume_traded = int(row['VolumeTraded'])
           
        body = {
                "measurement": measurement, 
                "time": current_time, 
                "tags": {
                    "insid": insid
                }, 
                "fields": {
                    "base_price": base_price, 
                    "comments": comments,                 
                    "error_id": error_id,                 
                    "error_message": error_message,                 
                    "internal_order_id": internal_order_id,                 
                    "last_traded_time": last_traded_time,                 
                    "last_update_time": last_update_time,                    
                    "local_time": local_time,                    
                    "long_short": long_short,                    
                    "match_condition": match_condition,                    
                    "note": note, 
                    "open_close": open_close,                 
                    "order_id": order_id,                 
                    "order_price": order_price,                 
                    "order_status": order_status,                 
                    "portfolio_id": portfolio_id,                 
                    "stratege_id": stratege_id,                    
                    "traded_price": traded_price,                    
                    "volume_origin_total": volume_origin_total,                    
                    "volume_total": volume_total, 
                    "volume_traded": volume_traded               
                }, 
            }
        
        
        json_body.append(body)
    print('转格式时间:', time.time()-t)
    
    t = time.time()
    
    res = client.write_points(json_body[:], batch_size = 10000)
    print('写入时间:', time.time()-t)
    break
#%%
# import datetime
# beginStr = '2021-08-17T21:00:00.0Z'
# endStr = '2021-08-18T15:00:00.0Z'
# begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
# end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
# result = client.query("select * from shfe_future_order where time >= %d and time <= %d limit 10;"%(begin,end)) 
# #%%
# points = result.get_points()
# l=[]
# for d in points:
#     l.append(d)
# print(len(l))
# ll = pd.DataFrame(l)












