"""
Basic attribute implementation for orderbook dynamics.
These attributes provide the foundation for more complex attributes.
"""
from typing import Callable, TypeVar, Generic, Optional
from ..models import Cell, OrderBook


T = TypeVar('T')


class BasicAttribute:
    """
    Basic attribute for order book analysis.
    Applies a function to an OrderBook to produce a Cell[T].
    """
    def __init__(self, func: Callable[[OrderBook], Cell]):
        """
        Initialize a basic attribute.
        
        Args:
            func: Function taking an OrderBook and returning a Cell
        """
        self.func = func
        
    def __call__(self, order_book: OrderBook) -> Cell:
        """Apply the function to the order book."""
        return self.func(order_book)
    
    def map(self, func: Callable[[T], T]) -> 'BasicAttribute':
        """Apply a function to the result of this attribute."""
        original_func = self.func
        return BasicAttribute(lambda ob: original_func(ob).map(func))


class BasicSet:
    """
    Set of basic order book attributes.
    """
    def __init__(self, max_level: int = 5):
        """
        Initialize a basic set.
        
        Args:
            max_level: Maximum order book level to consider
        """
        self.max_level = max_level
        
    def bid_price(self, level: int = 1) -> BasicAttribute:
        """
        Get bid price at a specific level.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            BasicAttribute for bid price
        """
        def get_bid_price(order_book: OrderBook) -> Cell:
            return order_book.bid_price(level)
            
        return BasicAttribute(get_bid_price)
        
    def ask_price(self, level: int = 1) -> BasicAttribute:
        """
        Get ask price at a specific level.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            BasicAttribute for ask price
        """
        def get_ask_price(order_book: OrderBook) -> Cell:
            return order_book.ask_price(level)
            
        return BasicAttribute(get_ask_price)
        
    def bid_volume(self, level: int = 1) -> BasicAttribute:
        """
        Get bid volume at a specific level.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            BasicAttribute for bid volume
        """
        def get_bid_volume(order_book: OrderBook) -> Cell:
            return order_book.bid_volume(level)
            
        return BasicAttribute(get_bid_volume)
        
    def ask_volume(self, level: int = 1) -> BasicAttribute:
        """
        Get ask volume at a specific level.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            BasicAttribute for ask volume
        """
        def get_ask_volume(order_book: OrderBook) -> Cell:
            return order_book.ask_volume(level)
            
        return BasicAttribute(get_ask_volume)
        
    def spread(self) -> BasicAttribute:
        """
        Get the bid-ask spread.
        
        Returns:
            BasicAttribute for spread
        """
        def get_spread(order_book: OrderBook) -> Cell:
            return order_book.spread()
            
        return BasicAttribute(get_spread)
        
    def mid_price(self) -> BasicAttribute:
        """
        Get the mid price.
        
        Returns:
            BasicAttribute for mid price
        """
        def get_mid_price(order_book: OrderBook) -> Cell:
            return order_book.mid_price()
            
        return BasicAttribute(get_mid_price) 