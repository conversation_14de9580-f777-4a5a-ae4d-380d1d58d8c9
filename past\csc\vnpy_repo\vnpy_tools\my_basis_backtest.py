#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('SHFE.dev')
 
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.basisContract import BasisStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
 
#%%
month = 9
time_list = list(pd.read_excel("C:/Users/<USER>/Desktop/"+str(month)+"月.xlsx")['DateTime'])
edge_list = [2,4]
gamma_list = [0.3,0.5]
alpha_list = [0.01, 0.03]

#%%
def run_backtest(startdate, enddate,edge = 2, gamma = 0.3, alpha = 0.01):

    
    contract = 'hc'

    makers= [contract + x for x in['2111.SHFE','2112.SHFE','2202.SHFE','2203.SHFE']]
    refer=contract+'2201.SHFE'
    all_ins = makers.copy()
    all_ins.extend([refer]) 
    edges={}
    for i in makers:
        edges[i] = edge
    lots = {}
    for i in makers:
        lots[i] = 1
    multiplier=10
  
    engine = BacktestingEngine(alwaysInQueueHeadFlag=False,onlyCrossFlag=True,cancelFailProba=0,refer_test=True,output_result=True)

    engine.set_parameters(
        vt_symbols=all_ins, # 回测品种
        interval=Interval.TICK, # 回测模式的数据间隔
        start=datetime.datetime(startdate[0], startdate[1], startdate[2], 21, 0), # 开始时间
        end=datetime.datetime(enddate[0], enddate[1],enddate[2], 15, 0), # 结束时间
        rates={x:0 for x in all_ins}, # 手续费率
        slippages={x:0 for x in all_ins}, # 滑点
        sizes={x: multiplier for x in all_ins}, # 合约规模
        priceticks={x: 1 for x in all_ins}, # 一个tick大小
        capital=1_000_000, # 初始资金
    )
    
    # 添加回测策略，并修改内部参数
    engine.clear_data()
    engine.add_strategy(BasisStrategy, {'lots': lots,'makers':makers,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edges,'alpha':alpha,'maxPos':3,'gamma':gamma})
    engine.load_data() # 加载历史数据
    engine.run_backtesting() # 进行回测
    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
    stat = engine.calculate_tick_statistics() # 统计日度策略指标
    
    return engine,contract

#%%
import time
import os
t = time.time()
for edge in edge_list:
    for gamma in gamma_list:
        for alpha in alpha_list:
            result = []
            for i in range(1,len(time_list)):
                startdate = [time_list[i-1].year,time_list[i-1].month,time_list[i-1].day]
                enddate = [time_list[i].year,time_list[i].month,time_list[i].day]
                engine,contract = run_backtest(startdate, enddate,edge, gamma, alpha)
                result.append(engine.result)

                print(time.time()-t, (startdate, enddate, edge, gamma, alpha))
            
            path = 'C:/Users/<USER>/Desktop/backtestresult/'+str(month)+'/'+contract+'/'
            folder = os.path.exists(path)
            if not folder:
                os.makedirs(path)
                
            df_result=pd.DataFrame(result)
            df_result.to_csv(path+str(edge)+'-'+str(gamma)+'-'+str(alpha)+'.csv')