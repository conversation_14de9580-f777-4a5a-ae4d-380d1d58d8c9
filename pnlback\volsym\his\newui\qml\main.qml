import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Window
import Qt.labs.platform as Platform

ApplicationWindow {
    id: root
    visible: true
    width: 1200
    height: 800
    title: "Volatility Curve"
    
    // Main layout
    ColumnLayout {
        anchors.fill: parent
        spacing: 5
        
        // Top toolbar
        Rectangle {
            Layout.fillWidth: true
            height: 40
            color: "lightgray"
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 5
                spacing: 10
                
                Button {
                    text: "选择文件"
                    onClicked: fileDialog.open()
                }
                
                ComboBox {
                    id: monthCombo
                    Layout.preferredWidth: 200
                    model: ["所有月份"]
                    enabled: false
                }
                
                // Controls for r, newIV, and live mode
                TextField {
                    Layout.preferredWidth: 50
                    text: "0"
                    validator: DoubleValidator {}
                    onTextChanged: if (acceptableInput) backend.updateCustomR(parseFloat(text))
                }
                
                CheckBox {
                    text: "newIV"
                    onCheckedChanged: backend.setUseNewIv(checked)
                }
                
                But<PERSON> {
                    text: "实时模式"
                    checkable: true
                    onClicked: backend.toggleLiveMode(checked)
                }
            }
        }
        
        // Main chart area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            border.color: "black"
            
            Canvas {
                id: chartCanvas
                anchors.fill: parent
                anchors.margins: 20
                
                // Chart properties
                property real minX: 2000
                property real maxX: 5000
                property real minY: 0
                property real maxY: 1
                property point panOffset: Qt.point(0, 0)
                property bool showPositions: true
                
                // Coordinate conversion functions
                function toScreenX(x) {
                    console.log("toScreenX input:", x)
                    const margin = 40
                    const chartWidth = width - 2 * margin
                    return margin + (x - minX) * chartWidth / (maxX - minX)
                }
                
                function toScreenY(y) {
                    console.log("toScreenY input:", y)
                    const margin = 40
                    const chartHeight = height - 2 * margin
                    return height - margin - (y - minY) * chartHeight / (maxY - minY)
                }
                function toDataX(screenX) { /* ... */ }
                function toDataY(screenY) { /* ... */ }
                
                onPaint: {
                    var ctx = getContext("2d")
                    ctx.clearRect(0, 0, width, height)
                    
                    // Debug: Print canvas dimensions
                    console.log("Canvas dimensions:", width, height)
                    
                    // Draw coordinate system
                    ctx.beginPath()
                    ctx.strokeStyle = "black"
                    ctx.lineWidth = 1
                    
                    // Draw X axis
                    ctx.moveTo(0, height - 40)
                    ctx.lineTo(width, height - 40)
                    
                    // Draw Y axis
                    ctx.moveTo(40, 0)
                    ctx.lineTo(40, height)
                    ctx.stroke()
                    
                    // Debug: Print market data
                    if (backend.market_data) {
                        console.log("Market data available:", backend.market_data.length)
                        console.log("Sample data:", JSON.stringify(backend.market_data[0]))
                        
                        // Draw actual data
                        backend.market_data.forEach(point => {
                            const x = toScreenX(point['K'])
                            const y = toScreenY(point['vol'])
                            console.log("Point coordinates:", point['K'], point['vol'], "->", x, y)
                            
                            ctx.beginPath()
                            ctx.arc(x, y, 4, 0, 2 * Math.PI)
                            ctx.fill()
                        })
                    } else {
                        console.log("No market data available")
                    }
                }
                
                // Mouse interaction area
                MouseArea {
                    anchors.fill: parent
                    hoverEnabled: true
                    // Zoom, pan, and tooltip handling
                }
            }
            
            // Tooltip
            Rectangle {
                id: tooltip
                visible: false
                // Tooltip properties and text
            }
        }
        
        // Bottom control bar
        Rectangle {
            Layout.fillWidth: true
            height: 40
            color: "lightgray"
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 5
                spacing: 10
                
                CheckBox {
                    text: "显示持仓"
                    checked: chartCanvas.showPositions
                    onCheckedChanged: {
                        chartCanvas.showPositions = checked
                        chartCanvas.requestPaint()
                    }
                }
                
                Label {
                    text: "Ctrl+滚轮缩放 | 拖动平移"
                }
                
                Button {
                    text: "重置视图"
                    onClicked: {
                        chartCanvas.panOffset = Qt.point(0, 0)
                        chartCanvas.requestPaint()
                    }
                }
            }
        }
    }
    
    // File Dialog
    Platform.FileDialog {
        id: fileDialog
        title: "选择数据文件"
        nameFilters: ["Parquet files (*.parquet)", "CSV files (*.csv)"]
        onAccepted: {
            backend.loadFile(file)
            monthCombo.enabled = true
        }
    }
    
    // Backend connections
    Connections {
        target: backend
        function onDataChanged() {
            chartCanvas.requestPaint()
        }
    }
}