# -*- coding: utf-8 -*-

import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random

class RandomStrategy(StrategyTemplate):
    """"""

    author = "vnpy"

    boll_window = 20
    boll_dev = 2
    trailing_long = 0.5
    trailing_short = 0.5
    lots = 1

    boll_up = 0
    boll_down = 0
    trading_size = 0
    intra_trade_high = 0
    intra_trade_low = 0
    long_stop = 0
    short_stop = 0

    buy_price = 0
    short_price = 0

    parameters = [
        'boll_window',
        'boll_dev',
        'trailing_long',
        'trailing_short',
        'lots',
        'edge',
        'minEdge',
        'gamma',
        'eta',
        'maxPos',
        'sizes',
        'loss',
        'maker',
        'refer',
        'priceticks',
        'validVolume',
        'safeVolume',
        'profit_edge',
        'loss_edge'
    ]
    variables = [
        'boll_up',
        'boll_down',
        'trading_size',
        'intra_trade_high',
        'intra_trade_low',
        'long_stop',
        'short_stop',
        'buy_price',
        'short_price'
    ]

    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        #self.sbg = SecondBarGenerator(self.on_bar)
        #self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        #self.am = ArrayManager(size=300)

        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_best_orderids = None
        self.short_best_orderids = None
        self.buy_nonbest_orderids = None
        self.short_nonbest_orderids = None
        self.hedge_orderids = None

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net=0
        self.realPnl=0
        self.fair = 0
        self.cumPnl=0
        length=99999
        self.mFair=np.zeros(length) # maker fair price 
        self.mAsk=np.zeros(length)  
        self.mBid=np.zeros(length)
        self.mCount=0
        self.rCount=0
        self.pauseCount=0
        self.lastRefer={"net":0, "tick":None, "timepoint":0}        
        self.lastMaker={"net":0, "tick":None, "timepoint":0}
        self.maxLoss = 0
        self.isQuoting=False
        self.lastTicks={self.maker:0,self.refer:0}
        self.refer_oid=0
        self.reverseOrderModeFlag=False
        self.bidOffset=[]
        self.askOffset=[]
        self.active_trade_orders :  Dict[str, TradeData] = {}
        self.comments  = None
        self.signal = None
        self.last_bidP = 0
        self.last_askP = 0
        self.last_ask_1 = 0
        self.last_bid_1 = 0
        
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def getAsk5(self,tick,ask_volume,away_price):
        volume=0
        for i in range(1,6):
            volume += tick.__getattribute__('ask_volume_%d'%i)
            if volume>=ask_volume:
                short_price = tick.__getattribute__('ask_price_%d'%i)
                break
        if volume<ask_volume:
            short_price = tick.__getattribute__('ask_price_%d'%i)+away_price
        return short_price
        
    def getBid5(self,tick,buy_volume,away_price):
        volume=0
        for i in range(1,6):
            volume += tick.__getattribute__('bid_volume_%d'%i)
            if volume>=buy_volume:
                buy_price = tick.__getattribute__('bid_price_%d'%i)
                break
        if volume<buy_volume:
            buy_price = tick.__getattribute__('bid_price_%d'%i) - away_price
        return buy_price
    
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair

    def sum_book_volume(self, tick: TickData):
        """统计盘口挂单量"""
        total_bid = 0
        total_ask = 0

        for level in range(1, 6):
            total_bid += tick.__getattribute__(f'bid_volume_{level}')
            total_ask += tick.__getattribute__(f'ask_volume_{level}')
        
        total = (total_bid + total_ask)/2
        return max(total, 1)

    def stop_profit(self,ts,tickCounts):
        stop = self.profit_edge * ts
        return stop

    def stop_loss(self,ts,tickCounts):
        if tickCounts <= 10:
            stop = self.loss_edge * ts
        else:
            stop = (0.01 * (tickCounts - 10) + self.loss_edge) * ts         
        return stop 

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.maker:
                    self.on_tick_maker(ticks[key])
                elif key==self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick
        
    def on_tick_refer(self,tick):
        self.rCount +=1  
        # 主力价格击穿 
        safeVolume = self.safeVolume
        ts = self.priceticks[self.refer]
        edge=self.edge
        if self.rCount>5 and  (tick.ask_price_1 < self.lastRefer["tick"].bid_price_1 or tick.bid_price_1 > self.lastRefer["tick"].ask_price_1):
            self.isQuoting=False 
            pauseCount=self.rCount+20      
            print("refer cross limit pause， GapRisk",tick.datetime+timedelta(hours=8))  
        # 主力缺乏流动性 (成交不连续、价差过大)
        askP_refer = self.getAsk5(tick, safeVolume, ts)   #   L2 行情准确
        bidP_refer = self.getBid5(tick, safeVolume, ts)   #  
        if   (askP_refer-bidP_refer)>1.2*edge*ts:
            self.isQuoting=False 
            pauseCount=self.rCount + 5
        # near to market limit price  or  缺失流动性   # 特殊价格处理， 无效价格对应量为0，先判断量  
        # if self.isQuoting and (tick.limit_up and tick.limit_down) and ( tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (                                                 
        #             tick.bid_price_1 > int(tick.limit_up) - 5*ts or  # 涨停附近                       
        #             tick.ask_price_1 < int(tick.limit_down) + 5*ts ):  # 跌停附近
        #     self.isQuoting  = False
        #     pauseCount = self.rCount+300
        #     print("price limit pause",tick.datetime+timedelta(hours=8)) 
        # save         
        self.lastRefer["timepoint"]=tick.datetime+timedelta(hours=8)
        self.lastRefer["net"]=self.net
        self.lastRefer["tick"]=tick  
        self.rCount+=1
            

    def on_tick_maker(self, tick):

        ts = self.priceticks[self.maker]
        minEdge = self.minEdge
        edge=self.edge
        net = self.net
        lots = self.lots
        eta = self.eta
        stdRange=0.5
        maxV=50
        shortResendFlag=False
        buyResendFlag=False
        adjust=1
        validVolume = self.validVolume
        mCount = self.mCount
        safeVolume = self.safeVolume
        isBetter = True

        for buf_vt_orderids in [
            self.buy_best_orderids,
            self.short_best_orderids,
            self.buy_nonbest_orderids,
            self.short_nonbest_orderids
        ]:  #做市合约的对冲未能全成，则在主力合约进行超价反手

            if buf_vt_orderids and buf_vt_orderids[0] in self.strategy_engine.active_limit_orders :  
                order = self.strategy_engine.active_limit_orders[buf_vt_orderids[0]]
                volume = order.volume - order.traded
                if order.direction.value == '空' :
                    self.hedge_orderids = self.short(self.refer,self.strategy_engine.ticks[self.refer].bid_price_1,volume,'short_refer')
                else:
                    self.hedge_orderids = self.buy(self.refer,self.strategy_engine.ticks[self.refer].ask_price_1,volume,'buy_refer')
                self.strategy_engine.active_limit_orders.pop(buf_vt_orderids[0])
            
        #创建单独的trade_list，用来记录成交的订单
        for trade in list(self.active_trade_orders.values()):
            if trade.comments == 'best_trade':
                tickCounts = round((tick.datetime - trade.datetime).total_seconds() * (1000/250)) #大商所500ms一个数据切片
                     
                pnl = (tick.bid_price_1 - trade.price)  if trade.direction.value=='多' else (trade.price - tick.ask_price_1)
                if pnl >= self.stop_profit(ts,tickCounts) or pnl <= self.stop_loss(ts,tickCounts):
                    if trade.direction.value=='多':
                        self.cancel_all()
                        self.buy_best_orderids=[]
                        self.short_best_orderids = self.short(self.maker, tick.bid_price_1, trade.volume,'short_best')
                    elif trade.direction.value=='空':
                        self.cancel_all()
                        self.short_best_orderids=[]
                        self.buy_best_orderids = self.buy(self.maker,tick.ask_price_1, trade.volume,'buy_best')
                    self.active_trade_orders.pop(trade.vt_tradeid)
        
        # 1. Filter ：非可控情景暂停 
        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
            # 持仓超限暂停 、在途订单超限暂停、
            if abs(net)>self.maxPos:
                self.isQuoting=False 
                self.pauseCount=self.rCount+100      
                print("Net Position limit pause",tick.datetime+timedelta(hours=8))
            if len(self.strategy_engine.active_limit_orders)>10:     #在途订单超限暂停 or mdDelay>mdDelayLimit or tdDelay>tdDelayLimit 
                self.isQuoting=False 
                self.pauseCount=self.rCount+10    
                print("Delay limit pause",tick.datetime+timedelta(hours=8))
            if self.cumPnl< -self.maxLoss: # 亏损超限暂停
                self.isQuoting=False 
                self.pauseCount=self.rCount+100
                self.maxLoss += self.loss  # 
                print("maker Loss limit pause",tick.datetime+timedelta(hours=8))
            # market cross pause
            if mCount>5 and (tick.ask_price_1 < self.lastMaker["tick"].bid_price_1 -ts or tick.bid_price_1 > self.lastMaker["tick"].ask_price_1 +ts):
                self.isQuoting=False 
                self.pauseCount=self.rCount+60     
                print("maker gap limit pause",tick.datetime+timedelta(hours=8)) #   
            # market big volume pause 
            if mCount>5 and (tick.volume -self.lastMaker["tick"].volume) > safeVolume : # 安全量可能不足、价格可能被击穿 
                self.isQuoting=False 
                self.pauseCount=self.rCount+10   
            # near to market limit price  
            if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (
                tick.bid_price_1 > float(tick.limit_up) - 5*ts or  # 涨停附近                   
                tick.ask_price_1 < float(tick.limit_down) + 5*ts):   # 跌停附近                  
                    self.isQuoting=False
                    self.pauseCount=self.rCount + 100
                    print("price limit pause", tick.datetime+timedelta(hours=8))

            # liquidity 
            if self.sum_book_volume(tick) < self.lots * 10:  #市场做市商少于10个
                    self.isQuoting=False
                    self.pauseCount=self.rCount + 20
                    print("price limit pause", tick.datetime+timedelta(hours=8))                

            #TODO： 时间逻辑： 1、 时间暂停 22:59 10:14 11:29  14:59.. 暂未实现  2、 收盘前 adjust=2 不增仓 ，积极处理持仓
            '''
            if self.isQuoting and not refer.datetime: # "2020-07-26 23:04:21.000001"
                 self.isQuoting=False 
                 self.pauseCount=self.rCount + 100  
                 print("time section pause", tick.datetime)
            '''
        #check resume quoting............................................... 
        else:
            if abs(net)<self.maxPos and self.pauseCount<self.rCount:
                self.isQuoting=True 
                
        #### Algo : JoinMarket  ~~~~~~~无效价格情况暂未处理~~~~~~~~~~~~~~~~~~ 
        spread = tick.ask_price_1 - tick.bid_price_1
        away_price = ts*max(1, adjust*eta*max(edge/3, spread/2, 3*stdRange)) 
        bidP_best = self.getBid5(tick,validVolume, 0)
        askP_best = self.getAsk5(tick,validVolume, 0)             
        bidP_safe = max(self.getBid5(tick,self.validVolume, away_price)-away_price,self.getBid5(tick,self.safeVolume, away_price))              
        askP_safe = min(self.getAsk5(tick,self.validVolume, away_price)+away_price,self.getAsk5(tick,self.safeVolume, away_price))         
        self.fair = self.getFairPrice(self.fair,tick.ask_price_1,tick.bid_price_1, edge*ts)                           
        pnl= net*(self.fair-self.avg)/(abs(net)*ts) if net!=0 and self.fair>0 else 0 

        # 随机报价
        random_number = random.random() 
        if random_number > 0.5 + self.net/ self.lots * 0.1 :
            self.comments = '多'
            bidP = bidP_best
            askP = askP_safe
        else:
            self.comments = '空'
            bidP = bidP_safe
            askP = askP_best

        loss= 3000
        if isBetter and (mCount>150 and  self.rCount>30+self.pauseCount and self.cumPnl>-2*loss and 
                         abs(net)<0.7*self.maxPos):              
             adjust=0.5  # 下一笔 Tick 尝试缩小 有效量 
        else:
             adjust=1   # 收盘前 adjust=3 , 有最新成交 adjust=3     
             
        if minEdge>1:  # 最小价宽限制
            gg=(ts*minEdge -(askP-bidP))
            if gg>0:
                smallerSide = int(gg / 2 / ts) *ts
                biggerSide = gg - smallerSide
                if  net>=0: 
                    askP += smallerSide
                    bidP -= biggerSide
                else:
                    askP += biggerSide
                    bidP -= smallerSide

               #if  spread > minEdge*ts/2:    # 市场价宽较大不报价  （超过2tick 不报价 ， if edge=5， 60%义务足够了）
                    #isQuoting  =False 
                    #pauseCount =rCount +5 
        # 报价取整
        bidP = ts*round(bidP/ts) 
        askP = ts*round(askP/ts)            

        stdEdge=round((askP-bidP)/ts)   #  标准价差tick数  
        #### Algo End ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        self.cumPnl=round((net*self.fair + self.realPnl)*self.sizes[self.maker])  # 累计盈亏 ？？  
        if bidP>0 and askP>0:               
            self.mBid[mCount]=bidP
            self.mAsk[mCount]=askP
            self.mFair[mCount]=self.fair # mid price  save 
            self.mCount +=1  
            
        # save 
        self.lastMaker["timepoint"]= tick.datetime+timedelta(hours=8)
        self.lastMaker["net"] =net
        self.lastMaker["tick"]=tick 

        # TODO:  收盘前自动处理净持仓， 连续成交保护( 市场单边大幅波动)    

        askP_market = self.getBid5(tick,10*self.lots, 0)
        bidP_market = self.getAsk5(tick,10*self.lots, 0)
        
        if askP_market!=self.short_price or bidP_market!=self.buy_price or tick.bid_price_1 != self.last_bid_1 or tick.ask_price_1 != self.last_ask_1: #用于判断市场行情是否发生变动
            if self.last_askP != askP:
                shortResendFlag=True
            if self.last_bidP != bidP:
                buyResendFlag=True

        self.short_price = askP_market
        self.buy_price = bidP_market

        self.last_bidP = bidP
        self.last_askP = askP

        self.last_bid_1 = tick.bid_price_1
        self.last_ask_1 = tick.ask_price_1

        if mCount>35 :
            if not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(self.maker,bidP, self.lots,self.comments)
            elif buyResendFlag and self.buy_vt_orderids[0] :
                if self.cancel_order(self.buy_vt_orderids[0]):  #cancel完会跳转至onOrder，然后自动发一个单
                    self.buy_vt_orderids = self.buy(self.maker,bidP, self.lots,self.comments)
            
            if not self.short_vt_orderids:
                self.short_vt_orderids = self.short(self.maker,askP, self.lots,self.comments)
            elif shortResendFlag and self.short_vt_orderids[0] :
                if self.cancel_order(self.short_vt_orderids[0]): #cancel完会跳转至onOrder，然后自动发一个单
                    self.short_vt_orderids = self.short(self.maker,askP, self.lots,self.comments)

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if volume > 0:
            if trade.direction.value=='多':
                if self.net>=0 :            
                     self.avg = (self.net*self.avg +volume*price)/(self.net+volume)              
                elif volume+self.net>0: # net<0 # 平仓
                     self.avg = price         
                self.net += volume 
                self.realPnl -=volume*price
            #         
            elif trade.direction.value=='空':    
                if self.net<=0:
                    self.avg =(-self.net*self.avg + volume*price)/(-self.net+volume)
                elif volume-self.net>0: # net >0 # 平仓
                    self.avg=price
                self.net -= volume 
                self.realPnl +=volume*price

        self.active_trade_orders[trade.vt_tradeid] = trade

        if trade.comments == trade.direction.value: #best_price被成交,触发止盈止损离场
            trade.comments = 'best_trade'                                 

        elif (trade.comments == '多' and trade.direction.value == '空') or (trade.comments == '空' and trade.direction.value == '多'): #nonbest_price被成交，触发立刻离场
            trade.comments = 'nonbest_trade'
            if trade.direction.value=='多':
                self.cancel_all()  #cancel完会跳转至onOrder，然后自动发一个单
                self.buy_nonbest_orderids=[]
                self.short_nonbest_orderids = self.short(self.maker, trade.price, trade.volume,'short_nonbest')
            elif trade.direction.value=='空':
                self.cancel_all()  #cancel完会跳转至onOrder，然后自动发一个单
                self.short_nonbest_orderids=[]
                self.buy_nonbest_orderids = self.buy(self.maker, trade.price, trade.volume,'buy_nonbest')
            self.active_trade_orders.pop(trade.vt_tradeid)
            
    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_best_orderids,
            self.short_best_orderids,
            self.buy_nonbest_orderids,
            self.short_nonbest_orderids,
            self.hedge_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)

    def on_time(self,dt):
        """
        Callback of new tick time update.
        """
        pass
        
