# encoding: UTF-8


from datetime import time

import numpy as np
from vnpy.app.vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    TickSetManager,
    TickArrayManager
)

from pnlback.signal import signals
from vnpy.trader.constant import Status


########################################################################
class IMStrategy(CtaTemplate):
    """基于Tick的交易策略"""
    className = 'IMStrategy'

    # 策略参数
    fixedSize = 1  # 下单数量
    maxorder = 3
    pidnum = 0
    size = 2  # 缓存大小

    refdealnum = 10
    wide = 0.4
    sig_thres = 1
    maxpos = 2
    mult1 = 0.5
    mult2 = 1
    sigmode = 'or'
    max_sigadj = 1

    # 止盈止损参数
    stoppl = {'active': True,
              'stop_ratio': None, 'track_threshold': None, 'fallback_boundary': None,
              'multiplier': None, }

    # 策略变量
    posPrice = 0  # 持仓价格
    pos = 0  # 持仓数量

    avg_prc = 0
    max_prof = 0
    scorelist = dict(totalscore=0,
                     futscore=0,
                     stkscore=0,
                     stopscore=0)
    deltabalance = 0
    deltaswap = 0

    timenow = None

    # 参数列表，保存了参数的名称
    parameters = ['name',
                  'className',
                  'author',
                  'vtSymbol',
                  'refdealnum',
                  'wide',
                  'sig_thres',
                  'maxpos',
                  'mult1',
                  'mult2',
                  'sigmode',
                  'stoppl',
                  'max_sigadj',
                  ]

    # 变数清单，保存了变数的名称
    varList = ['inited',
               'trading',
               'pos',
               'posPrice',
               ]

    # 同步清单，保存了需要保存到资料库的变数名称
    syncList = ['pos',
                'posPrice',
                ]

    # ----------------------------------------------------------------------
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """Constructor"""
        super(IMStrategy, self).__init__(
            cta_engine, strategy_name, vt_symbol, setting
        )
        self.sig_set = np.zeros(self.size, dtype=object)

        # 创建Array伫列
        self.tickArray = TickSetManager(self.size, self.cta_engine.pids)

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        # self.load_bar(10)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    # ----------------------------------------------------------------------
    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        pidnum = self.pidnum
        for key in self.scorelist:
            self.scorelist[key] = 0

        self.tickArray.updateTickset(tick, pidnum)
        # self.tickArray.updateRealBook(pidnum)
        self.timenow = tick.datetime.time()

        if tick.datetime.time() > time(hour=14, minute=55):
            if self.pos > 0:
                self.sell(self.tickArray.Tickset[pidnum, -1]['BidPrice1'], abs(self.pos))
            elif self.pos < 0:
                self.cover(self.tickArray.Tickset[pidnum, -1]['AskPrice1'], abs(self.pos))
            return

        self.scorelist['futscore'] = self.fut_signal(pidnum)
        self.pos_manager()
        if abs(self.scorelist['totalscore']) > 0:
            self.orderfilter(pidnum)

    # ----------------------------------------------------------------------
    def fut_signal(self, pidnum):
        sigclass = signals.Signal(self.tickArray.Tickset[pidnum, -1])
        sigclass.signal_im5(5, )
        sigclass.signal_imvol(5, )
        self.tickArray.Tickset[pidnum, -1], sig_names = sigclass.data_md, sigclass.addcol
        if self.tickArray.Tickset[0, 0] == 0:
            return 0
        pxchg = self.tickArray.Tickset[pidnum, -1][sig_names[0]] - self.tickArray.Tickset[pidnum, -2][sig_names[0]]
        pxchg2 = self.tickArray.Tickset[pidnum, -1][sig_names[1]] - self.tickArray.Tickset[pidnum, -2][sig_names[1]]
        if self.sigmode == 'chg':
            sig = pxchg
        elif self.sigmode == 'one':
            sig = self.tickArray.Tickset[pidnum, -1][sig_names[0]]
        elif self.sigmode == 'mix':
            sig = pxchg * self.mult1 + pxchg2 * self.mult2
        elif self.sigmode == 'or':
            sig = pxchg * self.mult1
            sig2 = pxchg2 * self.mult2
            if abs(sig) > self.sig_thres:
                sig = sig
            elif abs(sig2) > self.sig_thres:
                sig = sig2
        # 解释一下的话
        sigscore = min(max(round((sig / self.sig_thres), 0), -self.max_sigadj), self.max_sigadj)
        mktwide = round(
            self.tickArray.Tickset[pidnum, -1]['AskPrice1'] - self.tickArray.Tickset[pidnum, -1]['BidPrice1'], 2)
        if abs(sig) > self.sig_thres:
            if mktwide <= self.wide * abs(sigscore):
                return sigscore
            else:
                self.write_log(f"SIGNAL\t{round(self.tickArray.Tickset[pidnum, -1]['mid'], 2)} "
                                f"{round(sig, 2)} {round(mktwide, 2)}")
                return 0
        else:
            return 0

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.
        """
        self.bg.update_bar(bar)

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status != Status.NOTTRADED:
            self.write_log(f"order: {order.orderid}\t{order.price}\t{order.volume}\t{order.direction}\t" \
                    f"{self.tickArray.Tickset[0, -1]['BidPrice1']}\t{self.tickArray.Tickset[0, -1]['AskPrice1']}")
            if self.scorelist['stopscore'] != 0:
                self.write_log(f'止损停利\t{self.scorelist["stopscore"]}')

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        super().on_trade(trade)

        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """
        Callback of stop order update.
        """
        pass
