import numpy as np
import pandas as pd
import warnings
import matplotlib.pyplot as plt
import matplotlib
from datetime import datetime, timedelta, time

matplotlib.use('TkAgg')
warnings.filterwarnings("ignore")

# 常量定义
V0 = 100
DELTA = 0.001
TRADING_PERIODS = [
    (time(9, 30), time(10, 0)),   # 时段0
    (time(10, 0), time(10, 30)),  # 时段1
    (time(10, 30), time(11, 0)),  # 时段2
    (time(11, 0), time(11, 30)),  # 时段3
    (time(13, 0), time(13, 30)),  # 时段4
    (time(13, 30), time(14, 0)),  # 时段5
    (time(14, 0), time(14, 30)),  # 时段6
    (time(14, 30), time(14, 57))  # 时段7
]
N_PERIODS = len(TRADING_PERIODS)
SPREAD_LEVELS = [round(DELTA * i, 4) for i in range(1, 5)]  # [0.001, 0.002, 0.003, 0.004]
EVENT_TYPES = ['bid_best', 'bid_higher', 'ask_best', 'ask_lower']


def assign_period(t):
    """分配时间到交易时段,时间段索引（0-7）"""
    for i, (start, end) in enumerate(TRADING_PERIODS):
        if start <= t < end:
            return i
    if t >= time(14, 57):
        return N_PERIODS - 1
    return None


def Lee_Ready_Direction(df_tick):
    df_tick = df_tick.drop_duplicates()
    df_tick['Trade_Direction'] = np.nan
    df_tick['Trade_Volume'] = df_tick['Volume'].diff()
    df_tick['Trade_Volume'].iloc[0] = df_tick['Volume'].iloc[0]
    df_tick['Trade_Value'] = df_tick['TotalValueTraded'].diff()
    df_tick['Trade_Value'].iloc[0] = df_tick['TotalValueTraded'].iloc[0]

    df_tick['AvgTradePrice'] = df_tick['Trade_Value'] / df_tick['Trade_Volume']
    # 检查条件并更新列的值
    mask_1 = (df_tick['AvgTradePrice'] > (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2)
    mask_2 = (df_tick['AvgTradePrice'] < (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2)
    mask_3 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (
                df_tick['AvgTradePrice'] > df_tick['AvgTradePrice'].shift(1))
    mask_4 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (
                df_tick['AvgTradePrice'] < df_tick['AvgTradePrice'].shift(1))
    mask_5 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (
                df_tick['AvgTradePrice'] == df_tick['AvgTradePrice'].shift(1))

    df_tick.loc[mask_1, 'Trade_Direction'] = 1
    df_tick.loc[mask_2, 'Trade_Direction'] = -1
    df_tick.loc[mask_3, 'Trade_Direction'] = 1
    df_tick.loc[mask_4, 'Trade_Direction'] = -1

    # 修复 mask_5 的赋值逻辑：每行独立向前查找最近的非NaN值
    if mask_5.any():  # 确保有满足条件的行
        # 对每一行满足mask_5的位置，向前查找最近的非NaN值
        for idx in df_tick[mask_5].index:
            if idx > 0:  # 跳过第一行，因为无法向前查找
                prev_values = df_tick.loc[:idx - 1, 'Trade_Direction']
                if not prev_values.dropna().empty:
                    df_tick.loc[idx, 'Trade_Direction'] = prev_values.dropna().iloc[-1]

    return df_tick

def create_time_buckets():
    """创建时间桶（每15秒）"""
    buckets = []
    # 处理所有交易时段
    for period in TRADING_PERIODS:
        start, end = period
        current = datetime.combine(datetime.today(), start)
        end_dt = datetime.combine(datetime.today(), end)

        while current < end_dt:
            buckets.append(current.time())
            current += timedelta(seconds=15)

    return buckets


def discretize_spread(spread_val):
    """离散化价差（四舍五入到最小变动单位的倍数）"""
    # 将价差四舍五入到最接近的0.001倍数
    discrete_val = round(spread_val, 4)
    return discrete_val


def simulate_trade_events(df, V0=1000):
    """模拟成交事件"""
    # 识别价差变动事件
    df['spread_chg_flag'] = df['spread_chg'] != 0
    spread_chg_indices = df[df['spread_chg_flag']].index.tolist()

    # 存储模拟结果
    results = []

    # 处理每个价差变动区间
    for i in range(len(spread_chg_indices) - 1):
        start_idx = spread_chg_indices[i]
        end_idx = spread_chg_indices[i + 1]

        # 获取区间数据
        interval_df = df.loc[start_idx:end_idx]
        if interval_df.empty:
            continue

        # 获取区间开始时的订单簿状态
        start_row = interval_df.iloc[0]
        bid_vol = start_row['BidVol1']
        ask_vol = start_row['AskVol1']
        spread_val = start_row['spread']
        bid_price1 = start_row['BidPrice1']
        ask_price1 = start_row['AskPrice1']

        # 离散化价差
        discrete_spread = discretize_spread(spread_val)
        start_time = interval_df.iloc[0]['timestamp_str']
        end_time = interval_df.iloc[-1]['timestamp_str']
        # 将 time 对象转换为秒数（从午夜开始的秒数）
        start_seconds = start_time.hour * 3600 + start_time.minute * 60 + start_time.second + start_time.microsecond / 1e6
        end_seconds = end_time.hour * 3600 + end_time.minute * 60 + end_time.second + end_time.microsecond / 1e6
        duration_seconds = end_seconds - start_seconds

        # 计算区间内的主动买卖量（考虑撤单比率修正因子）
        cancellation_ratio_factor = 0  # 撤单率
        buy_volume = interval_df[(interval_df['Trade_Direction'] == 1) & (interval_df['AvgTradePrice'] >= ask_price1)]['Trade_Volume'].sum() * (1 - cancellation_ratio_factor)
        sell_volume = interval_df[(interval_df['Trade_Direction'] == -1) & (interval_df['AvgTradePrice'] <= bid_price1)]['Trade_Volume'].sum() * (1 - cancellation_ratio_factor)

        # 模拟成交事件
        # 1. 最优买价订单成交
        bid_best_trade = 1 if sell_volume > (bid_vol + V0) else 0

        # 2. 高于最优买价一个单位的订单成交
        bid_higher_trade = 1 if sell_volume > V0 else 0

        # 3. 最优卖价订单成交
        ask_best_trade = 1 if buy_volume > (ask_vol + V0) else 0

        # 4. 低于最优卖价一个单位的订单成交
        ask_lower_trade = 1 if buy_volume > V0 else 0

        # 记录结果
        results.append({
            'start_time': interval_df.iloc[0]['timestamp_str'],
            'end_time': interval_df.iloc[-1]['timestamp_str'],
            'period_idx': assign_period(start_time),
            'duration_seconds': duration_seconds,
            'discrete_spread': discrete_spread,
            'bid_best_trade': bid_best_trade,
            'bid_higher_trade': bid_higher_trade,
            'ask_best_trade': ask_best_trade,
            'ask_lower_trade': ask_lower_trade
        })

    return pd.DataFrame(results)


def assign_events_to_buckets(sim_results, buckets):
    """将模拟结果分配到时间桶和价差组合"""
    # 离散价差列表
    spread_levels = [round(DELTA * i, 4) for i in range(1, 5)]  # 0.001, 0.002, 0.003, 0.004
    event_types = ['bid_best', 'bid_higher', 'ask_best', 'ask_lower']

    # 初始化结果字典
    bucket_data = {}
    for spread in spread_levels:
        for event_type in event_types:
            bucket_data[(spread, event_type)] = np.zeros(len(buckets) - 1)

    # 初始化时间段索引列表
    period_indices = []

    # 处理每个时间桶
    for i in range(len(buckets) - 1):
        bucket_start = buckets[i]
        bucket_end = buckets[i + 1]

        # 确定时间段索引
        period_idx = assign_period(bucket_start)
        period_indices.append(period_idx)

        # 检查所有模拟事件是否在当前桶内
        for _, row in sim_results.iterrows():
            event_time = row['end_time']

            if bucket_start <= event_time < bucket_end:
                spread_val = row['discrete_spread']

                # 只处理在spread_levels范围内的价差
                if spread_val in spread_levels:
                    # 处理四种事件类型
                    for event_type in event_types:
                        if row[event_type + '_trade'] == 1:
                            bucket_data[(spread_val, event_type)][i] += 1

    return bucket_data, period_indices


def estimate_poisson_intensity(bucket_data, period_indices,sim_results):
    """估计泊松过程强度（事件数/15秒）"""
    # 初始化结果字典
    intensity_estimates = {}
    # 首先计算每个时段+价差组合的总持续时间（秒）
    period_spread_durations = {}
    for _, row in sim_results.iterrows():
        key = (row['period_idx'], row['discrete_spread'])
        period_spread_durations[key] = period_spread_durations.get(key, 0) + row['duration_seconds']

    # 离散价差列表
    spread_levels = [round(DELTA * i, 4) for i in range(1, 5)]
    event_types = ['bid_best', 'bid_higher', 'ask_best', 'ask_lower']

    for spread in spread_levels:
        spread_estimates = {}

        for event_type in event_types:
            # 获取该事件类型的事件序列
            events = bucket_data[(spread, event_type)]

            # 初始化每个时间段的强度估计
            period_intensities = np.zeros(N_PERIODS)

            # 统计每个时间段内的事件总数
            for period_idx in range(N_PERIODS):
                # 计算该时段该价差的总持续时间（转换为15秒桶数）
                total_duration_seconds = period_spread_durations.get((period_idx, spread), 0)
                bucket_count = total_duration_seconds / 15  # 转换为15秒桶

                # 找到属于该时间段的所有桶
                mask = [idx == period_idx for idx in period_indices]
                event_count = np.sum(events[mask])

                # 计算强度
                intensity = event_count / bucket_count if bucket_count > 0 else 0
                period_intensities[period_idx] = intensity

            spread_estimates[event_type] = period_intensities

        intensity_estimates[spread] = spread_estimates

    return intensity_estimates


def params_trade_esti_main():
    # 读取数据
    trade_path = 'E:\\Internship\\huatai_fin_inno\\Optimal_Market_Making\\data\\ETF_trade\\'
    trade_date = '20250603'
    etf_trade_data = pd.read_parquet(trade_path + 'md_' + trade_date + '_udp_receiver_1_50072.parquet')

    # 转换时间格式并过滤
    etf_trade_data['timestamp_str'] = pd.to_datetime(etf_trade_data['timestamp_str'], format='%H:%M:%S.%f').dt.time

    # 只保留交易时段数据
    time_condition = False
    for start, end in TRADING_PERIODS:
        time_condition |= ((etf_trade_data['timestamp_str'] >= start) & (etf_trade_data['timestamp_str'] < end))

    etf_trade_data = etf_trade_data[time_condition].reset_index(drop=True)

    # 计算中间价和价差
    etf_trade_data['mid_price'] = (etf_trade_data['AskPrice1'] + etf_trade_data['BidPrice1']) / 2
    etf_trade_data = Lee_Ready_Direction(etf_trade_data)
    etf_trade_data['spread'] = etf_trade_data['AskPrice1'] - etf_trade_data['BidPrice1']
    etf_trade_data['spread'] = etf_trade_data['spread'].round(4)
    etf_trade_data['spread_chg'] = etf_trade_data['spread'].diff()
    etf_trade_data['spread_chg'].iloc[0] = etf_trade_data['spread'].iloc[0]

    print(etf_trade_data)
    print(etf_trade_data.columns)
    print("Data loaded and preprocessed")
    print(f"Total records: {len(etf_trade_data)}")

    # 创建时间桶 (每15秒)
    all_buckets = create_time_buckets()
    n_buckets = len(all_buckets) - 1
    print(f"Created {n_buckets} time buckets")

    # 模拟成交事件
    sim_results = simulate_trade_events(etf_trade_data, V0)
    print(f"Trade events simulated: {len(sim_results)} intervals")

    # 将模拟结果分配到时间桶和价差组合
    bucket_data, period_indices = assign_events_to_buckets(sim_results, all_buckets)
    print("Events assigned to time buckets and spread levels")

    # 估计泊松过程强度
    intensity_estimates = estimate_poisson_intensity(bucket_data, period_indices,sim_results)

    # 打印估计结果
    print("\nPoisson Intensity Estimates (events per second):")
    spread_levels = [round(DELTA * i, 4) for i in range(1, 5)]
    event_types = ['bid_best', 'bid_higher', 'ask_best', 'ask_lower']

    for spread in spread_levels:
        print(f"\nSpread = {spread:.4f}:")
        for event_type in event_types:
            intensities = intensity_estimates[spread][event_type]
            print(f"  {event_type}:")
            for i in range(N_PERIODS):
                start, end = TRADING_PERIODS[i]
                print(f"    {start}-{end}: λ = {intensities[i]:.6f}")

    # 可视化结果
    plt.figure(figsize=(20, 16))
    plot_idx = 1

    for spread in spread_levels:
        spread_estimates = intensity_estimates[spread]

        for event_type in event_types:
            intensities = spread_estimates[event_type]

            # 创建时间段标签
            period_labels = [f"{start.strftime('%H:%M')}-{end.strftime('%H:%M')}"
                             for start, end in TRADING_PERIODS]

            plt.subplot(4, 4, plot_idx)
            plt.bar(period_labels, intensities, color='skyblue')
            plt.title(f'Spread = {spread:.4f}\n{event_type}')
            plt.xlabel('Trading Period')
            plt.ylabel('Intensity (events/sec)')
            plt.xticks(rotation=45, ha='right')
            plt.grid(True, alpha=0.3)

            plot_idx += 1

    plt.tight_layout()
    # plt.savefig(f'pics_testing\\poisson_intensity_by_spread_{trade_date}.png', dpi=300)
    plt.show()

    return intensity_estimates


if __name__ == '__main__':
    params_trade_estimation_results = params_trade_esti_main()
    print(params_trade_estimation_results[0.001])

