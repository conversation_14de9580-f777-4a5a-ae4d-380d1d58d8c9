#%% 
'''IP SETTING FOR INFLUXDB'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('DCE.dev')
import warnings
warnings.filterwarnings('ignore')


""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""


from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.reverseStrategy import ReverseStrategy

#from strategies.liquidityStrategy import LiquidityStrategy

from strategies.basisStrategy import BasisStrategy
import datetime
from vnpy.trader.constant import Interval
 
#%%

#maker='m2203.DCE'
#refer='m2201.DCE'

#makers=['hc2202.SHFE','hc2203.SHFE','hc2204.SHFE']
#refer='hc2201.SHFE'
makers=['pp2202.DCE','pp2203.DCE','pp2204.DCE']#,'pp2206.DCE']#,'l2203.DCE','l2204.DCE','l2206.DCE']
refer='pp2201.DCE'
all_ins = makers.copy()
all_ins.extend([refer]) 
#edges={'l2202.DCE':8,'l2203.DCE':5,'l2204.DCE':6,'l2206.DCE':8}
#lots={'l2202.DCE':4,'l2203.DCE':3,'l2204.DCE':2,'l2206.DCE':1}
edges={'pp2202.DCE':8,'pp2203.DCE':8,'pp2204.DCE':8}#,'pp2206.DCE':8}
lots={'pp2202.DCE':4,'pp2203.DCE':3,'pp2204.DCE':2}#,'pp2206.DCE':1}
#maker='pp2112.DCE'
#refer='pp2201.DCE'
#maker='c2111.DCE'
#refer='c2201.DCE'
# maker='CF203.CZCE'
# refer='CF201.CZCE'

maker='m2203.DCE'
refer='m2201.DCE'


multiplier=10


engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,Countertransaction=True,duty=False,save_result=True,refer_test=True) #Counter代表是否只统计对价成交，duty代表是否统计义务

'''
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔


    start=datetime.datetime(2021, 10, 12, 21, 00), # 开始时间
    end=datetime.datetime(2021, 10, 13, 15, 00), # 结束时间
>>>>>>> c7c3b495c8942d076df3377a970105966e96d354
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: 1,refer: 1}, # 一个tick大小
    capital=1_000_000, # 初始资金
)'''

engine.set_parameters(
    vt_symbols=all_ins, # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime.datetime(2021, 11, 1, 21, 00), # 开始时间
    end=datetime.datetime(2021, 11, 1, 22, 00), # 结束时间
    rates={x:0 for x in all_ins}, # 手续费率
    slippages={x:0 for x in all_ins}, # 滑点
    sizes={x: multiplier for x in all_ins}, # 合约规模
    priceticks={x: 1 for x in all_ins}, # 一个tick大小
    capital=1_000_000, # 初始资金
)
# 添加回测策略，并修改内部参数
engine.clear_data()
<<<<<<< HEAD
engine.add_strategy(LiquidityStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edges,'maxPos':30})
 
#%%
=======

<<<<<<< HEAD
# engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})

engine.add_strategy(BasisStrategy, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':4,'maxPos':3,'alpha':0.02,'gamma':0.3})
=======
#engine.add_strategy(ReverseStrategy, {'lots': 10,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':2,'edge':3,'eta':1,'gamma':1,'validVolume':100,'safeVolume':150,'maxPos':50,'loss':3000,
                                      #'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':True,'useReverseOrderTargetPosition':True,'reverseOrderTargetPosition':0,
                                      #'useSVMSingal':True}) #m2203
                                      
#engine.add_strategy(ReverseStrategy, {'lots': 5,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                     # 'minEdge':4,'edge':8,'eta':1,'gamma':0.5,'validVolume':30,'safeVolume':40,'maxPos':50,'loss':3000,'stop':8,
                                      #'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':True,'useReverseOrderTargetPosition':True,'reverseOrderTargetPosition':0,
                                      #'useSVMSingal':False}) #eb2111

#engine.add_strategy(ReverseStrategy, {'lots': 15,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':1,'edge':3,'eta':1,'gamma':1,'validVolume':200,'safeVolume':240,'maxPos':60,'loss':3000,'stop':3,
                                      #'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':True ,'useReverseOrderTargetPosition':True,'reverseOrderTargetPosition':0,'fixedFader':0,
                                      #'useSVMSingal':True}) #c2111
                                      
#engine.add_strategy(ReverseStrategy, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':1,'edge':4,'eta':0.5,'gamma':0.5,'validVolume':3,'safeVolume':5,'maxPos':6,'loss':5000}) #l2204

#engine.add_strategy(ReverseStrategy, {'lots': 25,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':9,'edge':9,'eta':0.6,'gamma':0.1,'validVolume':120,'safeVolume':150,'maxPos':100,'loss':5000,
                                      #'referHedgeFlag':True,'neverStopFlag':True,'useReverseOrder':True,'useReverseOrderTargetPosition':True,'reverseOrderTargetPosition':0,
                                      #'useSVMSingal':True}) #hc2111

#engine.add_strategy(ReverseStrategy, {'lots': 25,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':9,'edge':9,'eta':0.6,'gamma':0.1,'validVolume':150,'safeVolume':200,'maxPos':50,'loss':3000,
                                      #'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':True,'useReverseOrderTargetPosition':True,'reverseOrderTargetPosition':0}) #rb2202
#engine.add_strategy(ReverseStrategy, {'lots': 2,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':6,'edge':9,'eta':1,'gamma':0.3,'validVolume':12,'safeVolume':15,'maxPos':8,'loss':3000}) #jc2112
                                      
#engine.add_strategy(ReverseStrategy, {'lots': 10,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':8,'edge':8,'eta':1,'gamma':0.8,'validVolume':40,'safeVolume':60,'maxPos':35,'loss':6600,'stop':8,
                                      #'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':False,'useReverseOrderTargetPosition':False,'reverseOrderTargetPosition':0,'fixedFader':0,
                                      #'useSVMSingal':False,'tradingOffsetSignal':False}) #pp2112             


#engine.add_strategy(ReverseStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':14,'edge':14,'eta':1,'gamma':1,'validVolume':60,'safeVolume':60,'maxPos':25,'loss':3000,'stop':3,
                                       #'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':False,'useReverseOrderTargetPosition':False,'reverseOrderTargetPosition':0,'fixedFader':0,
                                      #'useSVMSingal':False,'tradingOffsetSignal':False,'agressiveTradingFade':False}) #pp2112            

#engine.add_strategy(ReverseStrategy, {'lots': 3,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      #'minEdge':8,'edge':9,'eta':1,'gamma':0.3,'validVolume':12,'safeVolume':15,'maxPos':10,'loss':3000,'stop':4,
                                      # 'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':False,'useReverseOrderTargetPosition':False,'reverseOrderTargetPosition':0,'fixedFader':0,
                                      #'useSVMSingal':False,'tradingOffsetSignal':False,'agressiveTradingFade':False}) #pp2112 
#engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7,'maxPos':20})

#engine.add_strategy(BasisStrategy, {'lots': 25,'makers':makers,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':9,'alpha':0.01,'maxPos':50})

engine.add_strategy(BasisStrategy, {'deltaRiskLimit':0, 'lots': lots,'makers':makers,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edges,'alpha':0.03,'maxPos':50})

>>>>>>> 47838c9f32cceab1297b328afb7bd0447e1ca30a

>>>>>>> c7c3b495c8942d076df3377a970105966e96d354
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
<<<<<<< HEAD
#duty = engine.duty_statistics()
#engine.show_tick_chart() # 显示图表
engine.duty_statistics()
 
 #%%
import pandas as pd
import numpy as np
from datetime import timedelta
=======
<<<<<<< HEAD
=======
#duty = engine.duty_statistics()
#engine.show_tick_chart() # 显示图表
#engine.duty_statistics()
>>>>>>> 47838c9f32cceab1297b328afb7bd0447e1ca30a

from vnpy.trader.analysis import Analysis

analysis = Analysis(engine)
df = analysis.pnl_plot(savefig = True) 
#%%
import pandas as pd
import numpy as np
from datetime import timedelta

trades = pd.DataFrame([{'time':(engine.trades[x].datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'insid':engine.trades[x].symbol+'.'+engine.trades[x].exchange.value,'comments':engine.trades[x].comments,
           'basePrice':engine.trades[x].basePrice,'midPrice':engine.trades[x].midPrice
           } for x in engine.trades])
all_trades={}
for iid in all_ins:
    trade = trades[trades.insid==iid].copy()
    trade['net'] = (trade.volume*trade.direction).cumsum()
    
    trade['spread'] = trade['direction']*(trade['midPrice']-trade['price'])
    trade['basis'] = trade['midPrice'] - trade['basePrice']
    
    cash_base = (trade['direction']*(-1)*trade['volume']*trade['basePrice'])*multiplier
    cash_basis = (trade['direction']*(-1)*trade['volume']*trade['basis'])*multiplier
    
    cash = (trade['direction']*(-1)*trade['volume']*trade['price'])*multiplier
    cashCum = np.cumsum(cash)
    
    cashCum_base = np.cumsum(cash_base)
    cashCum_basis = np.cumsum(cash_basis)
    
    trade['pnl'] = cashCum + trade.net*trade.midPrice*multiplier
    trade['delta_pnl'] = cashCum_base + trade.net*trade.basePrice*multiplier
    trade['basis_pnl'] = cashCum_basis + trade.net*trade.basis*multiplier
    trade['spread_pnl'] = np.cumsum(trade['spread']*trade.volume)*multiplier
    
    all_trades[iid] = trade
    
    
    
    
    
    
    
    
np.sum([all_trades[iid].iloc[-1]['pnl'] for iid in all_ins])
#orders = pd.DataFrame([{'time':(x.datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           #'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
           #} for x in engine.get_all_orders()])
#反手率
# trades[((trades.comments!='MM'))].volume.sum()/(trades[((trades.comments=='MM')&(trades.direction==-1))].volume.sum()+trades[((trades.comments=='MM')&(trades.direction==1))].volume.sum())
#((trades[trades.symbol=='pp2110'].price*trades[trades.symbol=='pp2110'].volume*trades[trades.symbol=='pp2110'].direction).sum())/((trades[trades.symbol=='pp2110'].volume*trades[trades.symbol=='pp2110'].direction).sum())
#((trades[trades.symbol=='pp2111'].price*trades[trades.symbol=='pp2111'].volume*trades[trades.symbol=='pp2111'].direction).sum())/((trades[trades.symbol=='pp2111'].volume*trades[trades.symbol=='pp2111'].direction).sum())
mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
        'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
        'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
        'last_price','volume','turnover']]
mkt['time'] = mkt['datetime'].apply(lambda x :(x+timedelta(hours=8)))
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))



#%%


import pandas as pd
import numpy as np
from datetime import timedelta

>>>>>>> c7c3b495c8942d076df3377a970105966e96d354
trades = pd.DataFrame([{'time':(engine.trades[x].datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments
           } for x in engine.trades])
trades['net'] = (trades.volume*trades.direction).cumsum()
cash = (trades['direction']*(-1)*trades['volume']*trades['price'])*multiplier
cashCum = np.cumsum(cash)
trades['pnl'] = cashCum + trades.net*trades.price*multiplier
orders = pd.DataFrame([{'time':(x.datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
           } for x in engine.get_all_orders()])
#反手率
# trades[((trades.comments!='MM'))].volume.sum()/(trades[((trades.comments=='MM')&(trades.direction==-1))].volume.sum()+trades[((trades.comments=='MM')&(trades.direction==1))].volume.sum())
#((trades[trades.symbol=='pp2110'].price*trades[trades.symbol=='pp2110'].volume*trades[trades.symbol=='pp2110'].direction).sum())/((trades[trades.symbol=='pp2110'].volume*trades[trades.symbol=='pp2110'].direction).sum())
#((trades[trades.symbol=='pp2111'].price*trades[trades.symbol=='pp2111'].volume*trades[trades.symbol=='pp2111'].direction).sum())/((trades[trades.symbol=='pp2111'].volume*trades[trades.symbol=='pp2111'].direction).sum())
 
#((trades[trades.symbol=='pp2201'].price*trades[trades.symbol=='pp2201'].volume*trades[trades.symbol=='pp2201'].direction).sum())/((trades[trades.symbol=='pp2201'].volume*trades[trades.symbol=='pp2201'].direction).sum())
 
#%%
mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
           'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
           'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
           'last_price','volume','turnover']]
mkt['datetime'] = mkt['datetime'].apply(lambda x : x+timedelta(hours=8))
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))
 
trade_split = pd.DataFrame(engine.trade_split,columns=['time','vt_symbol','trade']) #成交拆分
trade_split = trade_split[trade_split.vt_symbol==maker]
 
#%%
 
#shfe#%%
 
import pandas as pd
tradesDF = pd.DataFrame([])
stats=[]
date = '2021-06-25' #第一天晚上
start = datetime.datetime(2021, 6, 28) #第一天白天
end = datetime.datetime(2021, 8,2) #最后一天白天
delta = datetime.timedelta(days=1)
day = start
weekend=set([5,6])
date_formate = "%Y-%m-%d"
while day<=end:
    if day.weekday() in weekend:
        day+=delta
        continue
    else:
        beginStr = date+'T21:00:00.0Z'
        date = day.strftime(date_formate)
        endStr = date+'T15:00:00.0Z'
        
        day+=delta
        
        maker='hc2202.DCE'
        refer='hc2110.DCE'
        engine = BacktestingEngine(alwaysInQueueHeadFlag=True,cancelFailProba=0.1)
        engine.set_parameters(
            vt_symbols=[maker,refer], # 回测品种
            interval=Interval.TICK, # 回测模式的数据间隔
            start = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ'), # 开始时间
            end= datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ'), # 结束时间
            rates={maker: 0,refer: 0}, # 手续费率
            slippages={maker: 0,refer: 0}, # 滑点
            sizes={maker: 10,refer: 10}, # 合约规模
            priceticks={maker: 1,refer: 1}, # 一个tick大小
            capital=1_000_000, # 初始资金
        )
        # 添加回测策略，并修改内部参数
        engine.clear_data()
        engine.add_strategy(ReverseStrategy, {'lots': 25,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      'minEdge':9,'edge':9,'eta':1,'gamma':0.1,'validVolume':100,'safeVolume':150,'maxPos':50,'loss':3000,
                                      'referHedgeFlag':False,'neverStopFlag':False,'useReverseOrder':True,'useReverseOrderTargetPosition':True,'reverseOrderTargetPosition':0}) #hc2202
      
        engine.load_data() # 加载历史数据
        engine.run_backtesting() # 进行回测
        df = engine.calculate_tick_result() # 计算逐日盯市盈亏
        stat = engine.calculate_tick_statistics() # 统计日度策略指标
        stats.append(stat)
        trades = pd.DataFrame([{'time':(engine.trades[x].datetime+datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':engine.trades[x].price,
           'direction':engine.trades[x].direction.value,'volume':engine.trades[x].volume} for x in engine.trades])
        tradesDF = pd.concat([tradesDF,trades])
        
dfStats = pd.DataFrame(stats)
dfStats.total_net_pnl.sum()
dfStats.total_trade_count.sum()