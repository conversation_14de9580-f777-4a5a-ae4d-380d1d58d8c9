# -*- coding: utf-8 -*-
"""
Created on Mon Jul 12 17:20:53 2021
 
@author: humy2
"""

# 多路优选的规则定义：
# 1、新档行情和老档行情同时接收数据,相对于上一笔行情,先到的行情先更新
# 2、判断新行情的条件:
# 2.1 跟上一笔行情相比,exchange_t增加
# 2.2 新的行情和上一笔行情exchange_t相同,但上一笔行情source=1,新行情source=2 [有新二档行情则更新,利用更多信息]

#%%
from datetime import timedelta
from influxdb import InfluxDBClient
client_old = InfluxDBClient('202.0.3.200',8989,'reader','iamreader','testbase') #郑商所老行情
client_new = InfluxDBClient('202.0.3.200',8989,'reader','iamreader','testmd') #郑商所新行情

import datetime
beginStr = '2021-12-22T14:00:00.0Z'
endStr = '2021-12-22T15:00:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
 
result_old = client_old.query("select * from zce_md  where time >= %d and insid_md='CF203' and time <= %d;"%(begin,end)) 
result_new = client_new.query("select * from test_md where time >= %d and insid_md='CF203' and time <= %d;"%(begin,end)) 

#%%
points = result_old.get_points()
l=[]
for d in points:
    l.append(d)
print(len(l))
    
import pandas as pd
import datetime
df = pd.DataFrame(l)
df['datetime']=df.time.apply(lambda x:datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ'))+timedelta(hours=8)
df.index = df['datetime']
md_old = df
md_old['source_type'] = '1'

points = result_new.get_points()
l=[]
for d in points:
    l.append(d)
print(len(l))
    
import pandas as pd
import datetime
df = pd.DataFrame(l)
df['datetime']=df.time.apply(lambda x:datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ'))+timedelta(hours=8)
df.index = df['datetime']
md_new = df
md_new['exchange_t'] = md_new.exchange_t_tag.apply(lambda x : int(x))

#%%
df_merge = pd.concat([md_new,md_old],keys='time')
df = df_merge.sort_values(by='time')
df = df.reset_index(drop=True)

result = []
for i in df.index:
    if i == 0: #第一条接收的数据直接存储
        result.append(df.loc[i])
    else:
        current_exchange_t = df.loc[i,'exchange_t']
        last_exchange_t = df.loc[i-1,'exchange_t']
        current_source = df.loc[i,'source_type']
        last_source = df.loc[i-1,'source_type']        
        if current_exchange_t > last_exchange_t:
            result.append(df.loc[i])
        elif current_exchange_t == last_exchange_t and current_source == '2' and last_source == '1': #新二档行情的更新,也进行存储
            result.append(df.loc[i])
        else:
            continue

df_result = pd.DataFrame(result)







