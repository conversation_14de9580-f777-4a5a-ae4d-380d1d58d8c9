# 订单簿特征计算公式

本文档列出了订单簿数据分析中使用的各种特征计算公式。这些公式用于从订单簿中提取与时间无关的特征，可用于金融市场分析、算法交易和机器学习模型开发。

## 基本概念

在金融市场中，订单簿记录了市场中所有买入和卖出订单。订单簿的深度表示了价格档位的数量。例如：

- 卖方订单（Ask）：按价格从低到高排列
- 买方订单（Bid）：按价格从高到低排列

## 价格相关特征

### 1. 价格差异 (Price Spread)

第i级价格差异是指第i级卖出价格与买入价格的差值：

$$\text{PriceSpread}_i = \text{AskPrice}_i - \text{BidPrice}_i$$

### 2. 中间价格 (Mid Price)

第i级中间价格是指第i级卖出价格和买入价格的平均值：

$$\text{MidPrice}_i = \frac{\text{AskPrice}_i + \text{BidPrice}_i}{2}$$

### 3. 买入价格步长 (Bid Step)

第i级买入价格步长是指第i级与第i+1级买入价格的绝对差值：

$$\text{BidStep}_i = |\text{BidPrice}_i - \text{BidPrice}_{i+1}|$$

### 4. 卖出价格步长 (Ask Step)

第i级卖出价格步长是指第i级与第i+1级卖出价格的绝对差值：

$$\text{AskStep}_i = |\text{AskPrice}_i - \text{AskPrice}_{i+1}|$$

### 5. 平均卖出价格 (Mean Ask)

平均卖出价格是指所有有效档位的卖出价格的平均值：

$$\text{MeanAsk} = \frac{1}{n} \sum_{i=1}^{n} \text{AskPrice}_i$$

其中n是有效的订单簿深度（有值的档位数量）。

### 6. 平均买入价格 (Mean Bid)

平均买入价格是指所有有效档位的买入价格的平均值：

$$\text{MeanBid} = \frac{1}{n} \sum_{i=1}^{n} \text{BidPrice}_i$$

### 7. 累计价格差异 (Accumulated Price Spread)

累计价格差异是所有有效档位的价格差异之和：

$$\text{AccumulatedPriceSpread} = \sum_{i=1}^{n} (\text{AskPrice}_i - \text{BidPrice}_i)$$

## 成交量相关特征

### 1. 成交量差异 (Volume Spread)

第i级成交量差异是指第i级卖出量与买入量的差值：

$$\text{VolumeSpread}_i = \text{AskVolume}_i - \text{BidVolume}_i$$

### 2. 平均卖出量 (Mean Ask Volume)

平均卖出量是指所有有效档位的卖出量的平均值：

$$\text{MeanAskVolume} = \frac{1}{n} \sum_{i=1}^{n} \text{AskVolume}_i$$

### 3. 平均买入量 (Mean Bid Volume)

平均买入量是指所有有效档位的买入量的平均值：

$$\text{MeanBidVolume} = \frac{1}{n} \sum_{i=1}^{n} \text{BidVolume}_i$$

### 4. 累计成交量差异 (Accumulated Volume Spread)

累计成交量差异是所有有效档位的成交量差异之和：

$$\text{AccumulatedVolumeSpread} = \sum_{i=1}^{n} (\text{AskVolume}_i - \text{BidVolume}_i)$$

## 公式应用说明

1. 所有公式中，下标i表示订单簿中的深度级别，通常从1开始
2. 有效深度n是指订单簿中实际有值的深度级别数量
3. 当某一深度级别没有相应的数据时，该级别及更深的级别将不被计算
4. 所有计算结果都通过Cell类型返回，可以处理缺失值的情况

这些特征可以单独使用，也可以组合使用，为金融市场分析和交易策略提供重要依据。 