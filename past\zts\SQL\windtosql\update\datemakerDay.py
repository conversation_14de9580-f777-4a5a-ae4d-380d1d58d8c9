# -*- coding:utf-8 -*-

from WindPy import w
import pyodbc
import datetime
import pandas as pd

w.start()


def datemaker(table, conn, cursor, indexnum):
    sqltime = "SELECT a.* FROM %s a WHERE DateTime =(SELECT max(DateTime) " \
              "FROM %s)" % (table, table)
    lasttime = pd.read_sql(sqltime, conn, index_col=None, coerce_float=True, params=None, parse_dates=None,
                           columns=None, chunksize=None)
    lastday = lasttime.ix[0, 'DateTime']

    print(u"\n***数据库中最新交易日数据为 %s ***" % (str(lastday)))

    now = datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d")
    dt = datetime.datetime.strptime("%s  15:31:00" % now, "%Y-%m-%d %H:%M:%S")
    num = len(lasttime[lasttime['DateTime'] == lastday])

    if now == lastday and num >= indexnum:
        print(u"\n***数据已是最新")
        exit()

    if num < indexnum:
        beginDate = datetime.datetime.strptime("%s  09:00:00" % lastday, "%Y-%m-%d %H:%M:%S")
        sqldelete = "DELETE FROM %s WHERE DateTime = (?)" % table
        sqltuplede = (lasttime.ix[0, 'DateTime'],)
        cursor.execute(sqldelete, sqltuplede)
        conn.commit()
        print(u"\n*** 最新交易日数据不全： %s 时刻的数据有 %s 条***" % ("15:00:00", num))
    else:
        beginDate = datetime.datetime.strptime("%s  09:00:00" % lastday, "%Y-%m-%d %H:%M:%S") \
                    + datetime.timedelta(days=1)

    if datetime.datetime.now() < dt:
        print(u"\n***当前交易日 %s 未结束，数据更新至上一交易日" % (datetime.datetime.now()))
        dt = datetime.datetime.strptime("%s  15:31:00" % now, "%Y-%m-%d %H:%M:%S") - datetime.timedelta(days=1)

    if beginDate > dt:
        print(u"\n***数据已是最新")
        exit()

    print(u"\n***更新从 %s ---- % s 的数据***" % (str(beginDate), str(dt)))

    return dt, beginDate, lastday
