# -*- coding: utf-8 -*-
"""
Created on Tue Sep  7 14:42:36 2021

@author: yihw
"""

from influxdb import InfluxDBClient
import datetime
import time
import numpy as np
import pandas as pd
# from OmmDatabase import OmmDatabase

import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False    
# import re
from enum import Enum
#%%
class order_status(Enum):
    pending_add = 0
    pending_delete = 1
    exchange_order = 2
    partial_traded = 3
    all_traded = 4
    deleted = 5
    over_flow = 8
    cancelled = 9
#%%
client = InfluxDBClient('10.17.88.168',9001,'reader','reader','testbase') # 东坝机房


#%%
beginStr = '2021-10-12T20:50:00.0Z'
endStr = '2021-10-13T15:10:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000

exchange = 'dce'

result_order = client.query("select * from "+exchange+"_future_order where time >= %d and time <= %d;"%(begin,end)) 
# result_quote = client.query("select * from "+exchange+"_future_quote where time >= %d and time <= %d;"%(begin,end))
# result_trade = client.query("select * from "+exchange+"_future_trade where time >= %d and time <= %d;"%(begin,end))

#%%
# iid_list = ['hc2111', 'hc2112', 'hc2201', 'hc2202', 'hc2203', 'rb2111', 'rb2112', 'rb2201', 'rb2202', 'rb2203']
future_csv = pd.read_csv('future_{}.csv'.format(exchange))
future_csv.index = future_csv['iid']
iid_list = list(future_csv.index)
# iid_list = ['pp2112']
try:
    points1 = result_order.get_points()
    l=[]
    for d in points1:
        l.append(d)
    print(len(l))
    df_order = pd.DataFrame(l)
except:
    df_order = pd.DataFrame()

try:
    points2 = result_quote.get_points()
    l=[]
    for d in points2:
        l.append(d)
    print(len(l))
    df_quote = pd.DataFrame(l)
except:
    df_quote = pd.DataFrame()

try:
    l=[]
    if exchange == 'shfe':
        measurement = 'test10'
    elif exchange == 'zce':
        measurement = 'testzhengzhou'
    elif exchange == 'dce':
        measurement = 'testdalian'
    else:
        print('你这交易所有问题啊')
    for iid in iid_list:
        result_mkt = client.query("select * from "+measurement+" where time >= %d and time <= %d and insid_md='{}';".format(iid)%(begin,end)) 
        points3 = result_mkt.get_points()
        for d in points3:
            l.append(d)
    print(len(l))
    df_mkt = pd.DataFrame(l)
except:
    df_mkt = pd.DataFrame()

try:
    points4 = result_trade.get_points()
    l=[]
    for d in points4:
        l.append(d)
    print(len(l))
    df_trade = pd.DataFrame(l)
except:
    df_trade = pd.DataFrame()

#%%
# 增加时间戳
try:
    df_order['UnixStamp'] = df_order['local_time']/10**9
    df_order.index = df_order['UnixStamp']
    df_order = df_order.sort_index()
except:
    df_order.columns = ['time', 'base_price', 'comments', 'error_id', 'error_message', 'insid',
       'internal_order_id', 'last_traded_time', 'last_update_time', 'level',
       'local_time', 'long_short', 'match_condition', 'note', 'open_close',
       'order_id', 'order_price', 'order_status', 'portfolio_id',
       'strategy_id', 'traded_price', 'volume_original_total', 'volume_total',
       'volume_traded', 'UnixStamp']

try:
    df_quote['UnixStamp'] = df_quote['local_time']/10**9
    df_quote.index = df_quote['UnixStamp']
    df_quote = df_quote.sort_index()
except:
    df_quote = pd.DataFrame(columns=['time', 'ask_error_id', 'ask_open_close', 'ask_order_price',
       'ask_order_status', 'ask_volume_original_total', 'ask_volume_total',
       'ask_volume_traded', 'base_ask_price', 'base_bid_price', 'bid_error_id',
       'bid_open_close', 'bid_order_price', 'bid_order_status',
       'bid_volume_original_total', 'bid_volume_total', 'bid_volume_traded',
       'cancel_time', 'error_id', 'error_message', 'insert_time', 'insid',
       'internal_quote_id', 'last_traded_time', 'level', 'local_time', 'note',
       'portfolio_id', 'prevInternal_quote_id', 'quote_id', 'quote_request_id',
       'quote_status', 'strategy_id', 'UnixStamp'])   
    
def str_to_timestamp(x):
    try:
        t = (datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ')+datetime.timedelta(hours=8)).timestamp()
    except:
        t = (datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%SZ')+datetime.timedelta(hours=8)).timestamp()
    return t

try:
    # df_mkt['UnixStamp'] = df_mkt['time'].apply(str_to_timestamp) # 此处加了8小时, 只能运行一次, 否则时间戳会出错
    df_mkt['UnixStamp'] = df_mkt['local_t']/10**9
    df_mkt.index = df_mkt['UnixStamp']
    df_mkt = df_mkt.sort_index()      
except:
    df_mkt = pd.DataFrame(columns=['time', 'a_p1', 'a_p2', 'a_p3', 'a_p4', 'a_p5', 'a_v1', 'a_v2', 'a_v3',
       'a_v4', 'a_v5', 'b_p1', 'b_p2', 'b_p3', 'b_p4', 'b_p5', 'b_v1', 'b_v2',
       'b_v3', 'b_v4', 'b_v5', 'exchange_t', 'insid_md', 'last_p', 'local_t',
       'lower_limit_p', 'preclose_p', 'presettle_p', 'turnover',
       'upper_limit_p', 'v', 'UnixStamp'])

try:
    df_trade['UnixStamp'] = df_trade['local_time']/10**9
    df_trade.index = df_trade['UnixStamp']
    df_trade = df_trade.sort_index()      
except:
    df_trade = pd.DataFrame(columns=['time', 'base_price', 'comments', 'delta', 'insid', 'internal_order_id',
       'is_deleted', 'local_time', 'long_short', 'note', 'open_close',
       'order_id', 'portfolio_id', 'strategy_id', 'trade_base_price',
       'trade_id', 'trade_price', 'traded_time', 'volatility',
       'volume_traded', 'UnixStamp'])


#%%
def get_pnl(df_order, df_mkt, iid_list):
    result_pnl = {}
    for iid in iid_list:
        t = time.time()
        
        data_order = df_order[df_order.insid == iid]
        data_order = data_order.to_dict('records')
        
        data_mkt = df_mkt[df_mkt.insid_md == iid]    
        data_mkt = data_mkt.to_dict('records')
            
        data_order = iter(data_order)
        data_mkt = iter(data_mkt)
        
        trade_alive = {}
        
        net = 0
        pnl = 0
        bidamount = 0
        askamount = 0
        
        details_list = []
        trade_list = []
                
        i = next(data_order, None)
        j = next(data_mkt, None)
        
        print(iid+'数据转化完毕', time.time()-t)
        t = time.time()
    
        while True:        
            if i == None and j == None:
                break
            
            elif i == None:
                UnixStamp = j['UnixStamp']
                askP = j['a_p1'] if j['a_p1'] != -1 else np.inf
                bidP = j['b_p1']
                lastP = j['last_p']
                fairP = np.median([askP, bidP, lastP])
                pnl = net*fairP + askamount - bidamount
                details_list.append((UnixStamp, net, pnl, fairP, iid))
                j = next(data_mkt, None)
            
            elif j == None: 
                if i['order_status'] == order_status.partial_traded.value or i['order_status'] == order_status.all_traded.value:
                    comments = i['comments']
                    v_t = i['volume_original_total']
                    UnixStamp = i['UnixStamp']
                    internal_order_id = i['internal_order_id']
                    if not internal_order_id in trade_alive.keys():
                        vol = i['volume_traded']
                    elif internal_order_id in trade_alive.keys():
                        k = trade_alive[internal_order_id]
                        if i['volume_traded'] <= k['volume_traded']:
                            i = next(data_order, None)
                            continue
                        else:
                            vol = i['volume_traded'] - k['volume_traded']
                    traded_price = i['order_price'] 
                    if i['long_short'] == 0:
                        bidamount += vol*traded_price
                        net += vol
                    elif i['long_short'] == 1:
                        askamount += vol*traded_price
                        net -= vol
                    trade_alive[internal_order_id] = i
                    trade_list.append((UnixStamp, internal_order_id, traded_price, vol, v_t, i['long_short'], comments, iid))
                    
                    pnl = net*traded_price + askamount - bidamount
                    details_list.append((UnixStamp, net, pnl, traded_price, iid))            
                i = next(data_order, None)
                
            elif i['UnixStamp'] <= j['UnixStamp']:
                if i['order_status'] == order_status.partial_traded.value or i['order_status'] == order_status.all_traded.value:
                    comments = i['comments']
                    v_t = i['volume_original_total']
                    UnixStamp = i['UnixStamp']
                    internal_order_id = i['internal_order_id']
                    if not internal_order_id in trade_alive.keys():
                        vol = i['volume_traded']
                    elif internal_order_id in trade_alive.keys():
                        k = trade_alive[internal_order_id]
                        if i['volume_traded'] <= k['volume_traded']:
                            i = next(data_order, None)
                            continue
                        else:
                            vol = i['volume_traded'] - k['volume_traded']
                    traded_price = i['order_price'] 
                    if i['long_short'] == 0:
                        bidamount += vol*traded_price
                        net += vol
                    elif i['long_short'] == 1:
                        askamount += vol*traded_price
                        net -= vol
                    trade_alive[internal_order_id] = i
                    trade_list.append((UnixStamp, internal_order_id, traded_price, vol, v_t, i['long_short'], comments, iid))
                    
                    pnl = net*traded_price + askamount - bidamount
                    details_list.append((UnixStamp, net, pnl, traded_price, iid))            
                i = next(data_order, None)
            
            elif i['UnixStamp'] > j['UnixStamp']:
                UnixStamp = j['UnixStamp']
                askP = j['a_p1'] if j['a_p1'] != -1 else np.inf
                bidP = j['b_p1']
                lastP = j['last_p']
                fairP = np.median([askP, bidP, lastP])
                pnl = net*fairP + askamount - bidamount
                details_list.append((UnixStamp, net, pnl, fairP, iid))
                j = next(data_mkt, None)
        print(iid+'net, pnl, trade_list 统计完毕', time.time()-t)
        
        details_list = pd.DataFrame(details_list)
        details_list.columns = ['UnixStamp', 'net', 'pnl', 'fair_price', 'iid']
        details_list.index = details_list['UnixStamp']
        
        try:
            trade_list = pd.DataFrame(trade_list)
            trade_list.columns = ['UnixStamp', 'internal_order_id', 'trade_price', 'vol', 'volume_original_total', 'long_short', 'comments', 'iid']
            trade_list.index = trade_list['UnixStamp']
        except:
            trade_list = pd.DataFrame(columns = ['UnixStamp', 'internal_order_id', 'trade_price', 'vol', 'volume_original_total', 'long_short', 'comments', 'iid'])
        
        result_pnl[iid] = {'iid':iid, 'pnl&net':details_list, 'trade_list':trade_list, 'trade':trade_alive}
    return result_pnl

#%%
def get_mkt_details(df_mkt, multiplier):
    df_mkt = df_mkt.to_dict('records')
    df_mkt1 = []
    row = df_mkt[0]
    v = row['v']
    turnover = row['turnover'] / multiplier  
    a_p1 = row['a_p1']     
    b_p1 = row['b_p1']
    row['dv'] = v
    row['dt'] = turnover
    row['avp'] = turnover/v if v != 0 else 0
    row['direction'] = 0
    df_mkt1.append(row)    
    for row in df_mkt[1:]:
        dv = row['v'] - v
        dt = row['turnover']/multiplier - turnover
        row['dv'] = dv
        row['dt'] = dt
        row['avp'] = dt/dv if dv != 0 else 0
        row['direction'] = -1+2*((row['avp']-b_p1)/(a_p1-b_p1)) if row['avp'] > 0 else 0
        a_p1 = row['a_p1']     
        b_p1 = row['b_p1']        
        v = row['v']        
        turnover = row['turnover'] / multiplier 
        df_mkt1.append(row)   
    return df_mkt1
    


def get_pnl_details(df_order, df_mkt, iid_list, future_csv):
    result_pnl = {}
    for iid in iid_list:
        t = time.time()
        multiplier = future_csv.loc[iid]['multiplier']
        
        data_order = df_order[df_order.insid == iid]
        data_order = data_order.to_dict('records')
        
        # 对mkt数据做一些操作, 计算几个量
        data_mkt0 = df_mkt[df_mkt.insid_md == iid]   
        data_mkt = get_mkt_details(data_mkt0, multiplier)
            
        data_order = iter(data_order)
        data_mkt = iter(data_mkt)
        
        trade_alive = {}
        
        net = 0
        pnl = 0
        bidamount = 0
        askamount = 0
        
        details_list = []
        trade_list = []
                
        i = next(data_order, None)
        j = next(data_mkt, None)
        
        j_last = j.copy()
        
        print(iid+'数据转化完毕', time.time()-t)
        t = time.time()
    
        while True:        
            if i == None and j == None:
                break
            
            elif i == None:
                UnixStamp = j['UnixStamp']
                askP = j['a_p1'] if j['a_p1'] != -1 else np.inf
                bidP = j['b_p1']
                lastP = j['last_p']
                fairP = np.median([askP, bidP, lastP])
                pnl = net*fairP + askamount - bidamount                
                j_last = j.copy()
                details_list.append({**j_last, **{'UnixStamp':UnixStamp, 'net':net, 'pnl':pnl, 'fairP':fairP, 'iid':iid}})
                j = next(data_mkt, None)
            
            elif j == None: 
                if i['order_status'] == order_status.partial_traded.value or i['order_status'] == order_status.all_traded.value:
                    comments = i['comments']
                    v_t = i['volume_original_total']
                    UnixStamp = i['UnixStamp']
                    local_t = i['local_time']
                    internal_order_id = i['internal_order_id']
                    if not internal_order_id in trade_alive.keys():
                        vol = i['volume_traded']
                    elif internal_order_id in trade_alive.keys():
                        k = trade_alive[internal_order_id]
                        if i['volume_traded'] <= k['volume_traded']:
                            i = next(data_order, None)
                            continue
                        else:
                            vol = i['volume_traded'] - k['volume_traded']
                    traded_price = i['order_price'] 
                    if i['long_short'] == 0:
                        bidamount += vol*traded_price
                        net += vol
                    elif i['long_short'] == 1:
                        askamount += vol*traded_price
                        net -= vol
                    trade_alive[internal_order_id] = i
                    trade_list.append((UnixStamp, internal_order_id, traded_price, vol, v_t, i['long_short'], comments, iid))
                    
                    pnl = net*traded_price + askamount - bidamount
                    details_list.append({**j_last, **{'UnixStamp':UnixStamp, 'net':net, 'pnl':pnl, 'fairP':fairP, 'iid':iid}})          
                i = next(data_order, None)
                
            elif i['UnixStamp'] <= j['UnixStamp']:
                if i['order_status'] == order_status.partial_traded.value or i['order_status'] == order_status.all_traded.value:
                    comments = i['comments']
                    v_t = i['volume_original_total']
                    UnixStamp = i['UnixStamp']
                    internal_order_id = i['internal_order_id']
                    if not internal_order_id in trade_alive.keys():
                        vol = i['volume_traded']
                    elif internal_order_id in trade_alive.keys():
                        k = trade_alive[internal_order_id]
                        if i['volume_traded'] <= k['volume_traded']:
                            i = next(data_order, None)
                            continue
                        else:
                            vol = i['volume_traded'] - k['volume_traded']
                    traded_price = i['order_price'] 
                    if i['long_short'] == 0:
                        bidamount += vol*traded_price
                        net += vol
                    elif i['long_short'] == 1:
                        askamount += vol*traded_price
                        net -= vol
                    trade_alive[internal_order_id] = i
                    trade_list.append((UnixStamp, internal_order_id, traded_price, vol, v_t, i['long_short'], comments, iid))
                    
                    pnl = net*traded_price + askamount - bidamount
                    details_list.append({**j_last, **{'UnixStamp':UnixStamp, 'net':net, 'pnl':pnl, 'fairP':fairP, 'iid':iid}})           
                i = next(data_order, None)
            
            elif i['UnixStamp'] > j['UnixStamp']:
                UnixStamp = j['UnixStamp']
                askP = j['a_p1'] if j['a_p1'] != -1 else np.inf
                bidP = j['b_p1']
                lastP = j['last_p']
                fairP = np.median([askP, bidP, lastP])
                pnl = net*fairP + askamount - bidamount                
                j_last = j.copy()
                details_list.append({**j_last, **{'UnixStamp':UnixStamp, 'net':net, 'pnl':pnl, 'fairP':fairP, 'iid':iid}})
                j = next(data_mkt, None)
        print(iid+'net, pnl, trade_list 统计完毕', time.time()-t)
                
        details_list = pd.DataFrame(details_list)
        
        # details_list.columns = ['UnixStamp', 'net', 'pnl', 'fair_price', 'iid']
        details_list.index = details_list['UnixStamp']
        
        try:
            trade_list = pd.DataFrame(trade_list)
            trade_list.columns = ['UnixStamp', 'internal_order_id', 'trade_price', 'vol', 'volume_original_total', 'long_short', 'comments', 'iid']
            trade_list.index = trade_list['UnixStamp']
        except:
            trade_list = pd.DataFrame(columns = ['UnixStamp', 'internal_order_id', 'trade_price', 'vol', 'volume_original_total', 'long_short', 'comments', 'iid'])
        
        result_pnl[iid] = {'iid':iid, 'pnl&net':details_list, 'trade_list':trade_list, 'trade':trade_alive}
    return result_pnl

#%%
result_pnl2 = get_pnl_details(df_order, df_mkt, iid_list, future_csv)

#%%
# result_pnl = get_pnl(df_order, df_mkt, iid_list)


#%%
iid = 'hc2111'
pnl = result_pnl[iid]['pnl&net']

fig, ax = plt.subplots()
ax1 = pnl.plot(y='pnl', x='UnixStamp', ax=ax)
ax2 = pnl.plot(y='net', x='UnixStamp', ax=ax, secondary_y = True)
plt.show()



#%%
# 定位
def time_to_market(t, n, df_mkt): # t 代表时间, n 代表前后n秒
    try:
        df_mkt = df_mkt.to_dict('records')
    except:
        pass
    try:
        t0 = datetime.datetime.strptime(t,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp()
    except:
        t0 = t
    t_min = t0-np.abs(n)
    t_max = t0+np.abs(n)
    df_mkt_show = []
    for row in df_mkt:
        if row['UnixStamp'] < t_min:
            continue
        elif row['UnixStamp'] > t_max:
            break
        else:
            df_mkt_show.append(row)
    df_mkt_show = pd.DataFrame(df_mkt_show)
    df_mkt_show.index = df_mkt_show['UnixStamp']
    return df_mkt_show

#%%
iid = 'pp2112'
df_mkt_test = result_pnl2[iid]['pnl&net'].copy()
trade_list = result_pnl2[iid]['trade_list'].copy()

df_mkt_test['micro_price'] = (df_mkt_test['a_p1']*df_mkt_test['b_v1']+df_mkt_test['b_p1']*df_mkt_test['a_v1'])/(df_mkt_test['b_v1']+df_mkt_test['a_v1'])
df_mkt_test['direction_volume'] = df_mkt_test['direction'] * df_mkt_test['dv']
df_mkt_test['dmp'] = (df_mkt_test['micro_price'].diff()).fillna(0, inplace = True)

#%%
json_body = []
for iid in iid_list:
    df_mkt_test = result_pnl2[iid]['pnl&net'].copy()
    df_mkt_test['micro_price'] = (df_mkt_test['a_p1']*df_mkt_test['b_v1']+df_mkt_test['b_p1']*df_mkt_test['a_v1'])/(df_mkt_test['b_v1']+df_mkt_test['a_v1']) 
    df_mkt_test['direction_volume'] = df_mkt_test['direction'] * df_mkt_test['dv']    
    df_mkt_test = df_mkt_test.to_dict('records')
    t = time.time()
    
    for row in df_mkt_test:
        
        current_time = int(row['UnixStamp']*10**9)
        
        measurement = exchange+'_future_market'
        
        a_p1 = float(row['a_p1'])
        a_v1 = int(row['a_v1'])
        a_p2 = float(row['a_p2'])
        a_v2 = int(row['a_v2'])
        a_p3 = float(row['a_p3'])
        a_v3 = int(row['a_v3'])
        a_p4 = float(row['a_p4'])
        a_v4 = int(row['a_v4'])
        a_p5 = float(row['a_p5'])
        a_v5 = int(row['a_v5'])
        
        b_p1 = float(row['b_p1'])
        b_v1 = int(row['b_v1'])
        b_p2 = float(row['b_p2'])
        b_v2 = int(row['b_v2'])
        b_p3 = float(row['b_p3'])
        b_v3 = int(row['b_v3'])
        b_p4 = float(row['b_p4'])
        b_v4 = int(row['b_v4'])
        b_p5 = float(row['b_p5'])
        b_v5 = int(row['b_v5'])
        
        exchange_t = int(row['exchange_t'])  
        insid_md = str(row['insid_md'])
        last_p = float(row['last_p'])
        local_t = int(row['local_t'])
        lower_limit_p = str(row['lower_limit_p'])
        preclose_p = str(row['preclose_p'])
        presettle_p = str(row['presettle_p'])
        turnover = float(row['turnover'])
        upper_limit_p = float(row['upper_limit_p'])
        v = int(row['v'])
        
        dv = int(row['dv'])
        dt = float(row['dt'])    
        avp = float(row['avp'])
        direction = float(row['direction'])
        net = int(row['net'])
        pnl = float(row['pnl'])
        fairP = float(row['fairP'])
        micro_price = float(row['micro_price']) if not np.isnan(row['micro_price']) else float(fairP)
        direction_volume = float(row['direction_volume'])
        
        
    
    
           
        body = {
                "measurement": measurement, 
                "time": current_time, 
                "tags": {
                    "insid_md": insid_md
                }, 
                "fields": {
                    "a_p1": a_p1, 
                    "a_p2": a_p2,                 
                    "a_p3": a_p3,                 
                    "a_p4": a_p4,                 
                    "a_p5": a_p5,                 
                    "a_v1": a_v1,                 
                    "a_v2": a_v2,                    
                    "a_v3": a_v3,                    
                    "a_v4": a_v4,                    
                    "a_v5": a_v5,                    
                    "b_p1": b_p1, 
                    "b_p2": b_p2,                 
                    "b_p3": b_p3,                 
                    "b_p4": b_p4,                 
                    "b_p5": b_p5,                 
                    "b_v1": b_v1,                 
                    "b_v2": b_v2,                    
                    "b_v3": b_v3,                    
                    "b_v4": b_v4,                    
                    "b_v5": b_v5, 
                    "exchange_t": exchange_t,               
                    "last_p": last_p,
                    "local_t": local_t,
                    "lower_limit_p": lower_limit_p,
                    "preclose_p": preclose_p,
                    "presettle_p": presettle_p,
                    "turnover": turnover,
                    "upper_limit_p": upper_limit_p,
                    "v": v,
                    "dv": dv,
                    "dt": dt, 
                    "avp": avp,
                    "direction": direction, 
                    "net": net, 
                    "pnl": pnl,
                    "fairP": fairP,
                    "micro_price": micro_price,
                    "direction_volume": direction_volume
                    
                }, 
            }
        
        
        json_body.append(body)
    print(iid, time.time()-t)
#%% 
t = time.time()   
client = InfluxDBClient('10.17.88.168',9001,'reader','reader','omm')
res = client.write_points(json_body, batch_size = 10000)
print(time.time()-t)


#%%
t = trade_list.index[189]
print(t)
df_mkt_show = time_to_market(t, 5, df_mkt_test)
print(trade_list.loc[t])

#%%
df_mkt_test1 = df_mkt_test.to_dict('records')
df_mkt_pure = [df_mkt_test1[0]]
row0 = df_mkt_test1[0]
for row in df_mkt_test1[1:]:
    if row0['net'] == row['net']:
        df_mkt_pure.append(row)
    row0 = row
# 时间标准化
t_tick = 0.25
df_mkt_standard = []
row_l = df_mkt_test1[0]
for row in df_mkt_test1[1:]:
    t0 = row_l['UnixStamp']
    t1 = row['UnixStamp']
    n = np.max([1, round((t1-t0)/t_tick)])
    if n >= 600/t_tick:
        n = 1
    for i in range(n):
        row_i = row_l.copy()
        t_i = t0 + i*t_tick
        row_i['UnixStamp'] = t_i
        if i >= 1:
            row_i['dv'] = 0
            row_i['dt'] = 0
            row_i['avp'] = 0   
            row_i['direction'] = 0
            row_i['direction_volume'] = 0
        df_mkt_standard.append(row_i)
    row_l = row.copy()
    
df_mkt_standard.append(row_l)      
df_mkt_standard = pd.DataFrame(df_mkt_standard)
df_mkt_standard.columns = df_mkt_standard.columns
df_mkt_standard.index = df_mkt_standard.UnixStamp  
    
df_mkt_pure = pd.DataFrame(df_mkt_pure)
df_mkt_pure.columns = df_mkt_test.columns
df_mkt_pure.index = df_mkt_pure.UnixStamp
    
#%%
def get_trend(df_mkt, m, n):
    try:
        df_mkt = df_mkt.to_dict('records')
    except:
        pass
    is_trend = 0
    df_mkt_trend = []
    trend_stop = 0
    for i in range(len(df_mkt)-1):
        row = df_mkt[i].copy()
        m0 = np.min([len(df_mkt)-1-i, m])
        row_m = df_mkt[i+m0]
        row_next = df_mkt[i+1]
        
        if row_m['fairP'] - row['fairP'] >= n:
            if is_trend == 1 or row_next['fairP'] - row['fairP'] >= 1:
                is_trend = 1
                trend_stop = i+m0
        elif row_m['fairP'] - row['fairP'] <= -n:
            if is_trend == -1 or row_next['fairP'] - row['fairP'] <= -1:
                is_trend = -1  
                trend_stop = i+m0
        elif i > trend_stop:
            is_trend = 0
        row['is_trend'] = is_trend 
        df_mkt_trend.append(row)
    row = df_mkt[-1].copy()
    row['is_trend'] = 0
    df_mkt_trend.append(row)  
    
    df_mkt_trend = pd.DataFrame(df_mkt_trend)
    df_mkt_trend.index = df_mkt_trend.UnixStamp
    return df_mkt_trend
        

#%%
df_mkt_trend = get_trend(df_mkt_pure, 10, 4)

#%%
def get_up_down(l):
    max_up = []
    max_down = []
    max_now = -np.inf
    min_now = np.inf
    for i in range(len(l)-1):
        max_now = np.max([max_now, l[i]])
        min_now = np.min([min_now, l[i]])
        max_up.append(l[i+1]-min_now)
        max_down.append(l[i+1]-max_now)
    return(np.max(max_up), np.min(max_down))


def get_micro_price(micro_price_list, k):
    if len(micro_price_list) <= k:
        up, down, last = (0, 0, 0)
    else:
        up, down = get_up_down(micro_price_list)
        dmp_list = [micro_price_list[i+1]-micro_price_list[i] for i in range(len(micro_price_list)-1)]
        p0 = dmp_list[-1]
        if p0 > 0:
            last = 1
            for i in range(1, k+1):
                p = dmp_list[-i]
                if p <= 0:
                    last = 0
                    break
        elif p0 < 0:
            last = -1
            for i in range(1, k+1):
                p = dmp_list[-i]
                if p >= 0:
                    last = 0
                    break           
        else:
            last = 0
    return up, down, last
        


#%%
# 回测
df_mkt_standard1 = df_mkt_standard.to_dict('records')
df_mkt_standard2 = []
row_l = df_mkt_standard1[0]
count_up = 0
count_down = 0
count_up0 = 0
count_down0 = 0

di_vo = []
count_tp = 0
count_stop = 0
count_up_signal = 0
count_down_signal = 0
ask_is_quoting = True
bid_is_quoting = True
count_is_quoting = 0
micro_price_list = []

net_list = []
pnl_list = []
net_list0 = []
pnl_list0 = []

net = 0
pnl = 0
net0 = 0
pnl0 = 0

ask_amount = 0
bid_amount = 0
ask_amount0 = 0
bid_amount0 = 0

count = 0

bidP = 0
askP = np.inf

row_n = row_l.copy()
row_n['bidP'] = bidP
row_n['askP'] = askP
row_n['bid_is_quoting'] = bid_is_quoting
row_n['ask_is_quoting'] = ask_is_quoting
df_mkt_standard2.append(row_n)

for row in df_mkt_standard1[1:]:    
    if row['b_p1'] >= askP or (row['last_p'] > askP and row['dv'] > 0):
    # if row['b_p1'] >= row_l['a_p1']+1:
        count_up += 1
        count_tp = 5
        di_vo = []
        if net0 > -4:
            net0 -= 1
            ask_amount0 += (askP)*1
        pnl0 = ask_amount0 - bid_amount0 + net0*(askP)
        pnl_list0.append(pnl0)
        if ask_is_quoting:
            count_up0 += 1
            net -= 1
            ask_amount += (askP)*1
            pnl = ask_amount - bid_amount + net*(askP)
            pnl_list.append(pnl)
        row_l = row
        askP = row_l['a_p1'] + 1
        bidP = row_l['b_p1'] - 1
        ask_is_quoting = False
        bid_is_quoting = False   
        row_n = row_l.copy()
        row_n['bidP'] = bidP
        row_n['askP'] = askP
        row_n['bid_is_quoting'] = bid_is_quoting
        row_n['ask_is_quoting'] = ask_is_quoting
        df_mkt_standard2.append(row_n)
        continue
                    
    elif row['a_p1'] <= bidP or (row['last_p'] < bidP and row['dv'] > 0):
    # elif row['a_p1'] <= row_l['b_p1']-1:
        count_down += 1
        count_tp = 5
        di_vo = []
        if net0 < 4:
            net0 += 1
            bid_amount0 += (bidP)*1
        pnl0 = ask_amount0 - bid_amount0 + net0*(bidP)
        pnl_list0.append(pnl0)
        if bid_is_quoting:
            count_down0 += 1
            net += 1
            bid_amount += (bidP)*1
            pnl = ask_amount - bid_amount + net*(bidP)
            pnl_list.append(pnl)
        row_l = row
        askP = row_l['a_p1'] + 1
        bidP = row_l['b_p1'] - 1
        row_n = row_l.copy()
        row_n['bidP'] = bidP
        row_n['askP'] = askP
        row_n['bid_is_quoting'] = bid_is_quoting
        row_n['ask_is_quoting'] = ask_is_quoting
        df_mkt_standard2.append(row_n)
        continue
    
    ask_is_quoting = True
    bid_is_quoting = True    
    
    if len(di_vo) < 10:
        di_vo.append(row['direction_volume'])
    else:
        di_vo.pop(0)
        di_vo.append(row['direction_volume'])
    
    if len(micro_price_list) < 8:
        micro_price_list.append(row['micro_price'])
    else:
        micro_price_list.pop(0)
        micro_price_list.append(row['micro_price'])    
    
    if count_tp > 0:
        count_tp -= 1
        ask_is_quoting = False
        bid_is_quoting = False
    
    if count_up_signal > 0:
        count_up_signal -= 1
        ask_is_quoting = False     
    
    if count_down_signal > 0:
        count_down_signal -= 1
        bid_is_quoting = False        
        
    up, down, last = get_micro_price(micro_price_list, 4)
    # up, down, last = 0, 0, 0
            
    if np.sum(di_vo) >= 100 or row['direction_volume'] >= 100 or row['direction'] > 1 or count_tp > 0 or up >= 3 or last == 1:
    # if np.sum(di_vo) >= 80 or row['direction_volume'] >= 100:
        ask_is_quoting = False
        count += 1
        count_up_signal = 2
        # count_tp = 1
        
    if np.sum(di_vo) <= -100 or row['direction_volume'] <= -100 or row['direction'] < -1 or count_tp > 0 or down <= -3 or last == -1:
    # if np.sum(di_vo) <= -80 or row['direction_volume'] <= -100:
        bid_is_quoting = False
        count += 1
        count_down_signal = 2
        # count_tp = 1
    
    

    # if net >= 4:
    #     bid_is_quoting = False
    #     ask_is_quoting = True
    # if net <= -4:
    #     ask_is_quoting = False
    #     bid_is_quoting = True
    
    if ask_is_quoting and bid_is_quoting:
        count_is_quoting += 1
    elif ask_is_quoting or bid_is_quoting:
        count_is_quoting += 0.5
    
    
    row_l = row
    askP = row_l['a_p1']+1
    bidP = row_l['b_p1']-1
    row_n = row_l.copy()
    row_n['bidP'] = bidP
    row_n['askP'] = askP
    row_n['bid_is_quoting'] = bid_is_quoting
    row_n['ask_is_quoting'] = ask_is_quoting
    df_mkt_standard2.append(row_n)

print(count_up, count_up0, count_down, count_down0)
    
df_mkt_standard2 = pd.DataFrame(df_mkt_standard2)
df_mkt_standard2.index = df_mkt_standard2.UnixStamp

df_mkt_standard2 = get_trend(df_mkt_standard2, 12, 6)
    
#%%


#%%

fig, ax = plt.subplots()
ax1 = df_mkt_standard.plot(y='pnl', x='UnixStamp', ax=ax)
ax2 = df_mkt_standard.plot(y='net', x='UnixStamp', ax=ax, secondary_y = True)
plt.show()
#%%
# iid = 'c2111'
fig, ax = plt.subplots()
ax1 = df_mkt_show.plot(y='pnl', x='UnixStamp', ax=ax)
ax2 = df_mkt_show.plot(y='net', x='UnixStamp', ax=ax, secondary_y = True)
plt.show()

        
#%%
plt.figure()
plt.plot(df_mkt_standard['fairP'], label = 'fair_price')
plt.plot(df_mkt_standard['micro_price'], label = 'micro_price')
plt.legend()
plt.show()    

#%%
plt.figure()
plt.plot(df_mkt_standard['pnl'], label = 'pnl')
# plt.plot(df_mkt_standard['micro_price'], label = 'micro_price')
plt.legend()
plt.show()    

#%%
trade_list0 = trade_list.to_dict('records')
settle_price = (df_mkt_standard.iloc[-1000]['a_p1']+df_mkt_standard.iloc[-1000]['b_p1'])/2
mid_pnl = 0
spread_pnl = 0
trade_volume = np.sum(trade_list['vol'])
for trade_order in trade_list0:
    UnixStamp = trade_order['UnixStamp']
    trade_price = trade_order['trade_price']
    vol = trade_order['vol']
    mkt = df_mkt_standard.loc[UnixStamp]
    mid = (mkt['a_p1']+mkt['b_p1']) / 2
    long_short = trade_order['long_short']
    delta = mid - trade_price if long_short == 0 else trade_price - mid
    mid_pnl += (settle_price - mid) * vol if long_short == 0 else -(settle_price - mid) * vol
    spread_pnl += delta*vol
print(mid_pnl, spread_pnl, trade_volume) 

#%%
def pnl_duration(df_mkt, pnl):
    duration = []
    df_mkt = df_mkt.to_dict('records')
    pnl0 = df_mkt[0]['pnl']
    t0 = df_mkt[0]['UnixStamp']
    for row in df_mkt:
        
        
        
        
        
        
        


   
    
    















