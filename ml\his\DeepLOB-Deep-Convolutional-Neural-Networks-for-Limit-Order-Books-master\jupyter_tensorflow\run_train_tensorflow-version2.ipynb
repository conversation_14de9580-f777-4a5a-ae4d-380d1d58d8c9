{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeepLOB: Deep Convolutional Neural Networks for Limit Order Books\n", "\n", "### Authors: <AUTHORS>\n", "Oxford-Man Institute of Quantitative Finance, Department of Engineering Science, University of Oxford\n", "\n", "This jupyter notebook is used to demonstrate our recent paper [2] published in IEEE Transactions on Singal Processing. We use FI-2010 [1] dataset and present how model architecture is constructed here. \n", "\n", "### Data:\n", "The FI-2010 is publicly avilable and interested readers can check out their paper [1]. The dataset can be downloaded from: https://etsin.fairdata.fi/dataset/73eb48d7-4dbc-4a10-a52a-da745b47a649 \n", "\n", "Otherwise, the notebook will download the data automatically or it can be obtained from: \n", "\n", "https://drive.google.com/drive/folders/1Xen3aRid9ZZhFqJRgEMyETNazk02cNmv?usp=sharing.\n", "\n", "### References:\n", "[1] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>chmark dataset for mid‐price forecasting of limit order book data with machine learning methods. Journal of Forecasting. 2018 Dec;37(8):852-66. https://arxiv.org/abs/1705.03233\n", "\n", "[2] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>: Deep convolutional neural networks for limit order books. IEEE Transactions on Signal Processing. 2019 Mar 25;67(11):3001-12. https://arxiv.org/abs/1808.03668\n", "\n", "### This notebook runs on tensorflow 2."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data already existed.\n"]}], "source": ["# obtain data\n", "import os \n", "if not os.path.isfile('data.zip'):\n", "    !wget https://raw.githubusercontent.com/zcakhaa/DeepLOB-Deep-Convolutional-Neural-Networks-for-Limit-Order-Books/master/data/data.zip\n", "    !unzip -n data.zip\n", "    print('data downloaded.')\n", "else:\n", "    print('data already existed.')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 Physical GPUs, 1 Logical GPUs\n"]}], "source": ["# limit gpu memory\n", "import tensorflow as tf\n", "\n", "gpus = tf.config.experimental.list_physical_devices('GPU')\n", "if gpus:\n", "    try:\n", "    # Currently, memory growth needs to be the same across GPUs\n", "        for gpu in gpus:\n", "            tf.config.experimental.set_memory_growth(gpu, True)\n", "            logical_gpus = tf.config.experimental.list_logical_devices('GPU')\n", "        print(len(gpus), \"Physical GPUs,\", len(logical_gpus), \"Logical GPUs\")\n", "    except RuntimeError as e:\n", "    # Memory growth must be set before GPUs have been initialized\n", "        print(e)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# load packages\n", "import pandas as pd\n", "import pickle\n", "import numpy as np\n", "import keras\n", "from keras import backend as K\n", "from keras.models import load_model, Model\n", "from keras.layers import Flatten, Dense, Dropout, Activation, Input, LSTM, Reshape, Conv2D, MaxPooling2D\n", "from keras.optimizers import Adam\n", "from keras.layers.advanced_activations import LeakyReLU\n", "from keras.utils import np_utils\n", "\n", "from sklearn.metrics import classification_report, accuracy_score\n", "import matplotlib.pyplot as plt\n", "\n", "# set random seeds\n", "np.random.seed(1)\n", "tf.random.set_seed(2)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data preparation\n", "\n", "We used no auction dataset that is normalised by decimal precision approach in their work. The first 40 columns of the FI-2010 dataset are 10 levels ask and bid information for a limit order book and we only use these 40 features in our network. The last 5 columns of the FI-2010 dataset are the labels with different prediction horizons. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def prepare_x(data):\n", "    df1 = data[:40, :].T\n", "    return np.array(df1)\n", "\n", "def get_label(data):\n", "    lob = data[-5:, :].T\n", "    return lob\n", "\n", "def data_classification(X, Y, T):\n", "    [N, D] = X.shape\n", "    df = np.array(X)\n", "    dY = np.array(Y)\n", "    dataY = dY[T - 1:N]\n", "    dataX = np.zeros((N - T + 1, T, D))\n", "    for i in range(T, N + 1):\n", "        dataX[i - T] = df[i - T:i, :]\n", "    return dataX.reshape(dataX.shape + (1,)), dataY\n", "\n", "def prepare_x_y(data, k, T):\n", "    x = prepare_x(data)\n", "    y = get_label(data)\n", "    x, y = data_classification(x, y, T=T)\n", "    y = y[:,k] - 1\n", "    y = np_utils.to_categorical(y, 3)\n", "    return x, y"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(203701, 100, 40, 1) (203701, 3)\n", "(50851, 100, 40, 1) (50851, 3)\n", "(139488, 100, 40, 1) (139488, 3)\n"]}], "source": ["# please change the data_path to your local path\n", "# data_path = '/nfs/home/<USER>/limit_order_book/data'\n", "\n", "dec_data = np.loadtxt('Train_Dst_NoAuction_DecPre_CF_7.txt')\n", "dec_train = dec_data[:, :int(np.floor(dec_data.shape[1] * 0.8))]\n", "dec_val = dec_data[:, int(np.floor(dec_data.shape[1] * 0.8)):]\n", "\n", "dec_test1 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_7.txt')\n", "dec_test2 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_8.txt')\n", "dec_test3 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_9.txt')\n", "dec_test = np.hstack((dec_test1, dec_test2, dec_test3))\n", "\n", "k = 4 # which prediction horizon\n", "T = 100 # the length of a single input\n", "n_hiddens = 64\n", "checkpoint_filepath = './model_tensorflow2/weights'\n", "\n", "trainX_CNN, trainY_CNN = prepare_x_y(dec_train, k, T)\n", "valX_CNN, valY_CNN = prepare_x_y(dec_val, k, T)\n", "testX_CNN, testY_CNN = prepare_x_y(dec_test, k, T)\n", "\n", "print(trainX_CNN.shape, trainY_CNN.shape)\n", "print(valX_CNN.shape, valY_CNN.shape)\n", "print(testX_CNN.shape, testY_CNN.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Architecture\n", "\n", "Please find the detailed discussion of our model architecture in our paper."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"functional_1\"\n", "__________________________________________________________________________________________________\n", "Layer (type)                    Output Shape         Param #     Connected to                     \n", "==================================================================================================\n", "input_1 (InputLayer)            [(None, 100, 40, 1)] 0                                            \n", "__________________________________________________________________________________________________\n", "conv2d (Conv2D)                 (None, 100, 20, 32)  96          input_1[0][0]                    \n", "__________________________________________________________________________________________________\n", "leaky_re_lu (LeakyReLU)         (None, 100, 20, 32)  0           conv2d[0][0]                     \n", "__________________________________________________________________________________________________\n", "conv2d_1 (Conv2D)               (None, 100, 20, 32)  4128        leaky_re_lu[0][0]                \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_1 (LeakyReLU)       (None, 100, 20, 32)  0           conv2d_1[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_2 (Conv2D)               (None, 100, 20, 32)  4128        leaky_re_lu_1[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_2 (LeakyReLU)       (None, 100, 20, 32)  0           conv2d_2[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_3 (Conv2D)               (None, 100, 10, 32)  2080        leaky_re_lu_2[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_3 (LeakyReLU)       (None, 100, 10, 32)  0           conv2d_3[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_4 (Conv2D)               (None, 100, 10, 32)  4128        leaky_re_lu_3[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_4 (LeakyReLU)       (None, 100, 10, 32)  0           conv2d_4[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_5 (Conv2D)               (None, 100, 10, 32)  4128        leaky_re_lu_4[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_5 (LeakyReLU)       (None, 100, 10, 32)  0           conv2d_5[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_6 (Conv2D)               (None, 100, 1, 32)   10272       leaky_re_lu_5[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_6 (LeakyReLU)       (None, 100, 1, 32)   0           conv2d_6[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_7 (Conv2D)               (None, 100, 1, 32)   4128        leaky_re_lu_6[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_7 (LeakyReLU)       (None, 100, 1, 32)   0           conv2d_7[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_8 (Conv2D)               (None, 100, 1, 32)   4128        leaky_re_lu_7[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_8 (LeakyReLU)       (None, 100, 1, 32)   0           conv2d_8[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_9 (Conv2D)               (None, 100, 1, 64)   2112        leaky_re_lu_8[0][0]              \n", "__________________________________________________________________________________________________\n", "conv2d_11 (Conv2D)              (None, 100, 1, 64)   2112        leaky_re_lu_8[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_9 (LeakyReLU)       (None, 100, 1, 64)   0           conv2d_9[0][0]                   \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_11 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_11[0][0]                  \n", "__________________________________________________________________________________________________\n", "max_pooling2d (MaxPooling2D)    (None, 100, 1, 32)   0           leaky_re_lu_8[0][0]              \n", "__________________________________________________________________________________________________\n", "conv2d_10 (Conv2D)              (None, 100, 1, 64)   12352       leaky_re_lu_9[0][0]              \n", "__________________________________________________________________________________________________\n", "conv2d_12 (Conv2D)              (None, 100, 1, 64)   20544       leaky_re_lu_11[0][0]             \n", "__________________________________________________________________________________________________\n", "conv2d_13 (Conv2D)              (None, 100, 1, 64)   2112        max_pooling2d[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_10 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_10[0][0]                  \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_12 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_12[0][0]                  \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_13 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_13[0][0]                  \n", "__________________________________________________________________________________________________\n", "concatenate (Concatenate)       (None, 100, 1, 192)  0           leaky_re_lu_10[0][0]             \n", "                                                                 leaky_re_lu_12[0][0]             \n", "                                                                 leaky_re_lu_13[0][0]             \n", "__________________________________________________________________________________________________\n", "reshape (Reshape)               (None, 100, 192)     0           concatenate[0][0]                \n", "__________________________________________________________________________________________________\n", "dropout (Dropout)               (None, 100, 192)     0           reshape[0][0]                    \n", "__________________________________________________________________________________________________\n", "lstm (LSTM)                     (None, 64)           65792       dropout[0][0]                    \n", "__________________________________________________________________________________________________\n", "dense (Dense)                   (None, 3)            195         lstm[0][0]                       \n", "==================================================================================================\n", "Total params: 142,435\n", "Trainable params: 142,435\n", "Non-trainable params: 0\n", "__________________________________________________________________________________________________\n"]}], "source": ["def create_deeplob(T, NF, number_of_lstm):\n", "    input_lmd = Input(shape=(T, NF, 1))\n", "    \n", "    # build the convolutional block\n", "    conv_first1 = Conv2D(32, (1, 2), strides=(1, 2))(input_lmd)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "\n", "    conv_first1 = Conv2D(32, (1, 2), strides=(1, 2))(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "\n", "    conv_first1 = Conv2D(32, (1, 10))(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    \n", "    # build the inception module\n", "    convsecond_1 = Conv2D(64, (1, 1), padding='same')(conv_first1)\n", "    convsecond_1 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_1)\n", "    convsecond_1 = Conv2D(64, (3, 1), padding='same')(convsecond_1)\n", "    convsecond_1 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_1)\n", "\n", "    convsecond_2 = Conv2D(64, (1, 1), padding='same')(conv_first1)\n", "    convsecond_2 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_2)\n", "    convsecond_2 = Conv2D(64, (5, 1), padding='same')(convsecond_2)\n", "    convsecond_2 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_2)\n", "\n", "    convsecond_3 = MaxPooling2D((3, 1), strides=(1, 1), padding='same')(conv_first1)\n", "    convsecond_3 = Conv2D(64, (1, 1), padding='same')(convsecond_3)\n", "    convsecond_3 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_3)\n", "    \n", "    convsecond_output = keras.layers.concatenate([convsecond_1, convsecond_2, convsecond_3], axis=3)\n", "    conv_reshape = Reshape((int(convsecond_output.shape[1]), int(convsecond_output.shape[3])))(convsecond_output)\n", "    conv_reshape = keras.layers.Dropout(0.2, noise_shape=(None, 1, int(conv_reshape.shape[2])))(conv_reshape, training=True)\n", "\n", "    # build the last LSTM layer\n", "    conv_lstm = LSTM(number_of_lstm)(conv_reshape)\n", "\n", "    # build the output layer\n", "    out = Dense(3, activation='softmax')(conv_lstm)\n", "    model = Model(inputs=input_lmd, outputs=out)\n", "    adam = keras.optimizers.<PERSON>(lr=0.0001)\n", "    model.compile(optimizer=adam, loss='categorical_crossentropy', metrics=['accuracy'])\n", "\n", "    return model\n", "\n", "deeplob = create_deeplob(trainX_CNN.shape[1], trainX_CNN.shape[2], n_hiddens)\n", "deeplob.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/50\n", "3183/3183 - 189s - loss: 1.0196 - accuracy: 0.4403 - val_loss: 1.0866 - val_accuracy: 0.3789\n", "Epoch 2/50\n", "3183/3183 - 58s - loss: 0.9671 - accuracy: 0.5110 - val_loss: 1.0834 - val_accuracy: 0.4206\n", "Epoch 3/50\n", "3183/3183 - 58s - loss: 0.8897 - accuracy: 0.5708 - val_loss: 0.9295 - val_accuracy: 0.5546\n", "Epoch 4/50\n", "3183/3183 - 59s - loss: 0.7783 - accuracy: 0.6414 - val_loss: 0.8941 - val_accuracy: 0.5778\n", "Epoch 5/50\n", "3183/3183 - 59s - loss: 0.7282 - accuracy: 0.6752 - val_loss: 0.9152 - val_accuracy: 0.5708\n", "Epoch 6/50\n", "3183/3183 - 59s - loss: 0.6752 - accuracy: 0.7096 - val_loss: 0.8440 - val_accuracy: 0.6164\n", "Epoch 7/50\n", "3183/3183 - 59s - loss: 0.6319 - accuracy: 0.7355 - val_loss: 0.8229 - val_accuracy: 0.6381\n", "Epoch 8/50\n", "3183/3183 - 59s - loss: 0.6018 - accuracy: 0.7517 - val_loss: 0.8095 - val_accuracy: 0.6479\n", "Epoch 9/50\n", "3183/3183 - 59s - loss: 0.5787 - accuracy: 0.7642 - val_loss: 0.8030 - val_accuracy: 0.6554\n", "Epoch 10/50\n", "3183/3183 - 59s - loss: 0.5597 - accuracy: 0.7748 - val_loss: 0.7686 - val_accuracy: 0.6696\n", "Epoch 11/50\n", "3183/3183 - 59s - loss: 0.5447 - accuracy: 0.7820 - val_loss: 0.7936 - val_accuracy: 0.6609\n", "Epoch 12/50\n", "3183/3183 - 59s - loss: 0.5303 - accuracy: 0.7896 - val_loss: 0.7795 - val_accuracy: 0.6678\n", "Epoch 13/50\n", "3183/3183 - 59s - loss: 0.5173 - accuracy: 0.7956 - val_loss: 0.7819 - val_accuracy: 0.6648\n", "Epoch 14/50\n", "3183/3183 - 59s - loss: 0.5048 - accuracy: 0.8012 - val_loss: 0.7859 - val_accuracy: 0.6681\n", "Epoch 15/50\n", "3183/3183 - 59s - loss: 0.4943 - accuracy: 0.8069 - val_loss: 0.8109 - val_accuracy: 0.6618\n", "Epoch 16/50\n", "3183/3183 - 59s - loss: 0.4841 - accuracy: 0.8117 - val_loss: 0.7941 - val_accuracy: 0.6660\n", "Epoch 17/50\n", "3183/3183 - 59s - loss: 0.4737 - accuracy: 0.8167 - val_loss: 0.8239 - val_accuracy: 0.6605\n", "Epoch 18/50\n", "3183/3183 - 59s - loss: 0.4668 - accuracy: 0.8205 - val_loss: 0.7923 - val_accuracy: 0.6702\n", "Epoch 19/50\n", "3183/3183 - 59s - loss: 0.4580 - accuracy: 0.8242 - val_loss: 0.8138 - val_accuracy: 0.6656\n", "Epoch 20/50\n", "3183/3183 - 59s - loss: 0.4487 - accuracy: 0.8288 - val_loss: 0.7996 - val_accuracy: 0.6700\n", "Epoch 21/50\n", "3183/3183 - 59s - loss: 0.4422 - accuracy: 0.8310 - val_loss: 0.8025 - val_accuracy: 0.6724\n", "Epoch 22/50\n", "3183/3183 - 59s - loss: 0.4352 - accuracy: 0.8342 - val_loss: 0.8369 - val_accuracy: 0.6666\n", "Epoch 23/50\n", "3183/3183 - 59s - loss: 0.4289 - accuracy: 0.8363 - val_loss: 0.8509 - val_accuracy: 0.6647\n", "Epoch 24/50\n", "3183/3183 - 59s - loss: 0.4223 - accuracy: 0.8396 - val_loss: 0.8713 - val_accuracy: 0.6620\n", "Epoch 25/50\n", "3183/3183 - 59s - loss: 0.4155 - accuracy: 0.8434 - val_loss: 0.8565 - val_accuracy: 0.6574\n", "Epoch 26/50\n", "3183/3183 - 59s - loss: 0.4099 - accuracy: 0.8460 - val_loss: 0.8605 - val_accuracy: 0.6643\n", "Epoch 27/50\n", "3183/3183 - 59s - loss: 0.4047 - accuracy: 0.8476 - val_loss: 0.8538 - val_accuracy: 0.6656\n", "Epoch 28/50\n", "3183/3183 - 59s - loss: 0.3928 - accuracy: 0.8527 - val_loss: 0.9079 - val_accuracy: 0.6566\n", "Epoch 30/50\n", "3183/3183 - 59s - loss: 0.3888 - accuracy: 0.8548 - val_loss: 0.9045 - val_accuracy: 0.6568\n", "Epoch 31/50\n", "3183/3183 - 59s - loss: 0.3845 - accuracy: 0.8565 - val_loss: 0.9254 - val_accuracy: 0.6563\n", "Epoch 32/50\n", "3183/3183 - 59s - loss: 0.3794 - accuracy: 0.8585 - val_loss: 0.9202 - val_accuracy: 0.6612\n", "Epoch 33/50\n", "3183/3183 - 59s - loss: 0.3753 - accuracy: 0.8606 - val_loss: 0.9141 - val_accuracy: 0.6568\n", "Epoch 34/50\n", "3183/3183 - 59s - loss: 0.3705 - accuracy: 0.8624 - val_loss: 0.9341 - val_accuracy: 0.6601\n", "Epoch 35/50\n", "3183/3183 - 59s - loss: 0.3674 - accuracy: 0.8640 - val_loss: 0.9346 - val_accuracy: 0.6549\n", "Epoch 36/50\n", "3183/3183 - 59s - loss: 0.3625 - accuracy: 0.8657 - val_loss: 0.9559 - val_accuracy: 0.6528\n", "Epoch 37/50\n", "3183/3183 - 59s - loss: 0.3583 - accuracy: 0.8681 - val_loss: 0.9775 - val_accuracy: 0.6538\n", "Epoch 38/50\n", "3183/3183 - 59s - loss: 0.3546 - accuracy: 0.8689 - val_loss: 0.9475 - val_accuracy: 0.6551\n", "Epoch 39/50\n", "3183/3183 - 59s - loss: 0.3512 - accuracy: 0.8703 - val_loss: 0.9390 - val_accuracy: 0.6563\n", "Epoch 40/50\n", "3183/3183 - 59s - loss: 0.3471 - accuracy: 0.8720 - val_loss: 0.9605 - val_accuracy: 0.6595\n", "Epoch 41/50\n", "3183/3183 - 59s - loss: 0.3443 - accuracy: 0.8730 - val_loss: 0.9890 - val_accuracy: 0.6531\n", "Epoch 42/50\n", "3183/3183 - 59s - loss: 0.3405 - accuracy: 0.8741 - val_loss: 1.0255 - val_accuracy: 0.6499\n", "Epoch 43/50\n", "3183/3183 - 59s - loss: 0.3374 - accuracy: 0.8756 - val_loss: 0.9890 - val_accuracy: 0.6544\n", "Epoch 44/50\n", "3183/3183 - 59s - loss: 0.3348 - accuracy: 0.8771 - val_loss: 1.0204 - val_accuracy: 0.6469\n", "Epoch 45/50\n"]}], "source": ["%%time\n", "\n", "model_checkpoint_callback = tf.keras.callbacks.ModelCheckpoint(\n", "    filepath=checkpoint_filepath,\n", "    save_weights_only=True,\n", "    monitor='val_loss',\n", "    mode='auto',\n", "    save_best_only=True)\n", "\n", "deeplob.fit(trainX_CNN, trainY_CNN, validation_data=(valX_CNN, valY_CNN), \n", "            epochs=200, batch_size=128, verbose=2, callbacks=[model_checkpoint_callback])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Testing"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["deeplob.load_weights(checkpoint_filepath)\n", "pred = deeplob.predict(testX_CNN)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["accuracy_score: 0.7361063317274604\n", "              precision    recall  f1-score   support\n", "\n", "           0     0.7013    0.7435    0.7218     47915\n", "           1     0.8273    0.7275    0.7742     48050\n", "           2     0.6912    0.7375    0.7136     43523\n", "\n", "    accuracy                         0.7361    139488\n", "   macro avg     0.7399    0.7362    0.7365    139488\n", "weighted avg     0.7416    0.7361    0.7373    139488\n", "\n"]}], "source": ["print('accuracy_score:', accuracy_score(np.argmax(testY_CNN, axis=1), np.argmax(pred, axis=1)))\n", "print(classification_report(np.argmax(testY_CNN, axis=1), np.argmax(pred, axis=1), digits=4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.12"}}, "nbformat": 4, "nbformat_minor": 2}