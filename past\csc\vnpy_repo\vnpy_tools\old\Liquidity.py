#%%
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
import scipy.stats as ss
from OmmDatabase import OmmDatabase
from datetime import datetime
import copy
import seaborn as sns
import numba
import gc
import pickle
from sklearn.preprocessing import scale
import warnings
warnings.filterwarnings('ignore')


#%%
def get_Fair(df,edgeP=5):
    fair = df['lastPriceOnMarket']
    lastP = df['lastPriceOnMarket']
    bidP = df['Bid1Price']
    askP = df['Ask1Price']
    if bidP > 0 and askP > 0:
        # if  askP-bidP<=edgeP: # 有流动性，中间价
        #     fair=0.5*(askP+bidP)
        # else:   # 流动性不足, 不变
            if lastP>askP:
                fair=askP
            if lastP<bidP:
                fair=bidP
    return fair

def formatStd(x: int) -> str:
    """将精度改为0.01s"""
    # 可能的bug, x中只到秒如 20201016223659
    # x/10000时精度损失 20201016223659999271
    n = len(str(x)) # 数据位数
    if n == 14:
        return str(float(x))
    if n > 14:
        return str(round(x/10**(n-14), 1))

def setDtIndex(df, timeCol='SystemStamp'):
    """
    设置DatetimeIndex
    11.23 修改index精度为0.01s，增加df为None的处理（即无数据）
    """
    if  df is None:
        return df
    df = df.copy()
    dfDs = pd.to_datetime(df[timeCol].apply(formatStd), format='%Y%m%d%H%M%S.%f')
    df['time'] = dfDs
    df.index = dfDs
    return df

def processing(name,date):
    db_path = f'C:/Users/<USER>/Desktop/shfe/shfe_{date}/'
    testDB = OmmDatabase(db_path)
    # df = testDB.read_file('./', date) # 期货
    df = testDB.read_file(f'C:/Users/<USER>/Desktop/shfe/shfe_{date}/MarketDataService/default_mkt/HC/',
                          date)  # 期货行情
    df = setDtIndex(df)
    df = df[df.instrumentId==name]
    df['tV'] = df['tradedVolume'] - df['tradedVolume'].shift(1)
    df.tV.fillna(0,inplace=True)
    df['fair'] = df.apply(get_Fair,axis=1)
    df['x_pct'] = df['fair'] - df['fair'].shift(1)
    df.x_pct.fillna(0,inplace=True)
    df['Mid'] = (df['Bid1Price'] + df['Ask1Price'])/2
    df['spread'] = abs(df['Bid1Price'] - df['lastPriceOnMarket']) + abs(df['Ask1Price']- df['lastPriceOnMarket'])
    return df

def get_data(df):
    bidP = df[['Bid1Price','Bid2Price','Bid3Price','Bid4Price','Bid5Price']]
    askP = df[['Ask1Price', 'Ask2Price', 'Ask3Price', 'Ask4Price', 'Ask5Price']]
    bidV = df[['Bid1Volume','Bid2Volume','Bid3Volume','Bid4Volume','Bid5Volume']]
    askV = df[['Ask1Volume','Ask2Volume','Ask3Volume','Ask4Volume','Ask5Volume']]
    lastP =  df['lastPriceOnMarket']
    Fair = df['fair']
    Mid = df['Mid']
    x_pct = df['x_pct']
    tV = df['tV']
    Price = [bidP,askP,lastP,Fair,Mid,x_pct]
    Volume = [bidV,askV,tV]
    return Price,Volume

#%%
def get_Depth(df,k,tickN):
    # 基于历史成交量的市场深度
    # 若k=0,则只关注当前tick的成交情况
    # 若k=1,则关注当前tick以及向前一个tick的成交情况
    df['depth'] = 0
    for  i in range(k,df.shape[0]):
        temp = df.iloc[i-k:i+1]
        Price,Volume = get_data(temp)
        bidV,askV,tV = Volume[0],Volume[1],Volume[2]
        if tV.sum() == 0:
            Depth = np.nan
        else:
            bidDepth = (bidV.iloc[:,:tickN].sum(axis=1) * tV).sum()/ tV.sum()
            askDepth = (askV.iloc[:,:tickN].sum(axis=1) * tV).sum()/ tV.sum()
            Depth = bidDepth + askDepth
        df.loc[df.index[i],'depth'] = Depth

def get_Depth_Liquidity(df,k,tickN):
    # tickN代表 多少档的数据
    # k代表回溯过去多少个tick
    # 对于流动性差的合约，短期内可能没有成交量，建议放大k值
    df['depth_liquidity'] = 0
    for i in range(k,df.shape[0]):
        temp = df.iloc[i-k:i+1]
        Price,Volume = get_data(temp)
        bidV,askV,tV = Volume[0],Volume[1],Volume[2]
        if tV.sum() == 0:
            DI = np.nan
        else:
            bidDepth = (bidV.iloc[:,:tickN].sum(axis=1) * tV).sum()/ tV.sum()
            askDepth = (askV.iloc[:,:tickN].sum(axis=1) * tV).sum()/ tV.sum()
            Depth = bidDepth + askDepth
            DI = (askDepth - bidDepth) / Depth
        df.loc[df.index[i], 'depth_liquidity'] = DI

def get_Breadth(df,k):
    df['breadth'] = 0
    for i in range(k,df.shape[0]):
        temp = df.iloc[i-k:i+1]
        temp = temp[temp.tV != 0]  # 获取回溯期内存在交易量的数据
        Price, Volume = get_data(temp)
        x_pct,lastP,tV=Price[5],Price[2],Volume[2]
        Breadth = (abs(x_pct)/ (lastP*tV)).sum()/(k+1)
        df.loc[df.index[i], 'breadth'] = Breadth * 1e5

def get_Resilience(df1,k):
    df = df1.copy()
    df['resilience'] = 0
    for i in range(k,df.shape[0]):
        temp = df.iloc[i-k:i+1]
        temp = temp[temp.tV != 0]  # 获取回溯期内存在交易量的数据
        Price, Volume = get_data(temp)
        midP, lastP, tV = Price[4], Price[2], Volume[2]
        if tV.sum() == 0:
            Resilience = np.nan
        else:
            Resilience = ((midP-lastP)**2 * tV).sum()/ tV.sum()
        df.loc[df.index[i], 'resilience'] = Resilience
    return df

def get_Tightness(df,tickN=5):
    Price, Volume = get_data(df)
    bidP,askP,fair = Price[0],Price[1],Price[3]
    bidP = bidP.iloc[:,tickN-1]
    askP = askP.iloc[:,tickN-1]
    Tightness = (bidP - askP) / fair
    df['tightness'] = Tightness

def get_Immediacy(df,k,m):
    df['immediacy'] = 0
    for i in range(k,df.shape[0]):
        temp = df.iloc[i-k:i+1]
        Price, Volume = get_data(temp)
        x_pct = Price[5]
        x_pct_shift = x_pct.shift(m)
        Immediacy = pd.concat([x_pct,x_pct_shift],axis=1).iloc[m:].corr().iloc[0,1]
        df.loc[df.index[i], 'immediacy'] = Immediacy

def get_imbalance(df,tickN=5):
    Price, Volume = get_data(df)
    bidV, askV= Volume[0], Volume[1]
    BidV = bidV.iloc[:, :tickN].sum(axis=1)
    AskV = askV.iloc[:, :tickN].sum(axis=1)
    Imbalance =  (BidV - AskV) / (BidV + AskV)
    df['imbalance'] = Imbalance

def get_liquidity(df,tickN=5):
    Price, Volume = get_data(df)
    bidV, askV,tV= Volume[0], Volume[1], Volume[2]
    bidP, askP = Price[0], Price[1]
    BidV = bidV.iloc[:, :tickN].sum(axis=1)
    AskV = askV.iloc[:, :tickN].sum(axis=1)
    BidP = bidP.iloc[:,tickN-1]
    AskP = askP.iloc[:,tickN-1]
    Liquidity = (0.7*tV+0.3*(AskV+BidV))/(AskP-BidP)
    df['liquidity'] = Liquidity

def get_divergence(df1,tickN=5,k=10):
# 越密集说明越有可能成为趋势
# 不密集则更容易震荡
# 越密集 波动越大 则越可能上涨下跌,速度加快，有大的行情
# 衡量大资金的分歧情况
# 价差若在过去几个tick均出现放大的信号说明资金存在分歧，此时震荡的概率大，大概率出现反转
# 价差若在过去几个tick小于等于-6,即正常的价差，说明趋势继续，资金均认可当前走势
#两种情况
#第一，外部资金冲击
#第二，流动性较差
    df = df1.copy()
    Price, Volume = get_data(df)
    bidV, askV,tV= Volume[0], Volume[1], Volume[2]
    bidP, askP = Price[0], Price[1]
    def mid(a_list):
        a_list = np.array(a_list)
        a_list = a_list[a_list!=0]
        return a_list.mean()
    b_maxvolumeprice = (bidV.apply(lambda x: np.where(x == np.amax(x), 1, 0), axis=1) * bidP.apply(lambda x:np.array(x),axis=1)).apply(mid)
    a_maxvolumeprice = (askV.apply(lambda x: np.where(x == np.amax(x), 1, 0), axis=1) * askP.apply(lambda x:np.array(x),axis=1)).apply(mid)
    Divergence = a_maxvolumeprice - b_maxvolumeprice
    df['a_volumeprice'] = a_maxvolumeprice - a_maxvolumeprice.shift(1)
    df['a_spread'] = a_maxvolumeprice - askP.iloc[:,0]
    df['b_spread'] = bidP.iloc[:,0] - b_maxvolumeprice
    df['a-b'] = df['a_spread'] - df['b_spread']
    df['a+b'] = df['a_spread'] + df['b_spread']
    df['b_volumeprice'] = b_maxvolumeprice - b_maxvolumeprice.shift(1)
    df['divergence'] = Divergence
    df['a-b-ma'] = pd.Series.rolling(df['a-b'], window=10).sum()
    return df

def volume_information(df,tickN=5,k=10):
    #开盘后半小时的交易反映了投资者对隔夜信息的分歧度，噪声较多；
    #收盘前的交易反映出投资者对下一个交易日的预期，部分个人投资者厌恶承担日内波动，选择在临近收盘时交易
    #真正的知情交易者选择在盘中噪声较少时交易
    Open = df.loc[lambda x: (x.SystemStamp>=20210104085500000000) & (x.SystemStamp<=20210104090000000000)]
    Close = df.loc[lambda x: (x.SystemStamp>=20210104145500000000) & (x.SystemStamp<=20210104150000000000)]

def get_momentum(df,tickN=5):
    df['momentum'] = 0
    for i in range(k,df.shape[0]):
        if df.loc[df.index(i),'tV'] !=0 and df.loc[df.index(i),'x_pct'] !=0:
            nums = df.loc[df.index(i),'x_pct']

# #%%
# a = df.copy()
# fig,ax1=plt.subplots()
# ax1.plot(b['fair'],'g')
# ax2 = ax1.twinx()
# ax2.plot(b['a-b'],'b')
# # ax2.plot(df['a_spread'],'k')
# plt.show()



#%%
def run(name, tickN, k):
    df = processing(name)
    print(tickN, k)
    get_Depth(df, k, tickN)
    get_Breadth(df, k)
    get_Resilience(df, k)
    get_Tightness(df, tickN)
    get_Immediacy(df, k,1)
    get_Depth_Liquidity(df, k, tickN)
    df = df[(df.SystemStamp >= 20210104090000000000) & (df.SystemStamp <= 20210104145500000000)]
    return df
    # a = df[['spread', 'depth', 'breadth', 'resilience',
    #         'tightness', 'immediacy']]
    # a = a - a.shift(1)
    # a[['depth', 'breadth', 'resilience','tightness', 'immediacy']] = a[['depth', 'breadth', 'resilience','tightness', 'immediacy']].shift(1)
    # a = a.iloc[2:]
    # print(a.corr())





def relative_compare():
    df = pd.merge(maker,refer,on='unixStamp')

    df['relative_depth'] = df['depth_x'] / df['depth_y']
    df['relative_breadth'] = df['breadth_x'] / df['breadth_y']
    df['relative_resilience'] = df['resilience_x'] / df['resilience_y']
    df['relative_tightness'] = df['tightness_x'] / df['tightness_y']
    df['relative_immediacy'] = df['immediacy_x'] / df['immediacy_y']
    df['relative_fair'] = df['fair_x'] / df['fair_y']

    a = df[['relative_depth','relative_breadth','relative_resilience','relative_tightness'
            ,'relative_immediacy','relative_fair']]

    a = a - a.shift(1)
    a.iloc[:,:-1] = a.iloc[:,:-1].shift(1)
    a = a.iloc[2:]

    b = df[['fair_x','fair_y']]
    b = b - b.shift(1)
    s = []
    for i in range(60):
        b.fair_y = b.fair_y.shift(i)
        b = b.iloc[i:]
        s.append(b.corr().iloc[0,1])

    plt.plot(range(60),s)
    plt.show()

    c = a.tightness_x
    d = a.tightness_x.shift(1)
    e = pd.concat([c,d],axis=1)
    e.corr()

def statistics():
    upper = 0.6
    lower = 0.1
    m = 5

    up = maker.loc[lambda x:x.resilience > upper]
    low = maker.loc[lambda x:x.resilience < lower]

    up['win'] = 0
    low['win'] = 0

    for i in up.index:
        if i >= 39520:
            break
        else:
            signal1 = maker.loc[i,'fair'] - maker.loc[i-m,'fair']
            signal2 = maker.loc[i+m,'fair'] - maker.loc[i,'fair']
            up.loc[i,'win'] = np.sign(signal1*signal2)

    for i in low.index:
        if i>= 39520:
            break
        else:
            signal1 = maker.loc[i,'fair'] - maker.loc[i-m,'fair']
            signal2 = maker.loc[i+m,'fair'] - maker.loc[i,'fair']
            up.loc[i,'win'] = np.sign(signal1*signal2)