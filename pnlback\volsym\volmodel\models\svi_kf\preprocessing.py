import numpy as np
import pandas as pd
import datetime as dt
import scipy.optimize as opt
from scipy.stats import norm
from svi import fit_svi_with_ql, original_to_modified


def bsmPricer(discount, forward, volatility, strike, maturity, claimType):

    omega = None
    if str.upper(claimType) in ['C', 'CALL']:
        omega = 1.0
    elif str.upper(claimType) in ['P', 'PUT']:
        omega = -1.0
    else:
        raise Exception('Unrecognized claim type: ' + claimType)

    try:
        d1 = (np.log(forward / strike) + 0.5 * volatility * volatility * maturity) / (volatility * np.sqrt(maturity))
        d2 = d1 - volatility * np.sqrt(maturity)
        discount = discount

        return omega * discount * (forward * norm.cdf(omega * d1) - strike * norm.cdf(omega * d2))

    except:
        return None


def impVolCalculatorBrents(optionPrice, discount, forward, strike, maturity, claimType, minVol = 0.01, maxVol = 2.0):
    def rootFun(x):
        return bsmPricer(discount, forward, x, strike, maturity, claimType) - optionPrice

    assert rootFun(minVol) < 0 and rootFun(maxVol) > 0

    return opt.brentq(rootFun, minVol, maxVol)


def calculate_implied_volatilities(data):
    data = data.copy()
    data['时间'] = data['时间'].apply(lambda x: dt.datetime.strptime(x, '%Y-%m-%d %H:%M:%S'))
    data['到期日'] = data['到期日'].apply(lambda x: dt.datetime.strptime(str(x), "%Y%m%d"))

    for indx in data.index:
        data.loc[indx, '期限'] = (data.loc[indx, '到期日'].date() - data.loc[indx, '时间'].date()).days / 365

    data['价格'] = data['价格'].replace(0, np.nan)

    print((data['价格'].isna()).sum())

    df_sorted = data.sort_values(by=['代码', '时间'], ascending=[True, True])

    # 对每个合约按向前法填空值
    for value in df_sorted['代码'].unique():
        df_sorted.loc[df_sorted['代码'] == value, :] = df_sorted.loc[df_sorted['代码'] == value, :].ffill()

    # 无风险利率
    r = 0.02
    atm_strike = 2.45

    df_vols = pd.DataFrame(columns=['时间', '到期日', '期限', '远期价', '行权价', '隐含波动率'])

    for time, dfx in df_sorted.groupby('时间'):

        for maturity, dfy in dfx.groupby('期限'):

            discount = np.exp(-r * maturity)
            call_value = dfy.loc[(dfy['行权价'] == atm_strike) & (dfy['沽购'] == 'C')]['价格'].iloc[0]
            put_value = dfy.loc[(dfy['行权价'] == atm_strike) & (dfy['沽购'] == 'P')]['价格'].iloc[0]
            forward = (call_value - put_value) / discount + atm_strike

            for strike, dfz in dfy.groupby('行权价'):

                call_value = dfz.loc[dfz['沽购'] == 'C']['价格'].iloc[0]
                put_value = dfz.loc[dfz['沽购'] == 'P']['价格'].iloc[0]
                vol = None

                try:
                    if strike > atm_strike:
                        vol = impVolCalculatorBrents(call_value, discount, forward, strike, maturity, 'C')
                    else:
                        vol = impVolCalculatorBrents(put_value, discount, forward, strike, maturity, 'P')
                except:
                    pass

                df_vols.loc[len(df_vols)] = [time, dfy['到期日'].iloc[0], maturity, forward, strike, vol]

    return df_vols


def calculate_parameters(df_vols, m =None, sigma = None):
    df_vols = df_vols.copy()
    df_vols['时间'] = df_vols['时间'].apply(lambda x: dt.datetime.strptime(x, '%Y-%m-%d %H:%M:%S'))
    df_vols['到期日'] = df_vols['到期日'].apply(lambda x: dt.datetime.strptime(str(x), "%Y-%m-%d"))

    df_params = pd.DataFrame(
        columns=['时间', '到期日', 'a', 'b', 'rho', 'm', 'sigma', 'rms', 'x_a', 'x_s', 'x_c', 'x_l', 'x_r'])

    for key, dfx in df_vols.dropna().groupby(['时间', '到期日']):
        #print(key)
        #ks = np.log(dfx['行权价'] / dfx['远期价'])
        #vars = dfx['隐含波动率'] ** 2

        nMaturity = int(dfx['期限'].iloc[0] * 365)
        forward = dfx['远期价'].iloc[0]
        strikes = dfx['行权价'].values
        vols = dfx['隐含波动率'].values

        try:
            popt = fit_svi_with_ql(nMaturity, forward, strikes, vols, m, sigma)
            modified = original_to_modified(popt[0], popt[1], popt[2], popt[3], popt[4])
            df_params.loc[len(df_params)] = [key[0], key[1],
                                             popt[0], popt[1], popt[2], popt[3], popt[4], popt[5],
                                             modified[0], modified[1], modified[2], modified[3], modified[4]
                                             ]
        except:
            df_params.loc[len(df_params)] = [key[0], key[1]] + [np.nan]*11

    return df_params


if __name__ == '__main__':

    # 1. 计算所有的隐含波动率
    # data = pd.read_csv('data/option_510050.csv', index_col=0)
    # df_vols = calculate_implied_volatilities(data)
    # df_vols.to_csv('data/implied_volatilities.csv')

    # 2. 进行SVI参数拟合
    # df_vols = pd.read_csv('data/implied_volatilities.csv', index_col=0)
    # df_params = calculate_parameters(df_vols)
    # df_params.to_csv('data/svi_parameters.csv')

    df_params = pd.read_csv('data/svi_parameters.csv', index_col=0)

    # 3. 固定参数进行拟合
    # 3.1 期限1
    # df_vols = pd.read_csv('data/implied_volatilities.csv', index_col=0)
    # df_vols = df_vols[(df_vols['到期日'] == '2024-09-20') & (df_vols['时间'].str.startswith('2024-08-27'))]
    # m = 0.7805
    # sigma = 1.6663
    # df_params = calculate_parameters(df_vols, m, sigma)
    # df_params.to_csv('data/svi_parameters_20240920_0827.csv')
    #
    # # 3.2 期限2
    # df_vols = pd.read_csv('data/implied_volatilities.csv', index_col=0)
    # df_vols = df_vols[(df_vols['到期日'] == '2024-12-20') & (df_vols['时间'].str.startswith('2024-08-27'))]
    # m = 0.6892
    # sigma = 0.2106
    # df_params = calculate_parameters(df_vols, m, sigma)
    # df_params.to_csv('data/svi_parameters_20241220_0827.csv')
    #
    # # 3.3 期限3
    # df_vols = pd.read_csv('data/implied_volatilities.csv', index_col=0)
    # df_vols = df_vols[(df_vols['到期日'] == '2025-03-21') & (df_vols['时间'].str.startswith('2024-08-27'))]
    # m = -0.0132
    # sigma = 0.0123
    # df_params = calculate_parameters(df_vols, m, sigma)
    # df_params.to_csv('data/svi_parameters_20250321_0827.csv')
