from influxdb import InfluxDBClient
from OmmDatabase import OmmDatabase
import datetime
import time
import numpy as np
import pandas as pd

# =============================================================================
# #%% 
# # 基础操作
# client = InfluxDBClient('localhost',8086,'','','')
# 
# print (client.get_list_database()) # 显示所有数据库名称
# client.create_database('testdb') # 创建数据库
# client.drop_database('testdb') # 删除数据库
# 
# result1 = client.query('show measurements;') # 显示数据库中的表
# # 添加数据
# current_time = datetime.datetime.utcnow().isoformat("T")
# body = [
#     {
#         "measurement": "students",
#         "time": current_time,
#         "tags": {
#             "class": 1
#         },
#         "fields": {
#             "name": "Hyc",
#             "age": 3
#         },
#     }
# ]
# 
# res = client.write_points(body)
# client.query("drop measurement students") # 删除表
# =============================================================================

#%%
client = InfluxDBClient('localhost',8086,'','','')
client.create_database('test')

client = InfluxDBClient('localhost',8086,'','','test')
client = InfluxDBClient('***********',8989,'','','testbase') #大商所
#%%
date = '2021-07-20'
db_path = './'
testDB = OmmDatabase(db_path)

#%%
# MarketDataService
df30 = testDB.read_file('./MarketDataService/default_mkt/PG/', date)   
df30['UnixStamp'] = df30['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
df30['UnixStamp'] = df30['UnixStamp'] * 10**9
df30['UnixStamp'] = df30['UnixStamp'].apply(lambda x: int(x))
# df30[df30.isnull().T.any()]
df30.fillna(-1, inplace = True)


#%%
t = time.time()
json_body = []
for i, row in df30[:].iterrows():
    
    current_time = row['UnixStamp']
    
    measurement = 'testdalian'
    
    a_p1 = row['Ask1Price']
    a_v1 = int(row['Ask1Volume'])
    a_p2 = row['Ask2Price']
    a_v2 = int(row['Ask2Volume'])    
    a_p3 = row['Ask3Price']
    a_v3 = int(row['Ask3Volume'])        
    a_p4 = row['Ask4Price']
    a_v4 = int(row['Ask4Volume'])        
    a_p5 = row['Ask5Price']
    a_v5 = int(row['Ask5Volume'])    
    
    b_p1 = row['Bid1Price']
    b_v1 = int(row['Bid1Volume'])
    b_p2 = row['Bid2Price']
    b_v2 = int(row['Bid2Volume'])    
    b_p3 = row['Bid3Price']
    b_v3 = int(row['Bid3Volume'])    
    b_p4 = row['Bid4Price']
    b_v4 = int(row['Bid4Volume'])    
    b_p5 = row['Bid5Price']
    b_v5 = int(row['Bid5Volume']) 
    
    exchange_t = 0    
    insid_md = row['instrumentId']
    last_p = float(row['lastPriceOnMarket'])
    local_t = int(row['UnixStamp'])
    lower_limit_p = row['lowerLimit']
    preclose_p = 0
    presettle_p = 0
    turnover = float(row['turnover'])
    upper_limit_p = row['upperLimit']
    v = row['tradedVolume']
       
    body = {
            "measurement": measurement, 
            "time": current_time, 
            "tags": {
                "insid_md": insid_md
            }, 
            "fields": {
                "a_p1": a_p1, 
                "a_p2": a_p2,                 
                "a_p3": a_p3,                 
                "a_p4": a_p4,                 
                "a_p5": a_p5,                 
                "a_v1": a_v1,                 
                "a_v2": a_v2,                    
                "a_v3": a_v3,                    
                "a_v4": a_v4,                    
                "a_v5": a_v5,                    
                "b_p1": b_p1, 
                "b_p2": b_p2,                 
                "b_p3": b_p3,                 
                "b_p4": b_p4,                 
                "b_p5": b_p5,                 
                "b_v1": b_v1,                 
                "b_v2": b_v2,                    
                "b_v3": b_v3,                    
                "b_v4": b_v4,                    
                "b_v5": b_v5, 
                "exchange_t": exchange_t,               
                "last_p": last_p,
                "local_t": local_t,
                "lower_limit_p": lower_limit_p,
                "preclose_p": preclose_p,
                "presettle_p": presettle_p,
                "turnover": turnover,
                "upper_limit_p": upper_limit_p,
                "v": v
            }, 
        }
    
    
    json_body.append(body)
print(time.time()-t)
#%%    
res = client.write_points(json_body, batch_size = 10000)
#res = client.write_points([j], batch_size = 1)
print(time.time()-t)

#%%
result = client.query("select * from HC") 


#%%
# 输出
points = result.get_points()
list1 = []
for d in points:
    list1.append(d)
 
#%%
# df30 = pd.DataFrame()    
 

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    