import math
from data_process import FactorDataGenerator, BacktestDataGenerator
from model import OLSModel, RandomForestModel
import logging
import numpy as np
import pandas as pd

class Backtester:
    def __init__(self, data_dir, future, model, threshold=0.4, start_balance=5e5, fee=0.23e-5, position_limit=5, split_method=None):
        self.data_dir = data_dir
        self.future = future
        self.model = model
        self.threshold = threshold
        self.start_balance0 = start_balance
        self.start_balance = start_balance
        self.fee = fee
        self.position_limit = position_limit  # 持仓上限
        self.position = 0  # 当前持仓
        self.results = pd.Series(dtype=float)  # 回测结果
        self.trade_records = []  # 交易记录
        self.split_method = split_method

    def load_data(self):
        # 生成回测的基本行情数据
        self.generator_bt = BacktestDataGenerator(data_dir=self.data_dir, future=self.future)
        self.generator_bt.load_data()
        self.generator_bt.generate_backtest_data()
        self.daily_data = self.generator_bt.get_data_for_bt()

        # 生成回测上数据集的因子数据
        self.generator = FactorDataGenerator(data_dir=self.data_dir, future=self.future)
        self.generator.load_data()
        self.generator.generate_factor_data(use_similar=True, split_method=self.split_method) 
        self.factor_data = self.generator.daily_standmd_list


    def run_backtest(self):
        if len(self.daily_data) != len(self.factor_data):
            logging.error("Data length mismatch between daily_data and factor_data.")
            return

        #   如果是按照day来划分，则默认后20%的天为测试集 并进行backtest,否则就是用新的数据，此时之前的dir_data应该为新数据所在的路径
        if self.split_method == 'day':
            number_of_days = len(self.factor_data)
            test_days = max(math.floor(number_of_days * 0.2), 1)
        else: test_days = 0
            
        for data, factors in zip(self.daily_data[test_days:], self.factor_data[test_days:]):
            print('predict day')
            feature_columns = factors[[col for col in factors.columns if col.startswith('Feature')]]
            na_row_indices = feature_columns[feature_columns.isna().any(axis=1)].index.tolist()
            feature_cleaned = feature_columns.drop(na_row_indices).reset_index(drop=True)
            data_cleaned = data.drop(na_row_indices).reset_index(drop=True)

            try:
                predict_ret = self.model.predict(feature_cleaned)
            except Exception as e:
                logging.error(f"Model prediction failed: {e}")
                continue

            ind_results,t_results = self._execute_trades(data_cleaned, predict_ret)
            self.results=pd.concat([self.results,pd.Series(index=t_results,data=ind_results)])

    def _execute_trades(self, data_cleaned, predict_ret):
        ind_results = []
        t_results = []
        for i in range(0, len(predict_ret)):
            row = data_cleaned.iloc[i]
            pd_ret = predict_ret[i]
            mid_price = (row['AskPrice1'] + row['BidPrice1']) / 2
            ft_mid = mid_price * (1 + pd_ret)

            # 开多仓条件
            if self.position < self.position_limit and ft_mid > row['AskPrice1'] + self.threshold:
                self.position += 1
                trade_cost = row['AskPrice1'] * 200 * (1 + self.fee)
                self.start_balance -= trade_cost
                self.trade_records.append({
                    'type': 'long',
                    'price': row['AskPrice1'],
                    'cost': trade_cost,
                    'position': self.position,
                    'TimeStamp': row['TimeStamp'],
                    'mid_price':mid_price
                })

            # 开空仓条件
            elif self.position > -self.position_limit and ft_mid < row['BidPrice1'] - self.threshold:
                self.position -= 1
                trade_cost = row['BidPrice1'] * 200 * (1 - self.fee)
                self.start_balance += trade_cost
                self.trade_records.append({
                    'type': 'short',
                    'price': row['BidPrice1'],
                    'cost': trade_cost,
                    'position': self.position,
                    'TimeStamp': row['TimeStamp'],
                    'mid_price':mid_price

                })

            # 计算持仓盈亏
            hold_balance = self.start_balance + self.position * mid_price * 200

            # 最后5min不进行交易
            if i > len(predict_ret) - 600:
                if self.position > 0: hold_balance = hold_balance + self.position*row['BidPrice1']
                elif self.position < 0: hold_balance = hold_balance + self.position*row['AskPrice1']
                
                self.position = 0
                ind_results.append(hold_balance)
                t_results.append(row['TimeStamp'])

                break
            
            ind_results.append(hold_balance)
            t_results.append( row['TimeStamp'])
        self.start_balance = hold_balance

        return ind_results,t_results

    def analyze_results(self):
        if len(self.results)==0:
            logging.warning("No results to analyze.")
            return
        final_balance = self.results.iloc[-1]
        total_return = (final_balance - self.start_balance0) / self.start_balance0
        trade_records = pd.DataFrame(self.trade_records)
        trade_records.to_csv('./data/backtest/trade_records.csv', index=False)
        logging.info(f"Final Balance: {final_balance}, Total Return: {total_return * 100:.2f}%")


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    
    # 划分数据的方式：
    # 1.如果用模型的测试集进行回测，则split_method定义为‘day’,此时data_dir定义为和训练集相同的目录
    # 2.如果有另外的数据进行回测，则定义为None, 需要用新的数据来backtest，此时data_dir定义为新的backtest目录(推荐)
    split_method = None #'day'
    model = OLSModel(data_path='./data/featuresIC.csv')
    model.run(test_set=split_method)

    backtester = Backtester(data_dir='./data/backtest', future='IC', model=model, split_method=split_method)
    backtester.load_data()
    backtester.run_backtest()
    backtester.analyze_results()



    
