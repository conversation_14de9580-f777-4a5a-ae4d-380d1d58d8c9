#!/usr/bin/env python
# -*- coding:utf-8 -*-
from os import path
from sklearn.decomposition import PCA
import pandas as pd
import numpy as np

delres = pd.read_csv(open(path.dirname(__file__) + '//result.csv'))  # 上交所做市商ETF


# delresPCA = np.log(delres[['VOLUME', 'UNIT_TOTAL','money']])
delres['sum'] = delres['shuhui'] - delres['shengou']
# delresPCA = delresPCA.join(delres[['dvolP', 'svol']]).dropna()

# from sklearn.preprocessing import StandardScaler
#
# stdsc=StandardScaler()
#
# delresPCA=stdsc.fit_transform(delres[['VOLUME', 'UNIT_TOTAL','dvolP', 'svol', 'sum']].dropna())


# 导入归一化方法
from sklearn.preprocessing import MinMaxScaler
mms = MinMaxScaler()

# 调用方法对数据进行归一化处理

delresPCA = mms.fit_transform(delres[['VOLUME', 'UNIT_TOTAL','dvolP', 'svol', 'sum']].dropna())


# delresPCA=delres[['VOLUME', 'UNIT_TOTAL','dvolP', 'svol', 'sum']]
pca = PCA()  # 保留所有成分
pca.fit(delresPCA)
k = pca.components_  # 返回模型的各个特征向量
# n=pca.singular_values_
p = pca.explained_variance_ratio_  # 返回各个成分各自的方差百分比(也称贡献率）
pca = PCA(5)  # 选取累计贡献率大于80%的主成分（3个主成分）
pca.fit(delresPCA)
low_d = pca.transform(delresPCA)  # 降低维度
#
# for i in range(df1.shape[1]):
#     if 'delta' in str(df1.columns.tolist()[i]):
#         print(str(df1.columns.tolist()[i]))
#         df1[df1.columns.tolist()[i]].plot(secondary_y=[str(df1.columns.tolist()[i])])
#     else:
#         df1[df1.columns.tolist()[i]].plot()

# df1.plot()
# plt.show()

print(low_d)
print(p)
print(k)
pd.DataFrame(low_d).to_csv('low_d.csv')
pd.DataFrame(p).to_csv('p.csv')
pd.DataFrame(k).to_csv('k.csv')
