# -*- coding:utf-8 -*-

from datetime import datetime
import os
import shutil
import sys

import docx

codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
sys.path.append(codepath)
from MMaker import sendmail


def sendpic():
    # codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))

    if datetime.now().weekday() >= 5:
        print("今天是周末")
        os._exit(0)

    # 邮件接收人，用列表保存，可以添加多个
    receivers1 = ["<EMAIL>", ]  # 接收邮箱
    receivers2 = ["<EMAIL>", "<EMAIL>"]  # 提醒邮箱

    dt = datetime.now()
    dt = datetime.strftime(dt, "%Y-%m-%d")
    # dt = "2019-08-19"
    copyPath = u'D:\\onedrive\\中泰衍生\\周汇报\\50ETF期权日报(%s).docx' % dt
    pic = '日报(%s).docx' % dt
    try:
        shutil.copy(copyPath, pic)
        # 获取文档对象
        file = docx.Document(pic)
        # print("段落数:"+str(len(file.paragraphs)))#段落数为13，每个回车隔离一段

        # 输出每一段的内容
        content = ""
        for para in file.paragraphs:
            content = content + '\t' + para.text + '\r\n'

        subject = u'#IFTTT 50ETF%s盘后复盘' % dt  # input(u"{'请输入邮件主题：'}")
        try:
            sendmail.sendmail(receivers2, subject, content, pic)
        except:
            sendmail.sendmail(receivers2, u'#IFTTT 损益邮件发生错误', u'检查图片', '')

        os.remove(pic)
    except:
        print(u'日报未更新')
        sendmail.sendmail(receivers2, u'#IFTTT 日报未更新', u'检查内网文件错误', '')


if __name__ == '__main__':
    sendpic()
