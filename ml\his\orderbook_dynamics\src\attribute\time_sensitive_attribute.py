"""
Time sensitive attribute implementation for orderbook dynamics.
These attributes depend on the time sequence of order book updates.
"""
from typing import Callable, TypeVar, List, Dict, Optional
import numpy as np
import time
from datetime import timedelta
from ..models import Cell, OrderBook, OpenBookMsg, Side
from .basic_attribute import BasicSet


T = TypeVar('T')


class TimeSensitiveAttribute:
    """
    Time sensitive attribute for order book analysis.
    Applies a function to a trail of OpenBookMsg to produce a Cell[T].
    """
    def __init__(self, func: Callable[[List[OpenBookMsg]], Cell]):
        self.func = func

    def __call__(self, orders_trail: List[OpenBookMsg]) -> Cell:
        """Apply the function to the order book trail."""
        return self.func(orders_trail)

    def map(self, func: Callable[[T], T]) -> 'TimeSensitiveAttribute':
        """Apply a function to the result of this attribute."""
        original_func = self.func
        return TimeSensitiveAttribute(lambda trail: original_func(trail).map(func))


class TimeSensitiveSet:
    """
    Set of time sensitive order book attributes.
    """
    def __init__(self, time_window_ms: int = 1000, basic_set: BasicSet = None):
        """
        Initialize with time window in milliseconds.
        
        Args:
            time_window_ms: Time window in milliseconds to consider for time-sensitive attributes
            basic_set: BasicSet instance to use for calculations
        """
        self.time_window_ms = time_window_ms
        self.basic_set = basic_set or BasicSet()

    def trail(self, current: List[OpenBookMsg], order: OpenBookMsg) -> List[OpenBookMsg]:
        """
        Update the trail with a new order.
        
        Args:
            current: Current trail of orders
            order: New order to add to the trail
            
        Returns:
            Updated trail with orders within the time window
        """
        cutoff_time = order.source_time - self.time_window_ms
        filtered_trail = [o for o in current if o.source_time >= cutoff_time]
        return filtered_trail + [order]

    def _arrival_rate(self, filter_func: Callable[[OpenBookMsg], bool]) -> TimeSensitiveAttribute:
        """
        Calculate the arrival rate of orders matching a filter.
        
        Args:
            filter_func: Function to filter orders
            
        Returns:
            TimeSensitiveAttribute for arrival rate
        """
        def calc_arrival_rate(orders_trail: List[OpenBookMsg]) -> Cell:
            if not orders_trail:
                return Cell.empty()
                
            head = orders_trail[0]
            last = orders_trail[-1]
            
            time_diff = last.source_time - head.source_time
            if time_diff == 0:
                return Cell.empty()
                
            matching_orders = sum(1 for order in orders_trail if filter_func(order))
            return Cell.value(matching_orders / time_diff)
            
        return TimeSensitiveAttribute(calc_arrival_rate)

    def bid_arrival_rate(self) -> TimeSensitiveAttribute:
        """Calculate the arrival rate of bid orders."""
        return self._arrival_rate(lambda order: order.side == Side.BID)

    def ask_arrival_rate(self) -> TimeSensitiveAttribute:
        """Calculate the arrival rate of ask orders."""
        return self._arrival_rate(lambda order: order.side == Side.ASK)
        
    def order_intensity(self) -> TimeSensitiveAttribute:
        """Calculate the total order arrival rate."""
        def calc_order_intensity(orders_trail: List[OpenBookMsg]) -> Cell:
            if not orders_trail:
                return Cell.empty()
                
            head = orders_trail[0]
            last = orders_trail[-1]
            
            time_diff = last.source_time - head.source_time
            if time_diff == 0:
                return Cell.empty()
                
            return Cell.value(len(orders_trail) / time_diff)
            
        return TimeSensitiveAttribute(calc_order_intensity)
        
    def buy_sell_ratio(self) -> TimeSensitiveAttribute:
        """Calculate the ratio of buy orders to sell orders."""
        def calc_buy_sell_ratio(orders_trail: List[OpenBookMsg]) -> Cell:
            if not orders_trail:
                return Cell.empty()
                
            buy_orders = sum(1 for order in orders_trail if order.side == Side.BID)
            sell_orders = sum(1 for order in orders_trail if order.side == Side.ASK)
            
            if sell_orders == 0:
                return Cell.empty()
                
            return Cell.value(buy_orders / sell_orders)
            
        return TimeSensitiveAttribute(calc_buy_sell_ratio)
        
    def price_impact(self) -> TimeSensitiveAttribute:
        """
        Calculate the price impact of orders.
        
        The price impact measures how much the price moves in response to orders.
        """
        def calc_price_impact(orders_trail: List[OpenBookMsg]) -> Cell:
            if len(orders_trail) < 2:
                return Cell.empty()
                
            # Create a simplified order book to track price changes
            symbol = orders_trail[0].symbol
            order_book = OrderBook(symbol)
            
            # Track price changes
            price_changes = []
            
            for i, order in enumerate(orders_trail):
                # Update order book
                prev_mid_price = order_book.mid_price().get_or_else(None)
                order_book.update(order)
                new_mid_price = order_book.mid_price().get_or_else(None)
                
                if prev_mid_price is not None and new_mid_price is not None:
                    price_change = abs(new_mid_price - prev_mid_price)
                    if price_change > 0:
                        price_changes.append(price_change)
            
            if not price_changes:
                return Cell.empty()
                
            # Calculate average price impact
            return Cell.value(np.mean(price_changes))
            
        return TimeSensitiveAttribute(calc_price_impact)
        
    def time_weighted_average_price(self) -> TimeSensitiveAttribute:
        """
        Calculate the time-weighted average price (TWAP).
        
        TWAP weights each price by the time it was active in the order book.
        """
        def calc_twap(orders_trail: List[OpenBookMsg]) -> Cell:
            if len(orders_trail) < 2:
                return Cell.empty()
                
            # Create order book and track mid prices
            symbol = orders_trail[0].symbol
            order_book = OrderBook(symbol)
            
            weighted_prices = []
            weights = []
            last_time = orders_trail[0].source_time
            
            for order in orders_trail:
                # Update order book
                order_book.update(order)
                mid_price = order_book.mid_price()
                
                if mid_price.is_value:
                    # Calculate time delta since last update
                    time_delta = order.source_time - last_time
                    if time_delta > 0:
                        weighted_prices.append(mid_price.get() * time_delta)
                        weights.append(time_delta)
                
                last_time = order.source_time
            
            if not weights:
                return Cell.empty()
                
            # Calculate TWAP
            return Cell.value(sum(weighted_prices) / sum(weights))
            
        return TimeSensitiveAttribute(calc_twap)
        
    def order_imbalance_ratio(self) -> TimeSensitiveAttribute:
        """
        Calculate the order imbalance ratio.
        
        This measures the imbalance between buy and sell volume.
        """
        def calc_order_imbalance(orders_trail: List[OpenBookMsg]) -> Cell:
            if not orders_trail:
                return Cell.empty()
                
            buy_volume = sum(order.volume for order in orders_trail if order.side == Side.BID)
            sell_volume = sum(order.volume for order in orders_trail if order.side == Side.ASK)
            
            total_volume = buy_volume + sell_volume
            if total_volume == 0:
                return Cell.empty()
                
            return Cell.value((buy_volume - sell_volume) / total_volume)
            
        return TimeSensitiveAttribute(calc_order_imbalance)
        
    def price_volatility(self) -> TimeSensitiveAttribute:
        """
        Calculate the price volatility within the time window.
        
        This measures how much the price fluctuates over time.
        """
        def calc_volatility(orders_trail: List[OpenBookMsg]) -> Cell:
            if len(orders_trail) < 2:
                return Cell.empty()
                
            # Create order book and track mid prices
            symbol = orders_trail[0].symbol
            order_book = OrderBook(symbol)
            
            prices = []
            
            for order in orders_trail:
                # Update order book
                order_book.update(order)
                mid_price = order_book.mid_price()
                
                if mid_price.is_value:
                    prices.append(mid_price.get())
            
            if len(prices) < 2:
                return Cell.empty()
                
            # Calculate standard deviation as a measure of volatility
            return Cell.value(np.std(prices))
            
        return TimeSensitiveAttribute(calc_volatility) 