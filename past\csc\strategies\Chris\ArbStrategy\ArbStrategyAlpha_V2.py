# -*- coding: utf-8 -*-
import re
import pandas as pd
import datetime
import numpy as np
from datetime import  time as dtime
import codecs
import csv
import os

from cqtrade.strategy.template import BaseStrategy,EXCHANGE_NAME_TO_ENUM
from cqtrade.trade.model import SendOrderData, OrderData, SendOrderResponse
from cqtrade.trade.constant import OrderDirection
from cqtrade.trade.model import RealDealHisData, RealDealHisResponse,SendOrderData
from cqtrade.trade.constant import FUTURE_PORTFOLIO_TYPE_KEY, FuturePortfolioType

class ArbStrategyAlpha(BaseStrategy):
    """"""

    #author = ""
    #'SP l2209&l2301.DCE'
    #策略账号(每天会改变，需要手动修改)
    ice_algoid = 1204280031
    vt_symbol = 'SP pp2209&pp2301.DCE'
    lots = 1
    max_QT = 300 #单边最大交易量
    max_pos = 15
    order_type= 1  #1Arb 2Swap
    OC_flag = True
    start_pos=0
    pos=0
    path = 50
    grid_start = 2
    grid_interval = 1
    grid_layer = 5
    active_limit_orders = {} #系统返回发单成功的orders
    send_orders = []  #自身维护的orders
    cancel_count = 0
    QT_buy = 0
    QT_sell = 0

    parameters = [
        'vt_symbol',
        'lots',  
        'order_type', #套利或者互换单
        'OC_flag', #开或者平
        'start_pos',
        'max_pos',
        'grid_start',
        'grid_interval',
        'grid_layer',
        'path',
        'min_std',
        'open_std',
        'close_std',
        'max_QT',
        'ice_algoid',
    ]

    variables = [
        'pos',
        'QT_buy',
        'QT_sell'
    ]
    
    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.trade_price = 0
        self.last_tick = None
        self.buy_price = 0
        self.short_price = 0
        self.pricetick = self.get_tick_size()
        #print(self.pricetick)
        self.pos = self.start_pos #初始化基差敞口
        self.last_pos=0
        self.MMTrading = False
        self.end_flag = False
        self.QT_buy=0
        self.QT_sell=0
        self.basislist = np.zeros(99999)
        self.subscribe_tick(self.vt_symbol)
        self.log_info('策略初始化')

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.log_info("策略启动")
        self.subscribe_tick(self.vt_symbol)
        self.mCount = 0
        self.send_orders=[]
        self.active_limit_orders = {}
        # self.oper_id = "7127"
        # self.deal_ret = []
        # start_date = (datetime.datetime.now()).strftime("%Y%m%d")
        # end_date = start_date
        # self.log_info(f"查询历史流水：oper_id={self.oper_id}, start_date={start_date}, end_date={end_date}")
        # self.get_real_deal_his(oper_id=self.oper_id, start_date=start_date, end_date=end_date)

    
    def process_tick(self,tick): #处理非五档行情的tick数据
        ts = self.pricetick
        if len(tick.bid_prices) != 5 or tick.bid_prices[1] == 0: #非五档行情
            bid_price_1 = tick.bid_prices[0]
            bid_price_2 = tick.bid_prices[0] - ts
            bid_price_3 = tick.bid_prices[0] - 2 * ts
            bid_price_4 = tick.bid_prices[0] - 3 * ts
            bid_price_5 = tick.bid_prices[0] - 4 * ts
            ask_price_1 = tick.ask_prices[0]
            ask_price_2 = tick.ask_prices[0] + ts
            ask_price_3 = tick.ask_prices[0] + 2 * ts
            ask_price_4 = tick.ask_prices[0] + 3 * ts
            ask_price_5 = tick.ask_prices[0] + 4 * ts
            tick.bid_prices = [bid_price_1,bid_price_2,bid_price_3,bid_price_4,bid_price_5]
            tick.ask_prices = [ask_price_1,ask_price_2,ask_price_3,ask_price_4,ask_price_5]
        tick.bid_price_1,tick.bid_price_2,tick.bid_price_3,tick.bid_price_4,tick.bid_price_5 = tick.bid_prices
        tick.ask_price_1,tick.ask_price_2,tick.ask_price_3,tick.ask_price_4,tick.ask_price_5 = tick.ask_prices       
        tick.bid_volume_1,tick.bid_volume_2,tick.bid_volume_3,tick.bid_volume_4,tick.bid_volume_5 = tick.bid_volumes
        tick.ask_volume_1,tick.ask_volume_2,tick.ask_volume_3,tick.ask_volume_4,tick.ask_volume_5 = tick.ask_volumes 
        tick.last_price = tick.last  
        return tick  
    
    def get_tick_size(self):
        df = pd.read_csv('Y:/YSP-NFS/cscquant-pythonplugin/cscquant/zhanghongchi/.trader/config.csv',index_col=0,error_bad_lines=False)
        config = df.to_dict()
        symbol,exchange = self.vt_symbol.split('.')
        text_start = symbol.find(' ')
        text_end = re.search(r'\d',symbol).start()
        contract = symbol[text_start+1:text_end].upper()
        exchange = exchange.upper()
        if exchange == 'ZCE':
            exchange = 'CZC'
        Ticker = contract +'.' + exchange
        pricetick = config['pricetick'][Ticker]
        return pricetick
    def on_tick(self, tick):
        self.mCount += 1
        ts = self.pricetick
        tick = self.process_tick(tick)
        if tick.bid_price_1 == tick.ask_price_1:
            return
        lots = self.lots
        self.MMTrading = self.on_time()
        orders = list(self.active_limit_orders.items())

        #计算Alpha因子
        if self.mCount > self.path:
            MA = np.mean(self.basislist[self.mCount - self.path:self.mCount])
            std = np.std(self.basislist[self.mCount - self.path:self.mCount])
            std = max(std,self.min_std*ts) 
        else:
            MA = 0.5*(tick.bid_price_1 + tick.ask_price_1)
            std = self.min_std
        
        for vt_orderid, order in orders:
            if order.price==tick.bid_price_1 and order.direction.value in ['BuyOpen','BuyClose']:
                if self.pos > self.max_pos:
                    self.cancel_order(vt_orderid)
                    continue
                elif self.pos <0 and order.price > MA - std * self.close_std:
                    self.cancel_order(vt_orderid)
                    continue
                elif self.pos >=0 and order.price > MA - std * self.open_std:
                    self.cancel_order(vt_orderid)
                    continue
                
            if order.price==tick.ask_price_1 and order.direction.value in ['SellOpen','SellClose']:
                if self.pos < -self.max_pos:
                    self.cancel_order(vt_orderid)
                    continue
                elif self.pos >0 and order.price < MA + std * self.close_std:
                    self.cancel_order(vt_orderid)
                    continue
                elif self.pos <=0 and order.price < MA + std * self.open_std:
                    self.cancel_order(vt_orderid)   
                    continue

        if self.MMTrading: #规定报价时间才报价
             quote_prices = [order.price for order in self.send_orders]  
             # 开始铺单
             my_bid_1 = np.floor((tick.bid_price_1 - (self.grid_start-1)*ts)/self.grid_interval/ts)*self.grid_interval*ts
             my_ask_1 = np.ceil((tick.ask_price_1 + (self.grid_start-1)*ts)/self.grid_interval/ts)*self.grid_interval*ts
             for i in range(self.grid_layer):
                my_bid = my_bid_1 - i * self.grid_interval * ts
                my_ask = my_ask_1 + i * self.grid_interval * ts 
                if my_bid not in quote_prices and self.pos < self.max_pos:
                    self.buy(self.vt_symbol,my_bid,lots,'MM')
                if my_ask not in quote_prices and self.pos > -self.max_pos:
                    self.short(self.vt_symbol,my_ask,lots,'MM')      
             #盘口有edge直接铺单
             bid1_orders = np.sum([order.price == tick.bid_price_1 for order in self.send_orders]) 
             ask1_orders = np.sum([order.price == tick.ask_price_1 for order in self.send_orders]) 
             if self.pos >-self.maxPos and self.pos < self.maxPos and self.mCount > self.path:
                if bid1_orders==0 and self.pos>=0 and tick.bid_price_1 <= MA - std*self.open_std:
                    self.buy(self.vt_symbol,tick.bid_price_1,lots,'MM')               
                if bid1_orders==0 and self.pos<0 and tick.bid_price_1 <= MA - std*self.close_std:
                    self.buy(self.vt_symbol,tick.bid_price_1,lots,'MM')
                if ask1_orders==0 and self.net<=0 and tick.ask_price_1 >= MA + std*self.open_std:
                    self.short(self.vt_symbol,tick.ask_price_1,lots,'MM')
                if ask1_orders==0 and self.net>0 and tick.ask_price_1 >= MA + std*self.close_std:
                    self.short(self.vt_symbol,tick.ask_price_1,lots,'MM')              
        # self.on_end(tick)
        self.last_pos = self.pos
        self.last_tick = tick
        
    def sendorder(self,ice_algoid,order,comments):
        if self.order_type==1:
            self.send_order(ice_algoid, order,comments,kwargs={FUTURE_PORTFOLIO_TYPE_KEY:FuturePortfolioType.ARBITRAGE})
        if self.order_type==2:
            self.send_order(ice_algoid, order,comments,kwargs={FUTURE_PORTFOLIO_TYPE_KEY:FuturePortfolioType.EXCHANGE})
        
        self.send_orders.append(order) #本地订单维护
        # self.log_info(f"发出下单指令，方向:{order.direction.value}, 价格:{order.price},数量:{order.quantity},发单时间：{datetime.datetime.now()}")

    def buy(self,vt_symbol,price,qty,comments): #买
        symbol, exchange = vt_symbol.split('.')
        exchange = EXCHANGE_NAME_TO_ENUM[exchange]
        order = SendOrderData(
            symbol=symbol,
            exchange=exchange,
            direction=OrderDirection.BUY_OPEN if self.OC_flag else OrderDirection.BUY_CLOSE,
            quantity=qty,
            price=price
        )
        if self.QT_buy <= self.max_QT:
            self.sendorder(self.ice_algoid, order,comments)
        else:
            self.log_info('买成交量超限！')

    def short(self,vt_symbol,price,qty,comments): #卖
        symbol, exchange = vt_symbol.split('.')
        exchange = EXCHANGE_NAME_TO_ENUM[exchange]
        order = SendOrderData(
            symbol=symbol,
            exchange=exchange,
            direction=OrderDirection.SELL_OPEN if self.OC_flag else OrderDirection.SELL_CLOSE,
            quantity=qty,
            price=price
        )
        if self.QT_sell <= self.max_QT:
            self.sendorder(self.ice_algoid, order,comments)
        else:
            self.log_info('卖成交量超限！')

    def cancel_order(self,orderid):
        self.cancel_one_order_id(self.ice_algoid,orderid)
        order = self.active_limit_orders[orderid]
        order_price = order.price
        order_direction = order.direction
        # for _order in self.send_orders: #一旦撤单，则本地维护的同价位同方向的单全部撤掉
        #     if _order.price == order_price and _order.direction == order_direction:
        #         self.send_orders.remove(_order)
        # self.log_info(f"发出撤单指令，方向:{order.direction.value}, 价格:{order.price},数量:{order.quantity},信息：{orderid},撤单时间：{datetime.datetime.now()}")

    def cancel_signal(self,signal): #暂时先不要用
        self.cancel_one_signal_id(self.ice_algoid,signal)

    def cancel_all(self):
        orderids = list(self.active_limit_orders.keys())
        for orderid in orderids:
            self.cancel_order(orderid)


    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.log_info("策略停止！")

    def on_pause(self):   
        self.log_info("策略暂停，撤单！") 
        self.cancel_all()

    def on_end(self,tick): #收盘清仓,只发一次对价清仓指令
        dt = datetime.datetime.now()
        if  ((dt.time() > dtime(22, 55) and dt.time() < dtime(23, 00)) \
            or (dt.time() > dtime(11, 28) and dt.time() < dtime(11, 30))  \
            or (dt.time() > dtime(14, 55) and dt.time() < dtime(15, 00))):
            if not self.end_flag:
                self.log_info('time to clear net')
                self.log_info(f'remaining net:{self.net}')
                self.cancel_all()
                if self.net > 0:
                    self.short(self.vt_symbol,tick.bid_price_1,abs(self.net),'end')
                if self.net < 0:
                    self.buy(self.vt_symbol,tick.ask_price_1,abs(self.net),'end')
            self.end_flag = True
        else:
            self.end_flag = False

    def on_time(self):
        dt = datetime.datetime.now()
        if not(
            (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 55))
            or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 15))
            or (dt.time() > dtime(10, 30) and dt.time() < dtime(11, 25))
            or (dt.time() > dtime(13, 32) and dt.time() < dtime(14, 55))
        ):
            self.MMTrading = False
        else:
            self.MMTrading = True
        return self.MMTrading
 
    def on_order(self,order:OrderData): #订单状态发生变化，则调用一次on_order,不记录首次发单

        self.log_info(order)

        orderid = order.order_id
        current_sid = order.sid.split('.')[-1] # order.sid形如cq094551636018.AT0708.AuctionTrade
        volume_traded = order.filled_qty - order.original_filled_qty
        price = self.active_limit_orders[orderid].price
        direction = self.active_limit_orders[orderid].direction.value
        if current_sid.isdigit():
            return # 过滤算法单回报
        if order.order_status.value in ['Filled','Cancelled','Invalid','InvalidCancel','LocalCancelled','Unsend']:
            self.log_info(f"订单状态变化，订单状态：{order.order_status.value}, 信息:{current_sid},错误代码:{order.error_code},错误信息：{order.error_message}")
            orderid = order.order_id
            if orderid in self.active_limit_orders:
                self.active_limit_orders.pop(orderid) #全成、撤单或发单失败则移除
            for send_order in self.send_orders:
                if send_order.price == price:
                    self.send_orders.remove(send_order) 
                    
                    
                    
        if order.order_status.value in ['Filled','PartialFilled']:
            self.trade_price = price
            self.log_info(f"收到成交回报, 方向:{direction}, 价格:{price},数量:{volume_traded}, 信息:{current_sid},成交时间：{datetime.datetime.now()}")
            if direction in ['BuyOpen','BuyClose']:
                self.QT_buy += volume_traded
                self.pos+=volume_traded
            if direction in ['SellOpen','SellClose']:
                self.QT_sell += volume_traded
                self.pos-=volume_traded
            self.update_data()     
        if order.order_status.value in ['Invalid']:
            self.log_info(f"废单，订单状态：{order.order_status.value}, 信息:{current_sid},错误代码:{order.error_code},错误信息：{order.error_message}")
        if order.order_status.value in ['Cancelled']:
            self.log_info(f"收到撤单回报, 方向:{direction}, 价格:{price},数量:{order.order_qty},信息:{current_sid},撤单时间：{datetime.datetime.now()}")
        if order.order_status.value in ['Cancelled','PartialCancelled']:
            self.cancel_count += 1  
        if order.order_status.value in ['Open']:
            self.log_info(f"收到下单回报, 方向:{direction}, 价格:{price}, 数量:{order.order_qty}, 信息:{current_sid},下单时间：{datetime.datetime.now()}")

    def on_send_order(self, response: SendOrderResponse): #记录首次发单返回的数据（包括是否发单成功）
        if not response.success:
            self.log_info(f"下单失败:{response}")
        else:
            current_sid = response.sid.split('.')[-1]
            if current_sid.isdigit():
                return # 过滤算法单回报
            for orderid, order in zip(response.orderid_list, response.order_list):
                cq_symbol = f"{order.symbol}.{order.exchange.value}"
                if isinstance(orderid, str): # 失败
                    self.log_info(f"下单标的{cq_symbol}失败, 方向:{order.direction.value}, 价格:{order.price}, 数量:{order.quantity}, 信息:{orderid},下单时间：{datetime.datetime.now()}")
                else:
                    # self.log_info(f"收到预发单回报, 方向:{order.direction.value}, 价格:{order.price}, 数量:{order.quantity}, 信息:{orderid},下单时间：{datetime.datetime.now()}")
                    self.active_limit_orders[str(orderid)] = order #记录成功发单的订单