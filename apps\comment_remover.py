#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import shutil

# 支持的文件类型和它们对应的注释格式
COMMENT_PATTERNS = {
    # C风格语言 (C, C++, Java, JavaScript等)
    'c': {
        'extensions': ['.c', '.cpp', '.h', '.hpp', '.java', '.js', '.jsx', '.ts', '.tsx', '.cs', '.php'],
        'patterns': [
            r'//.*?$',                          # 单行注释
            r'/\*[\s\S]*?\*/',                  # 多行注释
        ],
        'author_pattern': r'@author|Author:|Author\s+:|作者|创建者'
    },
    # Python风格
    'python': {
        'extensions': ['.py', '.pyw'],
        'patterns': [
            r'#.*?$',                           # 单行注释
            r'"""[\s\S]*?"""',                  # 多行文档字符串
            r"'''[\s\S]*?'''"                   # 多行文档字符串(单引号)
        ],
        'author_pattern': r'@author|Author:|Author\s+:|作者|创建者',
        'preserve_patterns': [
            r'.*\+=\s*f"[^"]*\{.*\}[^"]*"',     # report += f"- 分析因子总数: {len(factors)}\n" 格式
            r'.*=\s*f"[^"]*\{.*\}[^"]*"',       # report = f"- 分析因子总数: {len(factors)}\n" 格式
            r'.*print\s*\(\s*f"[^"]*\{.*\}[^"]*"', # print(f"- 分析因子总数: {len(factors)}\n") 格式
            r'.*\+=\s*f\'[^\']*\{.*\}[^\']*\'', # 单引号版本
            r'.*=\s*f\'[^\']*\{.*\}[^\']*\'',
            r'.*print\s*\(\s*f\'[^\']*\{.*\}[^\']*\'',
            r'.*=\s*"[^"]*#[^"]*"',             # report = "# 因子分析报告\n\n" 格式
            r'.*\+=\s*"[^"]*#[^"]*"',           # report += "# 因子分析报告\n\n" 格式
            r'.*=\s*\'[^\']*#[^\']*\'',         # 单引号版本
            r'.*\+=\s*\'[^\']*#[^\']*\''
        ]
    },
    # HTML/XML风格
    'markup': {
        'extensions': ['.html', '.htm', '.xml', '.svg', '.jsx', '.tsx'],
        'patterns': [
            r'<!--[\s\S]*?-->'                  # HTML/XML注释
        ],
        'author_pattern': r'@author|Author:|Author\s+:|作者|创建者'
    },
    # CSS风格
    'css': {
        'extensions': ['.css', '.scss', '.sass', '.less'],
        'patterns': [
            r'/\*[\s\S]*?\*/'                   # CSS注释
        ],
        'author_pattern': r'@author|Author:|Author\s+:|作者|创建者'
    },
    # 脚本风格
    'shell': {
        'extensions': ['.sh', '.bash', '.zsh', '.bat', '.cmd'],
        'patterns': [
            r'#.*?$',                           # Shell风格单行注释
            r'rem\s.*?$'                        # Windows批处理文件注释
        ],
        'author_pattern': r'@author|Author:|Author\s+:|作者|创建者'
    },
}

def get_file_type(file_path):
    """根据文件扩展名确定文件类型"""
    ext = os.path.splitext(file_path)[1].lower()
    for file_type, info in COMMENT_PATTERNS.items():
        if ext in info['extensions']:
            return file_type
    return None

def is_in_string_or_code(content, match_start):
    """检查位置是否在字符串或代码中，而不是在注释中"""
    # 简单检查：计算到这个位置的引号数量
    single_quotes = content[:match_start].count("'") % 2
    double_quotes = content[:match_start].count('"') % 2
    
    # 如果引号数量为奇数，说明在字符串内部
    return single_quotes == 1 or double_quotes == 1

def is_docstring(content, match):
    """检查是否是真正的文档字符串而不是多行字符串字面量"""
    match_text = match.group(0)
    match_start = match.start()
    
    # 获取匹配行的行号
    line_no = content[:match_start].count('\n')
    lines = content.split('\n')
    
    # 检查是否在函数/类定义后的首行
    prev_lines = lines[:line_no]
    if prev_lines:
        # 向上查找最近的非空行
        for i in range(len(prev_lines)-1, -1, -1):
            if prev_lines[i].strip():
                prev_line = prev_lines[i].strip()
                # 如果上一个非空行是函数/类定义或装饰器，这可能是文档字符串
                if (prev_line.startswith('def ') or 
                    prev_line.startswith('class ') or 
                    prev_line.endswith(':') or 
                    prev_line.startswith('@')):
                    return True
                # 否则可能是代码中的多行字符串
                else:
                    # 检查是否在赋值语句右侧
                    if '=' in prev_line:
                        return False
                    # 检查是否在返回语句中
                    if 'return' in prev_line:
                        return False
                    # 检查是否在引号内
                    if is_in_string_or_code(content, match_start):
                        return False
                break
    
    # 默认行为：如果不能确定，假设是文档字符串（保守做法）
    return True

def process_file(file_path):
    """处理单个文件，移除注释但保留作者信息，返回处理后的内容"""
    file_type = get_file_type(file_path)
    if not file_type:
        print(f"跳过不支持的文件类型: {file_path}")
        return None, False
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return None, False
    
    original_content = content
    
    # 处理文件内容
    patterns = COMMENT_PATTERNS[file_type]['patterns']
    author_pattern = COMMENT_PATTERNS[file_type]['author_pattern']
    
    # 先找出所有包含作者信息的注释
    author_comments = []
    for pattern in patterns:
        for comment_match in re.finditer(pattern, content, re.MULTILINE):
            comment_text = comment_match.group(0)
            if re.search(author_pattern, comment_text, re.IGNORECASE):
                author_comments.append(comment_text)
    
    # 特殊处理Python文档字符串（如果有）
    if file_type == 'python' and 'docstring_patterns' in COMMENT_PATTERNS[file_type]:
        for pattern in COMMENT_PATTERNS[file_type]['docstring_patterns']:
            for docstring_match in re.finditer(pattern, content, re.MULTILINE):
                # 检查是否真正的文档字符串
                if is_docstring(content, docstring_match):
                    docstring_text = docstring_match.group(0)
                    if re.search(author_pattern, docstring_text, re.IGNORECASE):
                        author_comments.append(docstring_text)
    
    # 保存需要保留的报告输出语句
    preserve_lines = []
    
    # 检查是否有需要保留的报告行
    if file_type == 'python' and 'preserve_patterns' in COMMENT_PATTERNS[file_type]:
        for pattern in COMMENT_PATTERNS[file_type]['preserve_patterns']:
            for match in re.finditer(pattern, content, re.MULTILINE):
                line_start = content.rfind('\n', 0, match.start()) + 1
                line_end = content.find('\n', match.start())
                if line_end == -1:
                    line_end = len(content)
                preserve_lines.append((line_start, line_end))
    
    # 移除所有注释但保留特定行
    for pattern in patterns:
        if file_type == 'python' and pattern == r'#.*?$':
            # 对Python的单行注释进行特殊处理
            def replace_comment(match):
                match_start = match.start()
                match_text = match.group(0)
                
                # 检查是否在需要保留的行范围内
                for start, end in preserve_lines:
                    if start <= match_start < end:
                        return match_text  # 保留这行中的注释
                
                # 检查是否是字符串内的 #
                line_start = content.rfind('\n', 0, match_start) + 1
                line_end = content.find('\n', match_start)
                if line_end == -1:
                    line_end = len(content)
                
                current_line = content[line_start:line_end]
                before_match = current_line[:match_start-line_start]
                
                # 检查引号数量，确定是否在字符串内
                single_quotes = before_match.count("'") % 2
                double_quotes = before_match.count('"') % 2
                
                # 如果在字符串内，保留 #
                if single_quotes == 1 or double_quotes == 1:
                    return match_text
                
                # 检查是否在赋值语句中包含 # 的情况
                if '=' in before_match and ('#' in before_match or match_text in ['#', '# ']):
                    # 检查 = 右侧的内容
                    equal_pos = before_match.rfind('=')
                    after_equal = before_match[equal_pos+1:].strip()
                    # 如果 = 右侧紧跟着引号，可能是赋值给字符串开始
                    if after_equal.startswith('"') or after_equal.startswith("'"):
                        return match_text
                
                return ''  # 其他情况删除注释
            
            content = re.sub(pattern, replace_comment, content, flags=re.MULTILINE)
        else:
            content = re.sub(pattern, '', content, flags=re.MULTILINE)
    
    # 特殊处理Python文档字符串（如果有）
    if file_type == 'python' and 'docstring_patterns' in COMMENT_PATTERNS[file_type]:
        for pattern in COMMENT_PATTERNS[file_type]['docstring_patterns']:
            def replace_if_docstring(match):
                if is_docstring(original_content, match):
                    return ''
                return match.group(0)  # 保留非文档字符串的多行字符串
            
            content = re.sub(pattern, replace_if_docstring, content, flags=re.MULTILINE)
    
    # 恢复包含作者信息的注释
    for author_comment in author_comments:
        first_line = content.splitlines()[0] if content.splitlines() else ""
        content = first_line + "\n" + author_comment + "\n" + "\n".join(content.splitlines()[1:]) if content.splitlines() else author_comment
    
    # 比较修改前后内容是否一致
    modified = content != original_content
    return content, modified

def process_directory(input_directory, output_directory, recursive=True, extensions=None, exclude_files=None):
    """处理目录中的所有文件，可选是否递归处理子目录，将结果保存到新目录
    
    Args:
        input_directory: 输入目录路径
        output_directory: 输出目录路径
        recursive: 是否递归处理子目录，默认为True
        extensions: 要处理的文件扩展名列表，如['.py', '.js']
        exclude_files: 要排除的文件名列表，这些文件将被直接复制而不进行处理
    """
    processed_count = 0
    modified_count = 0
    
    # 确保输出目录存在
    os.makedirs(output_directory, exist_ok=True)
    
    if extensions:
        supported_extensions = set(extensions)
    else:
        supported_extensions = set()
        for file_type in COMMENT_PATTERNS.values():
            supported_extensions.update(file_type['extensions'])
    
    # 转换exclude_files为集合以提高查找效率
    exclude_files_set = set(exclude_files) if exclude_files else set()
    
    for root, dirs, files in os.walk(input_directory):
        # 创建对应的输出子目录
        rel_path = os.path.relpath(root, input_directory)
        if rel_path == '.':
            current_output_dir = output_directory
        else:
            current_output_dir = os.path.join(output_directory, rel_path)
            os.makedirs(current_output_dir, exist_ok=True)
        
        for file in files:
            file_path = os.path.join(root, file)
            ext = os.path.splitext(file)[1].lower()
            output_file_path = os.path.join(current_output_dir, file)
            
            # 检查文件是否在排除列表中
            if file in exclude_files_set:
                # 直接复制文件而不处理
                shutil.copy2(file_path, output_file_path)
                print(f"跳过处理文件(在排除列表中): {file_path} -> {output_file_path}")
                continue
            
            if ext in supported_extensions:
                processed_count += 1
                new_content, modified = process_file(file_path)
                
                if new_content is not None:
                    if modified:
                        # 写入处理后的内容到新文件
                        try:
                            with open(output_file_path, 'w', encoding='utf-8') as f:
                                f.write(new_content)
                            modified_count += 1
                            print(f"已处理文件: {file_path} -> {output_file_path}")
                        except Exception as e:
                            print(f"写入文件 {output_file_path} 时出错: {e}")
                    else:
                        # 如果内容没有变化，直接复制原文件
                        shutil.copy2(file_path, output_file_path)
                        print(f"文件未变化，已复制: {file_path} -> {output_file_path}")
            else:
                # 对于不支持的文件类型，直接复制
                shutil.copy2(file_path, output_file_path)
                print(f"复制不支持的文件类型: {file_path} -> {output_file_path}")
        
        if not recursive:
            break  # 如果不递归处理，则在处理完顶层目录后退出
    
    return processed_count, modified_count

def run_comment_remover(input_directory, output_directory, recursive=False, extensions=None, exclude_files=None):
    """运行注释删除工具的主函数，可直接通过参数调用
    
    Args:
        input_directory: 输入目录路径
        output_directory: 输出目录路径
        recursive: 是否递归处理子目录，默认为False
        extensions: if not None，仅处理这些扩展名的文件，例如 ['.py', '.js']
        exclude_files: 如果不为None，这些文件将被跳过处理，直接复制到目标目录
    """
    if not os.path.isdir(input_directory):
        print(f"错误：'{input_directory}' 不是一个有效的目录")
        return False
    
    if os.path.exists(output_directory) and not os.path.isdir(output_directory):
        print(f"错误：'{output_directory}' 已存在且不是一个目录")
        return False
    
    print(f"开始处理目录: {input_directory}")
    print(f"输出目录: {output_directory}")
    print(f"递归处理子目录: {'是' if recursive else '否'}")
    if extensions:
        print(f"仅处理以下文件类型: {', '.join(extensions)}")
    if exclude_files:
        print(f"排除以下文件: {', '.join(exclude_files)}")
    
    processed_count, modified_count = process_directory(input_directory, output_directory, recursive, extensions, exclude_files)
    
    print("\n处理完成!")
    print(f"共处理文件: {processed_count}")
    print(f"修改文件: {modified_count}")
    
    return True

if __name__ == "__main__":
    
    # 你可以直接调用函数或添加命令行解析
    # 示例用法

        # 直接运行示例
        # 注意：这里我们排除了一个示例文件，它将不会被处理
    dir_path = r"ml\LIODAF"
    output_dir = f"output\\{dir_path}"
    run_comment_remover(dir_path, output_dir, True, ['.py', '.js'], ["factors_for_use.py"]) 