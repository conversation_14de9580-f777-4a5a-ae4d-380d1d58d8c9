#!/usr/bin/env python
# -*- coding:utf-8 -*-
import math
import pyodbc
from functools import reduce

import pandas as pd
import numpy as np
from WindPy import w
from datetime import datetime, timedelta
from os import path

w.start()

server = '***********'
user = 'mktdtr'
password = '123678'
database = 'MktDataHis'

date1 = u'2019-07-08'
date2 = u'2019-07-12'

slip = 1 / 1000  # 滑价比率
amount = 1

tradedates = w.tdays(date1, date2, "").Data[0]

shenfee = ((0.00487 + 0.0045) / 100 + slip / 2)  # 申购费 sellpr
shufee = ((0.00487 + 0.0045) / 100 + 1 / 1000 + slip / 2)  # 赎回费 buypr

taolifee = 3 / 1000


##################################################
# 获取行情数据
def getFutcodes():  # 近月期货
    # 每日提取一次数据
    futcodes = w.wset("futurecc", "startdate=%s;enddate=%s;wind_code=IH.CFE" % (date1, date2)).Data[2]
    lasttrade = w.wss(futcodes, "lasttrade_date").Data[0]
    trade_hiscode = w.wsd("IH.CFE", "trade_hiscode", date1, date2, "").Data[0]
    futlist = []
    for i in range(0, len(tradedates)):
        # 期货最后交易日转为次月合约
        lastdayt = lasttrade[0]
        futnum = 0
        for ii in range(0, len(futcodes)):
            if trade_hiscode[i] == futcodes[ii]:
                lastdayt = lasttrade[ii]
                futnum = ii
                break
        if tradedates[i] == lastdayt:
            forward1 = futcodes[futnum + 1]
        else:
            forward1 = trade_hiscode[i]
        futlist.append(forward1)
    return futlist


def getFutdata(database):
    sql1 = u"SELECT [RecordTime],[Price] FROM [MktDataHis].[dbo].[" \
           u"%s] where [Symbol]='%s' and [RecordTime]>'%s' and [RecordTime]<'%s' ORDER BY [RecordTime]" \
           % (database, 'IH' + futlist[0][2:-4], date1, datetime.strptime(date2, "%Y-%m-%d") + timedelta(days=1))
    futpf = pd.read_sql(sql1, conn, index_col=['RecordTime'], columns=['IH'], coerce_float=True, params=None,
                        parse_dates=True, chunksize=None)
    futpf.columns = ['IH']
    sql1 = u"SELECT [RecordTime],[Price] FROM [MktDataHis].[dbo].[" \
           u"%s] where [Symbol]='%s' and [RecordTime]>'%s' and [RecordTime]<'%s' ORDER BY [RecordTime]" \
           % (database, 'IF' + futlist[0][2:-4], date1, datetime.strptime(date2, "%Y-%m-%d") + timedelta(days=1))
    futpf = futpf.join(
        pd.read_sql(sql1, conn, index_col=['RecordTime'], columns=['IF'], coerce_float=True, params=None,
                    parse_dates=True, chunksize=None))
    futpf.columns = ['IH', 'IF']
    sql1 = u"SELECT [RecordTime],[Price] FROM [MktDataHis].[dbo].[" \
           u"%s] where [Symbol]='%s' and [RecordTime]>'%s' and [RecordTime]<'%s' ORDER BY [RecordTime]" \
           % (database, 'IC' + futlist[0][2:-4], date1, datetime.strptime(date2, "%Y-%m-%d") + timedelta(days=1))
    futpf = futpf.join(
        pd.read_sql(sql1, conn, index_col=['RecordTime'], columns=['IC'], coerce_float=True, params=None,
                    parse_dates=True, chunksize=None))
    futpf.columns = ['IH', 'IF', 'IC']
    return futpf


# #####################获取期货数据
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE=database, PWD=password, UID=user)
futlist = getFutcodes()
if len(set(futlist)) == 1:
    futpf = getFutdata('FutMktDataAdj')
    # futpf = futpf.append(getFutdata('FutMktDataAdjHis'))

futpf = futpf.resample('1min', closed='left', label='left').asfreq().dropna().sort_index()
# #############ETF清单
etf = pd.read_csv(open(path.dirname(__file__) + '//db//sseETFmmclean.csv'))  # 上交所做市商ETF
getf = etf.groupby('etf').size()  # etf做市商数量
yelist = getf.index.values
#
dfetf = pd.read_csv(open(path.dirname(__file__) + '//db//ETFallclean.csv'))  # SHSZ全部上市ETF
# ETF对应指数
etfdata = w.wsd(dfetf['0'].apply(lambda x: str(x) + '.sh').tolist(), "fund_trackindexcode", date2, date2, "")
indexcodes = pd.DataFrame(etfdata.Data, index=None, columns=etfdata.Codes)
# indexcodes = indexcodes.T
colist = list(set(indexcodes.iloc[0, :]))
# ETF对应指数日收盘价
etfdata = w.wsd(list(set(indexcodes.iloc[0, :])), "close",
                datetime.strptime(date1, "%Y-%m-%d") - timedelta(days=30), date2, "")
indexdata = pd.DataFrame(etfdata.Data, index=etfdata.Codes, columns=etfdata.Times)
indexdata = indexdata.T
# #############现货行情数据
print('read stocks data')
# 行情
sql1 = u"SELECT [Symbol],[Buy1Px],[Sell1Px],[Price],[Volume],[RecordTime] FROM [XHLSHQ].[dbo].[" \
       u"Stock] where RecordTime>'%s' and RecordTime<'%s' ORDER BY [RecordTime]" % (
           date1, datetime.strptime(date2, "%Y-%m-%d") + timedelta(days=1))
df = pd.read_sql(sql1, conn, index_col=['RecordTime'], coerce_float=True, params=None,
                 parse_dates=True, columns=None, chunksize=None)
print('read etfs data')
sql2 = u"SELECT [Symbol],[Buy1Px],[Sell1Px],[Price],[Volume],[Buy1Qty],[Sell1Qty],[RecordTime] FROM " \
       u"[XHLSHQ].[dbo].[ETF] where RecordTime>'%s' and RecordTime<='%s' ORDER BY [RecordTime]" % (
           date1, datetime.strptime(date2, "%Y-%m-%d") + timedelta(days=1))
etfdf0 = pd.read_sql(sql2, conn, index_col=['Symbol', 'RecordTime'], coerce_float=True, params=None,
                     parse_dates=True, columns=None, chunksize=None)
print('read index data')
sql3 = u"SELECT [Symbol],[Price],[Volume],[RecordTime] FROM " \
       u"[XHLSHQ].[dbo].[Index] where RecordTime>'%s' and RecordTime<='%s' ORDER BY [RecordTime]" % (
           date1, datetime.strptime(date2, "%Y-%m-%d") + timedelta(days=1))
indexdf0 = pd.read_sql(sql3, conn, index_col=['Symbol', 'RecordTime'], coerce_float=True, params=None,
                       parse_dates=True, columns=None, chunksize=None)
conn.close()
#########################################################################################################
# 数据清理，去除噪点
print('data clean')
# 买一卖一价格为0替换
df['Sell1Px'] = df[['Sell1Px', 'Price']].replace(0, np.nan).fillna(method='bfill', axis='columns')
df['Buy1Px'] = df[['Buy1Px', 'Price']].replace(0, np.nan).fillna(method='bfill', axis='columns')

etfdf0['Sell1Px'] = etfdf0[['Sell1Px', 'Price']].sort_index(level='Symbol').replace(0, np.nan).fillna(
    method='bfill', axis='columns')
etfdf0['Buy1Px'] = etfdf0[['Buy1Px', 'Price']].sort_index(level='Symbol').replace(0, np.nan).fillna(method='bfill',
                                                                                                    axis='columns')

list2 = [' 09:30:00', ' 14:57:00', ' 14:58:00', ' 14:59:00', ' 15:00:00']
df = df[~df.index.isin(reduce(lambda x, y: [i + j for i in x for j in y],
                              [list(map(lambda x: datetime.strftime(x, "%Y-%m-%d"), tradedates)),
                               list2]))]
etfdf0 = etfdf0.reset_index().set_index(['RecordTime']).sort_index()
etfdf0 = etfdf0[~etfdf0.index.isin(reduce(lambda x, y: [i + j for i in x for j in y],
                                          [list(map(lambda x: datetime.strftime(x, "%Y-%m-%d"), tradedates)),
                                           list2]))].reset_index().set_index(['Symbol', 'RecordTime'])
#########################################################################################################
df1 = pd.DataFrame(columns=[])
delres = pd.DataFrame(columns=[])
try:
    for ii, etfcode in enumerate(dfetf['0']):
        # etfcode = 510070
        df2 = pd.DataFrame(columns=[])
        print(ii)
        print(etfcode)

        etfdata = w.wsd(str(etfcode) + '.sh',
                        "fund_etfpr_minnav,close,volume,amt,unit_total,fund_etfpr_estcash,fund_etfpr_cashbalance",
                        date1,
                        datetime.strftime(w.tdaysoffset(1, date2, "").Data[0][0], "%Y-%m-%d"), "")  # 多取一个交易日
        etfdf = pd.DataFrame(etfdata.Data, index=etfdata.Fields, columns=etfdata.Times)
        etfdf = etfdf.T
        etfnp = etfdf.mean()
        try:
            etfnp = etfnp.append(pd.Series(getf[etfcode], index=['mmnum']), ignore_index=False)
        except:
            etfnp = etfnp.append(pd.Series(0, index=['mmnum']), ignore_index=False)


        #########################################################################
        # 期现套利
        # 函数：计算相关系数
        def calc_corr(a, b):
            a_avg = sum(a) / len(a)
            b_avg = sum(b) / len(b)

            # 计算分子，协方差————按照协方差公式，本来要除以n的，由于在相关系数中上下同时约去了n，于是可以不除以n
            cov_ab = sum([(x - a_avg) * (y - b_avg) for x, y in zip(a, b)])

            # 计算分母，方差乘积————方差本来也要除以n，在相关系数中上下同时约去了n，于是可以不除以n
            sq = math.sqrt(sum([(x - a_avg) ** 2 for x in a]) * sum([(x - b_avg) ** 2 for x in b]))
            ss = sum([(x - a_avg) ** 2 for x in a])

            corr_factor = cov_ab / sq
            beta = cov_ab / ss

            return corr_factor, beta


        indexcodes.columns = list(map(lambda x: x[:6], indexcodes.columns))
        indexcode = indexcodes[str(etfcode)][0]

        cordf = pd.DataFrame(columns=[])
        mapx = {}
        corr = pd.Series(
            calc_corr(np.diff(np.log(indexdata['000016.SH'])),
                      np.diff(np.log(indexdata[indexcode])))[
                0],
            index=['IH'])
        corr = corr.append(
            pd.Series(calc_corr(
                np.diff(np.log(indexdata['000300.SH'])),
                np.diff(np.log(indexdata[indexcode])))[0],
                      index=['IF']))
        corr = corr.append(
            pd.Series(calc_corr(
                np.diff(np.log(indexdata['000905.SH'])),
                np.diff(np.log(indexdata[indexcode])))[0],
                      index=['IC']))

        list_a_max_list = max(corr[['IH', 'IC', 'IF']])  # 返回最大值
        max_index = corr.index[np.argwhere(corr == list_a_max_list)].values[0][0]  # 返回最大值的索引
        for i, dd in enumerate(tradedates):
            corr = pd.Series(max_index, index=['fut'])
            futindex = corr.map({'IH': '000016.SH', "IF": '000300.SH', "IC": '000905.SH'})['fut']
            balist = calc_corr(
                np.diff(np.log(indexdata[futindex][(0 + i):(len(indexdata.index) - len(tradedates) + i)])),
                np.diff(np.log(indexdata[indexcode][(0 + i):(len(indexdata.index) - len(tradedates) + i)])))
            corr = corr.append(pd.Series(balist[0], index=['cor']))

            corr = corr.append(pd.Series(balist[1], index=['beta']))
            corr = corr.append(
                pd.Series(corr['beta'] * indexdata[indexcode][
                    (len(indexdata.index) - len(tradedates) + i)] / indexdata[futindex][
                              (len(indexdata.index) - len(tradedates) + i)],
                          index=['mult']))
            indexmean = indexdata[indexcode][
                        (len(indexdata.index) - len(tradedates) + i - 5):(
                                len(indexdata.index) - len(tradedates) + i)].mean()
            # corr = corr.append(pd.Series(indexmean/indexdata[futindex]
            # [(len(indexdata.index) - len(tradedates) + i - 5):(len(indexdata.index) - len(tradedates) + i)].mean()/corr['mult']-1,
            #                              index=['alpha']))  # fut alpha
            corr = corr.append(pd.Series(1 / corr['beta'] - 1,
                                         index=['alpha']))  # fut alpha
            cordf[datetime.strftime(dd, "%Y-%m-%d")] = corr
        #############################
        # ETF行情
        etfdf00 = etfdf0.sort_index(level='Symbol').loc[str(etfcode), :]
        etfdf00.columns = list(
            map(lambda x: str(etfcode) + 'etf' + x,
                ['Buy1Px', 'Sell1Px', 'Price', 'Volume', 'Buy1Qty', 'Sell1Qty', ]))

        ####################################################################
        for ti, date0 in enumerate(tradedates):

            wdata = w.wset("etfconstituent", "date=%s;windcode=%s.sh" % (date0, etfcode))
            if len(wdata.Data) == 0:
                print(str(etfcode) + ' NO')
                continue
            etflist = pd.DataFrame(wdata.Data, index=wdata.Fields)
            etflist = etflist.T

            date0n = date0 + timedelta(days=1)
            date0s = datetime.strftime(date0, "%Y-%m-%d")
            dftoday = df[date0s].reset_index()

            # print('get constituent')
            no_cashlist = etflist[etflist['cash_substitution_mark'].isin([u'禁止', u'允许', u'退补', u'深市退补'])]
            cash = etflist[etflist['cash_substitution_mark']
                .isin([u'必须'])]['fixed_substitution_amount'].sum() \
                   + etfdf.loc[
                       lambda x: datetime.date(tradedates[ti + 1]) if ti < len(tradedates) - 1 else datetime.date(
                           w.tdaysoffset(1, date2, "").Data[0][0]), 'FUND_ETFPR_CASHBALANCE']

            dftoday = dftoday.sort_index().set_index('Symbol').loc[no_cashlist['wind_code'].apply(lambda x: x[:6]), :] \
                .reset_index()
            dftoday = dftoday.set_index(['RecordTime', 'Symbol'])

            # print('colculate price')
            results1 = []
            results2 = []
            results3 = []
            results4 = []
            L = list(dftoday.index.get_level_values(0))[:(241 - len(list2))]
            T = []
            index = L
            index = pd.DataFrame(index=index)[date0s].index.drop_duplicates().tolist()
            dftoday = dftoday.sort_index(level='RecordTime')
            for i in index:
                df_no = dftoday.loc[i, :]
                try:
                    if no_cashlist.size != 0:
                        results1.append(np.dot(no_cashlist['volume'].values.T, df_no.loc[:, 'Price'].values) + cash)
                        results2.append(np.dot(no_cashlist['volume'].values.T, df_no.loc[:, 'Buy1Px'].values) + cash)
                        results3.append(np.dot(no_cashlist['volume'].values.T, df_no.loc[:, 'Sell1Px'].values) + cash)
                    else:
                        results1.append(0 + cash)
                        results2.append(0 + cash)
                        results3.append(0 + cash)
                except Exception as e:
                    results1.append(results1[-1])
                    results2.append(results2[-1])
                    results3.append(results3[-1])
                    print(e)
                    pass

            dfper = pd.DataFrame([results1, results2, results3] / etfnp['FUND_ETFPR_MINNAV'],
                                 columns=index,
                                 index=list(
                                     map(lambda x: str(etfcode) + x, ['Price', 'Buy1Px', 'Sell1Px']))).T.sort_index()

            # 添加期货行情
            dfper = dfper.join(futpf[cordf.loc['fut', date0s]])
            dfper = dfper.rename(columns={cordf.loc['fut', date0s]: str(etfcode) + cordf.loc['fut', date0s]})

            # 添加指数行情
            dfper = dfper.join(indexdf0.sort_index(level='Symbol').loc[indexcode[:6], :][date0s]['Price'])
            dfper = dfper.rename(columns={'Price': str(etfcode) + 'indexPrice'})

            # 添加ETF行情
            dfper = dfper.join(etfdf00[date0s])
            dfper = dfper.join(pd.DataFrame(np.append(np.diff(dfper['%setfVolume' % etfcode]), 0), index=dfper.index,
                                            columns=[str(etfcode) + 'diffVol']))
            ##############################################################################################
            # 转换为折溢价价差率
            mult2 = 1 / cordf.loc['mult', date0s] * dfper[str(etfcode) + 'indexPrice'][0] / \
                    dfper[str(etfcode) + 'etfPrice'][0]  # 用第一天的mult
            dfper['%sdeltaP' % etfcode] = (dfper[str(etfcode) + 'Price'] - dfper[str(etfcode) + 'etfPrice']) / dfper[
                str(etfcode) + cordf.loc['fut', date0s]] * mult2
            dfper['%sdeltaS' % etfcode] = (dfper[str(etfcode) + 'Buy1Px'] - dfper[str(etfcode) + 'etfSell1Px']) / dfper[
                str(etfcode) + cordf.loc['fut', date0s]] * mult2
            dfper['%sdeltaB' % etfcode] = (dfper[str(etfcode) + 'Sell1Px'] - dfper[str(etfcode) + 'etfBuy1Px']) / dfper[
                str(etfcode) + cordf.loc['fut', date0s]] * mult2

            # 计算基差率
            # dfper['%sdeltaFS' % etfcode] = (1 - (dfper[str(etfcode) + 'indexPrice']/dfper[str(etfcode)
            #                 + cordf.ix['fut', date0s]])/cordf.loc['mult', date0s]) + cordf.loc['alpha', date0s]
            dfper['%sdeltaFS' % etfcode] = 1 - dfper[str(etfcode) + 'etfSell1Px'] / dfper[
                str(etfcode) + cordf.loc['fut', date0s]] * mult2 + cordf.loc['alpha', date0s]
            dfper['%sdeltaFB' % etfcode] = 1 - dfper[str(etfcode) + 'etfBuy1Px'] / dfper[
                str(etfcode) + cordf.loc['fut', date0s]] * mult2 + cordf.loc['alpha', date0s]
            dfper['%sdeltaFS' % etfcode] = 1 - dfper[str(etfcode) + 'etfSell1Px'] / dfper[
                str(etfcode) + cordf.loc['fut', date0s]] * mult2 + cordf.loc['alpha', date0s]

            df2 = df2.append(dfper)

            print(date0s, 'done')


        # 扣费后的折溢价差
        def function(a, b, c, shuhuifee, shengoufee):
            if b > 0:  # 赎回
                if b > shuhuifee:
                    return b - shuhuifee
                else:
                    return 0
            elif c < 0:  # 申购
                if c < -shengoufee:
                    return c + shengoufee
                else:
                    return 0
            else:
                return 0


        # 扣费后的基差
        def function3(a, b, c):
            if a > c / 2:  # 正基差
                return a - (c / 2)
            elif b < - c / 2:  # 申购
                return b - (- c / 2)
            else:
                return 0


        # 利润计算
        def function2(a, b, c):
            if a > 0:  # 赎回/正基差
                return a * b
            elif a < 0:  # 申购
                return -a * c
            else:
                return 0


        def function5(a, b, c):
            if a > b + c:
                return a - b - c
            else:
                return 0


        #########################################################################
        # 折溢价&基差
        df2 = df2.dropna()

        df2['%sfee1' % etfcode] = shenfee
        df2['%sfee2' % etfcode] = shufee
        df2['%sfee3' % etfcode] = taolifee

        df2['%snet2' % etfcode] = df2.apply(
            lambda x: function3(x['%sdeltaFS' % etfcode], x['%sdeltaFB' % etfcode], x['%sfee3' % etfcode]),
            axis=1)  # 净基差
        # df2['%snet2' % etfcode][df2['%snet2' % etfcode] < 0] = 0  # 仅做正基差

        # df2[list(map(lambda x: str(etfcode) + x, ['Price', 'Buy1Px', 'Sell1Px']))] = df2[list(
        #     map(lambda x: str(etfcode) + x, ['Price', 'Buy1Px', 'Sell1Px']))] / etfdata.Data[0][0]
        df2['%snet' % etfcode] = df2.apply(
            lambda x: function(x['%sdeltaP' % etfcode], x['%sdeltaS' % etfcode], x['%sdeltaB' % etfcode],
                               x['%sfee2' % etfcode], x['%sfee1' % etfcode]), axis=1)  # 净折溢价
        tradedates2 = tradedates[::-1]
        df2['%snet3' % etfcode] = 0
        for ti, date0 in enumerate(tradedates2):
            if ti == 0:
                npnet3 = df2['%snet2' % etfcode][datetime.strftime(tradedates2[0], "%Y-%m-%d")]
                continue
            datetime.strftime(date0, "%Y-%m-%d")
            t1 = df2[datetime.strftime(tradedates2[ti], "%Y-%m-%d")]['%sdeltaFB' % etfcode].quantile(0.2)
            npnet3 = df2[datetime.strftime(tradedates2[ti], "%Y-%m-%d")] \
                .apply(lambda x: function5(x['%sdeltaFS' % etfcode], t1, x['%sfee3' % etfcode]), axis=1).append(npnet3)
        df2['%snet3' % etfcode] = npnet3
        
        df2['%smoneyZY1' % etfcode] = df2.apply(
            lambda x: function2(x['%snet' % etfcode], x['%setfBuy1Qty' % etfcode], x['%setfSell1Qty' % etfcode]),
            axis=1) * df2['%sPrice' % etfcode] / df2[str(etfcode) + corr['fut']]
        # 净折溢价利润
        df2['%smoneyZY2' % etfcode] = df2['%snet' % etfcode] * df2['%sdiffVol' % etfcode] * df2['%sPrice' % etfcode] / \
                                      df2[str(etfcode) + corr['fut']]
        df2['%smoneyTL1' % etfcode] = df2.apply(
            lambda x: function2(x['%snet2' % etfcode], x['%setfSell1Qty' % etfcode], x['%setfBuy1Qty' % etfcode]),
            axis=1) * df2['%sPrice' % etfcode] / df2[str(etfcode) + corr['fut']]  # 净折溢价利润
        df2['%smoneyTL2' % etfcode] = df2['%snet2' % etfcode] * df2['%sdiffVol' % etfcode] * df2['%sPrice' % etfcode] \
                                      / df2[str(etfcode) + corr['fut']]
        df2['%smoneyTL31' % etfcode] = df2.apply(
            lambda x: function2(x['%snet3' % etfcode], x['%setfSell1Qty' % etfcode], x['%setfBuy1Qty' % etfcode]),
            axis=1) * df2['%sPrice' % etfcode] / df2[str(etfcode) + corr['fut']]  # 净折溢价利润
        df2['%smoneyTL32' % etfcode] = df2['%snet3' % etfcode] * df2['%sdiffVol' % etfcode] * df2['%sPrice' % etfcode] \
                                       / df2[str(etfcode) + corr['fut']]

        # 结果统计
        etfnp = etfnp.append(
            pd.Series(corr['fut'],
                      index=['fut']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(corr['cor'],
                      index=['cor']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(corr['beta'],
                      index=['beta']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(corr['mult'],
                      index=['mult']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series((df2['%sdeltaP' % etfcode]).std(),
                      index=['dvolP']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series((df2['%snet' % etfcode]).std(),
                      index=['netvol']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(np.diff(np.log(df2[str(etfcode) + 'Price'])).std() * np.sqrt(252 * 240), index=['svol']),
            ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(np.sum(abs(df2['%smoneyZY1' % etfcode])), index=['moneyZY1']),
            ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(np.sum(abs(df2['%smoneyZY2' % etfcode])), index=['moneyZY2']),
            ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(np.sum(abs(df2['%smoneyTL1' % etfcode])), index=['moneyTL1']),
            ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(np.sum(abs(df2['%smoneyTL2' % etfcode])), index=['moneyTL2']),
            ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(np.sum(abs(df2['%smoneyTL31' % etfcode])), index=['moneyTL31']),
            ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(np.sum(abs(df2['%smoneyTL32' % etfcode])), index=['moneyTL32']),
            ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2['%sdeltaFS' % etfcode].std(),
                      index=['dvolFS']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2['%snet2' % etfcode].std(),
                      index=['dvolnet2']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2['%snet3' % etfcode].std(),
                      index=['dvolnet3']), ignore_index=False)
        # 折溢价损益拆解
        etfnp = etfnp.append(
            pd.Series(df2[df2['%snet' % etfcode] > 0]['%snet' % etfcode].sum(),
                      index=['shuhui']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2[df2['%snet' % etfcode] > 0]['%snet' % etfcode].count(),
                      index=['shuhuiNum']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2[df2['%snet' % etfcode] < 0]['%snet' % etfcode].sum(),
                      index=['shengou']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2[df2['%snet' % etfcode] < 0]['%snet' % etfcode].count(),
                      index=['shengouNum']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2['%snet' % etfcode].abs().sum(),
                      index=['netsum']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2['%snet2' % etfcode].abs().sum(),
                      index=['net2sum']), ignore_index=False)
        etfnp = etfnp.append(
            pd.Series(df2['%snet3' % etfcode].abs().sum(),
                      index=['net3sum']), ignore_index=False)
        # df1 = df2.join(df1)
        df2.to_csv(path.dirname(__file__) + "//result//etf_data%s.csv" % etfcode)
        delres[etfcode] = etfnp
except Exception as e:
    print(e)

delres = delres.T

delres.to_csv('result.csv')
# getf.to_("etf_sse.csv")
# df1.to_csv("etf_data.csv")
res = df1.describe()
print(res)
res.to_csv("res_data2.csv")

from sklearn.decomposition import PCA

delresPCA = np.log(delres[['VOLUME', 'UNIT_TOTAL']])
delres['sum'] = delres['shuhui'] + delres['shengou']
delresPCA = delresPCA.join(delres[['dvolP', 'svol', 'sum']]).dropna()
pca = PCA()  # 保留所有成分
pca.fit(delresPCA)
k = pca.components_  # 返回模型的各个特征向量
n = pca.explained_variance_
p = pca.explained_variance_ratio_  # 返回各个成分各自的方差百分比(也称贡献率）
pca = PCA(3)  # 选取累计贡献率大于80%的主成分（3个主成分）
pca.fit(delresPCA)
low_d = pca.transform(delresPCA)  # 降低维度
#
# for i in range(df1.shape[1]):
#     if 'delta' in str(df1.columns.tolist()[i]):
#         print(str(df1.columns.tolist()[i]))
#         df1[df1.columns.tolist()[i]].plot(secondary_y=[str(df1.columns.tolist()[i])])
#     else:
#         df1[df1.columns.tolist()[i]].plot()

# df1.plot()
# plt.show()

print(low_d)
print(p)
print(k)
pd.DataFrame(low_d).to_csv('low_d.csv')
pd.DataFrame(p).to_csv('p.csv')
pd.DataFrame(k).to_csv('k.csv')
