"""
订单时间敏感特征
@author: lining
"""
import pandas as pd
import numpy as np
from factor_manager import factor_manager, Factor, FactorCategory

# ------------------------------ time sensitive features ------------------------------
# 注册时间敏感特征

# 订单到达率计算函数
def calculate_order_arrival_rate(data: pd.DataFrame, side: str = None) -> pd.Series:
    """
    计算订单到达率（每毫秒）
    
    公式：特定类型订单数量 / 时间窗口长度
    
    Args:
        data: 包含订单数据的DataFrame
        side: 订单方向，可以是'BUY'、'SELL'或None（表示所有订单）
        
    Returns:
        订单到达率序列
    """
    # 假设data中有以下列：
    # - order_side: 订单方向（'BUY'或'SELL'）
    # - timestamp: 订单时间戳（毫秒）
    if 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    if side and 'order_side' not in data.columns:
        return pd.Series(0, index=data.index)
    
    # 获取时间窗口（假设为1000毫秒）
    window_ms = 1000
    
    # 对每个时间点计算订单到达率
    result = pd.Series(index=data.index)
    for idx in data.index:
        # 获取当前时间点
        current_time = data.loc[idx, 'timestamp']
        
        # 获取时间窗口内的所有订单
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        # 根据side筛选订单
        if side:
            filtered_orders = window_data[window_data['order_side'] == side].shape[0]
        else:
            filtered_orders = window_data.shape[0]
        
        # 如果窗口内有数据，计算到达率
        if not window_data.empty:
            time_span = window_data['timestamp'].max() - window_data['timestamp'].min()
            if time_span > 0:
                result.loc[idx] = filtered_orders / time_span
            else:
                result.loc[idx] = 0
        else:
            result.loc[idx] = 0
            
    return result

# 注册买单到达率因子
factor_manager.register_factor(Factor(
    name="bid_arrival_rate",
    category=FactorCategory.TIME_SENSITIVE,
    description="买单到达率（每毫秒）",
    calculation=lambda data: calculate_order_arrival_rate(data, 'BUY'),
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))

# 注册卖单到达率因子
factor_manager.register_factor(Factor(
    name="ask_arrival_rate",
    category=FactorCategory.TIME_SENSITIVE,
    description="卖单到达率（每毫秒）",
    calculation=lambda data: calculate_order_arrival_rate(data, 'SELL'),
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))

# 订单强度计算函数
def calculate_order_intensity(data: pd.DataFrame) -> pd.Series:
    """
    计算订单强度（总订单到达率，每毫秒）
    
    公式：总订单数量 / 时间窗口长度
    
    Args:
        data: 包含订单数据的DataFrame
        
    Returns:
        订单强度序列
    """
    # 假设data中有以下列：
    # - timestamp: 订单时间戳（毫秒）
    if 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    # 获取时间窗口（假设为1000毫秒）
    window_ms = 1000
    
    # 对每个时间点计算订单强度
    result = pd.Series(index=data.index)
    for idx in data.index:
        # 获取当前时间点
        current_time = data.loc[idx, 'timestamp']
        
        # 获取时间窗口内的所有订单
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        # 计算总订单数量
        total_orders = window_data.shape[0]
        
        # 如果窗口内有数据，计算订单强度
        if not window_data.empty:
            time_span = window_data['timestamp'].max() - window_data['timestamp'].min()
            if time_span > 0:
                result.loc[idx] = total_orders / time_span
            else:
                result.loc[idx] = 0
        else:
            result.loc[idx] = 0
            
    return result

# 买卖比率计算函数
def calculate_buy_sell_ratio(data: pd.DataFrame) -> pd.Series:
    """
    计算买卖比率
    
    公式：买单数量 / 卖单数量
    
    Args:
        data: 包含订单数据的DataFrame
        
    Returns:
        买卖比率序列
    """
    # 假设data中有以下列：
    # - order_side: 订单方向（'BUY'或'SELL'）
    # - timestamp: 订单时间戳（毫秒）
    if 'order_side' not in data.columns or 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    # 获取时间窗口（假设为1000毫秒）
    window_ms = 1000
    
    # 对每个时间点计算买卖比率
    result = pd.Series(index=data.index)
    for idx in data.index:
        # 获取当前时间点
        current_time = data.loc[idx, 'timestamp']
        
        # 获取时间窗口内的所有订单
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        # 计算买单和卖单数量
        buy_orders = window_data[window_data['order_side'] == 'BUY'].shape[0]
        sell_orders = window_data[window_data['order_side'] == 'SELL'].shape[0]
        
        # 计算买卖比率
        if sell_orders > 0:
            result.loc[idx] = buy_orders / sell_orders
        else:
            result.loc[idx] = float('nan')  # 如果没有卖单，返回NaN
            
    return result

# 订单不平衡比率计算函数
def calculate_order_imbalance_ratio(data: pd.DataFrame) -> pd.Series:
    """
    计算订单不平衡比率 ([-1, 1]范围)
    
    公式：(买单数量 - 卖单数量) / (买单数量 + 卖单数量)
    
    Args:
        data: 包含订单数据的DataFrame
        
    Returns:
        订单不平衡比率序列
    """
    # 假设data中有以下列：
    # - order_side: 订单方向（'BUY'或'SELL'）
    # - timestamp: 订单时间戳（毫秒）
    if 'order_side' not in data.columns or 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    # 获取时间窗口（假设为1000毫秒）
    window_ms = 1000
    
    # 对每个时间点计算订单不平衡比率
    result = pd.Series(index=data.index)
    for idx in data.index:
        # 获取当前时间点
        current_time = data.loc[idx, 'timestamp']
        
        # 获取时间窗口内的所有订单
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        # 计算买单和卖单数量
        buy_orders = window_data[window_data['order_side'] == 'BUY'].shape[0]
        sell_orders = window_data[window_data['order_side'] == 'SELL'].shape[0]
        total_orders = buy_orders + sell_orders
        
        # 计算订单不平衡比率
        if total_orders > 0:
            result.loc[idx] = (buy_orders - sell_orders) / total_orders
        else:
            result.loc[idx] = 0
            
    return result



# 时间加权平均价格计算函数
def calculate_time_weighted_average_price(data: pd.DataFrame) -> pd.Series:
    """
    计算时间加权平均价格
    
    公式：Σ(价格 * 时间权重) / Σ(时间权重)
    其中时间权重是该价格持续的时间长度
    
    Args:
        data: 包含订单数据的DataFrame
        
    Returns:
        时间加权平均价格序列
    """
    # 假设data中有以下列：
    # - price: 订单价格
    # - timestamp: 订单时间戳（毫秒）
    if 'price' not in data.columns or 'timestamp' not in data.columns:
        return pd.Series(0, index=data.index)
    
    # 获取时间窗口（假设为1000毫秒）
    window_ms = 1000
    
    # 对每个时间点计算TWAP
    result = pd.Series(index=data.index)
    for idx in data.index:
        # 获取当前时间点
        current_time = data.loc[idx, 'timestamp']
        
        # 获取时间窗口内的所有订单
        window_data = data[(data['timestamp'] >= current_time - window_ms) & 
                           (data['timestamp'] <= current_time)]
        
        # 如果窗口内有足够的数据，计算TWAP
        if len(window_data) >= 2:
            # 按时间排序
            window_data = window_data.sort_values('timestamp')
            
            # 计算每个价格的持续时间
            timestamps = window_data['timestamp'].values
            prices = window_data['price'].values
            
            # 计算时间差
            time_diffs = np.diff(timestamps)
            
            # 计算TWAP
            weighted_sum = np.sum(prices[:-1] * time_diffs)
            total_time = np.sum(time_diffs)
            
            if total_time > 0:
                result.loc[idx] = weighted_sum / total_time
            else:
                result.loc[idx] = prices.mean()  # 如果时间差为0，使用简单平均
        else:
            # 如果只有一个价格点，直接返回该价格
            if len(window_data) == 1:
                result.loc[idx] = window_data['price'].iloc[0]
            else:
                result.loc[idx] = 0
            
    return result





# 注册订单强度因子
factor_manager.register_factor(Factor(
    name="order_intensity",
    category=FactorCategory.TIME_SENSITIVE,
    description="订单强度（总订单到达率，每毫秒）",
    calculation=calculate_order_intensity,
    dependencies=["timestamp"],
    source="lining-orderbook-dynamic-features v6"
))

# 注册买卖比率因子
factor_manager.register_factor(Factor(
    name="buy_sell_ratio",
    category=FactorCategory.TIME_SENSITIVE,
    description="买卖比率",
    calculation=calculate_buy_sell_ratio,
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))

# 注册订单不平衡比率因子
factor_manager.register_factor(Factor(
    name="order_imbalance_ratio",
    category=FactorCategory.TIME_SENSITIVE,
    description="订单不平衡比率 ([-1, 1]范围)",
    calculation=calculate_order_imbalance_ratio,
    dependencies=["order_side", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))


# 注册时间加权平均价格因子
factor_manager.register_factor(Factor(
    name="time_weighted_average_price",
    category=FactorCategory.TIME_SENSITIVE,
    description="时间加权平均价格",
    calculation=calculate_time_weighted_average_price,
    dependencies=["price", "timestamp"],
    source="lining-orderbook-dynamic-features v6"
))