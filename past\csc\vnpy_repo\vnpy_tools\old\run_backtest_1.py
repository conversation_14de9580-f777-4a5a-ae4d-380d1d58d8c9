#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('DCE.prod')
import warnings
warnings.filterwarnings('ignore')

""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.basisStrategy import BasisStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
 
#%%
 
# maker='SR203.CZCE'
# refer='SR201.CZCE'
 
# multiplier=10
 
maker='c2203.DCE'
refer='c2205.DCE'
 
multiplier=10

#%%
def run_backtest(startdate, enddate, maker, refer, edge = 2, maxPos = 3, gamma = 0.3, alpha = 0.01, save_flag = False, name = 'basis_strategy', number = 0, typ = 'maker', onlyCrossFlag=False):
    save_strategy=True
    save_risk = True
    save_order = True
    save_trade = True
    onlyCrossFlag=False
    typ = 'maker'
    tick_size = 1
    duty=False
    lots=10
    
    parameters = {'alpha':alpha, 'edge':edge, 'gamma':gamma}
    
    engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=onlyCrossFlag,duty=True,save_result=False,refer_test=False,rule_out = False,fast_join=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
    start=datetime.datetime(startdate[0], startdate[1], startdate[2], 21, 0) # 开始时间
    end=datetime.datetime(enddate[0], enddate[1],enddate[2], 15, 0) # 结束时间
    
    engine.set_parameters(
        vt_symbols=[maker,refer], # 回测品种
        interval=Interval.TICK, # 回测模式的数据间隔
    
        start=start, # 开始时间
        end=end, # 结束时间
        rates={maker: 0,refer: 0}, # 手续费率
        slippages={maker: 0,refer: 0}, # 滑点
        sizes={maker: multiplier,refer: multiplier}, # 合约规模
        priceticks={maker: tick_size,refer: tick_size}, # 一个tick大小
        capital=1_000_000, # 初始资金
    )
    
    # 添加回测策略，并修改内部参数
    engine.clear_data()
    
    # engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})
    
    engine.add_strategy(BasisStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':maxPos,'alpha':alpha,'gamma':gamma,'typ':typ,'vol_flag':True})
    
    engine.load_data() # 加载历史数据
    engine.run_backtesting() # 进行回测
    
    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
    stat = engine.calculate_tick_statistics() # 统计日度策略指标
    df_mkt = pd.DataFrame()
    if save_flag:
        if save_risk:
            df_mkt = engine.get_risk(multiplier=multiplier)
        else:
            df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
        if save_strategy:
            df_strategy = engine.get_strategy()
        else:
            df_strategy = pd.DataFrame([])
        engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade)
    
    
    # print(engine.result)
    engine.duty_statistics()
    # from vnpy.trader.analysis import Analysis
    
    # analysis = Analysis(engine)
    # df = analysis.pnl_plot() 
    return engine, df_mkt #, df
# # =======
# # time_list = [7,8,11,12,13,14,15]
# time_list = [7,8]
#%%

multiplier=10
startdate, enddate, maker, refer, edge, maxPos, gamma, alpha, number = ([2022, 1, 13], [2022, 1, 14], 'c2203.DCE', 'c2205.DCE', 1.5, 30, 0.5, 0.03, 4)
engine, df_mkt = run_backtest(startdate, enddate, maker, refer, edge, maxPos, gamma, alpha, save_flag=True, number=number)
df_offer_list = pd.DataFrame(engine.strategy.offer_list)
df_offer_list.columns = engine.strategy.offer_list_head
 
    
#%%
a = get_risk(engine, multiplier=5)
    
test1 = a[a.symbol=='rb2202']
 
 
#%%
import time
import os
 
t = time.time()
result = {'insid' : [],
          'edge':[],
          'maxPos':[],
          'gamma':[],
          'alpha':[],
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []}
count = 1
 
k = 0
 
 
 
for i in range(10*k-9,np.min([len(time_list), 10*k+1])):
    startdate = [int(x) for x in time_list[i-1].split('/')]
    enddate = [int(x) for x in time_list[i].split('/')]
    for underlying in underlying_list:
        refer = underlying + '2201.SHFE'
        for m in maker_list:
            maker = underlying + m
            for edge in edge_list:
                for gamma in gamma_list:
                    for maxPos in maxPos_list:
                        for alpha in alpha_list:
                            try:
                                count += 1
                                engine = run_backtest(startdate=startdate, enddate=enddate, maker=maker, refer=refer, edge=edge, maxPos=maxPos, gamma=gamma, alpha=alpha, save_flag = True, name = 'basis_strategy', number = 0)
                                df = pd.DataFrame(engine.strategy.offer_list)
                                df.columns = ['datetime','theto','my_bidP','my_askP', 'maker_bidP', 'maker_askP', 'fair_refer','fair_maker', 'pos_adjust', 'ewma','Basis','net']
                                for key in engine.result.keys():
                                    result[key].append(engine.result[key])
                                result['insid'].append(maker)
                                result['edge'].append(edge)
                                result['maxPos'].append(maxPos)
                                result['gamma'].append(gamma)
                                result['alpha'].append(alpha)
                                
                                path = 'C:/Users/<USER>/Desktop/backtestresult/'+underlying+'/'+str(engine.end.date())+'/'
                                folder = os.path.exists(path)
                                if not folder:
                                    os.makedirs(path)
                                df.to_csv(path+maker.split('.')[0]+'-'+refer.split('.')[0]+'-'+str(edge)+'-'+str(maxPos)+'-'+str(int(10*gamma))+'-'+str(int(100*alpha))+'.csv')
                                df_result=pd.DataFrame(result)
 
                                df_result.to_csv('C:/Users/<USER>/Desktop/backtestresult/result_total'+str(k)+'.csv')
                                print(time.time()-t, count, (startdate, enddate, maker, refer, edge, maxPos, gamma, alpha))
                            except:
                                pass
 
 
 
#%%
df_dict = df.to_dict('records')
df_correct = []
net_last = 0
ask_last = 0
bid_last = 0
 
askamount = 0
bidamount = 0
askamount_refer = 0
bidamount_refer = 0
 
net = 0
pnl = 0
net_refer = 0
pnl_refer = 0
multiplier = 10
 
for row in df_dict:
    net = row['net']
    trade = net-net_last
    net_refer = - net
    fair_refer = row['fair_refer']
    maker_bidP = row['maker_bidP']
    maker_askP = row['maker_askP']
    my_bidP = row['my_bidP']
    my_askP = row['my_askP']    
    
    askamount += -trade*bid_last if trade < 0 else 0
    bidamount += trade*ask_last if trade > 0 else 0
    askamount_refer += trade*fair_refer if trade > 0 else 0
    bidamount_refer += -trade*fair_refer if trade < 0 else 0
    
    pnl = (askamount-bidamount+net*(row['maker_bidP']+row['maker_askP'])/2)*multiplier
    pnl_refer = (askamount_refer-bidamount_refer+net_refer*fair_refer)*multiplier
    
    
    net_last = net
    row['cumPnl'] = pnl+pnl_refer
    row['pnl'] = pnl
    row['pnl_refer'] = pnl_refer
    row['trade_price'] = maker_bidP if trade < 0 else maker_askP if trade > 0 else 0
    ask_last = row['maker_askP']
    bid_last = row['maker_bidP']
    
    df_correct.append(row.copy())
 
df_correct = pd.DataFrame(df_correct)
 
 
 
#%%
 
import pandas as pd
import numpy as np
from datetime import timedelta
 
trades = pd.DataFrame([{'time':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
    'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
    'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments
    } for x in engine.trades])
orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
    'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
    } for x in engine.get_all_orders()])
 
trades['net'] = (trades.volume*trades.direction).cumsum()
trades['cash'] = (trades['direction']*(-1)*trades['volume']*trades['price'])*engine.sizes[engine.vt_symbols[0]]
trades['realPnl'] = trades['cash'].cumsum()
trades['pnl'] = trades.realPnl + trades.net * trades.price * multiplier
 
 
mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
        'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
        'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
        'last_price','volume','turnover']]
mkt['time'] = mkt['datetime'].apply(lambda x : (x).strftime('%Y-%m-%d %H:%M:%S.%f'))
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))
 
mkt_main = mkt[mkt.symbol==engine.main_symbol]
mkt_main['dV'] = mkt_main.volume - mkt_main.volume.shift(1)
mkt_main['dT'] = (mkt_main.turnover - mkt_main.turnover.shift(1)).apply(lambda x:str(x))
 
#%%
df_test = df_correct[df_correct.trade_price!=0]
df_test = df_test.reset_index()
 
#%%
p1 = df_test.trade_price
p2 = trade_maker.price
 
p1 = np.array(p1)
p2 = np.array(p2)
 
for i in range(len(p1)):
    if p1[i] != p2[i]:
        print(i)