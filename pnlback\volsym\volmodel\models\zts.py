import numpy as np
from scipy.optimize import minimize
import sys
import os

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

from pnlback.volsym.volmodel.utils import get_data,find_best_method

class VolatilityModel:
    """波动率计算模型"""
    params_name = ['base_vol', 'wing', 'rr', 'wd']
    
    def __init__(self, rd=1.0):
        """初始化模型参数"""
        self.rd = rd
        self.params = None

    def calculate(self, spot, strike):
        """计算给定参数下的波动率"""
        if self.params is None:
            raise ValueError("模型参数未设置")
            
        base_vol, wing, rr,wd= self.params
        
        spot = np.asarray(spot)
        strike = np.asarray(strike)
        
        temp = np.abs(spot - strike) / (spot * 0.05)
        wing_adj = np.power(temp, wd) * wing * 0.01
        rr_adj = np.power(temp, self.rd) * rr * 0.01
        
        vol = np.where(
            spot > strike,
            base_vol + wing_adj - rr_adj,
            base_vol + wing_adj + rr_adj
        )
        
        return float(vol) if vol.size == 1 else vol
    
    def calc_rmse(self,spot,strike,weights,market_sigma):
        return np.sum(np.square(weights * (self.calculate(spot, strike) - market_sigma)))
    
def fit(spot, strike, weights, market_sigma,exp=None,config=None,last_exp_data=None,verbose=False,method=None):
    """拟合波动率模型并计算特征值"""
    # 创建模型
    model = VolatilityModel()
    
    # 设置参数优化边界
    bounds = [
        (0.01, 2),  # base_vol: 基础波动率范围 0.1-0.5 (10%-50%)
        (0.0, 5000.0),  # wing: 翼展参数范围 0-5 (0%-500%)
        (-2000.0, 2000.0),  # rr: 风险逆转参数范围 -2-2 (-200%-200%)
        (1.1, 1.5),  # wd: 翼展参数范围 0-10 (0%-1000%)
    ]

    initial_params = [0.2, 1.0, 0.0, 1.3]

    # 使用历史参数作为初始值，否则使用默认值
    if last_exp_data is not None and exp in last_exp_data:
        initial_params = [last_exp_data[exp].get(param, v) for param,v in zip(model.params_name,initial_params)]

    # 定义目标函数
    def objective(params):
        model.params = params
        fit_error = model.calc_rmse(spot, strike, weights, market_sigma)
        # 与上次参数的偏离程度
        if last_exp_data is not None and exp in last_exp_data:
            last_params = [last_exp_data[exp].get(param, v) for param,v in zip(model.params_name,initial_params)]
            # 计算加权参数变化惩罚
            param_change_penalty = sum([np.abs(config['param_weights'][param] * (p - l_p)) for p, l_p, param in zip(params, last_params, model.params_name)])
            # 组合所有权重
            final_penalty_weight = config['svi_weights']['param'] * param_change_penalty
            return fit_error + final_penalty_weight
        return fit_error

    # 执行拟合
    best_result, best_rmse, best_method = find_best_method(objective, initial_params, bounds,verbose=verbose,method=method)
        
    model.params = best_result.x
    sigma_fit = model.calculate(spot, strike)

    return sigma_fit, model, best_rmse, best_method


def fit_svi_model(config, exp_data, exp, last_exp_data=None):
    """拟合波动率模型并计算特征值"""
    # 准备数据
    exe_cut_set=np.exp(0.1)*100
    spot,strike,T,x,totalv,market_sigma,weights,x_raw = get_data(exp_data,exe_cut=config.get('exe_cut',exe_cut_set))
    
    # 创建模型
    model = VolatilityModel(rd=config.get('rd', 1.0))
    
    # 执行拟合
    sigma_fit, model,opt_rmse,best_method = fit(spot, strike, weights,market_sigma,exp=exp,config=config,last_exp_data=last_exp_data,verbose=False,method="Powell")
        
    # 计算拟合结果
    sigma_fit = model.calculate(exp_data['forward'].values, exp_data['K'].values)
    rmse_error = model.calc_rmse(exp_data['forward'].values, exp_data['K'].values, exp_data['vega'].values, exp_data['market_sigma'].values)

    vol_diff = sigma_fit - market_sigma
    px_diff = vol_diff*exp_data['vega']/0.0001*100
    sum_px_diff = np.sum(np.abs(px_diff))

    # 返回结果
    voltime = {
        **dict(zip(model.params_name, model.params)),
        'rd': model.rd,
        'rmse_error': rmse_error*1e10, 'best_method': best_method,
        'sum_px_diff': sum_px_diff
    }
    
    return exp, sigma_fit, voltime
