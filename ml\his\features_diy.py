import math
import numpy as np
import pandas as pd

signal_list = ['im5', 'im5vol', 'mid_minnum', 'mid_minnum2', 'mid_level', 'turnover_mid', 'im1', 'press', 'im2mult',
               'voi', 'edge_minnum']

class Signal:
    def __init__(self, data_md, minnum=1, roundnum=1, sig=signal_list, cal_sig=False):

        self.data_md = data_md
        self.addcol = []
        self.roundnum = roundnum
        self.minnum = minnum
        self.cal_sig = cal_sig
        self.sig = sig

    def sigall(self, ):
        data_md = self.data_md
        addcol = self.addcol
        sig = self.sig
        cal_sig = self.cal_sig
        if 'im1' in sig or cal_sig:
            data_md['im1'] = (
                    (data_md['BidVol1'] - data_md['AskVol1']) / (data_md['BidVol1'] + data_md['AskVol1'])).round(2)
            addcol += ['im1']
        if 'im5' in sig or cal_sig:
            self.signal_im5(5, )
        if 'imvol' in sig or cal_sig:
            self.signal_imvol(5, )
        if 'im2mult' in sig or cal_sig:
            self.signal_mult(2, )
        if 'mid_minnum' in sig or cal_sig:
            print('minnum', self.minnum)
            self.signal_minnum(self.minnum, )
        if 'mid_minnum2' in sig or cal_sig:
            print('minnum2', self.minnum)
            self.signal_minnum2(self.minnum, )
        if 'mid_level' in sig or cal_sig:
            self.signal_level(4, )
        if 'press' in sig or cal_sig:
            self.price_weighted_pressure(5, )
        if 'voi' in sig or cal_sig:
            self.volume_order_imbalance(5, )
        if 'turnover_mid' in sig or cal_sig:
            self.turnover_mid()
        if 'edge_minnum' in sig or cal_sig:
            self.edge_minnum(self.minnum)
        if 'mixedge_minnum' in sig or cal_sig:
            self.mixedge_minnum(self.minnum)

        return self.data_md, self.addcol

    def signal_im5(self, level, ):
        i1 = 1
        i2 = 1
        data_md = self.data_md
        addcol = self.addcol
        data_md['imsum'] = 0
        data_md['im5'] = 0
        for j in ['Bid', 'Ask']:
            for i in range(1, level + 1):
                data_md[f'imvol{j}{i}'] = 1 / data_md[f'{j}Vol{i}'] ** i1
                data_md[f'imprc{j}{i}'] = abs(data_md[f'{j}Price{i}'] / data_md['mid'] - 1) ** i2
                data_md['imsum'] += data_md[f'imvol{j}{i}'] * data_md[f'imprc{j}{i}']
                data_md['im5'] += data_md[f'{j}Price{i}'] * data_md[f'imvol{j}{i}'] * data_md[f'imprc{j}{i}']

        data_md[f'im{level}'] = np.round((data_md['im5'] / data_md['imsum'] - data_md['mid']), 2)
        addcol += [f'im{level}']

        self.data_md = data_md
        self.addcol = addcol

    def signal_imvol(self, level, ):
        # mean(vol/distance)
        i2 = 1
        finalcol = f'im{level}vol'
        data_md = self.data_md
        addcol = self.addcol
        for j in ['Bid', 'Ask']:
            data_md[finalcol + j] = 0
            for i in range(1, level + 1):
                data_md[finalcol + j] += data_md[f'{j}Vol{i}'] / (
                        abs(data_md[f'{j}Price{i}'] / data_md['mid'] - 1) ** i2)

        data_md[finalcol] = np.round((data_md[finalcol + 'Bid'] - data_md[finalcol + 'Ask'])
                                     / (data_md[finalcol + 'Bid'] + data_md[finalcol + 'Ask']), 2)
        addcol += [finalcol]
        self.data_md = data_md
        self.addcol = addcol

    def price_weighted_pressure(self, level, ):
        # ln(mean(vol/distance)) = bid - ask
        data = self.data_md
        addcol = self.addcol
        bench_price = data['mid']
        press = {}
        for j in ['Bid', 'Ask']:
            _d = [1 / abs(bench_price - data[f'{j}Price{s}']) for s in np.arange(1, level + 1)]  # 分子
            _denominator = sum(_d)  # 分母
            _weights = [(d / _denominator).replace(np.nan, 1) for d in _d]
            press[j] = sum([data[f'{j}Vol{i + 1}'] * w for i, w in enumerate(_weights)])

        data['press'] = np.round((np.log(press['Bid']) - np.log(press['Ask'])).replace([-np.inf, np.inf], np.nan), 2)
        addcol += ['press']

        self.data_md = data
        self.addcol = addcol

    def signal_mult(self, level, ):
        """
        计算加权订单簿不平衡指标:
        
        im{level}mult = (∑BidVol_i * w_i - ∑AskVol_i * w_i) / (∑BidVol_i * w_i + ∑AskVol_i * w_i)
        
        其中:
        - w_i 为各档位权重: w1=1.0, w2=0.5, w3=0.3, w4=0.2, w5=0.1
        - BidVol_i 为买i档委托量
        - AskVol_i 为卖i档委托量
        """
        mults = dict(mult1=1, mult2=0.5, mult3=0.3, mult4=0.2, mult5=0.1)
        finalcol = f'im{level}mult'
        temp_sum = 'immultsum'
        data_md = self.data_md
        addcol = self.addcol
        data_md[finalcol] = 0
        data_md[temp_sum] = 0
        for j in ['Bid', 'Ask']:
            for i in range(1, level + 1):
                # data_md[f'imprc{j}{i}'] = abs(data_md[f'{j}Price{i}'])
                if j == 'Bid':
                    data_md[finalcol] += data_md[f'{j}Vol{i}'] * mults[f'mult{i}']
                else:
                    data_md[finalcol] -= data_md[f'{j}Vol{i}'] * mults[f'mult{i}']
                data_md[temp_sum] += data_md[f'{j}Vol{i}'] * mults[f'mult{i}']

        data_md[finalcol] = (data_md[finalcol] / data_md[temp_sum]).round(2)
        addcol += [finalcol]

        self.data_md = data_md
        self.addcol = addcol

    def min_vol(self, x, minvol):
        # 计算最小价格
        for j in ['Bid', 'Ask']:
            s = 0
            for i in range(1, 6):
                s += x[f'{j}Vol{i}']
                if s > minvol:
                    x[f'{j}_min_level'] = i
                    x[f'{j}_min_prc'] = x[f'{j}Price{i}']
                    break
                elif i == 5:
                    x[f'{j}_min_level'] = i + 1
                    x[f'{j}_min_prc'] = x[f'{j}Price{i}']
        return [x['Bid_min_prc'], x[f'Ask_min_prc'], x['Bid_min_level'], x['Ask_min_level']]

    def signal_minnum(self, minvol, ):
        data_md = self.data_md
        addcol = self.addcol
        data_md[f'Bid_min_level'] = 0
        data_md[f'Ask_min_level'] = 0
        data_md[f'Bid_min_prc'] = 0
        data_md[f'Ask_min_prc'] = 0

        data_md[['Bid_min_prc', 'Ask_min_prc', 'Bid_min_level', 'Ask_min_level']] = data_md.apply(
            lambda x: self.min_vol(x, minvol), axis=1, result_type='expand')  # 可以返回多列
        data_md['mid_minnum'] = np.round(data_md[f'Bid_min_prc'] / 2 + data_md[f'Ask_min_prc'] / 2 - data_md['mid'], 2)
        addcol += [f'mid_minnum']

        self.data_md = data_md
        self.addcol = addcol

    def min_vol2(self, x, minvol):
        # 计算最小价格
        # 遍历买卖双方
        for j in ['Bid', 'Ask']:
            s = 0  # 累计成交量
            p = 0  # 累计价格*成交量
            for i in range(1, 6):
                s += x[f'{j}Vol{i}']  # 累加成交量
                p += x[f'{j}Price{i}'] * x[f'{j}Vol{i}']  # 累加价格*成交量
                if s > minvol:  # 当累计成交量超过最小成交量时
                    x[f'{j}_min_level'] = i  # 记录当前档位
                    if s!=0:
                        x[f'{j}_min_prc'] = p / s  # 计算加权平均价格
                    else:
                        x[f'{j}_min_prc'] = x[f'{j}Price{i}']  # 如果成交量为0则使用当前价格
                    break
                elif i == 5:  # 如果遍历到最后一档仍未超过最小成交量
                    x[f'{j}_min_level'] = i + 1  # 设置档位为6
                    if s != 0:
                        x[f'{j}_min_prc'] = p / s  # 计算加权平均价格
                    else:
                        x[f'{j}_min_prc'] = x[f'{j}Price{i}']  # 如果成交量为0则使用当前价格
        # 返回买卖双方的最小价格和对应档位
        return [x['Bid_min_prc'], x[f'Ask_min_prc'], x['Bid_min_level'], x['Ask_min_level']]

    def signal_minnum2(self, minvol, ):
        data_md = self.data_md
        addcol = self.addcol
        data_md[f'Bid_min_level'] = 0
        data_md[f'Ask_min_level'] = 0
        data_md[f'Bid_min_prc'] = 0
        data_md[f'Ask_min_prc'] = 0

        data_md[['Bid_min_prc', 'Ask_min_prc', 'Bid_min_level', 'Ask_min_level']] = data_md.apply(
            lambda x: self.min_vol2(x, minvol), axis=1, result_type='expand')  # 可以返回多列
        data_md['mid_minnum2'] = np.round(data_md[f'Bid_min_prc'] / 2 + data_md[f'Ask_min_prc'] / 2 - data_md['mid'], 2)
        addcol += [f'mid_minnum2']

        self.data_md = data_md
        self.addcol = addcol

    def mixedge_minnum(self, minvol, ):
        data_md = self.data_md
        addcol = self.addcol
        data_md[f'Bid_min_level'] = 0
        data_md[f'Ask_min_level'] = 0
        data_md[f'Bid_min_prc'] = 0
        data_md[f'Ask_min_prc'] = 0

        data_md[['Bid_min_prc', 'Ask_min_prc', 'Bid_min_level', 'Ask_min_level']] = data_md.apply(
            lambda x: self.min_vol(x, minvol), axis=1, result_type='expand')  # 可以返回多列
        data_md['mixedge_minnum'] = np.round(
            (data_md[f'Bid_min_prc'] / 2 + data_md[f'Ask_min_prc'] / 2 - data_md['mid'])
            / (data_md[f'Ask_min_prc'] - data_md[f'Bid_min_prc']) / 2, 2)
        addcol += [f'mixedge_minnum']

        self.data_md = data_md
        self.addcol = addcol

    def signal_level(self, level, ):
        data_md = self.data_md
        addcol = self.addcol
        data_md[f'Bid_min_level'] = 0
        data_md[f'Ask_min_level'] = 0
        data_md[f'Bid_min_prc'] = 0
        data_md[f'Ask_min_prc'] = 0

        data_md['mid_level'] = np.round(
            data_md[f'BidPrice{level}'] / 2 + data_md[f'AskPrice{level}'] / 2 - data_md['mid'],
            2)
        addcol += [f'mid_level']

        self.data_md = data_md
        self.addcol = addcol

    def edge_minnum(self, minvol, ):
        data_md = self.data_md
        addcol = self.addcol
        data_md[f'Bid_min_level'] = 0
        data_md[f'Ask_min_level'] = 0
        data_md[f'Bid_min_prc'] = 0
        data_md[f'Ask_min_prc'] = 0

        data_md[['Bid_min_prc', 'Ask_min_prc', 'Bid_min_level', 'Ask_min_level']] = (
            data_md.apply(lambda x: self.min_vol(x, minvol), axis=1, result_type='expand'))
        data_md['edge_minnum'] = np.round((data_md[f'Ask_min_prc'] - data_md[f'Bid_min_prc']) / 2 * self.roundnum, 2)
        addcol += [f'edge_minnum']

        self.data_md = data_md
        self.addcol = addcol

    def volume_order_imbalance(self, level, ):
        """
        Reference From <Order imbalance Based Strategy in High Frequency Trading>
        """
        data = self.data_md
        addcol = self.addcol
        data[f'voi'] = 0
        sumedge = 0
        for i in range(1, level + 1):
            bid_price_diff = data[f'BidPrice{i}'].diff(1)
            bvol_diff = data[f'BidVol{i}'].diff(1)
            bid_increment = np.where(bid_price_diff > 0, data[f'BidVol{i}'],
                                     np.where(bid_price_diff < 0, 0, bvol_diff))

            ask_price_diff = data[f'AskPrice{i}'].diff(1)
            avol_diff = data[f'AskVol{i}'].diff(1)
            ask_increment = np.where(ask_price_diff < 0, data[f'AskVol{i}'],
                                     np.where(ask_price_diff > 0, 0, avol_diff))

            _ = pd.Series(bid_increment - ask_increment, index=data.index)
            _.loc[_.groupby(_.index.date).apply(lambda x: x.index[0])] = np.nan  # drop first

            data[f'voi'] += _ / (data[f'AskPrice{i}'] - data[f'BidPrice{i}'])
            # sumedge += (data[f'AskPrice{i}'] - data[f'BidPrice{i}'])
            sumedge += 1 / (data[f'AskPrice{i}'] - data[f'BidPrice{i}'])

        data[f'voi'] = np.round(data[f'voi'] / sumedge, 2)
        addcol += [f'voi']

        self.data_md = data
        self.addcol = addcol

    def turnover_mid(self, ):
        data = self.data_md
        addcol = self.addcol
        # 计算中间价回归因子
        # data['turnover_mid'] = np.round((data['avg_prc'] / mult - (data['mid'].shift() + data['mid']) / 2) / data['edge'],
        #                                 2)
        data['turnover_mid'] = np.round((data['avg_prc'] - (data['mid'].shift() + data['mid']) / 2),2)
        # 之前考虑过的另一种计算方式，结合了交易量的对数变换
        # 旨在通过交易量信息对周转率的影响进行加权
        # data['turnover_mid'] = np.round((data['avg_prc'] / mult - (data['mid'].shift() + data['mid']) / 2)
        #                                 * (np.log(data['tradedVol'])+1), 2)
        # .shift() 向下移动，即取前值
        addcol += ['turnover_mid']

        self.data_md = data
        self.addcol = addcol

    def CDFShift(self, kg, scale1, weight1):
        # 标准化
        CDF1 = (1 / (1 + math.exp(-(np.mean(kg) - 0) / scale1)) - 0.5) * 2

        shift = CDF1 * weight1

        return shift


