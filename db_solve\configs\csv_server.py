
SERVER = 'http://168.231.2.88:9528'
TRANS_URL = '/ssh/api/downloadBySftp'
DOWNLOAD_URL = '/fs/download/'

ENV_CONFIG = {
    'SHFE': {
        'server_id': 26,
        'remote_paths': [
            '/app/latest-shfe-fcore/Data/OrderDetail_*.csv'
        ]
    },
    'DCE': {
        'server_id': 60,
        'remote_paths': [
            '/app/latest-dce-fcore/Data/OrderDetail_*.csv'
        ]
    },
    'ZCE': {
        'server_id': 22,
        'remote_paths': [
            '/app/latest-zce-fcore/Data/OrderDetail_*.csv'
        ]
    },
    'SH300': {
        'server_id': 39,
        'remote_paths': [
            '/app/latest-sse-vcore/MD-log/md_*_cffex_multicast.csv',
            '/app/latest-sse-vcore/MD-log/md_*_tcp_receiver_1_3276.csv',
            '/app/latest-sse-vcore/MD-log/md_*_udp_receiver_1_25103.csv',
            '/app/latest-sse-vcore/MD-log/md_*_sse_mdgw.csv',
            '/app/latest-sse-vcore/Data/*_OrderDetails.csv'
        ]
    },
    'SH500': {
        'server_id': 76,
        'remote_paths': [
            '/app/latest-sse-vcore/MD-log/md_*_cffex_multicast.csv',
            '/app/latest-sse-vcore/MD-log/md_*_tcp_receiver_1_3276.csv',
            '/app/latest-sse-vcore/MD-log/md_*_udp_receiver_2_25103.csv',
            '/app/latest-sse-vcore/MD-log/md_*_sse_mdgw.csv',
            '/app/latest-sse-vcore/Data/*_OrderDetails.csv'
        ]
    },
    'SH50': {
        'server_id': 38,
        'remote_paths': [
            '/app/latest-sse-vcore/MD-log/md_*_cffex_multicast.csv',
            '/app/latest-sse-vcore/MD-log/md_*_tcp_receiver_1_3276.csv',
            '/app/latest-sse-vcore/MD-log/md_*_udp_receiver_2_25103.csv',
            '/app/latest-sse-vcore/MD-log/md_*_sse_mdgw.csv',
            '/app/latest-sse-vcore/Data/*_OrderDetails.csv'
        ]
    },
    'SZ100': {
        'server_id': 9,
        'remote_paths': [
            '/app/latest-sse-vcore/MD-log/md_*_cffex_multicast.csv',
            '/app/latest-sse-vcore/MD-log/md_*_shm_receiver_sub.csv',
            '/app/latest-sse-vcore/MD-log/md_*_sse_mdgw.csv',
            '/app/latest-sse-vcore/Data/*_OrderDetails.csv'
        ]
    },
    'VOLS_SH500': {
        'server_id': 77,
        'remote_paths': [
            '/home/<USER>/.archive/vol_log/510500/vols_*.zip',
        ]
    },
}

CHUNK_SIZE = 50*1024*1024


'''
config.py
    SERVER: 指定的运维平台domain
    TRANS_URL: 服务器文件转存到数据中台的请求url
    DOWNLOAD_URL: 从数据中台下载到本地交易机的url
    ENV_CONFIG: 配置映射表
        server_id 是交易服务器的id，在运维平台服务器管理条目点击左侧箭头展开后查看ID
        remote_paths 配置远程交易服务器上要下载的文件绝对路径，* 号是日期占位符，通过handle_files 处理后变成 %Y%m%d 格式
    SAVE_BASE_DIR: 交易机本地保存路径，windows是双反斜线作为路径分隔符
    CHUNK_SIZE: 从数据中台下载文件时通过块的方式写入到本地，这是显示块的大小，默认5M
'''