import itertools
import numpy as np
import pandas as pd
from pulp import LpProblem, LpVariable, LpMinimize, lpSum, LpInteger, LpStatusOptimal, value, PULP_CBC_CMD
from tqdm import tqdm

def ilp_decompose_trade_prices(last, volume, turnover, tick_size=0.2, max_levels=11, verbose=False):
    """
    用整数线性规划精确分解成交价分布，严格满足VWAP和总量约束。
    返回: (分布dict, VWAP误差, 是否最优)
    """
    vwap = turnover / volume
    last = round(last / tick_size) * tick_size
    vwap = round(vwap / tick_size, 6) * tick_size
    N = max_levels // 2
    min_p = min(last, vwap) - N * tick_size
    max_p = max(last, vwap) + N * tick_size
    price_levels = np.round(np.arange(min_p, max_p + tick_size/2, tick_size), 6)
    price_levels = price_levels[price_levels > 0]
    if last not in price_levels:
        price_levels = np.sort(np.append(price_levels, last))
    if vwap not in price_levels:
        price_levels = np.sort(np.append(price_levels, vwap))
    price_levels = [float(p) for p in price_levels]
    # ILP建模
    prob = LpProblem("VWAP_Decompose", LpMinimize)
    vols = [LpVariable(f'v_{i}', lowBound=0, cat=LpInteger) for i in range(len(price_levels))]
    # 目标：让vwap附近的价格数量最多，离vwap越远数量越少（严格递减）
    vwap_idx = price_levels.index(vwap)
    # 目标函数：sum(|p-vwap|*v) + 1e-6*sum(|p-last|*v)  (主目标是让数量集中在vwap)
    prob += lpSum([abs(p - vwap) * v for p, v in zip(price_levels, vols)]) + 1e-6 * lpSum([abs(p - last) * v for p, v in zip(price_levels, vols)])
    # 约束1：总成交量
    prob += lpSum(vols) == int(volume)
    # 约束2：VWAP
    prob += lpSum([p * v for p, v in zip(price_levels, vols)]) == turnover
    # 约束3：last必须有成交
    prob += vols[price_levels.index(last)] >= 1
    # 求解（关闭CBC求解器所有输出）
    status = prob.solve(PULP_CBC_CMD(msg=verbose))
    if status == LpStatusOptimal:
        result = {p: int(value(v)) for p, v in zip(price_levels, vols) if int(value(v)) > 0}
        actual_vwap = sum([p*v for p,v in result.items()]) / volume
        vwap_error = abs(actual_vwap - vwap)
        if verbose:
            print(f"[ILP] VWAP目标: {vwap}, 实际: {actual_vwap}, 误差: {vwap_error}")
        return result, vwap_error, True
    else:
        if verbose:
            print(f"[ILP] 无可行解，max_levels={max_levels}")
        return None, None, False

def simple_decompose_trade_prices(last, volume, turnover, min_price=None, max_price=None, tick_size=0.2,round_price=6):
    """
    改进版简单分解：先分配1手到last价，其余按VWAP附近n个tick分配,n为vwap和last的tick距离。
    """
    last_rounded = last

    if int(volume) == 1:
        return {last_rounded: 1}, abs((last_rounded - turnover / volume))
    # 先分配1手到last
    remain_vol = int(volume) - 1
    remain_turnover = turnover - last_rounded
    vwap = remain_turnover / remain_vol
    lower = np.floor(vwap / tick_size) * tick_size
    upper = np.ceil(vwap / tick_size) * tick_size
    if min_price is None:
        min_price = min(last,vwap,lower-5)
    if max_price is None:
        max_price = max(last,vwap,upper+5)
    min_price = min(last, vwap, min_price)
    max_price = max(last, vwap, max_price)
    lower = round(lower, round_price)
    upper = round(upper, round_price)
    dist = {last_rounded: 1}
    if abs(upper - lower) < 1e-8:
        dist[lower] = dist.get(lower, 0) + remain_vol
    else:
        x = (remain_turnover - upper * remain_vol) / (lower - upper)
        y = remain_vol - x
        x_int = int(round(x))
        y_int = int(round(y))
        if x_int + y_int != remain_vol:
            if abs(x - x_int) > abs(y - y_int):
                x_int = remain_vol - y_int
            else:
                y_int = remain_vol - x_int
        if x_int > 0:
            dist[lower] = dist.get(lower, 0) + x_int
        if y_int > 0:
            dist[upper] = dist.get(upper, 0) + y_int
    # 合并last和VWAP tick
    if dist.get(last_rounded, 0) == 0:
        del dist[last_rounded]
    # 以量最大的价格为中心, 向两边扩展, 直到成交量为0
    max_vol = max(dist.values())
    max_vol_price = max(dist.keys(), key=lambda x: dist[x])
    dis=min((max_vol_price-min_price)/tick_size,(max_price-max_vol_price)/tick_size)
    for i in range(int(abs(dis)) + 1):
        if dist[max_vol_price]>2 and dist[max_vol_price]>=max_vol/2:
            price_plus = round(max_vol_price + i * tick_size, round_price)
            price_minus = round(max_vol_price - i * tick_size, round_price)
            dist[price_plus] = dist.get(price_plus, 0) + 1
            dist[price_minus] = dist.get(price_minus, 0) + 1
            dist[max_vol_price] = dist.get(max_vol_price, 0) - 2
        else:
            break
    #从小到大排序
    dist = dict(sorted(dist.items(), key=lambda x: x[0]))
    # 计算实际VWAP误差
    actual_vwap = sum([p*v for p,v in dist.items()]) / volume
    vwap_error = abs(actual_vwap - (turnover / volume))
    return dist, vwap_error

def decompose_trade_prices(last, volume, turnover, min_price=None, max_price=None, tick_size=0.2, max_levels=11, max_vwap_error=None, verbose=False, show_progress=False, method='ilp',round_price=6):
    """
    支持ILP法和简单法，批量和单组均兼容。
    method: 'ilp' 或 'simple'
    """
    if isinstance(last, (np.ndarray, list, pd.Series)):
        last = np.asarray(last)
        volume = np.asarray(volume)
        turnover = np.asarray(turnover)
        min_price = np.asarray(min_price)
        max_price = np.asarray(max_price)
        results = []
        total = len(last)
        iterator = zip(last, volume, turnover,min_price,max_price)
        if show_progress:
            iterator = tqdm(iterator, total=total, desc="VWAP分布计算")
        for p, v, t, min_p, max_p in iterator:
            if abs(v) > 0:
                results.append(decompose_trade_prices(p, v, t, tick_size=tick_size, max_levels=max_levels, max_vwap_error=max_vwap_error, verbose=verbose,min_price=min_p, max_price=max_p,  show_progress=False, method=method,round_price=round_price))
            else:
                results.append(({0: 0}, 0.0, True))
        return results

    assert np.isscalar(volume) and np.isscalar(last) and np.isscalar(turnover), "Single mode expects scalars!"
    if volume <= 0:
        return {round(last / tick_size) * tick_size: 0}, 0.0
    if method == 'simple':
        return simple_decompose_trade_prices(last, volume, turnover, tick_size=tick_size,min_price=min_price, max_price=max_price,round_price=round_price)
    elif method == 'ilp':
        for levels in range(max_levels, 21, 2):
            dist, err, ok = ilp_decompose_trade_prices(last, volume, turnover, tick_size=tick_size, max_levels=levels, verbose=verbose,min_price=min_price, max_price=max_price,round_price=round_price)
            if ok:
                return dist, err
        if verbose:
            print("[ILP] 无解，返回空")
    else:
        raise ValueError(f"Invalid method: {method}")

    return {}, None

if __name__ == '__main__':
    # 单组测试
    last = 6066.2
    volume = 86.0
    turnover = 521999.6
    tick_size = 0.2
    dist, err = decompose_trade_prices(last, volume, turnover, tick_size, max_levels=11, verbose=False, method='simple')
    total = 0
    for k, v in dist.items():
        total += v*k
    err = total - turnover

    print(f"单组分布: {dist}, VWAP误差: {err:.2e}, err2: {err:.2f}, 成交量: {volume}, 成交额: {turnover}, 最新trd: {last}")
    # # 批量测试
    # df = pd.DataFrame(0.0, index=range(1000), columns=range(3))
    # df.iloc[:,0] = last
    # df.iloc[:,1] = volume
    # df.iloc[:,2] = turnover
    # last = df.iloc[:,0]
    # volume = df.iloc[:,1]
    # turnover = df.iloc[:,2]
    # batch = decompose_trade_prices(last, volume, turnover, tick_size, max_levels=11, verbose=False, show_progress=True)
    # print("批量分布前3:", batch[:3])
