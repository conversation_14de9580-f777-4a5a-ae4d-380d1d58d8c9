{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": true, "slideshow": {"slide_type": "slide"}}, "source": ["# The microprice\n", "\n", "An estimator of the fair price, given the state of the order book\n", "\n", "KISS\n", "\n", "https://papers.ssrn.com/sol3/papers.cfm?abstract_id=2970694"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Big question:\n", "\n", "What is the fair price, given the bid/ask prices and sizes?\n", "\n", "We will call this notion of fair price the microprice.\n", "\n", "This notion can be useful to algorithmic traders, HFT, market makers, etc..."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Definitions\n", "\n", "The bid, ask, bid size, ask size: $P^b, P^a, Q^b, Q^a$\n", "\n", "The mid-price: $$M=\\frac{P^b+P^a}{2}$$\n", "\n", "The weighted mid-price: $$M=P^b (1-I) +P^a I$$\n", "\n", "The imbalance: $$I=\\frac{Q_b}{Q_b+Q_a}$$\n", "\n", "The bid-ask spread: $$S=(P^a-P^b)$$"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true, "slideshow": {"slide_type": "subslide"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_df(ticker):\n", "    file1='%s_20110301_20110331.csv'%str(ticker)\n", "    df=pd.read_csv(file1)\n", "    df.columns = ['date','time','bid','bs','ask','as']\n", "    df = df.dropna()\n", "    df['date']=df['date'].astype(float)\n", "    df['time']=df['time'].astype(float)\n", "    df['bid']=df['bid'].astype(float)\n", "    df['ask']=df['ask'].astype(float)\n", "    df['bs']=df['bs'].astype(float)\n", "    df['as']=df['as'].astype(float)\n", "    df['mid']=(df['bid'].astype(float)+df['ask'].astype(float))/2\n", "    df['imb']=df['bs'].astype(float)/(df['bs'].astype(float)+df['as'].astype(float))\n", "    df['wmid']=df['ask'].astype(float)*df['imb']+df['bid'].astype(float)*(1-df['imb'])\n", "    return df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>time</th>\n", "      <th>bid</th>\n", "      <th>bs</th>\n", "      <th>ask</th>\n", "      <th>as</th>\n", "      <th>mid</th>\n", "      <th>imb</th>\n", "      <th>wmid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>40603.0</td>\n", "      <td>34222.0</td>\n", "      <td>14.33</td>\n", "      <td>20.0</td>\n", "      <td>14.34</td>\n", "      <td>471.0</td>\n", "      <td>14.335</td>\n", "      <td>0.040733</td>\n", "      <td>14.330407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>40603.0</td>\n", "      <td>34223.0</td>\n", "      <td>14.33</td>\n", "      <td>25.0</td>\n", "      <td>14.34</td>\n", "      <td>148.0</td>\n", "      <td>14.335</td>\n", "      <td>0.144509</td>\n", "      <td>14.331445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>40603.0</td>\n", "      <td>34224.0</td>\n", "      <td>14.33</td>\n", "      <td>31.0</td>\n", "      <td>14.34</td>\n", "      <td>95.0</td>\n", "      <td>14.335</td>\n", "      <td>0.246032</td>\n", "      <td>14.332460</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>40603.0</td>\n", "      <td>34225.0</td>\n", "      <td>14.33</td>\n", "      <td>29.0</td>\n", "      <td>14.34</td>\n", "      <td>92.0</td>\n", "      <td>14.335</td>\n", "      <td>0.239669</td>\n", "      <td>14.332397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>40603.0</td>\n", "      <td>34226.0</td>\n", "      <td>14.33</td>\n", "      <td>21.0</td>\n", "      <td>14.34</td>\n", "      <td>65.0</td>\n", "      <td>14.335</td>\n", "      <td>0.244186</td>\n", "      <td>14.332442</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      date     time    bid    bs    ask     as     mid       imb       wmid\n", "0  40603.0  34222.0  14.33  20.0  14.34  471.0  14.335  0.040733  14.330407\n", "1  40603.0  34223.0  14.33  25.0  14.34  148.0  14.335  0.144509  14.331445\n", "2  40603.0  34224.0  14.33  31.0  14.34   95.0  14.335  0.246032  14.332460\n", "3  40603.0  34225.0  14.33  29.0  14.34   92.0  14.335  0.239669  14.332397\n", "4  40603.0  34226.0  14.33  21.0  14.34   65.0  14.335  0.244186  14.332442"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data=get_df('BAC')[0:100]\n", "data.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x1021cae48>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYQAAAD8CAYAAAB3u9PLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nIYiICKCEICIiISUEEREB4H+R4kD7pJhHhgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x10d59ef60>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data[['bid','ask','mid','wmid']].plot()"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# The mid vs. the weighted mid\n", "\n", "The mid-price:\n", "- Not a martingale (Bid-ask bounce)\n", "- Medium frequency signal \n", "- Doesn't use volume at the best bid and ask prices. \n", "\n", "The weighted mid-price:\n", "- Uses the volume at the best bid and ask prices.\n", "- High frequency signal\n", "- Is quite noisy, particularly when the spread widens to two ticks\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Desirable features of the Micro-Price\n", "\n", " $P_t^{micro}=F(M_t,I_t,S_t) = M_t+ G(I_t,S_t)$ \n", "- <PERSON><PERSON><PERSON>\n", "- Computationally fast\n", "- Better short term price predictions than the midprice or weighted midprice\n", "- Should work for large tick stocks (like BAC) or small tick stocks (like CVX)"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Outline\n", "\n", "1. General definition\n", "2. A discrete Markov model\n", "3. Data analysis\n", "4. Conclusion"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Micro-price definition\n", "\n", "Define\n", "\\begin{equation*}\n", "P_t^{micro}=\\lim_{n\\to\\infty} P_t^n\n", "\\end{equation*}\n", "where the approximating sequence of martingale prices is given by\n", "$$P^n_t=\\mathbb{E} \\left[  M_{\\tau_n} |  I_t,S_t \\right] $$\n", "\n", "$\\tau_1,...,\\tau_n$ are (random) times when the mid-price $M_t$ changes\n", " \n", "The micro-price is the expected mid-price in the distant future\n", " \n", "In practice, the distant future is well captured by $P_t^6$, the expected mid price after 6 price moves."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Main result\n", "\n", "\n", "The $i$-th approximation to the micro-price can be written as\n", "\\begin{equation*}\n", "P_t^{n} =  M_t + \\sum_{k=1}^{n}g^k(I_t,S_t) \n", "\\end{equation*}\n", "where\n", "$$g^1(I_t,S_t)= \\mathbb{E}  \\left[  M_{\\tau_1} - M_t  | I_t, S_t \\right] $$\n", "and\n", "$$\n", "g^{n+1}(I_t,S_t)= \\mathbb{E}\\left[ g^n(I_{\\tau_{1}},S_{\\tau_{1}}) | I_{t},S_t\\right], \\forall j\\geq 0\n", "$$\n", "can be computed recursively."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Finite state Markov chain\n", "\n", "- The imbalance takes discrete values $1\\leq i_I \\leq n$, \n", "- The spread takes discrete values $1\\leq i_S \\leq m$\n", "- The mid-price changes takes values in $K =[-0.01 \\quad -0.005 \\quad 0.005 \\quad 0.01]$. \n", "- Define the state $X_t=(I_t,S_t)$ with discrete values $1\\leq i \\leq nm$"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Computing $g^1$\n", "\n", "The first step approximation to the micro-price \n", "\\begin{align*}\n", "g^1(i) = &  \\mathbb{E} \\left[M_{\\tau_{1}}- M_{t} | X_t= i \\right] \\\\\n", "= {}& \\big(1-Q\\big)^{-1}R^1\\underline{k}\n", "\\end{align*}\n", "\n", "Where\n", "$$Q_{ij} := \\mathbb{P}(M_{t+1}- M_t =0 \\wedge X_{t+1}=j | X_t= i)$$\n", "are the transition probabilities for transient states (mid price does not move) \n", "\n", "$$R^1_{ik} := \\mathbb{P}(M_{t+1}- M_t =k | X_t = i)$$ are the transition probabilities into absorbing states (mid price does move) \n", "\n", "and $\\underline{k}=[-0.01 \\quad -0.005 \\quad 0.005 \\quad 0.01]^T$ "]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Computing $g^{i+1}$\n", "\n", "\n", "We can compute recursively\n", "\\begin{align*}\n", "g^{n+1}=  B g^n\n", "\\end{align*}\n", "where $B:=\\big(1-Q\\big)^{-1}R^2$\n", "\n", "and $R^2$ is a new matrix of absorbing states\n", "\\begin{align*}\n", "R^2_{ik} := {}& \\mathbb{P}(M_{t+1}- M_t \\neq 0 \\wedge I_{t+1}=k  | I_t = i)\n", "\\end{align*}"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Does the micro-price converge?\n", "\n", "Yes. But we have to appropriately symmetrize the data.\n", "\n", "Technical conditions are available in the paper."]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "slide"}}, "source": ["# Data analysis on BAC and CVX"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Estimation\n", "\n", "1. On every quote, compute $I_t, S_t, (M_{t+1}-M_t)$, after having discretized the state space\n", "2. Symmetrize the data, by making a copy where $I^2_t=n-I_t, S^2_t=S_t, (M^2_{t+1}-M^2_t)=-(M_{t+1}-M_t)$\n", "3. Estimate transition probability matrices $Q, R^1, R^2$\n"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# Computation\n", "\n", "Compute the first micro-price adjustment:\n", "$$p^{1}-M = g^1 = \\big(1-Q\\big)^{-1}R^1\\underline{k}$$\n", "Use our recursive formula to compute the 6th micro-price adjustment:\n", "$$p^{6}-M = g^1+g^2+ \\ldots +g^6 = g^1 + B g^1 + \\ldots + B^5 g^1 $$\n", "In practice this converges after 6 price moves"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["from scipy.linalg import block_diag\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true, "slideshow": {"slide_type": "subslide"}}, "outputs": [], "source": ["def prep_data_sym(T,n_imb,dt,n_spread):\n", "    spread=T.ask-T.bid\n", "    ticksize=np.round(min(spread.loc[spread>0])*100)/100\n", "    T.spread=T.ask-T.bid\n", "    # adds the spread and mid prices\n", "    T['spread']=np.round((T['ask']-T['bid'])/ticksize)*ticksize\n", "    T['mid']=(T['bid']+T['ask'])/2\n", "    #filter out spreads >= n_spread\n", "    T = T.loc[(T.spread <= n_spread*ticksize) & (T.spread>0)]\n", "    T['imb']=T['bs']/(T['bs']+T['as'])\n", "    #discretize imbalance into percentiles\n", "    T['imb_bucket'] = pd.qcut(T['imb'], n_imb, labels=False)\n", "    T['next_mid']=T['mid'].shift(-dt)\n", "    #step ahead state variables\n", "    T['next_spread']=T['spread'].shift(-dt)\n", "    T['next_time']=T['time'].shift(-dt)\n", "    T['next_imb_bucket']=T['imb_bucket'].shift(-dt)\n", "    # step ahead change in price\n", "    T['dM']=np.round((T['next_mid']-T['mid'])/ticksize*2)*ticksize/2\n", "    T = T.loc[(T.dM <= ticksize*1.1) & (T.dM>=-ticksize*1.1)]\n", "    # symetrize data\n", "    T2 = T.copy(deep=True)\n", "    T2['imb_bucket']=n_imb-1-T2['imb_bucket']\n", "    T2['next_imb_bucket']=n_imb-1-T2['next_imb_bucket']\n", "    T2['dM']=-T2['dM']\n", "    T2['mid']=-T2['mid']\n", "    T3=pd.concat([T,T2])\n", "    T3.index = pd.RangeIndex(len(T3.index)) \n", "    return T3,ticksize"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": true, "slideshow": {"slide_type": "subslide"}}, "outputs": [], "source": ["def estimate(T):\n", "    no_move=T[T['dM']==0]\n", "    no_move_counts=no_move.pivot_table(index=[ 'next_imb_bucket'], \n", "                     columns=['spread', 'imb_bucket'], \n", "                     values='time',\n", "                     fill_value=0, \n", "                     aggfunc='count').unstack()\n", "    Q_counts=np.resize(np.array(no_move_counts[0:(n_imb*n_imb)]),(n_imb,n_imb))\n", "    # loop over all spreads and add block matrices\n", "    for i in range(1,n_spread):\n", "        Qi=np.resize(np.array(no_move_counts[(i*n_imb*n_imb):(i+1)*(n_imb*n_imb)]),(n_imb,n_imb))\n", "        Q_counts=block_diag(Q_counts,Qi)\n", "    #print Q_counts\n", "    move_counts=T[(T['dM']!=0)].pivot_table(index=['dM'], \n", "                         columns=['spread', 'imb_bucket'], \n", "                         values='time',\n", "                         fill_value=0, \n", "                         aggfunc='count').unstack()\n", "\n", "    R_counts=np.resize(np.array(move_counts),(n_imb*n_spread,4))\n", "    T1=np.concatenate((Q_counts,R_counts),axis=1).astype(float)\n", "    for i in range(0,n_imb*n_spread):\n", "        T1[i]=T1[i]/T1[i].sum()\n", "    Q=T1[:,0:(n_imb*n_spread)]\n", "    R1=T1[:,(n_imb*n_spread):]\n", "\n", "    K=np.array([-0.01, -0.005, 0.005, 0.01])\n", "    move_counts=T[(T['dM']!=0)].pivot_table(index=['spread','imb_bucket'], \n", "                     columns=['next_spread', 'next_imb_bucket'], \n", "                     values='time',\n", "                     fill_value=0, \n", "                     aggfunc='count') #.unstack()\n", "\n", "    R2_counts=np.resize(np.array(move_counts),(n_imb*n_spread,n_imb*n_spread))\n", "    T2=np.concatenate((Q_counts,R2_counts),axis=1).astype(float)\n", "\n", "    for i in range(0,n_imb*n_spread):\n", "        T2[i]=T2[i]/T2[i].sum()\n", "    R2=T2[:,(n_imb*n_spread):]\n", "    Q2=T2[:,0:(n_imb*n_spread)]\n", "    G1=np.dot(np.dot(np.linalg.inv(np.eye(n_imb*n_spread)-Q),R1),K)\n", "    B=np.dot(np.linalg.inv(np.eye(n_imb*n_spread)-Q),R2)\n", "    \n", "    return G1,B,Q,Q2,R1,R2,K"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true, "slideshow": {"slide_type": "subslide"}}, "outputs": [], "source": ["def plot_Gstar(ticker,G1,B,T):\n", "    G2=np.dot(B,G1)+G1\n", "    G3=G2+np.dot(np.dot(B,B),G1)\n", "    G4=G3+np.dot(np.dot(np.dot(B,B),B),G1)\n", "    G5=G4+np.dot(np.dot(np.dot(np.dot(B,B),B),B),G1)\n", "    G6=G5+np.dot(np.dot(np.dot(np.dot(np.dot(B,B),B),B),B),G1)\n", "    plt.plot(imb,np.linspace(-0.005,0.005,n_imb)*0,label='Mid adj',marker='o')\n", "    plt.plot(imb,np.linspace(-0.005,0.005,n_imb),label='Weighted mid adj',marker='o')\n", "    for i in range(0,n_spread):\n", "        plt.plot(imb,G6[(0+i*n_imb):(n_imb+i*n_imb)],label=\"spread = \"+str(i+1)+\" tick adj\",marker='o')\n", "    plt.ylim(-0.005,0.005)\n", "    plt.legend(loc='upper left')\n", "    plt.title(ticker+' adjustments')\n", "    plt.xlabel('Imbalance')\n", "    return G6"]}, {"cell_type": "markdown", "metadata": {"slideshow": {"slide_type": "subslide"}}, "source": ["# BAC"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true, "slideshow": {"slide_type": "subslide"}}, "outputs": [], "source": ["n_imb=10\n", "n_spread=2\n", "dt=1\n", "data=get_df('BAC') \n", "ticker='BAC'\n", "pd.set_option('mode.chained_assignment', None)\n", "import warnings\n", "warnings.simplefilter(action='ignore', category=UserWarning)\n", "T,ticksize=prep_data_sym(data,n_imb,dt,n_spread)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYwAAAEWCAYAAAB1xKBvAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAIABJREFUeJzs3Xd0lMXXwPHvZNMLCZ2EjoCUJECkShGJFGlSlJ8gFhAQ\nFQVUsAEi6iuCihQVERSwgggIRpAuSJHeOwgSCC2QhPTs7rx/7CZseu/3c44n2afMMxvi3szcKUpr\njRBCCJEZu8KugBBCiOJBAoYQQogskYAhhBAiSyRgCCGEyBIJGEIIIbJEAoYQQogskYAhRC4opZ5R\nSv1t8zpSKVWnMOskRH6RgCGKNKXUBaVUjPWD+LZSKkgpVT2N6yYrpbRSqlUa57yVUguUUiFKqTtK\nqZNKqXeVUm55XV+ttbvW+nxuylBKLVRKvZ9Xdcrms2tZf472hfF8UbRJwBDFQS+ttTvgDVwDZtue\nVEop4CnglvWr7blywE7ABWijtfYAOgOewD35X3UhSg4JGKLY0FrHAsuARilOtccSTF4GHldKOdqc\newW4AwzWWl+wlnNJaz1Ga304recopX5RSl1VSoUrpbYqpRrbnCuvlFqllIpQSu0mRdCx/nVe1/r9\nFqXUMJtzSd1XymKGUuq6tawjSilfpdQI4AlgvLVVtdp6/QWl1Dil1GGlVJS1xVRZKbXG2mraoJQq\na/Os1kqpHUqpMKXUIaVUR5tzW5RS7ymltlvvXaeUqmA9vdX6Ncz6/DZKqbpKqb+sP4+bSqkl6f8r\niZJMAoYoNpRSrsD/gF0pTj0NrAaWWl/3sjn3ELBca23OxqPWAPWASsB+4Aebc58DsVgC1FDrfznR\nBegA1MfS2hkAhGqt51mfN83avWX7XvpjaR3Vx/Ie1wBvARWx/L/8MoBSqioQBLwPlANeA35VSlW0\nKWsQMMT6Hh2t12CtE4CX9fk7gfeAdUBZoBopWnii9JCAIYqDlUqpMCAcywfm9MQT1iDyGPCj1joB\nSwvEtluqPBCSnYdprb/RWt/RWscBk4EmSilPpZQBy4f2JK11lNb6KLAoh+8pAfAAGgBKa31Ca51Z\nPWdrra9prS8D24B/tNYHrC2vFUAz63WDgT+01n9orc1a6/XAXqC7TVnfaq1Pa61jsATappnUtSbg\no7WO1Vr/ncG1ogSTgCGKgz5aay/AGRgF/KWUqmI91xcwAn9YX/8APGzz13QoltZAliilDEqpqUqp\nc0qpCOCC9VQFLH/J2wOXbG65mIP3g9Z6EzAHS4vlulJqnlKqTCa3XbP5PiaN1+7W72sCj1m7o8Ks\nwbYdyX8OV22+j7a5Ny3jAQXsVkodU0rltFUlijkJGKLY0FqbtNbLAROWD0CwdEe5A/8ppa4CvwAO\nWLpcADYAfZVSWf1dHwQ8gqUryxOoZT2ugBtYgpPtKK0aGZQVBbjavK5ie1JrPUtrfR+WnEx9YFzi\nqSzWNT2XgO+01l42/7lpradm4d5Uz9ZaX9VaD9da+wDPAV8k5mlE6SIBQxQb1kTxI1j60k9Y++oD\ngZ5YulSaAk2Aj7jbLfUpUAZYpJSqaS2nqlLqU6WUfxqP8QDisLRMXIH/SzyhtTYBy4HJSilXpVQj\nLAErPQeBftZr6wLP2ryXFkqpVkopByyBJRZIzLNcA3Izl+N7oJdSqqu1xeSslOqolKqWhXtvWOuR\n9Hyl1GM2997GElSykxMSJYQEDFEcrFZKRQIRwAfA01rrY8CTwEGt9TrrX8FXtdZXgVmAv1LKV2t9\nC7gfSz/8P0qpO8BGLPmQs2k8azGWbqbLwHFSJ9hHYWnRXAUWAt9mUO8ZQDyWALCI5MnzMsDXWD6A\nL2IJUIm5mQVAI2t30soMfzJp0FpfwtJKegtLALiEpfWS6f/vWutoLD/j7dbntwZaYPnZRQKrgNG5\nnWsiiiclGygJkTes3V4moKbW+r/Cro8QeU1aGELkHV8sXUtXM7tQiOJIAoYQeUAp1R/YDLyutY4v\n7PoIkR+kS0oIIUSWSAtDCCFElpSoFSkrVKiga9WqVdjVEEKIgnHlIGlP21Hgk9Hk/eT27dt3U2td\nMbPrSlTAqFWrFnv37i3sagghRP67fgLmtgdzQupzntVhbNY/C5VSWVqxQLqkhBCiONEadn8N8zqC\nvTMYHJOfd3CBwEn58mgJGEIIUVxE3oCfHoc/XoNa7eHl/fDI55YWBcrytdcs8B+QL48vUV1SQghR\nYp3dACueh9hw6PYRtHoOlLIEh3wKECmV+ICRkJBAcHAwsbGxhV0VUYicnZ2pVq0aDg4OhV0VIbIn\nIRY2vgu7voCKDeGplVA5aU8vgs4HMXP/TK5GXaWKWxVGB4ymR50e+VKVEh8wgoOD8fDwoFatWlh2\n8hSljdaa0NBQgoODqV27dmFXR4isu34Cfh0G145CyxHQeYolR2EVdD6IyTsmE2uy/EEcEhXC5B2T\nAfIlaJT4HEZsbCzly5eXYFGKKaUoX768tDJF8WGb2I68BoN+ge7TkwULgJn7ZyYFi0Sxplhm7p+Z\nL9Uq8S0MQIKFkN8BUXxE3oBVo+D0WqjbGfp8Ae6V0rz0alTay5aldzy3SkXAEEKIYiG9xHYawmLD\nsLezJyGNeRhV3KqkcUfulfguqaJAKcXgwYOTXhuNRipWrEjPnj0BWLVqFVOnpr0Zmrt7RjtnZmzh\nwoWMGjUKgLlz57J48eIclyWEyEcJsbD2Tfi+P7iWhxGbofXIdIPFzZibDPlzCGazGQe75AM5nA3O\njA4YnS/VlBZGCisPXGb6n6e4EhaDj5cL47reS59mVXNVppubG0ePHiUmJgYXFxfWr19P1ap3y+zd\nuze9e/fObdUzNHLkyHwtXwiRQ5kktlO6GnWV4euGcy36Gl92/pJbsbdklFRhWHngMm8uP0JMggmA\ny2ExvLn8CECug0b37t0JCgri0Ucf5aeffmLgwIFs27YNsLQE9u7dy5w5c/j3338ZNGgQkZGRPPLI\nI+mW16dPHy5dukRsbCyjR49mxIgRAHz77bd8+OGHeHl50aRJE5ycnACYPHky7u7uvPbaa7l6H0KI\nPKI17JkP6yaAk4clsV2/S4a3XIq4xLB1w4iIj2DuQ3MJqBwA5M+IqLSUqoDx7upjHL8Ske75A/+F\nEW9KvlVxTIKJ8csO89PutDdQa+RThnd6NU7znK3HH3+cKVOm0LNnTw4fPszQoUOTAoat0aNH8/zz\nz/PUU0/x+eefp1veN998Q7ly5YiJiaFFixb079+f+Ph43nnnHfbt24enpycPPvggzZo1y7RuQogC\nFnUTfnsxS4ntROfDzjN83XDizHHM7zKfxhUy/9zJa5LDsJEyWGR2PDv8/f25cOECP/30E927d0/3\nuu3btzNw4EAAnnzyyXSvmzVrFk2aNKF169ZcunSJM2fO8M8//9CxY0cqVqyIo6Mj//vf/3JdbyFE\nHju7Ab5oA+c2WxLbT/ySabA4eeskQ/4cgkmb+Lbrt4USLKCUtTAyawm0nbqJy2ExqY5X9XJhyXNt\ncv383r1789prr7FlyxZCQ0PTvS6zIaBbtmxhw4YN7Ny5E1dXVzp27ChzDIQo6jKZsZ2eQzcO8fyG\n53FzcGN+l/nULFOzACqbNmlh2BjX9V5cHAzJjrk4GBjX9d48KX/o0KG88847+Pn5pXtN27Zt+fnn\nnwH44Ycf0rwmPDycsmXL4urqysmTJ9m1axcArVq14q+//iI0NJSEhAR++eWXPKm3ECKXrp+E+YGW\nYNFyhGUUVBaCxZ6rexixbgReTl4s6raoUIMFSMBIpk+zqnzYz4+qXi4oLC2LD/v55TrhnahatWq8\n/PLLGV4zc+ZMPv/8c/z8/Lh8+XKa13Tr1g2j0UjDhg154403aN26NQDe3t5MnjyZNm3a0LZtWxo2\nbJjsPpm8JkQBS5qx/UCGM7bT8vflv3l+w/NUcavCwm4L8XH3KYAKZ6xE7endvHlznXIDpRMnTqT6\n4CyNXnrpJQICAhgyZEhhV6XQyO+CKFA5SGwn2nhxI69tfY26XnX5qvNXlHMul69VVUrt01o3z+y6\nPGlhKKW6KaVOKaXOKqXeSOO8UkrNsp4/rJQKyMa9ryqltFKqQl7UtTSaOHEi//zzT77P9RBCWOUg\nsZ3o9/O/8+pfr9KofCMWdF2Q78EiO3IdMJRSBuBz4GGgETBQKdUoxWUPA/Ws/40AvszKvUqp6kAX\nIO0xrSJL3nvvPXbv3k358uULuypClGzZnLGd0rLTy3hr21sEVA5gXud5lHEsk88Vzp68aGG0BM5q\nrc9rreOBn4GUM84eARZri12Al1LKOwv3zgDGk/Yu50IIUXTkMLGd6Lvj3/HuzndpV7UdXwR+gZuD\nWz5WNmfyYlhtVeCSzetgoFUWrqma0b1KqUeAy1rrQxkla5VSI7C0WqhRo0bO3oEQQmTX4aWwcQqE\nB4OzJ8RFgmvZLM3YTmne4XnMPjCbzjU781H7j3AwFM2NvorkPAyllCvwFpbuqAxprecB88CS9M7n\nqgkhhCVYrH4ZEqzztmLDQNnBA29kK1horZl1YBbzj8ynZ52evNf2PeztiuTHMpA3XVKXgeo2r6tZ\nj2XlmvSO3wPUBg4ppS5Yj+9XSuXPmr1CCJEdG6fcDRaJtBm2f5blIszazNTdU5l/ZD6P1X+MD9p9\nUKSDBeRNwNgD1FNK1VZKOQKPA6tSXLMKeMo6Wqo1EK61DknvXq31Ea11Ja11La11LSxdVQFa6/zZ\nFSQfjR07ls8+u/tL1LVrV4YNG5b0+tVXX+XTTz/NsIz7778/0+fUqlWLmzdvpjq+ZcsWduzYkY0a\nZ1xeVuzduzfd+Sa5KXfLli1ZWhJeiHxljIPwS2mfCw/OUhEms4nJOybz48kfebLRk0xsPRE7VfSn\nxeW6hlprIzAK+BM4ASzVWh9TSo1USiWuqf0HcB44C3wNvJDRvbmtU64cXgozfGGyl+Xr4aW5Kq5t\n27ZJH9hms5mbN29y7Njdt7hjx45MA0JOPvAT5TRg5Ebz5s2ZNWtWvj6jd+/evPFGqlHYQuSv6yfh\n607pn/eslmkRCeYE3tz2JivOrmBkk5GMaz6u2EyqzZOQprX+Q2tdX2t9j9b6A+uxuVrrudbvtdb6\nRet5P6313ozuTaP8WlrrnP1Zmh2J/ZLhlwBt+br65VwFjfvvv5+dO3cCcOzYMXx9ffHw8OD27dvE\nxcVx4sQJAgIs01KmT59OixYt8Pf355133kkqI3ETJbPZzAsvvECDBg3o3Lkz3bt3Z9myZUnXzZ49\nm4CAAPz8/Dh58iQXLlxg7ty5zJgxg6ZNm7Jt2zZu3LhB//79adGiBS1atGD79u0AhIaG0qVLFxo3\nbsywYcNIb0Knu7s748aNo3Hjxjz00EPs3r2bjh07UqdOHVatsjQsbVsCWS33+eefp3nz5jRu3DjZ\ne1+7di0NGjQgICCA5cuXJx233RxKiHxnO2P7zlVo81Lq2doOLhA4KcNi4kxxvLLlFdZcWMPY+8by\nYtMXi02wgCKa9M43a96Aq0fSPx+8B0xxyY8lxMBvo2DforTvqeIHD6ffNeLj44O9vT3//fcfO3bs\noE2bNly+fJmdO3fi6emJn58fjo6OrFu3jjNnzrB792601vTu3ZutW7fSoUOHpLKWL1/OhQsXOH78\nONevX6dhw4YMHTo06XyFChXYv38/X3zxBR9//DHz589n5MiRyfbBGDRoEGPHjqVdu3b8999/dO3a\nlRMnTvDuu+/Srl07Jk2aRFBQEAsWLEjz/URFRdGpUyemT59O3759mTBhAuvXr+f48eM8/fTTqSYH\nZrXcDz74gHLlymEymQgMDOTw4cPUr1+f4cOHs2nTJurWrSur74rCEXXT8hlwek3yGdve/ndHSXlW\nswQL/wHpFhNjjGH0ptHsDNnJW63eYmCDgQX4JvJG6QoYmUkZLDI7nkX3338/O3bsYMeOHbzyyitc\nvnyZHTt24OnpSdu2bQFYt24d69atS9q/IjIykjNnziQLGH///TePPfYYdnZ2VKlShQcffDDZc/r1\n6wfAfffdl+yvcVsbNmzg+PHjSa8jIiKIjIxk69atSff06NGDsmXLpnm/o6Mj3bp1A8DPzw8nJycc\nHBzw8/PjwoULqa7ParlLly5l3rx5GI1GQkJCOH78OGazmdq1a1OvXj0ABg8ezLx589K8X4h8cXYj\nrHweYsJS77HtPyDDAGErMj6SFze+yMEbB3mv7Xv0qdsnHyudf0pXwMigJQBYchZpJbM8q8OQoBw/\nNjGPceTIEXx9falevTqffPIJZcqUSVrbSWvNm2++yXPPPZfj5yTurmcwGDAajWleYzab2bVrF87O\nzjl6hoODQ1IT2s7OLumZdnZ26T4zM//++y8ff/wxe/bsoWzZsjzzzDOyXLsoXMY42DD57lLkT67I\n1iQ8W+Fx4YxcP5KTt07yUfuP6Fa7W97WtQAV/bR8QQqclKN+yczcf//9/P7775QrVw6DwUC5cuUI\nCwtj586dSQnvrl278s033xAZGQnA5cuXuX79erJy2rZty6+//orZbObatWts2bIl02d7eHhw586d\npNddunRh9uzZSa8PHjwIQIcOHfjxxx8BWLNmDbdv387Ve06UlXIjIiJwc3PD09OTa9eusWbNGgAa\nNGjAhQsXOHfuHAA//fRTntRJiAxdPwlf53zGtq2bMTcZ8ucQTt0+xYwHZxTrYAESMJLzHwC9Zlla\nFCjL116zstzsTI+fnx83b95MWoY88ZinpycVKljWVOzSpQuDBg2iTZs2+Pn58eijjyb7oAfo378/\n1apVo1GjRgwePJiAgAA8PT0zfHavXr1YsWJFUtJ71qxZ7N27F39/fxo1asTcuXMBeOedd9i6dSuN\nGzdm+fLleTZrPivlNmnShGbNmtGgQQMGDRqU1E3n7OzMvHnz6NGjBwEBAVSqlHzxtuKULBTFQLLE\ndggMWprlpcjTcjXqKkPWDiH4TjCfB35Ox+od87a+hUCWNy9mIiMjcXd3JzQ0lJYtW7J9+3aqVCl9\n8xk/+eQTIiIiePfdd7N8T0n7XRB5KFli+yF45AvwqJzj4i7ducTwdcMJjwvn88DPCagckPlNhSir\ny5uXrhxGCdCzZ0/CwsKIj49n4sSJpTJYzJ07l4ULF6ab2BciW5IS27eh21Ro+RzY5bzz5Xz4eYb/\nOZw4cxzzu8wvtP2384MEjGImK3mLkm7kyJGMHDky8wuFyIgxDja8C7s+tyS2By+HKr65KvLUrVOM\nWD8CheKbrt9Qv2z9PKps0SABQwhR+lw/Cb8Og2tHLIntzlNynKtIdPjGYUZuGImrvSvzu8ynlmet\nvKlrESIBQwhRemgNexfAn2+Do7slsV2/a46KCjofxMz9M7kadZVyzuWIiIugsltl5nedT1X3qnlc\n8aJBAoYQonSIugmrXoJTf+Q6sR10PojJOyYTa7LMFwqNDUWheLLRkyU2WIAMqxVClAZnN8KX91v2\n2u421bLJUS5GQc3cPzMpWCTSaBYeW5jLihZtEjBKuGeeeSbZAoU51a1bN7y8vJIWFUzLwoULuXLl\nStLrYcOGJVuGJKXJkyfz8ccf57pukHzZ9KwsBy9KCWMcrH0Lvu8HLmVh+GZo/XyuRkGBZY5Fdo6X\nFBIwUgg6H0SXZV3wX+RPl2VdCDqf8yVBciunS23kh3HjxvHdd99leE3KgDF//nwaNWqU31VLpaCX\ncxdFVNKM7c+tM7a35HoUFMDJWyfT3buiilvJHuYuAcNGYr9kSFQIGk1IVAiTd0zOVdCIioqiR48e\nNGnSBF9fX5YsWQJY/iIeP348fn5+tGzZkrNnzwKWFsHIkSNp1aoV48ePJyoqiqFDh9KyZUuaNWvG\nb7/9BsCFCxdo3749AQEBBAQEJH1Iaq0ZNWoU9957Lw899FCq5UVyKjAwEA8Pj3TPL1u2jL179/LE\nE0/QtGlTYmJi6NixI4kTKdeuXUtAQABNmjQhMDAw1f1ff/01Dz/8MDExyXcxW716Na1ataJZs2Y8\n9NBDXLt2Dch42fTE5eBFKaU17JmfZzO2E5m1mcXHFjMoaBCu9q442jkmO+9scGZ0wOhcPaOoK1VJ\n7492f8TJWyfTPX/4xmHizfHJjsWaYpm0fRLLTqfdrdOgXANeb/l6umWuXbsWHx8fgoIsQSc8PDzp\nnKenJ0eOHGHx4sWMGTOG33//HYDg4GB27NiBwWDgrbfeolOnTnzzzTeEhYXRsmVLHnroISpVqsT6\n9etxdnbmzJkzDBw4kL1797JixQpOnTrF8ePHuXbtGo0aNUq2BHqi6dOn88MPP6Q63qFDhxxtfvTo\no48yZ84cPv74Y5o3Tz5h9MaNGwwfPpytW7dSu3Ztbt26lez8nDlzWL9+PStXrkxazDBRu3bt2LVr\nF0op5s+fz7Rp0/jkk0+yvGy6KGXyMLFt62bMTSZsn8D2y9vpWL0jU+6fwo4rO5JGSVVxq8LogNH0\nqNMjD95E0VWqAkZmUgaLzI5nhZ+fH6+++iqvv/46PXv2pH379knnBg4cmPR17NixSccfe+wxDAYD\nYFn2fNWqVUl9/bGxsfz333/4+PgwatQoDh48iMFg4PTp04BlOfGBAwdiMBjw8fGhU6e0dwcbN24c\n48aNy/H7yo5du3bRoUMHateuDUC5cuWSzi1evJjq1auzcuVKHBwcUt0bHBzM//73P0JCQoiPj08q\nI6vLpotS5NwmWDEyz2ZsJ9oWvI0J2ycQlRDFhFYTGHDvAJRS9KjTo8QHiJRKVcDIqCUA0GVZF0Ki\nQlId93bz5ttu3+bomfXr12f//v388ccfTJgwgcDAQCZNsqx+a7t4nu33bm5uSd9rrfn111+59957\nk5U7efJkKleuzKFDhzCbzdlerjyvWxg55efnx8GDBwkODk4KBrZeeuklXnnlFXr37s2WLVuYPHly\ngdVNFBPGOMtGRjvnQMUGeTJjGyy743227zO+P/E99crWY0GXBdQtWzcPKlx8SQ7DxuiA0Tgbkn/w\n5rZf8sqVK7i6ujJ48GDGjRvH/v37k84l5jOWLFlCmzZt0ry/a9euzJ49O6mP/sCBA4Cla8vb2xs7\nOzu+++47TCYTYPnAX7JkCSaTiZCQEDZv3pxmuePGjePgwYOp/stNsEi5lHqi1q1bs3XrVv7991+A\nZF1SzZo146uvvqJ3797JEuaJwsPDqVrVMq590aK7ux7m13Lsohg4vNSyd81kL/j4XpgVYAkWLYbn\nWWL7fNh5ngh6gu9PfM+gBoP4qcdPpT5YQClrYWQmsXmZl/2SR44cYdy4cdjZ2eHg4MCXX36ZdO72\n7dv4+/vj5OSU7l4PEydOZMyYMfj7+yftQPf777/zwgsv0L9/fxYvXky3bt2SWiV9+/Zl06ZNNGrU\niBo1aqQbiLKrffv2nDx5ksjISKpVq8aCBQvo2jX5DNnEhL2Li0vSPuYAFStWZN68efTr1w+z2ZyU\nf0nUrl07Pv74Y3r06MH69euTlnwHS0vqscceo2zZsnTq1Ckp6LzzzjsMHDiQxo0bc//99ydbNl2W\nPS/BDi+F1S9btk4GiLQOY20zCrp+kOvitdb8cvoXpu+Zjou9C58Hfk6Hah0yv7GUkOXNC0mtWrXY\nu3dvsg9HkXuhoaEEBARw8eLFVOeK6u+CyIaMdsUcezRXRYfFhjF552Q2/reRNt5t+KDdB1R0rZir\nMosLWd5clDpXrlyhY8eOvPbaa4VdFZFfwoOzdzyLdofs5s2/3+RW7C1ea/4aTzZ6Mt25FqWZBIxC\ncuHChcKuQonj4+OTNFpMlDCJiW3S6RHxrJajYhPMCXxx8AsWHFlAzTI1md19No3KF/xk0+JCQqgQ\nomi7cQrmB1oS27UfBPsUE/AcXCBwUraLvRRxiafXPM38I/PpW68vS3ouKZbBInz1as50CuREw0ac\n6RRI+OrV+fYsaWEIIYqmZEuRu8HAJXBvN0vie+MUSzeUZzVLsPAfkK2iV59bzfu73sdgZ+DjBz6m\na62cLXFe2MJXryZk4iR0rGUhROOVK4RMtARPz1698vx5EjCEEEVPVCisGmWZsX1PIPT58u6Mbf8B\n2Q4QiSLjI3n/n/cJOh9EQKUAprafire7dx5WvGBd/3RGUrBIpGNjuT7jMwkYQohSwHbGdtcPodXI\nPJmxfejGIV7f+jpXo67yYtMXGe43HIOdIQ8qXLCMN28SuWULdzZuwhiSeqIxkO7x3JIcRgmXF8ub\nHzx4kDZt2tC4cWP8/f2TJhymJMubi1wxxlm6n77ra12KfBO0eSHXwcJkNvHVoa94es3TACzstpCR\nTUYWq2ARd/48ofPnc2HgIM6070DIhInEnjqJnc2qELbsvfOn1SQtjBTCV6/m+ozPMIaEYO/tTaWx\nY/KlaZcVRqMRe/vC/ydydXVl8eLF1KtXjytXrnDffffRtWtXvLy8kl23cOFCfH198fHxASzLmxcG\nWd68GLpxCn59Fq4esczY7vJerleXBcv+FG9se4N91/bxcK2HmdhmIh6O6a+6XFRok4mYQ4e4s3Ej\nkRs3EW8dVencqBEVXnwRj8BOODVoQMTvvyfLYQAoZ2cqjR2TL/WSFoaNxASS8coV0DopgZSbUQcl\nYXnz+vXrU69ePcAydLVSpUrcuHEj2TWyvLnIkcSlyL/qABFXLIntHh/nSbBYf3E9/Vf150ToCT5o\n9wEfdfioSAcLc0wMdzZt4srbb3OmwwNcHPQEtxYtxsHHh8oTJ1B38yZqL/+ViqNexLlhQ5RSePbq\nhfd7U7D38QGlsPfxwfu9Kfn2R27h//lagK7+3/8RdyL95c1jDh1CxydfmVbHxhLy9gTClv6S5j1O\nDRtQ5a230i2zpC1vvnv3buLj47nnnnuSHZflzUW2ZZTYzoXohGim7ZnGr2d+pXH5xkzrMI0aZWpk\nfmMhMN66ReTmLdzZtImo7dvRsbHYubvj3qED7oGdcO/QAUMG+9CAZTRUQfWClKqAkZmUwSKz41lR\nkpY3DwkJ4cknn2TRokXYZaNfWZY3F6nkU2L7ROgJxm8dz8WIizzr+ywvNn0RB0Pq36vCFH/hAnc2\nbuLOpk3b7VhbAAAgAElEQVTEHDgAZjP23t549euHe2An3Fq0QDk6Zl5QIShVASOjlgDAmU6Blu6o\nFOx9fKj53eIcPbOkLG8eERFBjx49+OCDD2jdunW2npURWd68lEm1FPmvUMUv18WatZnvj3/PZ/s/\no6xTWeZ1mUdr77z7Pc0NbTYTc+gQkZs2cWfTZuLPnQPAqUEDKowciXtgJ5wbNSoWi2ZKDsNGpbFj\nUCk+eHObQCoJy5vHx8fTt29fnnrqKR599NF036ssby4yZDtjO2kp8twHi5sxN3lhwwtM3zuddlXb\nsaz3sgIJFhnNsDbHxnJn82ZCJk605CMGDiL024XYV6pI5bff5p4NG6izcgUVX34Jl8aNi0WwgDxq\nYSilugEzAQMwX2s9NcV5ZT3fHYgGntFa78/oXqXUdKAXEA+cA4ZorcPyor7pSewHzMtRUiVhefOl\nS5eydetWQkNDWbhwIWAZEdW0adNk18ny5iJNWsPeb6wztl3vztjOoaDzQUlbEHg5exFvjMeojUxs\nPZHH6j9WIP/+ac+wnkjUnr2Yb98i8u/t6JgY7NzccOvQHo9Ogbh3aI/B0zPf65afcr28uVLKAJwG\nOgPBwB5goNb6uM013YGXsASMVsBMrXWrjO5VSnUBNmmtjUqpjwC01hlumSfLmwtZ3ryIyePEdtD5\nICbvmEysyWYYKYrRAaN51u/ZvKhxlqTXfQ1gX7ky7p0exKNTIK6tWmJXRPMRtgpyefOWwFmt9Xnr\ng38GHgFsZ2w9AizWlui0SynlpZTyBmqld6/Wep3N/buA9PtChECWNy9y8iGxPXP/zGTBAkCjWXJq\nSb4GDK01CRcvEnPkKLFHj6QbLFCKuls2l9hWbl4EjKqA7Y4mwVhaEZldUzWL9wIMBdKeXlxMyfLm\neU+WNy8i8imxfSz0GCFRaS95cTXqaq7Lt5Vw7RqxR45YAsSRI8QcPYo5IgIA5eSEcnBAJySkus/e\n27vEBgsoBqOklFJvA0Yg9ZAey/kRwAggWT+2La11if5HFJkrSTtLFmnJZmwPgy7v53oS3vXo68zc\nP5NV51Zhhx1mzKmuqeJWJcflG2/fJvboMWKP3g0QxsSJqQYDTvXrU6ZrV5z9fHHx98epbl0i1qwp\n0BnWRUVeBIzLQHWb19Wsx7JyjUNG9yqlngF6AoE6nf/jtdbzgHlgyWGkPO/s7ExoaCjly5eXoFFK\naa0JDQ3N9tBjkQ15nNgGiDHGsOjYIr45+g1Gs5EhvkOo4VGDj3Z/lKxbytngzOiA0Vkq0xwVRezx\n40ldSzFHjpJw6W4nh2Pt2ri2aY2Lrx/Ofr44N2yIXRq/N/kxQKY4yIuAsQeop5SqjeXD/nFgUIpr\nVgGjrDmKVkC41jpEKXUjvXuto6fGAw9oraNzWrlq1aoRHBycaikLUbo4OztTrVrOdmUTmYgKhVUv\nwamgPElsa635498/mLFvBteir9G5ZmfG3jeW6h6Wvy0rbDuOw7yleIWbCPM0kDCiFx3r9EhVjjk+\nnrhTp4g5coRYa4CIO3cezJYWir2PNy6+fngNeAwXPz+cGzfOdFa1rYKcYV1U5DpgWEcxjQL+xDI0\n9hut9TGl1Ejr+bnAH1hGSJ3FMqx2SEb3WoueAzgB660tg11a65HZrZ+Dg0OaE8KEEHkgjxPbh24c\nYtqeaRy+cZiG5RryYfsPaVGlRdL58NWrqTJ7BTrWMu+oXLgJNXsFYRWa4NKoETGHjxBz1BIg4k6d\nSsozGMqVw9nPF48u1q4lX1/sZYRituV6WG1RktawWiFEHkm2011VqNgQzq63JLb7z89VYjskMoTP\n9n/GH//+QQWXCowOGE3ve3pjp5IHn3SHsypl6RYD7NzccPb1xcXPF2dfP1z8fLH38ZEu6QwU5LBa\nIURJd3gprH4ZEqyrCYcHW/6r3REG/ZzjxHZ0QjTfHP2GhccWAjDcbzjD/Ibh6uCa6lptNKY/nFVr\nvKd+iIufH461a6PyYF0qkZoEDCFE5jZOuRssbN06l6NgYdZmVp1bxaz9s7gRc4OHaz/M2ICxaW6X\nGnf+POErVhD+26p0y7P38cGrT59s10NkjwQMIUTmwoOzdzwD+67tY9qeaRwPPY5/BX8+7fgpTSsl\nX2bGdOcOEUF/EL5iBTGHDoHBgHv79th37Ur4L7+UuuGsRYUEDCFExs5tSpYjSMYz6yPPgu8E8+m+\nT1l/cT2VXSvzYfsP6V67e1KeQptMRO3aRfjyFdzZsAEdF4dj3XuoNG4cnr17YV+xIgCu/n6lbjhr\nUSEBQwiRNtsZ2x7eEHPLciyRgwsETsq0mMj4SL4+8jXfHf8Oezt7Xmj6As80fgYXe0tXVvyFC4St\nXEn4yt8wXr2KXZkyePXvh2ffvjj7+qZKVpfG4axFhQQMIURqKWdsd34PTv5uM0qqmiVY+A9ItwiT\n2cSKsyuYfWA2t2Jv0fue3rzc7GUqu1XGFBlF2NplhK1YScy+fWBnh1u7tlR+fTzunTphl2LnRVE0\nSMAQQtyVasb2z3Dvw5Zz/gMyDBC2/gn5h2l7pnH69mmaVWrG54Gf07hcI6J37+HKik+JWLceHROD\nY+3aVHz1FTx798ahcu63ZxX5SwKGEMIi2YztTtYZ29lbo+lixEU+2fsJmy9txsfNh+kPTOdBQ2Mi\nflzJuZWjSbhyBTt3dzx79cKrX1+cmzSR+RHFiAQMIQSc22ydsX0rRzO2I+Ij+OrQV/x48kcc7RwZ\n2+gFev9XgaiJP3J+zx5QCrc2bag4diwenR9Kc30mUfRJwBCiNEu1FPmybM3YNpqNLDu9jM8Pfk54\nbBjP6fZ0P+lC/CfzuREdjUPNGlQcM9rS5eTjk49vRBQECRhClFY3TsOvQ5Mnth1Tz7BOtGXBlGSL\n/v03qANLal4h7L+zPHXBh3YHDXBlM/Gurng83A2vfv1wCQiQLqcSRAKGEKWN1rDvW1j7VurEdjq2\nLJiC12c/4WTdM6hcuAmPeZsZVdYOn1CN0pdwbdUKz5fHUKZLF+xc0w88oviSgCFEaZLDxLbDvKVJ\nwSLpmAmqhJqp+OIoPPs8gqMsH1/iScAQorRIltj+P2j1fJYS2zeib1A23JTmOaWh4qgX87qmooiS\ngCFESWeb2K5wLzzxC3j7Z3pbWPQt1ix+F4+lG7gnvWs8DXlbV1GkScAQoiS7cdo6Y/twlhLbAFGR\nt9k4byIeyzbT9JaZiAquXH+wDp7bjuJkvHtdnAMkjMjaRD5RMkjAEKIkSpnYfvwnaNA9w1uib91g\n+5yJeKzaRr1IM9dreKBeGUnLvk+jDIZUo6QSRgyg47OZryUlSg7ZcU+Ikiabie3oy5fYO+sdPNbs\nwjle828DL6qNHIVf10EyJLaUkB33hCiNspHYjj51kiOz3sdt8z7KaTjerBx1nh9L9/aPFnClRXEh\nAUOIkiCLiW2tNdF79nJ6zkc47z6GowPsblOO+i+8xmMBfaRFITIkAUOI4s42sd38WejyfqrEtjaZ\nuLNhIxfmfobDiX+Jd4G/HipHoxGv8Ixf36RNjITIiAQMIYqrLCS2zXFxhK/8jStff4ld8FVCvWBb\nr7I0eXoMoxr1w95OPgJE1slvixDFkW1iu86D0HdussS2KTyc2z8v4fqib1G3wvi3Cmwe4Enz/73E\nGw0ew9HgWIiVF8WVBAwhioPDS+/ududWwZKzMMamSmwnhIRwa9Fibi1dAtExHKqt2NizDG17Pcf7\nDQcmbYsqRE5IwBCiqDu8FFa/DAkxltdRNwAFnSZAG8uyHLGnT3NrwTeE//47Zm1mRwNY186dTp2G\n8lmjJ3F3dC+8+osSQwKGEEXdxil3g0USjd77LTEuDxA6fwGRf/2F0dHA+mawvrULnVs/wde+Q/Fy\n9iqUKouSSQKGEEWZ1hB+ifALLlw/7IEx2oC9qwn3qrHE3ooldt5TxHk4sbqDA+vus6Nbk8dY7D+C\niq4VC7vmogSSgCFEURUVCqtfJvyCC8F7vLAzWeZIGKPtCTvjTpwz/PywExt9zXRr8Ag/NxlJVfeq\nhVxpUZJJwBCiKLKZsX3xRBUcTMZUl0Q4gqlPZ5Y2fYHanrULoZKitJHZOkIUJcZ4WDcBvuuDdirD\nnQZTsQ9PHSwAKkTA9AemS7AQBUZaGEIUFTYztmMq9uPa37HEHPgYkx3Ym1NffrNMwVdRlG4SMIQo\nbDYztuOjXbke0o07P+8itowzPz7sQJTByPC1GmebhkasPazpUo4OhVdrUQpJwBCiMEXfglUvYTz4\nBzcvNeDWwQiM9sdY2c6ONW2ga8N+NCpTk2/tZ/LopjjKR0BoGVjWyYmuQ98q7NqLUkYChhCF5dxm\nzL88T+j+aK6fqgoJYWxooljbqQwP3zeQ1Q0HUcGlAgCVRlTi/eYzuRp1lSpuVRgdMJoedXoU8hsQ\npY0EDCEKmjEevX4yN3/6lpBjXjhEubG3vpkND3vT9YGh/FqvH64OyVeb7VGnhwQIUejyJGAopboB\nMwEDMF9rPTXFeWU93x2IBp7RWu/P6F6lVDlgCVALuAAM0Frfzov6prTywGWm/3mKK2Ex+Hi5MK7r\nvfRpVrDj2YtCHaQe+V8Pff0U1z8ezKVt4bjd9uJfH9j2dB069XyRb2t1SXf12KLw8ygKdZB6FG49\ncr1Fq1LKAJwGOgPBwB5goNb6uM013YGXsASMVsBMrXWrjO5VSk0Dbmmtpyql3gDKaq1fz6guOdmi\ndeWBy7y5/AgxCaakYy4OBj7s51dg//hFoQ5Sj3yuh9b89+MELi5YRoUrdoSUhf39GtHuiddo5d06\nw42LisLPoyjUQeqRf/XI6hateREw2gCTtdZdra/fBNBaf2hzzVfAFq31T9bXp4COWFoPad6beI3W\nOkQp5W29/96M6pKTgNF26iYuh6VcpwccDXY0q1Ew6/Ac+C+MeFPqcZMFWQepR/7Vo9ztHXTZsZAG\np42Eu8LaNjU45DsER7uaBVqP3CgKdZB6ZL0eVb1c2P5GpyyXU5B7elcFLtm8DsbSisjsmqqZ3FtZ\nax1i/f4qUDmthyulRgAjAGrUqJHtyl9JI1gAaf4j5Jf0nlWQdZB65G09NBodu5cH//me9gfvYDLA\njpZl2dh8LCanamRnN4qi8PMoCnWQemT9eel9ruVWsUh6a621UirNppDWeh4wDywtjOyW7ePlkmYL\no6qXC0uea5Pd4nIkvVZOQdZB6pE39UgwJ/DnydWc+3om7TZdxzkervsaaPrWNJo2686zBVSPvFYU\n6iD1yHo9fLzyZ9+TvFga5DJQ3eZ1NeuxrFyT0b3XrF1RWL9ez4O6pjKu6724OBiSHXNxMDCua4a9\nXyWuDlKP3NUjKiGKRYe/5d23H8Dz6bfpsuY6psoJ1Hy1LQ/+sJuyzbqnuic/6pFfikIdpB6FX4+8\naGHsAeoppWpj+bB/HBiU4ppVwCil1M9YupzCrbmJGxncuwp4Gphq/fpbHtQ1lcTEUGGOdigKdZB6\n5KweN6Jv8P3x7zgR9BP9N0TS8gYkVDRRvZ0R9+fmpNpjO7/qkd+KQh2kHoVfj1wnvSFpFNRnWIbG\nfqO1/kApNRJAaz3XOqx2DtANy7DaIVrrvendaz1eHlgK1AAuYhlWeyujeuQk6S1ETpwPO8/CYws5\nun0VAzfG43tRo8s6UK3hNTweaI3q91WyPbaFKMoKbJRUUSIBQ+S1oPNBzNx/d4Z1rzq9OHX7FMeP\nbuGJrYr7jxnBw5XKvlGUrRmK6vIOtH4haY9tIYqDghwlJUSJFHQ+iD/nvc2EpDWcLrH8/i+557YD\nz+/T2BkMlO98L+U9/sLgUxf6LwVv/8KuthD5RgKGEOn4+5v/Y8jvcUmrxFaMgBFrNZp4ynYPpKL3\nfhyiNkPzodDlA3B0zbhAIYo5CRhCpKC1ZvOlzTy87layJcUBFHDbDRqXWwZmF3j8R2ggazyJ0kEC\nhhBWWmt2huxkzoE5nL5ymMURaV9XNgqo0Rr6zpXEtihVJGAIARy4foBZ+2dx5vweHj3kxlv7HFGk\nPVvW6GkPg5dLYluUOhIwRKl2IvQEsw/M5vSRrTy2z5HXDoGd8Q4eDwXiGLqBG4c0dqa7CwOaDZqa\n95kkWIhSSQKGKJXOh51nzsE5nN+5jv577Bh1woSyN+LVpx/lhgzBqU5tmOyFk6Mz1w97YIw2YO9q\nopL/HTwrxRZ29YUoFBIwRKkSfCeYLw9+waUNq+mzSzP8ognl4UK5Yc9Q9snBOFSqZNlje++3AHjW\nisGzVoquKc/qaZQsRMknAUOUCtejr/P1/rmErFpGr11GalzX2FWqSIXxQ/Aa8BgGd3fLhdY9tjn5\nO1RsCLf/BaNNi8LBBQInFc6bEKKQScAQJdrt2Nss3DOXm0t/pts/8VSIAEOdWlQa+xyePbqjHG0W\nGj+/BVaMhKiblnkVrV+Ao8tg4xQIDwbPapZg4T+g0N6PEIVJAoYoke7E3+GnnXO5/d33dNobj3ss\nGJr54T3iedwfeABlm7Q2xsOm92DHbKhQDwYtAe8mlnP+AyRACGElAUOUKNEJ0azY/AXhi7+n7cE4\nHExg6NCG6iNfwrVZs9Q33DwDvz4LIYdkxrYQmZCAIUqEeFM8QX/MJGrhDzQ7Hoc2KAwPP0Sd51+x\njHhKSWvYvwjWvgn2zjJjW4gskIAhirUEUwKbf/mUmEU/0eDfOGKdDZgH9abBc69aRjylxTaxXacj\n9JkLZbwLstpCFEsSMESxZIqPY/t300n47heqX40nwtOB6JEDaPLsa9h7eKR/Y7LE9vvQ+kWZhCdE\nFknAEMWKKSqKvfM/wvzTSiqGJXCtkiNhrw2m1ZOvYefklP6NGSW2hRBZIgFDFElbFkzBYd5SvMJN\nhHkaMD3xCB53jJh/DaJMtInztZyJHf0UHQaMwWDI5NfYNrF93xDo+n+S2BYiByRgiCJny4IpeH32\nE04Jltflwk3oL5ajgSMNnfEcOoyuPUZhb5fJr68ktoXIUxIwRJHjMG9pUrBIpIAwd0W/Zf/gaHBM\n875kJLEtRJ6TgCGKjNjTpwlfvoKy4aY0z3tG6qwFC0lsC5EvJGCIQmUKCyM8KIjw5SuIPXYMs8GO\nBHtwMqa+NszTkHFhktgWIl9JwBAFThuNRG3fTtiKlURu3IhOSCD+nqoE9SjL73UjePh6NXotC07W\nLRXnAAkjMliiQxLbQuQ7CRiiwMSdO0f4ihWE/7YK440bGMqWxdynC9/WvMBah1PUL1ufGS1m0Mq7\nFVuqJR8llTBiAB2fTWOV2JSJ7f/9AA17FvybE6IUUFrrwq5DnmnevLneu3dvYVdD2DBFRBDxxxrC\nViwn9tBhMBhw79ABundinsc+Vl78nXLO5Xi52cv0qdsHg10m3U62bBPbtR+Avl9JYluIHFBK7dNa\nN8/sOmlhiDynTSaidu4ifMUK7mzYgI6Lw6leXSqNH49T9858d2013x6dhjHMyFDfoQz3G467o3v2\nHnL+L2ti+4YktoUoIBIwRJ6Jv3CBsBUrCf/tN4xXr2Ln6YlX//549u2LY+OGrLmwhs+2PcO16Gt0\nrtmZsfeNpbpHNnevM8bD5vdh+yxrYvtnSWwLUUAkYIhcMUVGErFmDeErVhKzfz/Y2eHWri2V33gd\n9wcfxM7JiYPXDzJ9zVMcvnmYhuUa8lGHj7iv8n3Zf9jNM/DrMAg5KIltIQqBBAyRbdpsJnr3bsKW\nL+fOuvXo2Fgca9em4quv4Nn7ERwqW1aJDYkMYcY/M1jz7xoqulTk/bbv0+ueXtipLHQdHV6afKe7\nOh3h6K+S2BaiEEnAEFkWf+kS4StWEr5yJQlXrmDn4YHnI4/g1bcPzk2aoJQCLJsYLTi6gEXHFgHw\nnP9zDPUdiqtDFlsDh5fC6pchIcbyOvwSHPgOKjSAp1ZKYluIQiIBQyQTvno112d8hjEkBHtvbyq8\n8DzKzkD4ihVE79kDSuHWpg0VX3kFj4cCsXN2TrrXrM2sOreKWftncSPmBt1rd2dMwBi83bP5Ab9x\nyt1gYSshUoKFEIVIAoZIEr56NSETJ6FjYwEwXrnC1QkTAXCoWYOKY0bj+cgjOHin/tDee3Uv0/ZM\n48StE/hX8GfGgzNoUjGHyejw4HSOX85ZeUKIPCEBo5TTWmMMCSHmyFGuvjslKVjYMlQozz1r1yZ1\nOdm6dOcSM/bNYP3F9VRxq8LU9lPpXrt7mtdmyc0zYGcP5oTU5zyr5axMIUSekIBRyhhv3SL2yBFi\njhy1fD16FFNoaIb3mEJvpQoAkfGRzDsyj++Pf4+9nT0vNn2Rpxs/jYu9S84qpjXsXwxr3wCDAygF\npvi75x1cIDCNmd5CiAIjAaMEM0VGEnv0GLFH7waIhCtXLCeVwvGeOri3b4+zny8ufn4Ejx6DMSQk\nVTn2Nl1QJrOJ5WeXM+fAHG7F3qL3Pb15udnLVHarnPOKRt+yJLlPrL47Y/vCtuSjpAIngX8Ga0kJ\nIfJdrgKGUqocsASoBVwABmitb6dxXTdgJmAA5mutp2Z0v1KqMzAVcATigXFa6025qWtJZ46LI+7k\nyWQth/jz5y1/uQMO1arh3MSfsk88gbOfL86NGmNwd0tWRqVXxibLYQAoZ2cqjR0DwK6QXUzbM40z\nt88QUCmALwK/oHGFxrmruO2M7c7vQZtRlhnb/gMkQAhRxORqLSml1DTgltZ6qlLqDaCs1vr1FNcY\ngNNAZyAY2AMM1FofT+9+pVQz4JrW+opSyhf4U2tdNbP6lJa1pLTRSNy5c8m6lmJPnwajZU1wQ4UK\nuPj6JrUcnP38sC9bNktlpxwlVWnsGG538OOTvZ+wJXgLVd2r8sp9r9C5Zuec5ykg+Yzt8nWh/3zw\naZrz8oQQOVZQa0k9AnS0fr8I2AK8nuKalsBZrfV5a8V+tt53PL37tdYHbO4/BrgopZy01nG5rG+R\nldYHtWevXmitSbh40RIYEruWTpxAx1iGndp5eODs25jyQ4YkBQj7KlVy/GH+d2M7Zr5g4GqUPZVc\nFXWdg/jnt0k42TsxJmAMgxsNxsnglLs3m2rG9gfg6Jb5fUKIQpXbgFFZa53Y6X0VSKsjuypwyeZ1\nMNAqG/f3B/aX9GCRcjjrlTff4ua8rzFeu4Y5IgKwdA85N2yI12OPWloOvr441qyJyqNF94LOBzF5\nx2RiTZZ6XIu+xrXoa7So3IJpD0yjgkuF3D3ANrFt7yQztoUoZjINGEqpDUCVNE69bftCa62VUjnu\n30rrfqVUY+AjoEsG9RsBjACoUaNGTh9faLTZzLWPpqUezmo0Ev/vv3j165fUcnCqWxdln3/jFGbu\nn5kULGwFRwbnPlikldiWSXhCFCuZfvporR9K75xS6ppSyltrHaKU8gaup3HZZcB2SdJq1mMA6d6v\nlKoGrACe0lqfy6B+84B5YMlhZPZ+igJzXBxRO3cSuXETd7ZsxnTzZtoXmkx4T3m3QOqktSYkKvUI\nKYCrUVdzV3h6iW0hRLGS2z9XVwFPYxnR9DTwWxrX7AHqKaVqYwkUjwODMrpfKeUFBAFvaK2357KO\nRYLx9m0i//qLyI2biNy+HR0djZ2rK24dOhC9axemsLBU99inMaM6P9yJv8N7O99L93wVt7QamFmQ\nMrE98CdJbAtRjOU2YEwFliqlngUuAgMAlFI+WIbPdtdaG5VSo4A/sQyr/UZrfSyj+4FRQF1gklIq\ncbZWF611Wi2YIiv+0iXubNxI5MZNRO/fDyYT9pUq4dm7Fx6Bgbi2aoWdo2OqHAYkH86anw5eP8gb\n297gatRVutTswtbgrcm6pZwNzowOGJ39gm+ete6xfRDue8a6FLkktoUozmSL1jykzWZijx7lzqZN\nRG7cRNyZMwA41auHe2AnPAIDcW7cOM0kdXqjpPKL0Wzk6yNf89Whr6jiVoWPOnxEk4pNCDofxMz9\nM7kadZUqblUYHTCaHnV6ZL3glInt3rOhYf69DyFE7mV1WK0EjFwyx8cTvWsXdzZuInLzZozXr4Od\nHa733ZcUJByrZ3NXuXwWEhnCG9veYP/1/fSo04MJrSZkf4vUtEhiW4hiSfb0zkem8HAi//qLOxs3\nEbVtG+boaJSrK+7t2uER2Am3Dh2yPFGuoP154U/e3fkuJrOJ/2v3f/S6J4/++v93Kyx/zprYngJt\nXpLEthAljASMLIoPvkzkpo3c2biJ6L17wWTCULECZXr2xCOwE66tW2PnlMsJbfkoOiGaqbunsuLs\nCvwq+PFR+4+oXiYPWj7GeNj8AWyfCeXvgYEbJLEtRAklAYO08wdlevYk9tjxpCARd+oUAI5176H8\ns8/iEdgJZz+/PJs0l5+Ohx7n9a2vczHiIsP9hvN80+dxsHPIfcGS2BaiVCn1OYy0RihhMKDc3NAR\nEWBnh0tAMzw6BeIR2AnHmjXzuNb5x6zNLD62mJkHZlLOuRxT20+lRZUWuS9YEttClCiSw8ii6zM+\nSz3L2mSC+Hi8P/wQ944PFNl8REZuRN/g7b/fZmfITgJrBDK5zWS8nL1yX3CyxHYHa2LbJ/flCiGK\nvFIfMNLa/wFAx8Xh1bdPAdcmb/x16S8mbp9IjDGGSW0m8Wi9R3O3smwiSWwLUaqV+oBh7+2NMXFT\noRTHi5s4Uxyf7v2UH0/+yL1l72Vah2nU8aqT+4IlsS2EQAIGlcaOKbRZ1nnp7O2zjN82njO3zzC4\n4WDG3DcmZ8uQH16afKe7ls/B0WWWxHbA09DtQ0lsC1FKlfqAkTibuiBnWeclrTVLTi3h470f4+bg\nxheBX9C+WvucFXZ4qSU/kWDZa4PwS7B+Aji4wv++l8S2EKVcqQ8YYAkaxSVA2Lode5tJOyax5dIW\n2lZty/tt38/dMuQbp9wNFracvSRYCCEkYBRXu0J28da2twiLC2N8i/E80fAJ7FQuE9DhwWkfv5P2\nwAAhROkiAaOYSTAlMOfgHL49+i21PGvxeeDnNCzfMPcFG+PByR3i7qQ+51kt9+ULIYo9CRjFyMWI\ni83qmh8AAA4NSURBVLy+9XWOhR7j0fqPMq75OFwdXHNfcOKM7bg7oAygTXfPObhA4KT07xVClBoS\nMIoBrTWrzq3ig38+wMHOgRkdZ/BQzXQ3QsxOwSn22P7eksOwHSUVOAn8B2RelhCixJOAUcRFxEfw\n/s73WXNhDc0rN+fD9h/mfAc8W9G3YPVoOLEq9YxtCRBCiDRIwCjCDl4/yOtbX+da9DVebvYyQ32H\nYrAz5L5gmbEthMgBCRhFRMqd7hqXb8ymS5vwdvNm0cOLaFKxSe4fkmrG9nrwaZb7coUQpYIEjCIg\n6HwQk3dMTtpLOyQqhJCoEJpWbMqXD32ZN7vh2S5FLjO2hRA5IAGjCJi5f2ZSsLB1Lfpa7oOF1nDg\nO1jz+t3EtkzCE0LkgASMIuBq1NVsHc+yjBLbQgiRTRIwigBPJ0/C4sJSHc/VaChJbAsh8pgEjEK2\n4swKwuLCUCg0d3c/dDY4MzpgdPYLNMbDlv+Dvz+TxLYQIk9JwChEP5z4gam7p9LWpy1da3Xly0Nf\nJo2SGh0wmh51emSvwJtnYfkwuHJAEttCiDwnAaOQzD8yn5n7Z9KpeiemPzAdR4Mjfev1zVlhktgW\nQhQACRgFTGvN7AOz+frI13Sv3Z33272Pg51DzguUxLYQooBIwChAWmum7ZnG9ye+p3+9/kxsPTF3\nM7f/3QYrnoPI65LYFkLkOwkYBcRkNvHervf49cyvDG44mPEtxqOUyllhKRPbwySxLYTIfxIwCoDR\nbGTC9gkEnQ9iuN9wXmr2Us6DReg5y4xtSWwLIQqYBIx8Fm+KZ/zW8Wz8byOjA0YzzG9YzgrSGg58\nb01sO8KA76BR77ytrBBCZEACRj6KMcYwdstYtl/ezhst3+CJhk/krCBJbAshigAJGPkkKiGKURtH\nse/a/7d398FV1Xcex98fElJYRdRChaI0opStHXUFfJgUdoVCBdwW13Htg1PRlq6uY4nO7FRn/3Ch\njiutHQs2bRkfsOhs67K2XbRUFKhat9TlIWN50KKYTBUbHgrdKAgK5Lt/nANe0pvkhNx7Q3I/r5lM\n7jn397v5fkm433vO75zfbx1zauZw5cgrj+2Fjgxsb4dJc6Bmlge2zaxbuGAUQfN7zdy04iY27drE\n3PFzmTZiWudf5C8Gtld4YNvMupULRoHt2reLG5bfQENzA/deei8Th0/M1nH94g+WRh0wBCqq4P/+\n4IFtMztuuGAU0Pa92/na8q/RtKeJuol11AyrydZx/WJ4claynjbAO03J94tvhKnfKk6wZmad1KWT\n4ZJOlbRc0mvp91PaaDdF0mZJWyTdnrW/pOGS9kj6l67EWQpv7XmL65Zdx453d7Bg8oLsxQKSI4vD\nxSLX75cWLkAzsy7q6ujp7cDKiBgJrEy3jyKpAvg+MBU4B/iipHMy9r8XeKqLMRZdY3MjM56awdvv\nv80Dkx9gzGljOvcCzVs7t9/MrBt0tWBMBxaljxcBV+RpcxGwJSIaIuJ94LG0X7v9JV0BNAKbuhhj\nUW3evZnrll3HgZYDLLxsIecOPjd750MHYMVsyJnW/CgDTy9EiGZmBdHVgnFaRKQn3NkGnJanzTDg\nzZztrem+NvtLOhG4DZjTUQCS/knSWklrd+7ceQwpHLuNf9rIV57+CpV9Knl4ysOMOnVU9s67XoeH\nJsP/fBc+Nh4q+x/9fN/+8Ok7ChuwmVkXdFgwJK2QtDHP1/TcdhERtPlRuWOt+s8GvhsRezL0uz8i\nxkbE2MGDBx/rj++0+u31zHxmJgOqBrBoyiJGDByRrWME1D8KC8bD7sbkju3rfwGfuw8GngEo+f7Z\n++C8q4uag5lZZ3R4lVRETGrrOUnbJQ2NiCZJQ4EdeZq9BZyRs316ug+grf4XA1dJ+jZwMtAiaX9E\n1GXIqehW/XEVtb+qZcgJQ3jgMw9kX0r13d3wi1vg5SVQPT65Y3tgerB13tUuEGZ2XOvqKakngBnp\n4xnAkjxt1gAjJZ0pqQr4Qtqvzf4RMT4iqiOiGpgH/PvxUiyee/M5bl55M8NPGs7DUx7OXiwaX4AF\n45IrnybNgWuXfFAszMx6gK4WjLnAZEmvAZPSbSR9VNIvASLiIHAz8DTwCrA4Ija11/94taxxGbc+\neyujThnFwssWMqj/oI47HR7YXvTZZFxi5goYdwt0ZR0MM7NuoGTooHcYO3ZsrF27tiiv/fPXfs7s\n387mgo9cQN3EOk6sOrHjTkdNRX4tTJnrO7bN7LgjaV1EjO2one/0zuDHr/yYu1ffTc1Ha5g3YR79\nW1/R1FruVOQVfeHqR+Cc6e33MTM7zrlgdOChDQ8xr34eE86YwHf+7jtUVVS136G9gW0zsx7MBaMN\nEUHdS3Xcv/5+pp45lbvG3UXfPn3b73TUVOSz06nIPVZhZr2DC0YeEcE9a+/h0Zcf5cqRV3LHJXdQ\n0d4b/6ED8OxdnorczHo1F4xWWqKFO1+8k8dffZxrPnEN37jwG/RROxeTeWDbzMqECwawtGEp8+vn\ns23vNvpV9mPfwX3MPHcmsy6YhaT8nTywbWZlpuwLxtKGpcxeNZv9h/YDyTrclark7JPPbrtY7Ptz\nssa2B7bNrIyU/eLQ8+vnHykWhx2Mg8yvn5+/Q+ML8MNPpXdsz/Yd22ZWNsr+CGPb3m3Z9ucObJ86\nAr66HIaNLkGEZmbHh7IvGENOGELT3qa8+49oPbB92d3woQx3epuZ9SJlf0qqdnQt/Sr6HbWvX0U/\nakfX5pmK/BH43PdcLMysLJX9EcblIy4HOHKV1JAThlA7upbLh9bAf83wwLaZWarsCwYkReNw4QA+\nGNj2HdtmZke4YACsXwwrvwnNW5PTTe+9A6ee5YFtM7McLhjrF8OTs+DAvmT7vXdAFcmaFS4WZmZH\nlP2gNyu/+UGxOCwOwfPf7p54zMyOU71qASVJO4E/dKbPmKF9xrT13LqmlnVdDur4Ngj4U3cHUWLO\nuTw45875WEQM7qhRryoYXSVpbZZVp3qLcssXnHO5cM7F4VNSZmaWiQuGmZll4oJxtPu7O4ASK7d8\nwTmXC+dcBB7DMDOzTHyEYWZmmbhgmJlZJmVXMCRNkbRZ0hZJt+d5XpLuS59fL6nH3+6dIedr0lw3\nSFol6fzuiLOQOso5p92Fkg5KuqqU8RVDlpwlXSrpJUmbJD1f6hgLLcPf9kBJT0r6XZrz9d0RZ6FI\nWihph6SNbTxf3PeviCibL6ACeB0YAVQBvwPOadVmGvAUIOAS4H+7O+4S5FwDnJI+nloOOee0+xXw\nS+Cq7o67BL/nk4GXgeHp9ke6O+4S5PyvwLfSx4OB3UBVd8fehZz/FhgNbGzj+aK+f5XbEcZFwJaI\naIiI94HHgOmt2kwHHonEi8DJkoaWOtAC6jDniFgVEX9ON18ETi9xjIWW5fcM8HXgp8COUgZXJFly\n/hLws4h4AyAienreWXIOYIAkASeSFIyDpQ2zcCLi1yQ5tKWo71/lVjCGAW/mbG9N93W2TU/S2Xy+\nSvIJpSfrMGdJw4B/AH5YwriKKcvv+ePAKZKek7RO0rUli644suRcB3wC+COwAaiNiJbShNctivr+\n5dlq7QhJE0gKxrjujqUE5gG3RURL8uGzLFQCY4BPA/2B30p6MSJe7d6wiuoy4CVgInAWsFzSCxHx\ndveG1TOVW8F4CzgjZ/v0dF9n2/QkmfKRdB7wIDA1InaVKLZiyZLzWOCxtFgMAqZJOhgR/12aEAsu\nS85bgV0RsRfYK+nXwPlATy0YWXK+HpgbyQn+LZIagb8GVpcmxJIr6vtXuZ2SWgOMlHSmpCrgC8AT\nrdo8AVybXm1wCdAcEU2lDrSAOsxZ0nDgZ8CXe8mnzQ5zjogzI6I6IqqBx4GbenCxgGx/20uAcZIq\nJf0VcDHwSonjLKQsOb9BckSFpNOAUUBDSaMsraK+f5XVEUZEHJR0M/A0yRUWCyNik6Qb0+cXkFwx\nMw3YArxL8gmlx8qY8x3Ah4EfpJ+4D0YPnukzY869SpacI+IVScuA9UAL8GBE5L08syfI+Hu+E/iR\npA0kVw7dFhE9dtpzST8BLgUGSdoK/BvQF0rz/uWpQczMLJNyOyVlZmbHyAXDzMwyccEwM7NMXDDM\nzCwTFwwzM8vEBcMMkLSnk+1/1JkZbiVVtzXDqFlP4YJhZmaZuGCY5UjXi3he0hJJDZLmpuuFrE7X\nCzkrp/kkSWslvSrp79P+1ZJekFSfftXk+Rl526Q/+zlJj0v6vaT/SGdZPbxux6p0XYfVkgZIqpB0\nj6Q16doHN5TkH8nKVlnd6W2W0fkkM5zuJplG4sGIuEhSLcmU6Lek7apJptg+C3hW0tkkU6VPjoj9\nkkYCPyGZtypXe20uAD5JMrvqb4BPSVoN/Cfw+YhYI+kkYB/JRJHNEXGhpA8Bv5H0TEQ0FvofxAxc\nMMzyWXN4/h1JrwPPpPs3ABNy2i1Op8p+TVIDyaR2jUCdpL8BDpFMKd5a33barI6IrenPfomkKDUD\nTRGxBuDwTKuSPgOclzOWMhAYmcZgVnAuGGZ/6b2cxy052y0c/X+m9bw6AdwKbCc5SukD7M/z+u21\nyf3Zh2j//6iAr0fE0+20MSsYj2GYHbt/lNQnHdcYAWwm+ZTflB55fJlkUrzWsrTJtRkYKulCgHT8\nopJk0r1/ltQ33f9xSScUIjGzfHyEYXbs3iBZV+Ek4MZ0TOIHwE/T1eyWAXvz9MvS5oiIeF/S54Hv\nSepPMn4xiWT9kmqgPh0c3wlcUZDMzPLwbLVmZpaJT0mZmVkmLhhmZpaJC4aZmWXigmFmZpm4YJiZ\nWSYuGGZmlokLhpmZZfL/0z1nu0ywyTYAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x10e42cf28>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["imb=np.linspace(0,1,n_imb)\n", "G1,B,Q,Q2,R1,R2,K=estimate(T)\n", "G6=plot_Gstar(ticker,G1,B,T)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"text/plain": ["<matplotlib.text.Text at 0x10dc60ba8>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAX4AAAEWCAYAAABhffzLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nCJBmAAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0x10dc18940>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["W=np.linalg.matrix_power(B,100)\n", "for i in range(0,n_spread):\n", "    plt.plot(imb,W[0][(0+i*n_imb):(n_imb+i*n_imb)],label=\"spread = \"+str(i+1)+\" tick \",marker='o')\n", "    \n", "plt.legend(loc='upper left')\n", "plt.title('stationary distribution')\n", "plt.xlabel('Imbalance')"]}, {"cell_type": "markdown", "metadata": {"collapsed": true, "slideshow": {"slide_type": "subslide"}}, "source": ["# CVX"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [], "source": ["n_imb=4\n", "n_spread=4\n", "dt=1\n", "data=get_df('CVX') \n", "ticker='CVX'\n", "T,ticksize=prep_data_sym(data,n_imb,dt,n_spread)\n", "imb=np.linspace(0,1,n_imb)\n", "G1,B,Q,Q2,R1,R2,K=estimate(T)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYwAAAEWCAYAAAB1xKBvAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nIiIxUcEQEZGYqGCIiEhMVDBERCQm/w8J3YDyOtGiXgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x10dd31b70>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["G6=plot_Gstar(ticker,G1,B,T)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"slideshow": {"slide_type": "subslide"}}, "outputs": [{"data": {"text/plain": ["<matplotlib.text.Text at 0x10dc02dd8>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAX4AAAEWCAYAAABhffzLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x10dc42828>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["W=np.linalg.matrix_power(B,100)\n", "for i in range(0,n_spread):\n", "    plt.plot(imb,W[0][(0+i*n_imb):(n_imb+i*n_imb)],label=\"spread = \"+str(i+1)+\" tick \",marker='o')\n", "    \n", "plt.legend(loc='upper left')\n", "plt.title('stationary distribution')\n", "plt.xlabel('Imbalance')"]}, {"cell_type": "markdown", "metadata": {"collapsed": true, "slideshow": {"slide_type": "subslide"}}, "source": ["# Summary\n", "\n", "1. The micro-price is the expected mid-price in the distant future\n", "2. In practice, the distant future is adequately approximated by $\\tau^6$ the time of the 6th mid price move\n", "3. Paper provides evidence that the micro-price is a good predictor of future mid prices\n", "3. Micro-price can fit very different microstructures\n", "4. Micro-price is horizon independent\n", "5. Micro-price seems to live between the bid and the ask"]}], "metadata": {"celltoolbar": "Slideshow", "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 1}