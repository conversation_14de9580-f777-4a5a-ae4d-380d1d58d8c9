MM SDK (导出系统日志功能)

功能介绍：
基于运维平台《服务器管理 导出文件》 能力，通过python实现自动化导出文件功能。
调用http post接口开启交易服务器转存目标文件到数据中台服务器，通过get请求从数据中台服务器通过流的方式写入到本地
本工程依赖requests 库和python 3
core.py
    download 导出文件函数，参数为
        exchange: config.py 中的ENV_CONFIG的key值
        file_path: 服务器上要导出文件的绝对路径
    handle_files 根据ENV_CONFIG配置实现的文件路径处理函数，并执行download
        exchange: config.py 中的ENV_CONFIG的key值

config.py
    SERVER: 指定的运维平台domain
    TRANS_URL: 服务器文件转存到数据中台的请求url
    DOWNLOAD_URL: 从数据中台下载到本地交易机的url
    ENV_CONFIG: 配置映射表
        server_id 是交易服务器的id，在运维平台服务器管理条目点击左侧箭头展开后查看ID
        remote_paths 配置远程交易服务器上要下载的文件绝对路径，* 号是日期占位符，通过handle_files 处理后变成 %Y%m%d 格式
    SAVE_BASE_DIR: 交易机本地保存路径，windows是双反斜线作为路径分隔符
    CHUNK_SIZE: 从数据中台下载文件时通过块的方式写入到本地，这是显示块的大小，默认5M

使用说明：
1. 将mmsdk 作为依赖库添加到自己的python工程，通过 from mmsdk import handle_files, download 引入core的方法
2. 也可以将mmsdk作为独立运行的工程，实现一个test2.py 的脚本，通过 from mmsdk import handle_files, download 引入core的方法
3. handle_files 是提供的一种默认的文件路径处理方法，也可以根据自己实际情况实现其他方法，调用download 实现文件导出

依赖离线安装：
1. 本工程提供了requirements.txt 和lib 资源，在安装python 环境之后，通过
pip install --no-index --find-links=.\lib -r .\requirements.txt
实现通过requirements和lib 离线安装依赖库


