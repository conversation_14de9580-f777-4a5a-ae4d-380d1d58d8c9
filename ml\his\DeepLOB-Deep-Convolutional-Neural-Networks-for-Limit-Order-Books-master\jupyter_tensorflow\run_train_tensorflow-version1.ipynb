{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeepLOB: Deep Convolutional Neural Networks for Limit Order Books\n", "\n", "### Authors: <AUTHORS>\n", "Oxford-Man Institute of Quantitative Finance, Department of Engineering Science, University of Oxford\n", "\n", "This jupyter notebook is used to demonstrate our recent paper [2] published in IEEE Transactions on Singal Processing. We use FI-2010 [1] dataset and present how model architecture is constructed here. \n", "\n", "### Data:\n", "The FI-2010 is publicly avilable and interested readers can check out their paper [1]. The dataset can be downloaded from: https://etsin.fairdata.fi/dataset/73eb48d7-4dbc-4a10-a52a-da745b47a649 \n", "\n", "Otherwise, the notebook will download the data automatically or it can be obtained from:\n", "\n", "https://drive.google.com/drive/folders/1Xen3aRid9ZZhFqJRgEMyETNazk02cNmv?usp=sharing\n", "\n", "### References:\n", "[1] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>chmark dataset for mid‐price forecasting of limit order book data with machine learning methods. Journal of Forecasting. 2018 Dec;37(8):852-66. https://arxiv.org/abs/1705.03233\n", "\n", "[2] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>: Deep convolutional neural networks for limit order books. IEEE Transactions on Signal Processing. 2019 Mar 25;67(11):3001-12. https://arxiv.org/abs/1808.03668\n", "\n", "### This notebook runs on tensorflow 1."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["data already existed.\n"]}], "source": ["import os \n", "if not os.path.isfile('data.zip'):\n", "    !wget https://raw.githubusercontent.com/zcakhaa/DeepLOB-Deep-Convolutional-Neural-Networks-for-Limit-Order-Books/master/data/data.zip\n", "    !unzip -n data.zip\n", "    print('data downloaded.')\n", "else:\n", "    print('data already existed.')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/nfs/home/<USER>/anaconda3/envs/tensorflow/lib/python3.6/site-packages/tensorflow/python/framework/dtypes.py:523: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_qint8 = np.dtype([(\"qint8\", np.int8, 1)])\n", "/nfs/home/<USER>/anaconda3/envs/tensorflow/lib/python3.6/site-packages/tensorflow/python/framework/dtypes.py:524: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_quint8 = np.dtype([(\"quint8\", np.uint8, 1)])\n", "/nfs/home/<USER>/anaconda3/envs/tensorflow/lib/python3.6/site-packages/tensorflow/python/framework/dtypes.py:525: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_qint16 = np.dtype([(\"qint16\", np.int16, 1)])\n", "/nfs/home/<USER>/anaconda3/envs/tensorflow/lib/python3.6/site-packages/tensorflow/python/framework/dtypes.py:526: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_quint16 = np.dtype([(\"quint16\", np.uint16, 1)])\n", "/nfs/home/<USER>/anaconda3/envs/tensorflow/lib/python3.6/site-packages/tensorflow/python/framework/dtypes.py:527: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_qint32 = np.dtype([(\"qint32\", np.int32, 1)])\n", "/nfs/home/<USER>/anaconda3/envs/tensorflow/lib/python3.6/site-packages/tensorflow/python/framework/dtypes.py:532: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  np_resource = np.dtype([(\"resource\", np.ubyte, 1)])\n", "Using TensorFlow backend.\n"]}], "source": ["# load packages\n", "import pandas as pd\n", "import pickle\n", "import numpy as np\n", "import tensorflow as tf\n", "import keras\n", "from keras import backend as K\n", "from keras.models import load_model, Model\n", "from keras.layers import Flatten, Dense, Dropout, Activation, Input, LSTM, Reshape, Conv2D, MaxPooling2D, CuDNNLSTM\n", "from keras.optimizers import Adam\n", "from keras.layers.advanced_activations import LeakyReLU\n", "from keras.utils import np_utils\n", "\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import classification_report, accuracy_score\n", "\n", "# set random seeds\n", "from tensorflow import set_random_seed\n", "from keras.backend.tensorflow_backend import set_session\n", "np.random.seed(1)\n", "set_random_seed(2)\n", "\n", "# limit gpu usage for keras with tensorflow 1\n", "config = tf.ConfigProto()\n", "config.gpu_options.allow_growth = True\n", "set_session(tf.Session(config=config))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data preparation\n", "\n", "We used no auction dataset that is normalised by decimal precision approach in their work. The first 40 columns of the FI-2010 dataset are 10 levels ask and bid information for a limit order book and we only use these 40 features in our network. The last 5 columns of the FI-2010 dataset are the labels with different prediction horizons. "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def prepare_x(data):\n", "    df1 = data[:40, :].T\n", "    return np.array(df1)\n", "\n", "def get_label(data):\n", "    lob = data[-5:, :].T\n", "    return lob\n", "\n", "def data_classification(X, Y, T):\n", "    [N, D] = X.shape\n", "    df = np.array(X)\n", "    dY = np.array(Y)\n", "    dataY = dY[T - 1:N]\n", "    dataX = np.zeros((N - T + 1, T, D))\n", "    for i in range(T, N + 1):\n", "        dataX[i - T] = df[i - T:i, :]\n", "    return dataX.reshape(dataX.shape + (1,)), dataY\n", "\n", "def prepare_x_y(data, k, T):\n", "    x = prepare_x(data)\n", "    y = get_label(data)\n", "    x, y = data_classification(x, y, T=T)\n", "    y = y[:,k] - 1\n", "    y = np_utils.to_categorical(y, 3)\n", "    return x, y"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(203701, 100, 40, 1) (203701, 3)\n", "(50851, 100, 40, 1) (50851, 3)\n", "(139488, 100, 40, 1) (139488, 3)\n"]}], "source": ["# please change the data_path to your local path\n", "# data_path = '/nfs/home/<USER>/limit_order_book/data'\n", "\n", "dec_data = np.loadtxt('Train_Dst_NoAuction_DecPre_CF_7.txt')\n", "dec_train = dec_data[:, :int(np.floor(dec_data.shape[1] * 0.8))]\n", "dec_val = dec_data[:, int(np.floor(dec_data.shape[1] * 0.8)):]\n", "\n", "dec_test1 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_7.txt')\n", "dec_test2 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_8.txt')\n", "dec_test3 = np.loadtxt('Test_Dst_NoAuction_DecPre_CF_9.txt')\n", "dec_test = np.hstack((dec_test1, dec_test2, dec_test3))\n", "\n", "k = 4 # which prediction horizon\n", "T = 100 # the length of a single input\n", "n_hiddens = 64\n", "checkpoint_filepath = './model_tensorflow1_weights'\n", "\n", "trainX_CNN, trainY_CNN = prepare_x_y(dec_train, k, T)\n", "valX_CNN, valY_CNN = prepare_x_y(dec_val, k, T)\n", "testX_CNN, testY_CNN = prepare_x_y(dec_test, k, T)\n", "\n", "print(trainX_CNN.shape, trainY_CNN.shape)\n", "print(valX_CNN.shape, valY_CNN.shape)\n", "print(testX_CNN.shape, testY_CNN.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Architecture\n", "\n", "Please find the detailed discussion of our model architecture in our paper."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["__________________________________________________________________________________________________\n", "Layer (type)                    Output Shape         Param #     Connected to                     \n", "==================================================================================================\n", "input_1 (InputLayer)            (None, 100, 40, 1)   0                                            \n", "__________________________________________________________________________________________________\n", "conv2d_1 (Conv2D)               (None, 100, 20, 32)  96          input_1[0][0]                    \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_1 (LeakyReLU)       (None, 100, 20, 32)  0           conv2d_1[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_2 (Conv2D)               (None, 100, 20, 32)  4128        leaky_re_lu_1[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_2 (LeakyReLU)       (None, 100, 20, 32)  0           conv2d_2[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_3 (Conv2D)               (None, 100, 20, 32)  4128        leaky_re_lu_2[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_3 (LeakyReLU)       (None, 100, 20, 32)  0           conv2d_3[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_4 (Conv2D)               (None, 100, 10, 32)  2080        leaky_re_lu_3[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_4 (LeakyReLU)       (None, 100, 10, 32)  0           conv2d_4[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_5 (Conv2D)               (None, 100, 10, 32)  4128        leaky_re_lu_4[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_5 (LeakyReLU)       (None, 100, 10, 32)  0           conv2d_5[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_6 (Conv2D)               (None, 100, 10, 32)  4128        leaky_re_lu_5[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_6 (LeakyReLU)       (None, 100, 10, 32)  0           conv2d_6[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_7 (Conv2D)               (None, 100, 1, 32)   10272       leaky_re_lu_6[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_7 (LeakyReLU)       (None, 100, 1, 32)   0           conv2d_7[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_8 (Conv2D)               (None, 100, 1, 32)   4128        leaky_re_lu_7[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_8 (LeakyReLU)       (None, 100, 1, 32)   0           conv2d_8[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_9 (Conv2D)               (None, 100, 1, 32)   4128        leaky_re_lu_8[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_9 (LeakyReLU)       (None, 100, 1, 32)   0           conv2d_9[0][0]                   \n", "__________________________________________________________________________________________________\n", "conv2d_10 (Conv2D)              (None, 100, 1, 64)   2112        leaky_re_lu_9[0][0]              \n", "__________________________________________________________________________________________________\n", "conv2d_12 (Conv2D)              (None, 100, 1, 64)   2112        leaky_re_lu_9[0][0]              \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_10 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_10[0][0]                  \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_12 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_12[0][0]                  \n", "__________________________________________________________________________________________________\n", "max_pooling2d_1 (MaxPooling2D)  (None, 100, 1, 32)   0           leaky_re_lu_9[0][0]              \n", "__________________________________________________________________________________________________\n", "conv2d_11 (Conv2D)              (None, 100, 1, 64)   12352       leaky_re_lu_10[0][0]             \n", "__________________________________________________________________________________________________\n", "conv2d_13 (Conv2D)              (None, 100, 1, 64)   20544       leaky_re_lu_12[0][0]             \n", "__________________________________________________________________________________________________\n", "conv2d_14 (Conv2D)              (None, 100, 1, 64)   2112        max_pooling2d_1[0][0]            \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_11 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_11[0][0]                  \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_13 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_13[0][0]                  \n", "__________________________________________________________________________________________________\n", "leaky_re_lu_14 (LeakyReLU)      (None, 100, 1, 64)   0           conv2d_14[0][0]                  \n", "__________________________________________________________________________________________________\n", "concatenate_1 (Concatenate)     (None, 100, 1, 192)  0           leaky_re_lu_11[0][0]             \n", "                                                                 leaky_re_lu_13[0][0]             \n", "                                                                 leaky_re_lu_14[0][0]             \n", "__________________________________________________________________________________________________\n", "reshape_1 (Reshape)             (None, 100, 192)     0           concatenate_1[0][0]              \n", "__________________________________________________________________________________________________\n", "dropout_1 (Dropout)             (None, 100, 192)     0           reshape_1[0][0]                  \n", "__________________________________________________________________________________________________\n", "cu_dnnlstm_1 (CuDNNLSTM)        (None, 64)           66048       dropout_1[0][0]                  \n", "__________________________________________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)                 (None, 3)            195         cu_dnnlstm_1[0][0]               \n", "==================================================================================================\n", "Total params: 142,691\n", "Trainable params: 142,691\n", "Non-trainable params: 0\n", "__________________________________________________________________________________________________\n"]}], "source": ["def create_deeplob(T, NF, number_of_lstm):\n", "    input_lmd = Input(shape=(T, NF, 1))\n", "    \n", "    # build the convolutional block\n", "    conv_first1 = Conv2D(32, (1, 2), strides=(1, 2))(input_lmd)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "\n", "    conv_first1 = Conv2D(32, (1, 2), strides=(1, 2))(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "\n", "    conv_first1 = Conv2D(32, (1, 10))(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    conv_first1 = Conv2D(32, (4, 1), padding='same')(conv_first1)\n", "    conv_first1 = keras.layers.LeakyReLU(alpha=0.01)(conv_first1)\n", "    \n", "    # build the inception module\n", "    convsecond_1 = Conv2D(64, (1, 1), padding='same')(conv_first1)\n", "    convsecond_1 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_1)\n", "    convsecond_1 = Conv2D(64, (3, 1), padding='same')(convsecond_1)\n", "    convsecond_1 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_1)\n", "\n", "    convsecond_2 = Conv2D(64, (1, 1), padding='same')(conv_first1)\n", "    convsecond_2 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_2)\n", "    convsecond_2 = Conv2D(64, (5, 1), padding='same')(convsecond_2)\n", "    convsecond_2 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_2)\n", "\n", "    convsecond_3 = MaxPooling2D((3, 1), strides=(1, 1), padding='same')(conv_first1)\n", "    convsecond_3 = Conv2D(64, (1, 1), padding='same')(convsecond_3)\n", "    convsecond_3 = keras.layers.LeakyReLU(alpha=0.01)(convsecond_3)\n", "    \n", "    convsecond_output = keras.layers.concatenate([convsecond_1, convsecond_2, convsecond_3], axis=3)\n", "    conv_reshape = Reshape((int(convsecond_output.shape[1]), int(convsecond_output.shape[3])))(convsecond_output)\n", "    conv_reshape = keras.layers.Dropout(0.2, noise_shape=(None, 1, int(conv_reshape.shape[2])))(conv_reshape, training=True)\n", "    \n", "    # build the last LSTM layer\n", "    # If GPU is not available, change CuDNNLSTM to LSTM\n", "    conv_lstm = CuDNNLSTM(number_of_lstm)(conv_reshape)\n", "\n", "    # build the output layer\n", "    out = Dense(3, activation='softmax')(conv_lstm)\n", "    model = Model(inputs=input_lmd, outputs=out)\n", "    adam = keras.optimizers.<PERSON>(lr=0.0001)\n", "    model.compile(optimizer=adam, loss='categorical_crossentropy', metrics=['accuracy'])\n", "\n", "    return model\n", "\n", "deeplob = create_deeplob(trainX_CNN.shape[1], trainX_CNN.shape[2], n_hiddens)\n", "deeplob.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Training"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train on 203701 samples, validate on 50851 samples\n", "Epoch 1/200\n", " - 54s - loss: 1.0248 - acc: 0.4215 - val_loss: 1.0870 - val_acc: 0.3723\n", "Epoch 2/200\n", " - 51s - loss: 0.9407 - acc: 0.4742 - val_loss: 1.0933 - val_acc: 0.3655\n", "Epoch 3/200\n", " - 51s - loss: 0.8884 - acc: 0.5236 - val_loss: 1.0440 - val_acc: 0.4301\n", "Epoch 4/200\n", " - 51s - loss: 0.8515 - acc: 0.5580 - val_loss: 0.9658 - val_acc: 0.4909\n", "Epoch 5/200\n", " - 51s - loss: 0.8151 - acc: 0.5920 - val_loss: 0.9749 - val_acc: 0.4886\n", "Epoch 6/200\n", " - 51s - loss: 0.7855 - acc: 0.6218 - val_loss: 0.8983 - val_acc: 0.5480\n", "Epoch 7/200\n", " - 51s - loss: 0.7536 - acc: 0.6524 - val_loss: 0.8887 - val_acc: 0.5708\n", "Epoch 8/200\n", " - 51s - loss: 0.7157 - acc: 0.6828 - val_loss: 0.8994 - val_acc: 0.5770\n", "Epoch 9/200\n", " - 51s - loss: 0.6790 - acc: 0.7089 - val_loss: 0.8328 - val_acc: 0.6199\n", "Epoch 10/200\n", " - 51s - loss: 0.6524 - acc: 0.7252 - val_loss: 0.8478 - val_acc: 0.6162\n", "Epoch 11/200\n", " - 51s - loss: 0.6273 - acc: 0.7390 - val_loss: 0.8556 - val_acc: 0.6190\n", "Epoch 12/200\n", " - 51s - loss: 0.6082 - acc: 0.7500 - val_loss: 0.7872 - val_acc: 0.6530\n", "Epoch 13/200\n", " - 51s - loss: 0.5934 - acc: 0.7582 - val_loss: 0.8180 - val_acc: 0.6320\n", "Epoch 14/200\n", " - 51s - loss: 0.5804 - acc: 0.7648 - val_loss: 0.7917 - val_acc: 0.6573\n", "Epoch 15/200\n", " - 51s - loss: 0.5671 - acc: 0.7722 - val_loss: 0.7642 - val_acc: 0.6693\n", "Epoch 16/200\n", " - 51s - loss: 0.5554 - acc: 0.7774 - val_loss: 0.7776 - val_acc: 0.6701\n", "Epoch 17/200\n", " - 51s - loss: 0.5457 - acc: 0.7834 - val_loss: 0.7449 - val_acc: 0.6819\n", "Epoch 18/200\n", " - 51s - loss: 0.5349 - acc: 0.7886 - val_loss: 0.7504 - val_acc: 0.6849\n", "Epoch 19/200\n", " - 51s - loss: 0.5273 - acc: 0.7921 - val_loss: 0.7964 - val_acc: 0.6614\n", "Epoch 20/200\n", " - 52s - loss: 0.5197 - acc: 0.7955 - val_loss: 0.7531 - val_acc: 0.6874\n", "Epoch 21/200\n", " - 52s - loss: 0.5107 - acc: 0.8003 - val_loss: 0.7297 - val_acc: 0.6936\n", "Epoch 22/200\n", " - 52s - loss: 0.5044 - acc: 0.8031 - val_loss: 0.7360 - val_acc: 0.6959\n", "Epoch 23/200\n", " - 52s - loss: 0.4968 - acc: 0.8066 - val_loss: 0.7387 - val_acc: 0.6946\n", "Epoch 24/200\n", " - 52s - loss: 0.4902 - acc: 0.8105 - val_loss: 0.7463 - val_acc: 0.6887\n", "Epoch 25/200\n", " - 52s - loss: 0.4840 - acc: 0.8126 - val_loss: 0.7507 - val_acc: 0.6930\n", "Epoch 26/200\n", " - 52s - loss: 0.4774 - acc: 0.8160 - val_loss: 0.7477 - val_acc: 0.6888\n", "Epoch 27/200\n", " - 52s - loss: 0.4728 - acc: 0.8184 - val_loss: 0.7470 - val_acc: 0.6904\n", "Epoch 28/200\n", " - 52s - loss: 0.4674 - acc: 0.8200 - val_loss: 0.7539 - val_acc: 0.6883\n", "Epoch 29/200\n", " - 52s - loss: 0.4624 - acc: 0.8224 - val_loss: 0.7674 - val_acc: 0.6889\n", "Epoch 30/200\n", " - 52s - loss: 0.4571 - acc: 0.8243 - val_loss: 0.7620 - val_acc: 0.6845\n", "Epoch 31/200\n", " - 52s - loss: 0.4530 - acc: 0.8260 - val_loss: 0.7400 - val_acc: 0.6942\n", "Epoch 32/200\n", " - 52s - loss: 0.4479 - acc: 0.8286 - val_loss: 0.7603 - val_acc: 0.6895\n", "Epoch 33/200\n", " - 52s - loss: 0.4434 - acc: 0.8310 - val_loss: 0.7808 - val_acc: 0.6836\n", "Epoch 34/200\n", " - 52s - loss: 0.4395 - acc: 0.8331 - val_loss: 0.7585 - val_acc: 0.6978\n", "Epoch 35/200\n", " - 52s - loss: 0.4344 - acc: 0.8349 - val_loss: 0.7672 - val_acc: 0.6958\n", "Epoch 36/200\n", " - 52s - loss: 0.4309 - acc: 0.8366 - val_loss: 0.7707 - val_acc: 0.6872\n", "Epoch 37/200\n", " - 52s - loss: 0.4257 - acc: 0.8388 - val_loss: 0.7861 - val_acc: 0.6887\n", "Epoch 38/200\n", " - 52s - loss: 0.4231 - acc: 0.8403 - val_loss: 0.7701 - val_acc: 0.6910\n", "Epoch 39/200\n", " - 52s - loss: 0.4188 - acc: 0.8422 - val_loss: 0.7782 - val_acc: 0.6919\n", "Epoch 40/200\n", " - 52s - loss: 0.4148 - acc: 0.8438 - val_loss: 0.7657 - val_acc: 0.6924\n", "Epoch 41/200\n", " - 52s - loss: 0.4116 - acc: 0.8440 - val_loss: 0.7722 - val_acc: 0.6902\n", "Epoch 42/200\n", " - 52s - loss: 0.4076 - acc: 0.8469 - val_loss: 0.8069 - val_acc: 0.6860\n", "Epoch 43/200\n", " - 52s - loss: 0.4044 - acc: 0.8478 - val_loss: 0.8243 - val_acc: 0.6750\n", "Epoch 44/200\n", " - 52s - loss: 0.4011 - acc: 0.8491 - val_loss: 0.7867 - val_acc: 0.6888\n", "Epoch 45/200\n", " - 51s - loss: 0.3982 - acc: 0.8508 - val_loss: 0.8032 - val_acc: 0.6899\n", "Epoch 46/200\n", " - 52s - loss: 0.3937 - acc: 0.8524 - val_loss: 0.8006 - val_acc: 0.6896\n", "Epoch 47/200\n", " - 52s - loss: 0.3922 - acc: 0.8526 - val_loss: 0.8281 - val_acc: 0.6788\n", "Epoch 48/200\n", " - 52s - loss: 0.3880 - acc: 0.8546 - val_loss: 0.8208 - val_acc: 0.6869\n", "Epoch 49/200\n", " - 52s - loss: 0.3850 - acc: 0.8558 - val_loss: 0.8451 - val_acc: 0.6729\n", "Epoch 50/200\n", " - 52s - loss: 0.3821 - acc: 0.8573 - val_loss: 0.8139 - val_acc: 0.6878\n", "Epoch 51/200\n", " - 52s - loss: 0.3798 - acc: 0.8584 - val_loss: 0.8499 - val_acc: 0.6815\n", "Epoch 52/200\n", " - 52s - loss: 0.3763 - acc: 0.8597 - val_loss: 0.8292 - val_acc: 0.6843\n", "Epoch 53/200\n", " - 52s - loss: 0.3737 - acc: 0.8608 - val_loss: 0.8298 - val_acc: 0.6800\n", "Epoch 54/200\n", " - 52s - loss: 0.3715 - acc: 0.8614 - val_loss: 0.8400 - val_acc: 0.6785\n", "Epoch 55/200\n", " - 52s - loss: 0.3678 - acc: 0.8633 - val_loss: 0.8596 - val_acc: 0.6743\n", "Epoch 56/200\n", " - 52s - loss: 0.3661 - acc: 0.8639 - val_loss: 0.8635 - val_acc: 0.6775\n", "Epoch 57/200\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<timed exec>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/tensorflow/lib/python3.6/site-packages/keras/engine/training.py\u001b[0m in \u001b[0;36mfit\u001b[0;34m(self, x, y, batch_size, epochs, verbose, callbacks, validation_split, validation_data, shuffle, class_weight, sample_weight, initial_epoch, steps_per_epoch, validation_steps, **kwargs)\u001b[0m\n\u001b[1;32m   1037\u001b[0m                                         \u001b[0minitial_epoch\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0minitial_epoch\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1038\u001b[0m                                         \u001b[0msteps_per_epoch\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0msteps_per_epoch\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1039\u001b[0;31m                                         validation_steps=validation_steps)\n\u001b[0m\u001b[1;32m   1040\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1041\u001b[0m     def evaluate(self, x=None, y=None,\n", "\u001b[0;32m~/anaconda3/envs/tensorflow/lib/python3.6/site-packages/keras/engine/training_arrays.py\u001b[0m in \u001b[0;36mfit_loop\u001b[0;34m(model, f, ins, out_labels, batch_size, epochs, verbose, callbacks, val_f, val_ins, shuffle, callback_metrics, initial_epoch, steps_per_epoch, validation_steps)\u001b[0m\n\u001b[1;32m    197\u001b[0m                     \u001b[0mins_batch\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mins_batch\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtoarray\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    198\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 199\u001b[0;31m                 \u001b[0mouts\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mf\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mins_batch\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    200\u001b[0m                 \u001b[0mouts\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mto_list\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mouts\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    201\u001b[0m                 \u001b[0;32mfor\u001b[0m \u001b[0ml\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mo\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mzip\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mout_labels\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mouts\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/tensorflow/lib/python3.6/site-packages/keras/backend/tensorflow_backend.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, inputs)\u001b[0m\n\u001b[1;32m   2713\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_legacy_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2714\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2715\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2716\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2717\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mpy_any\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mis_tensor\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mx\u001b[0m \u001b[0;32min\u001b[0m \u001b[0minputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/tensorflow/lib/python3.6/site-packages/keras/backend/tensorflow_backend.py\u001b[0m in \u001b[0;36m_call\u001b[0;34m(self, inputs)\u001b[0m\n\u001b[1;32m   2673\u001b[0m             \u001b[0mfetched\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_callable_fn\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0marray_vals\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrun_metadata\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mrun_metadata\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2674\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2675\u001b[0;31m             \u001b[0mfetched\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_callable_fn\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0marray_vals\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2676\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mfetched\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0moutputs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2677\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/tensorflow/lib/python3.6/site-packages/tensorflow/python/client/session.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1437\u001b[0m           ret = tf_session.TF_SessionRunCallable(\n\u001b[1;32m   1438\u001b[0m               \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_session\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_session\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_handle\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mstatus\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1439\u001b[0;31m               run_metadata_ptr)\n\u001b[0m\u001b[1;32m   1440\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mrun_metadata\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1441\u001b[0m           \u001b[0mproto_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtf_session\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mTF_GetBuffer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mrun_metadata_ptr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["%%time\n", "\n", "model_checkpoint_callback = tf.keras.callbacks.ModelCheckpoint(\n", "    filepath=checkpoint_filepath,\n", "    save_weights_only=True,\n", "    monitor='val_loss',\n", "    mode='auto',\n", "    save_best_only=True)\n", "\n", "deeplob.fit(trainX_CNN, trainY_CNN, validation_data=(valX_CNN, valY_CNN), \n", "            epochs=200, batch_size=128, verbose=2, callbacks=[model_checkpoint_callback])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Model Testing"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["deeplob.load_weights(checkpoint_filepath)\n", "pred = deeplob.predict(testX_CNN)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["accuracy_score: 0.7489461459050241\n", "              precision    recall  f1-score   support\n", "\n", "           0     0.7188    0.7609    0.7393     47915\n", "           1     0.8298    0.7402    0.7825     48050\n", "           2     0.7067    0.7454    0.7255     43523\n", "\n", "   micro avg     0.7489    0.7489    0.7489    139488\n", "   macro avg     0.7518    0.7488    0.7491    139488\n", "weighted avg     0.7533    0.7489    0.7499    139488\n", "\n"]}], "source": ["print('accuracy_score:', accuracy_score(np.argmax(testY_CNN, axis=1), np.argmax(pred, axis=1)))\n", "print(classification_report(np.argmax(testY_CNN, axis=1), np.argmax(pred, axis=1), digits=4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.12"}}, "nbformat": 4, "nbformat_minor": 2}