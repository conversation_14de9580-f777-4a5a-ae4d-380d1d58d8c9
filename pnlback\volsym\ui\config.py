import sys
import os

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

# from pnlback.volsym.volmodel.models.zts import VolatilityModel,fit_svi_model
from pnlback.volsym.volmodel.models.svi_cs import VolatilityModel,fit_svi_model
# from pnlback.volsym.volmodel.models.svi_git import VolatilityModel,fit_svi_model

MULT=10000
UNDER=['510500','510050','510300','i'][0]

def param_weights(model):
    weights = [0.3, 3.0, 2, 0.1, 0.5]
    return {p: weights[i] for i, p in enumerate(model.params_name)}
volparams=param_weights(VolatilityModel)
volconfig={
            'svi_enabled': True,  # 添加SVI拟合开关
            'svi_weights': {
                'fit': 0.1,
                'param': 0,
                'atm': 0.8,
                'time_decay': 20
            },
            'param_weights': volparams,
            'atm_weights': {
                'atm_vol': 1,
                'skew': 1.0,
                'convexity': 1.0,
                'otm_slope': 1.0,
                'itm_slope': 1.0
            },
            'FIT_SVI_MODEL': fit_svi_model
        }