from PySide6.QtWidgets import QComboBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QStandardItemModel, QStandardItem
import numpy as np


class CheckableComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.view().pressed.connect(self.handle_item_pressed)
        self.setModel(QStandardItemModel(self))

    def handle_item_pressed(self, index):
        item = self.model().itemFromIndex(index)
        all_months_item = self.model().item(0)  # 将这行移到这里

        if item.text() == "所有月份":
            # 如果选择"所有月份"，取消选择其他所有月份
            self.set_all_months_state(Qt.Unchecked)
            item.setCheckState(Qt.Checked)
        else:
            #如果选择其他月份，取消选择"所有月份"
            all_months_item.setCheckState(Qt.Unchecked)

            if item.checkState() == Qt.Checked:
                item.setCheckState(Qt.Unchecked)
            else:
                item.setCheckState(Qt.Checked)

        # 检查是否有选中的月份，如果没有，则选中"所有月份"
        if not any(self.model().item(i).checkState() == Qt.Checked for i in range(1, self.model().rowCount())):
            all_months_item.setCheckState(Qt.Checked)

    def set_all_months_state(self, state):
        for i in range(1, self.model().rowCount()):
            self.model().item(i).setCheckState(state)

    def item_checked(self, index):
        return self.model().item(index).checkState() == Qt.Checked

    def get_checked_items(self):
        return [self.model().item(i).text() for i in range(self.model().rowCount()) if self.item_checked(i)]