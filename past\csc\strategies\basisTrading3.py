# -*- coding: utf-8 -*-

import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random

class BasisTrading3(StrategyTemplate):
    """"""

    author = "vnpy"

    buy_price = 0
    short_price = 0

    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'path',
        'eta',
        'x_stop',
        'net_limit',
        'edge',
        'type'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]

    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        self.buy_refer_orderids = None
        self.short_refer_orderids = None
        
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net=0
        self.fair_maker = 0
        self.fair_refer = 0
        self.rCount=0
        self.mCount = 0
        self.pauseCount=0
        self.isQuoting=False
        self.lastTicks={self.maker:0,self.refer:0}
        self.comments  = None
        self.ewma = 0
        self.Basis = 0
        self.isQuoting = True
        self.offer_list = []
        self.offer_list_head = []
        self.basisCount = 0
        self.basislist = np.zeros(99999999)
        self.bidP = 0
        self.askP = 0
        self.refer_buy_price = 0
        self.refer_short_price = 0
        self.maker_fair_order = 0
        self.refer_fair_order = 0
        self.last_net = 0
        self.last_bidP = 0
        self.last_askP = 0
        self.last_signal = 0


    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.maker:
                    self.on_tick_maker(ticks[key])
                elif key==self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick
        
    def Validtick(self,tick): #无效的tick不计算ewma
        if tick.ask_price_1 - tick.bid_price_1 > 9*self.priceticks[self.maker]:
            return False
        return True

    def on_hedge_2(self,tick):

        buyResendFlag=False
        shortResendFlag=False
        ts = self.priceticks[self.refer]
        lots = self.lots
            
        if tick.ask_price_1 <= self.refer_short_price - self.x_stop * ts and self.refer_short_price != 0: 
            shortResendFlag=True
        if tick.bid_price_1 >= self.refer_buy_price + self.x_stop * ts and self.refer_buy_price != 0: 
            buyResendFlag=True

        if self.net >= abs(self.net_limit):
            if not self.short_hedge_orderids: #首次发单
                self.refer_short_price = max(np.ceil(self.fair_refer),tick.ask_price_1 - ts)
                self.short_hedge_orderids = self.short(self.refer,self.refer_short_price, lots,'refer_hedge')
                self.refer_fair_mid = self.fair_refer 
            elif shortResendFlag and self.short_hedge_orderids[0] :
                self.cancel_order(self.short_hedge_orderids[0])
                self.refer_short_price = tick.ask_price_1
                self.short_hedge_orderids = self.short(self.refer,self.refer_short_price, lots,'refer_hedge')
        if self.net <= -abs(self.net_limit):
            if not self.buy_hedge_orderids: #首次发单
                self.refer_buy_price = min(np.floor(self.fair_refer),tick.bid_price_1 + ts)
                self.buy_hedge_orderids = self.buy(self.refer,self.refer_buy_price, lots,'refer_hedge')
                self.refer_fair_mid = self.fair_refer  
            elif buyResendFlag and self.buy_hedge_orderids[0] :
                self.cancel_order(self.buy_hedge_orderids[0])
                self.refer_buy_price = tick.bid_price_1
                self.buy_hedge_orderids = self.buy(self.refer,self.refer_buy_price,lots,'refer_hedge')  

    def on_hedge_1(self,tick):
        lots = self.lots
        if abs(self.net) > abs(self.last_net):
            if self.net > self.last_net:
                self.short(self.refer,tick.ask_price_1,lots,'refer_sell')
                self.refer_fair_mid = self.fair_refer 
            else:
                self.buy(self.refer,tick.bid_price_1,lots,'refer_buy')
                self.refer_fair_mid = self.fair_refer 

    def on_refer_cancel(self,tick):
        orders = list(self.strategy_engine.active_limit_orders.items())
        if self.net > 0:
            for vt_orderid, order in orders:
                if order.price == tick.bid_price_1 and order.direction.value == '多' and order.vt_symbol == self.refer:
                    self.cancel_order(vt_orderid)
        if self.net < 0:
            for vt_orderid, order in orders:
                if order.price == tick.ask_price_1 and order.direction.value == '空' and order.vt_symbol == self.refer:
                    self.cancel_order(vt_orderid)
                    
    def on_refer_time(self,tick):
        if (tick.datetime.time() > dtime(14,54) and tick.datetime.time() < dtime(14, 55)):
            self.cancel_symbol(self.maker) #做市合约撤单
            if self.net > 0:
                self.short(self.refer,tick.bid_price_1,abs(self.net),'end_short')
            if self.net < 0:
                self.buy(self.refer,tick.ask_price_1,abs(self.net),'end_buy')
                
    def on_maker_pricing(self,tick):
        if self.mCount > 10 and self.Validtick(tick): #启动后10个tick开始定价,对有效的tick进行定价
            self.basisCount += 1
            self.Basis = self.fair_maker - self.fair_refer
            self.basislist[self.basisCount] = self.Basis

        buyResendFlag=False
        shortResendFlag=False
        self.signal = 'MM'

        if self.basisCount > self.path:
            MovingAverage = np.mean(self.basislist[self.basisCount - self.path:self.basisCount])
            Std = np.std(self.basislist[self.basisCount - self.path:self.basisCount])
            bollup = MovingAverage + self.eta * Std
            bolldown = MovingAverage - self.eta * Std
            self.offer_list.append([self.maker.split('.')[0],tick.datetime,self.Basis,bollup,bolldown,MovingAverage,Std])
            if self.MMTrading and self.Validtick(tick):
                if self.Basis > bollup and self.get_pos(self.maker) > -self.maxPos and abs(self.net) < abs(self.net_limit):
                    self.bidP = min(tick.bid_price_1,tick.ask_price_1 - self.edge)
                    self.askP = tick.ask_price_1
                    self.signal = 'up_sell'
                    
                elif self.Basis < bolldown and self.get_pos(self.maker) < self.maxPos and abs(self.net) < abs(self.net_limit):
                    self.askP = max(tick.ask_price_1,tick.bid_price_1 + self.edge)
                    self.bidP = tick.bid_price_1
                    self.signal = 'down_buy'
    
                else:
                    self.bidP = 0
                    self.askP = 0
            else:
                    self.bidP = 0
                    self.askP = 0   

        if self.last_bidP !=self.bidP or self.signal != self.last_signal:
            shortResendFlag=True
        if self.last_askP !=self.askP or self.signal != self.last_signal:
            buyResendFlag=True     
            
        if self.type == 2:
            if self.signal == 'up_sell':
                self.bidP = 0
            if self.signal == 'down_buy':
                self.askP = 0            

        if self.askP > 0 :
            if not self.short_vt_orderids:
                self.short_vt_orderids = self.short(self.maker,self.askP, self.lots,self.signal)
            elif shortResendFlag and self.short_vt_orderids[0] :
                self.cancel_order(self.short_vt_orderids[0])
                self.short_vt_orderids = self.short(self.maker,self.askP, self.lots,self.signal)
        else:
            if self.short_vt_orderids :
                self.cancel_order(self.short_vt_orderids[0])

        if self.bidP > 0 :
            if not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(self.maker,self.bidP, self.lots,self.signal)
            elif buyResendFlag and self.buy_vt_orderids[0] :
                self.cancel_order(self.buy_vt_orderids[0])
                self.buy_vt_orderids = self.buy(self.maker,self.bidP, self.lots,self.signal)
        else:
            if self.buy_vt_orderids :
                self.cancel_order(self.buy_vt_orderids[0])

        self.last_bidP = self.bidP
        self.last_askP = self.askP

        self.last_signal = self.signal
        
        
    def on_tick_refer(self,tick):
        self.rCount += 1 
        self.fair_refer = (tick.bid_price_1 + tick.ask_price_1) / 2
        
        tick_maker = self.strategy_engine.ticks[self.maker]
        self.on_maker_pricing(tick_maker)

        self.on_refer_cancel(tick)

        if not self.strategy_engine.refer_test:    
            if abs(self.net) < self.net_limit:
                self.on_hedge_1(tick)
            else:
                self.on_hedge_2(tick)  

        self.on_refer_time(tick)
        self.last_net = self.net

    def on_tick_maker(self, tick):
        self.mCount += 1

        ts = self.priceticks[self.refer]
        net = self.net 
        lots = self.lots

        self.fair_maker = (tick.bid_price_1 + tick.ask_price_1) / 2
        self.offer_list_head = ['insid','datetime','Basis','bollup','bolldown','MovingAverage','Std']   
        
    def  on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if volume > 0:
            if trade.direction.value=='多':
                if self.net>=0 :            
                     self.avg = (self.net*self.avg +volume*self.fair_refer)/(self.net+volume)              
                elif volume+self.net>0: # net<0 # 平仓
                     self.avg = self.fair_refer         
                self.net += volume 
            #         
            elif trade.direction.value=='空':    
                if self.net<=0:
                    self.avg =(-self.net*self.avg + volume*self.fair_refer)/(-self.net+volume)
                elif volume-self.net>0: # net >0 # 平仓
                    self.avg=self.fair_refer
                self.net -= volume 
        if trade.vt_symbol == self.maker:        
            trade.basePrice = self.fair_refer
            trade.midPrice = self.fair_maker
        self.trade_basis = trade.midPrice - trade.basePrice
                
    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids,
            self.buy_refer_orderids,
            self.short_refer_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """ # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if not(
            (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 55))
            or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 14))
            or (dt.time() > dtime(10, 31) and dt.time() < dtime(11, 29))
            or (dt.time() > dtime(13, 31) and dt.time() < dtime(14, 55))
        ):
            self.cancel_all()
            self.MMTrading = False
        else:
            self.MMTrading = True
