#pragma once

#include <optional>
#include <memory>
#include <array>
#include <chrono>
#include <math.h>
#include "../Strategy.h"
#include "../MessageSubscriber.h"
#include "../MarketDataService.h"
#include "../IQuoteListener.h"

#define FUTURE_MM_NAN (sqrt(-1))

namespace FutureLiquidity
{
	struct PriceVolume
	{
		double price = 0.;
		VolumeType volume = 0;
	};
	struct FadeVolume
	{
		VolumeType bidFade = 0.;
		VolumeType askFade = 0.;
	};
	struct MMTrade
	{
		double price = 0.;
		VolumeType volume = 0;
		LongShort longShort = LongShort::LONG_SHORT_UNINITIALIZED;
		char comments[CHAR_SMALL_SIZE] = { 0 };
	};
	struct TransferedMarketData
	{
		std::string instrumentId;

		PriceType bid_price1;
		VolumeType bid_volume1;
		PriceType bid_price2;
		VolumeType bid_volume2;
		PriceType bid_price3;
		VolumeType bid_volume3;
		PriceType bid_price4;
		VolumeType bid_volume4;
		PriceType bid_price5;
		VolumeType bid_volume5;

		PriceType ask_price1;
		VolumeType ask_volume1;
		PriceType ask_price2;
		VolumeType ask_volume2;
		PriceType ask_price3;
		VolumeType ask_volume3;
		PriceType ask_price4;
		VolumeType ask_volume4;
		PriceType ask_price5;
		VolumeType ask_volume5;

		VolumeType volume; // 市场成交量
		PriceType upper_limit;
		PriceType lower_limit;
		PriceType last_price;
		MoneyType turnover;

		chrono::steady_clock::time_point datetime;
	};
	struct LastPoint
	{
		VolumeType net = 0;
		TransferedMarketData tick;
		chrono::steady_clock::time_point timepoint;
	};
}

class FutureLiquidityStrategy : public Strategy, public MessageSubscriber<MarketData>, public IQuoteListener
{
public:
	enum class SimpleOrderStatus
	{
		Ready,
		PendingAdd,
		ExchangeOrder,
		PendingDelete,
		Terminated,
	};
	SimpleOrderStatus getSimpleOrderStatus(Quote* quote_)
	{
		if (!quote_)
		{
			return SimpleOrderStatus::Ready;
		}
		switch (quote_->quoteStatus)
		{
		case QuoteStatus::PENDING_ADD:
			return SimpleOrderStatus::PendingAdd;
		case QuoteStatus::EXCHANGE_ORDER:
		case QuoteStatus::PARTIAL_FILLED:
			return SimpleOrderStatus::ExchangeOrder;
		case QuoteStatus::PENDING_DELETE:
			return SimpleOrderStatus::PendingDelete;
		case QuoteStatus::FILLED:
		case QuoteStatus::DELETED:
		case QuoteStatus::INSERT_FAILED:
		case QuoteStatus::READY:
		default:
			return SimpleOrderStatus::Terminated;
		}
	}
	SimpleOrderStatus getSimpleOrderStatus(Order* order_)
	{
		if (!order_)
		{
			return SimpleOrderStatus::Ready;
		}
		switch (order_->orderStatus)
		{
		case OrderStatus::PENDING_ADD:
			return SimpleOrderStatus::PendingAdd;
		case OrderStatus::EXCHANGE_ORDER:
		case OrderStatus::PARTIAL_FILLED:
			return SimpleOrderStatus::ExchangeOrder;
		case OrderStatus::PENDING_DELETE:
			return SimpleOrderStatus::PendingDelete;
		case OrderStatus::FILLED:
		case OrderStatus::DELETED:
		case OrderStatus::INSERT_FAILED:
		case OrderStatus::READY:
		default:
			return SimpleOrderStatus::Terminated;
		}
	}
	SimpleOrderStatus getSyntheticOrderStatus(Order* askOrder_, Order* bidOrder_)
	{
		OrderStatus bidStatus = bidOrder_ ? bidOrder_->orderStatus : OrderStatus::READY;
		OrderStatus askStatus = askOrder_ ? askOrder_->orderStatus : OrderStatus::READY;

		auto isBothStatus = [=](OrderStatus status_) { return bidStatus == status_ && askStatus == status_; };
		auto isAnyStatus = [=](OrderStatus status_) { return bidStatus == status_ || askStatus == status_; };

		if (isAnyStatus(OrderStatus::PENDING_ADD))
		{
			return SimpleOrderStatus::PendingAdd;
		}
		else if (isAnyStatus(OrderStatus::PENDING_DELETE))
		{
			return SimpleOrderStatus::PendingDelete;
		}
		else if (isAnyStatus(EXCHANGE_ORDER) || isAnyStatus(PARTIAL_FILLED))
		{
			return SimpleOrderStatus::ExchangeOrder;
		}
		else if (isBothStatus(READY))
		{
			return SimpleOrderStatus::Ready;
		}
		else
		{
			return SimpleOrderStatus::Terminated;
		}
	}

	enum class MakerEventLogicChain
	{
		Ready,
		CheckHedge,
		CancelQuoteForHedging,
		Hedging,
		Quoting,
		Finish,
	};
	enum class ReferEventLogicChain
	{
		Ready,
		Filter,
		Hedging,
		Finish,
	};

	FutureLiquidityStrategy(int id_, PriceSuggestor priceSuggester_, int portfolioId_,
		StrategyTypeId strategyTypeId_, bool isAlive_, StrategyStatus strategyStatus_, std::string const& parameters_,
		std::set<int> const& appliedInstruments_, std::string const& comments_, int level_, bool isQuotingStrategy,
		bool useQuoteService_, bool isQuoteDeleteRequired_, bool beGrouped_, int groupId_);
	FutureLiquidityStrategy() {}

	void strategyMain() override final;

	void onOrder(Order const& order_) override;
	void onQuote(Quote const& quote_) override;
	void onTrade(Trade const& trade_) override;
	void onMessage(MarketData const& marketData_) override;

	bool sendQuote(Quote& quote_);
	bool deleteQuote(Quote& quote_);

private:
	bool initStrategyJobs() override final;
	bool cleanUpStrategyJobs() override final;

	LockFreeMPMCQueue<MarketData> _marketDataQueue{ MAX_QUEUE_LENGTH };
	LockFreeMPMCQueue<Quote> _quoteFeedQueue{ MAX_QUEUE_LENGTH };
	std::unordered_map<int64_t, Quote> _quoteFeedsByInternalQuoteId;

	void processMarketDatas();
	void processTrades() override;
	void processQuotes();
	void processOrders() override;

	FutureLiquidity::PriceVolume getBid5(FutureLiquidity::TransferedMarketData quote, VolumeType bid_volume, double away_price);
	FutureLiquidity::PriceVolume getAsk5(FutureLiquidity::TransferedMarketData quote, VolumeType ask_volume, double away_price);
	double getFairPrice(double lastP, double askP, double bidP, double edgeP);

	FutureLiquidity::TransferedMarketData transferMarketData(const MarketData& marketData_);
	void onMMTrade(FutureLiquidity::MMTrade trade);

	void adjustOrderBook();
	int min_book_volume();
	int threeMedian(int a, int b, int c);
	int adjustedVolume(int volume);
	int depthSignal();
	bool checkNearMarketClose();
	void updateAutoCloseMode();
	void makerFilter();
	void refreshPrice();
	OpenClose getOpenClose(Instrument* pInstrument_, LongShort longShort_, int volume_);
	bool checkHedge();
	bool doSendHedge();
	bool doCancelAll();
	bool doCancelAllQuote();
	bool doCancelAllOrder();
	bool doSend();
	bool doSendQuote();
	bool doSendOrder();
	void doReset();
	bool doCounterReset();
	SimpleOrderStatus getSimpleMMOrderStatus();
	bool checkNeedDelete();
	bool checkCanSend();
	bool makerEventLogicChain();

	int getObligationTicks();
	void updateObligation();

	void setParameters(string const& paramStr_);
	std::string getParameters() const override final;

	const char _delimeter = INNER_LEVEL_1_DELIMINATOR;
	Instrument* _pInstrument = nullptr;
	Instrument* _pBaseInstrument = nullptr;
	Quote* _pPreQuote = nullptr;
	Quote* _pQuote = nullptr;
	Order* _pHedgeOrder = nullptr;
	Order* _pBidOrder = nullptr;
	Order* _pAskOrder = nullptr;
	FutureLiquidity::TransferedMarketData maker, refer,orderBook;

	// 策略内部变量
	// super parameters
	//std::atomic<double> _fixedEdge = 4;			// max loss per cycle
	std::atomic<int> _edge = 5;					// 最大有效价宽 tick数（ask-bid）
	std::atomic<VolumeType> _maxPos = 50;			// 最大单边 or 跨期持仓量
	std::atomic<VolumeType> _hold = 400;			// 最小持仓量 （单边， 成交量义务）

	//std::atomic<VolumeType> _maxV = 10; // 单笔最大下单数量 （因为交易所规定超过maxV，手续费不返还）
	//std::atomic<int> _minEdge = 1; // 报价最小价宽  （有些合约只考核时间义务，报窄了没有意义, 输入数值大于1，起作用, 取值要小于最大有效价宽）
	

	//var
	class Buffer
	{
	private:
		static const int size = 35000;
		std::array<double, size> buffer;
		std::atomic<int> count = 0;
	public:
		int getCount()
		{
			return count;
		}
		void reset()
		{
			buffer.fill(0);
			count = 0;
		}
		void insert(const double newValue)
		{
			if (count < size)
			{
				buffer[count++] = newValue;
			}
		}
		double getLastMin(int num_)
		{
			int i = count;
			if (i >= num_ && num_ > 0)
			{
				int lowerBound = i - num_;
				double ret = buffer[--i];
				while (--i >= lowerBound)
				{
					double item = buffer[i];
					ret = item < ret ? item : ret;
				}
				return ret;
			}
			else
			{
				return FUTURE_MM_NAN;
			}
		}
		double getLastMax(int num_)
		{
			int i = count;
			if (i >= num_ && num_ > 0)
			{
				int lowerBound = i - num_;
				double ret = buffer[--i];
				while (--i >= lowerBound)
				{
					double item = buffer[i];
					ret = item > ret ? item : ret;
				}
				return ret;
			}
			else
			{
				return FUTURE_MM_NAN;
			}
		}
		double getLast10Median()
		{
			int i = count;
			std::array<double, 10> last10;
			int j = 10;
			if (i >= 10)
			{
				int lowerBound = i - 10;
				last10[--j] = buffer[--i];
				while (--i >= lowerBound)
				{
					last10[--j] = buffer[i];
				}
				std::sort(last10.begin(), last10.end());
				return last10[5];
			}
			else
			{
				return FUTURE_MM_NAN;
			}
		}
		double getLast()
		{
			int i = count;
			if (i > 0)
			{
				return buffer[i - 1];
			}
			else
			{
				return FUTURE_MM_NAN;
			}
		}
		double getLastStd(int num_)
		{
			int totalNum = count;
			if (totalNum >= num_ && num_ > 1)
			{
				int lowerBound = totalNum - num_;
				double sum = 0.;
				int i = totalNum;
				while (--i >= lowerBound)
				{
					sum += buffer[i];
				}
				double mean = sum / num_;
				i = totalNum;
				double residual = 0.;
				while (--i >= lowerBound)
				{
					double error = buffer[i] - mean;
					residual += error * error;
				}
				return sqrt(residual / (num_));
			}
			else
			{
				return FUTURE_MM_NAN;
			}
		}
	};
	Buffer _mBid,_mEdge,_mFair;
	int _rCount = 0;
	int _pauseCount = 0;
	bool _isQuoting = true;
	double _stdRange = 1.;			// 价格波动范围

	// position & PNL
	VolumeType _net = 0;			// 净持仓
	double _avg = 0.;				// 净持仓均价
	double _pnl = 0.;				// 净持仓浮动盈亏。已实现盈亏,锁仓算平仓,计入已实现
	double _stop = 0.;
	double _cumPnl = 0.;			// 累计盈亏
	double _realPnl = 0.;			// 实现盈亏
	//double _maxLoss = _loss;		// 初始亏损额度
	double _newPnl = 0.;			// 跟踪最大损失 ，用于回调止盈止损
	double _fair = 0;
	PriceType _bidP = 0;
	PriceType _askP = 0;
	double _spread = 0;
	int _lots = 1;
	int _stdEdge = 0;
	char _tradeFlag = 0;
	double _tradePrice = 0.0;

	MakerEventLogicChain _makerTradingStatus = MakerEventLogicChain::Ready;
	ReferEventLogicChain _referTradingStatus = ReferEventLogicChain::Ready;
	bool _isMakerChanging = false;
	bool _isReferChanging = false;

	PriceType _revBidP, _revAskP;
	VolumeType _revBidV, _revAskV;
	int _reverseLastPeriodsCount = 0;

	PriceType _tick = 0;
	PriceType _mu = 0;
	FutureLiquidity::LastPoint lastMaker, lastRefer;

	// P统计
	static const int _maxStatTicks = 6;
	std::array<int, _maxStatTicks> _obligationArray;
	int _obligationTicks = 0;
	chrono::steady_clock::time_point _obligationStartTime;
	std::atomic<bool> _useObligationStat = true;
	std::atomic<int> _maxObligationTicks = 5;

	// 持仓初始化
	bool _isAvgInitialized = true;
	bool initializeAvg();

	// 暂停参数
	std::atomic<int> _referLiquidityPauseTick = 6;
	std::atomic<int> _referLiquidityPausePeriod = 3;

	// 自动平仓
	std::atomic<bool> _useAutoCloseFlag = true;
	bool _isAutoCloseMode = false;
	std::atomic<int> _autoCloseSpreadTicks = 2;

	// 手动成交处理
	std::atomic<bool> _handleManualTrade = true; //TODO

	// 切片间连续处理
	bool _tradedInTick = false;
	bool _betweenTickFlag = false;

	//log精简参数
	int _canSendCount = 0;
	int _canHedgeCount = 0;
	bool _needHedge = true;
	bool _quoteNeedDelete = true;
	bool _askNeedDelete = true;
	bool _bidNeedDelete = true;
};
