# -*- coding: utf-8 -*-
"""
Created on Wed Aug 18 14:03:45 2021

@author: csctest
"""

from OmmDatabase import OmmDatabase
import datetime
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False    
# import re
import time
from enum import Enum

#%%
class order_status(Enum):
    pending_add = 0
    pending_delete = 1
    exchange_order = 2
    partial_traded = 3
    all_traded = 4
    deleted = 5
    over_flow = 8
    cancelled = 9

#%%
print(order_status.pending_add.value)
#%%
dfO = pd.read_csv('C:/Users/<USER>/Desktop/shfe_future_order.csv', encoding = 'utf-8')
# dfT = pd.read_csv('C:/Users/<USER>/Desktop/shfe_future_trade.csv', encoding = 'utf-8')

#%%
iid = 'rb2202'

dfO = dfO[dfO.insid == iid]
# dfT = dfT[dfT.insid == iid]

#%%
dfO = dfO[['internal_order_id', 'local_time', 'order_status']]

dfO.index = dfO['local_time']
dfO = dfO.sort_index()

dfO = dfO.to_dict('records')

#%%
def my_in(a, b):
    j = 0
    for i in b:
        if a == i[0]:
            result = (True, i)
            j = 1
    if j == 0:
        result = (False, None)
    return result
        

pre_order_alive = {}
pre_cancel_alive = {}
pre_order_delay = []
pre_cancel_delay = []

error_id = []

error_test = []



for i in dfO:
    if i['order_status'] == order_status.pending_add.value:
        order_id = i['internal_order_id']
        j = 0
        while True:
            if (order_id, j) in pre_order_alive.keys():
                j += 1
            else:                
                pre_order_alive[(order_id, j)] = i
                break
    if i['order_status'] == order_status.pending_delete.value:
        order_id = i['internal_order_id']
        j = 0
        while True:
            if (order_id, j) in pre_cancel_alive.keys():
                j += 1
            else:                
                pre_cancel_alive[(order_id, j)] = i
                break   
    if i['order_status'] in [order_status.exchange_order.value, order_status.over_flow.value]:
        order_id = i['internal_order_id']
        tf, key = my_in(order_id, pre_order_alive.keys())
        if tf:
            if i['order_status'] == order_status.exchange_order.value:
                t1 = pre_order_alive[key]['local_time']/10**9
                t2 = i['local_time']/10**9                                
                pre_order_delay.append((t1, t2-t1))            
            pre_order_alive.pop(key)
        else:
            error_id.append(order_id)
            error_test.append(i)
    if i['order_status'] in [order_status.deleted.value, order_status.cancelled.value]:
        order_id = i['internal_order_id']
        tf, key = my_in(order_id, pre_cancel_alive.keys())
        if tf:
            if i['order_status'] == order_status.deleted.value:
                t1 = pre_cancel_alive[key]['local_time']/10**9
                t2 = i['local_time']/10**9                                
                pre_cancel_delay.append((t1, t2-t1))           
            pre_cancel_alive.pop(key)
        else:
            error_id.append(order_id)
            error_test.append(i)
    if i['order_status'] in [order_status.all_traded.value]:
        pass

#%%
pre_order_delay = pd.DataFrame(pre_order_delay)
pre_cancel_delay = pd.DataFrame(pre_cancel_delay)

pre_order_delay.index = pre_order_delay[0]
pre_cancel_delay.index = pre_cancel_delay[0]

#%%
plt.figure()
plt.plot(pre_order_delay[1], label = 'order')
plt.plot(pre_cancel_delay[1], label = 'cancel')
plt.legend()
plt.show()
#%%
from datetime import timedelta
from influxdb import InfluxDBClient
# client = InfluxDBClient('192.168.203.11',8989,'','','testbase') #上期所prod
client = InfluxDBClient('10.17.88.168',9001,'','','testbase') #东坝
# client = InfluxDBClient('10.101.237.137',9090,'','','testbase') #东坝测试，带实时行情
# client = InfluxDBClient('202.0.3.209',8989,'','','testbase') #大商所
import datetime
beginStr = '2021-08-13T21:00:00.0Z'
endStr = '2021-08-16T15:00:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() * 1000000000

result1 = client.query("select * from shfe_future_order where time >= %d and time <= %d;"%(begin,end)) 
points1 = result1.get_points()
l=[]
for d in points1:
    l.append(d)

import pandas as pd    
df1 = pd.DataFrame(l)


#%%
iid_list = ['hc2111', 'hc2112', 'hc2202', 'hc2203', 'rb2111', 'rb2112', 'rb2202', 'rb2203']
json_body = []
for iid in iid_list:
    t = time.time()
    
    df_summary = df1[df1.insid == iid]
    df_summary['UnixStamp'] = df_summary['local_time'].copy()/10**9
    df_summary = df_summary.to_dict('records')
    
    result = []
    pre_order_alive = {}
    pre_cancel_alive = {}
    pre_order_delay = []
    pre_cancel_delay = []
    for i in df_summary:
        if i['order_status'] == order_status.pending_add.value:
            order_id = i['internal_order_id']
            if not order_id in pre_order_alive.keys():
                pre_order_alive[order_id] = i
        if i['order_status'] == order_status.pending_delete.value:
            order_id = i['internal_order_id']
            if not order_id in pre_cancel_alive.keys():        
                pre_cancel_alive[order_id] = i
        if i['order_status'] == order_status.deleted.value:
            order_id = i['internal_order_id']
            if order_id in pre_cancel_alive.keys():
                t1 = pre_cancel_alive[order_id]['UnixStamp']
                t2 = i['UnixStamp']
                pre_cancel_delay.append((t1, t2-t1, 0))
                pre_cancel_alive.pop(order_id)
        if i['order_status'] == order_status.exchange_order.value:
            order_id = i['internal_order_id']
            if order_id in pre_order_alive.keys():
                t1 = pre_order_alive[order_id]['UnixStamp']
                t2 = i['UnixStamp']
                pre_order_delay.append((t1, t2-t1, 0))
                pre_order_alive.pop(order_id)   
        if i['order_status'] in [order_status.partial_traded.value, order_status.all_traded.value]:
            order_id = i['internal_order_id']
            if order_id in pre_cancel_alive.keys():
                t1 = pre_cancel_alive[order_id]['UnixStamp']
                t2 = i['UnixStamp']
                pre_cancel_delay.append((t1, t2-t1, 1)) # 1代表撤单失败
                pre_cancel_alive.pop(order_id)
    result.append([iid, pre_order_delay, pre_order_alive, pre_cancel_delay, pre_cancel_alive])
    # print(iid + '已完成')
    
    pre_order_df = pd.DataFrame(pre_order_delay)
    pre_order_df.columns = ['time', 'delay', 'status']
    
    pre_cancel_df = pd.DataFrame(pre_cancel_delay)
    pre_cancel_df.columns = ['time', 'delay', 'status']
    
    deleted_failed = pre_cancel_df[(pre_cancel_df.delay>0.2)|(pre_cancel_df.status==1)]
    deleted_failed.index = deleted_failed.time
    
    
    result2 = client.query("select * from test10 where insid_md='"+ iid +"' and time >= %d and time <= %d;"%(begin,end)) 
    points2 = result2.get_points()
    l=[]
    for d in points2:
        l.append(d)
    df2 = pd.DataFrame(l)
    dfMkt = df2[df2['insid_md'] == iid]
    

    dfMkt['cancel_failed'] = [0 for i in range(dfMkt.shape[0])]
        
    dfMkt = dfMkt.to_dict('records')
    deleted_failed = deleted_failed.to_dict('records')
    
    # dfMkt = list(reversed(dfMkt))
    # deleted_failed = list(reversed(deleted_failed))
        
    i, j = 0, 0
    while True:
        t_delay = deleted_failed[i]['time']
        t_mkt = dfMkt[j]['local_t']/10**9
        if t_delay >= t_mkt:
            if t_delay - t_mkt <= 0.45:
                dfMkt[j]['cancel_failed'] = 1
            j += 1
            if j >= len(dfMkt):
                break   
        elif t_delay < t_mkt:
            i += 1
            if i >= len(deleted_failed):
                break
    
    dfMkt1 = pd.DataFrame(dfMkt)
             
    # dfMkt2 = dfMkt1[dfMkt1['cancel_failed'] == 1]
    
    dfMkt3 = dfMkt1.to_dict('records')
    
    
    
    for row in dfMkt3:
        
        current_time = int(row['local_t'])
        
        measurement = 'test11'
        
        a_p1 = row['a_p1']
        a_v1 = row['a_v1']
        a_p2 = row['a_p2']
        a_v2 = row['a_v2']
        a_p3 = row['a_p3']
        a_v3 = row['a_v3']      
        a_p4 = row['a_p4']
        a_v4 = row['a_v4']     
        a_p5 = row['a_p5']
        a_v5 = row['a_v5']    
        
        b_p1 = row['b_p1']
        b_v1 = row['b_v1']
        b_p2 = row['b_p2']
        b_v2 = row['b_v2']
        b_p3 = row['b_p3']
        b_v3 = row['b_v3']      
        b_p4 = row['b_p4']
        b_v4 = row['b_v4']     
        b_p5 = row['b_p5']
        b_v5 = row['b_v5']    
        
        exchange_t = row['exchange_t']    
        insid_md = row['insid_md'] 
        last_p = row['last_p'] 
        local_t = row['local_t'] 
        lower_limit_p = row['lower_limit_p'] 
        preclose_p = row['preclose_p'] 
        presettle_p = row['presettle_p'] 
        turnover = row['turnover'] 
        upper_limit_p = row['upper_limit_p'] 
        v = row['v'] 
        cancel_failed = row['cancel_failed']
           
        body = {
                "measurement": measurement, 
                "time": current_time, 
                "tags": {
                    "insid_md": insid_md
                }, 
                "fields": {
                    "a_p1": a_p1, 
                    "a_p2": a_p2,                 
                    "a_p3": a_p3,                 
                    "a_p4": a_p4,                 
                    "a_p5": a_p5,                 
                    "a_v1": a_v1,                 
                    "a_v2": a_v2,                    
                    "a_v3": a_v3,                    
                    "a_v4": a_v4,                    
                    "a_v5": a_v5,                    
                    "b_p1": b_p1, 
                    "b_p2": b_p2,                 
                    "b_p3": b_p3,                 
                    "b_p4": b_p4,                 
                    "b_p5": b_p5,                 
                    "b_v1": b_v1,                 
                    "b_v2": b_v2,                    
                    "b_v3": b_v3,                    
                    "b_v4": b_v4,                    
                    "b_v5": b_v5, 
                    "exchange_t": exchange_t,               
                    "last_p": last_p,
                    "local_t": local_t,
                    "lower_limit_p": lower_limit_p,
                    "preclose_p": preclose_p,
                    "presettle_p": presettle_p,
                    "turnover": turnover,
                    "upper_limit_p": upper_limit_p,
                    "v": v,
                    "cancel_failed": cancel_failed
                }, 
            }
        
        
        json_body.append(body)
    print(iid+'总时间:', time.time()-t)
#%%
client = InfluxDBClient('10.17.88.168',9001,'','','testbase')
t = time.time()

res = client.write_points(json_body[:], batch_size = 10000)
print(time.time()-t)
            
        














