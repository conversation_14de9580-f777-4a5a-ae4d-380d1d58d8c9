from ...factor_manager import factor_manager, Factor, FactorCategory
from ..basic_factors import calculate_spread, calculate_price_diff, calculate_imbalance
from ..market_microstructure_factors import calculate_midpx_imbalance
import numpy as np
import pandas as pd

# 计算复杂因子：价差与价格压力组合因子
def calculate_complex_spread_pressure_factor(data: pd.DataFrame) -> pd.Series:
    """
    计算复杂的价差与价格压力组合因子
    公式: shift(ma((ma(spread_3, 20)+(AskPrice4*(diff(bid_price_diff_max, 2)*sign((log(((AskVol2*-(std(bid_price_diff_3, 5)))*(diff(bid_price_diff_max, 2)+-(diff(shift(midpx2_midpx3_imb, 1))))))/shift(abs(imbalance_5), 1)))) * random(0.1, 1.0))), 20), 2)
    """
    if 'spread_3' not in data.columns:
        spread_3 = calculate_spread(data, 'Bid', 3)
    else:
        spread_3 = data['spread_3']
    
    if 'bid_price_diff_max' not in data.columns:
        bid_diff_max = calculate_price_diff(data, 'Bid', 5, 1)
    else:
        bid_diff_max = data['bid_price_diff_max']

    if 'bid_price_diff_3' not in data.columns:
        bid_diff_3 = calculate_price_diff(data, 'Bid', 3)
    else:
        bid_diff_3 = data['bid_price_diff_3']

    if 'imbalance_5' not in data.columns:
        imbalance_5 = calculate_imbalance(data, 5)
    else:
        imbalance_5 = data['imbalance_5']

    # 计算中间变量
    spread_3_ma20 = spread_3.rolling(window=20).mean()
    bid_diff_max_diff2 = bid_diff_max.diff(2)
    bid_diff_3_std5 = bid_diff_3.rolling(window=5).std()
    
    # 计算中间价不平衡度
    midpx2_midpx3_imb = calculate_midpx_imbalance(data, 2, 3)
    midpx2_midpx3_imb_shift1 = midpx2_midpx3_imb.shift(1)
    midpx2_midpx3_imb_shift1_diff = midpx2_midpx3_imb_shift1.diff()
    
    # 计算不平衡度绝对值的移位
    imb5_abs_shift1 = imbalance_5.abs().shift(1)
    
    # 计算内部复杂表达式
    inner_term1 = data['AskVol2'] * (-bid_diff_3_std5)
    inner_term2 = bid_diff_max_diff2 + (-midpx2_midpx3_imb_shift1_diff)
    inner_product = inner_term1 * inner_term2
    
    # 避免对负数取对数和除零
    log_term_input = inner_product.clip(1e-10)
    log_term = np.log(log_term_input)
    ratio_term = log_term / imb5_abs_shift1.replace(0, np.nan)
    
    # 计算符号和随机乘数
    sign_term = np.sign(ratio_term)
    random_factor = np.random.uniform(0.1, 1.0)
    
    # 组合最终表达式
    price_term = data['AskPrice4'] * (bid_diff_max_diff2 * sign_term) * random_factor
    combined_term = spread_3_ma20 + price_term
    ma_term = combined_term.rolling(window=20).mean()
    
    # 最终移位
    result = ma_term.shift(2)
    
    return result

# 注册复杂价差压力因子
factor_manager.register_factor(Factor(
    name="complex_spread_pressure_factor",
    category=FactorCategory.ADVANCED,
    description="复杂的价差与价格压力组合因子，结合了多层级价差、价格压力和不平衡度",
    calculation=calculate_complex_spread_pressure_factor,
    dependencies=[
        "spread_3", "AskPrice4", "bid_price_diff_max", "bid_price_diff_3", 
        "AskVol2", "mid_price_level_2", "mid_price_level_3", "imbalance_5"
    ],
    source="deap_algorithm"
))
