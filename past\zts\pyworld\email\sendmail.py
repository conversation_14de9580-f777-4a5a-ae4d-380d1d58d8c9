import os
import shutil
from datetime import datetime
import sys

# codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
sys.path.append(codepath)
from MMaker import myemail


def sendmail(receivers0, subject0, content0, filepath0):
    usr = '<EMAIL>'
    psw = 'cwffwmegabqcbjed'
    name = u""
    smtpSvr = 'smtp.qq.com'

    mm = myemail.MailSender(smtpSvr, 465, usr, psw, name)
    mm.login()
    if filepath0 != '':
        mm.add_attachment(filepath0)
    # res = False
    # for i in range(3):
    res = mm.send(subject0, content0, receivers0)
    if res:
        print(datetime.now(), ':success')
    mm.close()
