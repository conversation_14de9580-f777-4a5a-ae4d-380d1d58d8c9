from PySide6.QtCore import  Signal, QThread
import time


class DataUpdateThread(QThread):
    data_updated = Signal()  # 自定义信号，用于通知数据更新

    def __init__(self, data_manager, live_mode):
        super().__init__()
        self.data_manager = data_manager
        self.live_mode = live_mode
        self.running = True

    def run(self):
        """在单独的线程中每秒更新数据"""
        while self.running:
            if self.live_mode:
                self.data_manager.load_and_process_data(None, live_mode=True)
                # 数据存进influxdb
                # try:
                #     self.data_manager.save_to_influxdb(self.data_manager.current_data, 'current_data',
                #                                        taglist=['exp', 'K'], fieldlist=[], timelist=['time'])
                #     self.data_manager.save_to_influxdb(self.data_manager.voltime_data, 'svis', taglist=['index'],
                #                                        fieldlist=[], timelist=[])
                # except Exception as e:
                #     print(f"Error saving to InfluxDB: {e}")
                self.data_updated.emit()  # 发出数据更新信号
            time.sleep(1)  # 每秒执行一次

    def stop(self):
        """停止线程"""
        self.running = False
        self.quit()
        self.wait()  # 等待线程结束