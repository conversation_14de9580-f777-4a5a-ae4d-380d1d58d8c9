import os
import re
import sys

from db_solve.utility import dttype
from pnlback.signal.signals import *


def readmd(str5, codeslist, datetoday, tradetime00, Source=None,coltype='sim',
           mid=0.5, last=0.5, minnum=6, mult=200, cal_sig=True,
           sig=None):
    if sig is None:
        sig = ['']
    print('load md ' + str5)
    md_reader = pd.read_csv(str5 % datetoday, names=None, header=0, chunksize=500000, low_memory=False)
    size = 1
    data_md = pd.DataFrame()
    for chunk in md_reader:
        data_md = pd.concat([data_md, chunk])
        size += 1
        print(size)
    data_md['timestamp_str'] = pd.to_datetime(datetoday + ' ' + data_md['timestamp_str'], format='%Y%m%d %H:%M:%S.%f')
    data_md.index = data_md['timestamp_str']
    data_md = pd.concat([data_md.between_time(tradetime00[0][0], tradetime00[0][1]),
                         data_md.between_time(tradetime00[1][0], tradetime00[1][1])])
    data_md['Symbol'] = data_md['Symbol'].astype(str)
    if Source:
        data_md = data_md[data_md['Source'] == Source]
    # if codeslist[0] is None:
    #     print("none")
    # elif not codeslist[0] in (data_md['Symbol'].unique()):
    #     print('no such symbol')
    #     exit()
    print('DataSymbol ', data_md['Symbol'].unique(), 'Symbol ', codeslist)
    data_md['mid'] = data_md['AskPrice1'] / 2 + data_md['BidPrice1'] / 2
    data_md['mixfut'] = (data_md['mid'] * mid + data_md['LastPrice'] * last)
    data_md['edge'] = (data_md['AskPrice1'] - data_md['BidPrice1']) / 2

    if codeslist:
        data_md = data_md[data_md['Symbol'].isin(codeslist)].drop_duplicates(subset=['timestamp_str', 'Symbol'])
        data_md['tradedVol'] = data_md['Volume'].diff(1)
        data_md['tradedValue'] = data_md['TotalValueTraded'].diff(1)
    else:
        # 多个symbol
        data_md = pd.merge_asof(data_md,
                                data_md[['timestamp_str', 'Symbol', 'TotalValueTraded', 'Volume']].add_suffix(
                                    '_before'),
                                left_index=True, right_index=True, direction='backward',
                                left_by='Symbol', right_by='Symbol_before',
                                tolerance=pd.Timedelta('1020ms'), allow_exact_matches=False)
        data_md['tradedVol'] = data_md['Volume'] - data_md['Volume_before']
        data_md['tradedValue'] = data_md['TotalValueTraded'] - data_md['TotalValueTraded_before']

    data_md['avg_prc'] = (data_md['tradedValue'] / data_md['tradedVol'] / mult).round(5)

    addcol = ['mid', 'mixfut', 'edge', 'tradedVol', 'avg_prc']

    data_md, addcol2 = Signal(data_md, minnum=minnum, sig=sig, cal_sig=cal_sig).sigall()

    addcol = addcol + addcol2

    print(data_md[addcol2].corr().round(2))

    if coltype == 'all':
        data_md = data_md[[
                              'Symbol', 'timestamp_str', 'Volume',
                              'BidPrice5', 'BidPrice4', 'BidPrice3', 'BidPrice2', 'BidPrice1',
                              'AskPrice1', 'AskPrice2', 'AskPrice3', 'AskPrice4', 'AskPrice5',
                              'BidVol5', 'BidVol4', 'BidVol3', 'BidVol2', 'BidVol1',
                              'AskVol1', 'AskVol2', 'AskVol3', 'AskVol4', 'AskVol5',
                              'LastPrice', 'TotalValueTraded', 'ForQuoteSysID', 'Source', 'State',
                          ] + addcol]
    else:
        data_md = data_md[[
                              'Symbol', 'timestamp_str', 'Volume',
                              'BidPrice2', 'BidPrice1',
                              'AskPrice1', 'AskPrice2',
                              'BidVol2', 'BidVol1',
                              'AskVol1', 'AskVol2',
                              'LastPrice', 'TotalValueTraded', 'ForQuoteSysID', 'Source',
                          ] + addcol]
    print(str5 + ' md done')
    data_md = data_md.sort_index()

    if codeslist:
        data_md['dsmid'] = data_md['mid'].diff(1)
        data_md['dsema'] = np.round(data_md['dsmid'].ewm(alpha=0.1).mean(), 2)

    return data_md.dropna(axis=1, how='all')


def load_orders(path, codes, date, time_range):
    print('load orders')
    md_reader = pd.read_csv(path % date, names=None, header=0, chunksize=1000000, on_bad_lines='skip',
                            low_memory=False)
    size = 1
    order_data = pd.DataFrame()
    for chunk in md_reader:
        order_data = pd.concat([order_data, chunk])
        size += 1
        print(size)
        # if size == 3:
        #     print('break loadorders')
        #     break

    order_data = order_data.rename(columns={'symbol': 'Symbol'})
    order_data['Symbol'] = order_data['Symbol'].astype(str).apply(lambda x: re.sub(r'\.[\d]+', '', x))

    if codes:
        order_data = order_data[order_data['Symbol'].isin(codes)]
        # # 对齐交易所和柜台的时间戳
        # order_data = order_data.sort_values(u'updateTime')
        # order_data = (
        #     pd.merge_asof(
        #         order_data[(order_data['rspSrc'] == 2)],
        #         order_data[(order_data['rspSrc'] == 1)]
        #         .rename(columns={'updateTime': 'updateTimeEX'})[['updateTimeEX', 'OrderId']],
        #         left_on='updateTime', right_on='updateTimeEX', by='OrderId',
        #         direction='nearest',
        #         tolerance=pd.Timedelta('10000ms'), allow_exact_matches=True))
        order_data.to_csv(os.path.dirname(path) + u'\\N%s_OrderDetails.csv' % date)
        print('save orders csv ' + path)

    order_data['updateTime'] = pd.to_datetime(
        date + ' ' + order_data['updateTime'].str.replace('.', ':', regex=False).str[:15],
        format='%Y%m%d %H:%M:%S:%f')
    order_data['insertTime'] = pd.to_datetime(
        date + ' ' + order_data['insertTime'].str.replace('.', ':', regex=False).str[:15],
        format='%Y%m%d %H:%M:%S:%f')

    order_data = order_data.sort_values(u'updateTime')

    order_data = order_data.set_index('updateTime', drop=False)

    order_data = order_data.sort_index()
    order_data = pd.concat([order_data.between_time(time_range[0][0], time_range[0][1]),
                            order_data.between_time(time_range[1][0], time_range[1][1])])

    print('orders done')

    return order_data


def mixorders(optorders, longdir, shortdir):
    md121 = optorders[(optorders['stateStr'] == 'Accepted')]
    md122 = optorders[(optorders['stateStr'] == 'FullCanceled')]

    mdlong121 = md121[md121['directionStr'].isin(longdir)]
    mdshort121 = md121[md121['directionStr'].isin(shortdir)]

    ordersac = pd.merge_asof(mdlong121[['insertTime', 'price', 'Symbol', 'volume']],
                             mdshort121[['insertTime', 'price', 'Symbol', 'volume']],
                             on='updateTime', direction='nearest', by='Symbol',
                             tolerance=pd.Timedelta('20ms'), allow_exact_matches=True, suffixes=('_bid', '_ask'))

    ordersac = ordersac.set_index('updateTime', drop=False)
    print('md12 done')
    ordersac = ordersac.sort_index()

    return ordersac


def try_encodings(file_path):
    encodings = ['utf-8', 'utf-16', 'gbk', 'gb2312', 'latin1']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                f.read(1000)
            print(f"文件以 {encoding} 编码成功读取。")
            return encoding
        except UnicodeDecodeError:
            pass  # 忽略UnicodeDecodeError，继续尝试下一个编码
    print("无法用任何提供的编码格式读取文件。")
    return None


def loadtrade(datetoday, str3, tradetime00, under=None, s_mult=1000, multi=10000, multi2=10000):
    print('load trade' + str3)
    encoding = try_encodings(str3 % datetoday)
    trade_data = pd.read_csv(str3 % datetoday, index_col=2, names=None, header=0, low_memory=False,
                             encoding=encoding, sep='\t')
    if under == 'SZ500':
        trade_data = trade_data[trade_data[u'Strike'] > 5]
    elif under == 'SZ300':
        trade_data = trade_data[trade_data[u'Strike'] < 5]
    print('done load trade')
    # expTrade = tradeData['Expiry'].unique()
    trade_data.index = pd.to_datetime(datetoday + ' ' + trade_data.index, format='%Y%m%d %H:%M:%S:%f')
    trade_data = pd.concat(
        [trade_data.between_time(tradetime00[0][0], tradetime00[0][1]),
         trade_data.between_time(tradetime00[1][0], tradetime00[1][1])]).fillna(0)
    trade_data[u'委托时间'] = pd.to_datetime(datetoday + ' ' + trade_data[u'委托时间'], format='%Y%m%d %H:%M:%S:%f')
    trade_data = trade_data.reset_index()
    trade_data = trade_data[trade_data[u'策略'] == 8]
    trade_data.rename(columns={u'成交时间': 'tradetime'}, inplace=True)
    trade_data.rename(columns={u'委托时间': 'ordertime'}, inplace=True)
    trade_data = trade_data.set_index('tradetime')
    trade_data = trade_data.sort_index().dropna(how='all')
    trade_data[u'multi'] = trade_data['Strike'].apply(lambda x: multi if x * s_mult % 10 == 0 else multi2)
    trade_data['absnum'] = abs(trade_data['数量'])

    trade_data[u'Edge'] = (trade_data['AdjTv'] - trade_data[u'价格']) * trade_data[u'数量'] / trade_data['absnum']
    trade_data['edgepnl'] = (
            (trade_data['AdjTv'] - trade_data[u'价格']) * trade_data[u'数量'] * trade_data[u'multi']).round(1)
    trade_data['REdge_mult'] = (trade_data[u'Edge'] / trade_data['RawEdge']).round(1)
    trade_data['dSpot-1'] = (trade_data['Spot'].diff(1) * s_mult).round(2)
    trade_data['dSpot1'] = -(trade_data['Spot'].diff(-1) * s_mult).round(2)
    trade_data['absdelta'] = abs(trade_data['Delta'])
    trade_data['per_delta'] = (
            trade_data[u'Delta'] / trade_data[u'Spot'] / trade_data[u'multi'] / trade_data[u'数量']).round(2)
    trade_data['delta_type'] = abs(trade_data['per_delta']).round(1)
    trade_data['Tv'] = trade_data['Tv'].round(5)
    trade_data['AdjTv'] = trade_data['AdjTv'].round(5)
    trade_data['dt_trade_1'] = trade_data.index
    trade_data['dttrd_ord'] = trade_data.apply(
        lambda x: -1 if x[u'类型'] == 2008
        else (x['dt_trade_1'] - x[u'ordertime']) / np.timedelta64(1, 's')
        , axis=1)
    trade_data['dt_trade_1'] = trade_data['dt_trade_1'].diff(1) / np.timedelta64(1, 's')
    trade_data = dttype(trade_data, 'dttrd_ord', 'dttrd_ord_type')
    # -------------------------------------------------------------------------------------------------------------
    print('done trade mix')
    return trade_data


def loadvol(volstr, optcodes, tradetime00, all=False):
    voldata = pd.read_csv(volstr, parse_dates=None, index_col=None,
                          names=['time', 'spot', 'code', 'tv', 'bid', 'ask', 'sigma', 'a.1', 'a.2', 'a.3', 'a.4', 'a.5',
                                 'delta', 'vega', 'a.6', 'a.7', 'rf', 'br', 'time2expiry', 'K', 'Z', 'Call', 'exp',
                                 'a.8',
                                 'a.9', 'a.10', 'a.11', 'a.12'], header=None)
    if not all:
        voldata = voldata[voldata['code'].astype(str).isin(optcodes)]
    voldata = voldata[~(voldata.duplicated(subset=['exp', 'time', 'code']))]
    voldata['time'] = pd.to_datetime(voldata['time'] + '.000', format='%Y-%m-%d %H:%M:%S.%f')
    voldata = voldata.set_index('time', drop=True)

    voldata = voldata.drop(
        ['a.1', 'a.2', 'a.3', 'a.4', 'a.5', 'a.6', 'a.7', 'Call', 'a.8', 'a.9',
         'Z', 'Call', 'a.10', 'a.11', 'a.12'], axis=1)
    voldata = pd.concat([voldata.between_time(tradetime00[0][0], tradetime00[0][1]),
                         voldata.between_time(tradetime00[1][0], tradetime00[1][1])])
    print('done load vol')
    return voldata


if __name__ == '__main__':
    datetoday = '20240904'

    under = 'SH500'
    fut = 'IC2409'

    tradetime00 = [['09:30:00', '11:30:00'], ['13:00:00', '14:55:00']]

    cwd = 'G:\\'
    dirs = cwd + u'DATA\\' + under + '\\'
    str3 = dirs + u'md_%s_cffex_multicast.csv'
    str4 = dirs + u'%s_OrderDetails.csv'
    str5 = dirs + u'md_%s_sse_mdgw.csv'
    str_vol = dirs + 'vols_%s.csv'

    s_mult = 1000

    longdir = ['OpenLong', 'CloseShort']
    shortdir = ['OpenShort', 'CloseLong']

    optcodes = ['10007701']

    futs = 0
    opts = 1
    ords = 1
    vols = 0

    if futs == 1:
        fut1_data = readmd(str3, [fut, ], datetoday, tradetime00, coltype='all', minnum=6, cal_sig=True)
        # futdata = futdata[futdata['Source'] == 4]
        fut1_data['dsvol'] = fut1_data['Volume'].diff(1)
        fut1_data['dsstate'] = fut1_data['State'].diff(1)
        fut1_data['dropcol'] = abs(fut1_data['dsmid']) + abs(fut1_data['dsvol'])
        # futdata = drop_col_nan(futdata, 'dropcol')
        fut1_data['avg_prc'] = fut1_data['avg_prc'].round(2) - fut1_data['mid']
        fut1_data['dsavg'] = fut1_data['avg_prc'].diff(1)
        fut1_data['LastPrice'] = fut1_data['LastPrice'] - fut1_data['mid']
        fut1_data['dmidminum'] = fut1_data['mid_minnum'].diff(1)
        fut1_data['emamin'] = np.round(fut1_data['dmidminum'].ewm(alpha=0.5).mean(), 2)
        fut1_data.to_csv(dirs + u'%s' % ('out_' + datetoday + '_futs.csv'))
        #
        # mdData = readmd(str3, [fut, ], datetoday, tradetime00, coltype='all')
        # mdData.to_csv(u'%s' % ('N' + u'out_%s_futmkt.csv' % datetoday))

    if opts == 1:
        optmktdata = readmd(str5, optcodes, datetoday, tradetime00, mult=10000, coltype='all', cal_sig=False)
        optmktdata.to_csv(dirs + u'%s' % ('out_' + datetoday + '_optmkt.csv'))
        #
        # mdData = readmd(str3, [fut, ], datetoday, tradetime00, coltype='all')
        # mdData.to_csv(u'%s' % ('N' + u'out_%s_futmkt.csv' % datetoday))
    if ords == 1:
        ordersdata = load_orders(str4, optcodes, datetoday, tradetime00)

        # ordersdata = ordersdata[(ordersdata['stateStr'] == 'BeforSending') | (ordersdata['stateStr'] == 'BeforCanceling')]
        # ordersdata1 = ordersdata[ordersdata['orderProperty'].astype(str) == '-1']
        # ordersdata2 = ordersdata[ordersdata['orderProperty'].astype(str) == '87']

        ordersdata.to_csv(dirs + u'%s' % ('out_' + under + datetoday + '_orders.csv'))
        # ordersdata1.to_csv(dirs + u'%s' % ('out_' + under + datetoday + '_orders1.csv'))
        # ordersdata2.to_csv(dirs + u'%s' % ('out_' + under + datetoday + '_orders2.csv'))

        # from pnlback.csvreader.datamaker import mixorders

        # mdmix1, mdmix2 = mixorders(ordersdata, optmktdata, longdir, shortdir)
        # mdmix1.to_csv(u'%s' % ('out' + datetoday + 'orders_MERGE.csv'))
        # mdmix2.to_csv(u'%s' % ('out' + datetoday + 'orders_MERGE2.csv'))

    if vols == 1 and os.path.exists(str_vol % datetoday):
        print('load vol')
        md_data2 = loadvol(str_vol % datetoday, optcodes, tradetime00, all=False)

        md_data2['forward'] = md_data2['spot'] * np.exp(
            md_data2.rf * md_data2.time2expiry - md_data2['br'] * md_data2['time2expiry'])
        md_data2['basis'] = md_data2['forward'] - md_data2['spot']
        md_data2['mid'] = md_data2['bid'] / 2 + md_data2['ask'] / 2

        md_data2['dsspot'] = md_data2['spot'].diff(1).round(int(math.log10(s_mult)) + 2)
        md_data2['dsmid'] = md_data2['mid'].diff(1).round(int(math.log10(s_mult)) + 2)
        md_data2['dsbasis'] = md_data2['basis'].diff(1).round(int(math.log10(s_mult)) + 2)
        md_data2['dsbasis2'] = (md_data2['dsmid'] / md_data2['delta'] - md_data2['dsspot']).round(
            int(math.log10(s_mult)) + 2)

        md_data2['s'] = (md_data2['spot'].diff(1) * md_data2['delta']).round(int(math.log10(s_mult)) + 2)
        md_data2['b'] = (md_data2['basis'].diff(1) * md_data2['delta']).round(int(math.log10(s_mult)) + 2)
        md_data2['v'] = (md_data2['sigma'].diff(1) * md_data2['vega'] * 100).round(int(math.log10(s_mult)) + 2)
        md_data2['tv'] = (md_data2['tv'].diff(1)).round(int(math.log10(s_mult)) + 2)

        md_data2.to_csv(dirs + u'%s' % ('out_' + under + datetoday + '_vols.csv'))

    print('ALL done')
    sys.exit()
