"""
基于 DEAP 的遗传算法因子生成器
@author: lining
"""
import numpy as np
import pandas as pd
from typing import List, Tuple, Optional
import random
import sys
import os
import copy
import re
from functools import partial
from deap import creator, base, tools, gp
from deap import algorithms
from scipy import stats
from scipy.stats import linregress
sys.path.insert(0, sys.path[0]+"/../../../")
from core import config
from data.features.factor_manager import Factor, FactorCategory, factor_manager

# 定义返回类型
RET_TYPE = pd.Series

# 创建适应度类（多目标优化）
creator.create("FitnessMulti", base.Fitness, weights=(1.0, 1.0, 1.0))  # IC、Rank IC和胜率
creator.create("Individual", gp.PrimitiveTree, fitness=creator.FitnessMulti)

def safe_operation(operation_func, *args, **kwargs):
    """通用的安全操作包装器
    
    Args:
        operation_func: 要执行的操作函数
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        处理后的结果，确保没有无效值
    """
    try:
        # 处理输入数据中的无效值
        processed_args = []
        for arg in args:
            if isinstance(arg, pd.Series):
                # 处理无穷大和NaN
                processed_arg = arg.replace([np.inf, -np.inf], np.nan)
                # 对极端值进行裁剪
                processed_arg = processed_arg.clip(-1e10, 1e10)
                processed_args.append(processed_arg)
            else:
                processed_args.append(arg)
        
        # 执行操作
        result = operation_func(*processed_args, **kwargs)
        
        # 处理结果中的无效值
        if isinstance(result, pd.Series):
            # 处理无穷大和NaN
            result = result.replace([np.inf, -np.inf], np.nan)
            # 对极端值进行裁剪
            result = result.clip(-1e10, 1e10)
            # 填充NaN
            result = result.fillna(0)
        return result
    except Exception as e:
        print(f"操作执行错误: {str(e)}")
        return pd.Series(0, index=args[0].index if args and isinstance(args[0], pd.Series) else None)

def safe_linregress(x, y):
    """安全的线性回归计算
    
    Args:
        x: 自变量
        y: 因变量
        
    Returns:
        回归残差
    """
    try:
        # 检查x是否全部相同
        if x.nunique() <= 1:
            return pd.Series(0, index=x.index)
        
        # 检查是否有足够的非NaN值
        valid_mask = ~(x.isna() | y.isna())
        if valid_mask.sum() < 2:  # 至少需要2个点才能进行线性回归
            return pd.Series(0, index=x.index)
            
        # 执行线性回归
        slope, intercept, r_value, p_value, std_err = linregress(x[valid_mask], y[valid_mask])
        return y - (intercept + slope * x)
    except Exception as e:
        print(f"线性回归计算错误: {str(e)}")
        return pd.Series(0, index=x.index)

# 随机乘数生成 - 使用更安全的范围
nums = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]

def init_pset():
    """初始化基本操作集"""
    pset = gp.PrimitiveSetTyped("MAIN", [], RET_TYPE)

    # 随机乘数生成
    pset.addPrimitive(lambda a: safe_operation(lambda x: x * 0.5, a), [RET_TYPE], RET_TYPE, name="half_mul")

    # 基础运算符 - 使用安全包装器
    pset.addPrimitive(lambda a, b: safe_operation(lambda x, y: x + y, a, b), [RET_TYPE, RET_TYPE], RET_TYPE, name="add")
    pset.addPrimitive(lambda a, b: safe_operation(lambda x, y: x - y, a, b), [RET_TYPE, RET_TYPE], RET_TYPE, name="sub")
    pset.addPrimitive(lambda a, b: safe_operation(lambda x, y: x * y, a, b), [RET_TYPE, RET_TYPE], RET_TYPE, name="mul")
    def div(x, y):
        # 安全除法：除数接近零时返回1
        mask = np.abs(y) > 1e-10
        result = np.ones_like(x.values, dtype=float)
        result[mask] = x.values[mask] / y.values[mask]
        return pd.Series(result, index=x.index)
    pset.addPrimitive(lambda a, b: safe_operation(div, a, b), [RET_TYPE, RET_TYPE], RET_TYPE, name="div")
    pset.addPrimitive(lambda a: safe_operation(lambda x: -x, a), [RET_TYPE], RET_TYPE, name="neg")

    # 数学运算 - 使用安全包装器
    pset.addPrimitive(lambda a: safe_operation(lambda x: x.abs(), a), [RET_TYPE], RET_TYPE, name="abs")
    def log(x):
        # 确保输入值为正数
        x=np.abs(x)
        mask = x.values > 1e-10
        result = np.ones_like(x.values, dtype=float)
        result[mask] = np.log(x.values[mask])
        return pd.Series(result, index=x.index)
    pset.addPrimitive(lambda a: safe_operation(log, a), [RET_TYPE], RET_TYPE, name="log")
    def sqrt(x):
        # 确保输入为正数
        x = np.abs(x)
        mask = x.values > 1e-10
        result = np.ones_like(x.values, dtype=float)
        result[mask] = np.sqrt(x.values[mask])
        return pd.Series(result, index=x.index)
    pset.addPrimitive(lambda a: safe_operation(sqrt, a), [RET_TYPE], RET_TYPE, name="sqrt")
    
    # 三角函数 - 使用安全包装器
    pset.addPrimitive(lambda a: safe_operation(lambda x: np.sin(x), a), [RET_TYPE], RET_TYPE, name="sin")
    pset.addPrimitive(lambda a: safe_operation(lambda x: np.cos(x), a), [RET_TYPE], RET_TYPE, name="cos")
    pset.addPrimitive(lambda a: safe_operation(lambda x: np.tan(x), a), [RET_TYPE], RET_TYPE, name="tan")
    
    # 条件判断操作 - 使用安全包装器
    pset.addPrimitive(lambda a: safe_operation(lambda x: np.sign(x), a), [RET_TYPE], RET_TYPE, name="sign")

    # # 新增时间序列特征
    # for window in [5, 10, 20]:
    #     # 使用更合适的min_periods值，确保有足够的数据点
    #     min_periods = max(2, window // 2)  # 至少需要窗口大小的一半的有效数据点
        
    #     # 更新所有滚动窗口操作
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).sum().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_sum_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).std().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_stddev_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).max().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_max_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).min().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_min_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).rank().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_rank_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).skew().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_skewness_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).kurt().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_kurtosis_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.pct_change(window, fill_method=None).fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_return_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: ((x - x.rolling(**kwargs).mean()) / x.rolling(**kwargs).std().replace(0, np.nan).clip(1e-10, 1e10)).fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ts_zscore_{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).mean().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"ma{window}")
    #     pset.addPrimitive(lambda a: safe_operation(lambda x, **kwargs: x.rolling(**kwargs).std().fillna(0), a, window=window, min_periods=min_periods), [RET_TYPE], RET_TYPE, name=f"std{window}")
    #     pset.addPrimitive(lambda a, b: safe_operation(lambda x, y, **kwargs: x.rolling(**kwargs).corr(y).fillna(0), a, b, window=window, min_periods=min_periods), [RET_TYPE, RET_TYPE], RET_TYPE, name=f"ts_corr_{window}")
    #     pset.addPrimitive(lambda a, b: safe_operation(lambda x, y, **kwargs: x.rolling(**kwargs).cov(y).fillna(0), a, b, window=window, min_periods=min_periods), [RET_TYPE, RET_TYPE], RET_TYPE, name=f"ts_cov_{window}")
    
    # # 时间序列特征 - 使用安全包装器
    # pset.addPrimitive(lambda a: safe_operation(lambda x: x.diff(), a), [RET_TYPE], RET_TYPE, name="diff")
    # pset.addPrimitive(lambda a: safe_operation(lambda x: x.diff(2), a), [RET_TYPE], RET_TYPE, name="diff2")
    
    # # 统计特征 - 使用scipy.stats中的现成函数
    # pset.addPrimitive(lambda a: safe_operation(lambda x: pd.Series(stats.mstats.winsorize(x.clip(-1e10, 1e10), limits=[0.01, 0.01]), index=x.index), a), [RET_TYPE], RET_TYPE, name="winsorize")
    # pset.addPrimitive(lambda a: safe_operation(lambda x: pd.Series(stats.zscore(x.clip(-1e10, 1e10), nan_policy='omit'), index=x.index).fillna(0), a), [RET_TYPE], RET_TYPE, name="zscore")
    
    # # 回归残差计算 - 使用安全包装器
    # pset.addPrimitive(lambda a, b: safe_operation(lambda x, y: safe_linregress(x, y), a, b), [RET_TYPE, RET_TYPE], RET_TYPE, name="regress_resid")

    # def _non_linear(x1):
    #     x1 = safe_operation(lambda x: pd.Series(stats.zscore(x.clip(-1e10, 1e10), nan_policy='omit'), index=x.index).fillna(0), x1) + 1
    #     nl = safe_operation(lambda x, y: safe_linregress(x, y), x1 ** 3, x1)
    #     return nl
    # pset.addPrimitive(lambda a: safe_operation(lambda x: _non_linear(x), a), [RET_TYPE], RET_TYPE, name="non_linear")
    
    # # 信号处理 - 使用安全包装器
    # pset.addPrimitive(lambda a: safe_operation(lambda x: 1 / (1 + np.exp(-x)), a), [RET_TYPE], RET_TYPE, name="sigmoid")
    # pset.addPrimitive(lambda a, power=2: safe_operation(lambda x, power=power: np.sign(x) * np.power(np.abs(x), power), a, power=power), [RET_TYPE, RET_TYPE], RET_TYPE, name="signedpower")
    
    return pset

def convert_inverse_prim(prim, args):
    """转换逆操作
    
    Args:
        prim: 原始操作
        args: 操作参数
        
    Returns:
        str: 格式化的表达式字符串
    """
    prim = copy.copy(prim)
    
    # 基础运算符转换
    basic_ops = {
        'add': lambda *args_: "({}+{})".format(*args_),
        'sub': lambda *args_: "({}-{})".format(*args_),
        'mul': lambda *args_: "({}*{})".format(*args_),
        'div': lambda *args_: "({}/{})".format(*args_),
        'neg': lambda *args_: "-({})".format(*args_),
    }
    
    # 数学运算转换
    math_ops = {
        'abs': lambda *args_: "abs({})".format(*args_),
        'log': lambda *args_: "log({})".format(*args_),
        'exp': lambda *args_: "exp({})".format(*args_),
        'sqrt': lambda *args_: "sqrt({})".format(*args_),
        'sin': lambda *args_: "sin({})".format(*args_),
        'cos': lambda *args_: "cos({})".format(*args_),
        'tan': lambda *args_: "tan({})".format(*args_),
    }
    
    # 随机乘数转换
    random_ops = {}
    for num in nums:    
        random_ops[f"half_mul_{num}"] = lambda *args_: f"{num} * {args_[0]}"
    
    random_ops = {
        'half_mul': lambda *args_: "0.5 * {}".format(*args_),
    }
    
    
    # 时间序列特征转换
    timeseries_ops = {
        'diff': lambda *args_: "diff({})".format(*args_),
        'diff2': lambda *args_: "diff({}, 2)".format(*args_),
    }
    
    # 合并所有操作字典
    all_ops = {
        **basic_ops,
        **math_ops,
        **timeseries_ops,
        **random_ops,

    }
    
    # 获取对应的格式化函数
    prim_formatter = all_ops.get(prim.name)
    
    if prim_formatter is None:
        # 如果没有找到对应的格式化函数，使用默认的format方法
        return prim.format(*args)
    
    try:
        # 尝试使用对应的格式化函数
        return prim_formatter(*args)
    except Exception as e:
        # 如果格式化失败，使用默认的format方法
        print(f"Warning: Failed to format {prim.name} with custom formatter: {str(e)}")
        return prim.format(*args)

def stringify_for_sympy(f):
    """将表达式转换为可读字符串"""
    string = ""
    stack = []
    for node in f:
        stack.append((node, []))
        while len(stack[-1][1]) == stack[-1][0].arity:
            prim, args = stack.pop()
            string = convert_inverse_prim(prim, args)
            if len(stack) == 0:
                break
            stack[-1][1].append(string)
    return string

class DeapFactorGenerator:
    """基于 DEAP 的遗传算法因子生成器"""
    outdir = config.OUTDIR+'/features/auto_gen/'
    if not os.path.exists(outdir):
        os.makedirs(outdir)
    def __init__(self, data: pd.DataFrame, target_cols: List[str], factor_manager):
        self.data = data
        self.target_cols = target_cols
        self.factor_manager = factor_manager
        self.available_columns = [col for col in data.columns if col not in target_cols]
        self.toolbox = base.Toolbox()
        
        # 初始化基本操作集
        self.pset = init_pset()
        
        # 添加数据列作为终端
        for col in self.available_columns:
            self.pset.addTerminal(self.data[col], RET_TYPE, name=col)
        
        # 注册遗传算法操作
        self._setup_toolbox()
        
    def _setup_toolbox(self):
        """设置遗传算法工具箱"""
        # 注册个体生成函数 - 为高频数据优化树的深度和结构
        self.toolbox.register("expr", gp.genHalfAndHalf, pset=self.pset, min_=2, max_=6)  # 减小初始树深度，适合高频数据的简洁表达式
        self.toolbox.register("individual", tools.initIterate, creator.Individual, self.toolbox.expr)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # 注册遗传操作
        self.toolbox.register("evaluate", self.evaluate_fitness)
        # 使用NSGA-III进行多目标优化，更适合高维目标空间
        # 动态生成参考点，提高搜索效率和多样性
        ref_points = tools.uniform_reference_points(nobj=3, p=12)  # 增加参考点数量和分布密度
        self.toolbox.register("select", tools.selNSGA3, ref_points=ref_points, nd='log')  # 使用对数时间复杂度的非支配排序
        # 使用gp.cxOnePoint替代tools.cxUniform，因为cxUniform会导致不同arity节点的交换错误
        self.toolbox.register("mate", gp.cxOnePoint)  # 使用单点交叉，适用于树结构
        
        # 注册突变表达式生成器，控制生成树的深度范围
        self.toolbox.register("expr_mut", gp.genGrow, min_=0, max_=2)  # 使用genGrow生成更多样化的树结构
        self.toolbox.register("mutate", gp.mutNodeReplacement, pset=self.pset)  # 节点替换突变，保持表达式结构稳定性
        
        # 添加高频数据特有的突变操作
        self.toolbox.register("mutate_ephemeral", gp.mutEphemeral, mode="one")  # 对常量进行微调
        self.toolbox.register("mutate_insert", gp.mutInsert, pset=self.pset)  # 插入新节点，增加表达式复杂度
        
        # 限制树的高度和大小，避免过拟合和计算复杂度过高
        import operator
        self.toolbox.decorate("mate", gp.staticLimit(key=operator.attrgetter("height"), max_value=12))  # 减小最大高度
        self.toolbox.decorate("mutate", gp.staticLimit(key=operator.attrgetter("height"), max_value=12))
        self.toolbox.decorate("mutate", gp.staticLimit(key=len, max_value=30))  # 限制节点数量，提高计算效率
        self.toolbox.decorate("mate", gp.staticLimit(key=len, max_value=30))
        
    def evaluate_fitness(self, individual: gp.PrimitiveTree) -> Tuple[float, float, float]:
        """评估个体适应度"""
        try:
            # 计算因子值
            factor_values = self._evaluate_tree(individual)
            
            # 处理因子值中的无效值
            factor_values = factor_values.replace([np.inf, -np.inf], np.nan)
            
            # 如果因子值全部为NaN或者恒定值，返回低适应度
            if factor_values.isna().all() or factor_values.nunique() <= 1:
                return (0.0, 0.0, 0.5)

            factor_values = factor_values.fillna(0)
            
            # 计算IC
            try:
                ic = factor_values.corr(self.data[self.target_cols[0]])
                if pd.isna(ic):
                    ic = 0.0
            except:
                ic = 0.0
            
            # 计算Rank IC
            try:
                factor_ranks = factor_values.rank(method='average')
                target_ranks = self.data[self.target_cols[0]].rank(method='average')
                rank_ic = factor_ranks.corr(target_ranks)
                if pd.isna(rank_ic):
                    rank_ic = 0.0
            except:
                rank_ic = 0.0
            
            # 计算胜率
            try:
                pred = (factor_values > 0).astype(int)
                label = (self.data[self.target_cols[0]] > 0).astype(int)
                win_rate = (pred == label).mean()
                if pd.isna(win_rate):
                    win_rate = 0.5
            except:
                win_rate = 0.5
            
            return (abs(ic), abs(rank_ic), win_rate)
        except Exception as e:
            print(f"评估适应度时发生错误: {str(e)}")
            return (0.0, 0.0, 0.5)
            
    def _evaluate_tree(self, tree: gp.PrimitiveTree) -> pd.Series:
        """评估表达式树"""
        func = gp.compile(tree, self.pset)
        return func
        
    def generate_factors(self, n_factors: int = 10, population_size: int = 50, 
                        generations: int = 50, cxpb: float = 0.7, mutpb: float = 0.2) -> List[Factor]:
        """生成新因子"""
        # 创建初始种群
        pop = self.toolbox.population(n=population_size)
        
        # 评估初始种群
        fitnesses = list(map(self.toolbox.evaluate, pop))
        for ind, fit in zip(pop, fitnesses):
            ind.fitness.values = fit
            
        # 进化
        for gen in range(generations):
            # 选择下一代
            offspring = self.toolbox.select(pop, len(pop))
            offspring = list(map(self.toolbox.clone, offspring))
            
            # 交叉
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < cxpb:
                    self.toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values
                    
            # 变异
            for mutant in offspring:
                if random.random() < mutpb:
                    self.toolbox.mutate(mutant)
                    del mutant.fitness.values
                    
            # 评估新个体
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            fitnesses = map(self.toolbox.evaluate, invalid_ind)
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit
                
            # 更新种群
            pop[:] = offspring
            
            # 打印进度
            fits = [ind.fitness.values for ind in pop]
            # 找到ic最大的pop
            max_ic_ind = max(pop, key=lambda x: x.fitness.values[0])
            print(f"Generation {gen + 1}/{generations}, Best IC: {max(f[0] for f in fits):.4f}, Best Rank IC: {max(f[1] for f in fits):.4f}, Best Win Rate: {max(f[2] for f in fits):.4f}, Best Formula: {stringify_for_sympy(max_ic_ind)}")
            
        # 选择最佳因子
        best_individuals = sorted(pop, key=lambda x: sum(x.fitness.values), reverse=True)[:n_factors]
        factors = self.return_factors(best_individuals)

            
        return factors
    
    def return_factors(self,best_individuals):
        # 转换为Factor对象
        factors = []
        for i, ind in enumerate(best_individuals):
            # 获取公式字符串
            formula = stringify_for_sympy(ind)
            
            # 精确地提取公式中使用的列名
            dependencies = []
            for col in self.available_columns:
                # 确保列名作为完整的标识符出现在公式中
                if re.search(r'\b' + re.escape(col) + r'\b', formula):
                    dependencies.append(col)
            
            factor = Factor(
                name=f"deap_factor_{i+1}",
                category=FactorCategory.DIY,
                description=f"IC: {ind.fitness.values[0]:.4f}, Rank IC: {ind.fitness.values[1]:.4f}, Win Rate: {ind.fitness.values[2]:.4f} , raw formula: {ind}",
                calculation=formula,
                dependencies=dependencies,
                source="deap_algorithm"
            )
            # 保存表达式树
            factor.tree = ind
            factors.append(factor)
        return factors
        
    def generate_factors_mu_plus_lambda(self, n_factors: int = 10, mu: int = 50, lambda_: int = 100,
                                      generations: int = 50, cxpb: float = 0.7, mutpb: float = 0.2) -> List[Factor]:
        """
        使用μ+λ进化策略生成新因子
        
        Args:
            n_factors: 要生成的因子数量
            mu: 父代种群大小
            lambda_: 子代种群大小
            generations: 进化代数
            cxpb: 交叉概率
            mutpb: 变异概率
            
        Returns:
            List[Factor]: 生成的因子列表
        """
        # 创建初始种群
        pop = self.toolbox.population(n=mu)
        
        # 评估初始种群
        fitnesses = list(map(self.toolbox.evaluate, pop))
        for ind, fit in zip(pop, fitnesses):
            ind.fitness.values = fit
            
        # 使用eaMuPlusLambda算法进行进化
        pop, logbook = algorithms.eaMuPlusLambda(
            pop, self.toolbox, mu=mu, lambda_=lambda_,
            cxpb=cxpb, mutpb=mutpb, ngen=generations,
            stats=None, halloffame=None, verbose=True
        )
        
        # 选择最佳因子
        best_individuals = sorted(pop, key=lambda x: sum(x.fitness.values), reverse=True)[:n_factors]
        
        factors = self.return_factors(best_individuals)
            
        return factors

if __name__ == "__main__":
    from core import config
    from core.m1 import M1
    from utils.utils import log_print
    
    from data.features.libs.basic_factors import basic_col
    # from data.features.libs.technical_factors2 import technical_factors2_cols
    
    selected_features = basic_col
    
    # 初始化M1模型和数据
    m1 = M1()
    target_col = config.TARGET_COLS[0]
    m1.mix_df=m1.process_data(config.CODE_LIST)
    m1.selected_features = selected_features
    m1.generate_features_and_labels()
    gen_df = m1.mix_df[config.BASE_COL + m1.feature_cols + [config.TARGET_COLS[0]]]

    # 使用DEAP生成新因子
    log_print("开始使用DEAP生成新因子...", level='info')
    deap_generator = DeapFactorGenerator(gen_df, config.TARGET_COLS, factor_manager)
    deap_factors = deap_generator.generate_factors(
        n_factors=5,  # 生成的因子数量
        population_size=150,  # 种群大小
        generations=20,  # 进化代数
    )
    # deap_factors = deap_generator.generate_factors_mu_plus_lambda(
    #     n_factors=10,
    #     mu=50,
    #     lambda_=10,
    #     generations=20
    # )
    
    # 保存因子
    outdir = config.OUTDIR+'/features/auto_gen/'
    os.makedirs(outdir) if not os.path.exists(outdir) else None
    
    pd.DataFrame(deap_factors).to_csv(os.path.join(outdir, 'deap_factor_documentation.csv'), index=False, encoding='utf-8')

    

    