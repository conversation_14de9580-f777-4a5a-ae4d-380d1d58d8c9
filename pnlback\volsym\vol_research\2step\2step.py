import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
import numba
import pandas as pd
import scipy.stats as stats
from scipy.integrate import quad
from scipy.optimize import minimize,linprog
import scipy.optimize as opt
from scipy.interpolate import interp1d
import warnings
warnings.filterwarnings("ignore")
plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.rcParams['axes.unicode_minus']=False #正常显示负号
pd.set_option('display.max_columns',None)
pd.set_option('display.max_rows',None)

data_raw = pd.read_pickle("sse50_option_data_processed_20231110.pkl")
data_raw = data_raw.sort_values(["contract_month","exerciseprice"])


class svi_2step:
    def __init__(self, F, K, T, imp_vol, opt_paras):
        self.x = np.log(K / F)  # 远期在值程度
        self.T = T  # 到期时间
        self.imp_vol = imp_vol  # 隐含波动率
        self.w = imp_vol ** 2 * T  # 总方差
        # opt_paras：优化的a,d,c,m,sigma参数，需要字典形式如，{"a":0.0005,"d":-0.005,"c":0.01,"m":0.1,"sigma":0.1}
        # 初始传入的作为优化的初始值，随着优化算法迭代更新为最优值
        self.opt_paras = opt_paras

    def outer_func(self, m_sigma):
        m, sigma = m_sigma
        y = (self.x - m) / sigma

        def inner_func(adc):
            a, d, c = adc
            err = np.sum((a + d * y + c * np.sqrt(y ** 2 + 1) - self.w) ** 2)
            return err

        init_adc = (self.opt_paras["a"], self.opt_paras["d"], self.opt_paras["c"])
        bnds = ([1e-6, np.max(self.w)], [-4 * sigma, 4 * sigma], [1e-6, 4 * sigma])
        cons = (
            {"type": "ineq", "fun": lambda x: x[2] - np.abs(x[1])},
            {"type": "ineq", "fun": lambda x: 4 * sigma - x[2] - np.abs(x[1])}
        )
        a_star, d_star, c_star = minimize(fun=inner_func, x0=init_adc, method="SLSQP", bounds=bnds, constraints=cons).x

        self.opt_paras["a"], self.opt_paras["d"], self.opt_paras["c"] = a_star, d_star, c_star

        err = np.sum((a_star + d_star * y + c_star * np.sqrt(y ** 2 + 1) - self.w) ** 2)
        return err

    def fit(self):
        init_m_sigma = (self.opt_paras["m"], self.opt_paras["sigma"])
        m_star, sigma_star = minimize(fun=self.outer_func, x0=init_m_sigma, method="Nelder-Mead",
                                      bounds=((2 * min(self.x.min(), 0), 2 * max(self.x.max(), 0)), (1e-6, 1))).x
        self.opt_paras["m"], self.opt_paras["sigma"] = m_star, sigma_star
        return self.opt_paras

    def eval(self, x, output):
        y = (x - self.opt_paras["m"]) / self.opt_paras["sigma"]
        svi_w = self.opt_paras["a"] + self.opt_paras["d"] * y + self.opt_paras["c"] * np.sqrt(y ** 2 + 1)
        svi_imp_vol = np.sqrt(svi_w / self.T)
        if output == "w":
            return svi_w
        elif output == "imp_vol":
            return svi_imp_vol


class svi_surface:
    def __init__(self, data, opt_method):
        self.data = data
        self.opt_method = opt_method

    def get_fit_curve(self):
        fit_result = []

        # 循环每个月份，获得相应的拟合函数，返回一个包含svi实例的列表
        for month in self.data["contract_month"].unique():
            fit_option = self.data[(self.data["实虚值"] == "虚值") & (self.data["contract_month"] == month)]
            F = fit_option["F"].values[0]
            K = fit_option["exerciseprice"].values
            T = fit_option["maturity"].values[0]
            imp_vol = fit_option["market_imp_vol"].values
            opt_paras = {"a": 0.0005, "d": -0.005, "c": 0.01, "m": 0.1, "sigma": 0.1}  # 推荐的初始值，根据数据的情况调整
            svi = self.opt_method(F, K, T, imp_vol, opt_paras)
            svi.fit()
            fit_result.append(svi)

        return fit_result

    def plot_fit_curve(self):
        fit_result = self.get_fit_curve()
        fig, ax = plt.subplots(nrows=len(self.data["contract_month"].unique()), ncols=1, figsize=(8, 20))
        for i, month in enumerate(self.data["contract_month"].unique()):
            fit_option = self.data[(self.data["实虚值"] == "虚值") & (self.data["contract_month"] == month)]
            svi = fit_result[i]
            fit_option["svi_vol"] = svi.eval(svi.x, output="imp_vol")

            ax[i].scatter(x=fit_option["exerciseprice"], y=fit_option["market_imp_vol"], marker='+', c="r")
            ax[i].plot(fit_option["exerciseprice"], fit_option["svi_vol"])
            ax[i].set_title(month)

    #  根据拟合的SVI函数，和平远期插值，生成100*100的隐含波动率网格
    def gen_imp_vol_grid(self):
        x = np.log(self.data["exerciseprice"] / self.data["F"])
        t_array = np.linspace(self.data["maturity"].min(), self.data["maturity"].max(), 100)
        x_array = np.linspace(x.min(), x.max(), 100)
        t, x = np.meshgrid(t_array, x_array)

        # 计算4个期限上的svi拟合的总方差，并存储在100*4的矩阵里
        fit_result = self.get_fit_curve()
        w = np.zeros((100, len(fit_result)))
        for m in range(len(fit_result)):
            w[:, m] = fit_result[m].eval(x_array, output="w")

        # 在x的维度上循环100次，每次循环在t维度上平远期插值计算
        v = np.zeros_like(t)

        for n in range(100):
            f = interp1d(x=self.data["maturity"].unique(), y=w[n], kind="linear")
            v[n] = np.sqrt(f(t[n]) / t[n])  # 返回的还是隐含波动率而不是总方差

        return t, x, v

    def plot_surface(self):
        fig = plt.figure(figsize=(12, 7))
        ax = plt.axes(projection='3d')
        norm = mpl.colors.Normalize(vmin=0.1, vmax=0.2)
        # 绘图主程序
        t, x, v = self.gen_imp_vol_grid()
        surf = ax.plot_surface(t, x, v, rstride=1, cstride=1,
                               cmap=plt.cm.coolwarm, norm=norm, linewidth=0.5, antialiased=True)
        # 设置坐标轴
        ax.set_xlabel('maturity')
        ax.set_ylabel('strike')
        ax.set_zlabel('market_imp_vol')
        ax.set_zlim((0.1, 0.25))
        fig.colorbar(surf, shrink=0.25, aspect=5)


class svi_2step_45(svi_2step):
    def outer_func(self, m_sigma):
        m, sigma = m_sigma
        y = (self.x - m) / sigma

        bnd = ((0, 0, 0), (max(self.w.max(), 1e-6), 2 * np.sqrt(2) * sigma, 2 * np.sqrt(2) * sigma))
        z = np.sqrt(np.square(y) + 1)

        # 换元法，等价于d，c旋转45°，还原后除了约束条件变化外，这里的优化函数A也会相应变化

        A = np.column_stack([np.ones(len(y)), np.sqrt(2) / 2 * (y + z), np.sqrt(2) / 2 * (-y + z)])

        a_2, d_2, c_2 = opt.lsq_linear(A, self.w, bnd, tol=1e-12, verbose=False).x
        a_star, d_star, c_star = a_2, np.sqrt(2) / 2 * (d_2 - c_2), np.sqrt(2) / 2 * (d_2 + c_2)

        self.opt_paras["a"], self.opt_paras["d"], self.opt_paras["c"] = a_star, d_star, c_star

        err = np.sum((a_star + d_star * y + c_star * np.sqrt(y ** 2 + 1) - self.w) ** 2)
        return err

svi_fit = svi_surface(data=data_raw, opt_method=svi_2step)
svi_fit.plot_fit_curve()