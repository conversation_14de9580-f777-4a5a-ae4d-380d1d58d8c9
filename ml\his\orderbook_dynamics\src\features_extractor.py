"""
Features extractor implementation for orderbook dynamics.
This module defines feature extractors that can be used for machine learning.
"""
import numpy as np
from typing import Dict, List, Optional, Callable
from datetime import <PERSON><PERSON><PERSON>
from .models import OpenBookMsg, OrderBook
from .attribute.basic_attribute import BasicSet, BasicAttribute
from .attribute.time_insensitive_attribute import TimeInsensitiveSet, TimeInsensitiveAttribute
from .attribute.time_sensitive_attribute import TimeSensitiveSet, TimeSensitiveAttribute
from .attribute.label import MeanPriceMove, MeanPriceMovementLabel
from .attribute.labeled_points_extractor import (
    LabeledPointsExtractor, LabeledPointsExtractorBuilder,
    basic, time_insensitive, time_sensitive
)


class FeaturesExtractor:
    """
    Features extractor for order book dynamics.
    
    Defines common feature sets for predicting order book movements.
    """
    def __init__(self, max_level: int = 5, time_window_ms: int = 1000):
        """
        Initialize a features extractor.
        
        Args:
            max_level: Maximum order book level to consider
            time_window_ms: Time window in milliseconds
        """
        self.max_level = max_level
        self.time_window_ms = time_window_ms

    def create_features_extractor(self, symbol: str) -> LabeledPointsExtractor:
        """
        Create a labeled points extractor for a given symbol.
        
        Args:
            symbol: Symbol to extract features for
            
        Returns:
            A LabeledPointsExtractor configured with common features
        """
        builder = LabeledPointsExtractorBuilder(self.max_level)
        
        # Add basic set prices
        for level in range(1, self.max_level + 1):
            builder.add(basic(lambda bs, level=level: bs.ask_price(level)))
            builder.add(basic(lambda bs, level=level: bs.bid_price(level)))

        # Add basic set volumes
        for level in range(1, self.max_level + 1):
            builder.add(basic(lambda bs, level=level: bs.ask_volume(level)))
            builder.add(basic(lambda bs, level=level: bs.bid_volume(level)))

        # Add time insensitive set
        builder.add(time_insensitive(lambda tis: tis.mean_ask()))
        builder.add(time_insensitive(lambda tis: tis.mean_ask_volume()))
        builder.add(time_insensitive(lambda tis: tis.mean_bid()))
        builder.add(time_insensitive(lambda tis: tis.mean_bid_volume()))
        
        # Add time insensitive set steps
        for level in range(1, self.max_level):
            builder.add(time_insensitive(lambda tis, level=level: tis.ask_step(level)))
            builder.add(time_insensitive(lambda tis, level=level: tis.bid_step(level)))

        # Add price spread and volume spread
        builder.add(time_insensitive(lambda tis: tis.price_spread()))
        builder.add(time_insensitive(lambda tis: tis.volume_spread()))
        builder.add(time_insensitive(lambda tis: tis.accumulated_price_spread()))
        builder.add(time_insensitive(lambda tis: tis.accumulated_volume_spread()))

        # Add time sensitive set
        builder.add(time_sensitive(lambda ts: ts.ask_arrival_rate()))
        builder.add(time_sensitive(lambda ts: ts.bid_arrival_rate()))
        builder.add(time_sensitive(lambda ts: ts.order_intensity()))
        builder.add(time_sensitive(lambda ts: ts.buy_sell_ratio()))
        builder.add(time_sensitive(lambda ts: ts.price_impact()))
        builder.add(time_sensitive(lambda ts: ts.order_imbalance_ratio()))
        builder.add(time_sensitive(lambda ts: ts.price_volatility()))

        # Create extractor with mean price movement label
        return builder.result(symbol, MeanPriceMovementLabel(), self.time_window_ms) 