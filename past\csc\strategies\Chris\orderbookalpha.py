
# -*- coding: utf-8 -*-
"""
Created on Fri Oct 15 18:07:27 2021
#统计满足一定两笔，且市场上有成交量
@author: zhanghc
"""

#import python pacakges
import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
import math
from datetime import timedelta
import datetime
import GetMarketData
import PostTrade
import matplotlib.pyplot as plt

#%%
if __name__ == '__main__':
    
    volume_ratio= 2
    min_volume=10
    market_impact_slice = 2
    beginStr = '2022-2-15T21:00:00.0Z'
    endStr = '2022-2-16T15:00:00.0Z'
    mode = 'dev'
    contract = 'i2205.DCE'
    contract_ts = 0.5
    beginT = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    endT = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    df_mkt = GetMarketData.getmarketdata(mode,contract[-3:],contract[:-4],beginT,endT)
    df_mkt['v'] = df_mkt['v'].diff().fillna(0)
    
    df_trades = df_mkt[df_mkt['v']>0]
    
    #提取前1个切片的行情
    df_quote = df_mkt.iloc[df_trades.index-1]
    
    df_quote.index+=1 #重新对齐
    
    df = df_quote.merge(df_trades,left_index=True,right_index=True,suffixes=('_quote','_trade'))
    cols = ['time_quote', 'b_p5_quote', 'b_p4_quote', 'b_p3_quote', 'b_p2_quote','b_p1_quote',
            'a_p1_quote', 'a_p2_quote', 'a_p3_quote', 'a_p4_quote','a_p5_quote', 
            'b_v5_quote', 'b_v4_quote', 'b_v3_quote', 'b_v2_quote', 'b_v1_quote',
            'a_v1_quote', 'a_v2_quote', 'a_v3_quote', 'a_v4_quote','a_v5_quote', 
            'last_p_quote', 'turnover_quote','v_quote', 'a_p1_trade','a_v1_trade', 
            'b_p1_trade', 'b_v1_trade','last_p_trade', 'v_trade','time_trade']
    df = df[cols]
    df.reset_index(inplace=True)
    df['trade_through'] = 0
    df['direction']=0 #做市的成交方向
    df['quote_volume']=0
    df['other_volume']=0
    df['trade_price']=0.0
    for i in range(df.shape[0]):
        direction = 0
        tradethrough = 0
        quote_volume = 0
        other_volume = 0
        trade_price =0
        if df.at[i,'last_p_trade']>=df.at[i,'a_p1_quote']:
            direction = -1
            quote_volume = df.at[i,'a_v1_quote']
            other_volume = df.at[i,'b_v1_quote']
            trade_price = df.at[i,'a_p1_quote']
        elif df.at[i,'last_p_trade']<=df.at[i,'b_p1_quote']:
            direction = 1
            quote_volume = df.at[i,'b_v1_quote']
            other_volume = df.at[i,'a_v1_quote']
            trade_price = df.at[i,'b_p1_quote']
        else:
            direction = 0

        if (direction ==-1 and df.at[i,'a_p1_trade'] > df.at[i,'a_p1_quote']) or (direction==1 and df.at[i,'b_p1_trade'] < df.at[i,'b_p1_quote']):
            tradethrough = 1
        else:
            tradethrough =0
        df.at[i,'direction'] = direction
        df.at[i,'trade_through'] = tradethrough
        df.at[i,'quote_volume'] = quote_volume
        df.at[i,'other_volume'] = other_volume
        df.at[i,'trade_price'] = trade_price
        
    #Generate Trade Report
    trades = df[['time_trade','trade_price','direction','quote_volume','other_volume','trade_through','v_trade','last_p_trade']]
    trades.columns = ['datetime','price','direction','quote_volume','other_volume','trade_through','v_trade','last_p_trade']
    trades = trades[trades['direction']!=0]
    trades['volumeratio'] = trades['quote_volume']/trades['other_volume']
    trades_volumeratio = trades[(trades['volumeratio']>=volume_ratio) & (trades['quote_volume']>min_volume)]
    
    #Generate mkt_maker
    mkt_maker = df_mkt[['time','b_p1','a_p1']]
    mkt_maker.rename(columns = {"time":"datetime"},inplace=True)
    mkt_maker['mid'] = 0.5*(mkt_maker['b_p1']+mkt_maker['a_p1'])


#%%
#--------------------------------------------------------PLot and Print Statistics ------------------------------------------------------------------
    # plot volume ratio distribution
    print("VolumeTradeDistribution",'TotalVolumeCount',sum(trades['v_trade']>0))
    vol = pd.DataFrame(trades['v_trade'].groupby(trades['v_trade']).count())
    vol = vol.rename(columns={"v_trade":'count'})
    #剔除95百分位的成交量
    cutoff = np.percentile(vol.index,10)
    vol = vol[vol.index < cutoff]
    plt.bar(vol.index,vol['count'])
    plt.title('VolumeTradeDistribution')
    plt.show()
    
    # #plot volume ratio distribution
    print("量比分布图")
    # vol = trades['volumeratio'].groupby(trades['volumeratio'].count())
    plt.hist(trades['volumeratio'],range=(np.percentile(trades['volumeratio'],5),np.percentile(trades['volumeratio'],95)));
    plt.title('VolumeRatioDistribution')
    plt.show()
    
    print('所有盘口成交-RS')
    print('所有盘口打穿比例:' , "{:.2%}".format(trades['trade_through'].sum()/trades.shape[0]))
    [df_market_impacts,df_realized_spreads] = PostTrade.calculate_market_impact(trades,mkt_maker,market_impact_slice,contract_ts)
    print('总成交量',df_market_impacts.shape[0])
    # print('成交量占比:' , "{:.2%}".format(df_market_impacts.shape[0]/trades.shape[0]))
    PostTrade.plot_Realized_Spread(df_market_impacts,df_realized_spreads)
#%%    
    print('OrderbookImbalance盘口成交-RS')
    print("量比",volume_ratio)
    print("盘口最小量",min_volume)
    print('Imbalance盘口打穿比例:' , "{:.2%}".format(trades_volumeratio['trade_through'].sum()/trades.shape[0]))
    [df_market_impacts,df_realized_spreads] = PostTrade.calculate_market_impact(trades_volumeratio,mkt_maker,market_impact_slice,contract_ts)
    print('总成交量',df_market_impacts.shape[0])
    print('成交量占比:' , "{:.2%}".format(trades_volumeratio.shape[0]/trades.shape[0]))
    PostTrade.plot_Realized_Spread(df_market_impacts,df_realized_spreads)
    
    print("OrderBookImbalance成交量分布")
    plt.hist(trades_volumeratio['v_trade'],range=(np.percentile(trades_volumeratio['v_trade'],1),np.percentile(trades_volumeratio['v_trade'],90)));
    print('平均成交量:',trades_volumeratio['v_trade'].mean())
    plt.title('ImbalanceTradeVolume')
    plt.show()
    
    
    
    # print("OrderBookImbalance成交量分布-一定赚钱")
    # trades_volumeratio_pnl = trades_volumeratio[trades_volumeratio['trade_through']==0]
    # plt.hist(trades_volumeratio_pnl['v_trade'],range=(np.percentile(trades_volumeratio_pnl['v_trade'],1),np.percentile(trades_volumeratio_pnl['v_trade'],99)));
    # print('平均成交量:',trades_volumeratio_pnl['v_trade'].mean())
    # plt.title('ImbalanceTradeVolume')
    # plt.show()
    
    
    
    trades['trade_through'].sum()
    

    
    
    
    
    # # # tradethrough(bid1,ask1,last_next,bid1_next,ask1_next,direc)
    # # df['direction'] = tradethrough(df['b_p1_quote'],df['a_p1_quote'],df['last_p_trade'],df['b_p1_trade'],df['a_p1_trade'])
    # # df['tradethrough'] = tradethrough(df['b_p1_quote'],df['a_p1_quote'],df['last_p_trade'],df['b_p1_trade'],df['a_p1_trade'],0)
    
    
    # df['direction'] = (df[''])
    
    # df_test = df.copy(deep=False)
    
    
    
    # df_test['test'] = test(df['b_p1_quote'],df['a_p1_quote'])
    # df_test['test'] = df['b_p1_quote']>df['a_p1_quote']
        
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    