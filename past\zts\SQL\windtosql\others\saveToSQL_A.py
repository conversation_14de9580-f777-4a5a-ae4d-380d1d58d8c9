# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-24
from WindPy import w
import pymssql
from datetime import datetime

server = '***********'
user = 'Alex'
password = '789456'
dt = "2017-08-24"  # datetime.now()
# beginDate = "2017-06-24"
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')
cursor = conn.cursor()
cursor.execute("""
 IF OBJECT_ID('ZZStks_IPO', 'U') IS NOT NULL
    DROP TABLE ZZStks_IPO
 CREATE TABLE ZZStks_IPO (
    DateTime DATE NOT NULL,
    Time TIME(7) NOT NULL,
    STKID VARCHAR(20) NOT NULL,
    OPENPrice numeric(15, 2),
    HIGH numeric(15, 2),
    LOW numeric(15, 2),
    CLOSEPrice numeric(15, 2),
    Volume BIGINT,
    Amount numeric(15, 2),
    Ticks BIGINT,
    )
 """)


def tosql(zzcode):
    sql = "INSERT INTO ZZStks_IPO VALUES (%s,%d, %s, %d, %d, %d, %d, %d, %d,%d)"

    # 通过wset来取数据集数据
    print('\n\n' + '-----通过wset来取数据集数据,获取全部%s代码列表-----' % zzcode + '\n')
    wsetdata = w.wset("sectorconstituent", "date= %s ;sectorid= %s " % (dt, zzcode))
    print(wsetdata)

    for j in range(0, len(wsetdata.Data[1])):
        # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
        print("\n\n-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----\n" % (j, str(wsetdata.Data[1][j])))
        wssdata = w.wss(str(wsetdata.Data[1][j]), 'ipo_date')
        wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", wssdata.Data[0][0], dt,
                         "Fill=Blank", "PriceAdj=F")

        # wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
        if wsddata1.ErrorCode != 0:
            continue
        print(wsddata1)
        for i in range(0, len(wsddata1.Data[1])):
            sqllist = []

            if len(wsddata1.Times) > 1:
                sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
                sqllist.append("00:00:00.0000001")

            sqllist.append(str(wsetdata.Data[1][j]))

            for k in range(0, len(wsddata1.Fields)):
                sqllist.append(wsddata1.Data[k][i])

            sqllist.append(-1)
            try:
                sqltuple = tuple(sqllist)
                cursor.execute(sql, sqltuple)
            except:
                print(sqllist)
        conn.commit()


tosql(zzcode="a001010100000000")  # A股


conn.close()
