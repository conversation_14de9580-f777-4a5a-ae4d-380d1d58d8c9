"""
可视化模块
@author: lining
"""
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import seaborn as sns
from scipy import stats
from utils.utils import log_print
from core import config
import os
import matplotlib.pyplot as plt
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
import base64
import glob
import datetime
from bs4 import BeautifulSoup


class OrderBookVisualizer:
    """订单簿可视化器"""

    def __init__(self, use_chinese=True, figsize=(12, 8)):
        """
        初始化可视化器
        
        参数:
            use_chinese (bool): 是否使用中文显示
            figsize (tuple): 默认图形大小
        """
        self.figsize = figsize
        self.use_chinese = use_chinese
        self.plots_dir = os.path.join(config.OUTDIR, 'plots')
        if not os.path.exists(self.plots_dir):
            os.makedirs(self.plots_dir)

        # 设置样式
        sns.set(style="whitegrid")


    def plot_feature_correlation(self, train_data, y_train_dict, features=None, target=None, title=None):
        """
        绘制特征相关性图
        
        参数:
            data (pandas.DataFrame): 数据
            y_train_dict (dict): 目标列名字典
            features (list): 特征列名列表
            target (str): 目标列名
            title (str): 图表标题
        """
        data = train_data[target]
        y_train = y_train_dict[target]
        data = pd.concat([data, y_train], axis=1)
        if data is None or len(data) == 0:
            print("数据为空，无法绘制特征相关性图")
            return

        # 选择特征
        if features is None:
            # 排除非数值列
            features = data.select_dtypes(include=[np.number]).columns.tolist()

        if target is not None:
            features.append(target)

        # 如果特征太多，只选择前100个
        if len(features) > 1000:
            print(f"特征数量过多 ({len(features)})，只显示前1000个")
            features = features[:1000]

        # 计算相关性
        corr = data[features].corr()
        # 按照绝对值排序，不改变原正负号
        corrcolumns = corr.abs().sort_values(by=target, ascending=True).index
        corr = corr.loc[corrcolumns, corrcolumns]


        # 创建图形
        fig = go.Figure(data=go.Heatmap(
            z=corr,
            x=corr.columns,
            y=corr.columns,
            colorscale='RdBu',
            zmid=0,
            text=np.round(corr, 2),
            texttemplate='%{text}',
        ))

        # 设置标题
        title = title or '特征相关性热图'
        fig.update_layout(
            title_text=title,
        )
        fig.show()

        
        # 相关性大于0.8的特征,且不能是自己和自己的相关性
        corr_high = corr.copy()
        # 将对角线元素设为NaN
        np.fill_diagonal(corr_high.values, np.nan)
        # 筛选出绝对值大于0.8的元素
        corr_high = corr_high[corr_high.abs() > 0.7]

        # 删除整个行或者列是NaN的行或者列
        corr_high = corr_high.dropna(how='all', axis=0)
        corr_high = corr_high.dropna(how='all', axis=1)

        # 新画个图,相关性大于0.7的特征
        fig_high = go.Figure(data=go.Heatmap(
            z=corr_high,
            x=corr_high.columns,
            y=corr_high.index,
            colorscale='RdBu',
            zmid=0,
            text=np.round(corr_high, 2),
            texttemplate='%{text}',
        ))
        fig_high.update_layout(
            title_text='相关性大于0.7的特征',
        )
        fig_high.show()

        correlation_path = os.path.join(self.plots_dir, 'feature_correlation.html')
        fig.write_html(correlation_path)

        highlight_features = corr_high.columns.tolist()

        return highlight_features

    def plot_feature_importance(self, model, feature_cols, title=None, top_n=config.VISUALIZE_TOP_N):
        """
        绘制特征重要性图
        
        参数:
            importance_df (pandas.DataFrame): 特征重要性数据框，包含'feature'和'importance'列
            title (str): 图表标题
            top_n (int): 显示前N个重要特征
        """

        try:
            # 检查特征重要性的长度是否与特征列表长度相同
            feature_importances = model.feature_importances_
            model_feature_cols = model.feature_names_in_
            del_feature_cols = [col for col in model_feature_cols if col not in feature_cols]
            feature_importance = pd.DataFrame({
                'feature': model_feature_cols,
                'importance': feature_importances
            }).sort_values('importance', ascending=True)
            # 检查必要的列是否存在
            required_cols = ['feature', 'importance']
            missing_cols = [col for col in required_cols if col not in feature_importance.columns]
            if missing_cols:
                print(f"数据缺少必要的列: {missing_cols}")
                return [],[]
        except:
            log_print("特征重要性数据为空，无法绘制特征重要性图", 'warning')
            return [],[]
        
        # 选择前N个重要特征
        if len(feature_importance) > top_n:
            feature_importance = feature_importance.head(top_n)

        # 创建图形
        fig = go.Figure()

        # 添加条形图
        fig.add_trace(
            go.Bar(x=feature_importance['importance'],
                   y=feature_importance['feature'],
                   orientation='h',
                   name='特征重要性')
        )

        # 更新布局
        fig.update_layout(
            title_text=title or '特征重要性',
            xaxis_title="重要性",
            yaxis_title="特征",
            showlegend=False
        )

        fig.show()

        importance_path = os.path.join(self.plots_dir, 'feature_importance.html')
        fig.write_html(importance_path)

        # 返回最后10个特征
        least_important_features = feature_importance['feature'][:10].tolist()
        top_important_features = feature_importance['feature'][-5:][::-1].tolist()
        log_print(f"特征重要性最高的5个特征: {top_important_features}")
        return least_important_features, top_important_features

    def plot_IC_IR_win_rate(self, results: pd.DataFrame, top_n=config.VISUALIZE_TOP_N):
        """生成可视化报告"""
        # Top 50因子IC值和IR值对比图
        top_50_features = results.head(top_n)
        fig_top_50 = make_subplots(specs=[[{"secondary_y": True}]])

        fig_top_50.add_trace(
            go.Bar(x=top_50_features['feature'], y=top_50_features['ic'],
                   name='IC值', marker_color='blue', width=0.4, offset=-0.2),
            secondary_y=False
        )

        fig_top_50.add_trace(
            go.Bar(x=top_50_features['feature'], y=top_50_features['ir'],
                   name='IR值', marker_color='red', width=0.4, offset=0.2),
            secondary_y=True
        )

        # 添加胜率折线图
        fig_top_50.add_trace(
            go.Scatter(x=top_50_features['feature'], y=top_50_features['win_rate'],
                       name='胜率', mode='lines+markers', marker_color='green', line=dict(width=2)),
            secondary_y=True
        )

        # 添加0.5胜率参考线
        fig_top_50.add_hline(y=0.5, line_dash="dash", line_color="green", secondary_y=True)

        ic_max = max(abs(top_50_features['ic'].max()), abs(top_50_features['ic'].min()))
        ir_max = max(abs(top_50_features['ir'].max()), abs(top_50_features['ir'].min()))

        fig_top_50.update_layout(
            title='Top 50因子IC值和IR值对比',
            xaxis_tickangle=45,
            showlegend=True
        )

        fig_top_50.update_yaxes(title_text="IC值", secondary_y=False, range=[-ic_max, ic_max])
        fig_top_50.update_yaxes(title_text="IR值", secondary_y=True, range=[-ir_max, ir_max])

        fig_top_50.show()
        fig_top_50.write_html(os.path.join(self.plots_dir, 'factors_ic_ir_win_rate.html'))

        # ic小于0.02的特征
        ic_low = top_50_features[abs(top_50_features['ic']) < 0.02]

        log_print(f"ic前10: {top_50_features['feature'].head(10).to_list()}")

        return ic_low

    def analyze_factor_distribution(self, factor_series):
        """
        分析因子分布，绘制直方图、KDE图、箱线图，并输出统计指标。
        
        参数:
            factor_series (pd.Series): 待分析的因子值序列
        
        返回:
            fig (plotly.graph_objects.Figure): 绘图对象
        """
        # 预处理,移除无穷值和缺失值
        factor = factor_series.replace([np.inf, -np.inf], np.nan).dropna()
        print(f'无效值比例: {(len(factor_series) - len(factor))/len(factor_series)}')
        
        # 创建子图布局
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('直方图 + KDE', '箱线图', 'Q-Q图', ''),
            specs=[[{"colspan": 2}, None], [{}, {}]],
            vertical_spacing=0.1,
            horizontal_spacing=0.1
        )
        
        # 子图1: 直方图 + KDE
        hist_data = [factor]
        group_labels = ['factor']
        
        # 计算直方图数据
        counts, bins = np.histogram(factor, bins=50)
        bins_center = (bins[:-1] + bins[1:]) / 2
        
        # 添加直方图
        fig.add_trace(
            go.Bar(x=bins_center, y=counts/sum(counts)/(bins[1]-bins[0]), 
                   name='直方图', marker_color='skyblue', opacity=0.7),
            row=1, col=1
        )
        
        # 计算KDE (核密度估计, Kernel Density Estimation)
        # KDE是一种非参数方法，用于估计随机变量的概率密度函数
        # 它通过在每个数据点放置一个核函数(通常是高斯核)并将它们相加来平滑数据
        kde = stats.gaussian_kde(factor)
        x_kde = np.linspace(factor.min(), factor.max(), 1000)
        y_kde = kde(x_kde)
        
        # 添加KDE曲线
        fig.add_trace(
            go.Scatter(x=x_kde, y=y_kde, mode='lines', name='KDE', 
                      line=dict(color='blue', width=2)),
            row=1, col=1
        )
        
        # 添加正态分布参考线
        x = np.linspace(factor.min(), factor.max(), 1000)
        normal_pdf = stats.norm.pdf(x, loc=factor.mean(), scale=factor.std())
        fig.add_trace(
            go.Scatter(x=x, y=normal_pdf, mode='lines', name='正态分布', 
                      line=dict(color='red', width=2, dash='dash')),
            row=1, col=1
        )
        
        # 子图2: 箱线图
        # 添加箱线图
        # y: 数据源，这里使用因子数据
        # name: 图例名称
        # marker_color: 箱线图的颜色，设置为浅绿色
        # boxpoints: 设置为'outliers'表示只显示异常值点
        # jitter: 控制异常值点的水平抖动程度，值为0.3表示中等抖动，防止点重叠
        # pointpos: 控制异常值点相对于箱体的位置，-1.8表示点显示在箱体左侧
        fig.add_trace(
            go.Box(y=factor, name='factor', marker_color='lightgreen', 
                  boxpoints='outliers', jitter=0.3, pointpos=-1.8),
            row=2, col=1  # 指定图表位置：第2行第1列
        )
        
        # 子图3: Q-Q图
        quantiles = np.linspace(0.01, 0.99, 100)
        empirical_quantiles = factor.quantile(quantiles)
        theoretical_quantiles = stats.norm.ppf(quantiles, loc=factor.mean(), scale=factor.std())
        
        # 添加散点
        fig.add_trace(
            go.Scatter(x=theoretical_quantiles, y=empirical_quantiles, 
                      mode='markers', name='Q-Q点', 
                      marker=dict(color='blue', size=6)),
            row=2, col=2
        )
        
        # 添加参考线
        min_val = min(theoretical_quantiles.min(), empirical_quantiles.min())
        max_val = max(theoretical_quantiles.max(), empirical_quantiles.max())
        fig.add_trace(
            go.Scatter(x=[min_val, max_val], y=[min_val, max_val], 
                      mode='lines', name='参考线', 
                      line=dict(color='red', width=2, dash='dash')),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title=f'因子分布分析 (N={len(factor)})',
            showlegend=True
        )
        
        # 更新坐标轴标签
        fig.update_xaxes(title_text='因子值', row=1, col=1)
        fig.update_yaxes(title_text='密度', row=1, col=1)
        fig.update_xaxes(title_text='因子值', row=2, col=1)
        fig.update_xaxes(title_text='理论分位数', row=2, col=2)
        fig.update_yaxes(title_text='实际分位数', row=2, col=2)
        
        # 输出统计指标
        stats_dict = {
            '观测数': len(factor),
            '均值': factor.mean(),
            '标准差': factor.std(),
            '偏度': factor.skew(),
            '峰度': factor.kurtosis(),
            'Jarque-Bera检验': stats.jarque_bera(factor)[0],
            'JB检验P值': stats.jarque_bera(factor)[1],
        }
        # 把文字输出在图表外最下方,一行展示
        legend_text = ""
        for key, value in stats_dict.items():
            if value>1:
                legend_text += f"{key}: {int(value)}  "  # 添加两个空格作为分隔
            else:
                legend_text += f"{key}: {value:.4f}  "  # 添加两个空格作为分隔
        # 在图表底部添加统计信息文本框
        fig.add_annotation(
            xref="paper",
            yref="paper",
            x=0.5,
            y=-0.08,  # 调整位置到图表下方
            text=legend_text,
            showarrow=False,
            font=dict(size=12),
            align="center",
            bgcolor="white",
            opacity=0.8
        )

        # 显示图表
        fig.show()
        
        # 保存图表
        if hasattr(self, 'plots_dir'):
            factor_name = getattr(factor_series, 'name', 'factor')
            factor_path = os.path.join(self.plots_dir, f'{factor_name}_distribution.html')
            fig.write_html(factor_path)
        
        return fig

    def plot_backtest_results(self, results, title=None):
        """
        绘制回测结果
        
        参数:
            results (pandas.DataFrame): 回测结果数据框
            title (str): 图表标题
        """
        if results is None or len(results) == 0:
            log_print("数据为空，无法绘制回测结果", 'warning')
            return None

        # 创建子图
        fig = make_subplots(
            rows=4, cols=1, 
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=('价格和交易信号', '累计收益曲线', '回撤百分比', '持仓'),
            specs=[[{"type": "xy"}], 
                   [{"type": "xy", "secondary_y": True}],  # 第二行支持双y轴
                   [{"type": "xy", "secondary_y": True}], 
                   [{"type": "xy", "secondary_y": True}]]
        )

        # 绘制价格和信号
        price_col = 'mid' if 'mid' in results.columns else 'close'
        if price_col in results.columns:
            # 过滤掉价格为空的数据点
            valid_data = results[results[price_col].notna()]

            # 添加价格曲线 - 使用connectgaps=False确保空值处不连线
            fig.add_trace(
                go.Scatter(x=valid_data.index, y=valid_data[price_col],
                           name=price_col, line=dict(color='blue'),
                           connectgaps=False),
                row=1, col=1
            )

            # 标记买入点和卖出点
            if 'position' in results.columns:
                buy_points = valid_data[valid_data['position'] > 0]
                sell_points = valid_data[valid_data['position'] < 0]

                if len(buy_points) > 0:
                    fig.add_trace(
                        go.Scatter(x=buy_points.index, y=buy_points[price_col],
                                   mode='markers',
                                   marker=dict(symbol='triangle-up', size=10, color='green'),
                                   name='买入'),
                        row=1, col=1
                    )
                if len(sell_points) > 0:
                    fig.add_trace(
                        go.Scatter(x=sell_points.index, y=sell_points[price_col],
                                   mode='markers',
                                   marker=dict(symbol='triangle-down', size=10, color='red'),
                                   name='卖出'),
                        row=1, col=1
                    )

            # 标记平仓点
            if 'exit_price' in results.columns:
                exit_points = valid_data[valid_data['exit_price'].notna()]
                if len(exit_points) > 0:
                    fig.add_trace(
                        go.Scatter(x=exit_points.index, y=exit_points['exit_price'],
                                   mode='markers',
                                   marker=dict(symbol='x', size=10, color='black'),
                                   name='平仓'),
                        row=1, col=1
                    )

        # 绘制收益曲线 
        valid_returns = results[results['cum_return'].notna()]
        
        # 使用secondary_y参数将原始收益和扣除手续费的收益绘制在同一条线上
        fig.add_trace(
            go.Scatter(x=valid_returns.index, y=valid_returns['cum_return'],
                       name='累计收益', line=dict(color='blue'),
                       connectgaps=False),
            row=2, col=1,
            secondary_y=False
        )
        
        # 扣除手续费
        valid_net_returns = results[results['net_return'].notna()]
        fig.add_trace(
            go.Scatter(x=valid_net_returns.index, y=valid_net_returns['net_return'],
                       name='扣除手续费', line=dict(color='red'),
                       connectgaps=False),
            row=2, col=1,
            secondary_y=True
        )

        # 手续费
        fig.add_trace(
            go.Scatter(x=valid_net_returns.index, y=1+valid_net_returns['cum_return']-valid_net_returns['net_return'],
                       name='手续费', line=dict(color='orange', dash='dash', width=1),
                       connectgaps=False),
            row=2, col=1,
        )

        # 格式化y轴为百分比
        fig.update_yaxes(tickformat='.1%', row=2, col=1, secondary_y=False)
        fig.update_yaxes(tickformat='.1%', row=2, col=1, secondary_y=True)
        
        # 更新y轴标题
        fig.update_yaxes(title_text="累计收益", row=2, col=1, secondary_y=False)
        fig.update_yaxes(title_text="扣除手续费收益", row=2, col=1, secondary_y=True)

        # 绘制回撤
        drawdown = (valid_returns['cum_return'] / valid_returns['cum_return'].cummax() - 1) * 100
        drawdown2 = (valid_net_returns['net_return'] / valid_net_returns['net_return'].cummax() - 1) * 100
        fig.add_trace(
            go.Scatter(x=valid_returns.index, y=drawdown,
                       fill='tozeroy',
                       fillcolor='blue',
                       name='回撤',
                       opacity=0.3, # 透明度
                       line=dict(color='red'),
                       connectgaps=False),
            row=3, col=1
        )

        # 绘制回撤
        fig.add_trace(
            go.Scatter(x=valid_net_returns.index, y=drawdown2,
                       fill='tozeroy',
                       fillcolor='green',
                       opacity=0.3, # 透明度
                       name='扣除手续费回撤',
                       line=dict(color='red'),
                       connectgaps=False),
            row=3, col=1,
            secondary_y=True
        )
        # 更新y轴标题
        fig.update_yaxes(title_text="回撤 (%)", row=3, col=1, secondary_y=False)
        fig.update_yaxes(title_text="扣除手续费回撤 (%)", row=3, col=1, secondary_y=True)

        # 绘制持仓
        if 'position' in results.columns:
            fig.add_trace(
                go.Scatter(x=results.index, y=results['position'].ffill(),
                           name='持仓', line=dict(color='blue')),
                row=4, col=1
            )
            # 绘制总成交次数
            fig.add_trace(
                go.Scatter(x=results.index, y=results['total_trades'].ffill(),
                           name='总成交次数', line=dict(color='green', dash='dash', width=1)),
                row=4, col=1,
                secondary_y=True
            )
            # 更新y轴标题
            fig.update_yaxes(title_text="持仓", row=4, col=1, secondary_y=False)
            fig.update_yaxes(title_text="总成交次数", row=4, col=1, secondary_y=True)

        # 更新布局
        fig.update_layout(
            title_text=title or '回测结果',
            showlegend=True,
            grid={'rows': 4, 'columns': 1, 'pattern': "independent"}
        )

        # 更新坐标轴标签
        fig.update_xaxes(title_text="时间", row=4, col=1)
        fig.update_yaxes(title_text="价格", row=1, col=1)

        fig.show()
        backtest_path = os.path.join(self.plots_dir, 'backtest_results.html')
        fig.write_html(backtest_path)

    def plot_trade_analysis(self, trades_data, returns, standardize_qq=True, title=None):
        """
        绘制交易分析图
        
        参数:
            trades (pandas.DataFrame): 交易记录数据框
            title (str): 图表标题
        """
        if trades_data is None or len(trades_data) == 0:
            print("数据为空，无法绘制交易分析图")
            return
        
        close_trades = trades_data[trades_data['type'] == 'close']

        # 检查必要的列是否存在
        required_cols = ['entry_time', 'exit_time', 'return']
        missing_cols = [col for col in required_cols if col not in close_trades.columns]
        if missing_cols:
            print(f"数据缺少必要的列: {missing_cols}")
            return

        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('交易收益分布', 'QQ图', '持仓时间分布', '盈亏交易数量')
        )

        # 过滤非零收益
        non_zero_returns = returns[returns != 0]

        if len(non_zero_returns) == 0:
            log_print("没有交易记录，无法绘制图表", 'warning')
            return None

        # 1. 绘制收益分布直方图（左侧）
        fig.add_trace(
            go.Histogram(
                x=non_zero_returns,
                name='收益率',
                nbinsx=50,
                histnorm='probability density',
                marker_color='rgba(0, 114, 178, 0.6)',
                marker_line=dict(width=1, color='rgb(0, 0, 0)')
            ),
            row=1, col=1
        )

        # 添加零线
        fig.add_vline(x=0, line_dash="dash", line_color="red", row=1, col=1)

        # 添加正态分布曲线对比
        mu = non_zero_returns.mean()
        sigma = non_zero_returns.std()
        x = np.linspace(mu - 4 * sigma, mu + 4 * sigma, 200)
        y = 1 / (sigma * np.sqrt(2 * np.pi)) * np.exp(-(x - mu) ** 2 / (2 * sigma ** 2))

        fig.add_trace(
            go.Scatter(
                x=x,
                y=y,
                name='正态分布拟合',
                line=dict(color='red', width=2)
            ),
            row=1, col=1
        )

        # 2. 绘制QQ图（右侧）
        # 正确计算理论分位数和样本分位数
        n = len(non_zero_returns)
        # 根据参数决定是否标准化数据
        if standardize_qq:
            # 标准化数据
            plot_data = (non_zero_returns - mu) / sigma
        else:
            # 直接使用原始数据
            plot_data = non_zero_returns

        # 排序
        sorted_data = np.sort(plot_data)
        # 计算分位点（使用(i-0.5)/n计算)
        quantiles = np.arange(1, n + 1) / (n + 1)
        # 计算理论分位数
        theoretical_quantiles = stats.norm.ppf(quantiles)

        fig.add_trace(
            go.Scatter(
                x=theoretical_quantiles,
                y=sorted_data,
                mode='markers',
                name='QQ图',
                marker=dict(size=6, color='blue', opacity=0.7)
            ),
            row=1, col=2
        )

        # 添加对角线
        min_val = min(theoretical_quantiles.min(), sorted_data.min())
        max_val = max(theoretical_quantiles.max(), sorted_data.max())

        fig.add_trace(
            go.Scatter(
                x=[min_val, max_val],
                y=[min_val, max_val],
                mode='lines',
                name='正态参考线',
                line=dict(color='red', dash='dash', width=2)
            ),
            row=1, col=2
        )

        # 计算统计指标
        mean_return = non_zero_returns.mean()
        median_return = non_zero_returns.median()
        std_return = non_zero_returns.std()
        skew = stats.skew(non_zero_returns)
        kurtosis = stats.kurtosis(non_zero_returns)
        
        # 计算盈亏交易数量
        close_trades['profit'] = close_trades['return'] > 0
        profit_count = close_trades['profit'].sum()
        loss_count = len(close_trades) - profit_count
        win_rate = profit_count / len(close_trades)
        
        # 计算盈亏比
        win_p=close_trades[close_trades['return']>0]['return'].abs().mean()
        loss_p=close_trades[close_trades['return']<0]['return'].abs().mean()
        win_loss_ratio=win_p/loss_p

        # 添加统计信息注释
        stats_text = (
            f"平均收益率: {mean_return:.2%} | "
            f"中位收益率: {median_return:.2%} | "
            f"收益率标准差: {std_return:.2%} | "
            f"偏度: {skew:.2f} | "
            f"峰度: {kurtosis:.2f}<br>"
            f"胜率: {win_rate:.2%} | "
            f"平均盈利: {win_p:.2%} | "
            f"平均亏损: {loss_p:.2%} | "
            f"盈亏比: {win_loss_ratio:.2f} | "
            f"总平仓次数: {len(close_trades)} | "
            f"总交易次数: {len(trades_data)}"
        )

        fig.add_annotation(
            xref="paper", yref="paper",
            text=stats_text,
            x=0.5,
            y=0.5,  
            showarrow=False,
            font=dict(size=12),
            align="center",
        )
        
        # 根据持仓时间分组
        close_trades['holding_period_group'] = pd.cut(close_trades['holding_period'], bins=5, labels=['<5s', '5-10s', '10-15s', '15-20s', '>20s'])
        # 计算每组胜率和盈亏比
        holding_period_groups = close_trades.groupby('holding_period_group')
        # 计算每组的胜率
        win_rate_by_group = holding_period_groups['profit'].mean()
        # 计算每组的数量
        count_by_group = holding_period_groups['return'].count()

        # 计算每组的盈亏比，需要先分别计算每组的平均盈利和平均亏损
        avg_profit_by_group = holding_period_groups.apply(
            lambda x: x[x['return'] > 0]['return'].abs().mean() if len(x[x['return'] > 0]) > 0 else 0
        )
        avg_loss_by_group = holding_period_groups.apply(
            lambda x: x[x['return'] < 0]['return'].abs().mean() if len(x[x['return'] < 0]) > 0 else float('inf')
        )
        
        # 计算盈亏比，处理除以零的情况
        win_loss_ratio_by_group = avg_profit_by_group / avg_loss_by_group.replace(0, float('inf'))
        win_loss_ratio_by_group = win_loss_ratio_by_group.replace(float('inf'), 0)
        # 绘制持仓时间分布和胜率盈亏比
        fig.add_trace(
            go.Bar(x=win_rate_by_group.index, y=count_by_group,
                   name='持仓分布',
                   text=[f'数量{count_by_group[i]}<br>胜率{win_rate_by_group[i]:.2%}<br>盈亏比{win_loss_ratio_by_group[i]:.2f}' for i in range(len(win_loss_ratio_by_group))],
                   textposition='auto'),
            row=2, col=1
        )
        
        # 平仓盈利与亏损次数
        fig.add_trace(
            go.Bar(x=[f'盈利 {win_p:.2}', f'亏损 {loss_p:.2}'],
                   y=[profit_count, loss_count],
                   marker_color=['green', 'red'],
                   name='盈亏平仓次数',
                   text=[f'{profit_count} ({win_rate:.1%})', f'{loss_count} ({1 - win_rate:.1%})'],
                   textposition='auto'),
            row=2, col=2
        )

        # 更新布局
        fig.update_layout(
            title_text=title or '交易分析',
            showlegend=False,
            grid={'rows': 2, 'columns': 2, 'pattern': "independent"}
        )

        # 更新坐标轴标签
        fig.update_xaxes(title_text="收益", row=1, col=1)
        fig.update_xaxes(title_text="理论分位数", row=1, col=2)
        fig.update_xaxes(title_text="持仓时间 (s)", row=2, col=1)
        fig.update_xaxes(title_text="交易类型", row=2, col=2)

        fig.update_yaxes(title_text="概率密度", row=1, col=1)
        fig.update_yaxes(title_text="样本分位数", row=1, col=2)
        fig.update_yaxes(title_text="频率", row=2, col=1)
        fig.update_yaxes(title_text="平仓次数", row=2, col=2)

        fig.show()
        trade_path = os.path.join(self.plots_dir, 'trade_analysis.html')
        fig.write_html(trade_path)

    def plot_shap_values(self, shap_data, title=None, max_display=config.VISUALIZE_TOP_N):
        """
        绘制SHAP值图表，展示各特征对模型预测的贡献
        
        参数:
            shap_values (numpy.ndarray): SHAP值
            feature_data (pandas.DataFrame): 用于计算SHAP值的特征数据
            feature_names (list): 特征名列表
            title (str): 图表标题
            max_display (int): 最多显示的特征数量
        """
        if not SHAP_AVAILABLE:
            log_print("缺少shap库，无法绘制SHAP值图表", 'warning')
            return

        log_print("绘制SHAP值图表...")
        try:
            shap_values = shap_data['values']
            feature_data = shap_data['data']
            feature_names = shap_data['feature_names']
            
            # 创建SHAP总结图
            plt.figure()
            shap.summary_plot(shap_values, feature_data, feature_names=feature_names, 
                              max_display=max_display, show=False)
            
            # 设置标题
            title = title or 'SHAP值分析'
            plt.title(title)
            plt.tight_layout()
            
            # 保存图表
            shap_path = os.path.join(self.plots_dir, 'shap_values.png')
            plt.savefig(shap_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            log_print(f"SHAP值图表已保存至 {shap_path}")

        except Exception as e:
            log_print(f"绘制SHAP值图表失败: {e}")
    
    def plot_shap_summary(self, shap_importance_df, title=None, top_n=config.VISUALIZE_TOP_N):
        """
        绘制SHAP重要性条形图
        
        参数:
            shap_importance_df (pandas.DataFrame): 包含'feature'和'importance'列的DataFrame
            title (str): 图表标题
            top_n (int): 显示前N个重要特征
        """
        # 检查必要的列是否存在
        required_cols = ['feature', 'importance']
        missing_cols = [col for col in required_cols if col not in shap_importance_df.columns]
        if missing_cols:
            log_print(f"数据缺少必要的列: {missing_cols}", 'warning')
            return
        
        log_print("绘制SHAP重要性条形图...")
        
        # 选择前N个重要特征
        df = shap_importance_df.copy()
        if len(df) > top_n:
            df = df.head(top_n)
        
        # 创建图形
        fig = go.Figure()
        
        # 添加条形图
        df = df.sort_values(by='importance', ascending=True)
        fig.add_trace(
            go.Bar(x=df['importance'],
                   y=df['feature'],
                   orientation='h',
                   marker_color='#00AFBB',
                   name='SHAP重要性')
        )
        
        # 更新布局
        fig.update_layout(
            title_text=title or 'SHAP特征重要性',
            xaxis_title="平均|SHAP值|",
            yaxis_title="特征",
            showlegend=False
        )
        
        fig.show()
        
        # 保存图表
        importance_path = os.path.join(self.plots_dir, 'shap_importance.html')
        fig.write_html(importance_path)
        
        log_print(f"SHAP重要性条形图已保存至 {importance_path}")
    
    def plot_shap_dependence(self, shap_data, feature,
                           interaction_idx="auto", title=None):
        """
        绘制SHAP依赖图，展示单个特征与其SHAP值的关系
        
        参数:
            shap_values (numpy.ndarray): SHAP值
            feature_data (pandas.DataFrame): 用于计算SHAP值的特征数据
            feature_idx (int or str): 要分析的特征索引或名称
            feature_names (list): 特征名列表
            interaction_idx (str or int): 交互特征索引或名称，"auto"为自动选择
            title (str): 图表标题
        """
        if not SHAP_AVAILABLE:
            log_print("缺少shap库，无法绘制SHAP依赖图", 'warning')
            return
        
        shap_values = shap_data['values']
        feature_data = shap_data['data']
        feature_names = shap_data['feature_names']
        feature_idx = feature_names.index(feature)
        
        
        log_print(f"绘制特征'{feature_idx}'的SHAP依赖图...")
        
        try:
            
            # 创建SHAP依赖图
            feature_name = feature_names[feature_idx] if feature_names is not None else str(feature_idx)
            
            # 使用shap的依赖图，注意dependence_plot不直接返回图形对象
            # 而是在当前活动的matplotlib图形上绘制
            shap.dependence_plot(
                feature_idx, 
                shap_values, 
                feature_data, 
                feature_names=feature_names, 
                interaction_index=interaction_idx,
                show=True
            )
            
            # 设置标题
            title = title or f'特征"{feature_name}"的SHAP依赖分析'
            plt.title(title)
            
            # 调整布局并显示
            plt.tight_layout()
            
            # 保存图表
            safe_feature_name = "".join([c if c.isalnum() else "_" for c in feature_name])
            shap_path = os.path.join(self.plots_dir, f'shap_dependence_{safe_feature_name}.png')
            plt.savefig(shap_path, dpi=300, bbox_inches='tight')
            plt.close()
            log_print(f"SHAP依赖图已保存至 {shap_path}")
            
        except Exception as e:
            log_print(f"绘制SHAP依赖图失败: {e}")
