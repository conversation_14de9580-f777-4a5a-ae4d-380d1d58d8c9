# -*- coding:utf-8 -*-
import pyodbc
import pandas as pd
import numpy as np
from WindPy import w
from pandas import DataFrame
import datetime

# import sys
# reload(sys)
# sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
table = "[Alex].[dbo].[Future_M]"
under = "M.DCE"

# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
cursor = conn.cursor()

# Create a cursor from the connection
# cursor = cnxn.cursor()

w.start()

import sys
sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')
from SQL.windtosql.update import datemakerDay
indexnum = 1
dt, beginDate, lastday = datemakerDay.datemaker(table, conn, cursor, indexnum)

futuredata = w.wsd(under, "trade_hiscode", beginDate, dt, "PriceAdj=F")
print("\n\n-%s" % futuredata)


def codespre0():
    codesdict = {}

    futuredata2 = w.wsd(under, "trade_hiscode", lastday, dt, "PriceAdj=F")
    print(futuredata2.ErrorCode)
    df = DataFrame({"times": futuredata2.Times, "codes": futuredata2.Data[0]})

    print(futuredata2)
    print(df)

    for i in range(1, len(df)):
        if df.values[i][0] != df.values[i - 1][0]:
            codesdict[df.values[i][0]] = df.values[i - 1][1]

    for k, v in codesdict.items():
        date1 = v
        fcode = k
        wsddata1 = w.wsd(fcode, "open,high,low,close,volume,amt,dealnum,oi,lasttrade_date,dlmonth,lastdelivery_date",
                         "ED-100TD", date1, "PriceAdj=F")  # 前推100个交易日
        for i in range(0, len(wsddata1.Data[1])):
            sqllist = []

            sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))

            sqllist.append(str(fcode))

            for k in range(0, len(wsddata1.Fields)):
                sqllist.append(wsddata1.Data[k][i])

            sqllist[10] = sqllist[10].strftime('%Y-%m-%d')
            sqllist[12] = sqllist[12].strftime('%Y-%m-%d')
            sqllist.append(0)
            # print(sqllist)

            sqltuple = tuple(sqllist)

            sqllist2.append(sqltuple)

# codes=pf.index.values.tolist()


def update():
    # codes=pf.index.values.tolist()
    for i in range(0, len(futuredata.Data[0])):
        date1 = futuredata.Times[i]
        fcode = futuredata.Data[0][i]
        wdata = w.wss(fcode, "open,high,low,close,volume,amt,dealnum,oi,lasttrade_date,dlmonth,lastdelivery_date",
                      "tradeDate=%s;priceAdj=U;cycle=D" % date1)

        sqllist = []

        sqllist.append(date1.strftime('%Y-%m-%d'))

        sqllist.append(str(fcode))

        for k in range(0, len(wdata.Fields)):
            sqllist.append(wdata.Data[k][0])

        sqllist[10] = sqllist[10].strftime('%Y-%m-%d')
        sqllist[12] = sqllist[12].strftime('%Y-%m-%d')
        sqllist.append(1)
        print(sqllist)

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)


sql2 = "INSERT INTO %s VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)" % table

sqllist2 = []
codespre0()
update()

cursor.executemany(sql2, sqllist2)

conn.commit()

conn.close()
