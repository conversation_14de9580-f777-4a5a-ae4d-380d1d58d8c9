from vnpy.event import EventEngine, Event
from vnpy.trader.engine import MainEngine
from PySide6 import QtWidgets, QtCore
from vnpy.trader.event import EVENT_TICK
from vnpy.trader.object import TickData, OrderRequest, Direction, Offset, Status, SubscribeRequest, OrderType
from vnpy.trader.constant import Exchange

class FiveLevelOrderWidget(QtWidgets.QDockWidget):
    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        super().__init__("五档下单")
        
        self.main_engine = main_engine
        self.event_engine = event_engine
        
        self.symbol = ""
        self.exchange = None
        self.gateway_name = ""
        
        self.ask_prices = []
        self.ask_volumes = []
        self.bid_prices = []
        self.bid_volumes = []
        
        self.init_ui()
        self.register_event()
        
    def init_ui(self):
        widget = QtWidgets.QWidget()
        
        # 创建交易对选择控件
        symbol_label = QtWidgets.QLabel("交易对:")
        self.symbol_line = QtWidgets.QLineEdit()
        self.exchange_combo = QtWidgets.QComboBox()
        self.exchange_combo.addItems([e.value for e in Exchange])
        self.subscribe_button = QtWidgets.QPushButton("订阅")
        self.subscribe_button.clicked.connect(self.subscribe_symbol)
        
        # 添加网关选择下拉框
        self.gateway_combo = QtWidgets.QComboBox()
        self.gateway_combo.addItems(self.main_engine.get_all_gateway_names())
        symbol_layout = QtWidgets.QHBoxLayout()
        symbol_layout.addWidget(symbol_label)
        symbol_layout.addWidget(self.symbol_line)
        symbol_layout.addWidget(self.exchange_combo)
        symbol_layout.addWidget(self.subscribe_button)
        symbol_layout.addWidget(QtWidgets.QLabel("网关:"))
        symbol_layout.addWidget(self.gateway_combo)
        
        # 添加自动更新按钮
        self.auto_update_button = QtWidgets.QPushButton("自动更新")
        self.auto_update_button.setCheckable(True)
        self.auto_update_button.clicked.connect(self.toggle_auto_update)
        symbol_layout.addWidget(self.auto_update_button)
        
        # 创建五档行情显示和下单按钮
        self.price_labels = []
        self.volume_labels = []
        self.order_buttons = []
        
        grid_layout = QtWidgets.QGridLayout()
        
        for i in range(5):
            price_label = QtWidgets.QLabel(f"卖{5-i}:")
            volume_label = QtWidgets.QLabel("0")
            buy_button = QtWidgets.QPushButton("买入")
            buy_button.clicked.connect(lambda _, row=i: self.send_order(Direction.LONG, row))
            
            self.price_labels.append(price_label)
            self.volume_labels.append(volume_label)
            self.order_buttons.append(buy_button)
            
            grid_layout.addWidget(price_label, i, 0)
            grid_layout.addWidget(volume_label, i, 1)
            grid_layout.addWidget(buy_button, i, 2)
        
        for i in range(5):
            price_label = QtWidgets.QLabel(f"买{i+1}:")
            volume_label = QtWidgets.QLabel("0")
            sell_button = QtWidgets.QPushButton("卖出")
            sell_button.clicked.connect(lambda _, row=i: self.send_order(Direction.SHORT, row))
            
            self.price_labels.append(price_label)
            self.volume_labels.append(volume_label)
            self.order_buttons.append(sell_button)
            
            grid_layout.addWidget(price_label, i+5, 0)
            grid_layout.addWidget(volume_label, i+5, 1)
            grid_layout.addWidget(sell_button, i+5, 2)
        
        # 主布局
        main_layout = QtWidgets.QVBoxLayout()
        main_layout.addLayout(symbol_layout)
        main_layout.addLayout(grid_layout)
        
        widget.setLayout(main_layout)
        
        self.setWidget(widget)
        
    def register_event(self):
        self.event_engine.register(EVENT_TICK, self.process_tick_event)
        # 注册选中交易对变化的事件
        self.event_engine.register("EVENT_SYMBOL_SELECTED", self.update_symbol)

    def toggle_auto_update(self):
        if self.auto_update_button.isChecked():
            self.update_symbol()
        else:
            # 停止自动更新
            pass

    def update_symbol(self, event=None):
        if self.auto_update_button.isChecked():
            symbol, exchange = self.main_engine.get_selected_symbol()
            if symbol and exchange:
                self.symbol = symbol
                self.exchange = exchange
                self.symbol_line.setText(symbol)
                self.exchange_combo.setCurrentText(exchange.value)
                self.subscribe_symbol()

    def process_tick_event(self, event: Event):
        tick: TickData = event.data
        if tick.symbol == self.symbol and tick.exchange == self.exchange:
            self.update_tick(tick)
            
    def update_tick(self, tick: TickData):
        self.ask_prices = [tick.ask_price_5, tick.ask_price_4, tick.ask_price_3, tick.ask_price_2, tick.ask_price_1]
        self.ask_volumes = [tick.ask_volume_5, tick.ask_volume_4, tick.ask_volume_3, tick.ask_volume_2, tick.ask_volume_1]
        self.bid_prices = [tick.bid_price_1, tick.bid_price_2, tick.bid_price_3, tick.bid_price_4, tick.bid_price_5]
        self.bid_volumes = [tick.bid_volume_1, tick.bid_volume_2, tick.bid_volume_3, tick.bid_volume_4, tick.bid_volume_5]
        
        for i in range(5):
            self.price_labels[i].setText(f"{self.ask_prices[4-i]:.4f}")
            self.volume_labels[i].setText(str(self.ask_volumes[4-i]))
            
        for i in range(5, 10):
            self.price_labels[i].setText(f"{self.bid_prices[i-5]:.4f}")
            self.volume_labels[i].setText(str(self.bid_volumes[i-5]))
            
    def subscribe_symbol(self):
        self.symbol = self.symbol_line.text()
        self.exchange = Exchange(self.exchange_combo.currentText())
        self.gateway_name = self.gateway_combo.currentText()
        
        req = SubscribeRequest(
            symbol=self.symbol,
            exchange=self.exchange
        )
        self.main_engine.subscribe(req, self.gateway_name)
        
    def send_order(self, direction: Direction, row: int):
        price = self.ask_prices[4-row] if direction == Direction.LONG else self.bid_prices[row]
        volume = 1  # 这里可以添加数量输入框
        
        req = OrderRequest(
            symbol=self.symbol,
            exchange=self.exchange,
            direction=direction,
            type=OrderType.LIMIT,
            volume=volume,
            price=price,
            offset=Offset.OPEN,  # 这里可以添加开平选择
        )
        
        self.main_engine.send_order(req, self.gateway_name)

# 其他必要的方法和逻辑...
