import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta,time as dtime
 
class PostTradeAnalysis:
 
    def __init__(self, engine , multiplier):
        self.engine = engine
        self.multiplier = multiplier
 
    def mkt_calculate(self):
            multiplier = self.multiplier
            engine = self.engine
            trades = pd.DataFrame([{'time':(engine.trades[x].datetime).timestamp(),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
           'midPrice':engine.trades[x].midPrice} for x in engine.trades])
            
            trades.index = trades['time']
            trades = trades.sort_index()
            
            mkt = pd.DataFrame(engine.history_data.values())
            
            mkt['time'] = mkt['datetime'].apply(lambda x: x.timestamp())
            mkt.index = mkt['time']
            mkt = mkt.sort_index()
            mkt['mid_price'] = (mkt['bid_price_1'] + mkt['ask_price_1']) / 2
            
            insid_list = mkt['symbol'].drop_duplicates()
            
            net_list = {insid:0 for insid in insid_list}
            pnl_list = {insid:0 for insid in insid_list}
            askamount_list = {insid:0 for insid in insid_list}
            bidamount_list = {insid:0 for insid in insid_list}
            buyprice_list = {insid:np.nan for insid in insid_list}
            shortprice_list = {insid:np.nan for insid in insid_list} 
            turnover_list = {insid:0 for insid in insid_list}
            
            net_total = np.sum([net_list[insid] for insid in net_list.keys()])
            pnl_total = np.sum([pnl_list[insid] for insid in pnl_list.keys()])
                
            mkt_calculate = []
            
            mkt = mkt.to_dict('records')
            
            trades = trades.to_dict('records')
                
            i, j = 0, 0
            
            row = mkt[0].copy()
            
            while True:
                if i >= len(mkt) and j >= len(trades):
                    break
                elif i >= len(mkt):
                    j += 1
                elif j >= len(trades):
                    insid = mkt[i]['symbol']
                    
                    row = mkt[i].copy()
                    pnl_list[insid] = (askamount_list[insid]-bidamount_list[insid]+net_list[insid]*(row['bid_price_1']+row['ask_price_1'])*multiplier/2) - turnover_list[insid] * engine.rates[[x for x in engine.vt_symbols if insid in x][0]]
                    net_total = np.sum([net_list[insid] for insid in net_list.keys()])
                    pnl_total = np.sum([pnl_list[insid] for insid in pnl_list.keys()])
        
                    row['net'] = net_list[insid]
                    row['pnl'] = pnl_list[insid]
                    row['net_total'] = net_total
                    row['pnl_total'] = pnl_total
                    row['buyprice'] = buyprice_list[insid]
                    row['shortprice'] = shortprice_list[insid]
                    mkt_calculate.append(row.copy())
                    buyprice_list[insid] = np.nan
                    shortprice_list[insid] = np.nan                
                    i += 1
                elif mkt[i]['time'] >= trades[j]['time']:
                    insid = trades[j]['symbol']
                    direction = trades[j]['direction']
                    price = trades[j]['price']
                    volume = trades[j]['volume']   
                    turnover_list[insid] += price*volume*multiplier
                    askamount_list[insid] += price*volume*multiplier if direction < 0 else 0
                    bidamount_list[insid] += price*volume*multiplier if direction > 0 else 0       
                    net_list[insid] += direction*volume
                    pnl_list[insid] = (askamount_list[insid]-bidamount_list[insid]+net_list[insid]*price*multiplier) - turnover_list[insid] * engine.rates[[x for x in engine.vt_symbols if insid in x][0]]
 
                    if insid == engine.strategies[0].maker.split('.')[0]:
                        if len(engine.vt_symbols) != 1:
                            if trades[j]['direction'] == 1:
                                buyprice_list[insid] = trades[j]['midPrice'] - trades[j]['basePrice']
                            if trades[j]['direction'] == -1:
                                shortprice_list[insid] = trades[j]['midPrice'] - trades[j]['basePrice']          
                        else:
                            if trades[j]['direction'] == 1:
                                buyprice_list[insid] = trades[j]['price']
                            if trades[j]['direction'] == -1:
                                shortprice_list[insid] = trades[j]['price']              
                    j += 1
                    
                elif mkt[i]['time'] < trades[j]['time']:
                    insid = mkt[i]['symbol']
                    row = mkt[i].copy()
                    pnl_list[insid] = (askamount_list[insid]-bidamount_list[insid]+net_list[insid]*(row['bid_price_1']+row['ask_price_1'])*multiplier/2) - turnover_list[insid] * engine.rates[[x for x in engine.vt_symbols if insid in x][0]]
                    net_total = np.sum([net_list[insid] for insid in net_list.keys()])
                    pnl_total = np.sum([pnl_list[insid] for insid in pnl_list.keys()])
        
                    row['net'] = net_list[insid]
                    row['pnl'] = pnl_list[insid]
                    row['net_total'] = net_total
                    row['pnl_total'] = pnl_total
                    row['buyprice'] = buyprice_list[insid]
                    row['shortprice'] = shortprice_list[insid]
                    mkt_calculate.append(row.copy())   
                    buyprice_list[insid] = np.nan
                    shortprice_list[insid] = np.nan
                    i += 1         
                
            mkt_calculate = pd.DataFrame(mkt_calculate)
 
            # mkt_calculate.index = mkt_calculate.time
            # mkt_calculate = mkt_calculate.sort_index()
            return mkt_calculate

    def mkt_calculate_sp(self):
            multiplier = self.multiplier
            engine = self.engine
            trades = pd.DataFrame([{'time':(engine.trades[x].datetime).timestamp(),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
           'midPrice':engine.trades[x].midPrice} for x in engine.trades])
            
            trades.index = trades['time']
            trades = trades.sort_index()
            
            mkt = pd.DataFrame(engine.history_data.values())
            mkt = mkt[mkt.symbol==engine.sp_contract.split('.')[0]]
            
            mkt['time'] = mkt['datetime'].apply(lambda x: x.timestamp())
            mkt.index = mkt['time']
            mkt = mkt.sort_index()
            mkt['mid_price'] = (mkt['bid_price_1'] + mkt['ask_price_1']) / 2
            
            insid_list = mkt['symbol'].drop_duplicates()
            
            net_list = {insid:0 for insid in insid_list}
            pnl_list = {insid:0 for insid in insid_list}
            askamount_list = {insid:0 for insid in insid_list}
            bidamount_list = {insid:0 for insid in insid_list}
            buyprice_list = {insid:np.nan for insid in insid_list}
            shortprice_list = {insid:np.nan for insid in insid_list} 
            turnover_list = {insid:0 for insid in insid_list}
            
            net_total = np.sum([net_list[insid] for insid in net_list.keys()])
            pnl_total = np.sum([pnl_list[insid] for insid in pnl_list.keys()])
                
            mkt_calculate = []
            
            mkt = mkt.to_dict('records')
            
            trades = trades.to_dict('records')
                
            i, j = 0, 0
            
            row = mkt[0].copy()
            
            while True:
                if i >= len(mkt) and j >= len(trades):
                    break
                elif i >= len(mkt):
                    j += 1
                elif j >= len(trades):
                    insid = mkt[i]['symbol']
                    
                    row = mkt[i].copy()
                    pnl_list[insid] = (askamount_list[insid]-bidamount_list[insid]+net_list[insid]*(row['bid_price_1']+row['ask_price_1'])*multiplier/2) 
                    net_total = np.sum([net_list[insid] for insid in net_list.keys()])
                    pnl_total = np.sum([pnl_list[insid] for insid in pnl_list.keys()])
        
                    row['net'] = net_list[insid]
                    row['pnl'] = pnl_list[insid]
                    row['net_total'] = net_total
                    row['pnl_total'] = pnl_total
                    row['buyprice'] = buyprice_list[insid]
                    row['shortprice'] = shortprice_list[insid]
                    mkt_calculate.append(row.copy())
                    buyprice_list[insid] = np.nan
                    shortprice_list[insid] = np.nan                
                    i += 1
                elif mkt[i]['time'] >= trades[j]['time']:
                    insid = trades[j]['symbol']
                    direction = trades[j]['direction']
                    price = trades[j]['price']
                    volume = trades[j]['volume']   
                    turnover_list[insid] += price*volume*multiplier
                    askamount_list[insid] += price*volume*multiplier if direction < 0 else 0
                    bidamount_list[insid] += price*volume*multiplier if direction > 0 else 0       
                    net_list[insid] += direction*volume
                    pnl_list[insid] = (askamount_list[insid]-bidamount_list[insid]+net_list[insid]*price*multiplier)
                    if trades[j]['direction'] == 1:
                        buyprice_list[insid] = trades[j]['price']
                    if trades[j]['direction'] == -1:
                        shortprice_list[insid] = trades[j]['price']              
                    j += 1
                    
                elif mkt[i]['time'] < trades[j]['time']:
                    insid = mkt[i]['symbol']
                    row = mkt[i].copy()
                    pnl_list[insid] = (askamount_list[insid]-bidamount_list[insid]+net_list[insid]*(row['bid_price_1']+row['ask_price_1'])*multiplier/2)
                    net_total = np.sum([net_list[insid] for insid in net_list.keys()])
                    pnl_total = np.sum([pnl_list[insid] for insid in pnl_list.keys()])
        
                    row['net'] = net_list[insid]
                    row['pnl'] = pnl_list[insid]
                    row['net_total'] = net_total
                    row['pnl_total'] = pnl_total
                    row['buyprice'] = buyprice_list[insid]
                    row['shortprice'] = shortprice_list[insid]
                    mkt_calculate.append(row.copy())   
                    buyprice_list[insid] = np.nan
                    shortprice_list[insid] = np.nan
                    i += 1         
                
            mkt_calculate = pd.DataFrame(mkt_calculate)
 
            # mkt_calculate.index = mkt_calculate.time
            # mkt_calculate = mkt_calculate.sort_index()
            return mkt_calculate
    
    def plot_equity(self):
        df = self.mkt_calculate()
        N = df.shape[0]
        X_axis = range(N)
        lw = 1
        plt.figure(1,dpi=100)
        plt.plot(X_axis,df['pnl_total'],label="equity",linewidth=lw)
        plt.title('equity')
        plt.legend()
        plt.figure(2,dpi=100)
        plt.plot(X_axis,df['net'],label='running position',linewidth=lw)
        plt.legend()
        plt.title('pos')
        plt.figure(3,dpi=100)
        plt.plot(X_axis,df['fair_price'],label='fair_price',linewidth=lw)
        plt.legend()
        plt.title('market price')
        plt.figure(4,dpi=100)
        dotsize = 40
        plt.scatter(X_axis,df['buyprice'],c='r',s=dotsize)
        plt.scatter(X_axis,df['shortprice'],c='g',s=dotsize)
        plt.plot(X_axis,df['fair_price'],label='Mid',linewidth=lw)
        plt.title('buysellpoints')

    def plot_equity_sp(self):
        df = self.mkt_calculate_sp()
        N = df.shape[0]
        X_axis = range(N)
        lw = 1
        plt.figure(1,dpi=100)
        plt.plot(X_axis,df['pnl_total'],label="equity",linewidth=lw)
        plt.title('equity')
        plt.legend()
        plt.figure(2,dpi=100)
        plt.plot(X_axis,df['net'],label='running position',linewidth=lw)
        plt.legend()
        plt.title('pos')
        plt.figure(3,dpi=100)
        plt.plot(X_axis,df['fair_price'],label='fair_price',linewidth=lw)
        plt.legend()
        plt.title('market price')
        plt.figure(4,dpi=100)
        dotsize = 40
        plt.scatter(X_axis,df['buyprice'],c='r',s=dotsize)
        plt.scatter(X_axis,df['shortprice'],c='g',s=dotsize)
        plt.plot(X_axis,df['fair_price'],label='Mid',linewidth=lw)
        plt.title('buysellpoints')
 
    def plot_spd_meanreversion(self):
        df = self.mkt_calculate()
        engine = self.engine
        df = df[df.symbol == engine.strategies[0].maker.split('.')[0]]
        offer = pd.DataFrame(engine.strategies[0].offer_list)
        offer.columns = engine.strategies[0].offer_list_head
        df = pd.merge(df,offer,on = 'datetime',how = 'outer')
        N = df.shape[0]
        X_axis = range(N)
        lw = 1
        plt.figure(1,dpi=100)
        plt.plot(X_axis,df['MovingAverage'],label='MovingAverage',linewidth=2*lw)
        plt.plot(X_axis,df['fair_price'],label='Basis',linewidth=lw*0.1)
        plt.legend()
        plt.ylabel('basis')
        plt.title('price series')
        plt.figure(2,dpi=100)
        plt.plot(X_axis,df['pnl_total'],label="equity",linewidth=lw)
        plt.title('equity')
        plt.legend()
        plt.ylabel('$')
        plt.figure(3,dpi=100)
        plt.plot(X_axis,df['net'],label='running position',linewidth=lw)
        plt.legend()
        plt.title('pos')  
        plt.figure(4,dpi=100)
        dotsize = 40
        plt.scatter(X_axis,df['buyprice'],c='r',s=dotsize)
        plt.scatter(X_axis,df['shortprice'],c='g',s=dotsize)
        plt.plot(X_axis,df['fair_price'],label='Mid',linewidth=lw)
        plt.title('buysellpoints')
        plt.figure(5,dpi=100)
        plt.plot(X_axis,df['net_total'],label='net',linewidth=lw)
        plt.title('net_toal')
 
    def plotbacktestresults_meanreversion(self):
        df = self.mkt_calculate()
        engine = self.engine
        df = df[df.symbol == engine.strategies[0].maker.split('.')[0]]
        offer = pd.DataFrame(engine.strategies[0].offer_list)
        offer.columns = engine.strategies[0].offer_list_head
        df = pd.merge(df,offer,on = 'datetime',how = 'outer')
        N = df.shape[0]
        X_axis = range(N)
        lw = 1
 
        #画出布林带以及无套利区间
        plt.figure(1,dpi=100)
        plt.plot(X_axis,df['MovingAverage'],label='MovingAverage',linewidth=2*lw)
        plt.plot(X_axis,df['Basis'],label='Basis',linewidth=lw*0.1)
        plt.plot(X_axis,df['bollup'],label = 'bollup',linewidth=lw)
        plt.plot(X_axis,df['bolldown'],label='bolldown',linewidth=lw)
        plt.legend()
        plt.ylabel('basis')
        plt.title('price series')
        plt.figure(2,dpi=100)
        plt.plot(X_axis,df['pnl_total'],label="equity",linewidth=lw)
        plt.title('equity')
        plt.legend()
        plt.ylabel('$')
        plt.figure(3,dpi=100)
        plt.plot(X_axis,df['net'],label='running position',linewidth=lw)
        plt.legend()
        plt.title('pos')
        plt.figure(4,dpi=100)
        plt.plot(X_axis,df['Std'],label='std',linewidth=lw)
        plt.legend()
        plt.title('std')    
        plt.figure(5,dpi=100)
        dotsize = 40
        plt.scatter(X_axis,df['buyprice'],c='r',s=dotsize)
        plt.scatter(X_axis,df['shortprice'],c='g',s=dotsize)
        plt.plot(X_axis,df['Basis'],label='Mid',linewidth=lw)
        plt.title('buysellpoints')
        plt.figure(6,dpi=100)
        plt.plot(X_axis,df['net_total'],label='net',linewidth=lw)
        plt.title('net')
 
        #画出execution_pnl的图
        # trades = pd.DataFrame([{'datetime':(engine.trades[x].datetime),
        #    'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
        #    'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
        #    'midPrice':engine.trades[x].midPrice} for x in engine.trades])
        
        # mkt = pd.DataFrame(engine.history_data.values())
        # mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
        #         'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
        #         'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
        #         'last_price','volume','turnover']]
        # mkt_main = mkt[mkt.symbol==engine.strategies[0].refer.split('.')[0]]
        
        # multiplier = self.multiplier
 
        # execution_pnl_next_list = []
 
        # for i in trades.index:
        #     if trades.loc[i,'symbol']  == engine.strategies[0].maker.split('.')[0]:
        #         time = trades.loc[i,'datetime']
        #         price = trades.loc[i,'price']
        #         volume = trades.loc[i,'volume']
        #         direction = trades.loc[i,'direction']
        #         midPrice = trades.loc[i,'midPrice']
        #         basePrice = trades.loc[i,'basePrice']
        #         refer_next = mkt_main[mkt_main.datetime >= time].iloc[0]
        #         midPrice_next = (refer_next.bid_price_1 + refer_next.ask_price_1)/2
        #         exectuion_pnl_next = (price - midPrice) * -1 * direction * volume *multiplier + (midPrice_next - basePrice) * 1 * direction * volume * multiplier
        #         execution_pnl_next_list.append(exectuion_pnl_next)
 
        # plt.figure(7,dpi=100)
        # plt.hist(execution_pnl_next_list,bins=30)
        # plt.title('exectuion_pnl_next')
 
        # print('理论执行盈亏:',sum(execution_pnl_next_list))
        # print('逐笔理论执行盈亏:',sum(execution_pnl_next_list)/len(execution_pnl_next_list))
 
        return df
 
    def plot_mkt_impact(self,slices=1):
        df = self.mkt_calculate()
        N = df.shape[0]
        X_axis = range(N)
        lw = 1
        plt.figure(1,dpi=100)
        plt.plot(X_axis,df['pnl_total'],label="equity",linewidth=lw)
        plt.title('equity')
        plt.legend()
        plt.figure(2,dpi=100)
        plt.plot(X_axis,df['net'],label='running position',linewidth=lw)
        plt.legend()
        plt.title('pos')
        plt.figure(3,dpi=100)
        dotsize = 40
        plt.scatter(X_axis,df['buyprice'],c='r',s=dotsize)
        plt.scatter(X_axis,df['shortprice'],c='g',s=dotsize)
        plt.plot(X_axis,df['fair_price'],label='Mid',linewidth=lw)
        plt.title('buysellpoints')
        plt.figure(4,dpi=100)        
        ts = self.engine.priceticks[self.engine.strategies[0].maker]
        df_market_impacts,df_realized_spreads = self.calculate_market_impact(slices,ts)
        plt.plot(df_market_impacts.mean())
        plt.plot(df_realized_spreads.mean())
        plt.plot(df_realized_spreads.std())
        plt.legend(['avg market impact','avg realized spread','std'])
        plt.title('Impact&RealizedSpread')
        plt.show()
 
    def calculate_market_impact(self,slices=1, ts=1):
        multiplier = self.multiplier
        engine = self.engine
        ts = engine.priceticks[engine.strategies[0].maker]
        trades = pd.DataFrame([{'datetime':(engine.trades[x].datetime.timestamp()),
        'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
        'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
        'midPrice':engine.trades[x].midPrice} for x in engine.trades])
        trades = trades[trades.symbol == engine.strategies[0].maker.split('.')[0]]    
        mkt = pd.DataFrame(engine.history_data.values())    
        mkt['datetime'] = mkt['datetime'].apply(lambda x: x.timestamp())
        mkt_maker = mkt[mkt.symbol == engine.strategies[0].maker.split('.')[0]]    
        trades_list = trades.to_dict('records')
        mkt_list = mkt_maker.to_dict('records')
        times = trades['datetime'].tolist()
        prices = trades['price'].tolist()
        directions = trades['direction'].tolist()
        df_market_impacts = []
        df_realized_spreads = []
        for time, trade_price, direction in zip(times, prices, directions):
            i = mkt_maker.index[mkt_maker['datetime'] == time][0]
            if i + slices + 1 < mkt_maker.shape[0]:
                array_slice = np.array([mkt_list[j]['fair_price'] for j in range(i-1, i+slices)])
                slice_market_impact = (array_slice - array_slice[0]) * direction / float(ts)
                slice_realized_spread = (array_slice - trade_price) * direction / float(ts)
                df_market_impacts.append(slice_market_impact)
                df_realized_spreads.append(slice_realized_spread)
                
        df_market_impacts = pd.DataFrame(df_market_impacts)
        df_realized_spreads = pd.DataFrame(df_realized_spreads)
 
        return [df_market_impacts,df_realized_spreads]