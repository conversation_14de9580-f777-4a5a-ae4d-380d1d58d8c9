# !/usr/bin/python
# -*- coding: utf-8 -*-
import json
import shutil
from ftplib import FTP
import datetime
import os
import zipfile
import xlwings as xw
# from WindPy import w
import pandas as pd


# import shutil


def ftpconnect(host, username, password, iii):
    ftp = FTP()
    # ftp.set_debuglevel(2)
    ftp.connect(host, iii)
    ftp.login(username, password)
    return ftp


# 从ftp下载文件
def downloadfile(ftp, remotepath, localpath):
    bufsize = 1024
    fp = open(localpath, 'wb')
    ftp.retrbinary('RETR ' + remotepath, fp.write, bufsize)
    ftp.set_debuglevel(0)
    fp.close()


# 从本地上传文件到ftp
def uploadfile(ftp, remotepath, localpath):
    bufsize = 1024
    fp = open(localpath, 'rb')
    ftp.storbinary('STOR ' + remotepath, fp, bufsize)
    ftp.set_debuglevel(0)
    fp.close()


# FTP 传输文件
def transfile(ftp, ftp2, path1, path2):
    sock1 = ftp.transfercmd("RETR " + path1)
    sock2 = ftp2.transfercmd("STOR " + path2)
    flen = 0
    while 1:
        block = sock1.recv(1024)
        if len(block) == 0:
            break
        flen += len(block)
        while len(block) > 0:
            sentlen = sock2.send(block)
            block = block[sentlen:]
    sock1.close()
    sock2.close()
    # try:
    #     transfile(ftp, ftp2, "idxdata/data/asharedata/%s/close_weight/%scloseweight%s.zip" % (code,code,dt),
    #              ftp2path + "%s\\close_weight\\%scloseweight%s.zip" % (code, code, dt))
    # except:
    #     ftp.mkd(ftp2path + "%s\\close_weight" % code)
    #     transfile(ftp, ftp2, "idxdata/data/asharedata/%s/weight_for_next_trading_day/%scloseweight%s.zip" % (code,code,dt),
    # ftp2path + "%s\\close_weight\\%scloseweight%s.zip" % (code, code, dt))


def zip(file_list):
    file_list = os.listdir(r'.')

    for file_name in file_list:
        if os.path.splitext(file_name)[1] == '.zip':
            print(file_name)
            file_zip = zipfile.ZipFile(file_name, 'r')
            for file in file_zip.namelist():
                file_zip.extract(file, r'.')
            file_zip.close()
            os.remove(file_name)


def zip2(file_name):
    path = os.path.abspath(os.path.join(file_name, "..")) + "\\"
    file_zip = zipfile.ZipFile(file_name, 'r')
    for file in file_zip.namelist():
        if os.path.splitext(file)[1] == '.xls':
            print(file, end=" ")
            file_zip.extract(file, path)
    file_zip.close()
    os.remove(file_name)
    file = path + "\\" + file_zip.namelist()[0]
    return file


def xlsgen(filename, newpath):
    # 打开 data.xlsx 文件到 wookbook 中
    wb = app.books.open(filename)

    wb2 = app.books.add()

    # wb = xw.Book(filename)
    sht = wb.sheets[0]
    sht2 = wb2.sheets[0]

    # sht2.range('A1').options(transpose=True).value = sht.range('A1').expand('down').options(convert=None).value
    # a = sht.range('E1').expand('down').options(convert=None).value
    # sht2.range((1, 2), (len(a), 2)).options(transpose=True).value = a
    # sht2.range('C1').options(transpose=True).value = sht.range('F1').expand('down').options(convert=None).value
    # sht2.range('D1').options(transpose=True).value = sht.range('Q1').expand('down').options(numbers=float).value

    # sht.range('A1').value = [u"日期"]
    # sht.range('E1').value = [u"代码"]
    # sht.range('F1').value = [u"证券名称"]

    # rng=sht.range(2, 5).expand()
    # nrows = rng.rows.count
    # sht.range(1, 4).api.EntireColumn.Delete()
    # sht.range(1, 3).api.EntireColumn.Delete()
    # sht.range(1, 2).api.EntireColumn.Delete()
    # sd = pd.DataFrame(sht.range(2, 5).expand().value, index=None,
    #                   columns=sht. range(1, 5).expand('right').value)
    sd = pd.DataFrame(sht.range(2, 1).expand().value, index=None,
                      columns=sht.range(1, 1).expand('right').value)
    # sd.loc[:, u"权重(%)\nWeight(%)"]="\'"++sd.loc[:, u"权重(%)\nWeight(%)"]
    sd.loc[:, u"成分券代码\nConstituent Code"] = sd[u"成分券代码\nConstituent Code"].apply(lambda x: "\'" + x)
    # sd2=sd.rename(columns={u"自由流通比例(%)(归档后)\nCategorized  Inclusion Factor(%)":u"自由流通比例(归档后)\nCategorized  Inclusion Factor",u"权重(%)\nWeight(%)":u"权重\nWeight"})
    # sd2=sd.loc[:, [u"生效日期\nEffective Date",u"成分券代码\nConstituent Code",u"成分券名称\nConstituent Name",u"计算用市值\nMarket Cap in Index",u"权重\nWeight",u"计算用股本(股)\nShares in Index(share)",u"收盘\nClose"]]
    # sd.loc[:, u"权重(%)\nWeight(%)"]
    sht2.range(1, 1).value = sd.loc[:, [u"生效日期\nEffective Date", u"成分券代码\nConstituent Code", u"成分券名称\nConstituent Name",
                                        u"权重(%)\nWeight(%)"]]
    sht2.range(1, 1).api.EntireColumn.Delete()
    # sht2.range('D1').value = [u"权重"]
    # sht.range(1, 5).expand().clear()
    # sht2.range(1, 1).expand().autofit()

    # for i in range(1, 19):
    #     # sht.range('B1:').api.EntireColumn.Delete()
    #     i = 20-i
    #     if i in [5, 6, 17]:
    #         continue
    #     sht.range(1, i).api.EntireColumn.Delete()

    wb.close()
    wb2.save(filename[:-4] + "new.xlsx")
    if newpath == 0:
        pass
    else:
        wb2.save(newpath)
        # 复制到交易网
        shutil.copy(filename[:-4] + "new.xlsx",
                    path2net2 + "\\weight\\new\\" + os.path.basename(newpath))


def main(dt):
    i = 0
    for code in codes:
        i += 1
        o = ftp.nlst("idxdata/data/asharedata/%s/weight_for_next_trading_day/%sweightnextday%s.zip" % (
            code, code, dt))  # 获取目录下的文件
        if len(o) == 0:
            print(dt + code + "文件还未更新")
            return

        print(str(i) + ":" + code)
        if not os.path.exists(path2net + "\\weight\\new\\"):
            os.makedirs(path2net + "\\weight\\new\\")
        for file in os.listdir(path2net + "\\weight\\new\\"):
            targetFile = os.path.join(path2net + "\\weight\\new\\", file)
            if os.path.isfile(targetFile) and file[:6] == code:
                os.remove(targetFile)

        if not os.path.exists(path2net2 + "\\weight\\new\\"):
            os.makedirs(path2net2 + "\\weight\\new\\")
        for file in os.listdir(path2net2 + "\\weight\\new\\"):
            targetFile = os.path.join(path2net2 + "\\weight\\new\\", file)
            if file[:6] == code:
                os.remove(targetFile)

        # if not os.path.exists(localpath + "weight\\%s\\weight_for_next_trading_day" % code):
        #     os.makedirs(localpath + "weight\\%s\\weight_for_next_trading_day" % code)
        # downloadfile(ftp,
        #             "idxdata/data/asharedata/%s/weight_for_next_trading_day/%sweightnextday%s.zip" % (code, code, dt),
        # localpath +
        # "weight\\%s\\weight_for_next_trading_day\\%sweightnextday%s.zip" %
        # (code, code, dt))

        if not os.path.exists(path2net + "weight\\%s\\weight_for_next_trading_day" % code):
            os.makedirs(
                path2net + "weight\\%s\\weight_for_next_trading_day" % code)

        downloadfile(ftp,
                     "idxdata/data/asharedata/%s/weight_for_next_trading_day/%sweightnextday%s.zip" % (
                         code, code, dt),
                     path2net + "weight\\%s\\weight_for_next_trading_day\\%sweightnextday%s.zip" % (code, code, dt))
        filexls = zip2(
            path2net + "weight\\%s\\weight_for_next_trading_day\\%sweightnextday%s.zip" % (code, code, dt))
        xlsgen(filexls, path2net + "\\weight\\new\\" + '%sweightnextday%snew.xlsx' % (code, dt))
        # shutil.copy(filexls[:-4] + "new.xlsx", )

        # if not os.path.exists(localpath + "weight\\%s\\close_weight" % code):
        #     os.makedirs(localpath + "weight\\%s\\close_weight" % code)
        # downloadfile(ftp, "idxdata/data/asharedata/%s/close_weight/%scloseweight%s.zip" % (code, code, dt),
        # localpath+"weight\\%s\\close_weight\\%scloseweight%s.zip" % (code,
        # code, dt))
        if not os.path.exists(path2net + "weight\\%s\\close_weight" % code):
            os.makedirs(path2net + "weight\\%s\\close_weight" % code)
        downloadfile(ftp, "idxdata/data/asharedata/%s/close_weight/%scloseweight%s.zip" % (code, code, dt),
                     path2net + "weight\\%s\\close_weight\\%scloseweight%s.zip" % (code, code, dt))
        filexls = zip2(
            path2net + "weight\\%s\\close_weight\\%scloseweight%s.zip" % (code, code, dt))
        xlsgen(filexls, 0)
        print("成功下载权重数据")

        # 调用本地播放器播放下载的视频
        # os.system('start "C:\Program Files\Windows Media Player\wmplayer.exe" "C:/Users/<USER>/Desktop/test.mp4"')
        # uploadfile(ftp, "C:/Users/<USER>/Desktop/test.mp4", "test.mp4")


def getpsw(classname,name):
    import json
    data = open(u"D:/onedrive/文档/package.json", encoding='utf-8')
    strJson = json.load(data)
    users = strJson[classname]
    for user in users:
        if name == user["name"]:
            return user


if __name__ == "__main__":
    try:
        localpath = "C:\\Users\\<USER>\\Desktop\\"
        path2net = "\\\\%s\\共享\\GR李宁\\" % getpsw("computer","officepublic")['ip']
        path2net2 = "\\\\%s\\share\\共享\\李宁\\" % getpsw("computer","NASoa")['ip']
        # path2net = "z:\\共享\\"
        # os.system('net use z:' + path2net)
        # path2net = 'c:' + path2net
        # a = (os.listdir(path2net))

        dts = datetime.datetime.now()
        # dts = "20190823"

        # w.start()
        # dts = w.tdays("2017-12-25", "2018-01-04", "").Data[0]

        app = xw.App(visible=False, add_book=False)
        app.display_alerts = False
        ftp = ftpconnect("**************", "csiqlzq", "12142799", 21)
        print("成功连接中证ftp")
        # ftp2 = ftpconnect("***********", "yscp", "yscp666", 135)
        # print("成功连接共享机")

        codes = ["000300", "000016", "000905"]

        if isinstance(dts, list):
            for dt in dts:
                dt = datetime.datetime.strftime(dt, "%Y%m%d")
                print("\n" + dt)
                main(dt)
        else:
            if isinstance(dts, datetime.datetime):
                dts = datetime.datetime.strftime(dts, "%Y%m%d")
            print("\n" + dts)
            main(dts)

        app.quit()
        ftp.quit()

        print("更新完毕")
    except Exception as e:
        print(e)
