#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('DCE.prod')
import warnings
warnings.filterwarnings('ignore')

""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""

"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.FutureMMStrategy_zew import FutureMMStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np


maker='pg2209.DCE'
refer='pg2208.DCE'

multiplier=20
ts = 1

engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=True,save_result=False,refer_test=False,fast_join=True,refer_late=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔

    start=datetime.datetime(2022,7, 6, 21, 0), # 开始时间
    end=datetime.datetime(2022, 7,7, 15, 0), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: ts,refer: ts}, # 一个tick大小
    capital=1_000_000, # 初始资金
)

# 添加回测策略，并修改内部参数
engine.clear_data()

edge = 6
lots = 3
validVolume = 5 * lots
maxPos = 3 * lots
minEdge = 4
Trend_Flag = False
engine.add_strategy(FutureMMStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':maxPos,'validVolume':validVolume,'minEdge':4,'Trend_Flag':Trend_Flag})

engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
engine.duty_statistics()

#%%
import pandas as pd
import numpy as np
from datetime import timedelta

trades = pd.DataFrame([{'time':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'insid':engine.trades[x].symbol+'.'+engine.trades[x].exchange.value,'comments':engine.trades[x].comments,
           'basePrice':engine.trades[x].basePrice,'midPrice':engine.trades[x].midPrice
           } for x in engine.trades])

trades['net'] = (trades.volume*trades.direction).cumsum()
cash = (trades['direction']*(-1)*trades['volume']*trades['price'])*multiplier
cashCum = np.cumsum(cash)
trades['pnl'] = cashCum + trades.net*trades.price*multiplier