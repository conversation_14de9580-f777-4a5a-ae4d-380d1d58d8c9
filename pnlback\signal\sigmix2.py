import datetime
import os
import sys
import pandas as pd
import traceback
from PySide6.QtCore import QMetaObject, Qt, QGenericArgument, QObject, Signal
import logging

sys.path.extend(
    [os.path.abspath(os.path.join(os.path.abspath(__file__) if '__file__' in globals() else os.getcwd(), *(['..'] * i)))
     for i in range(5)])

from app.backengie import Engine
from db_solve import Logger
from db_solve.configs import paths
import configs

pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)
pd.set_option('display.expand_frame_repr', False)


class Sigmix2:
    def __init__(self):
        self.today = datetime.datetime.now().strftime("Y%m%d")
        self.engine = None
        self.setting = None
        self.mode_set = None
        self.stop_check = None

    def initialize(self, setting, **kwargs):
        self.setting = setting
        self.engine = Engine()
        self.engine.paths = paths.Paths(setting["under"], os.getcwd())
        self.setting.update(paths.UNDERCONFIG[self.setting["under"]])
        self.stop_check = setting.get('stop_check', lambda: False)

    def _setup_mode_set(self, mode_set):
        self.mode_set = mode_set

    def run(self):
        try:
            self.setting.update(self.mode_set)
            self.engine.add_strategy(self.setting['drvmode'], self.setting)

            out_file = '-'.join(
                [str(self.setting[key]) for key in ['datetoday', 'under', 'mode', 'sigmode', 'key', 'minpx']] +
                [datetime.datetime.now().strftime("%m%d_%H%M%S")])

            sys.stdout = Logger(self.engine.paths.outdirs + f"\\{out_file}.log")

            self.engine.load_data()
            self.engine.start_strategy()
            print(self.mode_set)
            group_results = self.engine.stat_func()

            self.engine.plot_summary(group_results)
            self.engine.plot_surface(group_results)
            self.engine.plot_time(group_results)
            self.engine.plot_pivot(group_results)

            if self.setting['trade']:
                self.engine.trade_mix_md()
                self.engine.mix_trade_sig()
            else:
                print('no trade')

            print('all done ' + self.setting['datetoday'])
            os.startfile(self.engine.paths.outdirs)
            return True
        except Exception as e:
            error_msg = f"回测过程中出现错误: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            return False
        finally:
            sys.stdout = sys.__stdout__  # 恢复 sys.stdout


def main(config):
    logging.info("run_sigmix2 开始运行")
    logging.info(f"接收到的配置: {config}")
    try:
        sigmix = Sigmix2()
        logging.info("Sigmix2 实例已创建")
        sigmix.initialize(config)
        logging.info("Sigmix2 实例已初始化")
        mode_set = config['mode_set'][config['mode']]
        logging.info(f"模式设置: {mode_set}")
        sigmix._setup_mode_set(mode_set)
        logging.info("模式设置已完成")
        logging.info("开始运行 Sigmix2")
        success = sigmix.run()
        logging.info(f"run_sigmix2 运行完成，结果: {success}")
        return success
    except Exception as e:
        error_msg = f"run_sigmix2 运行出错: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        return False


if __name__ == '__main__':
    default_config = configs.setting
    mode_settings = (configs.ModeSettings(default_config, paths.Paths(default_config["under"], os.getcwd()))
                     .get_mode(default_config["mode"]))
    default_config['mode_set'] = {default_config['mode']: mode_settings}
    main(default_config)
    os.startfile(paths.Paths(default_config["under"], os.getcwd()).outdirs)
