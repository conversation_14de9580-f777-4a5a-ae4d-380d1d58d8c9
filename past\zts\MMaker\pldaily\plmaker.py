# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os
import shutil

import sys

from datetime import datetime
import win32com.client

if datetime.now().weekday() >= 5:
    print("今天是周末")
    os._exit(0)

try:
    filename = u"盘后损益-Rt.xlsm"
    filepath = u'D:\\works\\中泰衍生\\做市组日常工作\\盘后损益\\'

    xls = win32com.client.Dispatch('Excel.Application')
    xls.Visible = 0
    xls.DisplayAlerts = 0
    wb = xls.Workbooks.Open(filepath + filename)
    xls.Application.Run(u"刷新")
    ret = xls.Application.Run("GetPL")
    xls.Application.Run(u"刷新")
    wb.Save()
    print('get pl')
    ret = xls.Application.Run(u"记录")
    print(ret)
    print('save pic')
    wb.Save()
    xls.Quit()
    # del xls
    print('complete')
    shutil.copy(filepath + filename, "D:\\onedrive\\中泰衍生\\做市组业务工作\\数据报告\\盘后损益\\his\\" +
                datetime.strftime(datetime.now(), "%Y%m%d")+filename)

except Exception as e:
    print(e)
    xls.Quit()
    del xls
