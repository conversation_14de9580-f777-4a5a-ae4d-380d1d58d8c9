"""
Basic example of using the orderbook_dynamics library.
"""
import numpy as np
import matplotlib.pyplot as plt
import sys
import os


sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..')))

from orderbook_dynamics import OrderBook, OpenBookMsg, Side
from orderbook_dynamics.src.attribute.basic_attribute import BasicSet
from orderbook_dynamics.src.attribute.time_insensitive_attribute import TimeInsensitiveSet
from orderbook_dynamics.src.attribute.time_sensitive_attribute import TimeSensitiveSet


def create_sample_order_book(symbol="AAPL", num_orders=20):
    """Create a sample order book with random orders."""
    order_book = OrderBook(symbol)
    
    # Base price
    base_price = 150.0
    
    # Current timestamp
    timestamp = 1000
    
    # Store messages for time-sensitive attributes
    messages = []
    
    for i in range(num_orders):
        # Alternate between bid and ask
        side = Side.BID if i % 2 == 0 else Side.ASK
        
        # Randomize price
        price_offset = np.random.normal(0, 0.5)
        if side == Side.BID:
            price = base_price - 0.5 - abs(price_offset)
        else:
            price = base_price + 0.5 + abs(price_offset)
            
        # Randomize volume
        volume = int(np.random.lognormal(4, 0.5))
        
        # Create message
        msg = OpenBookMsg(
            symbol=symbol,
            source_time=timestamp,
            source_time_micro_secs=0,
            price=price,
            volume=volume,
            side=side
        )
        
        # Update order book
        order_book.update(msg)
        
        # Store message
        messages.append(msg)
        
        # Increment timestamp
        timestamp += np.random.randint(1, 100)
    
    return order_book, messages


def main():
    """Run the example."""
    # Create a sample order book
    order_book, messages = create_sample_order_book()
    
    # Print order book state
    print("Order Book State:")
    print(f"Symbol: {order_book.symbol}")
    print("Asks:")
    for price, volume in sorted(order_book.asks.items()):
        print(f"  {price:.2f}: {volume}")
    print("Bids:")
    for price, volume in sorted(order_book.bids.items(), reverse=True):
        print(f"  {price:.2f}: {volume}")
    print()
    
    # Create feature sets
    basic_set = BasicSet()
    time_insensitive_set = TimeInsensitiveSet(basic_set)
    time_sensitive_set = TimeSensitiveSet(1000, basic_set)
    
    # Calculate time-insensitive attributes
    price_spread = time_insensitive_set.price_spread()(order_book)
    mid_price = time_insensitive_set.mid_price()(order_book)
    mean_ask = time_insensitive_set.mean_ask()(order_book)
    mean_bid = time_insensitive_set.mean_bid()(order_book)
    mean_ask_volume = time_insensitive_set.mean_ask_volume()(order_book)
    mean_bid_volume = time_insensitive_set.mean_bid_volume()(order_book)
    
    # Print time-insensitive attributes
    print("Time-Insensitive Attributes:")
    print(f"Price spread: {price_spread.get():.2f}")
    print(f"Mid price: {mid_price.get():.2f}")
    print(f"Mean ask: {mean_ask.get():.2f}")
    print(f"Mean bid: {mean_bid.get():.2f}")
    print(f"Mean ask volume: {mean_ask_volume.get():.2f}")
    print(f"Mean bid volume: {mean_bid_volume.get():.2f}")
    print()
    
    # Calculate time-sensitive attributes using the messages
    trail = []
    for msg in messages:
        trail = time_sensitive_set.trail(trail, msg)
    
    bid_arrival_rate = time_sensitive_set.bid_arrival_rate()(trail)
    ask_arrival_rate = time_sensitive_set.ask_arrival_rate()(trail)
    order_intensity = time_sensitive_set.order_intensity()(trail)
    buy_sell_ratio = time_sensitive_set.buy_sell_ratio()(trail)
    
    # Print time-sensitive attributes
    print("Time-Sensitive Attributes:")
    print(f"Bid arrival rate: {bid_arrival_rate.get():.4f} orders/ms")
    print(f"Ask arrival rate: {ask_arrival_rate.get():.4f} orders/ms")
    print(f"Order intensity: {order_intensity.get():.4f} orders/ms")
    print(f"Buy/sell ratio: {buy_sell_ratio.get():.2f}")
    
    # Visualize order book
    plt.figure(figsize=(10, 6))
    
    # Plot asks in red
    asks = sorted(order_book.asks.items())
    ask_prices, ask_volumes = zip(*asks) if asks else ([], [])
    plt.bar(ask_prices, ask_volumes, color='red', alpha=0.6, label='Asks')
    
    # Plot bids in green
    bids = sorted(order_book.bids.items())
    bid_prices, bid_volumes = zip(*bids) if bids else ([], [])
    plt.bar(bid_prices, bid_volumes, color='green', alpha=0.6, label='Bids')
    
    # Add mid price line
    if mid_price.is_value:
        plt.axvline(x=mid_price.get(), color='blue', linestyle='--', label='Mid Price')
    
    plt.title('Order Book Visualization')
    plt.xlabel('Price')
    plt.ylabel('Volume')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # Save or show plot
    plt.savefig('order_book_visualization.png')
    plt.show()


if __name__ == "__main__":
    main() 