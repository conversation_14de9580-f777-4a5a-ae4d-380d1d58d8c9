import ftplib
import os
import re

def ftp_download_files(ftp_host, ftp_user, ftp_pass, remote_dir, local_dir, pattern=None):
    ftp = ftplib.FTP(ftp_host)
    ftp.login(ftp_user, ftp_pass)
    ftp.cwd(remote_dir)
    os.makedirs(local_dir, exist_ok=True)
    files = ftp.nlst()
    for file in files:
        if pattern is None or re.search(pattern, file):
            local_path = os.path.join(local_dir, file)
            with open(local_path, 'wb') as f:
                ftp.retrbinary(f'RETR {file}', f.write)
            print(f"Downloaded: {file} -> {local_path}")
    ftp.quit()

def ftp_upload_files(ftp_host, port, ftp_user, ftp_pass, local_dir, remote_dir, pattern=None):
    ftp = ftplib.FTP()
    ftp.connect(ftp_host,port)
    ftp.login(ftp_user, ftp_pass)
    # 确保远程目录存在，不存在则创建
    try:
        ftp.cwd(remote_dir)
    except ftplib.error_perm:
        # 递归创建目录
        parts = remote_dir.strip('/').split('/')
        path = ''
        for part in parts:
            path += '/' + part
            try:
                ftp.mkd(path)
            except Exception:
                pass
        ftp.cwd(remote_dir)
    # 遍历本地文件并上传
    for file in os.listdir(local_dir):
        if pattern is None or re.search(pattern, file):
            local_path = os.path.join(local_dir, file)
            if os.path.isfile(local_path):
                with open(local_path, 'rb') as f:
                    ftp.storbinary(f'STOR {file}', f)
                print(f"Uploaded: {local_path} -> {remote_dir}/{file}")
    ftp.quit()

if __name__ == "__main__":
    # 用法示例
    ftp_download_files(
        ftp_host='your.ftp.server',
        port='21',
        ftp_user='your_username',
        ftp_pass='your_password',
        remote_dir='/remote/path',
        local_dir='D:/local/path',
        pattern=r'.*\.csv$'  # 只下载csv文件，可根据需要调整
    )
    # 用法示例
    ftp_upload_files(
        ftp_host='your.ftp.server',
        port='21',
        ftp_user='your_username',
        ftp_pass='your_password',
        local_dir='D:/local/path',      # 本地目录
        remote_dir='/remote/path',      # FTP目标目录
        pattern=r'.*\.csv$'             # 只上传csv文件，可根据需要调整
    )