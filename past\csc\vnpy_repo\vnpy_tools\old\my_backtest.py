#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('SHFE.dev')
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""

#import vnpy.app.my_portfolio_strategy.backtesting, strategies.tick_resilience_portfolio  
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
#from strategies.tick_resilience_portfolio import TickWithRefer
from strategies.reverseStrategy import ReverseStrategy
import datetime
from vnpy.trader.constant import Interval

#%%

maker='hc2202.SHFE'
refer='hc2110.SHFE'
engine = BacktestingEngine()
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime.datetime(2021, 7, 30, 21, 10), # 开始时间
    end=datetime.datetime(2021, 8, 2, 15, 0), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: 10,refer: 10}, # 合约规模
    priceticks={maker: 1,refer: 1}, # 一个tick大小
    capital=1_000_000, # 初始资金
)
# 添加回测策略，并修改内部参数
engine.clear_data()
engine.add_strategy(ReverseStrategy, {'lots': 25,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                      'minEdge':9,'edge':9,'eta':0.6,'gamma':0.1,'validVolume':120,'safeVolume':150,'maxPos':50,'loss':5000,
                                      'referHedgeFlag':False,'neverStopFlag':False}) #hc2202
                                       
#%%
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
#engine.show_tick_chart() # 显示图表
engine.duty_statistics()

#%%
import pandas as pd
from datetime import timedelta
trades = pd.DataFrame([{'time':(engine.trades[x].datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':engine.trades[x].direction.value,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol
           } for x in engine.trades])
orders = pd.DataFrame([{'time':(x.datetime+timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol
           } for x in engine.get_all_orders()])

mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
           'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
           'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
           'last_price','volume','turnover']]
mkt_maker = mkt[mkt.symbol==engine.target_symbol]