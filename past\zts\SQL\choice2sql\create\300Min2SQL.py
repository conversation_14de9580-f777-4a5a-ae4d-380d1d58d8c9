# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-9-14

from WindPy import w
import pyodbc
import datetime
import pandas as pd

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
beginDate = datetime.datetime.strptime("2017-09-15 09:00:00", "%Y-%m-%d %H:%M:%S")
dt = datetime.datetime.strptime("2017-09-15 15:01:12", "%Y-%m-%d %H:%M:%S")  # datetime.now()
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
cursor = conn.cursor()
cursor.execute("""
 IF OBJECT_ID('Stk300_new', 'U') IS NOT NULL
    DROP TABLE Stk300_new
 CREATE TABLE Stk300_new (
    DateTime DATE NOT NULL,
    Time TIME(7) NOT NULL,
    STKID VARCHAR(20) NOT NULL,
    OPENPrice numeric(15, 4),
    HIGH numeric(15, 4),
    LOW numeric(15, 4),
    CLOSEPrice numeric(15, 4),
    Volume BIGINT,
    Amount numeric(15, 2),
    Ticks BIGINT,
    )
 """)


def tosql():
    sql = "INSERT INTO Stk300_new VALUES (?,?,?,?,?,?,?,?,?,?)"
    # sql0= "select A.STKID from [Alex].[dbo].[StkType300_new] as A left join [Alex].[dbo].[IndexStkInfo300_new] as B "\
    #       "on A.STKID=B.STKID where B.STKID is null "
    sql0 = "SELECT DISTINCT [STKID] FROM [Alex].[dbo].[StkType300_New]"

    pf = pd.read_sql(sql0, conn, index_col=None, coerce_float=True, params=None, parse_dates=None,
                     columns=None, chunksize=None)

    # 通过wset来取数据集数据
    print(pf)
    codes = pf['STKID'].values
    for j in range(0, len(codes)):
        # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
        print(u"\n\n-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----\n" % (j, codes[j]))
        wsddata1 = w.wsi(codes[j], "open,high,low,close,volume,amt", beginDate,
                         dt, "PriceAdj=F")

        # wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
        if wsddata1.ErrorCode != 0:
            continue
        print(wsddata1)
        for i in range(0, len(wsddata1.Data[1])):
            sqllist = []

            if len(wsddata1.Times) > 1:
                sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
                sqllist.append(wsddata1.Times[i].strftime('%H:%M:%S'))

            sqllist.append(codes[j])

            for k in range(0, len(wsddata1.Fields)):
                sqllist.append(wsddata1.Data[k][i])

            sqllist.append(-1)

            sqltuple = tuple(sqllist)

            try:
                cursor.execute(sql, sqltuple)
            except Exception as e:
                print('str(Exception):\t', str(Exception))
                print('str(e):\t\t', str(e))
                print(sqllist)

            conn.commit()


tosql()  # A股

conn.close()
