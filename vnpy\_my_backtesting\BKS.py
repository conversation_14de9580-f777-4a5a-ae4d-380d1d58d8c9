# encoding: UTF-8

"""
展示如何執行策略回測。
"""
import re
import sys
import os
import pandas as pd
from pandas import DataFrame

# Add the project root to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

sys.path.extend([os.path.abspath(os.path.join(os.path.abspath(__file__) if '__file__' in globals() 
                else os.getcwd(), *(['..'] * i))) for i in range(5)])


def run(startdate, strategy, setting=None):
    # 参数设置
    engine = BacktestingEngine()  # 創建回測引擎

    engine.set_parameters(
        vt_symbol=f"{under}.{exchange}",
        pids=undercode,
        start=startdate,
        end=startdate,
        rate=0.0 / 10000,  # 手续费
        slippage=0,  # 滑点
        size=200,
        pricetick=0.2,  # tick
        interval=Interval.MINUTE,
        tickmaxnum=5,
        capital=1_000_000,
        datamode=DataMode.SNAPSHOT_MODE,
        mode=BacktestingMode.TICK
    )

    engine.add_strategy(strategy, setting)
    # csvdown.download_csv(under, startdate)
    engine.loadHistoryDataCsv(csvstr1 % startdate, startdate)
    engine.run_backtesting()
    engine.showBacktestingResult()
    engine.plotResultWithTime(path.outdirs)

    return engine


if __name__ == '__main__':
    from vnpy.app.vnpy_ctastrategy.backtesting import BacktestingEngine
    from vnpy.app.vnpy_ctastrategy.base import BacktestingMode, DataMode
    from vnpy.trader.constant import Interval
    from strategies.strategy_IM import IMStrategy
    from db_solve.configs import paths
    from db_solve.utility import output_settings

    date1 = 20250407
    date2 = 20250411
    under = ['SH300', 'SH500', 'CXFD8'][1]
    exchange = ['CFFEX', 'LOCAL'][0]
    undercode = ['IC2504', ]

    strategy = IMStrategy
    
    setting = dict(
        refdealnum=10,
        wide=0.4,
        sig_thres=0.3,
        maxpos=3,
        mult1=0.5,
        mult2=1,
        sigmode='or',
        max_sigadj=1,
        stoppl=dict(active=True, stop_ratio=0.1, track_threshold=0.2, fallback_boundary=0.02, multiplier=3)
    )

    path = paths.Paths(under, __file__)
    csvstr1 = path.str1
    df: DataFrame = DataFrame(columns=[])
    for date in range(date1, date2 + 1):
        date = str(date)
        pattern = rf"{date}"
        dir_path = paths.Paths(under).dirs
        for file in os.listdir(dir_path):
            if re.search(pattern, file):
                print(date, 'begine--------------------')
                engine = run(date, strategy, setting)
                df0 = engine.calculate_result()
                df = df0.copy() if df.empty else pd.concat([df, df0], ignore_index=False)
                break
        else:
            print(date, 'pass--------------------')
            continue
            
    engine.calculate_statistics(df)
    engine.show_chart(df)
    engine.output(output_settings(setting))
    with open(path.outdirs + "\\%s-%s-%s.txt" % (under, date1, date2), 'w', encoding='utf-8') as file: 
        file.writelines([f"{entry}\n" for entry in engine.logs])

    # setting = OptimizationSetting()
    # setting.set_target("sharpe_ratio")
    # setting.add_parameter("atr_length", 25, 27, 1)
    # setting.add_parameter("atr_ma_length", 10, 30, 10)
    #
    # engine2.run_bf_optimization(setting)

    sys.exit()
