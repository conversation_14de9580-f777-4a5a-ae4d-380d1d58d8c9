# %%
# -*- coding: utf-8 -*-
"""
Created on Wed Jan 17 16:34:10 2024

@author: 015391
"""

import xgboost as xgb
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import matplotlib.pyplot as plt
import datetime as dt
from math import sqrt, log, exp
from scipy import stats
from datetime import date, time, datetime, timedelta
import xlwings as xw
import warnings
from itertools import combinations
import time
from xgboost import plot_importance
import os
import lightgbm as lgb
import nolds
import mlflow
import mlflow.sklearn
from sklearn.metrics import mean_squared_error

start_time = time.time()

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)


def covert_time(t, date, format_t='%Y%m%d%H:%M:%S:%f'):
    return datetime.strptime(f'{date}{t}', format_t)


def ewmmidtrend(x, y):
    alpha = 0.9
    for i in range(1, len(x)):
        x.iloc[i] = x.iloc[i - 1] * alpha + y.iloc[i]


def snapdf(t, df_t, d1=timedelta(minutes=2)):
    t = covert_time(t, date1, '%Y%m%d%H:%M:%S')
    id1 = df_t.index[df_t.index < t + d1]
    id2 = df_t.index[df_t.index >= t]
    idtotal = id1.intersection(id2)
    return df_t.loc[idtotal, :].sort_index().copy()

def process_value(thres):
    def process(x):
        if x > thres:
            return 1
        elif x < -thres:
            return -1
        else:
            return 0
    return process


def inputprocess(file, ticks, fut1):
    # file_path_t = f'md_{date1}_exanic_cffex.csv'
    file_path_t = f'train/{file}'
    print(file_path_t)
    date1 = file.split('_')[1]
    df_t = pd.read_csv(file_path_t, encoding='gbk')
    df_t.index = pd.DatetimeIndex(df_t['timestamp_str'].apply(
        lambda t: covert_time(t, date1, '%Y%m%d%H:%M:%S.%f')))
    # datetime_index = pd.DatetimeIndex(df_t.apply(
    #     lambda row: covert_time(row['timestamp_str'], row['date_field'], '%Y%m%d%H:%M:%S.%f'), axis=1))
    df_t['timestamp_str'] = df_t['timestamp_str'].apply(
        lambda t: covert_time(t, date1, '%Y%m%d%H:%M:%S.%f'))
    droplist_t = df_t.columns.tolist()
    df_t = df_t.loc[df_t["Symbol"].str.startswith(fut1[:6])]
    df_t.index = pd.to_datetime(df_t.index)
    datanew(df_t, 0, fut1, ticks,droplist_t)
    return df_t, droplist_t


def datanew(df, inst, symbol="", n=1,drop = []):
    if inst > 0:
        multi = 100
        mintick = 0.2
    elif symbol[:2] == "IC" or symbol[:2] == "IM":
        multi = 200
        mintick = 0.2
    elif symbol[:2] == "IF" or symbol[:2] == "IH":
        multi = 300
        mintick = 0.2
    else:
        multi = 300
        mintick = 0.2

    df['aggbidV'] = 0
    df['aggaskV'] = 0
    df['dsA'] = 0
    df['dsB'] = 0
    # df['nsb'] = 0
    # df['nss'] = 0
    df['second'] = df['timestamp_str'].dt.second
    df['minute'] = df['timestamp_str'].dt.minute
    df['second_sin'] = np.sin(2 * np.pi * df['second']/60)
    df['second_cos'] = np.cos(2 * np.pi * df['second']/60)
    df['minute_sin'] = np.sin(2 * np.pi * df['minute']/60)
    df['minute_cos'] = np.cos(2 * np.pi * df['minute']/60)
    drop.append(f'second')
    drop.append(f'minute')
    # position
    df['poschg'] = df['AuctionPrice'].diff().fillna(0)

    for i in range(1, 6):
        df[f'midpx{i}'] = 0.5 * (df[f'AskPrice{i}'] + df[f'BidPrice{i}'])
        drop.append(f'midpx{i}')
    i = 0
    for ind, row in df.iterrows():

        if i > 0:
            ask_prices = [df[f'AskPrice{j}'].iloc[i] for j in range(1, 6)]
            bid_prices = [df[f'BidPrice{j}'].iloc[i] for j in range(1, 6)]
            ask_vols = [df[f'AskVol{j}'].iloc[i] for j in range(1, 6)]
            bid_vols = [df[f'BidVol{j}'].iloc[i] for j in range(1, 6)]
            # df['ds'].iloc[i] = 0.5*(sum([(df[f'AskPrice{j+1}'].iloc[i] - df[f'AskPrice{j}'].iloc[i] )*df[f'AskVol{j}'].iloc[i]
            #                         for j in range(1, 5)])/sum([ df[f'AskVol{j}'].iloc[i] for j in range(1, 5)]) +sum([(df[f'BidPrice{j}'].iloc[i] - df[f'BidPrice{j+1}'].iloc[i] )*df[f'BidVol{j}'].iloc[i]
            #                         for j in range(1, 5)])/sum([ df[f'BidVol{j}'].iloc[i] for j in range(1, 5)]))
            # df['dsA'].iloc[i] = (sum([(df[f'AskPrice{j+1}'].iloc[i] - df[f'AskPrice{j}'].iloc[i] ) * df[f'AskVol{j}'].iloc[i]
            #                         for j in range(1, 5)])/sum([ df[f'AskVol{j}'].iloc[i] for j in range(1, 5)]) )
            # df['dsB'].iloc[i] = -(sum([(df[f'BidPrice{j}'].iloc[i] - df[f'BidPrice{j+1}'].iloc[i] )*df[f'BidVol{j}'].iloc[i]
            #                          for j in range(1, 5)])/sum([ df[f'BidVol{j}'].iloc[i] for j in range(1, 5)]))

            df['dsA'].iloc[i] = (sum([(df[f'AskPrice{j+1}'].iloc[i] - df[f'AskPrice{j}'].iloc[i] ) / df[f'AskVol{j}'].iloc[i]
                                    for j in range(1, 5)])/sum([ 1/df[f'AskVol{j}'].iloc[i] for j in range(1, 5)]) )
            df['dsB'].iloc[i] = -(sum([(df[f'BidPrice{j}'].iloc[i] - df[f'BidPrice{j+1}'].iloc[i] ) / df[f'BidVol{j}'].iloc[i]
                                     for j in range(1, 5)])/sum([ 1/df[f'BidVol{j}'].iloc[i] for j in range(1, 5)]))

            # df['nsb'].iloc[i] = 0.2*(log(df[f'BidVol1'].iloc[i]+0.1)/log(df[f'midpx1'].iloc[i]/df[f'BidPrice1'].iloc[i])
            #                          +sum([log(df[f'BidVol{j}'].iloc[i] / df[f'BidVol{j+1}'].iloc[i] + 0.1 )/log(df[f'BidPrice{j}'].iloc[i] / df[f'BidPrice{j+1}'].iloc[i])
            #                         for j in range(1, 5)]))
            # df['nss'].iloc[i] = 0.2*(log(df[f'AskVol1'].iloc[i]+0.1)/log(df[f'AskPrice1'].iloc[i]/df['midpx1'].iloc[i])
            #                          +sum([log(df[f'AskVol{j+1}'].iloc[i] / df[f'AskVol{j}'].iloc[i]+0.1 )/log(df[f'AskPrice{j+1}'].iloc[i] / df[f'AskPrice{j}'].iloc[i])
            #                         for j in range(1, 5)]))
            if any(ask_price < df['AskPrice1'].iloc[i - 1] for ask_price in ask_prices):
                df['aggaskV'].iloc[i] = sum([ask_vols[j] for j, ask_price in enumerate(ask_prices) if
                                             ask_price < df['AskPrice1'].iloc[i - 1]])
            if any(bid_price > df['BidPrice1'].iloc[i - 1] for bid_price in bid_prices):
                df['aggbidV'].iloc[i] = sum([bid_vols[j] for j, bid_price in enumerate(bid_prices) if
                                             bid_price > df['BidPrice1'].iloc[i - 1]])

            for j, (ask_price, ask_vol) in enumerate(zip(ask_prices, ask_vols), start=1):
                if ask_price == df['AskPrice1'].iloc[i - 1] and j<=3:
                    df['aggaskV'].iloc[i] += max(ask_vol - df['AskVol1'].iloc[i - 1], 0)
            for j, (bid_price, bid_vol) in enumerate(zip(bid_prices, bid_vols), start=1):
                if bid_price == df['BidPrice1'].iloc[i - 1] and j<=3:
                    df['aggbidV'].iloc[i] += max(bid_vol - df['BidVol1'].iloc[i - 1], 0)

        i = i + 1
    for i in range(1, 6):
        df[f'bidpxdiff{i}'] = df[f'BidPrice{i}'].diff().fillna(0)
        df[f'askpxdiff{i}'] = df[f'AskPrice{i}'].diff().fillna(0)

    for i in range(1, 6):
        df[f'avgbid{i}'] = df[[f'bidpxdiff{j}' for j in range(1, i + 1)]].mean(axis=1)
        df[f'avgask{i}'] = df[[f'askpxdiff{j}' for j in range(1, i + 1)]].mean(axis=1)
        df[f'avgbidSUM{i}'] = df[[f'bidpxdiff{j}' for j in range(1, i + 1)]].sum(axis=1)
        df[f'avgaskSUM{i}'] = df[[f'askpxdiff{j}' for j in range(1, i + 1)]].sum(axis=1)
        df[f'sumbidv{i}'] = df[[f'BidVol{j}' for j in range(1, i + 1)]].sum(axis=1)
        df[f'sumaskv{i}'] = df[[f'AskVol{j}' for j in range(1, i + 1)]].sum(axis=1)
    for i in [3,4,5]:
        df[f'aggbidV_RES_{i}'] = np.where(df['aggbidV'] > 0, df['aggbidV'] - df[f'sumaskv{i}'], 0)#.apply(lambda  x : max(x,0))
        df[f'aggaskV_RES_{i}'] = np.where(df['aggaskV'] > 0, df['aggaskV'] - df[f'sumbidv{i}'], 0)#.apply(lambda  x : max(x,0))
        df[f'aggbidV_RATIO_{i}'] = (df['aggbidV'] / df[f'sumaskv{i}'])
        df[f'aggaskV_RATIO_{i}'] = (df['aggaskV'] / df[f'sumbidv{i}'])
        for window in range(2,20,2):

            df[f'aggbidV_RES_{i}_{window}_sum'] = df[f'aggbidV_RES_{i}'].rolling(window).sum()
            df[f'aggbidV_RES_{i}_{window}_avg'] = df[f'aggbidV_RES_{i}'].rolling(window).mean()
            df[f'aggbidV_RES_{i}_{window}_diff'] = (df[f'aggbidV_RES_{i}'] - df[f'aggbidV_RES_{i}'] .rolling(window).median().shift())

            df[f'aggaskV_RES_{i}_{window}_sum'] = df[f'aggaskV_RES_{i}'].rolling(window).sum()
            df[f'aggaskV_RES_{i}_{window}_avg'] = df[f'aggaskV_RES_{i}'].rolling(window).mean()
            df[f'aggaskV_RES_{i}_{window}_diff'] = (df[f'aggaskV_RES_{i}'] - df[f'aggaskV_RES_{i}'] .rolling(window).median().shift())


            df[f'aggbidV_RATIO_{i}_{window}_avg'] = df[f'aggbidV_RES_{i}'].rolling(window).mean()
            df[f'aggaskV_RATIO_{i}_{window}_avg'] = df[f'aggaskV_RES_{i}'].rolling(window).mean()
## groupby time
    for i in [3, 4, 5]:
        df[f'aggbidV_RES_{i}_groupbysecond_diff'] = (df[f'aggbidV_RES_{i}'] - df.groupby('minute')[f'aggbidV_RES_{i}'].transform(lambda x : x.expanding().median().shift().fillna(0)))
        df[f'aggaskV_RES_{i}_groupbysecond_diff'] = (df[f'aggaskV_RES_{i}'] - df.groupby('minute')[f'aggaskV_RES_{i}'] .transform(lambda x : x.expanding().median().shift().fillna(0)))


    df['vol5'] = df['BidVol1'] + df['BidVol2'] + df['BidVol3'] + df['BidVol4'] + df['BidVol5'] - (
            df['AskVol1'] + df['AskVol2'] + df['AskVol3'] + df['AskVol4'] + df['AskVol5'])

    df['voldiff'] = df['Volume'].diff().fillna(0)

    df['amtdiff'] = df['TotalValueTraded'].diff().fillna(0)

    drop.append(f'amtdiff')

    df['avgpx'] = (df['amtdiff'] / df['voldiff']) / multi
    df['avgpx'].fillna(method='ffill', inplace=True)

    df['avgdiff'] = df['avgpx'].diff()
    df['avgdiff'].iloc[0:2, ] = 0
    df['trddir'] = 0

    df['bidaskspread'] = (df['AskPrice1'] - df['BidPrice1'])


    prices = [f'midpx{i}' for i in range(1, 6)]

    for c in combinations(prices, 2):
        df[f'{c[0]}_{c[1]}_imb'] = df.eval(f'({c[0]} - {c[1]})')

    df['middiff'] = df['midpx1'].diff().fillna(0)
    df['middiff_hurst'] = nolds.hurst_rs(df['middiff'] )
    df['Lowdiff'] = df['Low'].diff().fillna(0)
    df['Highdiff'] = df['High'].diff().fillna(0)
    df['LowDura'] = df.groupby('Low').cumcount()
    df['HighDura'] = df.groupby('High').cumcount()
    df['LowdiffA'] = df['LowDura'] * df['Lowdiff']
    df['HighdiffA'] = df['HighDura'] * df['Highdiff']
###################
    decay = [0.9,0.95]

    for _ in decay:
        df[f'midtrend-{_}'] = 0
    i = 0
    for ind, row in df.iterrows():
        if i > 0:
            for _ in decay:
                df[f'midtrend-{_}'].iloc[i] = df[f'midtrend-{_}'].iloc[i - 1] * _ + df['middiff'].iloc[i]
        i = i + 1
    for _ in decay:
        df[f'trenddiff-{_}'] = df[f'midtrend-{_}'].diff().fillna(0)
        df[f'trendoftrend-{_}'] = 0

    i = 0
    for ind, row in df.iterrows():
        if i > 0:
            for _ in decay:
                df[f'trendoftrend-{_}'].iloc[i] = df[f'trendoftrend-{_}'].iloc[i - 1] * _ + df[f'trenddiff-{_}'].iloc[i]
        i = i + 1

    # midgap = [6,10,20,60,120]
    midgap = range(2,20,2)
    for gap in midgap:
        df[f'midgap{gap}'] = df['midpx1'].diff(gap).fillna(0)
        df[f'Lowgap{gap}'] = df['Low'].diff(gap).fillna(0)
        df[f'Highgap{gap}'] = df['High'].diff(gap).fillna(0)
        df[f'midgap{gap}_hurst'] = nolds.hurst_rs(df[f'midgap{gap}'])


    i = 0
    for ind, row in df.iterrows():
        if i > 0:
            if df['avgpx'].iloc[i] >= (df['AskPrice1'].iloc[i - 1] - 0.3 * df['bidaskspread'].iloc[i - 1]):
                df['trddir'].iloc[i] = 1
            elif df['avgpx'].iloc[i] <= (df['BidPrice1'].iloc[i - 1] + 0.3 * df['bidaskspread'].iloc[i - 1]) and \
                    df['avgpx'].iloc[i] > 0:
                df['trddir'].iloc[i] = -1
        i = i + 1
    drop.append('avgpx')
    for window in range(2,20,2):
        df[f"trddir_{window}_sum"] = df['trddir'].rolling(window).sum()
    df['trdaskV'] = df['voldiff'] * (df['trddir'] == -1)
    df['trdbidV'] = df['voldiff'] * (df['trddir'] == 1)
    df['trdimb'] = df['trdbidV'] - df['trdaskV']
    df['ordimb'] = df['aggbidV'] - df['aggaskV']
    df['BreakBid'] = df['aggbidV'] + df['trdbidV']
    df['BreakAsk'] = df['trdaskV'] + df['aggaskV']
    df['BreakBid1'] = df['aggbidV'] - df['trdbidV']
    df['BreakAsk1'] = df['aggaskV'] - df['trdaskV']

#   df[f'{col}_hurst'] = nolds.hurst_rs(df[f'{col}'])
    speccol = ['BreakBid', 'BreakAsk','BreakBid1','BreakAsk1','aggbidV','aggaskV','dsA','dsB','poschg','voldiff','vol5']
    for col in speccol:
        for window in range(2,20,2):
            df[f'{col}_{window}_diff'] = df[f'{col}'].rolling(window).median()
            df[f'{col}_{window}_diff'] = (df[f'{col}'] - df[f'{col}_{window}_diff']).fillna(0)
            df[f'{col}_{window}_sum'] = df[f'{col}'].rolling(window).sum()
            df[f'{col}_{window}_mean'] = df[f'{col}'].rolling(window).mean()
    for window in range(2,20,2):
        df[f'OI/VOL-{window}'] = df[f'poschg_{window}_sum'] / df[f'voldiff_{window}_sum']



    for col in speccol:
        df[f'{col}_groupbysecond_diff'] = (df[f'{col}'] - df.groupby('minute')[f'{col}'].transform(lambda x : x.expanding().median().shift().fillna(0)))
# 分价
    speccol = ['BreakBid', 'BreakBid1', 'aggbidV', 'BidVol1']
    for col in speccol:
        df[f'{col}_groupbyprice_diff'] = (df[f'{col}'] - df.groupby('BidPrice1')[f'{col}'].transform(
                                        lambda x: x.expanding().median().shift().fillna(0)))
    speccol = ['BreakAsk', 'BreakAsk1', 'aggaskV', 'AskVol1']
    for col in speccol:
        df[f'{col}_groupbyprice_diff'] = (df[f'{col}'] - df.groupby('AskPrice1')[f'{col}'].transform(
                                        lambda x: x.expanding().median().shift().fillna(0)))

    df['gapAsk'] = df['AskPrice2'] - df['AskPrice1']
    df['gapBid'] = df['BidPrice1'] - df['BidPrice2']
    speccol = ['gapAsk', 'gapBid','BidVol1','AskVol1','AskPrice1','BidPrice1','dsA','dsB','aggbidV','aggaskV']
    for col in speccol:
        for window in range(2,20,2):
            df[f'{col}_{window}_diff'] = df[f'{col}'].rolling(window).median()
            df[f'{col}_{window}_diff'] = (df[f'{col}'] - df[f'{col}_{window}_diff'].shift(1)).fillna(0)
            df[f'{col}_{window}_high'] = df[f'{col}'].rolling(window).max()
            df[f'{col}_{window}_high'] = (df[f'{col}'] - df[f'{col}_{window}_high'].shift(1)).fillna(0)
            df[f'{col}_{window}_high'] = df[f'{col}_{window}_high'].apply(lambda x : max(x,0))
            df[f'{col}_{window}_low'] = df[f'{col}'].rolling(window).min()
            df[f'{col}_{window}_low'] = (df[f'{col}'] - df[f'{col}_{window}_low'].shift(1)).fillna(0)
            df[f'{col}_{window}_low'] = df[f'{col}_{window}_low'].apply(lambda x : min(x,0))


    ## feature importance
    for window in range(2,20,2):
        df[f'dsX{window}'] = df[f'dsA_{window}_sum'] - df[f'dsB_{window}_sum']
        df[f'Break1{window}'] = df[f'BreakAsk1_{window}_sum'] - df[f'BreakBid1_{window}_sum']
        df[f'aggV{window}'] = df[f'aggaskV_{window}_sum'] - df[f'aggbidV_{window}_sum']




    delta = 0.4
    df['rawprice'] = (df['avgpx'].rolling(n).mean().shift(-n) - df['midpx1']).fillna(method='ffill')
    df['price'] = df['rawprice'].apply(lambda x: 1 if x > delta else (2 if x < -delta else 0))
    drop.append(f'rawprice')


listtrain = []
listtest = []

rawfile ='md_20250325_shm_receiver.csv'
rawdata = pd.read_csv(rawfile, encoding='gbk')
rawdata['date'] = rawdata['ForQuoteSysID'].apply(lambda x: x[:8])
# listd = os.listdir('train')
# datelist = []
# for file in listd:
#     datelist.append(file.split('_')[1])
datetrain = ['20240902','20240903','20240904','20240905','20240906','20240909','20240910','20240911','20240912','20240913']
datetest = ['20240918','20240919']

rawdata.index =  pd.DatetimeIndex(rawdata.apply(
    lambda row: covert_time(row['timestamp_str'], row['date'], '%Y%m%d%H:%M:%S.%f'), axis=1))
rawdata['timestamp_str'] = rawdata.apply(
    lambda row: covert_time(row['timestamp_str'], row['date'], '%Y%m%d%H:%M:%S.%f'), axis=1)


fut ="IC2409"
ticks = 10
for temp in datetrain:
    dftemp = rawdata.query(f'date == "{temp}" and Symbol == "{fut}"').copy().sort_index()
    droplist_t = dftemp.columns.tolist()
    dftemp.index = pd.to_datetime(dftemp.index)
    datanew(dftemp, 0, fut, ticks, droplist_t)
    print(temp)
    listtrain.append(dftemp)
for temp in datetest:
    dftemp = rawdata.query(f'date == "{temp}" and Symbol == "{fut}"').copy().sort_index()
    droplist_t = dftemp.columns.tolist()
    dftemp.index = pd.to_datetime(dftemp.index)
    datanew(dftemp, 0, fut, ticks, droplist_t)
    print(temp)
    listtest.append(dftemp)




# voldata = [f'AskVol{j}' for j in range(1, 6)] + [f'BidVol{j}' for j in range(1, 6)]
# droplist_t =  [x for x in droplist_t if x not in voldata]
train = pd.concat(listtrain).drop(droplist_t, axis=1).copy()
test = pd.concat(listtest).drop(droplist_t, axis=1).copy()



# dftest.to_csv(f'train.csv')
# dftest, droplist = inputprocess('20240227', 10, "IF2403.CFE")
# test = dftest.drop(droplist, axis=1).copy()
# dftest.to_csv(f'test.csv')

end_time = time.time()
print("耗时: {:.2f}秒".format(end_time - start_time))

#%% tocsv

listtest[0].head(10000).to_csv(f'train.csv')

#%% backforward
# gamma = 0
# train1 = train.query(f'price >= {gamma} or price <= -{gamma}')
# test1 = test.query(f'price >= {gamma} or price <= -{gamma}')

X_train, y_train = train.drop('price', axis=1), train['price']
X_test, y_test = test.drop('price', axis=1), test['price']
# X_train, X_test, y_train, y_test = train_test_split(X_train, y_train, random_state=100,test_size=0.2)
# X_test, y_test = test1.drop('price', axis=1), test1['price']
# split = int(len(train1) * 0.7)
# X_train, y_train = train1.drop('price', axis=1)[:split], train1['price'][:split]
# X_test, y_test = train1.drop('price', axis=1)[split:], train1['price'][split:]

#%% correlation filter
from sklearn.feature_selection import VarianceThreshold
# selector = VarianceThreshold()
# X_train_0 = selector.fit_transform(X_train)
selector = VarianceThreshold(np.median(X_train.var().values))
X_new = selector.fit_transform(X_train)
X_train = pd.DataFrame(X_new, columns=X_train.columns[selector.get_support()])
X_test = X_test[X_test.columns[selector.get_support()]]

#%% IC train IM

loaded_model = lgb.Booster(model_file='lgb_model_IM.txt')
X_train, y_train = train.drop('price', axis=1), train['price']
X_train = pd.DataFrame(X_train, columns=X_train.columns[selector.get_support()])



#%% 互信息

from sklearn.feature_selection import mutual_info_regression

def make_mi_scores(X, y, discrete_features):
    mi_scores = mutual_info_regression(X, y, discrete_features=discrete_features)
    mi_scores = pd.Series(mi_scores, name="MI Scores", index=X.columns)
    mi_scores = mi_scores.sort_values(ascending=False)
    return mi_scores

X_train.fillna(0,inplace=True)
discrete_features = X_train.dtypes == "object"
mi_scores = make_mi_scores(X_train, y_train, discrete_features)
mi_scores[::1]  # show a few features with their MI scores

def plot_mi_scores(scores):
    scores = scores.sort_values(ascending=True)
    width = np.arange(len(scores))
    ticks = list(scores.index)
    plt.barh(width, scores)
    plt.yticks(width, ticks)
    plt.title("Mutual Information Scores")


plt.figure(dpi=100, figsize=(8, 5))
plot_mi_scores(mi_scores)





#%% correlation plot

import seaborn as sns
corr_matrix = X_train.corr()

# 绘制相关性热图
plt.figure(figsize=(10, 8))  # 设置图形大小
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0)
plt.show()

#%% LGB


from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
from sklearn.utils.class_weight import compute_sample_weight
mlflow.set_tracking_uri("http://160.14.228.69:6040")
mlflow.set_experiment("hmj_test")

with mlflow.start_run() as run:

    eval_result = {}
    class_weights = {0:1,1:5,2:5}
    train_matrix = lgb.Dataset(X_train, y_train) # lgb.dataset()
    valid_matrix= lgb.Dataset(X_test, y_test)
    start_time = time.time()
    callback=[lgb.early_stopping(stopping_rounds=5000,verbose=True),lgb.log_evaluation(period=100),
              lgb.record_evaluation(eval_result)]

    #evals = [(dtrain_reg, "train"), (dtest_reg, "validation")]
    n = 3000
    params = {
                'objective': 'multiclass',
                'learning_rate': 0.05,
                'num_leaves': 128, # 128
                'num_class': 3,
                #'numclasss': 3,
                # 'class_weight': class_weights,
                'metric': 'multi_logloss',
                'subsample': 0.8, # 0.8
                'colsample_bytree': 0.8, # 0.8
                'max_depth': -1,
                'device':'gpu',
                'verbosity':1,
                'n_estimators': 1000
                }

    model = lgb.train(params, train_set=train_matrix, valid_sets=[train_matrix,valid_matrix], valid_names=['fast','check'],num_boost_round=3000,callbacks=callback)
    print(params)
    end_time = time.time()
    print("耗时: {:.2f}秒".format(end_time - start_time))
    lgb.plot_importance(model,  max_num_features=25,importance_type="gain", title="LightGBM Feature Importance (Gain)")
    plt.tight_layout()
    plt.show()
    lgb.plot_importance(model,  max_num_features=25,importance_type="split", title="LightGBM Feature Importance (split)")
    plt.tight_layout()
    plt.show()

    y_pred = model.predict(X_test).argmax(axis=1)
    print("\n================ 分类报告 ================")
    print(classification_report(
        y_test,
        y_pred,
        target_names=['平', '涨', '跌']
    ))
    mlflow.log_params(params)
    mlflow.log_metric("report", classification_report(
        y_test,
        y_pred,
        target_names=['平', '涨', '跌']
    ))



# %% sklearn boost
import lightgbm as lgb
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
mlflow.set_tracking_uri("http://160.14.228.69:6040")
mlflow.set_experiment("lgb_test")

with mlflow.start_run() as run:
    eval_result ={}
    callback=[lgb.log_evaluation(period=100),
              lgb.record_evaluation(eval_result)]

    class_weights = {
        0: 1.0,  # 多数类（平）权重较低
        1: 3.0,  # 少数类（涨）权重较高
        2: 3.0   # 少数类（跌）权重较高
    }
    class_weights = 'balanced'


    params = {
        'objective': 'multiclass',  # 多分类任务
        'num_class': 3,             # 类别数
        'metric': 'multi_logloss',  # 评估指标
        'class_weight': class_weights,  # 关键参数：注入类别权重
        'learning_rate': 0.05,
        'num_leaves':128,
        # 'feature_fraction': 0.8,
        'colsample_bytree': 0.8,
        'subsample': 0.9,
        'n_estimators': 3000, # 3000
        'verbosity': 1
    }

    model = lgb.LGBMClassifier(**params)
    model.fit(X_train, y_train,callbacks=callback,eval_set = [(X_train, y_train), (X_test, y_test)],eval_names = ['train','test'])

    y_pred = model.predict(X_test)
    print(classification_report(y_test, y_pred, target_names=['平', '涨', '跌']))
    lgb.plot_importance(model,  max_num_features=25,importance_type="gain", title="LightGBM Feature Importance (Gain)")
    plt.tight_layout()
    plt.show()
    lgb.plot_importance(model,  max_num_features=25,importance_type="split", title="LightGBM Feature Importance (split)")
    plt.tight_layout()
    plt.show()

    feature_importance = model.feature_importances_
    feature_names = model.feature_name_
    importance_list = list(zip(feature_names, feature_importance))
    importance_list.sort(key=lambda x: x[1], reverse=True)
    top_20_features = importance_list[:20]
    print(top_20_features)
    log1 = classification_report(
        y_test,
        y_pred,
        target_names=['平', '涨', '跌'],output_dict=True
    )
    mlflow.log_params(params)
    mlflow.log_param('newcol','trddir_rolling')

    #mlflow.log_metric("report", log1)
    #mlflow.log_dict(log1 ,"classification_report.json")
    for label, metrics in log1.items():
        if isinstance(metrics, dict):  # 处理具体类别或汇总部分 (precision/recall/f1-score/support)
            for metric_name, value in metrics.items():
                if isinstance(value, float):  # 只记录数值型的度量值
                    mlflow.log_metric(f"{label}_{metric_name}", value)



#%% predict train

y_pred = loaded_model.predict(X_train)
y_pred = np.argmax(y_pred, axis=1)
print(classification_report(y_train, y_pred, target_names=['平', '涨', '跌']))


#%% model save

model.booster_.save_model('lgb_model_IM.txt')