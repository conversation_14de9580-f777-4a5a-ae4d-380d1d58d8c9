# coding=utf-8
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
from OmmDatabase import OmmDatabase
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

import sys
sys.path.append("./中信建投/做市/")
from Liquidity import processing,get_Depth,get_Breadth,get_Resilience,get_Tightness,get_Immediacy,get_Depth_Liquidity

#%%
_maker = 'hc2103'
_refer = 'hc2105'

tickN = 1
k = 20
date = '2021-01-07'
maker = processing(_maker,date)
refer = processing(_refer,date)

refer_5 = get_Resilience(refer,5)
refer_10 = get_Resilience(refer,10)
refer_20 = get_Resilience(refer,20)
refer_30 = get_Resilience(refer,30)

maker_5 = get_Resilience(maker,5)
maker_10 = get_Resilience(maker,10)
maker_20 = get_Resilience(maker,20)
maker_30 = get_Resilience(maker,30)
maker_60 = get_Resilience(maker,60)


maker1 = maker.copy()
refer1 = refer.copy()

# print(maker1[['fair','resilience']].corr())
print(refer1[['fair','resilience']].corr())

#单独统计 resilience的信号

def test(df1,m=10,upper=0.6,lower=0.1):
    df = df1.copy()
    df.reset_index(drop=True,inplace=True)
    up = df.loc[lambda x:x.resilience > upper]
    low = df.loc[lambda x:x.resilience < lower]

    maxnums = df.shape[0] - m
    up['win'] = 0
    low['win'] = 0
    # 2代表无判断信号 -2代表判断失误 1代表动量 -1代表反转
    for i in up.index:
        if m <= i < maxnums:
            signal1 = df.loc[i,'fair'] - df.loc[i-m,'fair']
            signal2 = df.loc[i+m,'fair'] - df.loc[i,'fair']
            if signal1 == 0:
                up.loc[i, 'win'] = 2
            elif signal1 != 0 and signal2 == 0:
                up.loc[i, 'win'] = -2
            else:
                up.loc[i,'win'] = np.sign(signal1*signal2)

    for i in low.index:
        if m <= i < maxnums:
            signal1 = df.loc[i,'fair'] - df.loc[i-m,'fair']
            signal2 = df.loc[i+m,'fair'] - df.loc[i,'fair']
            if signal1 == 0:
                low.loc[i, 'win'] = 2
            elif signal1 != 0 and signal2 == 0:
                low.loc[i, 'win'] = -2
            else:
                low.loc[i,'win'] = np.sign(signal1*signal2)

    print('超过上限时的反转率：'+str(round(100*up.win.tolist().count(-1)/(up.win.tolist().count(-1)+up.win.tolist().count(1)),2))+'%')
    print('超过下限时的反转率：' + str(round(100 * low.win.tolist().count(-1) / (low.win.tolist().count(-1) + low.win.tolist().count(1)),2)) + '%')

#%% 开始进行测试
df1 = refer_60
m_list = [1,2,3,4,5]
upper=0.2
lower=0.2
for m in m_list:
    print(m)
    test(df1, m, upper, lower)



#%%
# 如果用refer的resilience来判断maker的resilience
# 问题在于maker的数据变化的频率不如refer，可能refer某一区间缓慢上涨，maker在某一时刻突然无量上升
maker = maker.reset_index(drop=True)
nums = len(maker.index)
fig,ax1=plt.subplots()
ax1.plot(maker['fair'],'b',label='fair')
plt.legend()
ax2 = ax1.twinx()
ax2.plot(maker['resilience'],'r')
plt.show()

# df = pd.merge(maker1,refer1,on='time',how='outer')
# df = df.sort_values(by='time',ascending=True)
# df.reset_index(drop=True,inplace=True)