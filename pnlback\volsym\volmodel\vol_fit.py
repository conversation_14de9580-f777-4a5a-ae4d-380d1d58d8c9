import os
import sys
import numpy as np
from scipy.optimize import minimize

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

from pnlback.volsym.volmodel.models.svi_cs import SVI


def fit_svi_model(config, exp_data, exp, last_exp_data):
    """
        使用SVI模型拟合波动率曲线并计算导数
        
        参数:
            exp_data: DataFrame, 包含特定到期日的期权数据
            exp: str, 到期日
        
        返回:
            K_fit: ndarray, 拟合用的行权价序列
            sigma_fit: ndarray, 拟合的波动率值
            params: tuple, SVI模型参数(a,b,rho,m,sigma)
            derivatives: dict, 包含各种导数的字典
        """
    t = exp_data['time2expiry']
    k = np.log(exp_data['K'] / exp_data['forward'])
    v = exp_data['market_sigma'] ** 2 * t.iloc[0]

    # 计算vega权重并归一化
    vega_weights = np.where((exp_data['bid'] == exp_data['ask']) | (exp_data['neg_bid'] == exp_data['neg_ask']), 0,
                            exp_data['vega'].values)
    vega_weights /= vega_weights.sum()

    # 获取上次的参数作为初始值
    if exp in last_exp_data:
        initial_params = last_exp_data[exp]['last_svi_params']
    else:
        initial_params = [0.04, 0.1, 0.1, 0.1, 0.1]

    def objective(params):
        a, b, rho, m, sigma = params
        names = ['a', 'b', 'rho', 'm', 'sigma']
        # 为不同参数设置不同的惩罚权重
        param_weights = config['param_weights']
        total_weight = config['svi_weights']

        # 先创建SVI模型实例
        _svi_model = SVI(a, b, rho, m, sigma)
        _svi_model.setparas(t.iloc[0])
        # 计算fitvol
        variance = _svi_model.variance(k)

        # 当前数据的拟合误差（vega加权）
        fit_error = np.sum((vega_weights * (variance - v)) ** 2)

        # 计算当前参数的ATM特征
        fit_atm_features = _svi_model.get_results()

        # 与上次参数的偏离程度
        if exp in last_exp_data:
            last_params = last_exp_data[exp]['last_svi_params']
            last_atm_features = last_exp_data[exp]['last_atm_features']

            # 计算加权参数变化惩罚
            param_change_penalty = sum([
                param_weights[param] * ((p - l_p) / l_p) ** 2
                for param, p, l_p in zip(names, params, last_params)
            ])
            # 根据实际的get_results方法调整ATM特征比较
            atm_penalty = sum(
                config['atm_weights'][key] * (
                        (fit_atm_features[key] - last_atm_features[key]) / (abs(last_atm_features[key]) + 1e-6)
                ) ** 2
                for key in ['atm_vol', 'skew', 'convexity', 'cslope', 'pslope']
            )
            # 组合所有权重
            final_penalty_weight = total_weight['fit'] * (
                    total_weight['param'] * param_change_penalty + total_weight['atm'] * atm_penalty)
            return fit_error + final_penalty_weight

        return fit_error

    # 设置参数优化边界
    bounds = [
        (1e-5, np.inf),  # a: amin到最大方差
        (1e-3, np.inf),  # b: bmin到1
        (-50, 50),  # rho: -1到1
        (-np.inf, np.inf),  # m: 2倍最小k到2倍最大k
        (0, np.inf)  # sigma: 0.01到1
    ]

    # 优化
    result = minimize(objective, initial_params, bounds=bounds, method='Nelder-Mead', tol=1e-12)

    if result.success:
        # 保存本次的ATM特征，供下次使用
        svi_model = SVI(*result.x)
        svi_model.setparas(t.iloc[0])
        atm_features = svi_model.get_results()

        # 生成拟合曲线
        sigma_fit = svi_model.fitvol(k)
        objective = result.fun
        rmse_error = np.sqrt(np.mean((vega_weights * (sigma_fit - exp_data['market_sigma'])) ** 2))
        r2 = 1 - np.sum((exp_data['market_sigma'] - sigma_fit) ** 2) / np.sum(
            (exp_data['market_sigma'] - np.mean(exp_data['market_sigma'])) ** 2)

        # 创建包含所有数据的字典
        voltime = {
            **dict(zip(['a', 'b', 'rho', 'm', 'sigma'], result.x)),  # SVI参数
            **atm_features,  # ATM特征
            'objective': objective,
            'rmse_error': rmse_error*1000,
            'r2': r2,
        }

        return exp, sigma_fit, voltime
    else:
        return None, None, None
