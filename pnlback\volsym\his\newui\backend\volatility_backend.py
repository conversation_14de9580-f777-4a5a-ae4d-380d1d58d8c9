from PySide6.QtCore import QObject, Signal, Property, Slot, QPointF, QTimer, QVariant
import numpy as np

import os
import sys
sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])
from newui.backend.data_manager import DataManager

class VolatilityBackend(QObject):
    dataChanged = Signal()
    
    def __init__(self):
        super().__init__()
        self.initialize_variables()
        
    def initialize_variables(self):
        # 基本变量
        self.annotation = None
        self.highlight_point = None
        self.timer = QTimer()
        self.current_direction = 0
        self.is_continuous = False
        self.press_time = None
        self.decimal_places = 4
        self.custom_r = 0
        self.use_new_iv = False
        self.show_bar_chart = True
        self.normalize_by_edgepnl = False
        self._main_plot_drag_start = None
        self.pnl_data = {}
        self.ax_spot = None
        self.update_tradpnl_enabled = True
        self.annotation_enabled = True
        self.live_mode = False
        self.data_subscriber = None

        # 图表数据
        self.market_data = []
        self._fitted_data = []
        self._position_data = []

        # SVI配置参数
        self.config = {
            'svi_enabled': True,
            'svi_weights': {
                'fit': 0.1,
                'param': 0,
                'atm': 0.8,
                'time_decay': 20
            },
            'param_weights': {
                'a': 0.3,
                'b': 3.0,
                'rho': 2,
                'm': 0.1,
                'sigma': 0.5
            },
            'atm_weights': {
                'atm_vol': 1,
                'skew': 1.0,
                'convexity': 1.0,
                'otm_slope': 1.0,
                'itm_slope': 1.0
            }
        }

        # 初始化数据管理器
        self.data_manager = DataManager()
        self.data_manager.updateparams(self.custom_r, self.use_new_iv, self.config)

    @Property('QVariant', notify=dataChanged)
    def market_data(self):
        print("Accessing market_data property")  # Debug print
        if self._market_data is not None:
            data = self._market_data.to_dict('records')
            print(f"Converting DataFrame to dict: {len(data)} records")  # Debug print
            return data
        return []
    
    @Property(list)
    def fittedData(self):
        return self._fitted_data
    
    @Property(list)
    def positionData(self):
        return self._position_data
    
    @Property(bool)
    def showBarChart(self):
        return self.show_bar_chart
    
    @showBarChart.setter
    def showBarChart(self, value):
        self.show_bar_chart = value
        self.dataChanged.emit()
    
    @Slot(str)
    def loadFile(self, file_path):
        """加载数据文件"""
        try:
            success = self.data_manager.load_and_process_data(file_path)
            if success:
                self.update_plot_data()
                return True
        except Exception as e:
            print(f"Error loading file: {e}")
        return False
    
    def update_plot_data(self):
        """更新绘图数据"""
        if self.data_manager.current_data is not None:
            self._market_data = self.data_manager.current_data
            print(f"Updated market data: {len(self._market_data)} rows")  # Debug print
            self.dataChanged.emit()
    
    @Slot(float)
    def updateCustomR(self, value):
        """更新自定义r值"""
        self.custom_r = value
        self.data_manager.updateparams(self.custom_r, self.use_new_iv, self.config)
        self.data_manager.cal_current_data()
        self.update_plot_data()
    
    @Slot(bool)
    def setUseNewIv(self, value):
        """设置是否使用新IV"""
        self.use_new_iv = value
        self.data_manager.updateparams(self.custom_r, self.use_new_iv, self.config)
        self.data_manager.cal_current_data()
        self.update_plot_data()
    
    @Slot(bool)
    def toggleLiveMode(self, value):
        """切换实时模式"""
        self.live_mode = value
        if self.data_manager.current_data is not None:
            success = self.data_manager.load_and_process_data(None, live_mode=value)
            if success:
                self.update_plot_data()
    
    @Slot(int)
    def setDecimalPlaces(self, value):
        """设置小数位数"""
        self.decimal_places = value
        self.dataChanged.emit()
