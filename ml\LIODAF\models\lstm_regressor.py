"""
LSTM回归模型实现
@author: lining
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.base import BaseEstimator, RegressorMixin
from torch.utils.data import DataLoader, TensorDataset
import time
from tqdm import tqdm
import pandas as pd

class LSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers=1, dropout=0):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout)
        self.fc = nn.Linear(hidden_size, 1)
    
    def forward(self, x):
        # x shape: (batch_size, seq_length, input_size)
        # 使用批处理以加速计算
        batch_size = x.size(0)
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
        
        out, _ = self.lstm(x, (h0, c0))
        # 只取序列的最后一个时间步的输出
        out = self.fc(out[:, -1, :])
        return out

class LSTMRegressor(BaseEstimator, RegressorMixin):
    """
    基于LSTM的回归模型
    """
    def __init__(self, input_size=None, hidden_size=64, num_layers=1, dropout=0,
                 learning_rate=0.001, epochs=1, batch_size=32, sequence_length=10,
                 early_stopping=5, use_cuda=True, verbose=1):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.batch_size = batch_size
        self.sequence_length = sequence_length
        self.early_stopping = early_stopping
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.verbose = verbose  # 0: 静默, 1: 进度条, 2: 每个epoch详细信息
        self.model = None
        self.device = torch.device("cuda" if self.use_cuda else "cpu")
        
    def _create_sequences(self, X):
        """将输入数据转换为序列形式"""
        # 对于大数据集，使用更高效的方式创建序列
        if self.sequence_length <= 1:
            return X
            
        # 预分配内存以提高效率
        if isinstance(X, np.ndarray):
            n_samples = len(X) - self.sequence_length + 1
            if len(X.shape) > 1:
                seq_data = np.zeros((n_samples, self.sequence_length, X.shape[1]))
                for i in range(n_samples):
                    seq_data[i] = X[i:i+self.sequence_length]
            else:
                seq_data = np.zeros((n_samples, self.sequence_length))
                for i in range(n_samples):
                    seq_data[i] = X[i:i+self.sequence_length]
            return seq_data
        else:
            # 兼容非numpy数组的情况
            n_samples = len(X) - self.sequence_length + 1
            seq_data = []
            for i in range(n_samples):
                seq_data.append(X[i:i+self.sequence_length])
            return np.array(seq_data)
    
    def fit(self, X, y):
        """训练LSTM模型"""
        start_time = time.time()
        
        if self.input_size is None:
            self.input_size = X.shape[1] if len(X.shape) > 1 else 1
        
        # 准备序列数据
        if self.verbose > 0:
            print("准备数据序列...")
        X_seq = self._create_sequences(X)
        y_seq = y[self.sequence_length-1:] if self.sequence_length > 1 else y
        
        # 将pandas Series转换为numpy数组，然后reshape
        y_numpy = y_seq.values if hasattr(y_seq, 'values') else np.array(y_seq)
        y_numpy = y_numpy.reshape(-1, 1)
        
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        y_tensor = torch.FloatTensor(y_numpy).to(self.device)
        
        # 使用DataLoader加速批处理
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        # 创建模型
        if self.verbose > 0:
            print(f"创建LSTM模型: 输入维度={self.input_size}, 隐藏层大小={self.hidden_size}, 层数={self.num_layers}")
            print(f"运行设备: {self.device}")
            
        self.model = LSTMModel(self.input_size, self.hidden_size, self.num_layers, self.dropout).to(self.device)
        
        # 使用优化器和损失函数
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        
        # 早停机制的变量
        best_loss = float('inf')
        patience_counter = 0
        
        # 显示训练信息
        if self.verbose > 0:
            print(f"开始训练: {self.epochs}轮, 批大小={self.batch_size}, 学习率={self.learning_rate}")
            print(f"数据集大小: {len(X_seq)} 样本, {len(dataloader)} 批次")
            print("-" * 50)
            
        # 训练循环
        epoch_iter = tqdm(range(self.epochs), desc="训练进度", disable=self.verbose == 0)
        history = {'loss': [], 'val_loss': []}
        
        for epoch in epoch_iter:
            self.model.train()
            epoch_loss = 0.0
            batch_count = 0
            
            # 创建批次进度条
            batch_iter = tqdm(
                dataloader, 
                desc=f"Epoch {epoch+1}/{self.epochs}", 
                leave=False, 
                disable=self.verbose < 2
            )
            
            # 训练一个epoch
            for batch_X, batch_y in batch_iter:
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
                
                # 更新批次进度条
                if self.verbose >= 2:
                    batch_iter.set_postfix({"loss": f"{loss.item():.6f}"})
            
            # 计算当前epoch的平均损失
            avg_loss = epoch_loss / batch_count
            history['loss'].append(avg_loss)
            
            # 更新总进度条
            if self.verbose >= 1:
                epoch_iter.set_postfix({
                    "loss": f"{avg_loss:.6f}", 
                    "best": f"{best_loss:.6f}",
                    "patience": f"{patience_counter}/{self.early_stopping}"
                })
            
            # 检查早停条件
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
                # 保存最佳模型
                best_model_state = self.model.state_dict().copy()
                if self.verbose >= 2:
                    print(f"Epoch {epoch+1}: 新的最佳模型 (loss={best_loss:.6f})")
            else:
                patience_counter += 1
                if patience_counter >= self.early_stopping and self.early_stopping > 0:
                    # 恢复最佳模型
                    self.model.load_state_dict(best_model_state)
                    if self.verbose >= 1:
                        print(f"早停在第 {epoch+1} 轮，共 {self.epochs} 轮 (best_loss={best_loss:.6f})")
                    break
        
        # 训练完成，显示总结
        training_time = time.time() - start_time
        if self.verbose >= 1:
            print("-" * 50)
            print(f"训练完成，耗时: {training_time:.2f} 秒")
            print(f"最佳损失: {best_loss:.6f}")
            
        # 恢复最佳模型
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
            
        return self
    
    def predict(self, X):
        """使用训练好的模型进行预测"""
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        # 保存原始数据的索引，用于后续返回结果
        original_index = X.index if hasattr(X, 'index') else None
        
        # 准备序列数据
        X_seq = self._create_sequences(X)
        
        # 转换为PyTorch张量并预测
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        
        # 使用更大的批量进行预测以提高速度
        batch_size = min(1024, len(X_tensor))
        dataset = TensorDataset(X_tensor)
        dataloader = DataLoader(dataset, batch_size=batch_size)
        
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for (batch_X,) in dataloader:
                batch_preds = self.model(batch_X).cpu().numpy()
                predictions.append(batch_preds)
        
        # 合并预测结果
        pred_values = np.vstack(predictions).flatten()
        
        # 如果使用了序列处理，预测结果的长度将比原始数据少 (sequence_length-1)
        # 需要调整索引以匹配预测结果的长度
        if original_index is not None and self.sequence_length > 1:
            # 对于序列模型，预测结果对应的是序列的最后一个时间步，因此需要调整索引
            adjusted_index = original_index[self.sequence_length-1:]
            # 确保预测结果长度与调整后的索引长度一致
            if len(pred_values) != len(adjusted_index):
                # 如果长度不匹配，截断至较短的长度
                min_len = min(len(pred_values), len(adjusted_index))
                pred_values = pred_values[:min_len]
                adjusted_index = adjusted_index[:min_len]
            
            # 返回带有调整后索引的pandas Series
            return pd.Series(pred_values, index=adjusted_index)
        
        # 没有索引或不需要处理序列时，直接返回numpy数组
        if original_index is None:
            return pred_values
        
        # 如果原始数据有索引但不需要序列处理，确保结果长度匹配
        if len(pred_values) != len(original_index):
            min_len = min(len(pred_values), len(original_index))
            pred_values = pred_values[:min_len]
            original_index = original_index[:min_len]
        
        # 返回带有原始索引的pandas Series
        return pd.Series(pred_values, index=original_index) 