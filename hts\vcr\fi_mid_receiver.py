# Author : gp
# Date : 20240319

import requests
import websocket
import json


class md_config:
    # ws_addr = 'ws://160.14.228.64:5557'  # 开发环境地址
    # http_addr = 'http://160.14.228.64:5555'
    ws_addr = 'ws://168.231.2.88:22919'              # 生产环境地址
    http_addr = 'http://168.231.2.88:22918'
    user_name = 'ln'
    password = 'ln'
    msg_name = 'time_curve_minute'
    filter_key = 'curve_name'
    filter_value_list = 'a_20231013_atmvol'
    token = ''


def on_open(ws):
    # 发送认证请求
    init_str = json.loads('{"type":"token_sub","data":{"token":0,"disable_sub_all":1}}')
    init_str["data"]["token"] = md_config.token;
    print("send init req : ", init_str)
    ws.send(json.dumps(init_str))
    # 发送订阅请求
    subscribe_json = json.loads(
        '{"seqno":1,"type":"table_action",'
        '"data":{"msg_name":"","action":"sub","interval_ms":0,"disable_snap":0,"filter_key":"","filter_value_list":""}'
        '}')
    subscribe_json["seqno"] = 1
    subscribe_json["data"]["msg_name"] = md_config.msg_name
    subscribe_json["data"]["filter_key"] = md_config.filter_key
    subscribe_json["data"]["filter_value_list"] = md_config.filter_value_list
    print("send subscribe req : ", subscribe_json)
    ws.send(json.dumps(subscribe_json))


def on_message(ws, message):
    print("Received message:", message)
    json_data = json.loads(message)
    if (json_data['type'] == md_config.msg_name):
        md_json = json_data['data']
        print("receive data ", md_json)


def on_error(ws, error):
    print("Error:", error)


def on_close(ws):
    print("Connection closed")


def main():
    # 发送http登陆请求
    url = md_config.http_addr + "/auth/login"
    login_req_json = json.loads('{"type":"login_req","data":{"user_name":"","password":""}}')
    login_req_json["data"]["user_name"] = md_config.user_name
    login_req_json["data"]["password"] = md_config.password
    r = requests.post(url, json=login_req_json)
    for cookie in r.cookies.keys():
        if cookie == "token":
            md_config.token = r.cookies.get(cookie)
    if (md_config.token == ''):
        print("ERROR get token ", r.text, r.cookies)
        return
    print("token ", md_config.token, r.text, r.cookies)
    # websocket
    ws = websocket.WebSocketApp(md_config.ws_addr, on_message=on_message, on_error=on_error, on_close=on_close)
    ws.on_open = on_open
    ws.run_forever()


if __name__ == "__main__":
    main()
