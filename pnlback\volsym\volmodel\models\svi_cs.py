import numpy as np
import sys
import os

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

from pnlback.volsym.volmodel.utils import get_data,find_best_method
import scipy.optimize as opt

class VolatilityModel:
    params_name = ['a','b','rho','m','sigma']
    

    def __init__(self,T,x,weights,totalv,bounds=True):
        self.params = None
        self.T = T
        self.x = x
        self.weights = weights
        self.totalv = totalv
        self.initial_bounds = [
                (-1e-2, max(self.totalv)),  # a: [1e-5, max(w_market)]
                (1e-9, 0.999),  # b: (0.001, 1)
                (-0.999, 999),  # rho: (-1, 1)
                (2 * np.min(self.x), 2 * np.max(self.x)),  # m: [2*min(k), 2*max(k)]
                (1e-9, np.inf)  # sigma: [0.01, 1]
            ]
        if bounds:
            self.bounds = self.initial_bounds
        else:
            self.bounds = None

        self.initial_params = [0,0.05,0.0,0.0,0.02]
        self.params = self.initial_params

    def variance(self, x):
        """计算方差 w(x)"""
        a,b,rho,m,sigma = self.params
        return a + b * (rho * (x - m) + np.sqrt((x - m) ** 2 + sigma ** 2))

    def fitvol(self, x):
        """计算fitvol"""
        return np.sqrt(self.variance(x) / self.T)

    def get_results(self, ):
        """计算所有SVI相关结果"""
        results = {}
        a,b,rho,m,sigma = self.params

        # ATM波动率
        results['atm_vol'] = np.sqrt(self.variance(0) / self.T)

        # 标准化偏斜
        results['skew'] = b * (rho - m / np.sqrt(m ** 2 + sigma ** 2)) / (
                2 * results['atm_vol'] * self.T)

        # 凸性
        results['convexity'] = b * sigma ** 2 / (m ** 2 + sigma ** 2) ** 1.5

        # 计算slope
        results['cslope'] = b * (rho + 1) - 2 * results['atm_vol'] * np.sqrt(self.T) * results['skew']
        results['pslope'] = 2 * results['atm_vol'] * np.sqrt(self.T) * results['skew'] - b * (rho - 1)

        return results

    @staticmethod
    def inverse_transform(atmvol, skew, convex, cslope, pslope, T):
        """逆变换：从特征值计算SVI参数"""
        # 计算参数
        a = T * atmvol ** 2 - (
                4 * cslope * pslope * (atmvol * np.sqrt(T) * skew * (pslope - cslope) + cslope * pslope)) / (
                    convex * (cslope + pslope) ** 2)
        b = (cslope + pslope) / 2
        rho = (cslope - pslope + 4 * atmvol * np.sqrt(T) * skew) / (cslope + pslope)
        m = 2 * cslope * pslope * (cslope - pslope) / (convex * (cslope + pslope) ** 2)
        try:
            sigma = 4 * (cslope * pslope) ** 1.5 / (convex * (cslope + pslope) ** 2)
        except:
            print('sigma error',cslope,pslope,convex)
            sigma = 0.1

        return a, b, rho, m, sigma
    
    def g_function(self, k, params):
        """计算无套利约束函数g(k) - 原始版本"""
        a, b, rho, m, sigma = params
        # 计算总方差及其导数
        w = a + b * (rho * (k - m) + np.sqrt((k - m) ** 2 + sigma ** 2))
        # 一阶导数
        dw_dk = b * (rho + (k - m) / np.sqrt((k - m) ** 2 + sigma ** 2))
        # 二阶导数
        d2w_dk2 = b * sigma ** 2 / ((k - m) ** 2 + sigma ** 2) ** 1.5
        # 计算g(k)函数
        term1 = (1 - (k * dw_dk) / (2 * w)) ** 2
        term2 = (dw_dk ** 2) / 4 * (1 / w + 1 / 4)
        g = term1 - term2 + d2w_dk2 / 2

        return g

    def arbitrage_constraints(self, params, epsilon=-0.01):
        """生成无套利约束条件"""
        constraints = []

        # 1. 蝶式套利约束：g(k) > epsilon
        for k_val in self.x:
            constraints.append({
                'type': 'ineq',
                'fun': lambda p, k=k_val: self.g_function(k, p) - epsilon,
                'message': f'g(k) {self.g_function(k_val,params):.2e} > {epsilon} at k={k_val:.2f}'
            })

        # 2. 参数边界约束
        a, b, rho, m, sigma = params

        # 确保最小总方差为正
        min_w = a + b * sigma
        # constraints.append({'type': 'ineq', 'fun': lambda p: p[0] - 1e-5,'message':'a > 0'})  # a > 0
        constraints.append({'type': 'ineq', 'fun': lambda p: min_w - 1e-5,'message':f'min(w) > {epsilon} {a:.2e} {b:.2e} {rho:.2e} {m:.2e} {sigma:.2e}'})  # min(w) > ε

        # 右翼斜率约束 (Roger Lee矩公式)
        right_slope = b * (1 + rho)
        constraints.append({'type': 'ineq', 'fun': lambda p: 2 - right_slope - 1e-5,'message':'b(1+ρ) < 2'})  # b(1+ρ) < 2

        return constraints
    
    #  Butterfly arbitrage constraints
    #  a−mb(ρ+1) 4−a+mb(ρ+1) −b2(ρ+1)2 >0
    #  a−mb(ρ−1) 4−a+mb(ρ−1) −b2(ρ−1)2 >0
    #  0<b2(ρ+1)2 < 4
    #  0<b2(ρ−1)2 < 4
    def Butterfly_arbitrage_constraints(self,params):
        a,b,rho,m,sigma = params
        constraints = []
        constraints.append({'type': 'ineq', 'fun': lambda p: a-m*b*(rho+1)/(4-a+m*b*(rho+1)-b**2*(rho+1)**2)+1e-2,'message':'right_butterfly_arbitrage'})
        constraints.append({'type': 'ineq', 'fun': lambda p: a-m*b*(rho-1)/(4-a+m*b*(rho-1)-b**2*(rho-1)**2)+1e-2,'message':'left_butterfly_arbitrage'})
        constraints.append({'type': 'ineq', 'fun': lambda p: 4-b**2*(rho+1)**2,'message':'4-b**2*(rho+1)**2>0'})
        constraints.append({'type': 'ineq', 'fun': lambda p: b**2*(rho+1)**2,'message':'b**2*(rho+1)**2>0'})
        constraints.append({'type': 'ineq', 'fun': lambda p: 4-b**2*(rho-1)**2,'message':'4-b**2*(rho-1)**2>0'})
        constraints.append({'type': 'ineq', 'fun': lambda p: b**2*(rho-1)**2,'message':'b**2*(rho-1)**2>0'})
        return constraints
    
    def calculate(self,x):
        return self.variance(x)
    
    def calc_rmse(self,x,weights,totalv):
        return np.sum(np.square(weights * (self.calculate(x) - totalv)))
    
    # 定义目标函数
    def objective(self,params):
        self.params = params
        rmse = self.calc_rmse(self.x,self.weights,self.totalv)
        # if not self.bounds_check(params,self.initial_bounds):
        #     return 1e10
        # if not self.constrains_check(params,self.arbitrage_constraints(params)):
        #     return 1e10
        return rmse

    def bounds_check(self,params,bounds,verbose=False):
        # 检查边界是否存在
        if bounds is None:
            print('bounds is None')
            return True
        bounds_error =0
        for i, (param, (lower, upper)) in enumerate(zip(params, bounds)):
            if param < lower or param > upper:
                bounds_error+=1
                if verbose:
                    print(f'{self.params_name[i]} out of bounds {param:.2e} min {lower:.2e} max {upper:.2e}')
                else:
                    return False
        if bounds_error>0:
            return False
        return True

    def constrains_check(self,params,constraints,verbose=False):
        arb=0
        for i,constraint in enumerate(constraints):
            if constraint['fun'](params)<0:
                arb+=1
                if verbose:
                    print(f'{i} {constraint["fun"](params):.2e} {constraint["message"]}')
                else:
                    return False
        if arb>0:
            return False
        return True

    def fit2(self,epsilon=0.01,method='SLSQP',verbose=False):
        """执行稳健校准"""
        # 无套利约束
        constraints = self.arbitrage_constraints(self.initial_params, epsilon)

        # 执行优化
        result = opt.minimize(
            fun=self.objective,
            x0=self.initial_params,
            # args=(self.weights,),
            method=method,
            bounds=self.bounds,
            constraints=constraints,
            options={'maxiter': 100, 'ftol': 1e-18}
        )

        if not result.success:
            print("优化警告:", result.message)

        self.opt_params = result.x

        self.params = result.x
        sigma_fit = self.calculate(self.x)
        results = self.get_results()

        return sigma_fit, self, result.fun, method ,results

    def fit(self,method=None,verbose=False):
        """拟合波动率模型并计算特征值"""
        # 创建模型
        self.params = self.initial_params

        # 执行拟合
        best_result, best_rmse, best_method = find_best_method(self.objective, self.initial_params, self.bounds,verbose=verbose,method=method)
            
        self.params = best_result.x
        sigma_fit = self.calculate(self.x)
        results = self.get_results()
        if verbose:
            print(self.T.round(3)*252,{k: round(float(v), 4) if isinstance(v, (int, float)) else v for k, v in results.items()})

        return sigma_fit, self, best_rmse, best_method ,results


def fit_svi_model(config, exp_data, exp, last_voltime):
    """使用SVI模型拟合波动率曲线

    Args:
        config: 配置参数
        exp_data: 期权数据
        exp: 到期日
        last_exp_data: 上一次的拟合结果

    Returns:
        tuple: (sigma_fit, voltime, derivatives)
    """
    # 准备输入数据
    F,K,T,x,totalv,imp_vol,weights,x_raw = get_data(exp_data,exe_cut=config.get('exe_cut',np.exp(1)))
    
    try:
        model = VolatilityModel(T,x,weights,totalv,bounds=False)
        # 获取上次的参数作为初始值
        if exp in last_voltime:
            model.initial_params = list(last_voltime[exp]['params'].values())
        totalv_fit, model,opt_rmse,best_method,results = model.fit(verbose=False,method="Powell")
        sigma_fit=np.sqrt(model.calculate(x_raw)/T) # 计算拟合结果
    except Exception as e:
        print('fit error',e)
        return None,None,None

    vol_diff = sigma_fit - imp_vol
    px_diff = vol_diff*exp_data['vega']/0.0001*100
    sum_px_diff = np.sum(np.abs(px_diff))

    para_dict = dict(zip(model.params_name, model.params))

    voltime = {
        'params': para_dict,
        'atm_features': results,
        'rmse_error': opt_rmse*1e10,
        'best_method': best_method,
        'sum_px_diff': sum_px_diff,

    }

    return exp, sigma_fit, voltime

