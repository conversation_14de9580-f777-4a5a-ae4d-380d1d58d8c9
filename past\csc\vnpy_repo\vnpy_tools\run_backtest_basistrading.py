#%% 
'''IP SETTING FOR INFLUXDB'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('SHFE.prod')
import warnings
warnings.filterwarnings('ignore')
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机""
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.basisTrading_mm import BasisTrading_mm
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
 
#%%
maker='rb2209.SHFE'
refer='rb2210.SHFE'

multiplier=5

engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=True,fast_join=True,refer_late = True,refer_test=False) #Counter代表是否只统计对价成交，duty代表是否统计义务

engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime.datetime(2022, 3,24,21, 0), # 开始时间
    end=datetime.datetime(2022, 3,25,15,0), # 结束时间
    rates={maker: 0,refer: 0.0001}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: 5,refer: 5}, # 一个tick大小
    capital=1_000_000, # 初始资金
)
# 添加回测策略，并修改内部参数

#%%
engine.clear_data()
engine.add_strategy(BasisTrading_mm, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'maxPos':30,'path':500,'eta':1,'x_stop':2,'net_limit':5,'edge':3})
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
duty = engine.duty_statistics()
#engine.show_tick_chart() # 显示图表

 #%%
from vnpy_tools.PostTradeAnalysis import PostTradeAnalysis
Analysis = PostTradeAnalysis(engine,multiplier)
<<<<<<< HEAD
<<<<<<< HEAD
# Analysis.plot_backtestresults_macd() #绘制买卖点
Analysis.plotbacktestresults_meanreversion() #绘制布林带
# Analysis.execution_pnl()
=======
Analysis.plotbacktestresults_meanreversion() 
>>>>>>> 7c6857f7637a8475fb4eabc989062f45d62f45d0
=======
_df = Analysis.plotbacktestresults_meanreversion() 

#%%
trades = pd.DataFrame([{'datetime':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
           'midPrice':engine.trades[x].midPrice} for x in engine.trades])
orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
           } for x in engine.get_all_orders()])

total_pnl = df["net_pnl"].sum()
print('总盈亏:',total_pnl)
print('逐笔盈亏:',total_pnl/len(trades[trades.symbol==maker.split('.')[0]]))
orders_main = orders[orders.symbol==refer.split('.')[0]]
print('主力撤单次数：',len(orders_main[orders_main.traded==0]))
print('成交量：',len(trades[trades.symbol==maker.split('.')[0]]))

trades_maker = trades[trades.symbol == maker.split('.')[0]]
print('做市合约总成交：',len(trades_maker))
trades_maker['willing'] = trades_maker.comments.apply(lambda x: 1 if x=='down_buy' else -1)
trades_maker_unwilling = trades_maker[trades_maker.direction != trades_maker.willing]
print('做市合约不符合预期的成交：',len(trades_maker_unwilling))

>>>>>>> 515bad9604803e1fb3e68255e718a85ca076e085
#%%
#盘后分析

import pandas as pd
import numpy as np
from datetime import timedelta

mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
           'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
           'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
           'last_price','volume','turnover']]
mkt_maker = mkt[mkt.symbol==engine.strategy.maker.split('.')[0]]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))

mkt_main = mkt[mkt.symbol==engine.strategy.refer.split('.')[0]]
mkt_main['dV'] = mkt_main.volume - mkt_main.volume.shift(1)
mkt_main['dT'] = (mkt_main.turnover - mkt_main.turnover.shift(1)).apply(lambda x:str(x))
 
trade_split = pd.DataFrame(engine.trade_split,columns=['time','vt_symbol','trade']) #成交拆分
trade_split = trade_split[trade_split.vt_symbol==maker]

trades = pd.DataFrame([{'datetime':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
           'midPrice':engine.trades[x].midPrice} for x in engine.trades])
trades['net'] = (trades.volume*trades.direction).cumsum()
cash = (trades['direction']*(-1)*trades['volume']*trades['price'])*multiplier
cashCum = np.cumsum(cash)
trades['pnl'] = cashCum + trades.net*trades.price*multiplier
orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
           } for x in engine.get_all_orders()])

#%%
import pandas as pd
import numpy as np
execution_pnl_actual = 0
execution_pnl_next = 0
exec_pnl_actual_list= []
exe_pnl_next_list=[]



for i in trades.index:
    if trades.loc[i,'symbol'] == maker.split('.')[0]:
        execution_pnl_actual += (trades.loc[i,'price'] - trades.loc[i,'midPrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier    
        execution_pnl_next += (trades.loc[i,'price'] - trades.loc[i,'midPrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier  
    if trades.loc[i,'symbol'] == refer.split('.')[0]:
        execution_pnl_actual += (trades.loc[i,'price'] - trades.loc[i-1,'basePrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier   
        execution_pnl_next += (trades.loc[i,'midPrice_next'] - trades.loc[i-1,'basePrice']) * -1 * trades.loc[i,'direction'] * trades.loc[i,'volume'] *multiplier

total_pnl = df["net_pnl"].sum()
<<<<<<< HEAD
trade_N = trades.shape[0]*0.5
print('理论盈亏:',total_pnl - execution_pnl_actual)
print('实际盈亏:',total_pnl)
print('执行盈亏:',execution_pnl_actual)
print('主力下一刻中价执行盈亏:',execution_pnl_next)
print('每笔理论盈亏:',(total_pnl-execution_pnl_actual)/trade_N)
print('每笔执行盈亏',execution_pnl_actual/trade_N)
print('每笔中价执行盈亏',execution_pnl_next/trade_N)
print('每笔实际盈亏:',(total_pnl)/trade_N)



=======
print('总盈亏:',total_pnl)
print('实际执行盈亏:',execution_pnl_actual)
print('中价执行盈亏:',execution_pnl_next)
>>>>>>> 7c6857f7637a8475fb4eabc989062f45d62f45d0

<<<<<<< HEAD
=======
#%%
import time
# 批量回测结果, 考察basis_Trading的报价时间, 逐笔盈亏, 逆向选择等情况
date_list = [(datetime.datetime(2022,2, 14+i, 21, 10), datetime.datetime(2022,2, 15+i, 15, 00)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 18, 21, 10), datetime.datetime(2022,2, 21, 15, 00))]
date_list += [(datetime.datetime(2022,2, 21+i, 21, 10), datetime.datetime(2022,2, 22+i, 15, 00)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 25, 21, 10), datetime.datetime(2022,2, 28, 15, 00)), (datetime.datetime(2022,2, 28, 21, 10), datetime.datetime(2022,3, 1, 15, 00))]
date_list += [(datetime.datetime(2022,3, 1+i, 21, 10), datetime.datetime(2022,3, 2+i, 15, 00)) for i in range(3)]
date_list += [(datetime.datetime(2022,3, 4, 21, 10), datetime.datetime(2022,3, 7, 15, 00))]
date_list += [(datetime.datetime(2022,3, 7+i, 21, 10), datetime.datetime(2022,3, 8+i, 15, 00)) for i in range(4)] 
 

name = 'basis_trading'
number = 0
parameter_list = [('rb2209.SHFE', 'rb2210.SHFE', 10, 1), ('hc2209.SHFE', 'hc2210.SHFE', 10, 1)] # maker, refer, multiplier, tick
typ_list = [1, 2]
 
edge_list = [3, 4]
# refer_detection_list = [True, False]
 
result1 = {
    'maker':[],
    'typ':[],
    'edge':[],
    'market_time':[],
    'my_time':[],    
 
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []
 }
save_flag = False
save_risk=True
save_strategy=False 
save_order=True
save_trade=True
count = 0
t = time.time()
for start, end in date_list:
    for maker, refer, multiplier, pricetick in parameter_list:
        # refer=maker
        save_mkt = True

        for edge in edge_list[:]:
            for typ in typ_list:

                count += 1
                try:
                    parameters = {}    
                    engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=True,fast_join=True,refer_late = True,refer_test=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
                    
                    engine.set_parameters(
                        vt_symbols=[maker,refer], # 回测品种
                        interval=Interval.TICK, # 回测模式的数据间隔
                        start=start, # 开始时间
                        end=end, # 结束时间
                        rates={maker: 0,refer: 0.0001}, # 手续费率
                        slippages={maker: 0,refer: 0}, # 滑点
                        sizes={maker: multiplier,refer: multiplier}, # 合约规模
                        priceticks={maker: pricetick,refer: pricetick}, # 一个tick大小
                        capital=1_000_000, # 初始资金
                    )
                    # 添加回测策略，并修改内部参数
                    
                    engine.clear_data()
                    engine.add_strategy(BasisTrading3, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'maxPos':30,'path':500,'eta':1,'x_stop':2,'net_limit':5, 'edge':edge, 'type':typ})
                    engine.load_data() # 加载历史数据
                    engine.run_backtesting() # 进行回测
                    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
                    stat = engine.calculate_tick_statistics() # 统计日度策略指标
                    duty = engine.duty_statistics()
                    

                    for key in engine.result.keys():
                        result1[key].append(engine.result[key])  
                    result1['maker'].append(maker)
                    # result1['maxPos'].append(maxPos)
                    # result1['secondConfirmationFlag'].append(secondConfirmationFlag)

                    result1['typ'].append(typ)
                    result1['edge'].append(edge)
                    try:
                        result1['market_time'].append(duty[maker]['market_time'])
                        result1['my_time'].append(duty[maker]['my_time'])
                    except:
                        result1['market_time'].append(0)
                        result1['my_time'].append(0)                        

                    # result1['主力撤单次数'].append(df_orders_cancel.shape[0])
                    df_mkt = pd.DataFrame()
                    if save_flag:
                        if save_risk:
                            df_mkt = engine.get_risk(multiplier=multiplier)
                        else:
                            df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
                        if save_strategy:
                            df_strategy = engine.get_strategy()
                        else:
                            df_strategy = pd.DataFrame([])
                        engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade, save_mkt=save_mkt)
                        if save_mkt:
                            save_mkt = False
                    result2 = pd.DataFrame(result1)
                    result2.to_csv('result_basis_trading.csv')
                    print(time.time()-t, count, 'maker={}'.format(maker), 'typ={}'.format(typ), 'edge={}'.format(edge), 'success')
                except Exception as err:
                    print(time.time()-t, count, 'maker={}'.format(maker), 'typ={}'.format(typ), 'edge={}'.format(edge), 'failed')
                    print(err)






#%%
def run_backtest(startdate, enddate, maker, refer, maxPos = 30, save_flag = False, name = 'basis_trading', number = 0,path = 500,eta = 1, onlyCrossFlag=False):
    save_strategy=True
    save_risk = True
    save_order = True
    save_trade = True
    onlyCrossFlag=False
    number=number
    tick_size = 1
    duty=False

    lots=1
    redifEWMAFlag=False
    
    parameters = {'path':path, 'eta':eta, 'maxPos':maxPos}
    
    engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=onlyCrossFlag,duty=duty,save_result=False,refer_test=False,rule_out = False,fast_join=True,refer_late = True) #Counter代表是否只统计对价成交，duty代表是否统计义务
    start=datetime.datetime(startdate[0], startdate[1], startdate[2], 21, 0) # 开始时间
    end=datetime.datetime(enddate[0], enddate[1],enddate[2], 15, 0) # 结束时间
    
    engine.set_parameters(
        vt_symbols=[maker,refer], # 回测品种
        interval=Interval.TICK, # 回测模式的数据间隔
    
        start=start, # 开始时间
        end=end, # 结束时间
        rates={maker: 0,refer: 0}, # 手续费率
        slippages={maker: 0,refer: 0}, # 滑点
        sizes={maker: multiplier,refer: multiplier}, # 合约规模
        priceticks={maker: tick_size,refer: tick_size}, # 一个tick大小
        capital=1_000_000, # 初始资金
    )
    
    # 添加回测策略，并修改内部参数
    engine.clear_data()

    engine.add_strategy(BasisTrading2, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'maxPos':maxPos,'path':path,'eta':eta,'x_stop':2,'net_limit':5})
    
    engine.load_data() # 加载历史数据
    engine.run_backtesting() # 进行回测
    
    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
    stat = engine.calculate_tick_statistics() # 统计日度策略指标
    df_mkt = pd.DataFrame()
    if save_flag:
        if save_risk:
            df_mkt = engine.get_risk(multiplier=multiplier)
        else:
            df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
        if save_strategy:
            df_strategy = engine.get_strategy()
        else:
            df_strategy = pd.DataFrame([])
        engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade)
    
    
    # print(engine.result)
    # engine.duty_statistics()
    # from vnpy.trader.analysis import Analysis
    
    # analysis = Analysis(engine)
    # df = analysis.pnl_plot() 
    return engine, df_mkt #, df
# # =======
# # time_list = [7,8,11,12,13,14,15]
# time_list = [7,8]
#%%
>>>>>>> 515bad9604803e1fb3e68255e718a85ca076e085


<<<<<<< HEAD
=======
startdate, enddate, maker, refer, maxPos,path ,eta, number = ([2022, 2, 27], [2022, 2, 28], 'rb2209.SHFE', 'rb2210.SHFE', 30,500,1,0)
engine, df_mkt = run_backtest(startdate, enddate, maker, refer, maxPos,save_flag=True, number=number,path=path,eta=eta)
>>>>>>> 515bad9604803e1fb3e68255e718a85ca076e085

