# encoding: UTF-8


from datetime import datetime, time
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    TickArrayManager
)


########################################################################
class NewVOIStrategy(CtaTemplate):
    """基於Tick的交易策略"""
    className = 'NewVOIStrategy'

    # 策略參數
    fixedSize = 1  # 下單數量
    Ticksize = 2  # 緩存大小
    initDays = 0
    onbook = 0
    N1 = 1
    N2 = 1
    Sigma = 15
    Weight = 1
    SWide = 0
    shiftThreshold1 = 0.75
    shiftThreshold2 = 0.5
    offsetThreshold = 0.3

    # DAY_START = time(8, 45)  # 日盤啟動和停止時間
    # DAY_END = time(13, 45)
    # NIGHT_START = time(15, 00)  # 夜盤啟動和停止時間
    # NIGHT_END = time(5, 00)

    # 策略變數
    posPrice = 0  # 持倉價格
    pos = 0  # 持倉數量

    # 參數列表，保存了參數的名稱
    paramList = ['name',
                 'className',
                 'author',
                 'vtSymbol',
                 'initDays',
                 'Ticksize',
                 'fixedSize',
                 'N1',
                 'N2',
                 'Sigma1',
                 'Sigma2',
                 'Sigma3',
                 'Weight1',
                 'Weight2',
                 'Weight3',
                 'SWide'
                 ]

    # 變數清單，保存了變數的名稱
    varList = ['inited',
               'trading',
               'pos',
               'posPrice'
               ]

    # 同步清單，保存了需要保存到資料庫的變數名稱
    syncList = ['pos',
                'posPrice',
                'intraTradeHigh',
                'intraTradeLow']

    # ----------------------------------------------------------------------
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """Constructor"""
        super(NewVOIStrategy, self).__init__(
            cta_engine, strategy_name, vt_symbol, setting
        )

        # 創建Array佇列
        self.tickArray = TickArrayManager(self.Ticksize, self.N1, self.N2, self.cta_engine.pids)

    # ----------------------------------------------------------------------
    def onminBarClose(self, bar):
        """"""

        # ----------------------------------------------------------------------

    def onInit(self):
        """初始化策略（必須由用戶繼承實現）"""
        self.write_log(u'%s策略初始化' % self.strategy_name)
        # tick級別交易，不需要過往歷史資料
        self.put_event()

    # ----------------------------------------------------------------------
    def onStart(self):
        """啟動策略（必須由用戶繼承實現）"""
        self.write_log(u'%s策略啟動' % self.strategy_name)
        self.put_event()

    # ----------------------------------------------------------------------
    def onStop(self):
        """停止策略（必須由用戶繼承實現）"""
        self.write_log(u'%s策略停止' % self.strategy_name)
        self.put_event()

    # ----------------------------------------------------------------------
    def onTick(self, tick):
        """收到行情TICK推送（必須由用戶繼承實現）"""

        TA = self.tickArray
        TA.updateTradeBook(tick, 0, 'tradebook')
        TA.updateRealBook(0)

        # TA.updateRealBook(tick)
        # TA.updateinnerTrade(tick)
        TA.NewVOIIndex(0)

        shift = TA.AdjCDFShift(self.Sigma, self.Weight)

        if shift:
            if self.pos < 0:
                if TA.AdjVOIArray[-1] >= self.shiftThreshold2:
                    self.buy(TA.TickaskPrice1Array[-1], 2, False)
            elif self.pos > 0:
                if TA.AdjVOIArray[-1] <= -self.shiftThreshold2:
                    self.short(TA.TickbidPrice1Array[-1], 2, False)

            if TA.AdjVOIArray[-1] > self.shiftThreshold1:
                # if self.pos < 0:
                #     self.buy(TA.TickaskPrice1Array[-1], abs(self.pos), False)
                if TA.AdjVOIArray[-2] >= TA.AdjVOIArray[-2]:
                    self.buy(TA.TickaskPrice1Array[-1], self.fixedSize, False)

            if TA.AdjVOIArray[-1] < -self.shiftThreshold1:
                # if self.pos > 0:
                #     self.short(TA.TickbidPrice1Array[-1], abs(self.pos), False)
                if TA.AdjVOIArray[-2] <= TA.AdjVOIArray[-2]:
                    self.short(TA.TickbidPrice1Array[-1], self.fixedSize, False)

        print("self.pos:" + str(self.pos))
        print("---------------------------------------------")

    # ----------------------------------------------------------------------
    def onBook(self, tick):
        # self.onbook += 1
        # print("onBook:" + str(self.onbook ))
        TA = self.tickArray
        TA.updateTradeBook(tick, 0, 'tradebook')
        # TA.NewVOIIndex()
        TA.AdjNewVOIIndex080(0)

        # if not TA.inited:
        #     return

        shift = TA.AdjCDFShift(self.Sigma, self.Weight)
        if shift:
            if self.pos < 0:
                if TA.AdjVOIArray[-1] >= self.shiftThreshold2:
                    self.buy(TA.TickaskPrice1Array[-1], 2, False)
            elif self.pos > 0:
                if TA.AdjVOIArray[-1] <= -self.shiftThreshold2:
                    self.short(TA.TickbidPrice1Array[-1], 2, False)

            if TA.AdjVOIArray[-2] > self.shiftThreshold1:
                # if self.pos < 0:
                #     self.buy(TA.TickaskPrice1Array[-1], abs(self.pos), False)
                if TA.AdjVOIArray[-1] >= TA.AdjVOIArray[-2]:
                    self.buy(TA.TickaskPrice1Array[-1], self.fixedSize, False)

            if TA.AdjVOIArray[-2] < -self.shiftThreshold1:
                # if self.pos > 0:
                #     self.short(TA.TickbidPrice1Array[-1], abs(self.pos), False)
                if TA.AdjVOIArray[-1] <= TA.AdjVOIArray[-2]:
                    self.short(TA.TickbidPrice1Array[-1], self.fixedSize, False)

        print("self.pos:" + str(self.pos))

        TA.initVOI()
        print("---------------------------------------------")

    # ----------------------------------------------------------------------
    def onBar(self, bar):
        """收到Bar推送（必須由用戶繼承實現）"""

    # ----------------------------------------------------------------------
    def onXminBar(self, bar):
        """收到X分鐘K線"""

    # ----------------------------------------------------------------------
    def onOrder(self, order):
        """收到委託變化推送（必須由用戶繼承實現）"""
        pass

    # ----------------------------------------------------------------------
    def onTrade(self, trade):

        self.posPrice = trade.price
        # 同步資料到資料庫
        self.saveSyncData()
        # 發出狀態更新事件
        self.putEvent()

    # ----------------------------------------------------------------------
    def onStopOrder(self, so):
        """停止單推送"""
        pass
