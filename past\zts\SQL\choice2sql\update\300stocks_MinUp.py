# -*- coding:utf-8 -*-
# Author:Lining
# Editdate:2017-9-18
import pyodbc
import pandas as pd

server = '10.25.18.36'
user = 'Alex'
password = '789456'
table = "[Alex].[dbo].[IndexStkInfo300_new]"
table2 = "StkType300_New"

from EmQuantAPI import *
c.start("ForceLogin=1")

# 连接数据库
# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
cursor = conn.cursor()

import sys
sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')
from SQL.choice2sql.update import datemakerSQL

indexnum = 270
dt, beginDate = datemakerSQL.datemakerMin(table, conn, cursor, indexnum)


def tosql():
    num = 0
    sql = "INSERT INTO %s VALUES (?,?,?,?,?,?,?,?,?,?)" % table
    # sql0= "select A.STKID from [Alex].[dbo].[StkType300_new] as A left join [Alex].[dbo].[IndexStkInfo300_new] as B "\
    #       "on A.STKID=B.STKID where B.STKID is null "
    sql0 = "SELECT DISTINCT [STKID] FROM %s" % table2

    pf = pd.read_sql(sql0, conn, index_col=None, coerce_float=True, params=None, parse_dates=None,
                     columns=None, chunksize=None)

    # 通过wset来取数据集数据
    codes = pf['STKID'].values
    for j in range(0, len(codes)):
        # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
        # sys.stdout.write('\r' + u"-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----\r" % (j, codes[j]))
        print("\r", end="")
        print(u"\r-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----" % (j, codes[j]), end="")
        wsddata1 = c.csc(codes[j],"Open,High,Low,Close,Volume,Amount",beginDate,dt,
                        "IsHistory=1,Period=1,AdjustFlag=3,BaseDate=2018-01-02")

        # wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
        if wsddata1.ErrorCode != 0:
            continue
        lenth = len(wsddata1.Data[1])
        num = num + lenth
        num2 = 0
        for i in range(0, lenth):
            sqllist = []

            if len(wsddata1.Dates) > 1:
                sqllist.append(wsddata1.Dates[i][:11])
                sqllist.append(wsddata1.Dates[i][12:])

            sqllist.append(codes[j])

            for k in range(0, len(wsddata1.Indicators)):
                sqllist.append(wsddata1.Data[k][i])

            sqllist.append(-1)

            sqltuple = tuple(sqllist)

            try:
                cursor.execute(sql, sqltuple)
                conn.commit()
            except:  # Exception as e:
                # print(u'str(Exception):\t', str(Exception))
                # print(u'str(e):\t\t', str(e))
                num2 += 1
        if num2 != 0:
            print(lenth, num2)
            num = num - num2

    print(num)


tosql()

conn.close()

