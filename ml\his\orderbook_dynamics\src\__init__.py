"""
Orderbook Dynamics package.

A Python library for analyzing high-frequency limit order book data and
building predictive models for financial market movements.
"""

# Import main package components for easier access
from .models import OrderBook, OpenBookMsg, Side, Cell
from .open_book import OpenBook, OpenBookFile
from .features_extractor import FeaturesExtractor
from .decision_tree_dynamics import DecisionTreeDynamics

# Version information
__version__ = '0.1.0' 