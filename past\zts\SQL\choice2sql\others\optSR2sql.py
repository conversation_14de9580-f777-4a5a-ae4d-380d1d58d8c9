# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-10-20
from WindPy import w
import pyodbc
from datetime import datetime
import pandas as pd

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = "2017-10-19"  # datetime.now()
# beginDate = "2017-06-24"
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
cursor = conn.cursor()
def changesql():
    sqllist = []
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[交易日期  ]','DateTime','column'")
    sqllist.append(u"sp_rename '[<PERSON>].[dbo].[SR].[品种代码   ]','Codes','column'")
    sqllist.append(u"sp_rename '[<PERSON>].[dbo].[SR].[昨结算    ]','PreClose','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[今开盘    ]' ,'OpenPrice','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[最高价    ]' ,'High','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[最低价    ]' ,'Low','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[今收盘    ]','ClosePrice','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[今结算    ]' ,'SettlePrice','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[涨跌1     ]' ,'CHG1','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[涨跌2     ]','CHG2' ,'column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[成交量(手)]' ,'Volume','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[空盘量    ]' ,'Vol_Null','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[增减量    ]','Vol_Change' ,'column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[成交额(万元)]' ,'Amount','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[DELTA     ]','DELTA','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[隐含波动率]','US_Impliedvol','column'")
    sqllist.append(u"sp_rename '[Alex].[dbo].[SR].[行权量]','EXE_VOL','column'")
    sqllist.append(u"""
            IF OBJECT_ID('SR2', 'U') IS NOT NULL 
            DROP TABLE SR2 
            CREATE TABLE SR2 (
            [Codes]  VARCHAR(20) NOT NULL,
            [EXE_DATE] VARCHAR(20),
            [EXE_PRICE] VARCHAR(20)
            )
            """)

    for sql in sqllist:
        cursor.execute(sql)
        conn.commit()

def update():
    sql = "SELECT distinct [Codes] FROM [Alex].[dbo].SR where codes <>'' order by codes "

    pf = pd.read_sql(sql, conn, index_col=["Codes"], coerce_float=True, params=None, parse_dates=None,
                     columns=None, chunksize=None)

    print(pf)

    codes=pf.index.values.tolist()

    codes2=[]
    for i in codes:
        i=i+".CZC"
        codes2.append(i)

    wdata= w.wss(codes2, "lasttradingdate,exe_price","tradeDate=20171030")

    #pf2 = pf.ix['2017-04-24', 'SR707C6200']
    #print(pf2)

    print(wdata)

    sqllist2 = []
    for i in range(0, len(wdata.Data[0])):
        sqllist = []

        sqllist.append(wdata.Codes[i])

        date1=wdata.Data[0][i]
        sqllist.append(date1.strftime('%Y-%m-%d'))
        sqllist.append(wdata.Data[1][i])

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)

    sql2 = "INSERT INTO [Alex].[dbo].[SR2] VALUES (?,?,?)"

    cursor = conn.cursor()

    cursor.executemany(sql2, sqllist2)

    conn.commit()

changesql()
update()

sql = "SELECT a.*,b.EXE_PRICE,b.EXE_DATE into [Alex].[dbo].SR3 FROM [Alex].[dbo].SR as a LEFT JOIN [Alex].[dbo].SR2 as b on a.Codes=left(b.Codes,10) "
cursor.execute(sql)
conn.commit()
sql = "IF OBJECT_ID('SR', 'U') IS NOT NULL DROP TABLE SR"
cursor.execute(sql)
conn.commit()
sql = "IF OBJECT_ID('SR2', 'U') IS NOT NULL DROP TABLE SR2"
cursor.execute(sql)
conn.commit()
