% load('skew_data.mat')
% load('vix_data.mat')
% load('left_date.mat')

vix_index=nan(length(left_date),1);
skew_index=nan(length(left_date),1);



for count=1:length(left_date)
    
    if left_date(count,1)>=7
        w1=(left_date(count,2)-30)/(left_date(count,2)-left_date(count,1));
        w2=(30-left_date(count,1))/(left_date(count,2)-left_date(count,1));
        
        vix_index(count,1)=w1*vix_data(count,2)+w2*vix_data(count,3);
        skew_index(count,1)=w1*skew_data(count,2)+w2*skew_data(count,3);
    else
        vix_index(count,1)=vix_data(count,3);
        skew_index(count,1)=skew_data(count,3);
        
    end
    
end



t=skew_data(:,1);

mark_1=t(1);
mark_2=t(round(length(t)/8));
mark_3=t(round(length(t)*2/8));
mark_4=t(round(length(t)*3/8));
mark_5=t(round(length(t)*4/8));
mark_6=t(round(length(t)*5/8));
mark_7=t(round(length(t)*6/8));
mark_8=t(round(length(t)*7/8));
mark_9=t(length(t));


[AX,H1,H2]=plotyy(t,vix_index,t,skew_index);
    set(AX,'Xlim',[min(t) max(t)],'xtick',[mark_1,mark_2,mark_3,mark_4,mark_5,mark_6,mark_7,mark_8,mark_9])
    set(AX,'xticklabel',{datestr(mark_1,26),datestr(mark_2,26),datestr(mark_3,26),datestr(mark_4,26),datestr(mark_5,26),datestr(mark_6,26),datestr(mark_7,26),datestr(mark_8,26),datestr(mark_9,26)})
    set(H1,'linewidth',1.5)
    set(H2,'linewidth',1.5)
    set(get(AX(1),'ylabel'),'string','VIX');
    set(get(AX(2),'ylabel'),'string','SKEW');    
    title('VIX and SKEW')
    grid on; 

