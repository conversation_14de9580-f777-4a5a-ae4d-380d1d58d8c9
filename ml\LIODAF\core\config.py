"""
配置文件，用于存储订单簿分析框架的配置参数
@author: lining
"""
from sklearn.preprocessing import RobustScaler
from datetime import datetime

SCALER = RobustScaler()
# 数据路径配置
DATA_PATH = "G:\\DATA\SH500\\md_%s_cffex_multicast.parquet"
DATE_STR = ["20250620",datetime.now().strftime("%Y%m%d")][1] # 数据日期
DAYSNUM = 15 # 数据天数
TEST_SIZE = 0.2 # 测试集比例
CODE_LIST = ['IM2509', ] # 合约列表, 如果没有具体月份，则使用近月合约
IS_SUB_CONTRACT = True
SUB_CODE_LIST = ['IC2509', ]
MULT = 300 if CODE_LIST[0].startswith(('IF', 'IH')) else 200
MIN_TICK = 0.2 # 最小变动单位
SUB_MULT = 300 if SUB_CODE_LIST[0].startswith(('IF', 'IH')) else 200
TIME_RANGE = [('09:15:00', '11:29:00'), ('13:00:00', '14:57:00')]
MODEL_PATH = ["output\\model_future_vwap_return_10_20250430_143918.json", "output\\models\\autogluon",None][2]

BACKMODE = ['rolling', 'normal', 'days'][2]  # 模式：0滚动窗口 1正常 2数据天数
OUTDIR="output/"

# 特征配置
WINDOW_SIZES = [5, 10, 20, 30]
EVALUATE_HORIZONS = [2,6,10,20,]
PREDICTION_HORIZONS = [6, ]
TARGET_LABEL = {0:"future_vwap_return", 1:"future_return_avg_mid", 2:"return_class", 3:"return_class2",4:"future_volatility_return",5:"future_volatility_return"}[0]
TARGET_COLS = [f"{TARGET_LABEL}_{period}" for period in PREDICTION_HORIZONS]

MODEL_TYPE = {0:'lightgbm',1:'linear', 2:'xgboost', 3:'gbdt', 4:'random_forest', 5:'svm', 6:'mlp', 7:'knn', 8:'decision_tree', 9:'extra_trees', 10:'lstm', 11:'deeplob', 12:'autogluon'}[0]
MODEL_PARAMETER_SEARCH_METHOD = 'bayesian'
# 模型交叉验证折数
CV_N_SPLITS = 3  # 交叉验证折数，较小折数(如3)训练更快，较大折数(如5)评估更全面

TRAIN_TEST_RATIO = 0.8
NUM_BOOST_ROUND = 200
EARLY_STOPPING_ROUNDS = 20
ROLLING_WINDOW_SIZE = 3000

VISUALIZE_TOP_N = 200 # 可视化前N个重要特征


# 回测配置
EXE_NEXT = True # 是否检查下一幅行情,是否满足发单价格
QUANTILE_LIST = [0.5, 0.80, 0.90, 0.95]  # 生成信号的阈值分位数列表
THRESHOLD_PERCENT = QUANTILE_LIST[-2]  # 生成信号的阈值分位数
CLOSING_THRESHOLD = QUANTILE_LIST[-3] # 平仓阈值分位数
ACTUAL_THRESHOLD = 0.5*MIN_TICK/6000 # 计算准确率时，实际阈值/tick
HOLDING_PERIOD = 10  # 持仓周期,秒
COMMISSION = 0.000023  # 手续费%
MAX_POSITION = 5.0  # 最大持仓量
STOP_LOSS = 0.001  # 止损%
TAKE_PROFIT = 0.0002  # 止盈%
MIN_PROFIT = 0.002  # 最小盈利%
USE_MID_PRICE = True  # 使用中间价
ORDER_SIZE = 1  # 每次开仓数量
SPREAD_THRESHOLD = 0.5*MIN_TICK  # 价差阈值/tick

# 日志控制配置
LOG_CONFIG = {
    'enabled': True,  # 总开关
    'levels': {
        'info': True,    # 信息级别开关
        'warning': True, # 警告级别开关
        'error': True    # 错误级别开关
    }
}

# 模型配置 
MODEL_SETTING = {
    'linear': {'params': {}},
    'xgboost': {
        'params': {
            'learning_rate': 0.1,
            'max_depth': 6,
            'min_child_weight': 1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'gamma': 0,
            'objective': 'reg:squarederror',
            'eval_metric': 'rmse',
            'nthread': 4,
            'seed': 42
        },
        'param_grid': {
            'learning_rate': [0.01, 0.05, 0.1],
            'max_depth': [3, 6, 9],
            'min_child_weight': [1, 2, 3],
            'subsample': [0.7, 0.8, 0.9],
        },
    },
    'lightgbm': {
        'params': {
            'learning_rate': 0.05,
            'max_depth': 6,
            'min_child_weight': 2,
            'subsample': 0.8,
            'colsample_bytree': 0.8, 
            'objective': 'regression',
            'metric': 'rmse',
        },
        'param_grid': {
            'learning_rate': [0.01, 0.05, 0.1],
            'max_depth': [3, 6, 9],
            'min_child_weight': [1, 2, 3],
            'subsample': [0.7, 0.8, 0.9],
        }
    },
    'gbdt': {
        'params': {
            'n_estimators': 100,
            'max_depth': 5,
            'learning_rate': 0.1,
            'random_state': 42
        },
        'param_grid': {
            'n_estimators': [50, 100, 200],
            'max_depth': [3, 5, 7],
            'learning_rate': [0.01, 0.05, 0.1]
        }
    },
    'random_forest': {
        'params': {
            'n_estimators': 100,
            'max_depth': 10,
            'random_state': 42
        },
        'param_grid': {
            'n_estimators': [10, 20, 40],
            'max_depth': [None, 10, 20],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
    },
    'svm': {
        'params': {
            'kernel': 'rbf',
            'C': 1.0,
            'epsilon': 0.1,
            'gamma': 'scale'
        },
        'param_grid': {
            'C': [0.1, 1, 10],
            'epsilon': [0.01, 0.1, 0.2],
            'gamma': ['scale', 'auto']
        }
    },
    'mlp': {
        'params': {
            'hidden_layer_sizes': (100, 50),
            'activation': 'relu',
            'solver': 'adam',
        }
    },
    'knn': {
        'params': {
            'n_neighbors': 5,
            'weights': 'uniform',
            'algorithm': 'auto'
        }
    },
    'decision_tree': {
        'params': {
            'max_depth': 5,
            'min_samples_split': 2,
            'min_samples_leaf': 1
        }
    },
    'extra_trees': {
        'params': {
            'n_estimators': 100,
            'max_depth': 5,
            'min_samples_split': 2,
            'min_samples_leaf': 1
        },
        'param_grid': { 
            'n_estimators': [50, 100, 200],
            'max_depth': [3, 5, 7],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
    },
    'cnn': {
        'params': {
            'input_size': None,  # 自动从数据推断
            'hidden_size': 64,   # 隐藏层大小
            'output_size': 1,    # 输出维度
            'learning_rate': 0.001,
            'batch_size': 32,
            'n_epochs': 100,
            'device': 'cuda'      # 使用CPU训练
        },
        'param_grid': {
            'hidden_size': [32, 64, 128],
            'learning_rate': [0.0001, 0.001, 0.01],
            'batch_size': [16, 32, 64]
        }
    },
    'lstm': {
        'params': {
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2
        },
        'param_grid': {
            'hidden_size': [32, 64, 128],
            'num_layers': [1, 2, 3],
            'dropout': [0.1, 0.2, 0.3]
        }
    },
    'deeplob': {
        'params': {
            'input_shape': (1, 32, 1),  # (channels, height, width)
            'num_classes': 1,
            'hidden_size': 32,
            'num_layers': 2,
            'dropout': 0.2,
            'learning_rate': 0.001
        },
        'param_grid': {
            'hidden_size': [16, 32, 64],
            'num_layers': [1, 2, 3],
            'dropout': [0.1, 0.2, 0.3],
            'learning_rate': [0.0001, 0.001, 0.01]
        }
    },
    'autogluon': {
        'params': {
            'time_limit': 10,  # 2分钟训练时间
            'presets': 'medium_quality',  # 中等质量预设
            'problem_type': 'regression',  # 问题类型
            'eval_metric': 'r2',  # 评估指标 ['r2', 'mean_squared_error', 'mse', 'root_mean_squared_error', 'rmse', 'mean_absolute_error', 'mae', 'median_absolute_error', 'mean_absolute_percentage_error', 'mape', 'symmetric_mean_absolute_percentage_error', 'smape', 'spearmanr', 'pearsonr']
            'label': 'target',  # 目标列名
            'path': OUTDIR+ '/models/autogluon/',  # 模型保存路径
            'verbosity': 2,  # 日志级别
        }
    }
}

# 遗传算法配置参数
genetic_factor_config = {
    'n_factors': 10,
    'population_size': 50,
    'generations': 50,
    'elite_size': 5,
    'use_genetic_factors': False
}

BASE_COL = [
    'Volume',
    'BidPrice5', 'BidPrice4', 'BidPrice3', 'BidPrice2', 'BidPrice1',
    'AskPrice1', 'AskPrice2', 'AskPrice3', 'AskPrice4', 'AskPrice5',
    'BidVol5', 'BidVol4', 'BidVol3', 'BidVol2', 'BidVol1',
    'AskVol1', 'AskVol2', 'AskVol3', 'AskVol4', 'AskVol5',
    'LastPrice', 'High', 'Low', 'TotalValueTraded', 
]