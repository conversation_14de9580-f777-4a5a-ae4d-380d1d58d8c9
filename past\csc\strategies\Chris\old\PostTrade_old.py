# -*- coding: utf-8 -*-
"""
Created on Wed Feb 23 20:28:23 2022

@author: dell
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def calculate_market_impact(trades,mkt_maker,slices,ts):#calculate market impact
    trades = trades.copy(deep=False)
    mkt_maker = mkt_maker.copy(deep=False)
    # mkt_maker['mid'] = 0.5*(mkt_maker['bid_price_1']+mkt_maker['ask_price_1'])
    times = trades['datetime'].tolist() #成交时间
    df_market_impacts = []
    df_realized_spreads = []
    for time in times:
        trade_price = trades['price'][trades['datetime']==time].sum()
        direction = trades['direction'][trades['datetime']==time].sum()
        # trade_price = trades['price'][trades['datetime']==time].mean()
        # direction = trades['direction'][trades['datetime']==time].mean()
        i = mkt_maker.index[mkt_maker['datetime']==time][0]
        if i+slices+1<mkt_maker.shape[0]:
            df_slice = mkt_maker['mid'][i-1:i+slices].reset_index()
            slice_market_impact = (df_slice['mid'] - df_slice['mid'][0]) * direction / float(ts)
            slice_realized_spread = (df_slice['mid'] - trade_price) * direction / float(ts)
            df_market_impacts.append(slice_market_impact)
            df_realized_spreads.append(slice_realized_spread)
            # print (slice_realized_spread)
    df_market_impacts = pd.DataFrame(df_market_impacts)
    df_realized_spreads = pd.DataFrame(df_realized_spreads)
    
    
    
    
    return[df_market_impacts,df_realized_spreads]

def plot_Realized_Spread(df_market_impacts,df_realized_spreads):
    # ax = plt.subplot(211)
    plt.plot(df_market_impacts.mean())
    plt.plot(df_realized_spreads.mean())
    plt.plot(df_realized_spreads.std())
    plt.legend(['avg market impact','avg realized spread','std'])
    plt.title('Impact&RealizedSpread')
    plt.show()

