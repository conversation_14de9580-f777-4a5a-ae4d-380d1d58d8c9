# -*- coding:utf-8 -*-
from __future__ import print_function
from datetime import datetime
from WindPy import w

# import os
import time
import sys
import logging

# reload(sys)
# sys.setdefaultencoding('utf-8')

i = 0
sqlpath = u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\SQL\\windtosql\\update'
wordpath = u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\optionweek\\update.py'
logpath = u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\daily'
vecmpath=u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\research'
# encode('gbk', 'ignore').decode('gbk') #.encode('gbk')

import subprocess

# import traceback
# import tempfile


def subexe(cmd):
    # this method is used for monitoring

    # import time
    import subprocess
    # import locale
    # import codecs

    mylist = []
    ps = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    while True:
        out = ps.stderr.read(1)
        if out == '' and ps.poll() != None:
            break
        if out != '':
            # sys.stdout.write(out)
            # sys.stdout.flush()
            data = ps.stdout.readline()
            mylist.append(data)
            print(u"%s" % data, end='')
        # if data == b'':
        #     if ps.poll() is not None:
        #         break
        # else:
        #     mylist.append(data)
        #     newlist = []
        #     for i in mylist:
        #         if i.find('192.168') > 0:
        #             newlist.append(i)
        #     newlist.sort()
        #     print('Sum of requests from LAN:', len(newlist))

    return mylist


def exeCmd(path2):
    # r = os.popen(path2)
    # text = r.read()
    # r.close()
    try:
        obj = subprocess.Popen(path2, shell=True, stdin=subprocess.PIPE,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        text = obj.stdout.read()
        try:
            text.decode('GBK').encode('GBK')
            writeFile('GBK\n')
            text = text.decode('GBK')
        except:
            writeFile('UTF-8\n')
            text = text.decode('UTF-8')
        obj.stdin.close()
        obj.stdout.close()
    except Exception as e:
        text = e
        print(e)
    return text


def whichEncode(text):
    '''
    获取编码格式
    '''
    if isinstance(text, str):
        return "unicode"
    try:
        text.decode("utf8")
        return 'utf8'
    except:
        pass
    try:
        text.decode("gbk")
        return 'gbk'
    except:
        pass


def writeFile(data):
    print(u"%s" % data, end='')
    logging.info(data)
    print('logging successful')


def auto_exit(timer, tt):
    for _ in range(0, timer, tt):
        print("\r", end="")
        print(u"\r程序将在 %d秒 内自动关闭" % timer, end="")
        time.sleep(tt)
        timer -= tt


def linetime(timer, tt):
    lineLength = timer
    delaySeconds = tt
    frontSymbol = '='
    frontSymbol2 = ['—', '\\', '|', '/']
    backSymbol = ' '

    lineTmpla = u"{:%s<%s} {} {:<10}" % (backSymbol, lineLength)
    print(u"本次更新将在 %d秒 内自动退出" % timer, end="")
    for _ in range(0, timer, delaySeconds):
        tmpSymbol = frontSymbol2[timer % (len(frontSymbol2))]
        sys.stdout.write("\r")
        # print(lineTmpla.format(frontSymbol * timer, tmpSymbol, str(timer) + u"秒后自动关闭"), end='')
        sys.stdout.write(lineTmpla.format(frontSymbol * timer, tmpSymbol, str(timer) + "秒后自动关闭"))
        sys.stdout.flush()
        time.sleep(delaySeconds)
        timer -= delaySeconds


logging.basicConfig(handlers=[logging.FileHandler('%s\\myapp.log' % logpath, 'a', 'utf-8')],
                    level=logging.DEBUG,
                    format='%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s',
                    datefmt='%Y-%m-%d %a %H:%M:%S'
                    )

now = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
writeFile(now)
w.start()
if w.isconnected():
    t = '\nwind opened successfully'
else:
    t = '\nwind api false'
writeFile(t)

# i = i + 1
# writeFile(u"\n\n             %s\n*\n*\n*开始更新399001.SZ,399006.SZ,000001.SH,000016.SH指数分钟数据\n*\n*" % i)
# text = exeCmd('%s\\Index2SQLUpdate.py' % sqlpath)
# writeFile(text)
# i = i + 1
# writeFile(u"\n\n             %s\n*\n*\n*开始更新豆粕期货主力合约数据\n*\n*" % i)
# text = exeCmd(u"%s\\MfuturesSQLup.py" % sqlpath)
# writeFile(text)
# i = i + 1
# writeFile(u"\n\n             %s\n*\n*\n*开始更新白糖期货主力合约数据\n*\n*" % i)
# text = exeCmd(u"%s\\SRfuturesSQLup.py" % sqlpath)
# writeFile(text)
# i = i + 1
# writeFile(u"\n\n             %s\n*\n*\n*开始更新豆粕期权日数据\n*\n*" % i)
# text = exeCmd(u"%s\\opt_M_UP.py" % sqlpath)
# writeFile(text)
# i = i + 1
# writeFile(u"\n\n             %s\n*\n*\n*开始更新白糖期权日数据\n*\n*" % i)
# text = exeCmd(u"%s\\opt_sr_UP.py" % sqlpath)
# writeFile(text)
#
# week = datetime.now().weekday() + 1
# i = i + 1
# if week == 5:  # and ((datetime.now().hour >= 15 and datetime.now().minute >= 30) or datetime.now().hour >= 16):
#     tvalue = 1
#     writeFile(u"\n\n             %s\n*\n*\n*\n*\n*现在是北京时间 星期%s %s点 %s分 \n开始更新300指数分钟数据\n*\n*"
#               % (i, week, datetime.now().hour, datetime.now().minute))
#     text = exeCmd("%s\\300Min2sqlUp.py" % sqlpath)
#     # os.system(u"%s\\300Min2sqlUp.py" % sqlpath)
#     writeFile(text)
#     # writeFile(text)
# else:
#     tvalue = 0
#     writeFile(u"\n\n             %s\n*\n*\n*今日不需要更新300指数分钟数据\n*\n*" % i)

# i = i + 1
# if datetime.now().month % 6 == 0 and datetime.now().day >= 26:
#     writeFile(u"\n\n             %s\n*\n*\n*\n*\n*\n开始更新指数成分股数据\n*\n*" % i)
#     text = exeCmd("%s\\IndexConstituent.py" % sqlpath)
#     # os.system(u"%s\\300Min2sqlUp.py" % sqlpath)
#     writeFile(text)
#     # writeFile(text)
# else:
#     writeFile(u"\n\n             %s\n*\n*\n*今日不需要更新指数成分股数据\n*\n*" % i)

i = i + 1
writeFile(u"\n\n             %s\n*\n*\n*开始更新vecm5数据\n*\n*" % i)
text = exeCmd('%s\\optionvecm5.py' % vecmpath)
writeFile(text)

i = i + 1
writeFile(u"\n\n             %s\n*\n*\n*开始更新vecm1数据\n*\n*" % i)
text = exeCmd('%s\\optionvecm1.py' % vecmpath)
writeFile(text)

# i = i + 1
# writeFile(u"\n\n             %s\n*\n*\n*开始更新vecmAver数据\n*\n*" % i)
# text = exeCmd('%s\\optionvecmAver.py' % vecmpath)
# writeFile(text)

command = 'taskkill /F /IM EXCEL.exe'

exeCmd(command)

tvalue = 1
i = i + 1
if tvalue == 1:
    writeFile(u"\n\n             %s\n*\n*\n*\n*\n*\n开始更新50ETF期权周报数据\n*\n*" % i)
    # text=exeCmd("%s\\300Min2sqlUp.py" % newpath)
    text = exeCmd(wordpath)
    # import os
    # os.system(wordpath)
    writeFile(text)
    # writeFile(text)
else:
    writeFile(u"\n\n             %s\n*\n*\n*今日不需要更新50ETF期权周报数据\n*\n*" % i)

# import msvcrtLINILININGZHONGTAIZHENGQU
# print(ord(msvcrt.getch()))

now = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
writeFile(now)
writeFile(u"*********更新结束\n\n\n\n\n")

exeCmd(command)

auto_exit(150, 1)
