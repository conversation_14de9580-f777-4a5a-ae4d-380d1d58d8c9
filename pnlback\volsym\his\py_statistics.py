
# Author : gp
# Date : 20240319

import requests
import websocket
import json
import csv

class md_config:
    # ws_addr = 'ws://160.14.228.64:5557'  # 开发环境地址
    # http_addr = 'http://160.14.228.64:5555'
    ws_addr = 'ws://168.231.2.88:22929'  # 生产环境地址
    http_addr = 'http://168.231.2.88:22928'
    user_name = 'ln'
    password = 'ln'
    msg_name = ['volGreeks', 'option_rs', ][0]
    filter_key = ['underlyer', ][0]
    filter_value_list = ['510500', ][0]
    token = ''
    disable_snap = 0  # 0是包括历史，1是实时的


class csv_manager :
    csv_month = open('./option_statistics_month.csv', 'w+', newline='')
    csv_date = open('./option_statistics_tradingday.csv', 'w+', newline='')
    csv_month_writer = csv.writer(csv_month)
    csv_date_writer = csv.writer(csv_date)

    def on_data(self, data) :
        if data["trading_day"] == "" :
            pass
            # self.csv_month_writer.writerow([data["month"], data["product_id"], data["item"], data["value"]])
        else :
            pass
            # self.csv_date_writer.writerow([data["month"], data["trading_day"], data["product_id"], data["item"], data["value"]])

    def on_close(self) :
        print("close")


def on_open(ws):
    # 发送认证请求
    init_str = json.loads('{"type":"token_sub","data":{"token":0,"disable_sub_all":1}}')
    init_str["data"]["token"] = md_config.token
    print("send init req : ", init_str)
    ws.send(json.dumps(init_str))
    # 发送订阅请求
    subscribe_json = json.loads('{"seqno":1,"type":"table_action","data":{"msg_name":"","action":"sub","interval_ms":0,"disable_snap":0,"filter_key":"","filter_value_list":""}}')
    subscribe_json["seqno"] = 1
    subscribe_json["data"]["msg_name"] = md_config.msg_name
    subscribe_json["data"]["filter_key"] = md_config.filter_key
    subscribe_json["data"]["filter_value_list"] = md_config.filter_value_list
    print("send subscribe req : ", subscribe_json)
    ws.send(json.dumps(subscribe_json))

    csv_manager.csv_month_writer.writerow(["month","product_id","item","value"])
    csv_manager.csv_date_writer.writerow(["month","trading_day","product_id","item","value"])

def on_message(ws, message):
    print("Received message:", message)
    json_data = json.loads(message)
    if(json_data['type'] == md_config.msg_name) :
        md_json = json_data['data']
        # print("receive data ", md_json)
        csv_manager.on_data(csv_manager, md_json)
    elif(json_data['type'] == "fi_msg_rsp") :
        if(json_data['seqno'] == 1) :
            csv_manager.on_close(csv_manager)
            exit()

def on_error(ws, error):
    print("Error:", error)

def on_close(ws, a, b):
    print("Connection closed")

def main():
    # 发送http登陆请求
    url = md_config.http_addr + "/auth/login"
    login_req_json = json.loads('{"type":"login_req","data":{"user_name":"","password":""}}')
    login_req_json["data"]["user_name"] = md_config.user_name
    login_req_json["data"]["password"] = md_config.password
    r = requests.post(url, json=login_req_json)
    for cookie in r.cookies.keys():
        if cookie == "token" :
            md_config.token = r.cookies.get(cookie)
    if(md_config.token == '') :
        print("ERROR get token ", r.text, r.cookies)
        return
    print("token ", md_config.token, r.text, r.cookies)
    # websocket
    ws = websocket.WebSocketApp(md_config.ws_addr, on_message=on_message, on_error=on_error, on_close=on_close)
    ws.on_open = on_open
    ws.run_forever()

if __name__ == "__main__":
    main()