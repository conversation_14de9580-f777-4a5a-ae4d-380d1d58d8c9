# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-11-29

import pandas as pd
import numpy as np
import pyodbc
from datetime import datetime
import math
import xlwt

from WindPy import w

w.start()

now= datetime.now()

def findbig(code):

    stockdata = w.wsd(code, "pre_close,open,high,low,close,volume", "2016-02-02", now, "PriceAdj=F")
    stockdata = pd.DataFrame(stockdata.Data, index=stockdata.Fields, columns=stockdata.Times)
    stockdata = stockdata.T  # 将矩阵转置
    # print(stockdata)
    num = 0
    boo = 0

    for i in range(1, len(fm.index)):
        index0 = fm.index[i]
        # print(index0)
        isa=fm['LOW'][i] <= fm['PRE_CLOSE'][i]*0.997
        if fm['CLOSE'][i] < fm['PRE_CLOSE'][i] or isa:
            boo = 1
            num += 1
        elif boo == 1 and (fm['LOW'][i - 1] < fm['PRE_CLOSE'][i - 1] * 0.99 or num >= 2):
            fm2 = fm[i - num:i]
            index1 = fm.index[i - 1]
            stockdata2 = stockdata[fm.index[i - num]:fm.index[i - 1]]

            stockdata2['chg'] = 0


            fm2des = fm2.describe()
            chg1=round((float(fm2des.loc['min', 'LOW'])-float(fm2['PRE_CLOSE'][0])) / float(
                fm2['PRE_CLOSE'][0]), 4) #最大下影线
            # print(stockdes)
            # print(fm2des)
            num2 = 0
            for ii in stockdata2.index:
                if stockdata2.loc[ii, 'VOLUME'] == 0:
                    continue
                stockdata2.loc[ii, 'chg'] = mm = float(float(stockdata2.loc[ii, 'HIGH'])-float(stockdata2.loc[ii, 'PRE_CLOSE'])) / float(stockdata2.loc[ii, 'OPEN'])
                if stockdata2.loc[ii, 'CLOSE'] >= stockdata2.loc[ii, 'PRE_CLOSE'] \
                        or mm >= 0.015:
                    num2 += 1
            stockdes = stockdata2.describe()
            aaaa = float(num2) / float(num)#逆市天数比
            chg2 = float(stockdes.loc['max', 'chg'])  # 上影线最大值
            if num2 == 0 or (aaaa < 0.5 and chg2 < 0.03):
                # list1.append(str(index1))
                # list1.append(-1)
                # list1.append(-1)
                # list1.append(-1)
                # list1.append(-1)
                # list1.append(-1)
                # list1.append(-1)
                boo = 0
                num = 0
                continue
            word = u'逆市上涨' + str(num2) + u'天'
            print(word)
            list1 = []
            list1.append(str(index1))
            list1.append(code)
            list1.append(round(fm2['PRE_CLOSE'][0], 2))
            list1.append(round(fm2des.loc['min', 'HIGH'], 2))
            list1.append(round(fm2des.loc['min', 'LOW'], 2))
            list1.append(round(fm2['CLOSE'][-1], 2))
            list1.append(chg1)
            list1.append(num)
            list1.append(chg2)
            s=stockdes.loc['std', 'CLOSE']
            try :
                if s>-10000:
                    list1.append(round(s,4))
                else:
                    list1.append(0)
            except:
                list1.append(0)
            # list1.append(stockdes.values)
            list1.append(word)
            list1.append(num2)
            list2.append(list1)
            boo = 0
            num = 0
        else:
            boo = 0
            num = 0



from collections import Iterable


def savexl(code, list1):
    sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet

    if isinstance(list1[1], Iterable):
        pass
        for i, row in enumerate(list1):
            for j, col in enumerate(row):
                sheet1.write(i, j, col)
    else:
        for i, row in enumerate(list1):
            sheet1.write(0, i, row)

    f.save('pro.xls')  # 保存文件


f = xlwt.Workbook()  # 创建工作簿

wsd_data = w.wsd("000001.SH", "pre_close,open,high,low,close,volume", "2016-02-02", now, "PriceAdj=F")
fm = pd.DataFrame(wsd_data.Data, index=wsd_data.Fields, columns=wsd_data.Times)
fm = fm.T  # 将矩阵转置
# print(fm)

codeset = w.wset("sectorconstituent", "date=2017-11-28;sectorid=1000000087000000")

list2 = []
countn = 0
for code in codeset.Data[1]:
    countn += 1
    print(countn)
    list1 = []
    findbig(code)
    print(list1)
savexl(code, list2)
