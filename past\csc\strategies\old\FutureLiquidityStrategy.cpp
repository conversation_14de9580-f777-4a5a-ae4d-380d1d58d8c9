#include <configuru.hpp>

#include "FutureLiquidityStrategy.h"
#include "OrderService.h"
#include "QuoteService.h"
#include "RiskService.h"
#include "InstrumentManager.h"
#include "TradingTimeManager.h"
#include <cstdlib>
#include <time.h>

using namespace std; 
using namespace chrono;
using namespace configuru;

FutureLiquidityStrategy::FutureLiquidityStrategy(int id_, PriceSuggestor priceSuggester_, int portfolioId_,
	StrategyTypeId strategyTypeId_, bool isAlive_, StrategyStatus strategyStatus_, string const& parameters_,
	set<int> const& appliedInstruments_, string const& comments_, int level_, bool isQuotingStrategy_,
	bool useQuoteService_, bool isQuoteDeleteRequired_, bool beGrouped_, int groupId_) :
	Strategy(id_, priceSuggester_, portfolioId_, strategyTypeId_, isAlive_, strategyStatus_, parameters_, appliedInstruments_, comments_, level_,
		isQuotingStrategy_, useQuoteService_, isQuoteDeleteRequired_, beGrouped_, groupId_)
{
	setParameters(parameters_);
}

bool FutureLiquidityStrategy::initStrategyJobs()
{
	if (_appliedInstruments.size() != 1)
	{
		LOG4CPLUS_ERROR(_logger, "应用合约多于一个！");
		return false;
	}

	OrderService::getInstance().registerOrderListener(this);
	OrderService::getInstance().registerTradeListener(this);
	QuoteService::getInstance().registerQuoteListener(this);
	QuoteService::getInstance().registerTradeListener(this);


	auto instruments = InstrumentManager::getInstance().getInstruments();
	for (auto index : _appliedInstruments)
	{
		auto pInstrument = &instruments[index];
		//if (pInstrument->instrumentType != InstrumentType::Futures)
		//{
		//	LOG4CPLUS_ERROR(_logger, "应用合约不是期货类型！");
		//	return false;
		//}
		MarketDataService::getInstance().subscribeMarketData(this, pInstrument->instrumentId);
		_pInstrument = pInstrument;
		if (pInstrument->pBaseInstrument)
		{
			_pBaseInstrument = pInstrument->pBaseInstrument;
			MarketDataService::getInstance().subscribeMarketData(this, _pBaseInstrument->instrumentId);
		}
		else
		{
			_pBaseInstrument = nullptr;
		}

		_tick = round(_pInstrument->minimumTick * _pInstrument->minTickMultiplier);
		_mu = _pInstrument->instrumentMultiplier / _pInstrument->minTickMultiplier;

		_pPreQuote = nullptr;
		_pQuote = nullptr;
		_pHedgeOrder = nullptr;
		_pBidOrder = nullptr;
		_pAskOrder = nullptr;

		_mBid.reset();

		_isQuoting = true;
		_pauseCount = -1;
		_stdEdge = _tick;

		_riskMapCache.clear();
		RiskService::getInstance().registerRiskListener(this);
		processRisks();
		RiskService::getInstance().unregisterRiskListener(this);
		for (auto& pair : _riskMapCache)
		{
			LOG4CPLUS_DEBUG(_logger, ::toString(pair.second));
		}
		auto& risk = _riskMapCache[_pInstrument->instrumentId];
		_net = risk.longPosition - risk.shortPosition;

        _isAvgInitialized = _net != 0 ? false : true;
		_avg = 0.;
		_pnl = 0.;
		_fair = 0.;
		_cumPnl = 0.;
		_realPnl = 0;
		_newPnl = 0.;

		lastMaker = FutureLiquidity::LastPoint();
		lastRefer = FutureLiquidity::LastPoint();

		_bidP = 0;
		_askP = 0;
		_stop = 0.;

		_obligationArray.fill(0);
		_obligationTicks = 0;
		_obligationStartTime = steady_clock::now();

		_isAutoCloseMode = false;

		_tradedInTick = false;
		_betweenTickFlag = false;

		LOG4CPLUS_INFO(_logger, "init"
			<< ",maker:" << _pInstrument->instrumentId
			<< ",refer:" << (_pBaseInstrument ? _pBaseInstrument->instrumentId : "")
			<< ",tick:" << _tick
			<< ",mu:" << _mu);
	}

	return true;
}

bool FutureLiquidityStrategy::cleanUpStrategyJobs()
{
	LOG4CPLUS_DEBUG(_logger, "Start Deleting Orders...");
	while (true)
	{
		processOrders();
		processQuotes();
		if (doCancelAll())
		{
			break;
		}
	}
	LOG4CPLUS_DEBUG(_logger, "Finish Deleting Orders...");

	_orderFeedsByInternalOrderId.clear();
	LOG4CPLUS_DEBUG(_logger, "Finish Clearing OrderMap...");
	_quoteFeedsByInternalQuoteId.clear();
	LOG4CPLUS_DEBUG(_logger, "Finish Clearing QuoteMap...");
	OrderService::getInstance().unregisterOrderListener(this);
	OrderService::getInstance().unregisterTradeListener(this);
	LOG4CPLUS_DEBUG(_logger, "Finish Unsub OrderFeeds...");
	LOG4CPLUS_DEBUG(_logger, "Finish Unsub QuoteFeeds...");
	MarketDataService::getInstance().unsubscribeMarketData(this);
	LOG4CPLUS_DEBUG(_logger, "Finish Cleaning-up...");
	return true;
}

void FutureLiquidityStrategy::setParameters(string const& paramStr_)
{
	stringstream ss;
	ss.str(paramStr_);
	string value;
	getline(ss, value, _delimeter);
	if (!ss) return;
	_edge = stoi(value);//0 
	getline(ss, value, _delimeter);
	if (!ss) return;
	_maxPos = stoi(value);//1
	getline(ss, value, _delimeter);
	if (!ss) return;
	_hold = stoi(value);//2
}

string FutureLiquidityStrategy::getParameters() const
{
	stringstream ss;
	ss << _edge//0
		<< _delimeter << _maxPos //1
		<< _delimeter << _hold; //2
	return ss.str();
}

FutureLiquidity::PriceVolume FutureLiquidityStrategy::getBid5(FutureLiquidity::TransferedMarketData quote, VolumeType bid_volume, double away_price)
{
	FutureLiquidity::PriceVolume ret;

	// 1b
	ret.price = quote.bid_volume1 > 0 ? quote.bid_price1 : NAN;
	ret.volume = quote.bid_volume1;
	bid_volume -= quote.bid_volume1;
	// 2b
	if (bid_volume > 0 && quote.bid_price2 > 0)
	{
		ret.price = quote.bid_volume2 > 0 ? quote.bid_price2 : ret.price;
		ret.volume += quote.bid_volume2;
		bid_volume -= quote.bid_volume2;
	}
	// 3b
	if (bid_volume > 0 && quote.bid_price3 > 0)
	{
		ret.price = quote.bid_volume3 > 0 ? quote.bid_price3 : ret.price;
		ret.volume += quote.bid_volume3;
		bid_volume -= quote.bid_volume3;
	}
	// 4b
	if (bid_volume > 0 && quote.bid_price4 > 0)
	{
		ret.price = quote.bid_volume4 > 0 ? quote.bid_price4 : ret.price;
		ret.volume += quote.bid_volume4;
		bid_volume -= quote.bid_volume4;
	}
	// 5b
	if (bid_volume > 0 && quote.bid_price5 > 0)
	{
		ret.price = quote.bid_volume5 > 0 ? quote.bid_price5 : ret.price;
		ret.volume += quote.bid_volume5;
		bid_volume -= quote.bid_volume5;
	}
	// 5+ b
	if (bid_volume > 0)
	{
		ret.price -= away_price;
	}
	return ret;
}

FutureLiquidity::PriceVolume FutureLiquidityStrategy::getAsk5(FutureLiquidity::TransferedMarketData quote, VolumeType ask_volume, double away_price)
{
	FutureLiquidity::PriceVolume ret;

	// 1b
	ret.price = quote.ask_volume1 > 0 ? quote.ask_price1 : NAN;
	ret.volume = quote.ask_volume1;
	ask_volume -= quote.ask_volume1;
	// 2b
	if (ask_volume > 0 && quote.ask_price2 > 0)
	{
		ret.price = quote.ask_volume2 > 0 ? quote.ask_price2 : ret.price;
		ret.volume += quote.ask_volume2;
		ask_volume -= quote.ask_volume2;
	}
	// 3b
	if (ask_volume > 0 && quote.ask_price3 > 0)
	{
		ret.price = quote.ask_volume3 > 0 ? quote.ask_price3 : ret.price;
		ret.volume += quote.ask_volume3;
		ask_volume -= quote.ask_volume3;
	}
	// 4b
	if (ask_volume > 0 && quote.ask_price4 > 0)
	{
		ret.price = quote.ask_volume4 > 0 ? quote.ask_price4 : ret.price;
		ret.volume += quote.ask_volume4;
		ask_volume -= quote.ask_volume4;
	}
	// 5b
	if (ask_volume > 0 && quote.ask_price5 > 0)
	{
		ret.price = quote.ask_volume5 > 0 ? quote.ask_price5 : ret.price;
		ret.volume += quote.ask_volume5;
		ask_volume -= quote.ask_volume5;
	}
	// 5+ b
	if (ask_volume > 0)
	{
		ret.price += away_price;
	}
	return ret;
}

double FutureLiquidityStrategy::getFairPrice(double lastP, double askP, double bidP, double edgeP)
{
	double fair = lastP;
	if (askP > 0 && bidP > 0)
	{
		if (askP - bidP <= edgeP)
		{
			fair = 0.5 * (askP + bidP);
		}
		else
		{
			if (lastP > askP)
			{
				fair = askP;
			}
			if (lastP < bidP)
			{
				fair = bidP;
			}
		}
	}
	return fair;
}

int FutureLiquidityStrategy::threeMedian(int a, int b, int c) {
	int temp;
	if (a < b) { 
		temp = a; a = b; b = temp; 
	}
	if (a < c) {
		temp = a; a = c; c = temp;
	}
	if (b < c) {
		temp = b; b = c; c = temp;
	}
	return b;
}

inline bool Equal(double a, double b){
	if ((a - b > -0.0000001) && (a - b < 0.0000001))
		return true;
	else
		return false;
}

void FutureLiquidityStrategy::adjustOrderBook()
{
	double bid_best, ask_best;
	double adjustedFairPrice = _tick * floor(_fair / _tick);
	if (_fair - adjustedFairPrice < 0.0000001) {
		bid_best = _fair - _tick;
		ask_best = _fair + _tick;
	} else {
		bid_best = adjustedFairPrice; //取整数部分
		ask_best = adjustedFairPrice + _tick;
	}
	//FutureLiquidity::TransferedMarketData orderBook;
	orderBook.bid_price1 = bid_best - 0 * _tick;
	orderBook.bid_price2 = bid_best - 1 * _tick;
	orderBook.bid_price3 = bid_best - 2 * _tick;
	orderBook.bid_price4 = bid_best - 3 * _tick;
	orderBook.bid_price5 = bid_best - 4 * _tick;

	orderBook.bid_volume1 = 1;
	orderBook.bid_volume2 = 1;
	orderBook.bid_volume3 = 1;
	orderBook.bid_volume4 = 1;
	orderBook.bid_volume5 = 1;

	orderBook.ask_price1 = ask_best + 0 * _tick;
	orderBook.ask_price2 = ask_best + 1 * _tick;
	orderBook.ask_price3 = ask_best + 2 * _tick;
	orderBook.ask_price4 = ask_best + 3 * _tick;
	orderBook.ask_price5 = ask_best + 4 * _tick;

	orderBook.ask_volume1 = 1;
	orderBook.ask_volume2 = 1;
	orderBook.ask_volume3 = 1;
	orderBook.ask_volume4 = 1;
	orderBook.ask_volume5 = 1;

	if (Equal(maker.bid_price1, orderBook.bid_price1)) orderBook.bid_volume1 = adjustedVolume(maker.bid_volume1);
	if (Equal(maker.bid_price2, orderBook.bid_price1)) orderBook.bid_volume1 = adjustedVolume(maker.bid_volume2);
	if (Equal(maker.bid_price3, orderBook.bid_price1)) orderBook.bid_volume1 = adjustedVolume(maker.bid_volume3);
	if (Equal(maker.bid_price4, orderBook.bid_price1)) orderBook.bid_volume1 = adjustedVolume(maker.bid_volume4);
	if (Equal(maker.bid_price5, orderBook.bid_price1)) orderBook.bid_volume1 = adjustedVolume(maker.bid_volume5);

	if (Equal(maker.bid_price1, orderBook.bid_price2)) orderBook.bid_volume2 = adjustedVolume(maker.bid_volume1);
	if (Equal(maker.bid_price2, orderBook.bid_price2)) orderBook.bid_volume2 = adjustedVolume(maker.bid_volume2);
	if (Equal(maker.bid_price3, orderBook.bid_price2)) orderBook.bid_volume2 = adjustedVolume(maker.bid_volume3);
	if (Equal(maker.bid_price4, orderBook.bid_price2)) orderBook.bid_volume2 = adjustedVolume(maker.bid_volume4);
	if (Equal(maker.bid_price5, orderBook.bid_price2)) orderBook.bid_volume2 = adjustedVolume(maker.bid_volume5);

	if (Equal(maker.bid_price1, orderBook.bid_price3)) orderBook.bid_volume3 = adjustedVolume(maker.bid_volume1);
	if (Equal(maker.bid_price2, orderBook.bid_price3)) orderBook.bid_volume3 = adjustedVolume(maker.bid_volume2);
	if (Equal(maker.bid_price3, orderBook.bid_price3)) orderBook.bid_volume3 = adjustedVolume(maker.bid_volume3);
	if (Equal(maker.bid_price4, orderBook.bid_price3)) orderBook.bid_volume3 = adjustedVolume(maker.bid_volume4);
	if (Equal(maker.bid_price5, orderBook.bid_price3)) orderBook.bid_volume3 = adjustedVolume(maker.bid_volume5);

	if (Equal(maker.bid_price1, orderBook.bid_price4)) orderBook.bid_volume4 = adjustedVolume(maker.bid_volume1);
	if (Equal(maker.bid_price2, orderBook.bid_price4)) orderBook.bid_volume4 = adjustedVolume(maker.bid_volume2);
	if (Equal(maker.bid_price3, orderBook.bid_price4)) orderBook.bid_volume4 = adjustedVolume(maker.bid_volume3);
	if (Equal(maker.bid_price4, orderBook.bid_price4)) orderBook.bid_volume4 = adjustedVolume(maker.bid_volume4);
	if (Equal(maker.bid_price5, orderBook.bid_price4)) orderBook.bid_volume4 = adjustedVolume(maker.bid_volume5);

	if (Equal(maker.bid_price1, orderBook.bid_price5)) orderBook.bid_volume5 = adjustedVolume(maker.bid_volume1);
	if (Equal(maker.bid_price2, orderBook.bid_price5)) orderBook.bid_volume5 = adjustedVolume(maker.bid_volume2);
	if (Equal(maker.bid_price3, orderBook.bid_price5)) orderBook.bid_volume5 = adjustedVolume(maker.bid_volume3);
	if (Equal(maker.bid_price4, orderBook.bid_price5)) orderBook.bid_volume5 = adjustedVolume(maker.bid_volume4);
	if (Equal(maker.bid_price5, orderBook.bid_price5)) orderBook.bid_volume5 = adjustedVolume(maker.bid_volume5);

	if (Equal(maker.ask_price1, orderBook.ask_price1)) orderBook.ask_volume1 = adjustedVolume(maker.ask_volume1);
	if (Equal(maker.ask_price2, orderBook.ask_price1)) orderBook.ask_volume1 = adjustedVolume(maker.ask_volume2);
	if (Equal(maker.ask_price3, orderBook.ask_price1)) orderBook.ask_volume1 = adjustedVolume(maker.ask_volume3);
	if (Equal(maker.ask_price4, orderBook.ask_price1)) orderBook.ask_volume1 = adjustedVolume(maker.ask_volume4);
	if (Equal(maker.ask_price5, orderBook.ask_price1)) orderBook.ask_volume1 = adjustedVolume(maker.ask_volume5);

	if (Equal(maker.ask_price1, orderBook.ask_price2)) orderBook.ask_volume2 = adjustedVolume(maker.ask_volume1);
	if (Equal(maker.ask_price2, orderBook.ask_price2)) orderBook.ask_volume2 = adjustedVolume(maker.ask_volume2);
	if (Equal(maker.ask_price3, orderBook.ask_price2)) orderBook.ask_volume2 = adjustedVolume(maker.ask_volume3);
	if (Equal(maker.ask_price4, orderBook.ask_price2)) orderBook.ask_volume2 = adjustedVolume(maker.ask_volume4);
	if (Equal(maker.ask_price5, orderBook.ask_price2)) orderBook.ask_volume2 = adjustedVolume(maker.ask_volume5);

	if (Equal(maker.ask_price1, orderBook.ask_price3)) orderBook.ask_volume3 = adjustedVolume(maker.ask_volume1);
	if (Equal(maker.ask_price2, orderBook.ask_price3)) orderBook.ask_volume3 = adjustedVolume(maker.ask_volume2);
	if (Equal(maker.ask_price3, orderBook.ask_price3)) orderBook.ask_volume3 = adjustedVolume(maker.ask_volume3);
	if (Equal(maker.ask_price4, orderBook.ask_price3)) orderBook.ask_volume3 = adjustedVolume(maker.ask_volume4);
	if (Equal(maker.ask_price5, orderBook.ask_price3)) orderBook.ask_volume3 = adjustedVolume(maker.ask_volume5);

	if (Equal(maker.ask_price1, orderBook.ask_price4)) orderBook.ask_volume4 = adjustedVolume(maker.ask_volume1);
	if (Equal(maker.ask_price2, orderBook.ask_price4)) orderBook.ask_volume4 = adjustedVolume(maker.ask_volume2);
	if (Equal(maker.ask_price3, orderBook.ask_price4)) orderBook.ask_volume4 = adjustedVolume(maker.ask_volume3);
	if (Equal(maker.ask_price4, orderBook.ask_price4)) orderBook.ask_volume4 = adjustedVolume(maker.ask_volume4);
	if (Equal(maker.ask_price5, orderBook.ask_price4)) orderBook.ask_volume4 = adjustedVolume(maker.ask_volume5);

	if (Equal(maker.ask_price1, orderBook.ask_price5)) orderBook.ask_volume5 = adjustedVolume(maker.ask_volume1);
	if (Equal(maker.ask_price2, orderBook.ask_price5)) orderBook.ask_volume5 = adjustedVolume(maker.ask_volume2);
	if (Equal(maker.ask_price3, orderBook.ask_price5)) orderBook.ask_volume5 = adjustedVolume(maker.ask_volume3);
	if (Equal(maker.ask_price4, orderBook.ask_price5)) orderBook.ask_volume5 = adjustedVolume(maker.ask_volume4);
	if (Equal(maker.ask_price5, orderBook.ask_price5)) orderBook.ask_volume5 = adjustedVolume(maker.ask_volume5);

	LOG4CPLUS_INFO(_logger, "refreshPrice:b1:" << maker.bid_volume1 << " " << orderBook.bid_price1 << " " << orderBook.bid_volume1 << " b2:" <<
		maker.bid_volume2 << " " << orderBook.bid_price2 << " " << orderBook.bid_volume2 << " b3:" <<
		maker.bid_volume3 << " " << orderBook.bid_price3 << " " << orderBook.bid_volume3 << " b4:" <<
		maker.bid_volume4 << " " << orderBook.bid_price4 << " " << orderBook.bid_volume4 << " b5:" <<
		maker.bid_volume5 << " " << orderBook.bid_price5 << " " << orderBook.bid_volume5 << " al:" <<
		maker.ask_volume1 << " " << orderBook.ask_price1 << " " << orderBook.ask_volume1 << " a2:" <<
		maker.ask_volume2 << " " << orderBook.ask_price2 << " " << orderBook.ask_volume2 << " a3:" <<
		maker.ask_volume3 << " " << orderBook.ask_price3 << " " << orderBook.ask_volume3 << " a4:" <<
		maker.ask_volume4 << " " << orderBook.ask_price4 << " " << orderBook.ask_volume4 << " a5:" <<
		maker.ask_volume5 << " " << orderBook.ask_price5 << " " << orderBook.ask_volume5);
	
}

int FutureLiquidityStrategy::adjustedVolume(int volume)
{
	int adjustedVolume;
	if (volume <= 10 * _lots) {
		adjustedVolume = 5 * _lots;
	}else {
		adjustedVolume = volume / 2;
	}
	return threeMedian(0.5 * _lots,volume,adjustedVolume);
}

FutureLiquidity::TransferedMarketData FutureLiquidityStrategy::transferMarketData(const MarketData& marketData_)
{
	FutureLiquidity::TransferedMarketData ret;
	ret.instrumentId = (string)(marketData_.instrumentId);
	ret.volume = marketData_.tradedVolume;
	ret.upper_limit = marketData_.upperLimit;
	ret.lower_limit = marketData_.lowerLimit;
	ret.last_price = marketData_.lastPriceOnMarket;
	ret.turnover = marketData_.turnover;
	ret.datetime = steady_clock::now();
	if (auto& bid = marketData_.bids[0]; bid && bid->volume > 0)
	{
		ret.bid_price1 = bid->price;
		ret.bid_volume1 = bid->volume;
	}
	else
	{
		ret.bid_price1 = _pInstrument->lowerLimit;
		ret.bid_volume1 = 0;
	}
	if (auto& bid = marketData_.bids[1]; bid && bid->volume > 0)
	{
		ret.bid_price2 = bid->price;
		ret.bid_volume2 = bid->volume;
	}
	else
	{
		ret.bid_price2 = _pInstrument->lowerLimit;
		ret.bid_volume2 = 0;
	}
	if (auto& bid = marketData_.bids[2]; bid && bid->volume > 0)
	{
		ret.bid_price3 = bid->price;
		ret.bid_volume3 = bid->volume;
	}
	else
	{
		ret.bid_price3 = _pInstrument->lowerLimit;
		ret.bid_volume3 = 0;
	}
	if (auto& bid = marketData_.bids[3]; bid && bid->volume > 0)
	{
		ret.bid_price4 = bid->price;
		ret.bid_volume4 = bid->volume;
	}
	else
	{
		ret.bid_price4 = _pInstrument->lowerLimit;
		ret.bid_volume4 = 0;
	}
	if (auto& bid = marketData_.bids[4]; bid && bid->volume > 0)
	{
		ret.bid_price5 = bid->price;
		ret.bid_volume5 = bid->volume;
	}
	else
	{
		ret.bid_price5 = _pInstrument->lowerLimit;
		ret.bid_volume5 = 0;
	}
	if (auto& ask = marketData_.asks[0]; ask && ask->volume > 0)
	{
		ret.ask_price1 = ask->price;
		ret.ask_volume1 = ask->volume;
	}
	else
	{
		ret.ask_price1 = _pInstrument->upperLimit;
		ret.ask_volume1 = 0;
	}
	if (auto& ask = marketData_.asks[1]; ask && ask->volume > 0)
	{
		ret.ask_price2 = ask->price;
		ret.ask_volume2 = ask->volume;
	}
	else
	{
		ret.ask_price2 = _pInstrument->upperLimit;
		ret.ask_volume2 = 0;
	}
	if (auto& ask = marketData_.asks[2]; ask && ask->volume > 0)
	{
		ret.ask_price3 = ask->price;
		ret.ask_volume3 = ask->volume;
	}
	else
	{
		ret.ask_price3 = _pInstrument->upperLimit;
		ret.ask_volume3 = 0;
	}
	if (auto& ask = marketData_.asks[3]; ask && ask->volume > 0)
	{
		ret.ask_price4 = ask->price;
		ret.ask_volume4 = ask->volume;
	}
	else
	{
		ret.ask_price4 = _pInstrument->upperLimit;
		ret.ask_volume4 = 0;
	}
	if (auto& ask = marketData_.asks[4]; ask && ask->volume > 0)
	{
		ret.ask_price5 = ask->price;
		ret.ask_volume5 = ask->volume;
	}
	else
	{
		ret.ask_price5 = _pInstrument->upperLimit;
		ret.ask_volume5 = 0;
	}

	return ret;
}

void FutureLiquidityStrategy::onMMTrade(FutureLiquidity::MMTrade trade_)
{
	auto volume = trade_.volume;
	auto price = trade_.price;
	switch (trade_.longShort)
	{
	case LongShort::LONG_:
		if (_net >= 0)
		{
			_avg = (_net * _avg + volume * price) / (_net + volume);
		}
		else if (volume + _net > 0)
		{
			_avg = price;
		}
		_net += volume;
		_realPnl -= volume * price;
		break;
	case LongShort::SHORT_:
		if (_net <= 0)
		{
			_avg = (-_net * _avg + volume * price) / (-_net + volume);
		}
		else if (volume - _net > 0)
		{
			_avg = price;
		}
		_net -= volume;
		_realPnl += volume * price;
		break;
	default:
		break;
	}
	_tradePrice = price;
	LOG4CPLUS_INFO(_logger, "trade:," << trade_.price << "," << trade_.volume << "," << LONG_SHORT_NAMES[trade_.longShort] << ",Net:" << _net << ",Avg:" << _avg << ",_realPnl:" << _realPnl);
}

bool FutureLiquidityStrategy::sendQuote(Quote& quote_)
{
	LOG4CPLUS_INFO(_logger, "sendQuote:" << quote_);
	return QuoteService::getInstance().pushSendQuote(quote_);
}

bool FutureLiquidityStrategy::deleteQuote(Quote& quote_)
{
	LOG4CPLUS_INFO(_logger, "deleteQuote:" << quote_);
	quote_.quoteStatus = PENDING_DELETE;
	return QuoteService::getInstance().pushDeleteQuote(quote_);
}

void FutureLiquidityStrategy::onMessage(MarketData const& marketData_)
{
	try
	{
		int retries = 0;
		while (retries++ < MAX_SEND_RETRIES && !_marketDataQueue.try_enqueue(marketData_));
		if (retries >= MAX_SEND_RETRIES)
		{
			LOG4CPLUS_ERROR(_logger, "将MarketData(" << ::toString(marketData_) << ")推入队列(" << _strategyId << ")时出现超过最大尝试次数.");
		}
	}
	catch (std::exception const& e)
	{
		LOG4CPLUS_ERROR(_logger, "将MarketData推入队列(" << _strategyId << ")时出现异常.异常信息:" << e.what());
	}
	catch (...)
	{
		LOG4CPLUS_ERROR(_logger, "将MarketData推入队列(" << _strategyId << ")时出现异常.异常信息:无");
	}
}

void FutureLiquidityStrategy::onOrder(Order const& order_)
{
	try
	{
		if (order_.strategyId != _strategyId) return;
		int retries = 0;
		while (retries++ < MAX_SEND_RETRIES && !_orderFeedQueue.try_enqueue(order_));
		if (retries >= MAX_SEND_RETRIES)
		{
			LOG4CPLUS_ERROR(_logger, "将Order(" << order_ << ")推入队列(" << _strategyId << ")时出现超过最大尝试次数.");
		}
	}
	catch (std::exception const& e)
	{
		LOG4CPLUS_ERROR(_logger, "将Order推入队列(" << _strategyId << ")时出现异常.异常信息:" << e.what());
	}
	catch (...)
	{
		LOG4CPLUS_ERROR(_logger, "将Order推入队列(" << _strategyId << ")时出现异常.异常信息:无");
	}
}

void FutureLiquidityStrategy::onQuote(const Quote& quote_)
{
	try
	{
		if (quote_.strategyId != _strategyId) return;
		int retries = 0;
		while (retries++ < MAX_SEND_RETRIES && !_quoteFeedQueue.try_enqueue(quote_));
		if (retries >= MAX_SEND_RETRIES)
		{
			LOG4CPLUS_ERROR(_logger, "将Quote(" << quote_ << ")推入队列(" << _strategyId << ")时出现超过最大尝试次数.");
		}
	}
	catch (std::exception const& e)
	{
		LOG4CPLUS_ERROR(_logger, "将Quote推入队列(" << _strategyId << ")时出现异常.异常信息:" << e.what());
	}
	catch (...)
	{
		LOG4CPLUS_ERROR(_logger, "将Quote推入队列(" << _strategyId << ")时出现异常.异常信息:无");
	}
}

void FutureLiquidityStrategy::onTrade(const Trade& trade_)
{
	try
	{
		//if (trade_.instrumentId != _pInstrument->instrumentId) return;
		//if (trade_.portfolioId != _portfolioId) return;
		if (strcmp(trade_.instrumentId, _pInstrument->instrumentId) != 0) return;
		int retries = 0;
		while (retries++ < MAX_SEND_RETRIES && !_tradeFeedQueue.try_enqueue(trade_));
		if (retries >= MAX_SEND_RETRIES)
		{
			LOG4CPLUS_ERROR(_logger, "将Trade(" << trade_ << ")推入队列(" << _strategyId << ")时出现超过最大尝试次数.");
		}
	}
	catch (std::exception const& e)
	{
		LOG4CPLUS_ERROR(_logger, "将Trade推入队列(" << _strategyId << ")时出现异常.异常信息:" << e.what());
	}
	catch (...)
	{
		LOG4CPLUS_ERROR(_logger, "将Trade推入队列(" << _strategyId << ")时出现异常.异常信息:无");
	}
}

void FutureLiquidityStrategy::processMarketDatas()
{
	MarketData marketData;
	while (_marketDataQueue.try_dequeue(marketData))
	{
		if (marketData.instrumentId == string(_pInstrument->instrumentId))
		{
			
			maker = transferMarketData(marketData);
			//LOG4CPLUS_INFO(_logger, "做市合约行情: " << ::toString(marketData));
			if (!_isAvgInitialized)
			{
				_isAvgInitialized = initializeAvg();
			}
			_isMakerChanging = true;
			_tradedInTick = false;
		}
		if (_pBaseInstrument && marketData.instrumentId == string(_pBaseInstrument->instrumentId))
		{
			lastRefer.timepoint = steady_clock::now();
			lastRefer.net = _net;
			lastRefer.tick = refer;
			refer = transferMarketData(marketData);
			//LOG4CPLUS_INFO(_logger, "主力合约行情: " << ::toString(marketData));
			_isReferChanging = true;
			_rCount ++;
		}
	}
}

bool FutureLiquidityStrategy::initializeAvg()
{
	if (maker.ask_volume1 > 0 && maker.bid_volume1 > 0)
	{
		_avg = (maker.ask_price1 + maker.bid_price1) * 0.5;
		_realPnl = -_net * _avg;
		return true;
	}
	return false;
}

void FutureLiquidityStrategy::processTrades()
{
	bool handleManualTrade = _handleManualTrade;

	Trade trade;
	while (_tradeFeedQueue.try_dequeue(trade))
	{
		if (handleManualTrade && trade.strategyId != _strategyId)
		{
			FutureLiquidity::MMTrade newTrade;
			newTrade.price = trade.tradedPrice;
			newTrade.volume = trade.volumeTraded;
			newTrade.longShort = trade.longShort;
			onMMTrade(newTrade);
		}
	}
}

void FutureLiquidityStrategy::processOrders()
{
	Order order;
	while (_orderFeedQueue.try_dequeue(order))
	{
		if (order.strategyId == _strategyId)
		{
			if (_orderFeedsByInternalOrderId.find(order.internalOrderId) == _orderFeedsByInternalOrderId.end())
			{
				LOG4CPLUS_ERROR(_logger, "找不到原订单, order[" << order << "]");
				continue;
			}
			auto& oldOrder = _orderFeedsByInternalOrderId[order.internalOrderId];

			auto newTradedVolume = max(order.volumeTraded, oldOrder.volumeTraded) - oldOrder.volumeTraded;
			if (newTradedVolume > 0)
			{
				FutureLiquidity::MMTrade newTrade;
				newTrade.price = order.orderPrice;
				newTrade.volume = newTradedVolume;
				newTrade.longShort = order.longShort;
				strncpy(newTrade.comments, order.comments, sizeof(newTrade.comments));
				onMMTrade(newTrade);
				_tradedInTick = true;
			}
			oldOrder = order;
			updateObligation();
		}
	}
}

void FutureLiquidityStrategy::processQuotes()
{
	Quote quote;
	while (_quoteFeedQueue.try_dequeue(quote))
	{
		if (strcmp(quote.instrumentId, _pInstrument->instrumentId) == 0)
		{
			if (_quoteFeedsByInternalQuoteId.find(quote.internalQuoteId) == _quoteFeedsByInternalQuoteId.end())
			{
				LOG4CPLUS_ERROR(_logger, "找不到原订单, quote[" << quote << "]");
				continue;
			}
			auto& oldQuote = _quoteFeedsByInternalQuoteId[quote.internalQuoteId];

			auto newBidTradedVolume = max(quote.bidVolumeTraded, oldQuote.bidVolumeTraded) - oldQuote.bidVolumeTraded;
			if (newBidTradedVolume > 0)
			{
				FutureLiquidity::MMTrade newTrade;
				newTrade.price = quote.bidOrderPrice;
				newTrade.volume = newBidTradedVolume;
				newTrade.longShort = LongShort::LONG_;
				strncpy(newTrade.comments, quote.comments, sizeof(newTrade.comments));
				onMMTrade(newTrade);
				_tradedInTick = true;
			}
			auto newAskTradedVolume = max(quote.askVolumeTraded, oldQuote.askVolumeTraded) - oldQuote.askVolumeTraded;
			if (newAskTradedVolume > 0)
			{
				FutureLiquidity::MMTrade newTrade;
				newTrade.price = quote.askOrderPrice;
				newTrade.volume = newAskTradedVolume;
				newTrade.longShort = LongShort::SHORT_;
				strncpy(newTrade.comments, quote.comments, sizeof(newTrade.comments));
				onMMTrade(newTrade);
				_tradedInTick = true;
			}
			oldQuote = quote;
			if (_pPreQuote && _pPreQuote->internalQuoteId == quote.internalQuoteId)
			{
				if (getSimpleOrderStatus(_pPreQuote) == SimpleOrderStatus::Terminated)
				{
					_pPreQuote = nullptr;
				}
			}
			updateObligation();
		}
	}
}

bool FutureLiquidityStrategy::checkNearMarketClose()
{
	auto exchangeTime = TradingTimeManager::getInstance().getExchangeTime(_pInstrument->exchangeId);
	auto exchangeTimet = system_clock::to_time_t(exchangeTime);
	auto local_time = *localtime(&exchangeTimet);
	auto hour = local_time.tm_hour;
	auto minute = local_time.tm_min;
	LOG4CPLUS_DEBUG(_logger, "checkNearMarketClose: hour=" << hour << ", minute=" << minute);
	if (hour == 14 && minute >= 58)
	{
		return true;
	}
	else if (hour == 22 && minute >= 58)
	{
		return true;
	}
    else if (hour == 11 && minute >= 29)
    {
        return true;
    }
	else
	{
		return false;
	}
}

void FutureLiquidityStrategy::updateAutoCloseMode()
{
	bool useAutoCloseFlag = _useAutoCloseFlag;

	if (useAutoCloseFlag && checkNearMarketClose()){
		_isAutoCloseMode = true;
	}else{
		_isAutoCloseMode = false;
	}
}

int FutureLiquidityStrategy::min_book_volume() {
	int total_bid = maker.bid_volume1 + maker.bid_volume2 + maker.bid_volume3 + maker.bid_volume4 + maker.bid_volume5;
	int total_ask = maker.ask_volume1 + maker.ask_volume2 + maker.ask_volume3 + maker.ask_volume4 + maker.ask_volume5;
	int total = min(total_bid, total_ask);
	return max(total, 1);
}

void FutureLiquidityStrategy::makerFilter()
{
	if (_pBaseInstrument){

		if (_pauseCount < _rCount && !_isQuoting)
			{
				_isQuoting = true;
				LOG4CPLUS_INFO(_logger, "pause finish");
			}

		if (_isQuoting){
			if (maker.ask_volume1 > 0 && maker.bid_volume1 > 0
				&& (maker.bid_price1 > maker.upper_limit - 5 * _tick
					|| maker.ask_price1 < maker.lower_limit + 5 * _tick))
			{
				_isQuoting = false;
				_pauseCount = _rCount + 100;
				LOG4CPLUS_INFO(_logger, "price limit pause");
				return;
			}

			if (maker.ask_price1 - maker.bid_price1 > _edge * _tick) {
				_isQuoting = false;
				_pauseCount = _rCount + 1;
				LOG4CPLUS_INFO(_logger, "liquidity spread pause");
				return;
			}
			
			if (min_book_volume() < _lots * 8) {
				_isQuoting = false;
				_pauseCount = _rCount + 1;
				LOG4CPLUS_INFO(_logger, "liquidity pause min_book_volume:" << min_book_volume());
				return;
			}
		}
	}
}

int FutureLiquidityStrategy::depthSignal()
{
	double  askDepth = 0;
	int  askV = 0;
	double  bidDepth = 0;
	int  bidV = 0;
	double fair = _fair;
	askDepth = (orderBook.ask_price1 - fair) * orderBook.ask_volume1 + (orderBook.ask_price2 - fair)* orderBook.ask_volume2 \
		+ (orderBook.ask_price3 - fair) * orderBook.ask_volume3 + (orderBook.ask_price4 - fair) * orderBook.ask_volume4 + (orderBook.ask_price5 - fair) * orderBook.ask_volume5;
	askV = orderBook.ask_volume1 + orderBook.ask_volume2 + orderBook.ask_volume3 + orderBook.ask_volume4 + orderBook.ask_volume5;
	bidDepth = (orderBook.bid_price1 - fair) * orderBook.bid_volume1 + (orderBook.bid_price2 - fair) * orderBook.bid_volume2 \
		+ (orderBook.bid_price3 - fair) * orderBook.bid_volume3 + (orderBook.bid_price4 - fair) * orderBook.bid_volume4 + (orderBook.bid_price5 - fair) * orderBook.bid_volume5;
	bidV = orderBook.bid_volume1 + orderBook.bid_volume2 + orderBook.bid_volume3 + orderBook.bid_volume4 + orderBook.bid_volume5;
	bidDepth = -1 * bidDepth;
	askDepth /= askV;
	bidDepth /= bidV;
	double depth = (askDepth - bidDepth) / (askDepth + bidDepth);
	if (depth > 0.1)
		return 1;
	else if (depth < -0.1)
		return - 1;
	else
		return 0;
}

void FutureLiquidityStrategy::refreshPrice()
{
	int edge = _edge;
	_lots = _pInstrument->lots[_level];

	_fair = getFairPrice(_fair, maker.ask_price1, maker.bid_price1, edge * _tick);
	_mFair.insert(_fair);

	adjustOrderBook();

	double pos_adjust = 0.0;
	if (_lots > 0) {
		pos_adjust = -0.5 * _net / _lots * _tick;
	}
	else {
		LOG4CPLUS_ERROR(_logger, "Lots 为0");
	}

	double fixedEdge = edge;
	double vol = 0;
	double currentEdge = (getAsk5(maker, 5 * _lots, 0).price - getBid5(maker, 5 * _lots, 0).price) / _tick; //设定报价价差, 随市场价宽变动
	_mEdge.insert(currentEdge);
	double last10EdgeMedian = _mEdge.getLast10Median();

	if (_mBid.getCount() > 10)
		vol = _mFair.getLastStd(5) * 6 / sqrt(_tick);

	if (_mBid.getCount() > 10)
		fixedEdge = max(max(max(1.0, edge * 0.5), max(currentEdge, last10EdgeMedian)),vol) - 1; //防谎骗

	//数挡位
	double bidPrice = _fair - fixedEdge * _tick * 0.5 + 0.5 * _tick;
	double askPrice = _fair + fixedEdge * _tick * 0.5 - 0.5 * _tick;
	double bidSafePrice = _fair - fixedEdge * _tick * 0.5 - 0.5 * _tick;
	double askSafePrice = _fair + fixedEdge * _tick * 0.5 + 0.5 * _tick;

	int signal = depthSignal();
	if (signal == 1) {
		_bidP = bidPrice + pos_adjust;
		_askP = askSafePrice + pos_adjust;
		_tradeFlag = 'u';
	}else if (signal == -1) {
		_askP = askPrice + pos_adjust;
		_bidP = bidSafePrice + pos_adjust;
		_tradeFlag = 'd';
	}else {
		_askP = askSafePrice + pos_adjust;
		_bidP = bidSafePrice + pos_adjust;
		_tradeFlag = 's';
	}

	_askP = _tick * round(_askP / _tick);
	_bidP = _tick * round(_bidP / _tick);

	if (Equal(_askP - _bidP,(_edge + 1) * _tick))//由于取整或仓位调整造成的价差问题
	{
		if (_net < 0)
			_bidP += _tick;
		if (_net > 0)
			_askP -= _tick;
	}

	if (Equal(_bidP, maker.bid_price1) && maker.bid_volume1 <= _lots * 3 && _net > _lots) {
		_bidP -= _tick;
	}
	if (Equal(_askP, maker.ask_price1) && maker.ask_volume1 <= _lots * 3 && _net < -1 * _lots) {
		_askP += _tick;
	} //不连续增仓

	if (_mBid.getCount() > 10) {
		if (_net <= -1 * _maxPos  && maker.bid_volume1 >= 3 * _lots)
			_bidP = min(_bidP, maker.bid_price1 + _tick);
		else
			_bidP = min(_bidP, maker.bid_price1);
		if (_net >= 1 * _maxPos  && maker.ask_volume1 >= 3 * _lots)
			_askP = max(_askP, maker.ask_price1 - _tick);
		else
			_askP = max(_askP, maker.ask_price1);
	}

	_stdEdge = round((_askP - _bidP) / _tick);

	if (_askP > 0 && _bidP > 0)
	{
		_mBid.insert(_bidP);
	}

	_cumPnl = round((_net * _fair + _realPnl) * _mu);
	LOG4CPLUS_INFO(_logger, "refreshPrice:"
		<< _mBid.getCount()
		<< ",myBid:" << _bidP
		<< ",myAsk:" << _askP
		<< ",Bid:" << bidPrice
		<< ",Ask:" << askPrice
		<< ",BidS:" << bidSafePrice
		<< ",AskS:" << askSafePrice
		<< ",bestBid:" << maker.bid_price1
		<< ",bestAsk:" << maker.ask_price1
		<< ",stdEdge:" << _stdEdge
		<< ",pnl:" << _pnl
		<< ",net:" << _net
		<< ",cumPnl:" << _cumPnl
		<< ",stdRange:" << _stdRange
		<< ",lots:" << _lots
		<< ",spread:" << _spread
		<< ",fair:" << _fair
		<< ",longFlag:" << _tradeFlag
	    << ",isQuoting:" << _isQuoting
		<< ",edge:" << edge
		<< ",fixedEdge:" << fixedEdge
		<< ",currentEdge:" << currentEdge
		<< ",last10EdgeMedian:" << last10EdgeMedian
		<< ",vol:" << vol
		<< ",pos_adjust:" << pos_adjust		
	);
}

bool FutureLiquidityStrategy::checkHedge()
{
	int edge = _edge;
	int autoCloseSpreadTicks = _autoCloseSpreadTicks;
	int stopFactor = 3;

	_spread = maker.ask_price1 - maker.bid_price1;
	_stdRange = _spread / _tick;

	/*
	double stop = max(3 * (double)edge, 3 * _stdRange);
	if (_tradeFlag == 'd' && _tradePrice > 0.0) {
		_pnl = (_tradePrice - _fair) / _tick;
	}else if (_tradeFlag == 'u' && _tradePrice > 0.0) {
		_pnl = (_fair - _tradePrice) / _tick;
	}else {
		if(_net >0)
			_pnl = (_fair - _avg) / _tick;
		else
			_pnl = (_avg - _fair) / _tick;
	}*/
	double stop = stopFactor * max((double)edge, _stdRange) * ((double)_lots / (abs(_net) + _lots));
	_pnl = _net * (_fair - _avg) / (abs(_net) * _tick);


	if (_net != 0 && _isAutoCloseMode){
		if (maker.ask_price1 - maker.bid_price1 <= autoCloseSpreadTicks * _tick){
			LOG4CPLUS_INFO(_logger, "AutoClose Triggered");
			return true;
		}
	}

	bool ret = _mBid.getCount() > 5 // TODO
		&& _net != 0
		&& _spread > 0
		&& _spread <= max(1., 0.8 * edge)
		&& _stdRange < 1.
		&& _stdEdge < edge
		&& _pnl < -stop;//TODO
	if (_mBid.getCount() > _canHedgeCount) {
		LOG4CPLUS_INFO(_logger, "checkHedge:" << ret << ","
			<< _mBid.getCount()
			<< ",stop:" << stop
			<< ",net:" << _net
			<< ",spread:" << _spread
			<< ",edge:" << edge
			<< ",stdRange:" << _stdRange
			<< ",PNL:" << _pnl);
		_canHedgeCount = _mBid.getCount();
	}
	return ret;
}

bool FutureLiquidityStrategy::doSendHedge()
{
	VolumeType maxV = _lots;
	Order hedgeOrder;
	if (_net > 0)
	{
		hedgeOrder.longShort = SHORT_;
		hedgeOrder.orderPrice = maker.bid_price1;
	}
	else if (_net < 0)
	{
		hedgeOrder.longShort = LONG_;
		hedgeOrder.orderPrice = maker.ask_price1;
	}
	else
	{
		return false;
	}

	if (_isAutoCloseMode)
	{
		hedgeOrder.volumeOriginalTotal = abs(_net);
	}
	else
	{
		VolumeType VV = min(maxV, max(1, abs(_net )));
		hedgeOrder.volumeOriginalTotal = VV;
	}

	STRNCPY(hedgeOrder.instrumentId, _pInstrument->instrumentId, sizeof(hedgeOrder.instrumentId));
	hedgeOrder.openClose = getOpenClose(_pInstrument, hedgeOrder.longShort, hedgeOrder.volumeOriginalTotal);
	strcpy(hedgeOrder.comments, "Hedge");
	hedgeOrder.orderType = LIMIT;
	hedgeOrder.matchCondition = FAK;
	hedgeOrder.strategyId = _strategyId;
	hedgeOrder.exchangeId = _pInstrument->exchangeId;
	hedgeOrder.basePrice = hedgeOrder.orderPrice;

	if (hedgeOrder.volumeOriginalTotal <= 0)
	{
		return false;
	}
	if (isnan(hedgeOrder.orderPrice)
		|| hedgeOrder.orderPrice < _pInstrument->lowerLimit || hedgeOrder.orderPrice > _pInstrument->upperLimit)
	{
		return false;
	}
	if (_pHedgeOrder = sendOrderWithoutInternalOrderId(hedgeOrder, _level, false); _pHedgeOrder)
	{
		LOG4CPLUS_INFO(_logger, "send hedgeOrder: net=" << _net
			<< ", pnl=" << _pnl
			<< ", order[" << *_pHedgeOrder << "]");
		return true;
	}
	return false;
}

bool FutureLiquidityStrategy::doCancelAll()
{
	return _useQuote ? doCancelAllQuote() : doCancelAllOrder();
}

// 这里可以加入超时处理逻辑
bool FutureLiquidityStrategy::doCancelAllQuote()
{
	switch (auto preQuoteStatus = getSimpleOrderStatus(_pPreQuote); preQuoteStatus)
	{
	case SimpleOrderStatus::ExchangeOrder:
		deleteQuote(*_pPreQuote);
		break;
	case SimpleOrderStatus::Terminated:
		_pPreQuote = nullptr;
		break;
	default:
		break;
	}

	switch (auto quoteStatus = getSimpleOrderStatus(_pQuote); quoteStatus)
	{
	case SimpleOrderStatus::ExchangeOrder:
		deleteQuote(*_pQuote);
		break;
	case SimpleOrderStatus::Terminated:
		_pQuote = _pPreQuote;
		break;
	default:
		break;
	}

	return !_pPreQuote && !_pQuote;
}

bool FutureLiquidityStrategy::doCancelAllOrder()
{
	switch (auto bidSideOrderStatus = getSimpleOrderStatus(_pBidOrder); bidSideOrderStatus)
	{
	case SimpleOrderStatus::ExchangeOrder:
		deleteOrder(*_pBidOrder);
		break;
	case SimpleOrderStatus::Terminated:
		_pBidOrder = nullptr;
		break;
	default:
		break;
	}

	switch (auto askSideOrderStatus = getSimpleOrderStatus(_pAskOrder); askSideOrderStatus)
	{
	case SimpleOrderStatus::ExchangeOrder:
		deleteOrder(*_pAskOrder);
		break;
	case SimpleOrderStatus::Terminated:
		_pAskOrder = nullptr;
		break;
	default:
		break;
	}

	return !_pBidOrder && !_pAskOrder;
}

bool FutureLiquidityStrategy::doSend()
{
	return _useQuote ? doSendQuote() : doSendOrder();
}

bool FutureLiquidityStrategy::doSendQuote()
{
	if (_pQuote) return false;
	if (_lots <= 0) {
		LOG4CPLUS_ERROR(_logger, "Lots 为0");
		return false;
	}

	int64_t internalQuoteId = (orderIdGenerator += 3) - 3;
	int64_t askSideInternalId = internalQuoteId + 1;
	int64_t bidSideInternalId = internalQuoteId + 2;

	Quote& quote = _quoteFeedsByInternalQuoteId[internalQuoteId];
	quote.quoteStatus = PENDING_ADD;
	quote.bidOrderStatus = PENDING_ADD;
	quote.askOrderStatus = PENDING_ADD;
	quote.bidOrderPrice = _bidP;
	quote.askOrderPrice = _askP;
	quote.bidVolumeOriginalTotal = _lots;
	quote.askVolumeOriginalTotal = _lots;
    quote.comments[0] = _tradeFlag;

	if (quote.bidVolumeOriginalTotal <= 0 || quote.askVolumeOriginalTotal <= 0)
	{
		return false;
	}
	if (isnan(quote.bidOrderPrice) || isnan(quote.askOrderPrice)
		||quote.bidOrderPrice < _pInstrument->lowerLimit || quote.bidOrderPrice > _pInstrument->upperLimit
		|| quote.askOrderPrice < _pInstrument->lowerLimit || quote.askOrderPrice > _pInstrument->upperLimit)
	{
		return false;
	}

	OpenClose ocBid = getOpenClose(_pInstrument, LONG_, _lots);
	OpenClose ocAsk = getOpenClose(_pInstrument, SHORT_, _lots);
	quote.bidOpenClose = ocBid;
	quote.askOpenClose = ocAsk;

	quote.bidVolumeTotal = quote.bidVolumeOriginalTotal;
	quote.askVolumeTotal = quote.askVolumeOriginalTotal;
	quote.bidVolumeTraded = 0;
	quote.askVolumeTraded = 0;
	STRNCPY(quote.instrumentId, _pInstrument->instrumentId, sizeof(quote.instrumentId));
	quote.level = _level;
	quote.strategyId = _strategyId;
	quote.portfolioId = _portfolioId;
	quote.platformId = _pInstrument->platformService->getPlatformId();
	quote.sourceId = SOURCE_MMSYSTEM;
	quote.internalQuoteId = internalQuoteId;
	quote.askInternalOrderId = askSideInternalId;
	quote.bidInternalOrderId = bidSideInternalId;
	quote.exchangeId = _pInstrument->exchangeId;
	if (_pBaseInstrument)
	{
		quote.basePrice = refer.bid_price1;
	}
	else
	{
		quote.basePrice = maker.bid_price1;
	}
	quote.prevInternalQuoteId = _pPreQuote ? _pPreQuote->internalQuoteId : -1;
	STRCPY(quote.quoteRequestId, "");

	if (isnan(quote.bidOrderPrice) || isnan(quote.askOrderPrice))
	{
		return false;
	}
	if (sendQuote(quote))
	{
		_pQuote = &quote;

		if (ocBid == CLOSE)
		{
			_pInstrument->totalClosableShortPosition -= quote.bidVolumeOriginalTotal;
			_pInstrument->totalClosableSelfShortPosition -= quote.bidVolumeOriginalTotal;
		}
		else if (ocBid == CLOSE_TODAY)
		{
			_pInstrument->totalClosableShortPosition -= quote.bidVolumeOriginalTotal;
			_pInstrument->todayClosableShortPosition -= quote.bidVolumeOriginalTotal;
			_pInstrument->totalClosableSelfShortPosition -= quote.bidVolumeOriginalTotal;
		}
		if (ocAsk == CLOSE)
		{
			_pInstrument->totalClosableLongPosition -= quote.askVolumeOriginalTotal;
			_pInstrument->totalClosableSelfLongPosition -= quote.askVolumeOriginalTotal;
		}
		else if (ocAsk == CLOSE_TODAY)
		{
			_pInstrument->totalClosableLongPosition -= quote.askVolumeOriginalTotal;
			_pInstrument->todayClosableLongPosition -= quote.askVolumeOriginalTotal;
			_pInstrument->totalClosableSelfLongPosition -= quote.askVolumeOriginalTotal;
		}
		return true;
	}
	else
	{
		return false;
	}
}

bool FutureLiquidityStrategy::doSendOrder()
{
	if (_pBidOrder || _pAskOrder) return false;
	if (_lots <= 0) {
		LOG4CPLUS_ERROR(_logger, "Lots 为0");
		return false;
	}

	Order bidOrder;
	bidOrder.orderPrice = _bidP;
	bidOrder.volumeOriginalTotal = _lots;	
	bidOrder.longShort = LONG_;
	bidOrder.openClose = getOpenClose(_pInstrument, LONG_, _lots);
	STRNCPY(bidOrder.instrumentId, _pInstrument->instrumentId, sizeof(bidOrder.instrumentId));
	bidOrder.orderType = LIMIT;
	bidOrder.matchCondition = GFD;
	bidOrder.strategyId = _strategyId;
	bidOrder.exchangeId = _pInstrument->exchangeId;
	if (_pBaseInstrument){
		bidOrder.basePrice = refer.bid_price1;
	}else{
		bidOrder.basePrice = maker.bid_price1;
	}

	Order askOrder;
	askOrder.orderPrice = _askP;
	askOrder.volumeOriginalTotal = _lots;
    bidOrder.comments[0] = _tradeFlag;
	askOrder.comments[0] = _tradeFlag;

	askOrder.longShort = SHORT_;
	askOrder.openClose = getOpenClose(_pInstrument, SHORT_, _lots);
	STRNCPY(askOrder.instrumentId, _pInstrument->instrumentId, sizeof(askOrder.instrumentId));
	askOrder.orderType = LIMIT;
	askOrder.matchCondition = GFD;
	askOrder.strategyId = _strategyId;
	askOrder.exchangeId = _pInstrument->exchangeId;
	if (_pBaseInstrument)
	{
		askOrder.basePrice = refer.ask_price1;
	}
	else
	{
		askOrder.basePrice = maker.ask_price1;
	}

	if (bidOrder.volumeOriginalTotal <= 0 || askOrder.volumeOriginalTotal <= 0)
	{
		return false;
	}
	if (isnan(bidOrder.orderPrice) || isnan(askOrder.orderPrice)
		|| bidOrder.orderPrice < _pInstrument->lowerLimit || bidOrder.orderPrice > _pInstrument->upperLimit
		|| askOrder.orderPrice < _pInstrument->lowerLimit || askOrder.orderPrice > _pInstrument->upperLimit)
	{
		return false;
	}
	_pBidOrder = sendOrderWithoutInternalOrderId(bidOrder, _level);
	_pAskOrder = sendOrderWithoutInternalOrderId(askOrder, _level);

	return _pBidOrder || _pAskOrder;
}

void FutureLiquidityStrategy::doReset()
{
	if (_useQuote)
	{
		_pQuote = _pPreQuote;
		_pPreQuote = nullptr;
	}
	else
	{
		_pBidOrder = nullptr;
		_pAskOrder = nullptr;
	}
}

bool FutureLiquidityStrategy::doCounterReset()
{
	if (_pPreQuote) return false;
	
	_pPreQuote = _pQuote;
	_pQuote = nullptr;
	return true;
}

bool FutureLiquidityStrategy::checkNeedDelete()
{
	if (_pInstrument->bidSideTradingFlag[_level] == 0
		|| _pInstrument->askSideTradingFlag[_level] == 0
		|| _pInstrument->retries[_level] >= _pInstrument->maxRetries[_level]
		|| _pInstrument->bidSideTraded[_level] >= _pInstrument->bidSideTradesAllowed[_level]
		|| _pInstrument->askSideTraded[_level] >= _pInstrument->askSideTradesAllowed[_level])
	{
		return true;
	}

	if (_useQuote){
		if (_pQuote){
			auto askQuoteResidualVolume = max(_pQuote->askVolumeOriginalTotal - _pQuote->askVolumeTraded, 0);
			auto bidQuoteResidualVolume = max(_pQuote->bidVolumeOriginalTotal - _pQuote->bidVolumeTraded, 0);
			bool ret = askQuoteResidualVolume < _lots
				|| bidQuoteResidualVolume < _lots
				|| _bidP != _pQuote->bidOrderPrice
				|| _askP != _pQuote->askOrderPrice
				|| !_isQuoting;
			if (ret != _quoteNeedDelete) {
				LOG4CPLUS_INFO(_logger, "checkNeedDelete:" << ret << ","
					<< _mBid.getCount()
					<< ",bidP:" << _bidP
					<< ",askP:" << _askP
					<< ",bidOrderPrice:" << _pQuote->bidOrderPrice
					<< ",askOrderPrice:" << _pQuote->askOrderPrice
					<< ",askQuoteResidualVolume:" << askQuoteResidualVolume
					<< ",bidQuoteResidualVolume:" << bidQuoteResidualVolume
					<< ",lots:" << _lots
					<< ",isQuoting:" << _isQuoting
					<< ",betweenTickFlag:" << _betweenTickFlag);
				_quoteNeedDelete = ret;
			}
			return ret;
		}else{
			LOG4CPLUS_INFO(_logger, "checkNeedDelete:0");
			return false;
		}
	}else{
		bool bidNeedDeleteFlag = false;
		if (_pBidOrder)
		{
			auto residualVolume = max(_pBidOrder->volumeOriginalTotal - _pBidOrder->volumeTraded, 0);
			bidNeedDeleteFlag = residualVolume < _lots
				|| _bidP != _pBidOrder->orderPrice
				|| !_isQuoting;
			if (bidNeedDeleteFlag != _bidNeedDelete) {
				LOG4CPLUS_INFO(_logger, "checkNeedDelete:"
					<< _mBid.getCount()
					<< ",bidNeedDeleteFlag:" << bidNeedDeleteFlag
					<< ",bidP:" << _bidP
					<< ",bidOrderPrice:" << _pBidOrder->orderPrice
					<< ",residualVolume:" << residualVolume
					<< ",lots:" << _lots
					<< ",isQuoting:" << _isQuoting
					<< ",betweenTickFlag:" << _betweenTickFlag);
				_bidNeedDelete = bidNeedDeleteFlag;
			}
		}else{
			if (!_betweenTickFlag)
			{
				LOG4CPLUS_INFO(_logger, "checkNeedDelete:"
					<< _mBid.getCount()
					<< ",bidNeedDeleteFlag:0");
			}
		}

		bool askNeedDeleteFlag = false;
		if (_pAskOrder)
		{
			auto residualVolume = max(_pAskOrder->volumeOriginalTotal - _pBidOrder->volumeTraded, 0);
			askNeedDeleteFlag = residualVolume < _lots
				|| _askP != _pAskOrder->orderPrice
				|| !_isQuoting;
			if (askNeedDeleteFlag != _askNeedDelete) {
				LOG4CPLUS_INFO(_logger, "checkNeedDelete:"
					<< _mBid.getCount()
					<< ",askNeedDeleteFlag:" << askNeedDeleteFlag
					<< ",askP:" << _askP
					<< ",askOrderPrice:" << _pAskOrder->orderPrice
					<< ",residualVolume:" << residualVolume
					<< ",lots:" << _lots
					<< ",isQuoting:" << _isQuoting
					<< ",betweenTickFlag:" << _betweenTickFlag);
				_askNeedDelete = askNeedDeleteFlag;
			}
					
		}else{
			if (!_betweenTickFlag)
			{
				LOG4CPLUS_INFO(_logger, "checkNeedDelete:"
					<< _mBid.getCount()
					<< ",askNeedDeleteFlag:0");
			}
		}
		auto ret = bidNeedDeleteFlag || askNeedDeleteFlag;
		return ret;
	}
}

FutureLiquidityStrategy::SimpleOrderStatus FutureLiquidityStrategy::getSimpleMMOrderStatus()
{
	return _useQuote ? getSimpleOrderStatus(_pQuote) : getSyntheticOrderStatus(_pAskOrder, _pBidOrder);
}

bool FutureLiquidityStrategy::checkCanSend()
{
	if (_pInstrument->bidSideTradingFlag[_level] == 0
		|| _pInstrument->askSideTradingFlag[_level] == 0
		|| _pInstrument->retries[_level] >= _pInstrument->maxRetries[_level]
		|| _pInstrument->bidSideTraded[_level] >= _pInstrument->bidSideTradesAllowed[_level]
		|| _pInstrument->askSideTraded[_level] >= _pInstrument->askSideTradesAllowed[_level])
	{
		return false;
	}

	int edge = _edge;
	bool ret = _mBid.getCount() > 3 //TODO
		&& _bidP < maker.upper_limit
		&& _bidP > maker.lower_limit
		&& _askP < maker.upper_limit
		&& _askP > maker.lower_limit
		&& _isQuoting
		&& _askP - _bidP < 3 * edge * _tick; //TODO
	if (_mBid.getCount() > _canSendCount) {
		LOG4CPLUS_INFO(_logger, "checkCanSend:" << ret << ","
			<< _mBid.getCount()
			<< ",bidP:" << _bidP
			<< ",askP:" << _askP
			<< ",upper_limit:" << maker.upper_limit
			<< ",lower_limit:" << maker.lower_limit
			<< ",isQuoting:" << _isQuoting
			<< ",edge:" << edge
			<< ",betweenTickFlag:" << _betweenTickFlag);
		_canSendCount = _mBid.getCount();
	}
	return ret;
}

bool FutureLiquidityStrategy::makerEventLogicChain()
{
	switch (_makerTradingStatus)
	{
	case MakerEventLogicChain::Ready:
		if (_isMakerChanging )
		{
			updateAutoCloseMode();
			makerFilter();
			refreshPrice();
			_isMakerChanging = false;
			_betweenTickFlag = false;
			_makerTradingStatus = MakerEventLogicChain::CheckHedge;
			return false;
		}
		if (!_tradedInTick)
		{
			_betweenTickFlag = true;
			_makerTradingStatus = MakerEventLogicChain::Quoting;
			return false;
		}
		break;
	case MakerEventLogicChain::CheckHedge:
		if (checkHedge())
		{
			_makerTradingStatus = MakerEventLogicChain::CancelQuoteForHedging;
			return false;
		}
		else
		{
			_makerTradingStatus = MakerEventLogicChain::Quoting;
			return false;
		}
		break;
	case MakerEventLogicChain::CancelQuoteForHedging:
		if (doCancelAll())
		{
			_makerTradingStatus = MakerEventLogicChain::Hedging;
			return false;
		}
		break;
	case MakerEventLogicChain::Hedging:
		//if(_pHedgeOrder)
		//	LOG4CPLUS_INFO(_logger, "HedgeOrder:" << *_pHedgeOrder);
		switch (auto hedgeOrderStatus = getSimpleOrderStatus(_pHedgeOrder); hedgeOrderStatus)
		{
		case SimpleOrderStatus::Ready:
			if (!doSendHedge())
			{
				// 发单不成功不再循环
				_makerTradingStatus = MakerEventLogicChain::Quoting;
				return true;
			}
			break;
		case SimpleOrderStatus::Terminated:
			if (_pHedgeOrder->orderStatus == FILLED) {
				_pHedgeOrder = nullptr;
				_makerTradingStatus = MakerEventLogicChain::Quoting;
				return false;
			}
			break;
		default:
			break;
		}
		break;
	case MakerEventLogicChain::Quoting:
		switch (auto quoteStatus = getSimpleMMOrderStatus(); quoteStatus)
		{
		case SimpleOrderStatus::Ready:
			if (!checkCanSend())
			{
				// 不满足条件分支不再循环
				_makerTradingStatus = MakerEventLogicChain::Finish;
				return true;
			}
			if (!doSend()){
				// 发单不成功不再循环
				_makerTradingStatus = MakerEventLogicChain::Finish;
				return true;
			}else{
				_makerTradingStatus = MakerEventLogicChain::Finish;
				return false;
			}
			break;
		case SimpleOrderStatus::ExchangeOrder:
			if (!checkNeedDelete())
			{
				// 不满足条件分支不再循环
				_makerTradingStatus = MakerEventLogicChain::Finish;
				return true;
			}
			if (!_isQuoteDeleteRequired && _useQuote && checkCanSend() && doCounterReset())
			{
				if (!doSend())
				{
					doReset();
					doCancelAll();
				}
				else
				{
					_makerTradingStatus = MakerEventLogicChain::Finish;
					return false;
				}
			}
			else
			{
				doCancelAll();
			}
			break;
		case SimpleOrderStatus::Terminated:
			doReset();
			_makerTradingStatus = MakerEventLogicChain::Finish;
			return false;
			break;
		default:
			break;
		}
		break;
	case MakerEventLogicChain::Finish:
		lastMaker.timepoint = steady_clock::now();
		lastMaker.net = _net;
		lastMaker.tick = maker;
		_makerTradingStatus = MakerEventLogicChain::Ready;
		return false;
		break;
	default:
		break;
	}
	return true;
}

void FutureLiquidityStrategy::strategyMain()
{
	try
	{
		processMarketDatas();
		processOrders();
		processQuotes();
		processTrades();

		while (!makerEventLogicChain());
	}
	catch (std::exception const& e)
	{
		LOG4CPLUS_ERROR(logger, "Exception When Strategy Running=" << e.what());
	}
	catch (char const* e)
	{
		LOG4CPLUS_ERROR(logger, "Exception When Strategy Running=" << e);
	}
	catch (int e)
	{
		LOG4CPLUS_ERROR(logger, "Exception When Strategy Running=" << e);
	}
	catch (...)
	{
		LOG4CPLUS_ERROR(logger, "Exception When Strategy Running= No Reason.");
	}
}

OpenClose FutureLiquidityStrategy::getOpenClose(Instrument* pInstrument_, LongShort longShort_, int volume_)
{
	VolumeType hold = _hold;

	OpenClose ret;
	switch (longShort_)
	{
	case LONG_:
		if (volume_ > pInstrument_->totalShortPosition - hold)
			ret = OPEN;
		else if (volume_ <= pInstrument_->todayClosableShortPosition)
			ret = CLOSE_TODAY;
		else if (volume_ <= pInstrument_->totalClosableShortPosition - pInstrument_->todayClosableShortPosition)
			ret = CLOSE;
		else
			ret = OPEN;
		break;
	case SHORT_:
		if (volume_ > pInstrument_->totalLongPosition - hold)
			ret = OPEN;
		else if (volume_ <= pInstrument_->todayClosableLongPosition)
			ret = CLOSE_TODAY;
		else if (volume_ <= pInstrument_->totalClosableLongPosition - pInstrument_->todayClosableLongPosition)
			ret = CLOSE;
		else
			ret = OPEN;
		break;
	default:
		ret = OPEN_CLOSE_UNINITIALIZED;
		break;
	}
	if (pInstrument_->exchangeId != ExchangeId::SHFE && ret == CLOSE_TODAY)
	{
		ret = CLOSE;
	}

	return ret;
}

int FutureLiquidityStrategy::getObligationTicks()
{
	int maxObligationTicks = _maxObligationTicks;
	if (_useQuote)
	{
		auto askQuoteResidualVolume = _pQuote ? max(_pQuote->askVolumeOriginalTotal - _pQuote->askVolumeTraded, 0) : 0;
		auto bidQuoteResidualVolume = _pQuote ? max(_pQuote->bidVolumeOriginalTotal - _pQuote->bidVolumeTraded, 0) : 0;
		if (getSimpleOrderStatus(_pQuote) == SimpleOrderStatus::ExchangeOrder
			&& askQuoteResidualVolume >= _lots
			&& bidQuoteResidualVolume >= _lots)
		{
			int ret = round((_pQuote->askOrderPrice - _pQuote->bidOrderPrice) / _tick);
			return ret <= maxObligationTicks ? ret : 0;
		}
	}
	else
	{
		if (getSimpleOrderStatus(_pBidOrder) == SimpleOrderStatus::ExchangeOrder
			&& getSimpleOrderStatus(_pAskOrder) == SimpleOrderStatus::ExchangeOrder)
		{
			auto bidResidualVolume = max(_pBidOrder->volumeOriginalTotal - _pBidOrder->volumeTraded, 0);
			auto askResidualVolume = max(_pAskOrder->volumeOriginalTotal - _pAskOrder->volumeTraded, 0);
			if(bidResidualVolume >= _lots
				&& askResidualVolume >= _lots)
			{
				int ret = round((_pAskOrder->orderPrice - _pBidOrder->orderPrice) / _tick);
				return ret <= maxObligationTicks ? ret : 0;
			}
		}
	}
	return 0;
}

void FutureLiquidityStrategy::updateObligation()
{
	int maxObligationTicks = _maxObligationTicks;
	bool useObligationStat = _useObligationStat;

	if (useObligationStat)
	{
		if (auto obligationTicks = getObligationTicks();
			obligationTicks != _obligationTicks)
		{
			auto nowTime = steady_clock::now();
			auto obligationPeriod = duration_cast<milliseconds>(nowTime - _obligationStartTime).count();
			if (_obligationTicks < _maxStatTicks)
			{
				_obligationArray[_obligationTicks] += obligationPeriod;

				double P = 0.;
				double totalRn = 0;
				stringstream ss;
				for (int n = 0; n < _maxStatTicks; ++n)
				{
					auto Rn = _obligationArray[n];
					ss << Rn << ",";
					totalRn += Rn;
					if (n != 0)
					{
						P += Rn * (15 - pow(1.6, n));
					}
				}
				P /= totalRn;
				LOG4CPLUS_INFO(_logger, "updateObligation: P=" << P << ", details=[" << ss.str() << "]");
			}

			if (0 < _obligationTicks && _obligationTicks <= maxObligationTicks)
			{
				_pInstrument->obligationFullfilCount += obligationPeriod;
			}

			_obligationTicks = obligationTicks;
			_obligationStartTime = nowTime;
		}
	}
}
