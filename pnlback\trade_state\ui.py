from PySide6.QtWidgets import (QA<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QLineEdit,
                               QPushButton, QComboBox, QCheckBox, QTextEdit, QDateEdit, QGroupBox, QFormLayout, QLabel,QGridLayout,
                               QTabWidget, QDoubleSpinBox, QSpinBox, QScrollArea, QSizePolicy, QToolButton, QFrame)
from PySide6.QtCore import QDate, QObject, Signal, QThread, Qt, QParallelAnimationGroup, QPropertyAnimation, QAbstractAnimation, QSize
from PySide6.QtGui import QTextCursor, QPalette, QColor, QIcon
import sys
import traceback
import os
import datetime

sys.path.extend(
    [os.path.abspath(os.path.join(os.path.abspath(__file__) if '__file__' in globals() else os.getcwd(), *(['..'] * i)))
     for i in range(5)])

from pnlback.trade_state import trades_stat
from pnlback.trade_state.configs import DEFAULT_CONFIG
from db_solve.configs import paths

class TradeStatApplication:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.ui = TradeStatUI()
        self.setup_style()

    def setup_style(self):
        self.app.setStyle('Fusion')
        palette = QPalette()
        palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.WindowText, Qt.white)
        palette.setColor(QPalette.ColorRole.Base, QColor(25, 25, 25))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.ToolTipBase, Qt.white)
        palette.setColor(QPalette.ColorRole.ToolTipText, Qt.white)
        palette.setColor(QPalette.ColorRole.Text, Qt.white)
        palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.ButtonText, Qt.white)
        palette.setColor(QPalette.ColorRole.BrightText, Qt.red)
        palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.ColorRole.HighlightedText, Qt.black)
        self.app.setPalette(palette)

        # 更新全局样式表
        self.app.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #363636;
                border: 1px solid #545454;
                color: #ffffff;
                padding: 2px;
            }
            QPushButton {
                background-color: #4a4a4a;
                color: #ffffff;
                border: 1px solid #545454;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
            QPushButton:pressed {
                background-color: #3a3a3a;
            }
            QCheckBox {
                color: #ffffff;
            }
            QCheckBox::indicator {
                width: 13px;
                height: 13px;
                background-color: #363636;
                border: 1px solid #545454;
            }
            QCheckBox::indicator:checked {
                background-color: #ffffff;
                border: 1px solid #ffffff;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #ffffff;
            }
            QCheckBox::indicator:unchecked:hover {
                background-color: #464646;
            }
            QToolButton {
                color: #ffffff;
                background-color: #4a4a4a;
                border: 1px solid #545454;
                padding: 5px;
                border-radius: 3px;
            }
            QToolButton:hover {
                background-color: #5a5a5a;
            }
            QScrollArea, QScrollBar {
                background-color: #2b2b2b;
                border: 1px solid #545454;
            }
            QScrollBar::handle {
                background-color: #4a4a4a;
            }
            QScrollBar::handle:hover {
                background-color: #5a5a5a;
            }
            QTabWidget::pane {
                border: 1px solid #545454;
            }
            QTabBar::tab {
                background-color: #3a3a3a;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #545454;
            }
            QTabBar::tab:selected {
                background-color: #4a4a4a;
            }
            QTabBar::tab:hover {
                background-color: #5a5a5a;
            }
            QGroupBox {
                border: 1px solid #545454;
                margin-top: 0.5em;
                padding-top: 0.5em;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)

    def run(self):
        self.ui.show()
        return self.app.exec()

class OutputRedirector(QObject):
    outputWritten = Signal(str)

    def write(self, text):
        self.outputWritten.emit(str(text))

    def flush(self):
        pass

class TradeStatThread(QThread):
    update_signal = Signal(str)
    finished_signal = Signal(bool)

    def __init__(self, params):
        super().__init__()
        self.params = params

    def run(self):
        try:
            sys.stdout = OutputRedirector()
            sys.stdout.outputWritten.connect(self.update_signal.emit)
            
            trades_stat.main(self.params)
            self.finished_signal.emit(True)
        except Exception as e:
            self.update_signal.emit(f"错误: {str(e)}\n{traceback.format_exc()}")
            self.finished_signal.emit(False)
        finally:
            sys.stdout = sys.__stdout__

class CollapsibleBox(QWidget):
    def __init__(self, title="", parent=None):
        super(CollapsibleBox, self).__init__(parent)

        self.toggle_button = QToolButton(text=title, checkable=True, checked=False)
        self.toggle_button.setStyleSheet("QToolButton { border: none; }")
        self.toggle_button.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        self.toggle_button.setArrowType(Qt.RightArrow)
        self.toggle_button.pressed.connect(self.on_pressed)

        self.toggle_animation = QParallelAnimationGroup(self)

        self.content_area = QScrollArea()
        self.content_area.setMaximumHeight(0)
        self.content_area.setMinimumHeight(0)
        self.content_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.content_area.setFrameShape(QFrame.NoFrame)

        lay = QVBoxLayout(self)
        lay.setSpacing(0)
        lay.setContentsMargins(0, 0, 0, 0)
        lay.addWidget(self.toggle_button)
        lay.addWidget(self.content_area)

        self.toggle_animation.addAnimation(QPropertyAnimation(self, b"minimumHeight"))
        self.toggle_animation.addAnimation(QPropertyAnimation(self, b"maximumHeight"))
        self.toggle_animation.addAnimation(QPropertyAnimation(self.content_area, b"maximumHeight"))

    def on_pressed(self):
        checked = self.toggle_button.isChecked()
        self.toggle_button.setArrowType(Qt.DownArrow if not checked else Qt.RightArrow)
        self.toggle_animation.setDirection(QAbstractAnimation.Forward if not checked else QAbstractAnimation.Backward)
        self.toggle_animation.start()

    def setContentLayout(self, layout):
        lay = self.content_area.layout()
        del lay
        self.content_area.setLayout(layout)
        collapsed_height = self.sizeHint().height() - self.content_area.maximumHeight()
        content_height = layout.sizeHint().height()
        for i in range(self.toggle_animation.animationCount()):
            animation = self.toggle_animation.animationAt(i)
            animation.setDuration(0)  # 减少动画持续时间
            animation.setStartValue(collapsed_height)
            animation.setEndValue(collapsed_height + content_height)

        content_animation = self.toggle_animation.animationAt(self.toggle_animation.animationCount() - 1)
        content_animation.setDuration(0)  # 减少动画持续时间
        content_animation.setStartValue(0)
        content_animation.setEndValue(content_height)

    def sizeHint(self):
        return QSize(self.minimumWidth(), self.minimumHeight())

class TradeStatUI(QWidget):
    def __init__(self):
        super().__init__()
        self.trade_stat_thread = None
        self.config = DEFAULT_CONFIG.copy()
        self.ui_components = {}
        self.initUI()

    def initUI(self):
        self.setWindowTitle('交易统计工具')
        self.setGeometry(100, 100, 1200, 800)

        main_layout = QHBoxLayout(self)

        left_layout = QVBoxLayout()
        right_layout = QVBoxLayout()

        # 创建设置标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.addTab(self.create_general_tab(), "常规设置")
        self.tab_widget.addTab(self.create_advanced_tab(), "高级设置")
        left_layout.addWidget(self.tab_widget)

        # 运行按钮
        self.run_button = QPushButton('运行分析')
        self.run_button.clicked.connect(self.run_trade_stat)
        left_layout.addWidget(self.run_button)

        # 结果显示
        result_group = QGroupBox("分析结果")
        result_layout = QVBoxLayout()
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        result_group.setLayout(result_layout)
        right_layout.addWidget(result_group)

        main_layout.addLayout(left_layout, 1)
        main_layout.addLayout(right_layout, 3)

        self.setLayout(main_layout)

    def create_general_tab(self):
        general_tab = QWidget()
        layout = QFormLayout(general_tab)

        general_fields = ['datetoday', 'under', 'optcodes', 'month', 'siglist']
        for field in general_fields:
            self.create_ui_component(field, layout)

        return general_tab

    def create_advanced_tab(self):
        advanced_tab = QWidget()
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(advanced_tab)
        
        scroll.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        main_layout = QVBoxLayout(advanced_tab)

        # 混合设置组
        mix_box = CollapsibleBox("混合设置")
        mix_box.setMinimumHeight(50)  # 设置最小高度
        mix_layout = QGridLayout()
        mix_fields = ['mix2', 'mix3', 'mix4', 'mix44', 'mix5', 'mix6', 'mix7', 'mixtrd']
        for i, field in enumerate(mix_fields):
            checkbox = QCheckBox(field)
            checkbox.setChecked(self.config[field] == 1)
            mix_layout.addWidget(checkbox, i // 4, i % 4)
            self.ui_components[field] = checkbox
        mix_box.setContentLayout(mix_layout)
        main_layout.addWidget(mix_box)

        # 期货设置组
        futures_box = CollapsibleBox("期货设置")
        futures_box.setMinimumHeight(50)  # 设置最小高度
        futures_layout = QFormLayout()
        futures_fields = ['fut', 'fut2', 'fut3', 'fut4', 'fut44']
        for field in futures_fields:
            self.create_ui_component(field, futures_layout)
        futures_box.setContentLayout(futures_layout)
        main_layout.addWidget(futures_box)

        # 列设置组
        columns_box = CollapsibleBox("列设置")
        columns_box.setMinimumHeight(50)  # 设置最小高度
        columns_layout = QFormLayout()
        columns_fields = ['col2', 'col3', 'col44']
        for field in columns_fields:
            self.create_ui_component(field, columns_layout)
        columns_box.setContentLayout(columns_layout)
        main_layout.addWidget(columns_box)

        # 常量设置组
        constants_box = CollapsibleBox("常量设置")
        constants_box.setMinimumHeight(50)  # 设置最小高度
        constants_layout = QFormLayout()
        constants_fields = ['atm', 's_mult', 'mintick', 'edgedelta', 'futfreq', 'volfreq', 'multi', 'multi2', 'roundnum']
        for field in constants_fields:
            self.create_ui_component(field, constants_layout)
        constants_box.setContentLayout(constants_layout)
        main_layout.addWidget(constants_box)

        # 列表设置组
        lists_box = CollapsibleBox("列表设置")
        lists_box.setMinimumHeight(50)  # 设置最小高度
        lists_layout = QFormLayout()
        lists_fields = ['dsflist', 'dvlist', 'longdir', 'shortdir']
        for field in lists_fields:
            self.create_ui_component(field, lists_layout)
        lists_box.setContentLayout(lists_layout)
        main_layout.addWidget(lists_box)

        # 其他设置
        other_box = CollapsibleBox("其他设置")
        other_box.setMinimumHeight(50)  # 设置最小高度
        other_layout = QFormLayout()
        for field in self.config.keys():
            if field not in ['datetoday', 'under', 'optcodes', 'month', 'siglist', 'under_options'] + mix_fields + futures_fields + columns_fields + constants_fields + lists_fields:
                self.create_ui_component(field, other_layout)
        other_box.setContentLayout(other_layout)
        main_layout.addWidget(other_box)

        main_layout.addStretch(1)

        return scroll

    def create_ui_component(self, field, layout):
        value = self.config[field]
        if field == 'datetoday':
            component = QDateEdit()
            component.setDate(QDate.fromString(value, "yyyyMMdd"))
        elif field == 'under':
            component = QComboBox()
            component.addItems(self.config['under_options'])
            component.setCurrentText(value)
        elif isinstance(value, bool):
            component = QCheckBox()
            component.setChecked(value)
        elif isinstance(value, int):
            component = QSpinBox()
            component.setRange(-1000000, 1000000)
            component.setValue(value)
        elif isinstance(value, float):
            component = QDoubleSpinBox()
            component.setRange(-1000000, 1000000)
            component.setDecimals(6)
            component.setValue(value)
        elif field == 'tradetime00':
            component = QTextEdit()
            component.setPlainText('\n'.join([' - '.join(time_range) for time_range in value]))
            component.setFixedHeight(100)
        elif isinstance(value, list):
            component = QTextEdit()
            component.setPlainText('\n'.join(map(str, value)))
            component.setFixedHeight(100)
        else:
            component = QLineEdit(str(value))

        layout.addRow(f"{field}:", component)
        self.ui_components[field] = component

    def run_trade_stat(self):
        if self.trade_stat_thread and self.trade_stat_thread.isRunning():
            self.result_text.append("分析已在运行中，请等待完成。")
            return
        
        # 更新配置
        for field, component in self.ui_components.items():
            if isinstance(component, QDateEdit):
                self.config[field] = component.date().toString("yyyyMMdd")
            elif isinstance(component, QComboBox):
                self.config[field] = component.currentText()
            elif isinstance(component, QCheckBox):
                self.config[field] = int(component.isChecked())
            elif isinstance(component, (QSpinBox, QDoubleSpinBox)):
                self.config[field] = component.value()
            elif isinstance(component, QTextEdit):
                text = component.toPlainText()
                if field == 'tradetime00':
                    self.config[field] = [time_range.split(' - ') for time_range in text.split('\n') if time_range.strip()]
                elif field in ['siglist', 'optcodes', 'longdir', 'shortdir']:
                    self.config[field] = [item.strip() for item in text.split('\n') if item.strip()]
                elif field in ['dsflist', 'dvlist']:
                    self.config[field] = [int(item.strip()) for item in text.split('\n') if item.strip()]
                else:
                    self.config[field] = text
            else:
                self.config[field] = component.text()

        self.result_text.clear()
        self.run_button.setEnabled(False)

        self.trade_stat_thread = TradeStatThread(self.config)
        self.trade_stat_thread.update_signal.connect(self.update_result)
        self.trade_stat_thread.finished_signal.connect(self.on_trade_stat_finished)
        self.trade_stat_thread.start()

    def update_result(self, text):
        self.result_text.moveCursor(QTextCursor.End)
        self.result_text.insertPlainText(text)
        self.result_text.moveCursor(QTextCursor.End)

    def on_trade_stat_finished(self, success):
        self.run_button.setEnabled(True)
        if success:
            self.result_text.append("分析成功完成")
        else:
            self.result_text.append("分析失败")

if __name__ == '__main__':
    trade_stat_app = TradeStatApplication()
    sys.exit(trade_stat_app.run())
