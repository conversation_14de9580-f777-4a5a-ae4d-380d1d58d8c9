# -*- coding:utf-8 -*-
import pyodbc
import pandas as pd
import numpy as np
from WindPy import w
import datetime

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'

# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)

# Create a cursor from the connection
# cursor = cnxn.cursor()

w.start()

beginDate = datetime.datetime.strptime("2017-04-19",'%Y-%m-%d')
dt = datetime.datetime.strptime("2017-10-27",'%Y-%m-%d')

def tosql(zzcode):
    sql = "INSERT INTO SR_OptN VALUES (%d,%s,%d, %d, %d, %d, %d, %d, %d,%d, %d, %d,%d, %d, %d, %d, %d, %d, %d)"

    # 通过wset来取数据集数据
    print('\n\n' + '-----通过wset来取数据集数据,获取全部%s代码列表-----' % zzcode + '\n')
    wsetdata0 = w.wset("futurecc","wind_code=SR.CZC" )
    print(wsetdata0)

    codeslist = []

    for j in range(0, len(wsetdata0.Data[8])):

        if wsetdata0.Data[8][j]>=beginDate:
            codeslist.append(wsetdata0.Data[1][j])

    for j in range(0, len(wsetdata0.Data[6])):
        sqllist2 = []
        # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
        print("\n\n-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----\n" % (j, str(wsetdata.Data[1][j])))

        wssdata = w.wss(str(wsetdata.Data[1][j]), 'ipo_date')
        wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", wssdata.Data[0][0], dt,
                         "Fill=Blank", "PriceAdj=F")

        # wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
        if wsddata1.ErrorCode != 0:
            continue
        print(wsddata1)
        for i in range(0, len(wsddata1.Data[1])):
            sqllist = []
            rrr=0

            if len(wsddata1.Times) > 1:
                sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
                sqllist.append("00:00:00.0000001")

            sqllist.append(str(wsetdata.Data[1][j]))

            for k in range(0, len(wsddata1.Fields)):
                sqllist.append(wsddata1.Data[k][i])

            sqllist.append(-1)

            for iii in range(0,len(sqllist)):
                if str(sqllist[iii]) == "nan" :
                    rrr = 1
                    print(sqllist)
                    break
            if rrr == 1:
                continue
            sqltuple = tuple(sqllist)

            sqllist2.append(sqltuple)

        cursor.executemany(sql, sqllist2)

        conn.commit()

cursor = conn.cursor()

cursor.execute("""
 IF OBJECT_ID('SR_OptN', 'U') IS NOT NULL
    DROP TABLE SR_OptN
 CREATE TABLE SR_OptN (
     [DateTime]  DATE NOT NULL
      ,[Codes]  VARCHAR(20) NOT NULL
      ,[OpenPrice] VARCHAR(20)
      ,[High] VARCHAR(20)
      ,[Low] VARCHAR(20) NOT NULL
      ,[ClosePrice] VARCHAR(20) 
      ,[SettlePrice] VARCHAR(20)
      ,[PreClose]  VARCHAR(20)
      ,[PreSettle]  VARCHAR(20)
      ,[CHG] VARCHAR(20) 
      ,[chg_settlement] VARCHAR(20)
      ,[Volume] VARCHAR(20)
      ,[oi] VARCHAR(20)
      ,[oi_Change] VARCHAR(20)
      ,[Amount] VARCHAR(20)
      ,[DELTA] VARCHAR(20)
      ,[US_Impliedvol] VARCHAR(20)
      ,[EXE_PRICE] VARCHAR(20)
      ,[EXE_DATE] VARCHAR(20)
    )
 """)

tosql("SR.CZC")
