# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-24
from WindPy import w
import pymssql
from datetime import datetime

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = "2017-09-10"  # datetime.now()
# beginDate = "2017-06-24"
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')
cursor = conn.cursor()

def tosql():
    sql = "INSERT INTO SR VALUES (%s,%s, %s, %d, %d, %d, %d, %d, %d)"

    # 通过wset来取数据集数据
    print('\n\n' + '-----通过wset来取数据集数据,获取全部%s代码列表-----' % "白糖" + '\n')
    wsetdata =w.wset("optionfuturescontractbasicinfo","exchange=CZCE;productcode=SR;contract=all")
    print(wsetdata)

    for j in range(0, len(wsetdata.Data[0])):
        sCODE = str(wsetdata.Data[0][j])+ ".CZC"
        # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
        print("\n\n-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----\n" % (j, str(wsetdata.Data[1][j])))
        listed_date = wsetdata.Data[9][j]
        wsddata1 = w.wsd(sCODE, "open,high,low,close,volume,oi", listed_date, dt, "TradingCalendar=CZCE;PriceAdj=F")

        # wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
        if wsddata1.ErrorCode != 0:
            continue
        print(wsddata1)
        for i in range(0, len(wsddata1.Data[0])):
            sqllist = []

            if len(wsddata1.Times) > 1:
                sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
                sqllist.append("00:00:00.0000001")

            sqllist.append(sCODE )

            for k in range(0, len(wsddata1.Fields)):
                sqllist.append(wsddata1.Data[k][i])

            # sqllist.append(-1)
            try:
                sqltuple = tuple(sqllist)
                cursor.execute(sql, sqltuple)
            except:
                print  sqllist
        conn.commit()


tosql()  # A股


conn.close()
