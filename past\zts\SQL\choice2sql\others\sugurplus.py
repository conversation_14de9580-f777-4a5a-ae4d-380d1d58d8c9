# -*- coding:utf-8 -*-
import pyodbc
import pandas as pd
import numpy as np
from WindPy import w
import datetime

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'

# Specifying the ODBC driver, server name, database, etc. directly
cnxn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)

# Create a cursor from the connection
# cursor = cnxn.cursor()

sql = "SELECT  codes,exe_price,exe_date  FROM [Alex].[dbo].[SR2] "

pf = pd.read_sql(sql, cnxn, index_col=["codes"], coerce_float=True, params=None, parse_dates=None,
                 columns=None, chunksize=None)

print(pf)

w.start()

codes=pf.index.values.tolist()

codes2=[]
for i in codes:
    i=i+".CZC"
    codes2.append(i)

wdata= w.wss(codes2, "lasttradingdate,exe_price","tradeDate=20171020")

#pf2 = pf.ix['2017-04-24', 'SR707C6200']
#print(pf2)

print(wdata)

sqllist2 = []
for i in range(0, len(wdata.Data[0])):
    sqllist = []

    sqllist.append(wdata.Codes[i])

    date1=wdata.Data[0][i]
    sqllist.append(date1.strftime('%Y-%m-%d'))
    sqllist.append(wdata.Data[1][i])

    sqltuple = tuple(sqllist)

    sqllist2.append(sqltuple)

sql2 = "INSERT INTO [Alex].[dbo].[SR2] VALUES (?,?,?)"

cursor = cnxn.cursor()

cursor.executemany(sql2, sqllist2)


cnxn.commit()

cnxn.close()
