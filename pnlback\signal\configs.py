from pnlback.signal.strategies.stray import Stray
from pnlback.signal.strategies.futone import futone
from db_solve.configs.paths import FUTCONFIG, UNDERCONFIG


class ModeSettings:
    def __init__(self, setting, setting_path):
        setting.update(UNDERCONFIG[setting["under"]])
        self.a50 = dict(
            drvmode=Stray, sigmode='cum', mixmode='backward', key=['LastPrice', ],
            fut1=setting['spot'] + setting['spot_month'],
            str1=setting_path.str1,
            fut2='CN' + setting['out_fut_month'], futmult2=1, str2=setting_path.str2,
            start=20, end=4900,
            minpx=1, minpx2=1, resetpxchg=1,
            minvol=10, rrratio=0, mult=1,
            lasttimelimit=100,
            groupList=['_signal', 'dtmdmix_type', '_dtfut_int', '_vol_type', ]
        )

        self.one = dict(
            drvmode=futone, sigmode='one', mixmode='backward',
            key=[setting['key_set'][key] for key in (6,)],
            fut1=setting['spot'] + setting['spot_month'],
            str1=setting_path.str1,
            fut2=setting['spot'] + setting['spot_month'],
            futmult2=FUTCONFIG[setting['spot']]['futmult'],
            futmintick=FUTCONFIG[setting['spot']]['mintick'],
            str2=setting_path.str1,
            minpx=0.6, mult1=setting['mult_dict'], sweight=0.5,
        )

        self.one2 = dict(
            drvmode=futone, sigmode='chg', mixmode='backward',
            key=[setting['key_set'][key] for key in (8,)],
            fut1='10007380',
            str1=setting_path.str_optmd,
            fut2='10007380', futmult2=10000,
            str2=setting_path.str_optmd,
            minpx=0.00000, mult1=setting['mult_dict'], sweight=0.5,
        )

        self.etffast = dict(
            drvmode=Stray, sigmode='cum', mixmode='backward', key=['LastPrice'],
            fut1=setting['spot'] + setting['spot_month'],
            str1=setting_path.str1,
            fut2='510500_tick', futmult2=1,
            str2=setting_path.str23,
            start=20, end=4900,
            minpx=0.0001, minpx2=0.0001, resetpxchg=0.0001,
            minvol=100000, rrratio=0, mult=1, lasttimelimit=100,
            groupList=['dtmdmix_type', '_dtfut_int', '_signal', '_vol_type']
        )

        self.etf = dict(
            drvmode=Stray, sigmode='fast', mixmode='backward', key=['ForQuoteSysID'],
            fut1=setting['spot'] + setting['spot_month'],
            fut2=setting['etf'][setting['under']],
            str2=setting_path.str23,
            start=20, end=4900,
            minpx=0.0002, minpx2=0.0002, resetpxchg=0.0002,
            minvol=500000, rrratio=0, mult=1, lasttimelimit=100,
        )

        self.fut = dict(
            drvmode=Stray, sigmode='one', mixmode='backward',
            key=[setting['key_set'][key] for key in (1,)],
            fut1=setting['spot'] + setting['spot_month'],
            fut2=setting['spot2'] + setting['spot_month'],
            futmult2=FUTCONFIG[setting['spot2']]['futmult'],
            str1=setting_path.str1,
            str2=setting_path.str1,
            start=-100, end=4900,
            minpx=0.1, minpx2=0.1, resetpxchg=0,
            minvol=1, rrratio=0,
            lasttimelimit=10000000,
            groupList=['_signal', 'dsf-1_y', 'dt_mdmix', ]
        )

        self.mix = dict(
            drvmode=Stray, sigmode='fast', mixmode='forward', key='mid',
            fut2='IM2401', str2=setting_path.str1,
            fut_ad='IC2401', key_ad=None, str_ad=setting_path.str1,
            start=-100, end=4900,
            minpx=0.4, minpx2=0.4, resetpxchg=0,
            minvol=0, rrratio=0, lasttimelimit=100,
            syn=0, mult=1, mult2=1
        )

    def get_mode(self, mode_name):
        return getattr(self, mode_name, None)


# 设置和路径的定义
setting = dict(
    datetoday='20250109', under='SH500', spot_month='2501', out_fut_month='2501',
    optcodes=['10008429', ],
    mode=['a50', 'one', 'fut', 'etffast'][1],
    etf=dict(SH300='16', SH500='159915', SH50='16'),
    silmode=['all', 'onlytrade', 'onlymid'][0],
    key_set={0: 'LastPrice', 1: 'mid', 2: 'ForQuoteSysID', 1.1: 'dsema', 1.2: 'BASIS', 1.3: 'basisema',1.4:'dsf-1',
             3: 'im1', 5: 'im5', 6: 'im5vol', 7: 'im2mult', 8: 'mid_minnum', 8.2: 'mid_minnum2',
             9: 'press', 10: 'voi', 11: 'turnover_mid', 12: 'mid_level', 13: 'mixedge_minnum'},
    mult_dict={'im5': 2, 'im5vol': 3, 'im2mult': 2, 'mid_minnum': 2, 'mid_minnum2': 2, 'press': 1,
               'voi': 1, 'turnover_mid': 1},
)
