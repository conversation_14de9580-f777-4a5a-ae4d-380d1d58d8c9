因子文档


## basic 类别因子

### spread_ratio
- 描述: 买卖价差与中间价的比率
- 依赖: AskPrice1, BidPrice1

### spread
- 描述: 卖一价与买一价之差
- 依赖: AskPrice1, BidPrice1

### imbalance_1
- 描述: 1档位的买卖委托量不平衡度
- 依赖: BidVol1, AskVol1
- 参数: {'level': 1}

### imbalance_2
- 描述: 2档位的买卖委托量不平衡度
- 依赖: BidVol2, AskVol2
- 参数: {'level': 2}

### imbalance_3
- 描述: 3档位的买卖委托量不平衡度
- 依赖: BidVol3, AskVol3
- 参数: {'level': 3}

### imbalance_4
- 描述: 4档位的买卖委托量不平衡度
- 依赖: BidVol4, AskVol4
- 参数: {'level': 4}

### imbalance_5
- 描述: 5档位的买卖委托量不平衡度
- 依赖: BidVol5, AskVol5
- 参数: {'level': 5}

### total_bid_vol
- 描述: 所有可用档位的总买单量
- 依赖: BidVol1, BidVol2, BidVol3, BidVol4, BidVol5

### total_ask_vol
- 描述: 所有可用档位的总卖单量
- 依赖: AskVol1, AskVol2, AskVol3, AskVol4, AskVol5

### vol_imbalance
- 描述: 总买卖委托量不平衡度
- 依赖: total_bid_vol, total_ask_vol

### bid_price_diff_1
- 描述: 买入1档与2档之间的价格差异
- 依赖: BidPrice1, BidPrice2
- 参数: {'level': 1, 'side': 'bid'}

### bid_price_diff_2
- 描述: 买入2档与3档之间的价格差异
- 依赖: BidPrice2, BidPrice3
- 参数: {'level': 2, 'side': 'bid'}

### bid_price_diff_3
- 描述: 买入3档与4档之间的价格差异
- 依赖: BidPrice3, BidPrice4
- 参数: {'level': 3, 'side': 'bid'}

### bid_price_diff_4
- 描述: 买入4档与5档之间的价格差异
- 依赖: BidPrice4, BidPrice5
- 参数: {'level': 4, 'side': 'bid'}

### ask_price_diff_1
- 描述: 卖出2档与1档之间的价格差异
- 依赖: AskPrice1, AskPrice2
- 参数: {'level': 1, 'side': 'ask'}

### ask_price_diff_2
- 描述: 卖出3档与2档之间的价格差异
- 依赖: AskPrice2, AskPrice3
- 参数: {'level': 2, 'side': 'ask'}

### ask_price_diff_3
- 描述: 卖出4档与3档之间的价格差异
- 依赖: AskPrice3, AskPrice4
- 参数: {'level': 3, 'side': 'ask'}

### ask_price_diff_4
- 描述: 卖出5档与4档之间的价格差异
- 依赖: AskPrice4, AskPrice5
- 参数: {'level': 4, 'side': 'ask'}

### bid_price_pressure
- 描述: 买方价格压力总和
- 依赖: BidPrice1, BidPrice2, BidPrice3, BidPrice4, BidPrice5

### ask_price_pressure
- 描述: 卖方价格压力总和
- 依赖: AskPrice1, AskPrice2, AskPrice3, AskPrice4, AskPrice5

### price_pressure
- 描述: 总价格压力
- 依赖: bid_price_pressure, ask_price_pressure

## time_series 类别因子

### mid_return_5
- 描述: 5周期中间价收益率
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 5}

### mid_return_10
- 描述: 10周期中间价收益率
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 10}

### mid_ma_10
- 描述: 10周期中间价移动平均
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 10}

### mid_std_10
- 描述: 10周期中间价标准差
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 10}

### vol_ma_10
- 描述: 10周期成交量移动平均
- 依赖: tradedVol
- 参数: {'window': 10}

### vol_std_10
- 描述: 10周期成交量标准差
- 依赖: tradedVol
- 参数: {'window': 10}

### imbalance_ma_10
- 描述: 10周期一档不平衡度移动平均
- 依赖: BidVol1, AskVol1
- 参数: {'window': 10}

### mid_return_20
- 描述: 20周期中间价收益率
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 20}

### mid_ma_20
- 描述: 20周期中间价移动平均
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 20}

### mid_std_20
- 描述: 20周期中间价标准差
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 20}

### vol_ma_20
- 描述: 20周期成交量移动平均
- 依赖: tradedVol
- 参数: {'window': 20}

### vol_std_20
- 描述: 20周期成交量标准差
- 依赖: tradedVol
- 参数: {'window': 20}

### imbalance_ma_20
- 描述: 20周期一档不平衡度移动平均
- 依赖: BidVol1, AskVol1
- 参数: {'window': 20}

## advanced 类别因子

### OFI
- 描述: 订单流不平衡指标
- 依赖: BidPrice1, AskPrice1, BidVol1, AskVol1

### bid_vol_change
- 描述: 买一量的变化
- 依赖: BidVol1

### ask_vol_change
- 描述: 卖一量的变化
- 依赖: AskVol1

### OFI_ma_5
- 描述: 订单流不平衡的5周期移动平均
- 依赖: BidPrice1, AskPrice1, BidVol1, AskVol1
- 参数: {'window': 5}

### volume_intensity
- 描述: 交易量与价差的比率，反映交易强度
- 依赖: tradedVol, AskPrice1, BidPrice1

### volatility_5
- 描述: 中间价5周期收益率的滚动标准差
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 5}

### volatility_10
- 描述: 中间价10周期收益率的滚动标准差
- 依赖: AskPrice1, BidPrice1
- 参数: {'window': 10}

## diy 类别因子

### im5
- 描述: 5档买卖失衡指标，考虑价格和成交量的加权
- 依赖: BidVol1, BidVol2, BidVol3, BidVol4, BidVol5, AskVol1, AskVol2, AskVol3, AskVol4, AskVol5, BidPrice1, BidPrice2, BidPrice3, BidPrice4, BidPrice5, AskPrice1, AskPrice2, AskPrice3, AskPrice4, AskPrice5, mid

### im5vol
- 描述: 5档成交量失衡指标，考虑价格距离的加权
- 依赖: BidVol1, BidVol2, BidVol3, BidVol4, BidVol5, AskVol1, AskVol2, AskVol3, AskVol4, AskVol5, BidPrice1, BidPrice2, BidPrice3, BidPrice4, BidPrice5, AskPrice1, AskPrice2, AskPrice3, AskPrice4, AskPrice5, mid

### mid_minnum
- 描述: 基于最小成交量的中间价格偏离
- 依赖: BidVol1, BidVol2, BidVol3, BidVol4, BidVol5, AskVol1, AskVol2, AskVol3, AskVol4, AskVol5, BidPrice1, BidPrice2, BidPrice3, BidPrice4, BidPrice5, AskPrice1, AskPrice2, AskPrice3, AskPrice4, AskPrice5, mid
- 参数: {'minvol': 1}

### turnover_mid
- 描述: 成交均价与中间价格的偏离
- 依赖: avg_prc, mid

### voi
- 描述: 订单失衡量指标，参考<Order imbalance Based Strategy in High Frequency Trading>
- 依赖: BidVol1, BidVol2, BidVol3, BidVol4, BidVol5, AskVol1, AskVol2, AskVol3, AskVol4, AskVol5, BidPrice1, BidPrice2, BidPrice3, BidPrice4, BidPrice5, AskPrice1, AskPrice2, AskPrice3, AskPrice4, AskPrice5
- 参数: {'level': 5}

## interaction 类别因子
