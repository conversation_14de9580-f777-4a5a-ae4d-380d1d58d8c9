import os
import shutil
import sys

KEY = 'Sepro_'
dir='D:\\Desktop\\'

trade_dirs = {"SH500": r"",
              "SH300": r"",
              "SH50": r"",
              "SHkcb": r"",

              "SZ300500": r"",
              "SZ100": r"",
              "SZcyb": r"",

              "DCE": r"\\Sepro_COM\\",
              "CZCE": r"\\Sepro_COM\\",
              "SHFE_aucu": r"\\Sepro_COM\\",
              "SHFE_rbsc": r"\\Sepro_COM\\",
              "GFEX": r"\\Sepro_COM\\",
              }

cpdirs = r"C:\\Users\\<USER>\\Desktop\\guitemp"
for cp in os.listdir(cpdirs):
    print("\n\n" + cp)
    for k in trade_dirs:
        shutil.copy(os.path.join(cpdirs, cp), dir+trade_dirs[k] + KEY + k)
        print("Copied " + k)

sys.exit()
