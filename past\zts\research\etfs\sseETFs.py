# -*- coding:utf-8 -*-
import json
from functools import reduce

from bs4 import BeautifulSoup
import requests

import re


# print(urls)

def get_curls(curls, page, data=None):
    url = u"http://query.sse.com.cn//search/getSearchResult.do?search=qwjs&jsonCallBack=jQuery111206353291224001172_1555901531539&page={}&searchword=T_L+CTITLE+T_D+E_KEYWORDS+T_JT_E+T_L%E6%B5%81%E5%8A%A8%E6%80%A7%E6%9C%8D%E5%8A%A1T_R++and+cchannelcode+T_E+T_L0T_D8311T_D8318T_D8319T_D8522T_D8540T_D8546T_D8551T_D8638T_D8662T_D8670T_D8673T_D8687T_D8698T_D8862T_D8874T_D8875T_D8879T_D8883T_D88888888T_DT_RT_R&orderby=-CRELEASETIME&perpage=10&_=1555901531550".format(
        page)
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                             'Chrome/70.0.3538.67 Safari/537.36',
               'Referer': 'http://www.sse.com.cn/home/<USER>/?webswd=%E6%B5%81%E5%8A%A8%E6%80%A7'
               }

    web_data = requests.get(url, headers=headers)

    jd = json.loads(web_data.text.strip('jQuery111203646049895497858_1555767885071(')[:-1])

    for data in jd['data']:
        curls.append(data['CURL'])

    page = page + 1
    if int(jd["countPage"]) > page:
        get_curls(curls, page)


def get_text(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                      'Chrome/70.0.3538.67 Safari/537.36'}
    req = requests.get(url, headers=headers)
    # req.encoding = 'utf-8'
    if req.encoding == 'ISO-8859-1':
        encodings = requests.utils.get_encodings_from_content(req.text)
        if encodings:
            encoding = encodings[0]
        else:
            encoding = req.apparent_encoding

        # encode_content = req.content.decode(encoding, 'replace').encode('utf-8', 'replace')
        global encode_content
        encode_content = req.content.decode(encoding, 'replace')  # 如果设置为replace，则会用?取代非法字符；

    # 将获取到的内容转换成BeautifulSoup格式，并将html.parser作为解析器
    soup = BeautifulSoup(encode_content, 'html.parser')
    # soup.select('#sse_query_list > dl:nth-child(5) > dd > a')
    # 以格式化的形式打印html
    # print(soup.prettify())

    # 查找所有a标签中class='title'的语句
    titles2 = soup.find_all('div', attrs={'class': 'allZoom'})

    result = []
    for title in titles2:
        result.append(title.get_text())
        print(title.get_text().replace(u'\xa0', u' '))
    results.append(result)

    # open()是读写文件的函数,with语句会自动close()已打开文件
    # with open(r"articles.txt", "w") as file:  # 在磁盘以只写的方式打开/创建一个名为 articles 的txt文件
    #     for title in titles2:
    #         file.write(title.get_text() + '\n')
    #         # file.write(title.get('href') + '\n\n')
    #         print(title.name, title.get_text())


def lists_combination(lists, code=''):
    '''输入多个列表组成的列表, 输出其中每个列表所有元素可能的所有排列组合
    code用于分隔每个元素'''
    try:
        import reduce
    except:
        from functools import reduce

    def myfunc(list1, list2):
        return [str(i) + code + str(j) for i in list1 for j in list2]

    return reduce(myfunc, lists)


if __name__ == "__main__":
    curls = []
    pagebegin = 1
    get_curls(curls, pagebegin)

    results = []
    m = 0
    for curl in curls:
        get_text("http://www.sse.com.cn" + curl)
        m = m + 1
        print(m)
    print('get done')

    resultfind = []
    table = {ord(f): ord(t) for f, t in zip(
        u'，。！？【】（）％＃＠＆１２３４５６７８９０',
        u',.!?[]()%#@&1234567890')}
    for i in range(0, len(results) - 1):
        try:
            num = re.findall("[\))](.*)号.*", results[i][0].translate(table))
            if num == []: num = [000]
            date = re.findall(re.compile("\s+[二|2](.*)日"), results[i][0].translate(table))
            if date[0][0]=="〇":
                date[0] = u"二" + date[0] + u"日"
            else:
                date[0] = u"2" + date[0] + u"日"
            funds = re.findall(re.compile("(5\d{5})"), results[i][0].translate(table))
            fundscompany = re.findall(".*经协商(.*)[,].*", results[i][0].translate(table))
            if len(fundscompany) == 0:
                fundscompany = ["none"]
            company = re.findall(".*同意(.*)为.*", results[i][0].translate(table))
            name = re.findall(".*提供(.*)服务.*", results[i][0].translate(table))
            if len(name) == 0:
                name = re.findall(".*[\))](.*)服务.*", results[i][0].translate(table))
            if company[0][:2] == u"下列":
                company = re.findall("服务商\.\s*(.*)\s*特此", results[i][0].translate(table))
            company = re.split('\s+|、|和', company[0])
            while '' in company:
                company.remove('')
            # name2=re.findall(".*[\))](.*)服务商.*", results[i][0].translate(table))
            resultfind.append([num, date, funds, fundscompany, company, name])
        except:
            print(i, results[i])

    lis2 = list()
    for li in resultfind:
        if li not in lis2:
            lis2.append(li)

    # open()是读写文件的函数,with语句会自动close()已打开文件
    with open(r"sseETFmm.txt", "w") as file:  # 在磁盘以只写的方式打开/创建一个名为 articles 的txt文件
        for title in lis2:
            # fn = lambda x, code='\t': reduce(lambda x, y: [str(i) + code + str(j) for i in x for j in y], title)
            # 直接调用fn(lists, code)
            fn2 = lists_combination(title, code='\t')

            for ll in fn2:
                file.write(ll + '\n')
    print('done')
