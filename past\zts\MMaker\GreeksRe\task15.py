# -*- coding:utf-8 -*-
from __future__ import print_function

import logging
import os
import sys
from datetime import datetime

# reload(sys)
# sys.setdefaultencoding('utf-8')

# encode('gbk', 'ignore').decode('gbk') #.encode('gbk')

codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
sys.path.append(codepath)
from daily import pylog

logging.basicConfig(handlers=[logging.FileHandler('%s\\daily\\myapp.log' % codepath, 'a', 'utf-8')],
                    level=logging.DEBUG,
                    format='%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s',
                    datefmt='%Y-%m-%d %a %H:%M:%S'
                    )

now = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
pylog.writeFile(now)

command = 'taskkill /F /IM EXCEL.exe'

i = 0
i = i + 1
pylog.writeFile(u"\n\n             %s\n*\n*\n*(内网)做市监控数据提取\n*\n*" % i)
text = pylog.exeCmd('%s\\MMaker\\GreeksRe\\getsql.py' % codepath)
pylog.writeFile(text)
try:
    pylog.exeCmd(command)
except:
    print(0)
    pass

i = i + 1
pylog.writeFile(u"\n\n             %s\n*\n*\n*(外网)上交所报送+风控文件更新\n*\n*" % i)
text = pylog.exeCmd('%s\\MMaker\\GreeksRe\\MMreport.py' % codepath)
pylog.writeFile(text)
try:
    pylog.exeCmd(command)
except:
    print(0)
    pass
