# -*- coding:utf-8 -*-
from __future__ import print_function
import pyodbc
import pandas as pd
import numpy as np
from WindPy import w
from pandas import DataFrame
import datetime
import math
import xlwt
import time

w.start()

wsd_data = w.wsi("SR801.CZC", "close", "2017-7-17 09:00:00", "2017-11-20 15:00:59", "")

fm = pd.DataFrame(wsd_data.Data, index=wsd_data.Fields, columns=wsd_data.Times)
fm = fm.T  # 将矩阵转置
print(fm)


sqllist2 = []
for index, row in fm.iterrows():
    time2 = index
    print(time2)
    print(time2.time())
    #if time2.time().timetuple()<time.mktime(time.strptime("9:30:00", "%H:%M:%S")) or time2.time().timetuple()>time.strptime("15:00:00", "%H:%M:%S"):
    if int(time2.hour)<9 or (int(time2.hour)==9 and int(time2.minute<30)) or time2.hour>15:
        print("pass")
        continue
    code = "SR801.CZC"
    # code = row['TRADE_HISCODE']
    price = row['close']
    a = math.modf(price / 100)[0]
    if a == 0.5:
        price2 = int(price / 100) * 100
    else:
        price2 = round(price / 100) * 100

    code1 = code[:-4] + "C" + str(price2)[0:4] + code[-4:]
    code2 = code[:-4] + "P" + str(price2)[0:4] + code[-4:]
    # print code2
    # wdata2 = w.wss("%s,%s" % (code1, code2), "close", "tradeDate=%s;priceAdj=F;cycle=D" % time2)
    wdata2 = w.wsi("%s,%s" % (code1, code2), "close", time2, datetime.datetime.strptime(str(time2), "%Y-%m-%d %H:%M:%S") + datetime.timedelta(minutes=1), "")
    # print wdata2

    # print C,P
    try:
        C = wdata2.Data[2][0]
        P = wdata2.Data[2][1]
        S = C - P + price2
    except Exception as e:
        print('str(Exception):\t', str(Exception))
        print('str(e):\t\t', str(e))
        continue

    sqllist = []

    sqllist.append(time2)
    sqllist.append(code)
    sqllist.append(price)
    sqllist.append(price2)
    sqllist.append(C)
    sqllist.append(P)
    sqllist.append(S)
    try:
        sqllist.append(S- price)
    except Exception as e:
        print('str(Exception):\t', str(Exception))
        print('str(e):\t\t', str(e))
        print(price)
        sqllist.append('N')

    sqltuple = tuple(sqllist)

    sqllist2.append(sqltuple)

print(sqllist2)

f = xlwt.Workbook()  # 创建工作簿
sheet1 = f.add_sheet(u'opt_fut', cell_overwrite_ok=True)  # 创建sheet
for i, row in enumerate(sqllist2):
    for j, col in enumerate(row):
        sheet1.write(i, j, col)
f.save('SR801Min.xls')  # 保存文件
