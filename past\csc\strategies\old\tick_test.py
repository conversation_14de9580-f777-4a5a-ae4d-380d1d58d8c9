""""""
from datetime import datetime, time as dtime
from typing import Any
from vnpy.app.cta_strategy import (
    CtaTemplate,
    ArrayManager,
    TickData,
    BarData,
    OrderData,
    TradeData
)

from vnpy.mytools.utility import SecondBarGenerator


class TickStrategy(CtaTemplate):
    """"""

    author = ""

    # partameters

    # variables
    not_send_order = True

    parameters = []
    variables = [
        'not_send_order',
    ]

    def __init__(
        self,
        cta_engine: Any,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        self.sbg = SecondBarGenerator(self.on_bar)
        self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        self.am = ArrayManager(size=300)

        self.buy_vt_orderids = []
        self.short_vt_orderids = []

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_tick(0)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """
        if (
            (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 45))
            or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 10))
            or (dt.time() > dtime(10, 35) and dt.time() < dtime(11, 25))
            or (dt.time() > dtime(13, 35) and dt.time() < dtime(14, 55))
        ):
            self.trading = True
        else:
            self.trading = False

    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.sbg.update_tick(tick)

        

        if self.not_send_order and self.trading:
            self.buy(14735, 10)
            self.buy(14745, 10)
            self.buy(14750, 10)
            self.buy(14755, 10)
            self.buy(14760, 10)

            self.short(14770, 10)
            self.short(14765, 10)
            self.short(14760, 10)
            self.short(14755, 10)
            self.short(14750, 10)
            self.short(14745, 10)

            self.not_send_order = False

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.(second)
        """
        self.sbg5.update_bar(bar)

    def on_5s_bar(self, bar: BarData):
        """"""
        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        pass

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        pass
