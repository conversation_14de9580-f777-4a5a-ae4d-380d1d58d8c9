const HTML_CONTENT = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Tab</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2280%22>⭐</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        const darkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        document.documentElement.style.setProperty('background-color', darkMode ? '#121212' : '#ffffff');
        document.documentElement.style.setProperty('color', darkMode ? '#ffffff' : '#333');
    </script>
    <style>
        /* 全局过渡效果 */
        .card,
        .search-engine,
        .round-btn,
        .delete-categor-btn,
        .search-bar button,
        input,
        #dialog-box,
        .admin-controls button {
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;  
            background: none;  
            position: relative;
            color: #333;
            transition: color 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(px);
            display: flex;         
            flex-direction: column; 
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: url('https://bing.img.run/rand.php') center/cover no-repeat;
            filter: blur(10px);
            -webkit-filter: blur(10px);
        }
        
        /* 顶部区域样式 */
        .top-section {
            position: relative;
            width: 100
            padding: 10x;
            box-sizing: border-box;
        }
        
        /* 中心内容样式 */
        .center-content {
            width: 100%;
            max-width: 90%;
            text-align: center;
            margin: 0 auto;
            padding: 10px;
            box-sizing: border-box;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            max-width: 90%;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
            overflow-y: auto; /* 改为 auto */
        }
        
        /* 管理员控制面板样式 */
        .admin-controls {
            position: fixed;  /* 固定定位 */
            top: 20px;       /* 距离顶部20px */
            right: 20px;     /* 距离右侧20px */
            display: flex;
            gap: 8px;
            align-items: center;
            z-index: 1000;   /* 确保显示在最上层 */
        }
        
        /* 统一输入框样式 */
        .admin-controls input {
            width: 100px;  /* 增加宽度 */
            height: 32px;
            padding: 0 12px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            line-height: 1;      
            display: inline-block;  
            text-align: center;
            font-size: 14px;
        }
        
        .admin-controls button {
            min-width: 60px;  /* 增加最小宽度 */
            height: 32px;
            padding: 0 12px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            line-height: 1;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .admin-controls {
                top: 10px;
                right: 10px;
            }

            .admin-controls input {
                width: 80px;
                height: 28px;
                font-size: 12px;
            }

            .admin-controls button {
                min-width: 50px;
                height: 28px;
                font-size: 12px;
                padding: 0 8px;
            }
        }
        
        /* 添加/删控钮样式 */
        .add-remove-controls {
            display: none;
            flex-direction: column;
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            align-items: center;
            gap: 10px;
        }
        
        .round-btn {
            background-color: rgba(255, 255, 255, 0.3);
            color: #333;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 24px;
            line-height: 40px;
            cursor: pointer;
            margin: 5px 0;
        }
        
        .add-btn { order: 1; }
        .remove-btn { order: 2; }
        .category-btn { order: 3; }
        .remove-category-btn { order: 4; }
        
        /* 搜索栏样式 */
        .search-container {
            position: relative;
            max-width: 600px;
            margin: 100px auto 20px;  /* 从200px减少到100px */
        }
        
        .search-bar {
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
        }
        
        /* 搜索栏基础样式 */
        .search-bar input {
            width: 500px;  
            height: 32px;
            padding: 0 12px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            line-height: 1;      
            display: inline-block;  
            text-align: center;
            font-size: 20px;
        }
        
        .search-bar input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.4);
        }
        
        .search-bar button {
            padding: 12px 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-left: none;
            border-radius: 0 25px 25px 0;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: inherit;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-bar button:hover {
            background: rgba(255, 255, 255, 0.4);
        }
        
        /* 搜索引擎按钮样式 */
        .search-engines {
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .search-engine {
            height: 24px;  
            padding: 0 12px;  
            font-size: 12px;  
            line-height: 24px;  
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 25px;
            color: inherit;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-engine:hover,
        .search-engine.active {
            background: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
        }
        
        /* 主题切换按钮样式 */
        #theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.3);
            color: inherit;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;          /* 使用 flex 布局 */
            align-items: center;    /* 垂直居中 */
            justify-content: center; /* 水平居中 */
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            font-size: 20px;       /* 设置符号大小 */
            line-height: 1;
        }

        #theme-toggle::before {
            content: '';
            width: 24px;
            height: 24px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            transition: transform 0.3s ease;
        }

        /* 亮色模式图标 */
        #theme-toggle:not(.dark)::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71'%3E%3C/path%3E%3Cpath d='M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71'%3E%3C/path%3E%3C/svg%3E");
        }

        /* 对话框样式优化 */
        #dialog-box {
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            border-radius: 12px;
            padding: 20px;
            min-width: 300px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* 对话框按钮容器 */
        .dialog-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            justify-content: flex-end;
        }

        /* 对话框按钮统一样式 */
        #dialog-box button {
            padding: 8px 16px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            color: inherit;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        #dialog-box button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        /* 对话框输入框样式 */
        #dialog-box input,
        #dialog-box select {
            width: 100%;
            padding: 8px 12px;
            margin: 5px 0 15px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            color: inherit;
            transition: all 0.3s ease;
        }

        #dialog-box input:focus,
        #dialog-box select:focus {
            outline: none;
            border-color: var(--accent-color, #4a9eff);
            background: rgba(255, 255, 255, 0.3);
        }

        /* 私密链接复选框容器 */
        .private-link-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 0;
        }

        #private-checkbox {
            width: auto !important;
            margin: 0 !important;
        }

        /* 暗色模式适配 */
        @media (prefers-color-scheme: dark) {
            #dialog-box {
                background: rgba(30, 30, 30, 0.9);
                color: #fff;
            }

            #dialog-box button {
                background: rgba(60, 60, 60, 0.6);
                color: #fff;
            }

            #dialog-box button:hover {
                background: rgba(80, 80, 80, 0.8);
            }

            #dialog-box input,
            #dialog-box select {
                background: rgba(60, 60, 60, 0.6);
                color: #fff;
                border-color: rgba(255, 255, 255, 0.2);
            }

            #dialog-box input:focus,
            #dialog-box select:focus {
                background: rgba(80, 80, 80, 0.8);
            }

            #dialog-box label {
                color: #ddd;
            }
        }
        
        /* 分类和卡片样式 */
        .section {
            margin-bottom: 20px;
        }
        
        /* 分类标题区域 */
        .category-header {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 8px;
        }
        
        /* 分类标题容器 */
        .section-title-container {
            display: flex;
            align-items: center;
            gap: 8px;  /* 所有元素的间距 */
            flex: 1;   /* 占据剩余空间 */
        }
        
        /* 分类标题样式 */
        .section-title {
            margin: 0;
            font-size: 1.2em;
            font-weight: 500;
            text-shadow: var(--text-stroke); /* 使用描边效果 */
        }
        
        /* 分类按钮样式统一 */
        .edit-category-btn,
        .delete-category-btn,
        .drag-handle {
            height: 32px;
            padding: 0 12px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            line-height: 1;      
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .drag-handle {
            cursor: move;  /* 特殊的拖拽光标 */
            padding: 0 8px;  /* 稍微窄一点 */
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .category-header {
                padding: 8px 12px;
            }

            .edit-category-btn,
            .delete-category-btn,
            .drag-handle {
                height: 28px;
                padding: 0 10px;
                font-size: 14px;
            }

            .drag-handle {
                padding: 0 6px;
            }
        }
        
        /* 卡片容器布局 */
        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        /* 卡片基础样式 */
        .card {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 12px;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.1);
            width: 100%;
            box-sizing: border-box;
            min-width: 0;  /* 确保 flex 子项可以正确缩小 */
            transition: all 0.3s ease; /* 添加过渡效果 */
        }
        
        /* 卡片悬停效果 */
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            background-color: rgba(255, 255, 255, 0.25); /* 略微增加透明度 */
        }
        
        /* 卡片标题悬停效果 */
        .card:hover .card-title {
            color: var(--accent-color, #4a9eff);
        }
        
        /* 卡片URL悬停效果 */
        .card:hover .card-url {
            opacity: 1;
        }
        
        /* 修改卡片图标悬停效果 */
        .card:hover .card-icon {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }
        
        /* 私密标签悬停效果 */
        .card:hover .private-tag {
            background-color: #ff7043;
            transform: scale(1.05);
            transition: all 0.3s ease;
        }
        
        /* 编辑按钮悬停效果 */
        .card:hover .edit-btn {
            opacity: 1;
            transform: scale(1);
        }
        
        /* 删除按钮悬停效果 */
        .card:hover .delete-btn {
            opacity: 1;
            transform: scale(1);
        }
        
        /* 在深色模式下的悬停效果 */
        @media (prefers-color-scheme: dark) {
            .card:hover {
                background-color: rgba(40, 40, 40, 0.8);
            }
        }
        
        /* 卡片标题容器 */
        .card-top {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 5px;
            min-width: 0;  /* 允许子元素缩小到比最小内容宽度更小 */
        }
        
        /* 卡片图标 */
        .card-icon {
            flex-shrink: 0;
            width: 16px;
            height: 16px;
        }
        
        /* 卡片标题 */
        .card-title {
            margin: 0;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
        }
        
        .card-url {
            font-size: 12px;
            opacity: 0.8;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .private-tag {
            background-color: #ff9800;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 3px;
            position: absolute;
            top: 5px;
            right: 5px;
        }
        
        .delete-btn {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: red;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: center;
            font-size: 14px;
            line-height: 20px;
            cursor: pointer;
            display: none;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .fixed-elements {
                position: relative;
                padding: 5px;
            }
            .content {
                margin-top: 10px;
            }

            .admin-controls input,
            .admin-controls button {
                height: 22px;  
                font-size: 11px;
            }

            .admin-controls input {
                width: 60px;
            }
            
            .admin-controls button {
                min-width: 40px;
            }

            .card-container {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 5px;
                padding: 5px;
            }
            
            .card {
                padding: 10px;
                font-size: 0.9em;
            }
            
            .add-remove-controls {
                right: 2px;
              }

            .round-btn, 
            #theme-toggle {
                right: 5px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 30px;
                height: 30px;
                font-size: 24px;
            }

            .search-container {
                margin-top: 75px;  /* 从150px减少到75px */
            }

            .search-bar input {
                height: 40px;  /* 原来是 20px，现在翻倍 */
                font-size: 16px;  /* 调整字体大小使其更协调 */
                border-radius: 20px;  /* 保持圆角与高度的比例 */
            }
            
            .search-engine {
                height: 20px
            }
        }
        
        /* 为标题和按钮添加特殊字体 */
        .section-title,
        .card-title,
        button,
        .search-engine {
            font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            font-weight: 500;
        }
        
        /* 为搜索框添加字体 */
        input,
        select {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        
        /* 统一按钮样式简化 */
        .search-engine, 
        .round-btn,
        #theme-toggle,
        .delete-category-btn,
        .edit-category-btn,
        .search-bar button,
        .admin-controls button {
            height: 32px;
            padding: 0 12px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            line-height: 1;      
            display: flex;  
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: inherit;
        }
        
        /* 按钮悬停效果简化 */
        .search-engine:hover, 
        .round-btn:hover,
        #theme-toggle:hover,
        .delete-category-btn:hover,
        .edit-category-btn:hover,
        .search-bar button:hover,
        .admin-controls button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 圆形按钮式简化 */
        .round-btn,
        #theme-toggle {
            width: 32px;
            border-radius: 50%;
        }
        
        /* 移动端样式调整 */
        @media (max-width: 480px) {
            .search-engine, 
            .round-btn,
            #theme-toggle,
            .delete-category-btn,
            .edit-category-btn,
            .search-bar button,
            .admin-controls button {
                height: 28px;
                font-size: 14px;
            }

            .round-btn,
            #theme-toggle {
                width: 28px;
                right: 15px;
                bottom: 15px;
            }
        }
        
        /* 拖拽效果 */
        .card.dragging {
            opacity: 0.5;
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        /* 分组和折叠样式 */
        .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .category-header h2 {
            margin: 0;
            font-size: 1.2em;
            font-weight: 500;
        }

        /* 分类按钮组样式 */
        .category-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* 分类按钮通用样式 */
        .category-buttons button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.2);
            color: inherit;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .category-buttons button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        /* 折叠按钮特殊样式 */
        .category-fold-btn {
            font-size: 14px !important;
        }

        /* 折叠状态样式 */
        .card-container.folded {
            display: none;
        }

        /* 响应式设 */
        @media (max-width: 480px) {
            .category-header {
                padding: 8px 12px;
            }

            .category-buttons button {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }

            .category-header h2 {
                font-size: 1.1em;
            }
        }

        /* 搜索框样式优化 */
        .search-container {
            position: relative;
            max-width: 600px;
            margin: 100px auto 20px;  /* 从200px减少到100px */
        }

        .search-input {
            width: 100%;
            padding: 12px 20px;
            padding-left: 40px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: var(--text-color);
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-color);
            opacity: 0.7;
        }

        /* 主题切换钮样式 */
        .theme-switcher {
            position: fixed;
            right: 20px;
            bottom: 20px;
            padding: 10px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .theme-switcher:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.3);
        }

        .edit-btn {
            position: absolute;
            top: 5px;
            right: 25px;
            background-color: rgba(255, 255, 255, 0.3);
            color: inherit;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: center;
            font-size: 12px;
            line-height: 20px;
            cursor: pointer;
            display: none;
            z-index: 1;
        }

        .card:hover .edit-btn {
            display: block;
        }

        .edit-btn:hover {
            background-color: rgba(255, 255, 255, 0.5);
        }

        #dialog-box h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: center;
        }

        /* 右侧圆形按钮通用样式 */
        .floating-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: inherit;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-btn:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        /* 右侧按钮容器 */
        .side-controls {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        }

        /* 添加/删除控 */

        /* 对话框遮罩层样式 */
        #dialog-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #dialog-overlay.active {
            opacity: 1;
        }

        /* 对话框基础���式 */
        #dialog-box {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 24px;
            width: 90%;
            max-width: 400px;
            position: relative;
            transform: translateY(20px);
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        #dialog-box.active {
            transform: translateY(0);
            opacity: 1;
        }

        /* 对话框表单容器 */
        .dialog-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
            width: 100%;
        }

        /* 标题样式 */
        #dialog-box h3 {
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 500;
            color: inherit;
        }

        /* 输入框和选择框通用样式 */
        .dialog-form input[type="text"],
        .dialog-form select {
            width: 100%;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            box-sizing: border-box;
        }

        /* 标签样式 */
        .dialog-form label {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            color: inherit;
        }

        /* 私密链接容器样式 */
        .private-link-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 5px 0;
        }

        .private-link-container input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        /* 按钮容器样式 */
        .dialog-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        /* 按钮样式 */
        .dialog-buttons button {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            background: rgba(0, 0, 0, 0.1);
            color: inherit;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        /* 删除按钮特殊样式 */
        .delete-link-btn {
            margin-right: auto;
            background-color: rgba(255, 59, 48, 0.1) !important;
            color: #ff3b30 !important;
        }

        /* 保存按钮特殊样式 */
        .save-btn {
            background-color: rgba(0, 122, 255, 0.1) !important;
            color: #007aff !important;
        }

        /* 暗色模式适配 */
        @media (prefers-color-scheme: dark) {
            #dialog-box {
                background: rgba(30, 30, 30, 0.95);
            }

            .dialog-form input[type="text"],
            .dialog-form select {
                background: rgba(60, 60, 60, 0.9);
                border-color: rgba(255, 255, 255, 0.1);
                color: #fff;
            }

            .dialog-buttons button {
                background: rgba(255, 255, 255, 0.1);
            }

            .delete-link-btn {
                background-color: rgba(255, 69, 58, 0.2) !important;
                color: #ff453a !important;
            }

            .save-btn {
                background-color: rgba(10, 132, 255, 0.2) !important;
                color: #0a84ff !important;
            }
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            #dialog-box {
                width: 100%;
                max-width: none;
                border-radius: 20px 20px 0 0;
                padding: 20px;
            }

            .dialog-form {
                gap: 12px;
            }

            .dialog-buttons button {
                flex: 1;
                padding: 12px 16px;
                font-size: 16px;
            }
        }

        /* 管理员模式下的卡片样式 */
        .card.admin-mode {
            cursor: pointer;
            position: relative;
        }

        .card.admin-mode:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .card.admin-mode:hover::after {
            content: '编辑';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 编辑对话框动画 */
        #dialog-overlay {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        #dialog-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        #dialog-box {
            transform: translateY(20px);
            opacity: 0;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        #dialog-box.active {
            transform: translateY(0);
            opacity: 1;
        }
    </style>
</head>

<body>
    <!-- 管理员控制面板移到body的最前面 -->
    <div class="admin-controls">
        <input type="password" id="admin-password" placeholder="密码">
        <button id="admin-mode-btn" onclick="toggleAdminMode()">设置</button>
        <button id="secret-garden-btn" onclick="toggleSecretGarden()">登录</button>
    </div>
    
    <div class="top-section">
        <div class="center-content">
            <!-- 搜索相关代码 -->
            <div class="search-container">
                <div class="search-bar">
                    <input type="text" id="search-input" placeholder="">
                </div>
                <div class="search-engines">
                    <button class="search-engine" data-engine="baidu">百度</button>
                    <button class="search-engine" data-engine="bing">必应</button>
                    <button class="search-engine" data-engine="google">谷歌</button>
                </div>
            </div>
        </div>
    </div>
    <div class="content">
        <!-- 添加/删除控制按钮 -->
        <div class="add-remove-controls">
            <button class="round-btn floating-btn add-btn" onclick="showAddDialog()">+</button>
            <button class="round-btn floating-btn remove-btn" onclick="toggleRemoveMode()">-</button>
            <button class="round-btn floating-btn category-btn" onclick="addCategory()">C+</button>
            <button class="round-btn floating-btn remove-category-btn" onclick="toggleRemoveCategory()">C-</button>

        </div>

        <!-- 分类和卡片容器 -->
        <div id="sections-container"></div>
        <!-- 主题切换按钮 -->
        <button id="theme-toggle" class="floating-btn" onclick="toggleTheme()">🌙</button>
        <!-- 添加链接对话框 -->
        <div id="dialog-overlay">
            <div id="dialog-box">
                <div class="dialog-form">
                    <label for="name-input">名称</label>
                    <input type="text" id="name-input">
                    
                    <label for="url-input">地址</label>
                    <input type="text" id="url-input">
                    
                    <label for="category-select">选择分类</label>
                    <select id="category-select"></select>
                    
                    <div class="private-link-container">
                        <label for="private-checkbox">私密链接</label>    
                        <input type="checkbox" id="private-checkbox">
                    </div>
                    
                    <div class="dialog-buttons">
                        <button class="delete-link-btn">删除</button>
                        <button class="cancel-btn">取消</button>
                        <button class="save-btn">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    // 搜索引置
    const searchEngines = {
        baidu: "https://www.baidu.com/s?wd=",
        bing: "https://www.bing.com/search?q=",
        google: "https://www.google.com/search?q="
    };
    
    let currentEngine = "baidu";
    
    // 日记录函数
    function logAction(action, details) {
        const timestamp = new Date().toISOString();
        const logEntry = timestamp + ': ' + action + ' - ' + JSON.stringify(details);
        console.log(logEntry); 
    }
    
    // 设置当前搜索引擎
    function setActiveEngine(engine) {
        currentEngine = engine;
        document.querySelectorAll('.search-engine').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.engine === engine) {
                btn.classList.add('active');
            }
        });
        logAction('设置搜索引擎', { engine });
    }
    
    // 搜引擎按钮点件
    document.querySelectorAll('.search-engine').forEach(button => {
        button.addEventListener('click', () => setActiveEngine(button.dataset.engine));
    });
    
    // 搜索输入框回车件
    document.getElementById('search-input').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const query = e.target.value;
            if (query) {
                logAction('执行搜索', { engine: currentEngine, query });
                window.open(searchEngines[currentEngine] + encodeURIComponent(query), '_blank');
            }
        }
    });
    
    // 初始化搜索引擎
    setActiveEngine(currentEngine);
    
    // 全局变量
    let publicLinks = [];
    let privateLinks = [];
    let isAdmin = false;
    let isLoggedIn = false;
    let removeMode = false;
    let isRemoveCategoryMode = false;
    let isDarkTheme = false;
    let links = [];
    const categories = {};
    let categoryOrder = []; // ���增：存储分���顺序
    
    // 添加新分类
    async function addCategory() {
        if (!await validateToken()) {
            return; 
        }
        const categoryName = prompt('请输入分类名称:');
        if (categoryName && !categories[categoryName]) {
            categories[categoryName] = [];
            categoryOrder.push(categoryName); // 添加到分类顺序
            
            // 保存更改
            await saveLinks();
            
            // 重新加载所有内容
            await loadLinks();
            
            logAction('添加分类', { categoryName, currentLinkCount: links.length });
        } else if (categories[categoryName]) {
            alert('该分类已存在');
            logAction('添加分类失败', { categoryName, reason: '分类已存在' });
        }
    }

    // 渲染分类(不重新加载链接)
    function renderCategories() {
        const container = document.getElementById('sections-container');
        container.innerHTML = '';

        (categoryOrder.length > 0 ? categoryOrder : Object.keys(categories)).forEach(category => {
            const section = document.createElement('div');
            section.className = 'section';

            // 创建分类标题容器
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'category-header';
            if (foldStates[category]) {
                categoryHeader.classList.add('folded');
            }

            // 创建左侧标题
            const titleText = document.createElement('h2');
            titleText.textContent = category;
            categoryHeader.appendChild(titleText);

            // 创建右侧按钮组
            const buttonGroup = document.createElement('div');
            buttonGroup.className = 'category-buttons';

            // 如果是管理员模式，添加编辑和删除按钮
            if (isAdmin) {
                const editBtn = document.createElement('button');
                editBtn.className = 'category-edit-btn';
                editBtn.innerHTML = '✎';
                editBtn.onclick = (e) => {
                    e.stopPropagation();
                    editCategory(category);
                };
                buttonGroup.appendChild(editBtn);

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'category-delete-btn';
                deleteBtn.textContent = '删除分类';
                deleteBtn.style.display = isRemoveCategoryMode ? 'inline-block' : 'none';
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    deleteCategory(category);
                };
                buttonGroup.appendChild(deleteBtn);
            }

            categoryHeader.appendChild(buttonGroup);

            // 创建卡片容器
            const cardContainer = document.createElement('div');
            cardContainer.className = 'card-container';
            cardContainer.id = category;
            if (foldStates[category]) {
                cardContainer.classList.add('folded');
            }

            // 添加折叠/展开功能
            categoryHeader.addEventListener('click', () => {
                const isFolded = categoryHeader.classList.toggle('folded');
                cardContainer.classList.toggle('folded');
                
                // 保存折叠状态
                const states = JSON.parse(localStorage.getItem('categoryFoldStates') || '{}');
                states[category] = isFolded;
                localStorage.setItem('categoryFoldStates', JSON.stringify(states));
            });

            section.appendChild(categoryHeader);
            section.appendChild(cardContainer);
            container.appendChild(section);

            // 渲染卡片
            const categoryLinks = links.filter(link => link.category === category);
            categoryLinks.forEach(link => {
                createCard(link, cardContainer);
            });
        });
    }

    // 添加一个函数重置所有分类的折叠状态
    function resetAllFoldStates() {
        localStorage.removeItem('categoryFoldStates');
        renderCategories();
    }

    // 添加一个函数来折叠/展开所有分类
    function toggleAllCategories(fold = true) {
        const states = {};
        categoryOrder.forEach(category => {
            states[category] = fold;
        });
        localStorage.setItem('categoryFoldStates', JSON.stringify(states));
        renderCategories();
    }
    
    // 编辑分类名称
    async function editCategory(oldName) {
        if (!await validateToken()) {
            return; 
        }
        const newName = prompt('请输入新的分类名称:', oldName);
        if (newName && newName !== oldName) {
            if (categories[newName]) {
                alert('该分类名称已存在');
                return;
            }

            // 更新categories对象
            categories[newName] = categories[oldName];
            delete categories[oldName];

            // 更新categoryOrder数组
            const orderIndex = categoryOrder.indexOf(oldName);
            if (orderIndex !== -1) {
                categoryOrder[orderIndex] = newName;
            }

            // 更新所有相关链接的分类
            links.forEach(link => {
                if (link.category === oldName) {
                    link.category = newName;
                }
            });

            // 同时更新publicLinks和privateLinks中的分类
            publicLinks.forEach(link => {
                if (link.category === oldName) {
                    link.category = newName;
                }
            });

            privateLinks.forEach(link => {
                if (link.category === oldName) {
                    link.category = newName;
                }
            });

            try {
                // 保存更改
                await saveLinks();
                
                // 重新加载所有内容
                await loadLinks();
                
                logAction('编辑分类名称', { oldName, newName });
            } catch (error) {
                console.error('保存分类更改失败:', error);
                alert('保存败，请重试');
            }
        }
    }
    
    // 分类拖拽相关函数
    let draggedCategory = null;

    function handleCategoryDragStart(e) {
        draggedCategory = e.target;
        e.target.classList.add('dragging');
        // 存储原始分类
        e.dataTransfer.setData('text/plain', e.target.getAttribute('data-category'));
    }

    function handleCategoryDragEnd(e) {
        if (!draggedCategory) return;
        draggedCategory.classList.remove('dragging');
        draggedCategory = null;
    }

    // 为document添加拖拽相关事件
    document.addEventListener('dragover', function(e) {
        e.preventDefault();
        if (!draggedCategory) return;

        const editBtn = e.target.closest('.edit-category-btn');
        if (!editBtn || editBtn === draggedCategory) return;

        const container = document.getElementById('sections-container');
        const draggedSection = draggedCategory.closest('.section');
        const targetSection = editBtn.closest('.section');
        
        const rect = editBtn.getBoundingClientRect();
        const midY = rect.top + rect.height / 2;
        
        if (e.clientY < midY) {
            container.insertBefore(draggedSection, targetSection);
        } else {
            container.insertBefore(draggedSection, targetSection.nextSibling);
        }
    });

    document.addEventListener('drop', async function(e) {
        e.preventDefault();
        if (!draggedCategory) return;

        // 更新分类顺序
        const sections = document.querySelectorAll('.section');
        categoryOrder = Array.from(sections).map(section => 
            section.querySelector('.section-title').textContent
        );
        
        // 保存新的顺序
        if (await validateToken()) {
            saveLinks();
            logAction('更新分类顺序');
        }
    });
    
    // 读取链接数
    async function loadLinks() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 如果登录，从 localStorage 取 token 并添加到请求头
        if (isLoggedIn) {
            const token = localStorage.getItem('authToken');
            if (token) {
                headers['Authorization'] = token; 
            }
        }
        
        try {
            const response = await fetch('/api/getLinks?userId=testUser', {
                headers: headers
            });
            
            if (!response.ok) {
                throw new Error("HTTP error! status: " + response.status);
            }
            
            
            const data = await response.json();
            console.log('Received data:', data); 
            
            if (data.categories) {
                Object.assign(categories, data.categories);
            }
            
            publicLinks = data.links ? data.links.filter(link => !link.isPrivate) : [];
            privateLinks = data.links ? data.links.filter(link => link.isPrivate) : [];
            links = isLoggedIn ? [...publicLinks, ...privateLinks] : publicLinks;

            loadSections();
            updateCategorySelect();
            updateUIState();

            // 在加载完链接后立即应用当前主题
            applyCurrentTheme();

            logAction('读取链接', { 
                publicCount: publicLinks.length, 
                privateCount: privateLinks.length,
                isLoggedIn: isLoggedIn,
                hasToken: !!localStorage.getItem('authToken')
            });
        } catch (error) {
            console.error('Error loading links:', error);
        }
    }
    
    
    // 更新UI状态
    function updateUIState() {
        const passwordInput = document.getElementById('admin-password');
        const adminBtn = document.getElementById('admin-mode-btn');
        const secretGardenBtn = document.getElementById('secret-garden-btn');
        const addRemoveControls = document.querySelector('.add-remove-controls');
    
        passwordInput.style.display = isLoggedIn ? 'none' : 'inline-block';
        secretGardenBtn.textContent = isLoggedIn ? "退出" : "登录";
        secretGardenBtn.style.display = 'inline-block';
    
        if (isAdmin) {
            adminBtn.textContent = "离开设置";
            adminBtn.style.display = 'inline-block';
            addRemoveControls.style.display = 'flex';
        } else if (isLoggedIn) {
            adminBtn.textContent = "设置";
            adminBtn.style.display = 'inline-block';
            addRemoveControls.style.display = 'none';
        } else {
            adminBtn.style.display = 'none';
            addRemoveControls.style.display = 'none';
        }
    
        logAction('更新UI状态', { isAdmin, isLoggedIn });
    }
    
    // 录状态显示加载所有链接）
    function showSecretGarden() {
        if (isLoggedIn) {
            links = [...publicLinks, ...privateLinks];
            loadSections();
            // 显示所有私密标签
            document.querySelectorAll('.private-tag').forEach(tag => {
                tag.style.display = 'block';
            });
            logAction('显示私密花园');
        }
    }
    
    // 加载分和链接
    function loadSections() {
        const container = document.getElementById('sections-container');
        container.innerHTML = '';
    
        Object.keys(categories).forEach(category => {
            const section = document.createElement('div');
            section.className = 'section';
    
            const titleContainer = document.createElement('div');
            titleContainer.className = 'section-title-container';
    
            const title = document.createElement('div');
            title.className = 'section-title';
            title.textContent = category;
    
            titleContainer.appendChild(title);
    
            if (isAdmin) {
                const editBtn = document.createElement('button');
                editBtn.innerHTML = '✎'; // 编辑图标
                editBtn.className = 'edit-category-btn';
                editBtn.setAttribute('draggable', 'true');
                editBtn.setAttribute('data-category', category);
                editBtn.addEventListener('dragstart', handleCategoryDragStart);
                editBtn.addEventListener('dragend', handleCategoryDragEnd);
                editBtn.addEventListener('click', (e) => {
                    // 防止拖拽时触发编辑
                    if (!draggedCategory) {
                        editCategory(category);
                    }
                });
                titleContainer.appendChild(editBtn);

                const deleteBtn = document.createElement('button');
                deleteBtn.textContent = '删除分类';
                deleteBtn.className = 'delete-category-btn';
                deleteBtn.style.display = 'none'; 
                deleteBtn.onclick = () => deleteCategory(category);
                titleContainer.appendChild(deleteBtn);
            }
    
            const cardContainer = document.createElement('div');
            cardContainer.className = 'card-container';
            cardContainer.id = category;
    
            section.appendChild(titleContainer);
            section.appendChild(cardContainer);
    
            container.appendChild(section);
    
            let privateCount = 0;
            let linkCount = 0;
    
            links.forEach(link => {
                if (link.category === category) {
                    if (link.isPrivate) privateCount++;
                    linkCount++;
                    createCard(link, cardContainer);
                }
            });
    
            if (privateCount < linkCount || isLoggedIn) {
                container.appendChild(section);
            }
        });
    
        logAction('加载类和链接', { isAdmin: isAdmin, linkCount: links.length, categoryCount: Object.keys(categories).length });
    }
    
    // 创建卡片
    function createCard(link, container) {
        const card = document.createElement('div');
        card.className = 'card';
        card.setAttribute('draggable', isAdmin);
        card.dataset.isPrivate = link.isPrivate;
    
        const cardTop = document.createElement('div');
        cardTop.className = 'card-top';
    
        // 定义默认的 SVG 图标
        const defaultIconSVG = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">' +
        '<path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>' +
        '<path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>' +
        '</svg>';
        
        // 创建图标元素
        const icon = document.createElement('img');
        icon.className = 'card-icon';
        // icon.src = 'https://api.iowen.cn/favicon/' + extractDomain(link.url) + '.png';
        icon.src = 'https://www.faviconextractor.com/favicon/' + extractDomain(link.url);
        icon.alt = 'Website Icon';
        
        // 如果图片加载失败，使用默认的 SVG 图标
        icon.onerror = function() {
            const svgBlob = new Blob([defaultIconSVG], {type: 'image/svg+xml'});
            const svgUrl = URL.createObjectURL(svgBlob);
            this.src = svgUrl;
            
            this.onload = () => URL.revokeObjectURL(svgUrl);
        };
        
        function extractDomain(url) {
            let domain;
            try {
                domain = new URL(url).hostname;
            } catch (e) {
                domain = url;
            }
            return domain;
        }
    
        const title = document.createElement('div');
        title.className = 'card-title';
        title.textContent = link.name;
    
        cardTop.appendChild(icon);
        cardTop.appendChild(title);
    
        const url = document.createElement('div');
        url.className = 'card-url';
        url.textContent = link.url;
    
        card.appendChild(cardTop);
        card.appendChild(url);
    
        if (link.isPrivate) {
            const privateTag = document.createElement('div');
            privateTag.className = 'private-tag';
            privateTag.textContent = '私密';
            card.appendChild(privateTag);
        }
    
        const correctedUrl = link.url.startsWith('http://') || link.url.startsWith('https://') ? link.url : 'http://' + link.url;
    
        // 修改卡片点击事件
        card.onclick = (event) => {
            event.stopPropagation();
            
            if (isAdmin) {
                // 在管理员模式下，显示编辑对话框
                const dialogOverlay = document.getElementById('dialog-overlay');
                const dialogBox = document.getElementById('dialog-box');
                
                // 重置表单内容
                document.getElementById('name-input').value = link.name;
                document.getElementById('url-input').value = link.url;
                document.getElementById('private-checkbox').checked = link.isPrivate;
                
                // 更新分类选择器
                const categorySelect = document.getElementById('category-select');
                categorySelect.innerHTML = '';
                Object.keys(categories).forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    if (category === link.category) {
                        option.selected = true;
                    }
                    categorySelect.appendChild(option);
                });
                
                // 设置按钮事件
                const buttons = dialogBox.querySelectorAll('button');
                const saveBtn = buttons[0];
                const cancelBtn = buttons[1];
                
                saveBtn.onclick = () => {
                    const updatedLink = {
                        name: document.getElementById('name-input').value,
                        url: document.getElementById('url-input').value,
                        category: document.getElementById('category-select').value,
                        isPrivate: document.getElementById('private-checkbox').checked
                    };
                    updateLink(updatedLink);
                };
                
                cancelBtn.onclick = hideAddDialog;
                
                // ���示对话框
                dialogOverlay.style.display = 'flex';
                setTimeout(() => {
                    dialogOverlay.classList.add('active');
                    dialogBox.classList.add('active');
                }, 10);
            } else {
                // 非管理员模式下，直接打开链接
                const correctedUrl = link.url.startsWith('http://') || link.url.startsWith('https://')
                    ? link.url 
                    : 'http://' + link.url;
                window.open(correctedUrl, '_blank');
                logAction('打开链接', { name: link.name, url: correctedUrl });
            }
        };
    
        // 如果是管理员模式，添加拖拽相关事件
        if (isAdmin) {
            card.addEventListener('dragstart', (e) => {
                dragStart(e);
                e.stopPropagation();
            });
            card.addEventListener('dragend', (e) => {
                dragEnd(e);
                e.stopPropagation();
            });
            card.addEventListener('dragover', (e) => {
                dragOver(e);
                e.stopPropagation();
            });
            card.addEventListener('drop', (e) => {
                drop(e);
                e.stopPropagation();
            });
        }
    
        const deleteBtn = document.createElement('button');
        deleteBtn.textContent = '–';
        deleteBtn.className = 'delete-btn';
        deleteBtn.onclick = function (event) {
            event.stopPropagation();
            removeCard(card);
        };
        card.appendChild(deleteBtn);
    
        updateCardStyle(card);
    
        card.addEventListener('dragstart', dragStart);
        card.addEventListener('dragover', dragOver);
        card.addEventListener('dragend', dragEnd);
        card.addEventListener('drop', drop);
        card.addEventListener('touchstart', touchStart, { passive: false });
    
        if (isAdmin && removeMode) {
            deleteBtn.style.display = 'block';
        }
    
        if (isAdmin || (link.isPrivate && isLoggedIn) || !link.isPrivate) {
            container.appendChild(card);
        }
        // logAction('创建卡片', { name: link.name, isPrivate: link.isPrivate });
    }
    
    // 更新卡片样式
    function updateCardStyle(card) {
        if (isDarkTheme) {
            card.style.backgroundColor = 'rgba(30, 30, 30, 0.6)';
            card.style.color = '#ffffff';
            card.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
        } else {
            card.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
            card.style.color = '#333';
            card.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
        }
    }
    
    // 更新类选择下拉框
    function updateCategorySelect() {
        const categorySelect = document.getElementById('category-select');
        categorySelect.innerHTML = '';
    
        Object.keys(categories).forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categorySelect.appendChild(option);
        });
    
        logAction('更新分类选择', { categoryCount: Object.keys(categories).length });
    }
    
    // 保存链接数据
    async function saveLinks() {
        if (isAdmin && !(await validateToken())) {
            return; 
        }

        let allLinks = [...publicLinks, ...privateLinks];
    
        try {
            await fetch('/api/saveOrder', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': localStorage.getItem('authToken')
                },
                body: JSON.stringify({ 
                    userId: 'testUser', 
                    links: allLinks,
                    categories: categories,
                    categoryOrder: categoryOrder // 增：保存分类顺序
                }),
            });
            logAction('保存链接和分类顺序', { linkCount: allLinks.length, categoryCount: Object.keys(categories).length });
        } catch (error) {
            logAction('保存失败', { error: error.message });
            alert('保存失败请重试');
        }
    }
    
    // 删除卡片
    async function removeCard(card) {
        if (!await validateToken()) {
            return; 
        }
        const name = card.querySelector('.card-title').textContent;
        const url = card.querySelector('.card-url').textContent;
        const isPrivate = card.dataset.isPrivate === 'true';
        
        links = links.filter(link => link.url !== url);
        if (isPrivate) {
            privateLinks = privateLinks.filter(link => link.url !== url);
        } else {
            publicLinks = publicLinks.filter(link => link.url !== url);
        }
    
        for (const key in categories) {
            categories[key] = categories[key].filter(link => link.url !== url);
        }
    
        card.remove();
    
        saveLinks();
    
        logAction('删除卡片', { name, url, isPrivate });
    }
    
    // 拖拽卡片
    let draggedCard = null;
    let touchStartX, touchStartY;
    
    // 触屏端拖拽卡片
    function touchStart(event) {
        if (!isAdmin) {
            return;
        }
        draggedCard = event.target.closest('.card');
        if (!draggedCard) return;
    
        event.preventDefault();
        const touch = event.touches[0];
        touchStartX = touch.clientX;
        touchStartY = touch.clientY;
    
        draggedCard.classList.add('dragging');
        
        document.addEventListener('touchmove', touchMove, { passive: false });
        document.addEventListener('touchend', touchEnd);
    
    }
    
    function touchMove(event) {
        if (!draggedCard) return;
        event.preventDefault();
    
        const touch = event.touches[0];
        const currentX = touch.clientX;
        const currentY = touch.clientY;

        const deltaX = currentX - touchStartX;
        const deltaY = currentY - touchStartY;
        draggedCard.style.transform = "translate(" + deltaX + "px, " + deltaY + "px)";
    
        const target = findCardUnderTouch(currentX, currentY);
        if (target && target !== draggedCard) {
            const container = target.parentElement;
            const targetRect = target.getBoundingClientRect();
    
            if (currentX < targetRect.left + targetRect.width / 2) {
                container.insertBefore(draggedCard, target);
            } else {
                container.insertBefore(draggedCard, target.nextSibling);
            }
        }
    }
    
    function touchEnd(event) {
        if (!draggedCard) return;
    
        const card = draggedCard;
        const targetCategory = card.closest('.card-container').id;
    
        validateToken().then(isValid => {
            if (isValid && card) {
                updateCardCategory(card, targetCategory);
                saveCardOrder().catch(error => {
                    console.error('Save failed:', error);
                });
            }
            cleanupDragState();
        });
    }
    
    function findCardUnderTouch(x, y) {
        const cards = document.querySelectorAll('.card:not(.dragging)');
        return Array.from(cards).find(card => {
            const rect = card.getBoundingClientRect();
            return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;
        });
    }

    // PC端拖拽卡片
    function dragStart(event) {
        if (!isAdmin) {
            event.preventDefault();
            return;
        }
        draggedCard = event.target.closest('.card');
        if (!draggedCard) return;
    
        draggedCard.classList.add('dragging');
        event.dataTransfer.effectAllowed = "move";
        logAction('开始拖拽卡片', { name: draggedCard.querySelector('.card-title').textContent });
    }
    
    function dragOver(event) {
        if (!isAdmin) {
            event.preventDefault();
            return;
        }
        event.preventDefault();
        const target = event.target.closest('.card');
        if (target && target !== draggedCard) {
            const container = target.parentElement;
            const mousePositionX = event.clientX;
            const targetRect = target.getBoundingClientRect();
    
            if (mousePositionX < targetRect.left + targetRect.width / 2) {
                container.insertBefore(draggedCard, target);
            } else {
                container.insertBefore(draggedCard, target.nextSibling);
            }
        }
    }
    
    // 清理拖拽态函数
    function cleanupDragState() {
        if (draggedCard) {
            draggedCard.classList.remove('dragging');
            draggedCard.style.transform = '';
            draggedCard = null;
        }
        
        document.removeEventListener('touchmove', touchMove);
        document.removeEventListener('touchend', touchEnd);
        
        touchStartX = null;
        touchStartY = null;
    }

    // PC端拖拽卡片
    function drop(event) {
        if (!isAdmin) {
            event.preventDefault();
            return;
        }
        event.preventDefault();
        
        const card = draggedCard;
        const targetCategory = event.target.closest('.card-container').id;
        
        validateToken().then(isValid => {
            if (isValid && card) {
                updateCardCategory(card, targetCategory);
                saveCardOrder().catch(error => {
                    console.error('Save failed:', error);
                });
            }
            cleanupDragState();
        });
    }

    function dragEnd(event) {
        if (draggedCard) {
            draggedCard.classList.remove('dragging');
            logAction('拖拽����片结束');
        }
    }
  
    // 更新卡片分类
    function updateCardCategory(card, newCategory) {
        const cardTitle = card.querySelector('.card-title').textContent;
        const cardUrl = card.querySelector('.card-url').textContent;
        const isPrivate = card.dataset.isPrivate === 'true';
    
        const linkIndex = links.findIndex(link => link.url === cardUrl);
        if (linkIndex !== -1) {
            links[linkIndex].category = newCategory;
        }
    
        const linkArray = isPrivate ? privateLinks : publicLinks;
        const arrayIndex = linkArray.findIndex(link => link.url === cardUrl);
        if (arrayIndex !== -1) {
            linkArray[arrayIndex].category = newCategory;
        }
    
        card.dataset.category = newCategory;
    }

    // 页面加载完成后添加触摸事件监听器
    document.addEventListener('DOMContentLoaded', function() {
        const cardContainers = document.querySelectorAll('.card-container');
        cardContainers.forEach(container => {
            container.addEventListener('touchstart', touchStart, { passive: false });
        });
    });    
    
    
    
    // 保存卡片顺序
    async function saveCardOrder() {
        if (!await validateToken()) {
            return; 
        }
        const containers = document.querySelectorAll('.card-container');
        let newPublicLinks = [];
        let newPrivateLinks = [];
        let newCategories = {};
    
        containers.forEach(container => {
            const category = container.id;
            newCategories[category] = [];
    
            [...container.children].forEach(card => {
                const url = card.querySelector('.card-url').textContent;
                const name = card.querySelector('.card-title').textContent;
                const isPrivate = card.dataset.isPrivate === 'true';
                card.dataset.category = category;
                const link = { name, url, category, isPrivate };
                if (isPrivate) {
                    newPrivateLinks.push(link);
                } else {
                    newPublicLinks.push(link);
                }
                newCategories[category].push(link); 
            });
        });
    
        publicLinks.length = 0;
        publicLinks.push(...newPublicLinks);
        privateLinks.length = 0;
        privateLinks.push(...newPrivateLinks);
        Object.keys(categories).forEach(key => delete categories[key]);
        Object.assign(categories, newCategories);
    
        logAction('保存卡片顺序', { 
            publicCount: newPublicLinks.length, 
            privateCount: newPrivateLinks.length, 
            categoryCount: Object.keys(newCategories).length 
        });
    
        try {
            const response = await fetch('/api/saveOrder', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': localStorage.getItem('authToken')
                },
                body: JSON.stringify({ 
                    userId: 'testUser', 
                    links: [...newPublicLinks, ...newPrivateLinks],
                    categories: newCategories
                }),
            });
            const result = await response.json();
            if (!result.success) {
                throw new Error('Failed to save order');
            }
            logAction('保存卡片顺序', { publicCount: newPublicLinks.length, privateCount: newPrivateLinks.length, categoryCount: Object.keys(newCategories).length });
        } catch (error) {
            logAction('保存顺序失败', { error: error.message });
            alert('保存顺失败，请重试');
        }
    }             
    
    // 设置重新加载卡片
    function reloadCardsAsAdmin() {
        document.querySelectorAll('.card-container').forEach(container => {
            container.innerHTML = '';
        });
        loadLinks().then(() => {
            if (isDarkTheme) {
                applyDarkTheme();
            }
        });
        logAction('重新加载卡片（管理员模式）');
    }
    
    // 密码输入框回车事件
    document.getElementById('admin-password').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            toggleSecretGarden();
        }
    });
    
    // 切换设置状态
    function toggleAdminMode() {
        const adminBtn = document.getElementById('admin-mode-btn');
        const addRemoveControls = document.querySelector('.add-remove-controls');
    
        if (!isAdmin && isLoggedIn) {
            if (!validateToken()) {
                return; 
            }

            // 进入管理员模式
            isAdmin = true;
            adminBtn.textContent = "退出设置";
            addRemoveControls.style.display = 'flex';
            
            // 更新所有卡片的样式
            document.querySelectorAll('.card').forEach(card => {
                card.classList.add('admin-mode');
            });
            
            reloadCardsAsAdmin();
        } else if (isAdmin) {
            // 退出管理员模式
            isAdmin = false;
            removeMode = false;
            adminBtn.textContent = "设置";
            addRemoveControls.style.display = 'none';
            
            // 移除所有卡片的编辑状态
            document.querySelectorAll('.card').forEach(card => {
                card.classList.remove('admin-mode');
            });
            
            reloadCardsAsAdmin();
        }

        updateUIState();
    }
    
    // 切换到登录状态
    function toggleSecretGarden() {
        const passwordInput = document.getElementById('admin-password');
        if (!isLoggedIn) {
            verifyPassword(passwordInput.value)
                .then(result => {
                    if (result.valid) {
                        isLoggedIn = true;
                        localStorage.setItem('authToken', result.token);
                        console.log('Token saved:', result.token); 
                        loadLinks(); 
                        alert('登录成功！');
                        logAction('登录成功');
                    } else {
                        alert('密码错误');
                        logAction('登录失败', { reason: result.error || '密码错误' });
                    }
                    updateUIState();
                })
                .catch(error => {
                    console.error('Login error:', error);
                    alert('登录过程出错，请重试');
                });
        } else {
            isLoggedIn = false;
            isAdmin = false;
            localStorage.removeItem('authToken');
            links = publicLinks;
            loadSections();
            alert('退出登录！');
            updateUIState();
            passwordInput.value = '';
            logAction('退出���录');
        }
    }
    
    // 应用暗色主题
    function applyDarkTheme() {
        document.body.style.backgroundColor = '#121212';
        document.body.style.color = '#ffffff';
        
        // 更新所有卡片
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.style.backgroundColor = '#1e1e1e';
            card.style.color = '#ffffff';
            card.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
            
            // 更新卡片内的URL文字颜色
            const cardUrl = card.querySelector('.card-url');
            if (cardUrl) {
                cardUrl.style.color = '#ffffff';
            }
        });

        // 更新所有输入框和按钮的文字颜色
        const inputs = document.querySelectorAll('input, select, button');
        inputs.forEach(input => {
            input.style.color = '#ffffff';
        });

        // 更新搜索引擎按钮的文字颜色
        const searchEngines = document.querySelectorAll('.search-engine');
        searchEngines.forEach(engine => {
            engine.style.color = '#ffffff';
        });

        // 更新分类标题的文字颜色
        const sectionTitles = document.querySelectorAll('.section-title');
        sectionTitles.forEach(title => {
            title.style.color = '#ffffff';
        });

        logAction('应用暗色主题');
    }
    
    // 显示添加链接对话框
    function showAddDialog() {
        if (!isAdmin) return;
        
        const dialogOverlay = document.getElementById('dialog-overlay');
        const dialogBox = document.getElementById('dialog-box');
        
        // 重置表单
        document.getElementById('name-input').value = '';
        document.getElementById('url-input').value = '';
        document.getElementById('private-checkbox').checked = false;
        
        // 显示对话框
        dialogOverlay.style.display = 'flex';
        // 触发重排后添加 active 类
        setTimeout(() => {
            dialogOverlay.classList.add('active');
            dialogBox.classList.add('active');
        }, 10);
    }
    
    // 隐藏添加链接对话框
    function hideAddDialog() {
        const dialogOverlay = document.getElementById('dialog-overlay');
        const dialogBox = document.getElementById('dialog-box');
        
        dialogOverlay.classList.remove('active');
        dialogBox.classList.remove('active');
        
        setTimeout(() => {
            dialogOverlay.style.display = 'none';
        }, 300);
    }
    
    // 切换删除卡片模式
    function toggleRemoveMode() {
        removeMode = !removeMode;
        const deleteButtons = document.querySelectorAll('.delete-btn');
        deleteButtons.forEach(btn => {
            btn.style.display = removeMode ? 'block' : 'none';
        });
        logAction('切换删除卡片模式', { removeMode });
    }
    
    //切换删除分类模式
    function toggleRemoveCategory() {
        isRemoveCategoryMode = !isRemoveCategoryMode;
        const deleteButtons = document.querySelectorAll('.delete-category-btn');
        deleteButtons.forEach(btn => {
            btn.style.display = isRemoveCategoryMode ? 'inline-block' : 'none';
        });
        logAction('切换删除分类模式', { isRemoveCategoryMode });
    }
    
    // 修改切换主题函数
    function toggleTheme(forceDark = null) {
        isDarkTheme = forceDark !== null ? forceDark : !isDarkTheme;
        
        // 定义主题颜色
        const colors = {
            dark: {
                background: '#121212',
                text: {
                    primary: '#f0f0f0',    // 主要文字
                    secondary: '#b0b0b0',  // 次要文字
                    tertiary: '#808080',    // 第三级文字
                    stroke: '-0.1px -0.1px 0 #000, 0.1px -0.1px 0 #000, -0.1px 0.1px 0 #000, 0.1x 0.1px 0 #000' // 暗色模式的文字描边
                },
                card: {
                    background: '#1e1e1e',
                    shadow: 'rgba(0, 0, 0, 0.5)'
                },
                input: {
                    background: 'rgba(68, 68, 68, 0.7)',
                    border: '#555'
                },
                button: {
                    background: 'rgba(30, 30, 30, 0.7)',
                    activeBackground: 'rgba(40, 40, 40, 0.9)',
                    border: 'rgba(255, 255, 255, 0.1)'
                },
                dialog: {
                    background: 'rgba(30, 30, 30, 0.9)'
                }
            },
            light: {
                background: '#ffffff',
                text: {
                    primary: '#333',
                    secondary: '#666',
                    tertiary: '#999',
                    stroke: '-0.1px -0.1px 0 #fff, 0.1px -0.1px 0 #fff, -0.1px 0.1px 0 #fff, 0.1x 0.1px 0 #fff' // 亮色模式的文字描边
                },
                card: {
                    background: 'rgba(255, 255, 255, 0.5)',
                    shadow: 'rgba(0, 0, 0, 0.1)'
                },
                input: {
                    background: 'rgba(255, 255, 255, 0.7)',
                    border: '#ccc'
                },
                button: {
                    background: 'rgba(255, 255, 255, 0.3)',
                    activeBackground: 'rgba(255, 255, 255, 0.5)',
                    border: 'rgba(255, 255, 255, 0.1)'
                },
                dialog: {
                    background: 'rgba(255, 255, 255, 0.7)'
                }
            }
        };

        const theme = isDarkTheme ? colors.dark : colors.light;

        // 更新基础样式
        document.body.style.backgroundColor = theme.background;
        document.body.style.color = theme.text.primary;

        // 更新主题切换按钮
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.textContent = isDarkTheme ? '☼' : '☾';

        // 更新卡片样式
        document.querySelectorAll('.card').forEach(card => {
            card.style.backgroundColor = theme.card.background;
            card.style.color = theme.text.primary;
            card.style.boxShadow = "0 4px 8px " + theme.card.shadow;
            
            const cardUrl = card.querySelector('.card-url');
            if (cardUrl) {
                cardUrl.style.color = theme.text.secondary;
            }
        });

        // 更��输入框样式
        document.querySelectorAll('input[type="text"], input[type="password"], select').forEach(input => {
            input.style.backgroundColor = theme.input.background;
            input.style.color = theme.text.primary;
            input.style.borderColor = theme.input.border;
        });

        // 更新按钮样式
        document.querySelectorAll('.search-engine, .round-btn, #theme-toggle, .delete-category-btn, .edit-category-btn, .search-bar button, .admin-controls button').forEach(button => {
            const isActive = button.classList.contains('active');
            button.style.backgroundColor = isActive ? theme.button.activeBackground : theme.button.background;
            button.style.color = theme.text.primary;
            button.style.borderColor = theme.button.border;
        });

        // 更新分类标题样式
        document.querySelectorAll('.section-title, .category-header').forEach(title => {
            title.style.color = theme.text.primary;
            title.style.textShadow = theme.text.stroke;
        });

        // 更新对话框样式
        const dialogBox = document.getElementById('dialog-box');
        if (dialogBox) {
            dialogBox.style.backgroundColor = theme.dialog.background;
            dialogBox.style.color = theme.text.primary;
        }

        logAction('切换主题', { isDarkTheme });
    }
    
    // 验证��码
    async function verifyPassword(inputPassword) {
        const response = await fetch('/api/verifyPassword', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ password: inputPassword }),
        });
        const result = await response.json();
        return result;
    }
    
    // 初始化加载
    document.addEventListener('DOMContentLoaded', async () => {
        await validateToken(); 
        await loadLinks();
        
        const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        isDarkTheme = darkModeMediaQuery.matches;
        
        toggleTheme(isDarkTheme);
        
        darkModeMediaQuery.addEventListener('change', e => {
            toggleTheme(e.matches);
        });
    });


    // 前端检查是否有 token
    async function validateToken() {
        const token = localStorage.getItem('authToken');
        if (!token) {
            isLoggedIn = false;
            updateUIState();
            return false;
        }

        try {
            const response = await fetch('/api/getLinks?userId=testUser', {
                headers: { 'Authorization': token }
            });
            
            if (response.status === 401) {
                await resetToLoginState('token已过期，请重新登录'); 
                return false;
            }
            
            isLoggedIn = true;
            updateUIState();
            return true;
        } catch (error) {
            return false;
        }
    }

    // 重置状态
    async function resetToLoginState() {
        cleanupDragState();
        
        localStorage.removeItem('authToken');
        isLoggedIn = false;
        isAdmin = false;
        removeMode = false;
        isRemoveCategoryMode = false;
        
        const passwordInput = document.getElementById('admin-password');
        if (passwordInput) {
            passwordInput.value = '';
            passwordInput.style.borderColor = 'red';
            setTimeout(() => {
                passwordInput.style.borderColor = '';
            }, 2000);
        }
        
        updateUIState();
        links = publicLinks;
        loadSections();
        
        const addRemoveControls = document.querySelector('.add-remove-controls');
        if (addRemoveControls) {
            addRemoveControls.style.display = 'none';
        }
        
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.style.display = 'none';
        });
        
        document.querySelectorAll('.delete-category-btn').forEach(btn => {
            btn.style.display = 'none';
        });
        
        const dialogOverlay = document.getElementById('dialog-overlay');
        if (dialogOverlay) {
            dialogOverlay.style.display = 'none';
        }
    }

    // 应用当前主题
    function applyCurrentTheme() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            updateCardStyle(card);
        });
        
        // 更新其他元素的主题
        updateOtherElementsTheme();
    }

    // 更新其他元素主题
    function updateOtherElementsTheme() {
        // 定义暗色主题的颜色变量
        const darkThemeColors = {
            primary: '#f0f0f0',      // 主要文字，接近白色但不刺眼
            secondary: '#b0b0b0',    // 次要文字，柔和的灰色
            tertiary: '#808080',     // 第三级文字，更深的灰色
            category: '#d8d8d8',     // 分类标题，更亮的灰色，提高可读性
            accent: '#4a9eff'        // 强调色，柔和的蓝色
        };

        const dialogBox = document.getElementById('dialog-box');
        if (dialogBox) {
            dialogBox.style.backgroundColor = isDarkTheme ? 'rgba(30, 30, 30, 0.9)' : 'rgba(255, 255, 255, 0.7)';
            dialogBox.style.color = isDarkTheme ? darkThemeColors.primary : '#333';
        }

        // 输入框样式
        const inputs = document.querySelectorAll('input[type="text"], input[type="password"], select');
        inputs.forEach(input => {
            input.style.backgroundColor = isDarkTheme ? 'rgba(68, 68, 68, 0.7)' : 'rgba(255, 255, 255, 0.7)';
            input.style.color = isDarkTheme ? darkThemeColors.primary : '#333';
            input.style.borderColor = isDarkTheme ? '#555' : '#ccc';
        });
        
        // 按钮样式
        const buttons = document.querySelectorAll('.search-engine, .round-btn, #theme-toggle, .delete-category-btn, .edit-category-btn, .search-bar button, .admin-controls button');
        buttons.forEach(button => {
            if (isDarkTheme) {
                button.style.backgroundColor = button.classList.contains('active') ? 'rgba(40, 40, 40, 0.9)' : 'rgba(30, 30, 30, 0.7)';
                button.style.color = darkThemeColors.primary;
                button.style.borderColor = 'rgba(255, 255, 255, 0.1)';
            } else {
                button.style.backgroundColor = button.classList.contains('active') ? 'rgba(255, 255, 255, 0.5)' : 'rgba(255, 255, 255, 0.3)';
                button.style.color = '#333';
                button.style.borderColor = 'rgba(255, 255, 255, 0.1)';
            }
        });

        // 分类标题样式
        document.querySelectorAll('.section-title').forEach(title => {
            title.style.color = isDarkTheme ? darkThemeColors.category : '#333';
        });

        // 分类头部的所有文字
        document.querySelectorAll('.category-header').forEach(header => {
            header.style.color = isDarkTheme ? darkThemeColors.category : '#333';
        });
    }

    // 拖拽效果优化
    function dragStart(event) {
        if (!isAdmin) return;
        draggedCard = event.target.closest('.card');
        event.target.classList.add('dragging');
        
        // 创建拖拽时的视觉效果
        const dragImage = draggedCard.cloneNode(true);
        dragImage.style.opacity = '0.5';
        dragImage.style.position = 'absolute';
        dragImage.style.left = '-9999px';
        document.body.appendChild(dragImage);
        event.dataTransfer.setDragImage(dragImage, 0, 0);
        
        // 延迟移除克隆元素
        setTimeout(() => document.body.removeChild(dragImage), 0);
    }

    function dragOver(event) {
        event.preventDefault();
        const container = event.target.closest('.card-container');
        if (!container || !draggedCard) return;

        const cards = [...container.querySelectorAll('.card:not(.dragging)')];
        const afterElement = getDragAfterElement(container, event.clientY);
        
        if (afterElement) {
            container.insertBefore(draggedCard, afterElement);
        } else {
            container.appendChild(draggedCard);
        }
    }

    function getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.card:not(.dragging)')];
        
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    function dragEnd(event) {
        if (!draggedCard) return;
        event.target.classList.remove('dragging');
        saveCardOrder();
        draggedCard = null;
    }

    // 删除分类
    async function deleteCategory(category) {
        if (!await validateToken()) {
            return;
        }

        try {
            // 直接删除分类
            links = links.filter(link => link.category !== category);
            publicLinks = publicLinks.filter(link => link.category !== category);
            privateLinks = privateLinks.filter(link => link.category !== category);
            delete categories[category];
            categoryOrder = categoryOrder.filter(cat => cat !== category);

            await saveLinks();
            await loadLinks();

            logAction('删除分类', { category });
        } catch (error) {
            console.error('删除分类失败:', error);
            logAction('删除分类失败', { category, error: error.message });
        }
    }

    async function addLink() {
        if (!await validateToken()) {
            return;
        }

        const nameInput = document.getElementById('name-input');
        const urlInput = document.getElementById('url-input');
        const categorySelect = document.getElementById('category-select');
        const privateCheckbox = document.getElementById('private-checkbox');

        const name = nameInput.value.trim();
        const url = urlInput.value.trim();
        const category = categorySelect.value;
        const isPrivate = privateCheckbox.checked;

        if (!name || !url || !category) {
            if (!name) nameInput.style.borderColor = 'red';
            if (!url) urlInput.style.borderColor = 'red';
            if (!category) categorySelect.style.borderColor = 'red';
            
            setTimeout(() => {
                nameInput.style.borderColor = '';
                urlInput.style.borderColor = '';
                categorySelect.style.borderColor = '';
            }, 2000);
            return;
        }

        const newLink = { name, url, category, isPrivate };

        if (isPrivate) {
            privateLinks.push(newLink);
        } else {
            publicLinks.push(newLink);
        }
        links.push(newLink);

        if (!categories[category]) {
            categories[category] = [];
        }
        categories[category].push(newLink);

        try {
            await saveLinks();
            await loadLinks();
            nameInput.value = '';
            urlInput.value = '';
            privateCheckbox.checked = false;
            hideAddDialog();
            logAction('添加链接', { name, category, isPrivate });
        } catch (error) {
            console.error('添加链接失败:', error);
            logAction('添加链接失败', { error: error.message });
        }
    }

    // 修改 showEditDialog 函数
    function showEditDialog(link) {
        if (!isAdmin) return;
        
        const dialogOverlay = document.getElementById('dialog-overlay');
        const dialogBox = document.getElementById('dialog-box');
        
        // 重置表单内容
        const nameInput = document.getElementById('name-input');
        const urlInput = document.getElementById('url-input');
        const categorySelect = document.getElementById('category-select');
        const privateCheckbox = document.getElementById('private-checkbox');
        
        // 设置表单值
        nameInput.value = link.name;
        urlInput.value = link.url;
        privateCheckbox.checked = link.isPrivate;
        
        // 更新分类选择器
        categorySelect.innerHTML = '';
        Object.keys(categories).forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            if (category === link.category) {
                option.selected = true;
            }
            categorySelect.appendChild(option);
        });
        
        // 设置按钮事件
        const deleteBtn = dialogBox.querySelector('.delete-link-btn');
        const cancelBtn = dialogBox.querySelector('.cancel-btn');
        const saveBtn = dialogBox.querySelector('.save-btn');
        
        // 删除按钮事件
        deleteBtn.onclick = async () => {
            if (!await validateToken()) {
                return;
            }

            if (confirm("确定要删除 " + link.name + " 吗？")) {
                try {
                    // 从所有数组中移除链接
                    links = links.filter(l => l.url !== link.url);
                    publicLinks = publicLinks.filter(l => l.url !== link.url);
                    privateLinks = privateLinks.filter(l => l.url !== link.url);
                    
                    // 从分类中移除链接
                    Object.keys(categories).forEach(category => {
                        categories[category] = categories[category].filter(l => l.url !== link.url);
                    });
                    
                    // 保存到存储
                    try {
                        await fetch('/api/saveOrder', {
                            method: 'POST',
                            headers: { 
                                'Content-Type': 'application/json',
                                'Authorization': localStorage.getItem('authToken')
                            },
                            body: JSON.stringify({ 
                                userId: 'testUser', 
                                links: [...publicLinks, ...privateLinks],
                                categories: categories,
                                categoryOrder: categoryOrder
                            }),
                        });
                        
                        // 重新加载所有内容
                        await loadLinks();
                        
                        // 隐藏对话框
                        hideAddDialog();
                        
                        logAction('删除链接', { name: link.name, url: link.url });
                    } catch (error) {
                        console.error('保存到存储失败:', error);
                        alert('删除失败，请重试');
                        logAction('删除链接失败', { error: error.message });
                        return; 
                    }
                } catch (error) {
                    console.error('删除链接失败:', error);
                    alert('删除失败，请重试');
                    logAction('删除链接失败', { error: error.message });
                } finally {
                    hideAddDialog();
                }
            }
        }; // 添加这个结束括号
        
        // 取消按钮事件
        cancelBtn.onclick = hideAddDialog;
        
        // 保存按钮事件
        saveBtn.onclick = async () => {
            const updatedLink = {
                name: nameInput.value.trim(),
                url: urlInput.value.trim(),
                category: categorySelect.value,
                isPrivate: privateCheckbox.checked
            };
            
            if (!updatedLink.name || !updatedLink.url || !updatedLink.category) {
                alert('请填写完整信息');
                return;
            }
            
            try {
                await updateLink(updatedLink);
                hideAddDialog();
            } catch (error) {
                console.error('更新链接失败:', error);
                alert('更新失败，请重试');
            }
        };
        
        // 显示对话框
        dialogOverlay.style.display = 'flex';
        setTimeout(() => {
            dialogOverlay.classList.add('active');
            dialogBox.classList.add('active');
        }, 10);
    }

    // 修改 hideAddDialog 函数
    function hideAddDialog() {
        const dialogOverlay = document.getElementById('dialog-overlay');
        const dialogBox = document.getElementById('dialog-box');
        
        dialogOverlay.classList.remove('active');
        dialogBox.classList.remove('active');
        
        setTimeout(() => {
            dialogOverlay.style.display = 'none';
        }, 300);
    }

    // 添加移动端对话框支持
    function addMobileDialogSupport(dialogBox) {
        let touchStartY = 0;
        let currentTranslateY = 0;
        
        dialogBox.addEventListener('touchstart', (e) => {
            touchStartY = e.touches[0].clientY;
        }, { passive: true });
        
        dialogBox.addEventListener('touchmove', (e) => {
            const deltaY = e.touches[0].clientY - touchStartY;
            if (deltaY > 0) { // 只允许向下滑动
                currentTranslateY = deltaY;
                requestAnimationFrame(() => {
                    dialogBox.style.transform = "translateY(" + currentTranslateY + "px)";
                });
            }
        }, { passive: true });
        
        dialogBox.addEventListener('touchend', () => {
            if (currentTranslateY > 100) { // 如果滑动距离超过100px
                hideAddDialog();
            } else {
                // 回弹动画
                dialogBox.style.transition = 'transform 0.3s ease';
                dialogBox.style.transform = 'translateY(0)';
                setTimeout(() => {
                    dialogBox.style.transition = '';
                }, 300);
            }
            currentTranslateY = 0;
        });
    }

    // 添加删除链接功能
    async function deleteLink(url, name) {
        if (!await validateToken()) {
            return;
        }
        
        if (confirm("确定要删除 " + name + " 吗？")) {
            try {
                // 从所有数组中移除链接
                links = links.filter(link => link.url !== url);
                publicLinks = publicLinks.filter(link => link.url !== url);
                privateLinks = privateLinks.filter(link => link.url !== url);
                
                // 从分类中移除链接
                Object.keys(categories).forEach(category => {
                    categories[category] = categories[category].filter(link => link.url !== url);
                });
                
                // 保存更改
                await saveLinks();
                
                // 重新加载所有内容
                await loadLinks();
                
                // 隐藏对话框
                hideAddDialog();
                
                logAction('删除链接', { name, url });
            } catch (error) {
                console.error('删除链接失败:', error);
                alert('删除失败，请重试');
                logAction('删除链接失败', { error: error.message });
            }
        }
    }

    // 添加更新链接的函数
    async function updateLink(oldLink) {
        if (!await validateToken()) {
            return;
        }

        const nameInput = document.getElementById('name-input');
        const urlInput = document.getElementById('url-input');
        const categorySelect = document.getElementById('category-select');
        const privateCheckbox = document.getElementById('private-checkbox');

        const name = nameInput.value.trim();
        const url = urlInput.value.trim();
        const category = categorySelect.value;
        const isPrivate = privateCheckbox.checked;

        if (!name || !url || !category) {
            alert('请填写完整信息');
            return;
        }

        // 创建更新后的链接对象
        const updatedLink = {
            name,
            url,
            category,
            isPrivate
        };

        try {
            // 更新数组中的链接
            const updateInArray = (arr) => {
                const index = arr.findIndex(link => 
                    link.url === oldLink.url && 
                    link.name === oldLink.name
                );
                if (index !== -1) {
                    arr[index] = updatedLink;
                }
            };

            updateInArray(links);
            updateInArray(publicLinks);
            updateInArray(privateLinks);

            // 更新分类中的链接
            if (oldLink.category === category) {
                const categoryLinks = categories[category];
                const index = categoryLinks.findIndex(link => 
                    link.url === oldLink.url && 
                    link.name === oldLink.name
                );
                if (index !== -1) {
                    categoryLinks[index] = updatedLink;
                }
            } else {
                // 如果分类改变，需要旧分类移除并添加到新分类
                categories[oldLink.category] = categories[oldLink.category].filter(link => 
                    link.url !== oldLink.url || 
                    link.name !== oldLink.name
                );
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push(updatedLink);
            }

            // 保存更改
            await saveLinks();
            
            // 重新加载所有内容
            await loadLinks();

            // 隐藏对话框
            hideAddDialog();

            logAction('更新链接', { 
                oldName: oldLink.name, 
                newName: name, 
                oldCategory: oldLink.category, 
                newCategory: category 
            });
        } catch (error) {
            console.error('更新链接失败:', error);
            logAction('更新链接失败', { error: error.message });
        }
    }

    async function editCategory(oldName) {
        if (!await validateToken()) {
            return; 
        }
        const newName = prompt('请输入新的分类名称:', oldName);
        if (newName && newName !== oldName) {
            if (categories[newName]) {
                alert('该分类名称已存在');
                return;
            }

            // 更新categories对象
            categories[newName] = categories[oldName];
            delete categories[oldName];

            // 更新categoryOrder数组
            const orderIndex = categoryOrder.indexOf(oldName);
            if (orderIndex !== -1) {
                categoryOrder[orderIndex] = newName;
            }

            // 更新所有相关链接的分类
            links.forEach(link => {
                if (link.category === oldName) {
                    link.category = newName;
                }
            });

            // 同时更新publicLinks和privateLinks中的分类
            publicLinks.forEach(link => {
                if (link.category === oldName) {
                    link.category = newName;
                }
            });

            privateLinks.forEach(link => {
                if (link.category === oldName) {
                    link.category = newName;
                }
            });

            try {
                // 保存更改
                await saveLinks();
                
                // 重新加载所有内容
                await loadLinks();
                
                logAction('编辑分类名称', { oldName, newName });
            } catch (error) {
                console.error('保存分类更改失败:', error);
                alert('保存失败，请重试');
            }
        }
    }

    // 在页面加载时设置初始符号
    document.addEventListener('DOMContentLoaded', () => {
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.textContent = isDarkTheme ? '☼' : '☾';
    });

    // 点击遮罩层关闭对话框
    document.getElementById('dialog-overlay').addEventListener('click', (e) => {
        if (e.target === e.currentTarget) {
            hideAddDialog();
        }
    });

    // 添加保存链接的事件监听器
    document.querySelector('.save-btn').addEventListener('click', addLink);
    </script>
</body>

</html>
`;

// 服务端 token 验证
async function validateServerToken(authToken, env) {
    if (!authToken) {
        return {
            isValid: false,
            status: 401,
            response: { error: 'Unauthorized', message: '未登录或登录已过期' }
        };
    }

    try {
        const [timestamp, hash] = authToken.split('.');
        const tokenTimestamp = parseInt(timestamp);
        const now = Date.now();
        
        const ONE_DAY = 24 * 60 * 60 * 1000;
        if (now - tokenTimestamp > ONE_DAY*30) {
            return {
                isValid: false,
                status: 401,
                response: { 
                    error: 'Token expired',
                    tokenExpired: true,
                }
            };
        }
        
        const tokenData = timestamp + "_" + env.ADMIN_PASSWORD;
        const encoder = new TextEncoder();
        const data = encoder.encode(tokenData);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const expectedHash = btoa(String.fromCharCode(...new Uint8Array(hashBuffer)));
        
        if (hash !== expectedHash) {
            return {
                isValid: false,
                status: 401,
                response: { 
                    error: 'Invalid token',
                    tokenInvalid: true,
                    message: '登录状态无效，请重新登录'
                }
            };
        }

        return { isValid: true };
    } catch (error) {
        return {
            isValid: false,
            status: 401,
            response: { 
                error: 'Invalid token',
                tokenInvalid: true,
                message: '登录验证失败，请重新登录'
            }
        };
    }
}

export default {
    async fetch(request, env) {
      const url = new URL(request.url);
  
      if (url.pathname === '/') {
        return new Response(HTML_CONTENT, {
          headers: { 'Content-Type': 'text/html' }
        });
      }
  
      if (url.pathname === '/api/getLinks') {
        const userId = url.searchParams.get('userId');
        const authToken = request.headers.get('Authorization');
        const data = await env.CARD_ORDER.get(userId);
  
        if (data) {
            const parsedData = JSON.parse(data);
            
            // 验证 token
            if (authToken) {
                const validation = await validateServerToken(authToken, env);
                if (!validation.isValid) {
                    return new Response(JSON.stringify(validation.response), {
                        status: validation.status,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }

                // Token 有效，返回完整数据
                return new Response(JSON.stringify(parsedData), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            }
            
            // 未提供 token，返回公开数据
            const filteredLinks = parsedData.links.filter(link => !link.isPrivate);
            const filteredCategories = {};
            Object.keys(parsedData.categories).forEach(category => {
                filteredCategories[category] = parsedData.categories[category].filter(link => !link.isPrivate);
            });
  
            return new Response(JSON.stringify({
                links: filteredLinks,
                categories: filteredCategories
            }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            });
        }
  
        return new Response(JSON.stringify({
            links: [],
            categories: {}
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });
      }
  
      if (url.pathname === '/api/saveOrder' && request.method === 'POST') {
        const authToken = request.headers.get('Authorization');
        const validation = await validateServerToken(authToken, env);
        
        if (!validation.isValid) {
            return new Response(JSON.stringify(validation.response), {
                status: validation.status,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        const { userId, links, categories, categoryOrder } = await request.json();
        await env.CARD_ORDER.put(userId, JSON.stringify({ links, categories, categoryOrder }));
        return new Response(JSON.stringify({ 
            success: true,
            message: '保存成功'
        }), { 
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });
      }
  
      if (url.pathname === '/api/verifyPassword' && request.method === 'POST') { 
        try {
            const { password } = await request.json();
            const isValid = password === env.ADMIN_PASSWORD;
            
            if (isValid) {
                // 生成含时间戳的加密 token
                const timestamp = Date.now();
                const tokenData = timestamp + "_" + env.ADMIN_PASSWORD; 
                const encoder = new TextEncoder();
                const data = encoder.encode(tokenData);
                const hashBuffer = await crypto.subtle.digest('SHA-256', data);
                
                // 使用指定格式：timestamp.hash
                const token = timestamp + "." + btoa(String.fromCharCode(...new Uint8Array(hashBuffer)));
                
                return new Response(JSON.stringify({ 
                    valid: true,
                    token: token 
                }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            }
            
            return new Response(JSON.stringify({ 
                valid: false,
                error: 'Invalid password'
            }), {
                status: 403,
                headers: { 'Content-Type': 'application/json' }
            });
        } catch (error) {
            return new Response(JSON.stringify({ 
                valid: false,
                error: error.message 
            }), {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            });
      }
  
      if (url.pathname === '/api/backupData' && request.method === 'POST') {
        const { sourceUserId } = await request.json();
        const result = await this.backupData(env, sourceUserId);
        return new Response(JSON.stringify(result), {
          status: result.success ? 200 : 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }  
  
      return new Response('Not Found', { status: 404 });
    }
  
    async function backupData(env, sourceUserId) {
        const MAX_BACKUPS = 10;
        const sourceData = await env.CARD_ORDER.get(sourceUserId);
        
        if (sourceData) {
            try {
                const currentDate = new Date().toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }).replace(/\//g, '-'); 
                
                const backupId = `backup_${currentDate}`;
                
                const backups = await env.CARD_ORDER.list({ prefix: 'backup_' });
                const backupKeys = backups.keys.map(key => key.name).sort((a, b) => {
                    const timeA = new Date(a.split('_')[1].replace(/-/g, '/')).getTime();
                    const timeB = new Date(b.split('_')[1].replace(/-/g, '/')).getTime();
                    return timeB - timeA;  // 降序排序，最新的在前
                });
                
                await env.CARD_ORDER.put(backupId, sourceData);
                
                const allBackups = [...backupKeys, backupId].sort((a, b) => {
                    const timeA = new Date(a.split('_')[1].replace(/-/g, '/')).getTime();
                    const timeB = new Date(b.split('_')[1].replace(/-/g, '/')).getTime();
                    return timeB - timeA;
                });
                
                const backupsToDelete = allBackups.slice(MAX_BACKUPS);
                
                if (backupsToDelete.length > 0) {
                    await Promise.all(
                        backupsToDelete.map(key => env.CARD_ORDER.delete(key))
                    );
                }
    
                return { 
                    success: true, 
                    backupId,
                    remainingBackups: MAX_BACKUPS,
                    deletedCount: backupsToDelete.length 
                };
            } catch (error) {
                return { 
                    success: false, 
                    error: 'Backup operation failed',
                    details: error.message 
                };
            }
        }
        return { success: false, error: 'Source data not found' };
    }
    }
}