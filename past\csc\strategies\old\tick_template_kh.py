""""""
from typing import Any
from vnpy.app.cta_strategy import (
    CtaTemplate,
    BarGenerator,
    ArrayManager,
    TickData,
    BarData,
    OrderData,
    TradeData
)

from vnpy.mytools.utility import SecondBarGenerator
from vnpy.trader.constant import Status

import numpy as np

class TickStrategy(CtaTemplate):
    """"""

    author = "sincerefall"

    boll_window = 20
    boll_dev = 2
    trailing_long = 0.5
    trailing_short = 0.5
    fixed_size = 10

    boll_up = 0
    boll_down = 0
    trading_size = 0
    intra_trade_high = 0
    intra_trade_low = 0
    long_stop = 0
    short_stop = 0

    buy_price = 0
    short_price = 0

    mCount = 0
    last_bid = None
    last_ask = None
    lastP1 = None
    midP1 = None

    parameters = [
        'boll_window',
        'boll_dev',
        'trailing_long',
        'trailing_short',
        'fixed_size',
    ]
    variables = [
        'boll_up',
        'boll_down',
        'trading_size',
        'intra_trade_high',
        'intra_trade_low',
        'long_stop',
        'short_stop',
        'buy_price',
        'short_price',
        'mCount',
        'last_bid',
        'last_ask',
        'lastP1',
        'midP1'
    ]

    def __init__(
        self,
        cta_engine: Any,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        self.sbg = SecondBarGenerator(self.on_bar)
        self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        self.am = ArrayManager(size=300)

        self.buy_vt_orderids = None
        self.sell_vt_orderids = None

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_tick(0)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_time(self, time):
        pass


    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.sbg.update_tick(tick)
        ts = 1
        self.bidP1 = tick.bid_price_1
        self.askP1 = tick.ask_price_1
        self.bidV1 = tick.bid_volume_1
        self.askV1 = tick.ask_volume_1
        self.lastP = tick.last_price
        bidP = self.bidP1 
        askP = self.askP1 
        bid_status = None
        ask_status = None
       
        # 撤单
        print(tick.datetime)
        print('start:', self.buy_vt_orderids, self.last_bid, bidP)
        if self.buy_vt_orderids and (self.last_bid != bidP ):
            for orderID in self.buy_vt_orderids:
                self.cancel_order(orderID)
                bid_status = True

        if self.sell_vt_orderids and (self.last_ask != askP ):
            for orderID in self.sell_vt_orderids:
                self.cancel_order(orderID)
                ask_status = True
       

        print('mid:', self.buy_vt_orderids, self.last_bid, bidP)
        # 检查之前委托都已经结束
        # if self.on_time(tick):
        if bid_status or not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(bidP, self.fixed_size)
                # print(tick.datetime)
                self.last_bid = bidP

        if ask_status or not self.sell_vt_orderids:
                self.sell_vt_orderids = self.short(askP, self.fixed_size)
                # print(tick.datetime)
                self.last_ask = askP

        print('end:', self.buy_vt_orderids, self.last_bid, bidP)

        # 记录上一批的lastP和midP
            
        # # 检查之前委托都已经结束
        # if not self.buy_vt_orderids:
        #     if bidV > 0 and askV >0:
        #         self.buy_vt_orderids = self.buy(bidP, bidV)
        #         self.last_bid = bidP

        # if not self.sell_vt_orderids:
        #     if bidV >0 and askV >0:
        #         self.sell_vt_orderids = self.short(askP, askV)
        #         self.last_ask = askP

        

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.(second)
        """
        self.sbg5.update_bar(bar)

        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return

    def on_5s_bar(self, bar: BarData):
        """"""
        pass

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        pass

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return
        print('on_order:', order.vt_orderid, order.status)

        # 移除全成或暂停的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.sell_vt_orderids,
        ]:
            if order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)

        if not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(self.bidP1, self.fixed_size)
                print('on_order, send_order:', self.buy_vt_orderids)

        if not self.sell_vt_orderids:
                self.sell_vt_orderids = self.short(self.askP1, self.fixed_size)