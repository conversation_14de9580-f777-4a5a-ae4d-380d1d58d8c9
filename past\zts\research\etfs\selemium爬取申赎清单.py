# -*- coding: utf-8 -*-
"""
Created on Fri Aug  9 13:20:04 2019

@author: <PERSON>
"""
import pandas as pd
import numpy as np
from selenium import webdriver
import re
import time
from sqlalchemy import create_engine


def replace_null(matched):
    '''
    替换中国股票简称中间的空格
    '''
    tempstr = matched.group()
    #print(tempstr)
    tempstr1 = tempstr[7:].replace(' ','')   
                        
    return tempstr[:7]+tempstr1

def replace_null2(matched):
    '''
    处理跨境ETF
    '''
    tempstr = matched.group()
    #print(tempstr)
    if(len(tempstr)>7):
        tempstr1 = tempstr[7:].replace(' ','')   
        return tempstr[:7]+tempstr1
    else:
        return tempstr

def get_etflist():
    '''
    1、获取所有详情链接
    2、得到申赎清单和其他数据
    '''
    data_basic=[]###存储基本信息
    qingdanl=pd.DataFrame()#存储所有申赎清单

    driver = webdriver.Chrome()
    driver.get("http://www.sse.com.cn/disclosure/fund/etflist")
    time.sleep(1)
    linkList=[]#####所有ETF详情链接
    aa=driver.find_elements_by_xpath('//*[@id="tableData_tableData1"]//*[@id="idStr" and @class="classStr"]')
    time.sleep(1)
    
    linka=driver.find_elements_by_xpath('//*[@id="tableData_tableData1"]//*[@href and text()="详情"]')####得到5个表首页所有详情链接
    for link in linka:
        linkn=link.get_attribute('href')
        if(linkn.startswith('http://www.sse.com.cn/disclosure/fund/etflist/detail')):
            linkList.append(linkn)
    
    #############遍历表一 获取翻页信息###############
    for i in range(len(aa)):
        aa[i].click()#翻页
        time.sleep(1)
        
        for link in driver.find_elements_by_xpath('//*[@id="tableData_tableData1"]//*[@href and text()="详情"]'):
            linkn=link.get_attribute('href')
            if(linkn.startswith('http://www.sse.com.cn/disclosure/fund/etflist/detail')):
                linkList.append(linkn)
        aa=driver.find_elements_by_xpath('//*[@id="tableData_tableData1"]//*[@id="idStr" and @class="classStr"]')
        #更新翻页信息
    time.sleep(1)
    
    
    #############遍历表二 获取翻页信息###############
    bb=driver.find_elements_by_xpath('//*[@id="tabs-658545"]/ul/li[2]/a')
    bb[0].click()#########点击到表二 跨市场ETF
    bb1=driver.find_elements_by_xpath('//*[@id="tableData_tableData2"]//*[@id="idStr" and @class="classStr"]')
    
    linkb=driver.find_elements_by_xpath('//*[@id="tableData_tableData2"]//*[@href and text()="详情"]')####得到5个表首页所有详情链接
    for link in linkb:
        linkn=link.get_attribute('href')
        if(linkn.startswith('http://www.sse.com.cn/disclosure/fund/etflist/detail')):
            linkList.append(linkn)
    
    ######len(bb1)为页数-1（需要翻的次数）
    for i in range(len(bb1)):
        bb1[i].click()#翻页
        time.sleep(1)
        
        for link in driver.find_elements_by_xpath('//*[@id="tableData_tableData2"]//*[@href and text()="详情"]'):
            linkn=link.get_attribute('href')
            if(linkn.startswith('http://www.sse.com.cn/disclosure/fund/etflist/detail')):
                linkList.append(linkn)
        
        bb1=driver.find_elements_by_xpath('//*[@id="tableData_tableData2"]//*[@id="idStr" and @class="classStr"]')
        #更新翻页信息
    linkl=list(set(linkList))#删除重复链接
    time.sleep(1)
    
    ###############################依次打开详情链接 提取信息######################
    for url in linkl:  
        driver.get(url)
        time.sleep(1)           #留出加载时间
        element1 = driver.find_element_by_id('tableData_tableData1')  
        element2 = driver.find_element_by_id('tableData_tableData2')  
        element3 = driver.find_element_by_id('tableData_tableData3')  
        element4 = driver.find_element_by_id('tableData_tableData4') 
        time.sleep(1)
        text1=element1.text.split()
        text2=element2.text.split()
        text3=element3.text.split()
        text4=element4.text
        '''
        patten = re.compile(r'([\u4e00-\u9fa5]{1})\s+([\u4e00-\u9fa5]{1})\s+')####删除掉京 东 方之间的空格（有些名字有异常）
        
        text4_11  = patten.sub(r'\1\2', text4)
        
        patten2 = re.compile(r'([\u4e00-\u9fa5]{1})\s+([\u4e00-\u9fa5][a-zA-Z]{1})')
        
        text4_22 = patten2.sub(r'\1\2', text4_11)
        '''
        
        model = "[0-9]{6} (.*?)\n"
        pattern = re.compile(model)
        text4_11=re.sub(pattern,replace_null,text4)
        
        text4_1=text4_11.split()
        
        basicnow={'基金代码':text1[8],'date':text1[2],'基金名称':text1[4],'基金公司':text1[6],
                  '现金差额ytd':text2[1],'最小申赎单位净值ytd':text2[3],'基金份额净值ytd':text2[5],
                  '最小申赎单位预估现金部分':text3[1],'现金替代比例上限':text3[3],'申购上限':text3[5],
                  '赎回上限':text3[7],'最小申赎单位':text3[11],'申赎允许情况':text3[13]}
        data_basic.append(basicnow)
        
        del(text4_1[6])
        length=len(text4_1)/6
        shenshul=np.array(text4_1).reshape(int(length),6)
        qingdan=pd.DataFrame(shenshul[1:],columns=shenshul[0])
        qingdan=qingdan.rename(columns={'股票数量(股)':'证券数量'})
        qingdan['基金代码']=text1[len(text1)-1] 
        qingdan['date']=text1[2]
        qingdan['type']='股票'
        qingdanl=pd.concat([qingdanl,qingdan],ignore_index=True)
        
        time.sleep(1)           #留出加载时间
    
    ###############################跨境ETF####################
    driver.get("http://www.sse.com.cn/disclosure/fund/etflist")
    time.sleep(1)
    linkList2=[]
    cc=driver.find_elements_by_xpath('//*[@id="tabs-658545"]/ul/li[3]/a')
    cc[0].click()#########点击到表三 跨境ETF
    time.sleep(1)
    
    linka=driver.find_elements_by_xpath('//*[@id="tableData_tableData3"]//*[@href and text()="详情"]')####得到5个表首页所有详情链接
    for link in linka:
        linkn=link.get_attribute('href')
        if(linkn.startswith('http://www.sse.com.cn/disclosure/fund/etflist/detail')):
            linkList2.append(linkn)
    time.sleep(1)
    
    for url in linkList2:  
        driver.get(url)
        time.sleep(1)           #留出加载时间
        element1 = driver.find_element_by_id('tableData_tableData1')  
        element2 = driver.find_element_by_id('tableData_tableData2')  
        element3 = driver.find_element_by_id('tableData_tableData3')  
        element4 = driver.find_element_by_id('tableData_tableData4') 
        time.sleep(1)
        text1=element1.text.split()
        text2=element2.text.split()
        text3=element3.text.split()
        text4=element4.text
        
        model = "[a-zA-Z\d\u4e00-\u9fa5]{2,6} (.*?)\n"
        pattern = re.compile(model)
        text4_11=re.sub(pattern,replace_null2,text4[49:])
        text4_22=text4[:49]+text4_11
        text4_1=text4_22.split()
        
        basicnow={'基金代码':text1[len(text1)-1],'date':text1[2],'基金名称':text1[4],'基金公司':text1[len(text1)-3],
                  '现金差额ytd':text2[1],'最小申赎单位净值ytd':text2[3],'基金份额净值ytd':text2[5],
                  '最小申赎单位预估现金部分':text3[1],'现金替代比例上限':text3[3],'申购上限':text3[5],
                  '赎回上限':text3[7],'最小申赎单位':text3[11],'申赎允许情况':text3[13]}
        data_basic.append(basicnow)
        
        del(text4_1[6])
        length=len(text4_1)/6
        shenshul=np.array(text4_1).reshape(int(length),6)
        qingdan=pd.DataFrame(shenshul[1:],columns=shenshul[0])
        qingdan=qingdan.rename(columns={'股票数量(股)':'证券数量'})
        qingdan['基金代码']=text1[len(text1)-1] 
        qingdan['date']=text1[2]
        qingdan['type']='跨境'
        qingdanl=pd.concat([qingdanl,qingdan],ignore_index=True)
        
        time.sleep(1)           #留出加载时间
    
    ###############################债券ETF####################
    driver.get("http://www.sse.com.cn/disclosure/fund/etflist")
    time.sleep(1)
    linkList3=[]
    cc=driver.find_elements_by_xpath('//*[@id="tabs-658545"]/ul/li[4]/a')
    cc[0].click()#########点击到表四 债券ETF
    time.sleep(1)
    linka=driver.find_elements_by_xpath('//*[@id="tableData_tableData4"]//*[@href and text()="详情"]')####得到5个表首页所有详情链接
    for link in linka:
        linkn=link.get_attribute('href')
        if(linkn.startswith('http://www.sse.com.cn/disclosure/fund/etflist/detail')):
            linkList3.append(linkn)
    time.sleep(1)
    
    
    for url in linkList3:  
        driver.get(url)
        time.sleep(1)           #留出加载时间
        element1 = driver.find_element_by_id('tableData_tableData1')  
        element2 = driver.find_element_by_id('tableData_tableData2')  
        element3 = driver.find_element_by_id('tableData_tableData3')  
        element4 = driver.find_element_by_id('tableData_tableData4') 
        time.sleep(1)
        text1=element1.text.split()
        text2=element2.text.split()
        text3=element3.text.split()
        text4=element4.text
        text4_1=text4.split()
        
        basicnow={'基金代码':text1[len(text1)-1],'date':text1[2],'基金名称':text1[4],'基金公司':text1[len(text1)-3],
                  '现金差额ytd':text2[1],'最小申赎单位净值ytd':text2[3],'基金份额净值ytd':text2[5],
                  '最小申赎单位预估现金部分':text3[1],'现金替代比例上限':text3[3],'申购上限':text3[5],
                  '赎回上限':text3[7],'最小申赎单位':text3[11],'申赎允许情况':text3[13]}
        data_basic.append(basicnow)
        
        del(text4_1[6])
        length=len(text4_1)/6
        shenshul=np.array(text4_1).reshape(int(length),6)
        qingdan=pd.DataFrame(shenshul[1:], columns=shenshul[0])
        qingdan=qingdan.rename(columns={'证券数量(手)':'证券数量'})
        qingdan['基金代码']=text1[len(text1)-1] 
        qingdan['date']=text1[2]
        qingdan['type']='债券'
        qingdanl=pd.concat([qingdanl,qingdan], ignore_index=True)
        
    ###############################黄金ETF####################
    driver.get("http://www.sse.com.cn/disclosure/fund/etflist")
    time.sleep(1)
    ee=driver.find_elements_by_xpath('//*[@id="tabs-658545"]/ul/li[5]/a')
    ee[0].click()#########点击到表五 黄金ETF
    linkList4=[]
    time.sleep(1)
    linka=driver.find_elements_by_xpath('//*[@id="tableData_tableData5"]//*[@href and text()="详情"]')####得到5个表首页所有详情链接
    for link in linka:
        linkn=link.get_attribute('href')
        if(linkn.startswith('http://www.sse.com.cn/disclosure/fund/etflist/detail')):
            linkList4.append(linkn)
    time.sleep(1)
    
    for url in linkList4:  
        driver.get(url)
        time.sleep(1)           #留出加载时间
        element1 = driver.find_element_by_id('tableData_tableData1')  
        element2 = driver.find_element_by_id('tableData_tableData2')  
        element3 = driver.find_element_by_id('tableData_tableData3')  
        element4 = driver.find_element_by_id('tableData_tableData4') 
        time.sleep(1)
        text1=element1.text.split()
        text2=element2.text.split()
        text3=element3.text.split()
        text4=element4.text
        text4_1=text4.split()
        
        basicnow={'基金代码':text1[len(text1)-1],'date':text1[2],'基金名称':text1[4],'基金公司':text1[len(text1)-3],
                  '现金差额ytd':text2[1],'最小申赎单位净值ytd':text2[3],'基金份额净值ytd':text2[5],
                  '最小申赎单位预估现金部分':text3[1],'现金替代比例上限':text3[3],'申购上限':text3[5],
                  '赎回上限':text3[7],'最小申赎单位':text3[11],'申赎允许情况':text3[13]}
        data_basic.append(basicnow)
        
        del(text4_1[6])
        length=len(text4_1)/6
        shenshul=np.array(text4_1).reshape(int(length),6)
        qingdan=pd.DataFrame(shenshul[1:],columns=shenshul[0])
        qingdan=qingdan.rename(columns={'证券数量(克)':'证券数量'})
        qingdan['基金代码']=text1[len(text1)-1] 
        qingdan['date']=text1[2]
        qingdan['type']='黄金'
        qingdanl=pd.concat([qingdanl,qingdan],ignore_index=True)
    driver.quit()
        
    return pd.DataFrame(data_basic),qingdanl


##################测试####################################
dataBasic,qingdanL=get_etflist()
df1=dataBasic.copy(deep=True)
df1['fundcode']=[str(int(x)-1)+'.SH' for x in df1['基金代码']]
df1[['基金份额净值ytd','最小申赎单位净值ytd','最小申赎单位预估现金部分','现金差额ytd']]=df1[['基金份额净值ytd','最小申赎单位净值ytd','最小申赎单位预估现金部分','现金差额ytd']].applymap(lambda x:float(x[1:]))

engine = create_engine('mysql+pymysql://root:123654mimi@localhost:3306/etf')##数据库funddata
df1.to_sql('basicdata', engine,if_exists='append', index= False,index_label='基金代码')
qingdanL.to_sql('shenshuqingdan', engine,if_exists='append', index= False,index_label='基金代码')










