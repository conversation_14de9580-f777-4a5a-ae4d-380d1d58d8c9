# -*- coding: utf-8 -*-
"""
Created on Thu Mar 31 14:29:55 2022

@author: yihw
"""

import numpy as np
import pandas as pd
import time


# %%
def location1(x, l_max):  # 定位函数, 解出排队位置与队列长度
    for j in range(1, l_max + 1):
        if x < (j * (j + 1)) / 2 + j:
            break
    l = j
    i = round(x - ((j - 1) * j) / 2 - j + 2)
    if (i == j + 1 and j != l_max) or (i >= j + 2 and j == l_max):
        i = np.inf
    return (i, l)


def location2(i, l, l_max):  # 定位函数, 通过排队位置与队列长度来确定表中的位置
    s = 0
    if i <= 0:
        i = 1
    if l <= 0:
        l = 1
    if l > l_max:
        l = l_max
    for j in range(l - 1):
        s += j + 2
    if l != l_max:
        if i > l:
            s += l
        else:
            s += i - 1
    elif l == l_max:
        if i > l + 1:
            s += l + 1
        else:
            s += i - 1
    else:
        s = np.inf
    return s


# %%

def hjb_queue(l_max, q_max, p_list, total_step, phi, pnlFlag=False):
    # 效用函数
    u_list = [
        [[[0 for a in range(int(((l_max + 1) * (l_max + 2) / 2)))] for b in range(int(((l_max + 1) * (l_max + 2) / 2)))]
         for q in range(2 * q_max + 1)] for t in range(total_step + 1)]
    u_list[-1] = [[[-phi * (q - q_max) ** 2 for a in range(int(((l_max + 1) * (l_max + 2) / 2)))] for b in
                   range(int(((l_max + 1) * (l_max + 2) / 2)))] for q in range(2 * q_max + 1)]
    # 临时效用, 不考虑挂撤问题, 先计算临时效用, 再计算真实效用. 临时效用只有一层
    u_temporary_list = [
        [[0 for a in range(int(((l_max + 1) * (l_max + 2) / 2)))] for b in range(int(((l_max + 1) * (l_max + 2) / 2)))]
        for q in range(2 * q_max + 1)]
    # 具体策略
    strategy_list = [[[[tuple() for a in range(int(((l_max + 1) * (l_max + 2) / 2)))] for b in
                       range(int(((l_max + 1) * (l_max + 2) / 2)))] for q in range(2 * q_max + 1)] for t in
                     range(total_step + 1)]
    # 期望pnl
    pnl_list = [
        [[[0 for a in range(int(((l_max + 1) * (l_max + 2) / 2)))] for b in range(int(((l_max + 1) * (l_max + 2) / 2)))]
         for q in range(2 * q_max + 1)] for t in range(total_step + 1)]
    pnl_temporary_list = [
        [[0 for a in range(int(((l_max + 1) * (l_max + 2) / 2)))] for b in range(int(((l_max + 1) * (l_max + 2) / 2)))]
        for q in range(2 * q_max + 1)]

    # 临时乱设的值
    p_t = 0.2  # 队列变化概率
    p_d_o = 0.05  # 基础成交概率
    p_r_o = 0.01  # 原始击穿概率

    for t in range(2, total_step + 2):
        # 首先计算 u_temporary_list
        for ask in range(int(((l_max + 1) * (l_max + 2) / 2))):
            i_a, l_a = location1(ask, l_max)  # 卖排队位置, 卖队列长度
            for bid in range(int(((l_max + 1) * (l_max + 2) / 2))):
                i_b, l_b = location1(bid, l_max)  # 买排队位置, 买队列长度
                for q in range(-q_max, q_max + 1):
                    # 考察在操作不变的情形下的效用函数
                    if i_a != np.inf:
                        # 卖方
                        p_d = p_d_o * (l_max + 2 - i_a)
                        p_r = p_r_o * (l_max + 2 - l_a)
                        u_a = (0.5 * p_t * u_list[-t + 1][q + q_max][bid][location2(i_a - 1, l_a, l_max)] \
                               + 0.5 * p_t * u_list[-t + 1][q + q_max][bid][
                                   location2(i_a - 1, l_a - 1, l_max)] + p_d * (
                                       u_list[-t + 1][max([q + q_max - 1, 0])][bid][
                                           location2(np.inf, l_a, l_max)] + 0.5) \
                               + p_r * (q - 1) - (p_t + p_d) * u_list[-t + 1][q + q_max][bid][ask])
                        if pnlFlag:
                            p_a = (0.5 * p_t * pnl_list[-t + 1][q + q_max][bid][location2(i_a - 1, l_a, l_max)] \
                                   + 0.5 * p_t * pnl_list[-t + 1][q + q_max][bid][
                                       location2(i_a - 1, l_a - 1, l_max)] + p_d * (
                                           pnl_list[-t + 1][max([q + q_max - 1, 0])][bid][
                                               location2(np.inf, l_a, l_max)] + 0.5) \
                                   + p_r * (q - 1) - (p_t + p_d) * pnl_list[-t + 1][q + q_max][bid][ask])

                    elif i_a == np.inf:

                        u_a = (0.5 * p_t * u_list[-t + 1][q + q_max][bid][location2(np.inf, l_a, l_max)] \
                               + 0.5 * p_t * u_list[-t + 1][q + q_max][bid][
                                   location2(np.inf, l_a - 1, l_max)] + p_r * q - p_t * u_list[-t + 1][q + q_max][bid][
                                   ask])
                        if pnlFlag:
                            p_a = (0.5 * p_t * pnl_list[-t + 1][q + q_max][bid][location2(np.inf, l_a, l_max)] \
                                   + 0.5 * p_t * pnl_list[-t + 1][q + q_max][bid][
                                       location2(np.inf, l_a - 1, l_max)] + p_r * q - p_t *
                                   pnl_list[-t + 1][q + q_max][bid][ask])

                    if i_b != np.inf:
                        # 买方
                        p_d = p_d_o * (l_max + 2 - i_b)
                        p_r = p_r_o * (l_max + 2 - l_b)
                        u_b = (0.5 * p_t * u_list[-t + 1][q + q_max][location2(i_b - 1, l_b, l_max)][ask] \
                               + 0.5 * p_t * u_list[-t + 1][q + q_max][location2(i_b - 1, l_b - 1, l_max)][
                                   ask] + p_d * (u_list[-t + 1][min([q + q_max + 1, 2 * q_max])][
                                                     location2(np.inf, l_b, l_max)][ask] + 0.5) \
                               - p_r * (q + 1) - (p_t + p_d) * u_list[-t + 1][q + q_max][bid][ask])
                        if pnlFlag:
                            p_b = (0.5 * p_t * pnl_list[-t + 1][q + q_max][location2(i_b - 1, l_b, l_max)][ask] \
                                   + 0.5 * p_t * pnl_list[-t + 1][q + q_max][location2(i_b - 1, l_b - 1, l_max)][
                                       ask] + p_d * (pnl_list[-t + 1][min([q + q_max + 1, 2 * q_max])][
                                                         location2(np.inf, l_b, l_max)][ask] + 0.5) \
                                   - p_r * (q + 1) - (p_t + p_d) * pnl_list[-t + 1][q + q_max][bid][ask])
                    elif i_b == np.inf:

                        u_b = (0.5 * p_t * u_list[-t + 1][q + q_max][location2(np.inf, l_b, l_max)][ask] \
                               + 0.5 * p_t * u_list[-t + 1][q + q_max][location2(np.inf, l_b - 1, l_max)][
                                   ask] - p_r * q - p_t * u_list[-t + 1][q + q_max][bid][ask])
                        if pnlFlag:
                            p_b = (0.5 * p_t * pnl_list[-t + 1][q + q_max][location2(np.inf, l_b, l_max)][ask] \
                                   + 0.5 * p_t * pnl_list[-t + 1][q + q_max][location2(np.inf, l_b - 1, l_max)][
                                       ask] - p_r * q - p_t * pnl_list[-t + 1][q + q_max][bid][ask])
                    u_o = u_list[-t + 1][q + q_max][bid][ask] - phi * q ** 2

                    u_temporary_list[q + q_max][bid][ask] = u_a + u_b + u_o
                    if pnlFlag:
                        p_o = pnl_list[-t + 1][q + q_max][bid][ask]
                        pnl_temporary_list[q + q_max][bid][ask] = p_a + p_b + p_o
        # 再计算决策
        for ask in range(int(((l_max + 1) * (l_max + 2) / 2))):
            i_a, l_a = location1(ask, l_max)  # 卖排队位置, 卖队列长度
            for bid in range(int(((l_max + 1) * (l_max + 2) / 2))):
                i_b, l_b = location1(bid, l_max)  # 买排队位置, 买队列长度
                for q in range(-q_max, q_max + 1):
                    # 考察最优决策, 同时考虑买卖挂撤单                    
                    if i_a != np.inf and i_b != np.inf:
                        # 卖方不撤单, 买方不撤单
                        u11 = u_temporary_list[q + q_max][bid][ask]
                        if pnlFlag:
                            p11 = pnl_temporary_list[q + q_max][bid][ask]
                        # 卖方不撤单, 买方撤单
                        u10 = u_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][ask]
                        if pnlFlag:
                            p10 = pnl_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][ask]
                        # 卖方撤单, 买方不撤单
                        u01 = u_temporary_list[q + q_max][bid][location2(np.inf, l_a, l_max)]
                        if pnlFlag:
                            p01 = pnl_temporary_list[q + q_max][bid][location2(np.inf, l_a, l_max)]
                        # 卖方撤单, 买方撤单
                        u00 = u_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][location2(np.inf, l_a, l_max)]
                        if pnlFlag:
                            p00 = pnl_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][
                                location2(np.inf, l_a, l_max)]


                    elif i_a != np.inf and i_b == np.inf:
                        # 卖方不撤单, 买方挂单
                        u11 = u_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][ask]
                        if pnlFlag:
                            p11 = pnl_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][ask]
                        # 卖方不撤单, 买方不挂单
                        u10 = u_temporary_list[q + q_max][bid][ask]
                        if pnlFlag:
                            p10 = pnl_temporary_list[q + q_max][bid][ask]
                        # 卖方撤单, 买方挂单
                        u01 = u_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][
                            location2(np.inf, l_a, l_max)]
                        if pnlFlag:
                            p01 = pnl_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][
                                location2(np.inf, l_a, l_max)]
                        # 卖方撤单, 买方不挂单
                        u00 = u_temporary_list[q + q_max][bid][location2(np.inf, l_a, l_max)]
                        if pnlFlag:
                            p00 = pnl_temporary_list[q + q_max][bid][location2(np.inf, l_a, l_max)]


                    elif i_a == np.inf and i_b != np.inf:
                        # 卖方挂单, 买方不撤单
                        u11 = u_temporary_list[q + q_max][bid][location2(l_a + 1, l_a + 1, l_max)]
                        if pnlFlag:
                            p11 = pnl_temporary_list[q + q_max][bid][location2(l_a + 1, l_a + 1, l_max)]
                        # 卖方挂单, 买方撤单
                        u10 = u_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][
                            location2(l_a + 1, l_a + 1, l_max)]
                        if pnlFlag:
                            p10 = pnl_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][
                                location2(l_a + 1, l_a + 1, l_max)]
                        # 卖方不挂单, 买方不撤单
                        u01 = u_temporary_list[q + q_max][bid][ask]
                        if pnlFlag:
                            p01 = pnl_temporary_list[q + q_max][bid][ask]
                        # 卖方不挂单, 买方撤单
                        u00 = u_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][ask]
                        if pnlFlag:
                            p00 = pnl_temporary_list[q + q_max][location2(np.inf, l_b, l_max)][ask]


                    elif i_a == np.inf and i_b == np.inf:
                        # 卖方挂单, 买方挂单
                        u11 = u_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][
                            location2(l_a + 1, l_a + 1, l_max)]
                        if pnlFlag:
                            p11 = pnl_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][
                                location2(l_a + 1, l_a + 1, l_max)]
                        # 卖方挂单, 买方不挂单
                        u10 = u_temporary_list[q + q_max][bid][location2(l_a + 1, l_a + 1, l_max)]
                        if pnlFlag:
                            p10 = pnl_temporary_list[q + q_max][bid][location2(l_a + 1, l_a + 1, l_max)]
                        # 卖方不挂单, 买方挂单
                        u01 = u_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][ask]
                        if pnlFlag:
                            p01 = pnl_temporary_list[q + q_max][location2(l_b + 1, l_b + 1, l_max)][ask]
                        # 卖方不挂单, 买方不挂单
                        u00 = u_temporary_list[q + q_max][bid][ask]
                        if pnlFlag:
                            p00 = pnl_temporary_list[q + q_max][bid][ask]

                    strategy = np.argmax([u00, u01, u10, u11])
                    if strategy == 0:
                        u_list[-t][q + q_max][bid][ask] = u00
                        strategy_list[-t][q + q_max][bid][ask] = (0, 0)
                        if pnlFlag:
                            pnl_list[-t][q + q_max][bid][ask] = p00
                    elif strategy == 1:
                        u_list[-t][q + q_max][bid][ask] = u01
                        strategy_list[-t][q + q_max][bid][ask] = (0, 1)
                        if pnlFlag:
                            pnl_list[-t][q + q_max][bid][ask] = p01
                    elif strategy == 2:
                        u_list[-t][q + q_max][bid][ask] = u10
                        strategy_list[-t][q + q_max][bid][ask] = (1, 0)
                        if pnlFlag:
                            pnl_list[-t][q + q_max][bid][ask] = p10
                    elif strategy == 3:
                        u_list[-t][q + q_max][bid][ask] = u11
                        strategy_list[-t][q + q_max][bid][ask] = (1, 1)
                        if pnlFlag:
                            pnl_list[-t][q + q_max][bid][ask] = p11

    return (u_list, strategy_list, pnl_list)


# %%
t = time.time()
u_list, strategy_list, pnl_list = hjb_queue(l_max=5, q_max=5, p_list=[], total_step=1000, phi=0.01, pnlFlag=False)
print(time.time() - t)

# %%
strategy_list[9][0][0][0]
# %%
u_test = pd.DataFrame(u_list[0][5])
pnl_test = pd.DataFrame(pnl_list[0][5])
strategy_test = pd.DataFrame(strategy_list[0][5])
# %%
t = time.time()
for i in range(50000):
    location1(x=10, l_max=5)
    location2(i=np.inf, l=5, l_max=5)
    np.argmax([1, 2, 3, 4])
    max([1, 2])
print(time.time() - t)

# %%
u_diff = pd.DataFrame(u_list[0][10]) - pd.DataFrame(u_list[1][10])
