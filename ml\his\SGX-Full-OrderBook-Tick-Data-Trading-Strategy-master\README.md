## Modeling High-Frequency Limit Order Book Dynamics Using Machine Learning 

* Framework to capture the dynamics of high-frequency limit order books.

  <img src="./Graph/pipline.png" width="650">
  
#### Overview

In this project I used machine learning methods to capture the high-frequency limit order book dynamics and simple trading strategy to get the P&L outcomes.

* Feature Extractor

  * Rise Ratio
  
    <img src="./Graph/Price_B1A1.png" width="650">

  * Depth Ratio
  
    <img src="./Graph/depth.png" width="650">
    
    [Note] : [Feature_Selection] (Feature_Selection) 
 
* Learning Model Trainer
  
  *  RandomForestClassifier
  *  ExtraTreesClassifier
  *  AdaBoostClassifier
  *  GradientBoostingClassifier
  *  SVM
  
*  Use best model to predict next 10 seconds

   <img src="./Graph/CV_Best_Model.png" width="650">
   
*  Prediction outcome

   <img src="./Graph/prediction.png" width="650">
   
*  Profit & Loss

   <img src="./Graph/P_L.png" width="650">
   
   [Note] : [Model_Selection] (Model_Selection) 

 
# 项目运行框架分析开始
"""
1. 环境依赖：
- Python 3.x
- 核心包：
  - pandas：数据处理
  - numpy：数值计算
  - matplotlib：可视化
  - scikit-learn：机器学习模型

2. 项目结构：
Data_Transformation/
  ├── order book data/       # 原始订单簿数据
  └── Train_Test_Builder/    # 训练测试数据构建

Feature_Selection/
  ├── SGX_A50_Market_Feature.ipynb              # 市场特征提取
  ├── Train_Test_Data_Builder_Feature_Depth_Extractor.ipynb    # 深度特征提取
  └── Train_Test_Data_Builder_Feature_Rise_Extractor.ipynb     # 上升特征提取

Model_Selection/
  └── Model_Selection.ipynb  # 模型选择与训练

Graph/
  └── 可视化结果存储

3. 运行流程：
Step 1: 数据准备
- 准备订单簿数据文件
- 放入正确的目录结构

Step 2: 特征工程
- 运行Rise特征提取
- 运行Depth特征提取
- 运行市场特征提取

Step 3: 模型训练
- 运行模型选择notebook
- 评估不同模型性能
- 选择最优模型

Step 4: 策略回测
- 使用最优模型预测
- 计算交易收益
"""
# 项目运行框架分析结束
