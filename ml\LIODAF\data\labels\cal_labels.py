"""
标签生成器
@author: lining
"""
import numpy as np
import pandas as pd
from utils.utils import log_print
from core import config

class LabelGenerator:
    """标签生成器"""

    def __init__(self, data, horizons=None):
        self.data = data
        self.horizons = horizons

    def generate_return_labels(self, ):
        """生成未来收益率标签"""

        for horizon in self.horizons:
            # 未来价格变动
            self.data[f'future_return_{horizon}'] = self.data['mid'].shift(-horizon) / self.data['mid'] - 1

            self.data[f'price_direction_{horizon}'] = np.sign(self.data[f'future_return_{horizon}'])

            log_print(f"已为 future_return_{horizon} 创建未来价格变动标签 数量: {self.data[f'future_return_{horizon}'].count()}")
    
    def generate_avg_return_labels(self, ):
        """生成未来平均mid标签"""
        for horizon in self.horizons:
            # 计算未来平均mid
            future_avg_mid = self.data['mid'].rolling(window=horizon).mean().shift(-horizon)
            # 未来价格变动
            self.data[f'future_return_avg_mid_{horizon}'] = future_avg_mid / self.data['mid'] - 1

            log_print(f"已为 future_return_avg_mid_{horizon} 创建未来平均mid标签 数量: {self.data[f'future_return_avg_mid_{horizon}'].count()}")

    def generate_return_vwap_labels(self, ):
        """生成未来VWAP收益率标签"""
        for horizon in self.horizons:
            # 计算 VWAP (成交量加权平均价格)
            vwap = self.data['TotalValueTraded'].diff(horizon) / (self.data['Volume'].diff(horizon) * config.MULT)
            # 处理可能的无效值并填充
            vwap = vwap.replace([np.inf, -np.inf], np.nan).ffill()
            
            # 计算未来VWAP收益率并直接添加到原数据框，避免不必要的复制
            self.data[f'future_vwap_return_{horizon}'] = vwap.shift(-horizon) / self.data['mid'] - 1

            log_print(f"已为 future_vwap_return_{horizon} 创建未来VWAP收益率标签 数量: {self.data[f'future_vwap_return_{horizon}'].count()}")
    
    def generate_return_class_labels(self, ):
        """生成未来收益率分类标签"""

        for horizon in self.horizons:
            # 根据收益率的绝对值大小划分为不同类别
            quantiles = [0, 0.8, 0.90, 1.0]
            
            # 创建5分类标签
            abs_returns = self.data[f'future_return_{horizon}'].abs()
            quantile_values = abs_returns.quantile(quantiles)
            
            # 根据绝对值大小分类
            labels = pd.cut(
                abs_returns,
                bins=quantile_values,
                labels=[0, 1, 2],
                include_lowest=True
            )
            
            # 处理NaN值
            labels = labels.fillna(0)  # 将NaN值填充为0
            
            # 转换为整数类型
            labels = labels.astype(int)
            
            # 根据原始收益率的正负调整标签
            self.data[f'return_class_{horizon}'] = labels
            self.data.loc[self.data[f'future_return_{horizon}'] < 0, f'return_class_{horizon}'] *= -1
            
            log_print(f"已为 return_class_{horizon} 创建5分类标签(-2,-1,0,1,2) 数量: {self.data[f'return_class_{horizon}'].count()}")

    def generate_return_class2_labels(self, ):
        """生成未来收益率分类标签"""
        for horizon in self.horizons:
            # 创建3分类标签：大于阈值为1，小于阈值为0，负值乘以-1
            abs_returns = self.data[f'future_return_{horizon}'].abs()
            threshold = abs_returns.quantile(0.8)
            
            # 简化分类逻辑
            self.data[f'return_class2_{horizon}'] = np.where(abs_returns > threshold, 1, 0)
            self.data.loc[self.data[f'future_return_{horizon}'] < 0, f'return_class2_{horizon}'] *= -1

            log_print(f"已为 return_class2_{horizon} 创建3分类标签(-1,0,1) 数量: {self.data[f'return_class2_{horizon}'].count()}")

    def generate_volatility_labels(self, ):
        """生成未来波动率标签"""

        for horizon in self.horizons:
            # 计算未来波动率
            future_std = self.data['mid'].rolling(window=horizon).std().shift(-horizon)
            self.data[f'future_volatility_{horizon}'] = future_std

            log_print(f"已为 future_return_{horizon} 创建未来波动率标签 数量: {self.data[f'future_volatility_{horizon}'].count()}")

    def generate_volatility_return_labels(self, ):
        """生成未来波动率收益率标签"""
        for horizon in self.horizons:
            # 计算未来波动率收益率
            future_std_diff = self.data['mid'].rolling(window=horizon).std().diff(horizon).shift(-horizon)
            future_volatility_return = future_std_diff
            self.data[f'future_volatility_return_{horizon}'] = future_volatility_return

            log_print(f"已为 future_volatility_return_{horizon} 创建未来波动率收益率标签 数量: {self.data[f'future_volatility_return_{horizon}'].count()}")


    def generate_all_labels(self, ):
        """生成标签"""
        target_label = config.TARGET_LABEL
        self.generate_return_labels()

        if target_label == "future_vwap_return":
            # 生成VWAP收益率标签
            self.generate_return_vwap_labels()

        elif target_label == "future_return_avg_mid":
            # 生成平均mid标签
            self.generate_avg_return_labels()

        elif target_label == "return_class":
            # 生成收益率分类标签
            self.generate_return_class_labels()

        elif target_label == "future_volatility":
            # 生成波动率标签
            self.generate_volatility_labels()
        
        elif target_label == "future_volatility_return":
            # 生成波动率收益率标签
            self.generate_volatility_return_labels()

        elif target_label == "return_class2":
            # 生成收益率分类标签
            self.generate_return_class2_labels()
        else:
            log_print(f"未找到目标标签: {target_label}")

        return self.data
