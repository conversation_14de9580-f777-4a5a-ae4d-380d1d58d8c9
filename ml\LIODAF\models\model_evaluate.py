"""
模型评估模块
@author: lining
"""
import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from utils.utils import log_print
from core import config
from utils.utils import calculate_win_rate_threshold


def model_evaluate(y_train_dict: pd.Series,  y_train_pred: pd.DataFrame, train_pred_quantile_dict: dict, set_name: str):
    """评估模型"""
    # 打印出是预测还是回测评估
    log_print(f"评估模型 {y_train_dict.name} {set_name}")
    # 删除包含NaN的行
    y_train_dict_clean = y_train_dict.dropna()

    y_train_pred_clean = y_train_pred.dropna()

    # 获取两个Series的索引交集,用于对齐数据
    common_indices = y_train_dict_clean.index.intersection(y_train_pred_clean.index)
    y_train_dict_clean = y_train_dict_clean[common_indices]
    y_train_pred_clean = y_train_pred_clean[common_indices]

    # 计算基础指标
    train_rmse = np.sqrt(mean_squared_error(y_train_dict_clean, y_train_pred_clean))
    train_mae = mean_absolute_error(y_train_dict_clean, y_train_pred_clean)
    train_r2 = r2_score(y_train_dict_clean, y_train_pred_clean)

    # 计算方向准确率
    direction_accuracy = calculate_win_rate_threshold(y_train_dict_clean, y_train_pred_clean)[0]

    # 计算多个阈值的方向准确率
    direction_accuracy_threshold = {i: calculate_win_rate_threshold(y_train_dict_clean, y_train_pred_clean, i, set_threshold=train_pred_quantile_dict[i]) for i
                                    in config.QUANTILE_LIST}

    # 计算夏普比率
    returns = y_train_dict_clean.diff()
    sharpe_ratio = np.sqrt(252) * (returns.mean() / returns.std()) if returns.std() != 0 else 0

    # 计算最大回撤
    cumulative_returns = (1 + returns).cumprod()
    rolling_max = cumulative_returns.expanding().max()
    drawdowns = (cumulative_returns - rolling_max) / rolling_max
    max_drawdown = drawdowns.min()
    
    # 计算盈亏比
    win_p = returns[returns > 0].abs().mean()
    loss_p = returns[returns < 0].abs().mean()
    win_loss_ratio = win_p / loss_p if loss_p != 0 else 0
    
    results = {
        'rmse': train_rmse,
        'mae': train_mae,
        'r2': train_r2,
        'direction_accuracy': direction_accuracy,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_loss_ratio': win_loss_ratio
    }
    for i in config.QUANTILE_LIST:
        results[f'direction_accuracy_{i}'] = direction_accuracy_threshold[i]

    # 打印评估结果
    log_print(
        f"模型评估结果: RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}, R2: {train_r2:.4f}, 方向准确率: {direction_accuracy * 100:.2f}%, 盈亏比: {win_loss_ratio:.4f}, 夏普比率: {sharpe_ratio:.4f}, 最大回撤: {max_drawdown:.4f}, 分位数方向胜率: " + " ".join(
        [f"{i}: {direction_accuracy_threshold[i][0] * 100:.2f}% {direction_accuracy_threshold[i][1]} " for i in
         config.QUANTILE_LIST]))
    return results

