# -*- coding: utf-8 -*-
"""
Created on Fri Apr 18 14:26:06 2025

@author: admin
"""

import numpy as np
import pandas as pd

def extract_all_features(sample_df: pd.DataFrame,
                         book_df: pd.DataFrame,
                         dt_v6_v7: float = 1.0,
                         dt_v8_short: float = 10.0,
                         dt_v8_long: float = 900.0,
                         base_interval: float = 0.25) -> pd.DataFrame:
    """
    提取 v1 到 v9 所有特征，输入为快照数据和订单簿行为数据。

    参数：
        sample_df: LOB 快照数据（包含 Ask/Bid 的价格和挂单量）
        book_df: 加挂/撤单/主动成交数据（output_book_changes）
        dt_v6_v7: v6/v7 的时间窗口（单位：秒）
        dt_v8_short: v8 的短期窗口（秒）
        dt_v8_long: v8 的长期窗口（秒）
        base_interval: 每个切片的时间长度（秒）

    返回：
        包含所有特征列的 DataFrame
    """

    def compute_v1_all(df):
        res = pd.DataFrame(index=df.index)
        for i in range(1, 6):
            res[f"ask_price_{i}"] = df[f"AskPrice{i}"]
            res[f"ask_vol_{i}"] = df[f"AskVol{i}"]
            res[f"bid_price_{i}"] = df[f"BidPrice{i}"]
            res[f"bid_vol_{i}"] = df[f"BidVol{i}"]
        return res

    def compute_v2_all(df):
        res = pd.DataFrame(index=df.index)
        for i in range(1, 6):
            res[f"spread_{i}"] = df[f"AskPrice{i}"] - df[f"BidPrice{i}"]
            res[f"mid_price_{i}"] = (df[f"AskPrice{i}"] + df[f"BidPrice{i}"]) / 2
        return res

    def compute_v3_all(df):
        res = pd.DataFrame(index=df.index)
        res["ask_price_range"] = df["AskPrice5"] - df["AskPrice1"]
        res["bid_price_range"] = df["BidPrice1"] - df["BidPrice5"]
        res["ask_price_abs_diff_sum"] = sum(abs(df[f"AskPrice{i}"] - df[f"AskPrice{i+1}"]) for i in range(1, 5))
        res["bid_price_abs_diff_sum"] = sum(abs(df[f"BidPrice{i+1}"] - df[f"BidPrice{i}"]) for i in range(1, 5))
        return res

    def compute_v4_all(df):
        res = pd.DataFrame(index=df.index)
        res["mean_ask_price"] = df[[f"AskPrice{i}" for i in range(1, 6)]].mean(axis=1)
        res["mean_bid_price"] = df[[f"BidPrice{i}" for i in range(1, 6)]].mean(axis=1)
        res["mean_ask_volume"] = df[[f"AskVol{i}" for i in range(1, 6)]].mean(axis=1)
        res["mean_bid_volume"] = df[[f"BidVol{i}" for i in range(1, 6)]].mean(axis=1)
        return res

    def compute_v5_all(df):
        res = pd.DataFrame(index=df.index)
        res["accum_price_diff"] = sum(df[f"AskPrice{i}"] - df[f"BidPrice{i}"] for i in range(1, 6))
        res["accum_vol_diff"] = sum(df[f"AskVol{i}"] - df[f"BidVol{i}"] for i in range(1, 6))
        return res

    def compute_v6_all(df, dt, base_interval):
        step = int(round(dt / base_interval))
        res = pd.DataFrame(index=df.index)
        for i in range(1, 6):
            res[f"ask_price_delta_{i}"] = df[f"AskPrice{i}"].diff(periods=step) / dt
            res[f"ask_vol_delta_{i}"] = df[f"AskVol{i}"].diff(periods=step) / dt
            res[f"bid_price_delta_{i}"] = df[f"BidPrice{i}"].diff(periods=step) / dt
            res[f"bid_vol_delta_{i}"] = df[f"BidVol{i}"].diff(periods=step) / dt
        return res

    def compute_v7_all(book_df, dt, base_interval):
        step = int(round(dt / base_interval))
        res = pd.DataFrame(index=book_df.index)
        res["v7_lb"] = book_df[[f"bid_add_{i}" for i in range(1, 6)]].sum(axis=1).rolling(window=step).sum() / dt
        res["v7_la"] = book_df[[f"ask_add_{i}" for i in range(1, 6)]].sum(axis=1).rolling(window=step).sum() / dt
        res["v7_cb"] = book_df[[f"bid_cancel_{i}" for i in range(1, 6)]].sum(axis=1).rolling(window=step).sum() / dt
        res["v7_ca"] = book_df[[f"ask_cancel_{i}" for i in range(1, 6)]].sum(axis=1).rolling(window=step).sum() / dt
        res["v7_mb"] = book_df["marketbid"].rolling(window=step).sum() / dt if "marketbid" in book_df.columns else 0
        res["v7_ma"] = book_df["marketask"].rolling(window=step).sum() / dt if "marketask" in book_df.columns else 0
        return res

    def compute_v8_v9_from_v7(v7_df, short_term_sec, long_term_sec, base_interval):
        short_step = int(round(short_term_sec / base_interval))
        long_step = int(round(long_term_sec / base_interval))
        v8_df = pd.DataFrame(index=v7_df.index)
        v9_df = pd.DataFrame(index=v7_df.index)
        for col in v7_df.columns:
            short_avg = v7_df[col].rolling(window=short_step).mean()
            long_avg = v7_df[col].rolling(window=long_step).mean()
            v8_df[f"v8_{col}"] = (short_avg > long_avg).astype(int)
            v9_df[f"v9_{col}"] = v7_df[col].diff() / base_interval
        return v8_df, v9_df

    # 计算所有特征
    v1 = compute_v1_all(sample_df)
    v2 = compute_v2_all(sample_df)
    v3 = compute_v3_all(sample_df)
    v4 = compute_v4_all(sample_df)
    v5 = compute_v5_all(sample_df)
    v6 = compute_v6_all(sample_df, dt_v6_v7, base_interval)
    v7 = compute_v7_all(book_df, dt_v6_v7, base_interval)
    v8, v9 = compute_v8_v9_from_v7(v7, dt_v8_short, dt_v8_long, base_interval)

    all_features = pd.concat([v1, v2, v3, v4, v5, v6, v7, v8, v9], axis=1)
    return all_features


# 读取数据文件
sample_df = pd.read_csv("sample3.csv")
book_df = pd.read_csv("output_book_changes.csv")

# 调用特征提取函数（你已经定义好的 extract_all_features）
features_df = extract_all_features(
    sample_df=sample_df,
    book_df=book_df,
    dt_v6_v7=1.0,         # v6/v7 使用 1 秒窗口
    dt_v8_short=10.0,     # v8 使用短期 10 秒
    dt_v8_long=900.0,     # v8 使用长期 900 秒
    base_interval=0.25    # 每个切片 0.25 秒
)

# 可选：保存为 CSV 文件
features_df.to_csv("all_features.csv", index=False)

# 查看前几行
print(features_df.head())