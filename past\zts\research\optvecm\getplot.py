import os
import pyodbc
import sys
from datetime import datetime, timedelta, date

import numpy as np
import pandas as pd

import matplotlib.pyplot as plt
from matplotlib import dates
import matplotlib.dates as mdate
import matplotlib.ticker as mtick

sys.path.append(os.path.dirname(os.path.realpath(__file__)))

from optvecm import optionvecm


def getLastWeekDay(day):
    now = day
    print(now)
    print(now.isoweekday())
    if now.isoweekday() == 1:
        dayStep = 3
    else:
        dayStep = 1
    print(dayStep)
    lastWorkDay = now - timedelta(days=dayStep)
    return lastWorkDay


# optionvecm.main()

optnum = 5
timeS = 30
optmod2 = ''  # aver/normal
date1 = '2019-04-26'
# date2 = '2019-03-05'
now = datetime.now()
date2 = datetime.strftime(now+timedelta(days=1), "%Y-%m-%d")
date1 = datetime.strftime(getLastWeekDay(now), "%Y-%m-%d")

inter = 60  # label显示间隔
loci = (inter if inter >= timeS else timeS) / timeS  # label

# 结果储存数据库
server2 = '10.25.18.73'
user2 = 'lining'
password2 = 'lining'
database2 = 'NINO'
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server2, DATABASE=database2, PWD=password2, UID=user2)

if optnum == 1:
    optmod2 = ''
table = 'LeaderO{}T{}{}'.format(optnum, timeS, optmod2)  # 存储结果
if optmod2 == 'aver':
    optnum = 1
else:
    optmod2 = ''

sql = "select * from [NINO].[dbo].[%s] where [index]>'%s' and [index]<'%s' order by [index]" % (table, date1, date2)
pf0 = pd.read_sql(sql, conn, index_col='index', coerce_float=True, params=None, parse_dates=True, columns=None,
                  chunksize=None)

col = ['cornflowerblue', 'yellow', 'lightgreen', 'lightskyblue', 'maroon',
       'mediumseagreen', 'mediumvioletred', 'paleturquoise', 'pink',
       'salmon', 'seagreen', 'orangered', 'olive', 'navy']
# 定义我们的颜色集合，没有特别说明的话，系统也会自己配置

plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
labels = [u"期货买卖 ", u"期货最新"]
for i in range(0, optnum):
    labels.append(u'期权买卖' + str(i - optnum // 2))
    labels.append(u'期货最新' + str(i - optnum // 2))
fig, ax = plt.subplots()
ax.stackplot(range(pf0.shape[0]), np.transpose(pf0.iloc[:, (optnum * 2 + 2):(optnum * 4 + 4)].values * 100),
             labels=labels, colors=col[:(optnum * 2 + 2)])
# ax.xaxis.set_major_locator(dates.MinuteLocator(byminute=range(pf0.shape[0]), interval=1000))

ax.spines['bottom'].set_position(('data', 0))  # 将两个坐标轴的位置设在数据点原点
ax.spines['left'].set_position(('data', 0))

fmt = '%.1f%%'
yticks = mtick.FormatStrFormatter(fmt)
ax.yaxis.set_major_formatter(yticks)

# for tick in ax.get_xticklabels():
#     a=tick.get_text()
#     tick.set_rotation(30)
ax.xaxis.set_major_locator(mtick.MultipleLocator(loci))
fig.canvas.draw()
labels = [item.get_text() for item in ax.get_xticklabels()]

xticks = []
for ii in pf0.index:
    xticks.append(str(ii))
xticks = xticks[::int(loci)]
dtick = len(labels) - len(xticks)
plt.gca().xaxis.set_major_formatter(mdate.DateFormatter('%Y-%m-%d %H:%M:%S'))
# plt.gca().xaxis.set_major_locator(mtick.MaxNLocator(nbins=pf0.shape[0]))
xticks = np.append(range(dtick // 2), xticks)
plt.gca().set_xticklabels(xticks)

plt.gcf().autofmt_xdate()
ax.legend(loc='lower right', bbox_to_anchor=((1 + 1 / (len(xticks) / (120 / (loci * timeS)))), 0), borderaxespad=0)
plt.grid(axis="x", ls='--')
plt.ylabel('贡献度')
plt.suptitle(table)

plt.subplots_adjust(bottom=0.1, right=0.8, top=0.9)
fig.set_size_inches(len(xticks) / (120 / (loci * timeS)) if len(xticks) / (120 / (loci * timeS)) > 5 else 7, 5)
plt.show()
fig.savefig('scatter2.png', dpi=600, bbox_inches='tight')
