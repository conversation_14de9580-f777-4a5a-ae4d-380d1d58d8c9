{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["done load vol\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22648\\3288013383.py:41: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n", "  data = data.groupby('code').resample('1T').last()\n"]}], "source": ["# -*- coding: utf-8 -*-\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from scipy import interpolate\n", "import pandas as pd\n", "import datetime\n", "import os\n", "import sys\n", "sys.path.extend(\n", "    [os.path.abspath(os.path.join(os.path.abspath(__file__) if '__file__' in globals() else os.getcwd(), *(['..'] * i)))\n", "     for i in range(5)])\n", "\n", "from db_solve.parreader import readmd, loadtrade, load_orders, loadvol\n", "\n", "\n", "def cubicspline(z, target):\n", "    z = z.sort_values(by=['delta'])\n", "    z1 = z.loc[z.delta > target].head(2)\n", "    z2 = z.loc[z.delta < target].tail(2)\n", "    z = pd.concat([z2, z1])\n", "    x = z.delta\n", "    y = z.sigma\n", "    try:\n", "        s = interpolate.CubicSpline(x, y)\n", "        return (s(target))\n", "    except:\n", "        return (-99)\n", "\n", "\n", "#    arr=np.arange(np.amin(x), np.amax(x), 0.01)\n", "#   fig,ax = plt.subplots(1, 1)\n", "#  ax.plot(x, y, 'bo', label='Data Point')\n", "\n", "# ax.plot(arr, s(arr), 'k-', label='Cubic Spline', lw=1)\n", "\n", "tradetime00=[['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']]\n", "def vol_cal(path):\n", "    data = loadvol(path,[],tradetime00, all=True)\n", "\n", "    data = data.groupby('code').resample('1T').last()\n", "    data = data.drop('code', axis=1)\n", "    data = data.reset_index()\n", "    data = data[data.delta > 0]\n", "    output = pd.DataFrame()\n", "    output['Delta50'] = data.groupby(['exp', 'time']).apply(lambda z: cubicspline(z, 0.50))\n", "    #  output['Delta25']=data.groupby(['exp','time']).apply(lambda z : cubicspline(z,0.25))\n", "    # output['Delta75']=data.groupby(['exp','time']).apply(lambda z : cubicspline(z,0.75))\n", "\n", "    output['time2expiry'] = data.groupby(['exp', 'time']).apply(lambda z: z.time2expiry.iloc[0])\n", "    output['spot'] = data.groupby(['exp', 'time']).apply(lambda z: z.spot.iloc[0])\n", "    output['forward'] = data.groupby(['exp', 'time']).apply(\n", "        lambda z: z.spot.iloc[0] * np.exp(z.rf.iloc[0] * z.time2expiry.iloc[0] - z.br.iloc[0] * z.time2expiry.iloc[0]))\n", "\n", "    output['forward_vol'] = output.Delta50\n", "    output = output.reset_index()\n", "    output['dt'] = output.groupby('time').time2expiry.diff(1)\n", "    output['dvol'] = output.groupby('time').forward_vol.diff(1)\n", "    #    output['forward_vol']=output.forward_vol/output.dt\n", "    #    output['forward_vol']=output.forward_vol.apply(lambda x :np.sqrt(x))\n", "    return (output)\n", "\n", "\n", "output = vol_cal(u'G:\\\\DATA\\\\SH300\\\\vols_20240930.parquet')\n", "output.to_csv('vols_20240930.csv')\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}