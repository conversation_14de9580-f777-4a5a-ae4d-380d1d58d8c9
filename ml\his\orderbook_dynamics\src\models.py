"""
Models for orderbook dynamics.
This module provides basic data structures for order book analysis.
"""
from enum import Enum
from typing import Dict, Optional, TypeVar, Generic, List


T = TypeVar('T')


class Cell(Generic[T]):
    """
    A container for a value that may or may not be present.
    Similar to Scala's Option type.
    """
    def __init__(self, value: Optional[T] = None):
        self._value = value
        self.is_value = value is not None
        self.is_missing = value is None
    
    def get(self) -> T:
        """Get the value if present, otherwise raise ValueError."""
        if self.is_missing:
            raise ValueError("Cell is empty")
        return self._value
    
    def get_or_else(self, default: T) -> T:
        """Get the value if present, otherwise return default."""
        if self.is_missing:
            return default
        return self._value
    
    def map(self, func):
        """Apply a function to the value if present, otherwise return empty Cell."""
        if self.is_missing:
            return Cell.empty()
        return Cell(func(self._value))
    
    @staticmethod
    def value(value: T) -> 'Cell[T]':
        """Create a Cell with a value."""
        return Cell(value)
    
    @staticmethod
    def empty() -> 'Cell':
        """Create an empty Cell."""
        return Cell(None)


class Side(Enum):
    """Enum for order side (bid or ask)."""
    BID = "BID"
    ASK = "ASK"


class OpenBookMsg:
    """
    Representation of an Open Book message.
    Contains information about an order.
    """
    def __init__(self, symbol: str, source_time: int, source_time_micro_secs: int,
                 price: float, volume: int, side: Side):
        """
        Initialize an OpenBookMsg.
        
        Args:
            symbol: Ticker symbol
            source_time: Timestamp in milliseconds
            source_time_micro_secs: Microsecond part of timestamp
            price: Order price
            volume: Order volume
            side: Order side (BID or ASK)
        """
        self.symbol = symbol
        self.source_time = source_time
        self.source_time_micro_secs = source_time_micro_secs
        self.price = price
        self.volume = volume
        self.side = side
    
    def __str__(self) -> str:
        """String representation of the message."""
        return (f"OpenBookMsg({self.symbol}, {self.source_time}.{self.source_time_micro_secs}, "
                f"{self.price}, {self.volume}, {self.side})")
    
    def __lt__(self, other):
        """Compare messages by timestamp."""
        if not isinstance(other, OpenBookMsg):
            return NotImplemented
        if self.source_time != other.source_time:
            return self.source_time < other.source_time
        return self.source_time_micro_secs < other.source_time_micro_secs


class OrderBook:
    """
    Representation of an order book for a specific symbol.
    Maintains bids and asks with their respective volumes.
    """
    def __init__(self, symbol: str):
        """
        Initialize an OrderBook.
        
        Args:
            symbol: Ticker symbol
        """
        self.symbol = symbol
        self.bids: Dict[float, int] = {}
        self.asks: Dict[float, int] = {}
    
    @staticmethod
    def empty(symbol: str) -> 'OrderBook':
        """Create an empty OrderBook for a symbol."""
        return OrderBook(symbol)
    
    def update(self, msg: OpenBookMsg) -> 'OrderBook':
        """
        Update the OrderBook with a new message.
        
        Args:
            msg: OpenBookMsg to update with
            
        Returns:
            Updated OrderBook (modified in place)
        """
        if msg.symbol != self.symbol:
            return self
        
        # Get the appropriate price map (bids or asks)
        price_map = self.bids if msg.side == Side.BID else self.asks
        
        # Update volume
        if msg.volume == 0:
            # Remove price level if volume is zero
            price_map.pop(msg.price, None)
        else:
            # Update price level with new volume
            price_map[msg.price] = msg.volume
        
        return self
    
    def best_bid(self) -> Cell[float]:
        """Get the highest bid price."""
        if not self.bids:
            return Cell.empty()
        return Cell.value(max(self.bids.keys()))
    
    def best_ask(self) -> Cell[float]:
        """Get the lowest ask price."""
        if not self.asks:
            return Cell.empty()
        return Cell.value(min(self.asks.keys()))
    
    def bid_price(self, level: int) -> Cell[float]:
        """
        Get bid price at a specific level.
        Level 1 is the highest bid.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            Cell containing the price if level exists, otherwise empty
        """
        if not self.bids or level <= 0 or level > len(self.bids):
            return Cell.empty()
        
        # Sort bids in descending order
        sorted_bids = sorted(self.bids.keys(), reverse=True)
        return Cell.value(sorted_bids[level - 1])
    
    def ask_price(self, level: int) -> Cell[float]:
        """
        Get ask price at a specific level.
        Level 1 is the lowest ask.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            Cell containing the price if level exists, otherwise empty
        """
        if not self.asks or level <= 0 or level > len(self.asks):
            return Cell.empty()
        
        # Sort asks in ascending order
        sorted_asks = sorted(self.asks.keys())
        return Cell.value(sorted_asks[level - 1])
    
    def bid_volume(self, level: int) -> Cell[int]:
        """
        Get bid volume at a specific level.
        Level 1 is the highest bid.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            Cell containing the volume if level exists, otherwise empty
        """
        price = self.bid_price(level)
        if price.is_missing:
            return Cell.empty()
        return Cell.value(self.bids[price.get()])
    
    def ask_volume(self, level: int) -> Cell[int]:
        """
        Get ask volume at a specific level.
        Level 1 is the lowest ask.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            Cell containing the volume if level exists, otherwise empty
        """
        price = self.ask_price(level)
        if price.is_missing:
            return Cell.empty()
        return Cell.value(self.asks[price.get()])
    
    def mid_price(self) -> Cell[float]:
        """
        Get the mid price (average of best bid and best ask).
        
        Returns:
            Cell containing the mid price if both best bid and best ask exist, otherwise empty
        """
        best_bid = self.best_bid()
        best_ask = self.best_ask()
        
        if best_bid.is_missing or best_ask.is_missing:
            return Cell.empty()
        
        return Cell.value((best_bid.get() + best_ask.get()) / 2.0)
    
    def spread(self) -> Cell[float]:
        """
        Get the bid-ask spread.
        
        Returns:
            Cell containing the spread if both best bid and best ask exist, otherwise empty
        """
        best_bid = self.best_bid()
        best_ask = self.best_ask()
        
        if best_bid.is_missing or best_ask.is_missing:
            return Cell.empty()
        
        return Cell.value(best_ask.get() - best_bid.get()) 