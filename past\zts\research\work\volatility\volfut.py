# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-29

import pandas as pd
import numpy as np
import pyodbc
from datetime import datetime
import math

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = datetime.now()
# beginDate = "2017-06-24"

day1=20

# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)

sql = "SELECT [DateTime],[Code],[OpenPrice],[High],[Low],[ClosePrice],[Volume],[Amount],[is_prime] FROM [Alex].[dbo].[Future_SR]"

pf = pd.read_sql(sql, conn, index_col=None, coerce_float=True, params=None, parse_dates=None, columns=None,
                 chunksize=None)
conn.commit()
conn.close()

print(pf)

# pf2 = pf.ix['2017-07-24', :]
# print(pf2)

pf['log_return'] = 0
pf['daily_vol'] = 0
pf['ann_vol'] = 0

pf0 = pf[pf['is_prime'] == '0']

codedata = pf.drop_duplicates('Code', keep='first', inplace=False)
codedata = codedata.set_index('Code')
print(len(codedata.index))
num = 0
for i in codedata.index:
    print(i)
    num += 1
    print(num)
    pf0 = pf[pf['Code'] == i]
    for j in range(1, len(pf0.index)):
        index2 = pf0.index[j]
        a = math.log(pf0.loc[pf0.index[j], ['ClosePrice']] / pf0.loc[pf0.index[j-1], ['ClosePrice']])
        pf.loc[index2, ['log_return']] = pf0.loc[index2,['log_return']]= a
        if j >= day1:
            a = pf0[j-day1:j+1]['log_return']
            stan = np.std(a)
            pf.loc[index2, ['daily_vol']] = stan
            pf.loc[index2, ['ann_vol']] = stan*math.sqrt(252)



pf1 = pf[pf['is_prime'] == '1']

pf1.to_excel('volfut2.xls',sheet_name='Mvol')






