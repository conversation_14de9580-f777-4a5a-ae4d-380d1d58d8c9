"""
OpenBook implementation for orderbook dynamics.
This module provides functionality for loading and processing order book data.
"""
import os
import glob
import datetime
from typing import List, Dict, Optional, Iterator, Iterable
import pandas as pd
from .models import OpenBookMsg, Side, OrderBook


class OpenBookFile:
    """
    Representation of an OpenBook data file.
    """
    def __init__(self, source: str, destination: str, date: datetime.date, path: str):
        """
        Initialize an OpenBook file.
        
        Args:
            source: Source identifier (e.g., 'AA')
            destination: Destination identifier (e.g., 'A')
            date: Date of the data
            path: Absolute path to the file
        """
        self.source = source
        self.destination = destination
        self.date = date
        self.path = path
        
    def __str__(self) -> str:
        """String representation of the file."""
        return f"OpenBookFile({self.source}, {self.destination}, {self.date}, {self.path})"


class OpenBook:
    """
    Loader and processor for OpenBook data.
    """
    @staticmethod
    def open_book_files(directory: str) -> List[OpenBookFile]:
        """
        Find OpenBook files in a directory.
        
        Args:
            directory: Directory to search for files
            
        Returns:
            List of OpenBookFile objects
        """
        pattern = os.path.join(directory, "openbookultra([A-Z]{2})_([A-Z])(\\d+)_1_of_1")
        files = []
        
        # Glob doesn't support regex capture groups, so we'll do simple filtering first
        for path in glob.glob(os.path.join(directory, "openbookultra*_1_of_1")):
            filename = os.path.basename(path)
            
            # Try to parse the filename
            try:
                # Extract parts: source (AA), destination (A), date (YYYYMMDD)
                source = filename[12:14]  # e.g., AA
                destination = filename[15]  # e.g., A
                date_str = filename[16:24]  # e.g., 20140101
                
                # Parse date
                date = datetime.datetime.strptime(date_str, "%Y%m%d").date()
                
                files.append(OpenBookFile(source, destination, date, path))
            except (IndexError, ValueError):
                # Skip files that don't match the expected format
                pass
                
        return files
    
    @staticmethod
    def parse_open_book_msg(line: str) -> Optional[OpenBookMsg]:
        """
        Parse a line from an OpenBook file into an OpenBookMsg.
        
        Args:
            line: Line from an OpenBook file
            
        Returns:
            OpenBookMsg or None if the line couldn't be parsed
        """
        try:
            fields = line.strip().split(',')
            
            if len(fields) < 6:
                return None
                
            symbol = fields[0]
            
            # Parse timestamp
            timestamp_parts = fields[1].split('.')
            if len(timestamp_parts) != 2:
                return None
                
            source_time = int(timestamp_parts[0])
            source_time_micro_secs = int(timestamp_parts[1])
            
            # Parse price and volume
            price = float(fields[2])
            volume = int(fields[3])
            
            # Parse side
            side_str = fields[4].upper()
            if side_str == 'B':
                side = Side.BID
            elif side_str == 'S':
                side = Side.ASK
            else:
                return None
                
            return OpenBookMsg(
                symbol=symbol,
                source_time=source_time,
                source_time_micro_secs=source_time_micro_secs,
                price=price,
                volume=volume,
                side=side
            )
        except (ValueError, IndexError):
            return None
    
    @staticmethod
    def iterate(filepath: str) -> Iterator[OpenBookMsg]:
        """
        Iterate through OpenBookMsg objects from a file.
        
        Args:
            filepath: Path to an OpenBook file
            
        Yields:
            OpenBookMsg objects
        """
        with open(filepath, 'r', encoding='latin-1') as f:
            for line in f:
                msg = OpenBook.parse_open_book_msg(line)
                if msg:
                    yield msg
    
    @staticmethod
    def order_log(files: List[OpenBookFile]) -> List[OpenBookMsg]:
        """
        Load order log from OpenBook files.
        
        Args:
            files: List of OpenBookFile objects
            
        Returns:
            List of OpenBookMsg objects sorted by time
        """
        orders = []
        
        for file in files:
            orders.extend(OpenBook.iterate(file.path))
            
        # Sort by time
        return sorted(orders)
    
    @staticmethod
    def order_log_by_symbol(symbol: str, files: List[OpenBookFile]) -> List[OpenBookMsg]:
        """
        Load order log for a specific symbol from OpenBook files.
        
        Args:
            symbol: Symbol to filter by
            files: List of OpenBookFile objects
            
        Returns:
            List of OpenBookMsg objects for the specified symbol sorted by time
        """
        orders = []
        
        for file in files:
            for msg in OpenBook.iterate(file.path):
                if msg.symbol == symbol:
                    orders.append(msg)
                    
        # Sort by time
        return sorted(orders) 