# -*- coding:utf-8 -*-
# Author:Lining
# Editdate:2019-6-20
import pyodbc

from os import path
from sqlalchemy import create_engine

import os
import pyodbc
import sys
from datetime import datetime, timedelta

import numpy as np
# 画图包
# 其他包
import pandas as pd
from WindPy import w
from scipy.linalg import solve
from statsmodels.tsa.api import VAR
from statsmodels.tsa.vector_ar.vecm import VECM

sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from optvecm import greeks


w.start()

date1 = u'2019-07-08'
date2 = u'2019-07-09'

etfcode = u'510050.SH'

# 结果储存数据库
server2 = '***********'
user2 = 'lining'
password2 = 'lining'
database2 = 'NINO'
table='etfConstituent'

table_mod = 'append'  # replace/append

tradedates = w.tdays(date1, date2, "").Data[0]

d = path.dirname(__file__)
etf = pd.read_csv(open(d + '//db//sseETFmm.txt'), sep='\t',
                  names=['id', 'date', 'etf', 'com', 'sto', 'ot'])  # 加载papa.txt,指定它的分隔符是 \t
getf = etf.groupby('etf').size()
etfcodes = getf.index.values
etflist0 = None
nolist = []




def daterange(server2, user2, password2, database2, table):
    # 数据库中最大日期
    conn2 = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server2, DATABASE=database2, PWD=password2, UID=user2)
    cursor = conn2.cursor()
    sql1 = u"SELECT max([index]) FROM [NINO].[dbo].[%s]" % table
    try:
        cursor.execute(sql1)
        dbdate = cursor.fetchone()[0]
        date1 = dbdate + timedelta(days=1)
        date2 = datetime.now()
        if datetime.strftime(dbdate, "%Y-%m-%d") == date2:
            print('newest')
            return
        # date2 = u"2019-04-18"
    except Exception as e:  # 新建数据库
        print(e)
        date1 = u"2019-07-08"
        date2 = u"2019-07-08"  # 包括第一天，包括最后一天
        date1 = datetime.strptime("%s  09:00:00" % date1, "%Y-%m-%d %H:%M:%S")
        date2 = datetime.strptime("%s  15:31:00" % date2, "%Y-%m-%d %H:%M:%S")
        print('new table ' + table)

    print(datetime.strftime(date1, "%Y-%m-%d") + '----' + datetime.strftime(date2, "%Y-%m-%d"))

    conn2.commit()
    conn2.close()

    return date1, date2


wdata = w.wset("etfconstituent", "date=%s;windcode=%s.sh" % (date0, etfcode))
if len(wdata.Data) == 0:
    print(str(etfcode) + ' NO')
etflist = pd.DataFrame(wdata.Data, index=wdata.Fields)


date1, date2 = daterange(server2, user2, password2, database2, table)

if date2 < datetime.strptime("%s  15:31:00" % datetime.strftime(date2, "%Y-%m-%d"), "%Y-%m-%d %H:%M:%S"):
    print(u"\n***交易日未结束")
    date2 = datetime.strptime("%s  15:31:00" % datetime.strftime(date2, "%Y-%m-%d"),
                              "%Y-%m-%d %H:%M:%S") - timedelta(days=1)
if date1 > date2:
    print(table + u"\n***数据已是最新")

date1 = u'2019-05-23'
etflist = etflist.T

engine = create_engine('mssql+pyodbc://%s:%s@%s:1433/%s?driver=SQL+Server' % (user2, password2, server2, database2))

etflist.to_sql(name=table, con=engine, if_exists=table_mod, index=True)  # replace/append

