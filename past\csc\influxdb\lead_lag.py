# -*- coding: utf-8 -*-
"""
Created on Thu Oct 21 13:25:53 2021

@author: yihw
"""
import pandas as pd
import datetime
from datetime import timedelta
from influxdb import InfluxDBClient
import numpy as np
import matplotlib.pyplot as plt

#%%
# client = InfluxDBClient('192.168.203.11',8989,'','','testbase') #上期所prod
client = InfluxDBClient('10.17.88.168',9001,'reader','reader','testbase') #东坝
# client = InfluxDBClient('10.101.237.137',9090,'','','testbase') #东坝测试，带实时行情
# client = InfluxDBClient('202.0.3.209',8989,'','','testbase') #大商所

#%%
beginStr = '2021-10-28T20:50:00.0Z'
endStr = '2021-10-29T15:10:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000

#%%
iid_list = ['pp2201', 'eb2112', 'pg2112', 'l2201', 'pp2202', 'pp2203', 'pp2204']
data_origin = {}
for iid in iid_list:
    result = client.query("select * from testdalian where insid_md='"+iid+"' and time >= %d and time <= %d;"%(begin,end)) 
    points = result.get_points()
    l=[]
    for d in points:
        l.append(d)
    print(len(l))
        
    df = pd.DataFrame(l)
    df['UnixStamp'] = df['local_t']/10**9
    df.index = df.UnixStamp
    df.sort_index(inplace = True)    
    df['micro_price'] = (df['a_p1']*df['b_v1']+df['b_p1']*df['a_v1'])/(df['a_v1']+df['b_v1'])
    df['dmp'] = df['micro_price'].diff()
    df['dmp'].fillna(0, inplace = True)
    df['return'] = df['dmp']/df['micro_price']
    df['log_return'] = df['return'].apply(lambda x: np.log(x+1))
    df['mid_price'] = (df['a_p1'] + df['b_p1'])/2
    df['d_mid'] = df['mid_price'].diff()
    df['d_mid'].fillna(0, inplace = True)
    data_origin[iid] = df.copy()

#%%
df_merge = pd.concat([data_origin[iid] for iid in iid_list])
df_merge.sort_index(inplace = True)

        

#%%
# Hayashi_Yoshida 相关系数
def Hayashi_Yoshida_correlation(r1, r2, t1, t2):
    def var(r):
        s = 0
        for i in r:
            s += (i)**2
        return s        
    var1 = var(r1)
    var2 = var(r2)
    i, j = 0, 0
    cov = 0
    while True:
        if i >= len(r1) or j >= len(r2):
            break
        if t1[i][1] <= t2[j][0]:
            i += 1
            continue
        if t1[i][0] >= t2[j][1]:
            j += 1
            continue
        cov += r1[i]*r2[j]
        if t1[i][1] < t2[j][1]:            
            i += 1
        elif  t1[i][1] > t2[j][1]:
            j += 1
        elif t1[i][1] == t2[j][1]:
            i += 1
            j += 1
    corr = cov/(np.sqrt(var1*var2))
    return corr

def rho_l(r1, r2, t1, t2, l): # l 为时间
    t10 = []
    for i in t1:
        t10.append([i[0]-l, i[1]-l])    
    return Hayashi_Yoshida_correlation(r1, r2, t10, t2)

#%%
data_all = {}
for iid in data_origin.keys():
    data_all[iid] = {}
    df = data_origin[iid]
    df = df.to_dict('records')
    row_l = df[0]
    r_list = []
    mr_list = []
    t_list = []
    log_r_list = []
    for row in df[1:]:
        r = row['dmp']
        log_r = row['log_return']
        mr = row['d_mid']
        t = (row_l['UnixStamp'], row['UnixStamp'])
        r_list.append(r)
        mr_list.append(mr)
        log_r_list.append(log_r)
        t_list.append(t)
        row_l = row.copy()
    data_all[iid]['r'] = r_list
    data_all[iid]['log_r'] = log_r_list
    data_all[iid]['mr'] = mr_list
    data_all[iid]['t'] = t_list

#%%
iid1 = 'pp2201'
iid2 = 'pp2202'
r1 = data_all[iid1]['r']
# log_r1 = data_all[iid1]['log_r']
t1 = data_all[iid1]['t']
r2 = data_all[iid2]['mr']
# log_r2 = data_all[iid2]['log_r']
t2 = data_all[iid2]['t']

# result = Hayashi_Yoshida_correlation(log_r1, log_r2, t1, t2)
result = Hayashi_Yoshida_correlation(r1, r2, t1, t2)
print(result)
    
        
#%%
i = 0.75
L = [(-10+j)*i for j in range(20)]

#%%
L = [-10+2*i for i in range(11) ]


#%%
plt.figure()
plt.title(str(i))
rho = [rho_l(r1, r2, t1, t2, l) for l in L]
# rho_r = [rho_l(log_r1, log_r2, t1, t2, l) for l in L]
plt.plot(L, rho, label = 'dP')
# plt.plot(L, rho_r, label = 'log_r')
plt.legend()
plt.show()       
#%%
def lead_lag(data_origin, iid_list, iid1, iid2, frequency = 0.25, plot = True):
    df_resample = {}
    for iid in iid_list:
        df = data_origin[iid]    
        df['datetime'] = df['UnixStamp'].apply(lambda x: datetime.datetime.fromtimestamp(x))
        df.index = df['datetime']
        
        df1 =  df.dmp.resample(str(int(1000*frequency))+'ms').sum()
        df1 = pd.DataFrame(df1)
        df1['datetime'] = df1.index
        df1['UnixStamp'] = df1.datetime.apply(lambda x: x.timestamp())
        df_resample[iid] = df1 

    data_all = {}
    for iid in df_resample.keys():
        data_all[iid] = {}
        df = df_resample[iid]
        df = df.to_dict('records')
        row_l = df[0]
        r_list = []
        t_list = []
        for row in df[1:]:
            r = row['dmp']
            t = (row_l['UnixStamp'], row['UnixStamp'])
            r_list.append(r)
            t_list.append(t)
            row_l = row.copy()
        data_all[iid]['r'] = r_list
        data_all[iid]['t'] = t_list
        
    r1 = data_all[iid1]['r']
    t1 = data_all[iid1]['t']
    r2 = data_all[iid2]['r']
    t2 = data_all[iid2]['t']    
    if plot == True:    
        i = frequency
        L = [(-10+j)*i for j in range(20)]
        plt.figure()
        plt.title(str(i)+'s')
        rho = [rho_l(r1, r2, t1, t2, l) for l in L]
        plt.plot(L, rho, label = 'dP')
        plt.legend()
        plt.show()               
    return Hayashi_Yoshida_correlation(r1, r2, t1, t2)

#%%
l = []
for i in range(1, 21):
    # frequency = 0.25*i
    frequency = 0.25*i
    l.append(lead_lag(data_origin, iid_list, iid1 = iid1, iid2 = iid2, frequency = frequency, plot = False))

#%%
lead_lag(data_origin, iid_list, iid1 = iid1, iid2 = iid2, frequency = 0.25, plot = True)


#%%
plt.figure()
plt.plot(l)
plt.show()



#%%



#%%
df1 = data_origin[iid1]
df2 = data_origin[iid2]
#%%
plt.figure()
plt.plot(df1.micro_price)
plt.plot(df2.micro_price)
plt.show()













