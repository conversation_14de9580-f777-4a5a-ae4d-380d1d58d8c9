import datetime
import os
import sys

import pandas as pd

sys.path.extend([os.path.abspath(os.path.join(os.getcwd(), *(['..'] * i))) for i in range(3)])

# 设置 Pandas 显示选项
pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.max_rows', None)  # 显示所有行
pd.set_option('display.width', None)  # 自动调整列宽
pd.set_option('display.max_colwidth', None)  # 显示所有单元格的内容
pd.set_option('display.expand_frame_repr', False)  # 设置Pandas选项以打印不换行的DataFrame

today = datetime.datetime.now().strftime("Y%m%d")


def main(datetoday='20240904', trade=True):
    setting = dict(datetoday=datetoday, under='SH500', spot_month='2409', out_fut_month='2409', optcodes=['10007447', ],
                   mode=['a50', 'one', 'fut', 'etffast'][1],
                   etf=dict(SH300='16', SH500='159915', SH50='16'),
                   # tradetime00=[['09:30:00', '09:55:00'], ['10:00:00', '10:00:00']],
                   silmode=['all', 'onlytrade', 'onlymid'][0], )

    engine = Engine()
    engine.paths = paths.Paths(setting["under"], os.getcwd())

    setting.update(paths.UNDERCONFIG[setting["under"]])

    key_set = {0: 'LastPrice', 1: 'mid', 2: 'ForQuoteSysID', 1.1: 'dsema', 1.2: 'BASIS', 1.3: 'basisema',
               3: 'im1',
               5: 'im5', 6: 'im5vol', 7: 'im2mult', 8: 'mid_minnum', 8.2: 'mid_minnum2', 9: 'press', 10: 'voi',
               11: 'turnover_mid',
               12: 'mid_level', 13: 'mixedge_minnum'}

    mult_dict = {'im5': 2, 'im5vol': 3, 'im2mult': 2, 'mid_minnum': 2, 'mid_minnum2': 2, 'press': 1, 'voi': 1,
                 'turnover_mid': 1}

    mode_set = (
        {'a50': dict(drvmode=Stray, sigmode='cum', mixmode='backward', key=['LastPrice', ],
                     fut1=setting['spot'] + setting['spot_month'],
                     str1=engine.paths.str1,
                     fut2='CN' + setting['out_fut_month'], futmult2=1, str2=engine.paths.str2,
                     # fut2='si2411', futmult2=5, str2=engine.paths.str22,
                     start=20, end=4900,
                     minpx=1,  # cum
                     minpx2=1,  # fast
                     resetpxchg=1,  # if>minpx,minpx会继续累计
                     minvol=10, rrratio=0, mult=1,
                     lasttimelimit=100,  # 0表示不跨期货行情
                     groupList=['_signal', 'dtmdmix_type', '_dtfut_int', '_vol_type', ]
                     ),
         'one': dict(drvmode=futone, sigmode='chg', mixmode='backward', key=[key_set[key] for key in (8,)],
                     fut1=setting['spot'] + setting['spot_month'],
                     str1=engine.paths.str1,
                     fut2=setting['spot'] + setting['spot_month'], futmult2=FUTCONFIG[setting['spot']]['futmult'],
                     futmintick=FUTCONFIG[setting['spot']]['mintick'],
                     str2=engine.paths.str1,
                     minpx=0.6, mult1=mult_dict, sweight=0.5,
                     ),
         'one2': dict(drvmode=futone, sigmode='chg', mixmode='backward', key=[key_set[key] for key in (8,)],
                      fut1='10007380',
                      str1=engine.paths.str_optmd,
                      fut2='10007380', futmult2=10000,
                      str2=engine.paths.str_optmd,
                      minpx=0.00000, mult1=mult_dict, sweight=0.5,
                      ),
         'etffast': dict(drvmode=Stray, sigmode='cum', mixmode='backward', key=['LastPrice'],
                         fut1=setting['spot'] + setting['spot_month'],
                         str1=engine.paths.str1,
                         fut2='510500_tick', futmult2=1,
                         str2=engine.paths.str23,
                         start=20, end=4900,
                         minpx=0.0001,  # cum
                         minpx2=0.0001,  # fast
                         resetpxchg=0.0001,  # if>minpx,minpx会继续累计
                         minvol=100000, rrratio=0, mult=1, lasttimelimit=100,  # 0表示不跨期货行情
                         groupList=['dtmdmix_type', '_dtfut_int', '_signal', '_vol_type']
                         ),
         'etf': dict(drvmode=Stray, sigmode='fast', mixmode='backward', key=['ForQuoteSysID'],
                     fut1=setting['spot'] + setting['spot_month'],
                     fut2=setting['etf'][setting['under']],
                     str2=engine.paths.str23,
                     start=20, end=4900,
                     minpx=0.0002,  # cum
                     minpx2=0.0002,  # fast
                     resetpxchg=0.0002,  # if>minpx,minpx会继续累计
                     minvol=500000, rrratio=0, mult=1, lasttimelimit=100,  # 0表示不跨期货行情
                     ),
         'fut': dict(drvmode=Stray, sigmode='one', mixmode='backward', key=[key_set[key] for key in (1,)],
                     fut1=setting['spot'] + setting['spot_month'],
                     fut2=setting['spot2'] + setting['spot_month'], futmult2=FUTCONFIG[setting['spot2']]['futmult'],
                     str1=engine.paths.str1,
                     str2=engine.paths.str1,
                     start=-100, end=4900,
                     minpx=0.1,  # cum
                     minpx2=0.1,  # fast
                     resetpxchg=0,  # if>minpx,minpx会继续累计
                     minvol=1, rrratio=0,  # 上一次跳动
                     lasttimelimit=10000000,  # 0表示不跨期货行情
                     groupList=['_signal', 'dsf-1_y', 'dt_mdmix', ]
                     ),
         'mix': dict(drvmode=Stray, sigmode='fast', mixmode='forward', key='mid',
                     fut2='IM2401', str2=engine.paths.str1,
                     fut_ad='IC2401', key_ad=None, str_ad=engine.paths.str1,
                     start=-100, end=4900,
                     minpx=0.4,  # cum
                     minpx2=0.4,  # fast
                     resetpxchg=0,  # if>minpx,minpx会继续累计
                     minvol=0, rrratio=0, lasttimelimit=100,  # 0表示不跨期货行情
                     syn=0, mult=1, mult2=1)})

    setting.update(mode_set[setting['mode']])
    engine.add_strategy(setting['drvmode'], setting)
    origin = sys.stdout
    out_file = '-'.join(
        [str(setting[key]) for key in ['datetoday', 'under', 'mode', 'sigmode', 'key', 'minpx']] +
        [datetime.datetime.now().strftime("%m%d_%H%M%S")])
    sys.stdout = Logger(engine.paths.outdirs + f"\\{out_file}.txt")

    engine.load_data()
    engine.start_strategy()
    print(mode_set[setting['mode']])
    group_results = engine.stat_func()

    engine.plot_summary(group_results)
    engine.plot_surface(group_results)
    engine.plot_time(group_results)
    engine.plot_pivot(group_results)
    if trade:
        engine.trade_mix_md()
        engine.mix_trade_sig()
    else:
        print('no trade')
    print('all done ' + datetoday)
    os.startfile(engine.paths.outdirs)
    sys.stdout = origin


if __name__ == '__main__':
    from pnlback.signal.stragtegies.futone import futone
    from pnlback.signal.stragtegies.stray import Stray
    from app.backengie import Engine
    from db_solve import Logger
    from db_solve.configs import paths
    from db_solve.configs.paths import FUTCONFIG

    main()
    sys.exit()

