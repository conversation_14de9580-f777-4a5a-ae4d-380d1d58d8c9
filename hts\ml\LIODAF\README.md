# OrderBook Dynamics Analysis Framework (ODAF)

基于高频订单簿数据的机器学习分析和交易系统。

## 项目概述

利用高频订单簿数据，构建完整的数据处理、特征工程、模型训练、回测与可视化流程

### 核心功能

- **数据处理**：加载和预处理高频订单簿数据
- **特征工程**：生成订单簿微观结构特征
- **因子分析**：计算和评估特征的预测能力，包括IC、IR等指标
- **模型训练**：支持多种机器学习模型，包括LightGBM、XGBoost等
- **参数优化**：支持模型参数自动搜索与优化
- **策略回测**：提供完整的交易回测系统，支持多种交易规则
- **结果可视化**：自动生成交易分析图表和特征重要性分析

## 系统架构

### 总体架构

```
ml/frames/
── core/              # 核心功能模块
   ── backtester.py  # 交易回测系统
   ── config.py      # 配置参数
   ── m1.py          # 主控制类
── data/              # 数据处理相关
   ── data_loader.py # 数据加载与预处理
   ── features/      # 特征工程
   ── labels/        # 标签生成
── models/            # 模型相关
   ── model_trainer.py # 模型训练器
   ── model_evaluate.py # 模型评估
── utils/             # 工具函数
   ── utils.py       # 通用工具函数
   ── visualizer.py  # 可视化工具
── output/            # 输出目录
── requirements.txt   # 依赖包
```

### 数据流动和组件交互

1. **数据加载流程**:
   ```
   OrderBookDataLoader → 原始数据 → 数据分割与标准化 → 训练/测试数据集
   ```

2. **特征工程流程**:
   ```
   FactorSystem → FactorManager → 特征注册 → OrderBookFeatureGenerator → 特征生成 → 特征分析
   ```

3. **模型训练流程**:
   ```
   训练数据 → OrderBookModelTrainer → 参数搜索 → 模型训练 → 模型评估 → 预测结果
   ```

4. **回测流程**:
   ```
   测试数据 + 模型预测 → OrderBookBacktester → 信号生成 → 交易执行 → 绩效评估
   ```

5. **可视化流程**:
   ```
   分析结果 → OrderBookVisualizer → 特征重要性图表 → 回测结果图表 → 交易分析报告
   ```

### 扩展设计

系统设计采用了模块化和可扩展性原则，主要体现在：

1. **因子系统**:
   - 装饰器模式实现的因子注册机制
   - 分类管理的因子体系
   - 标准化的因子接口
   
2. **模型系统**:
   - 工厂模式实现的模型创建
   - 统一的模型训练和评估接口
   - 可插拔的参数搜索组件

3. **回测系统**:
   - 事件驱动的交易逻辑
   - 可配置的交易规则
   - 详细的交易记录和分析

## 快速开始

### 环境配置

1. 安装依赖：

```bash
pip install -r ml/frames/requirements.txt
```

### 运行示例

1. 修改配置文件 `ml/frames/core/config.py` 设置相关参数：

```python
# 数据路径配置
DATA_PATH = "your_data_path/md_%s_cffex_multicast.parquet"
DATE_STR = "20250420"  # 日期
CODE_LIST = ['IM2506', ]  # 合约列表

```

2. 运行主程序：

```bash
python -m ml.frames.core.m1
```

## 项目扩展

### 添加新特征

在 `ml/frames/data/features/` 目录下的文件中添加新的特征计算函数，并使用 `factor_manager.register_factor()` 方法注册：

```python
# 1. 先定义特征计算函数
def calculate_new_feature(data: pd.DataFrame, window_size: int = 10) -> pd.Series:
    """计算新特征
    参数:
        data: 原始数据框
        window_size: 窗口大小参数
    返回:
        pd.Series: 计算结果
    """
    # 特征计算逻辑
    return result

# 2. 注册新特征到因子管理器
from ml.frames.data.features.factor_manager import factor_manager, Factor, FactorCategory

factor_manager.register_factor(Factor(
    name="new_feature",                # 特征名称
    category=FactorCategory.BASIC,     # 特征类别
    description="新特征描述",           # 特征描述
    calculation=calculate_new_feature, # 计算函数
    dependencies=["AskPrice1", "BidPrice1"], # A列表，表示该特征依赖的其他数据列
    parameters={"window_size": 10},    # 参数字典
    source="自定义特征 v1.0"            # 特征来源
))
```

也可以使用 lambda 函数直接定义计算逻辑：

```python
factor_manager.register_factor(Factor(
    name="price_diff",
    category=FactorCategory.BASIC,
    description="价格差异",
    calculation=lambda data: data['AskPrice1'] - data['BidPrice1'],
    dependencies=["AskPrice1", "BidPrice1"]
))
```

### 添加新模型

在 `ml/frames/models/set_model.py` 中添加新的模型类型支持:

```python
def set_model(model_type, params=None):
    if model_type == 'new_model':
        # 初始化新模型
        return new_model_instance
```