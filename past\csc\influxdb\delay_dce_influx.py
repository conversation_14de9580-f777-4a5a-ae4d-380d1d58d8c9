# -*- coding: utf-8 -*-
"""
Created on Wed Sep  1 09:56:32 2021

@author: yihw
"""

from influxdb import InfluxDBClient
import datetime
import time
import numpy as np
import pandas as pd
from OmmDatabase import OmmDatabase

import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False    
# import re
from enum import Enum
#%%
class order_status(Enum):
    pending_add = 0
    pending_delete = 1
    exchange_order = 2
    partial_traded = 3
    all_traded = 4
    deleted = 5
    over_flow = 8
    cancelled = 9
#%%

client = InfluxDBClient('10.17.88.168',9001,'reader','reader','testbase') # 东坝机房
# client = InfluxDBClient('10.17.88.168',9001,'reader','reader','omm') # 东坝机房2

#%%
beginStr = '2021-10-08T08:50:00.0Z'
endStr = '2021-10-08T15:10:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
# result_order = client.query("select * from dce_future_order where time >= %d and time <= %d;"%(begin,end)) 
# result_quote = client.query("select * from dce_future_quote where time >= %d and time <= %d;"%(begin,end)) 
# result_mkt = client.query("select * from test10 where time >= %d and time <= %d limit 10;"%(begin,end)) 
result_order = client.query("select * from shfe_future_order where time >= %d and time <= %d;"%(begin,end)) 
# result_quote = client.query("select * from shfe_future_quote where time >= %d and time <= %d;"%(begin,end)) 
# result = client.query("select * from testzhengzhou limit 10") 

#%%
# points = result.get_points()
# l=[]
# for d in points:
#     l.append(d)
# print(len(l))
# ll = pd.DataFrame(l)

#%%
points1 = result_order.get_points()
l=[]
for d in points1:
    l.append(d)
print(len(l))
dce_order = pd.DataFrame(l)

points2 = result_quote.get_points()
l=[]
for d in points2:
    l.append(d)
print(len(l))
dce_quote = pd.DataFrame(l)

# points3 = result_mkt.get_points()
# l=[]
# for d in points3:
#     l.append(d)
# print(len(l))
# dce_mkt = pd.DataFrame(l)

#%%
try:
    dce_order['UnixStamp'] = dce_order['local_time']/10**9
    dce_order.index = dce_order['UnixStamp']
    dce_order = dce_order.sort_index()
except:
    dce_order.columns = ['time', 'base_price', 'comments', 'error_id', 'error_message', 'insid',
       'internal_order_id', 'last_traded_time', 'last_update_time', 'level',
       'local_time', 'long_short', 'match_condition', 'note', 'open_close',
       'order_id', 'order_price', 'order_status', 'portfolio_id',
       'strategy_id', 'traded_price', 'volume_original_total', 'volume_total',
       'volume_traded', 'UnixStamp']

try:
    dce_quote['UnixStamp'] = dce_quote['local_time']/10**9
    dce_quote.index = dce_quote['UnixStamp']
    dce_quote = dce_quote.sort_index()
except:
    dce_quote = pd.DataFrame(columns=['time', 'ask_error_id', 'ask_open_close', 'ask_order_price',
       'ask_order_status', 'ask_volume_original_total', 'ask_volume_total',
       'ask_volume_traded', 'base_ask_price', 'base_bid_price', 'bid_error_id',
       'bid_open_close', 'bid_order_price', 'bid_order_status',
       'bid_volume_original_total', 'bid_volume_total', 'bid_volume_traded',
       'cancel_time', 'error_id', 'error_message', 'insert_time', 'insid',
       'internal_quote_id', 'last_traded_time', 'level', 'local_time', 'note',
       'portfolio_id', 'prevInternal_quote_id', 'quote_id', 'quote_request_id',
       'quote_status', 'strategy_id', 'UnixStamp'])          

try:
    dce_mkt['UnixStamp'] = shfe_mkt['local_time']/10**9
    dce_mkt.index = shfe_mkt['UnixStamp']
    dce_mkt = shfe_mkt.sort_index()      
except:
    dce_mkt = pd.DataFrame(columns=['time', 'a_p1', 'a_p2', 'a_p3', 'a_p4', 'a_p5', 'a_v1', 'a_v2', 'a_v3',
       'a_v4', 'a_v5', 'b_p1', 'b_p2', 'b_p3', 'b_p4', 'b_p5', 'b_v1', 'b_v2',
       'b_v3', 'b_v4', 'b_v5', 'exchange_t', 'insid_md', 'last_p', 'local_t',
       'lower_limit_p', 'preclose_p', 'presettle_p', 'turnover',
       'upper_limit_p', 'v'])

#%%
iid_list = list(set(list(dce_order.insid)+list(dce_quote.insid)))

# iid_list = ['pg2111']

result = {}
for iid in iid_list:
    t = time.time()
    
    df_dce_order = dce_order[dce_order.insid == iid]
    df_dce_order = df_dce_order.to_dict('records')
    
    df_dce_quote = dce_quote[dce_quote.insid == iid]
    df_dce_quote = df_dce_quote.to_dict('records')
    
    bid_order_alive = {}
    ask_order_alive = {}
    bid_quote_alive = {}
    ask_quote_alive = {}
    internal_quote_id_list = []
    
    cancel_slow = {}
    trade_id = {}
    
    
    pre_order_alive = {}
    pre_cancel_alive = {}
    pre_order_delay = []
    pre_cancel_delay = []
    
    df_order = iter(df_dce_order)
    df_quote = iter(df_dce_quote)
    
    net = 0
    pnl = 0
    bidamount = 0
    askamount = 0
    
    i = next(df_order, None)
    j = next(df_quote, None)
    
    print(iid+'数据转化完毕', time.time()-t)
    t = time.time()
    
    count = 0
    count1 = 0 # 成交次数
    count2 = 0 # 撤单太慢成交次数
    
    while True:
        
        if i == None and j == None:
            break
        
        elif i == None:
            if j['quote_status'] == order_status.pending_add.value:
                bid_order_id = j['internal_quote_id']+2
                ask_order_id = j['internal_quote_id']+1
                internal_quote_id_list.append(bid_order_id)
                internal_quote_id_list.append(ask_order_id)
                if not bid_order_id in pre_order_alive.keys():
                    j1 = j.copy()
                    j1['internal_order_id'] = bid_order_id
                    j1['long_short'] = 0                    
                    pre_order_alive[bid_order_id] = j1   

                if not ask_order_id in pre_order_alive.keys():
                    j2 = j.copy()
                    j2['internal_order_id'] = ask_order_id
                    j2['long_short'] = 1                    
                    pre_order_alive[ask_order_id] = j2   
                    
                bid_quote = bid_quote_alive
                ask_quote = ask_quote_alive
                
                if bid_quote_alive:
                    for bid_quote in bid_quote_alive.keys():
                        if not bid_quote in pre_cancel_alive.keys():
                            pre_cancel_alive[bid_quote] = bid_order_alive[bid_quote].copy()
                            pre_cancel_alive[bid_quote]['UnixStamp'] = j['UnixStamp']
                if ask_quote_alive: 
                    for ask_quote in ask_quote_alive.keys():
                        if not ask_quote in pre_cancel_alive.keys():
                            pre_cancel_alive[ask_quote] = ask_order_alive[ask_quote].copy()
                            pre_cancel_alive[ask_quote]['UnixStamp'] = j['UnixStamp'] 
            
            if j['quote_status'] == order_status.pending_delete.value:
                bid_order_id = j['internal_quote_id']+2
                ask_order_id = j['internal_quote_id']+1   
                if not j['bid_order_status'] == order_status.all_traded.value:
                    if not bid_order_id in pre_cancel_alive.keys():
                        j1 = j.copy()
                        j1['internal_order_id'] = bid_order_id
                        j1['long_short'] = 0                    
                        pre_cancel_alive[bid_order_id] = j1                                    
                if not j['ask_order_status'] == order_status.all_traded.value:
                    if not ask_order_id in pre_cancel_alive.keys():
                        j2 = j.copy()
                        j2['internal_order_id'] = ask_order_id
                        j2['long_short'] = 1                  
                        pre_cancel_alive[ask_order_id] = j2 
            
            if j['quote_status'] == order_status.over_flow.value:
                bid_order_id = j['internal_quote_id']+2
                ask_order_id = j['internal_quote_id']+1   
                if bid_order_id in pre_order_alive.keys():
                    t1 = pre_order_alive[bid_order_id]['UnixStamp']
                    t2 = j['UnixStamp']
                    pre_order_delay.append((t1, t2-t1, 1, bid_order_id)) # 1代表发单失败                   
                if not ask_order_id in pre_order_alive.keys():
                    t1 = pre_order_alive[bid_order_id]['UnixStamp']
                    t2 = j['UnixStamp']
                    pre_order_delay.append((t1, t2-t1, 1, ask_order_id)) # 1代表发单失败     

            j = next(df_quote, None)  


        elif j == None:
            if i['order_status'] == order_status.pending_add.value:
                order_id = i['internal_order_id']
                if not order_id in pre_order_alive.keys():
                    pre_order_alive[order_id] = i
                    
            if i['order_status'] == order_status.pending_delete.value:
                order_id = i['internal_order_id']
                if not order_id in pre_cancel_alive.keys():        
                    pre_cancel_alive[order_id] = i
                    
            if i['order_status'] == order_status.deleted.value:
                order_id = i['internal_order_id']
                if order_id in pre_cancel_alive.keys():
                    t1 = pre_cancel_alive[order_id]['UnixStamp']
                    t2 = i['UnixStamp']
                    pre_cancel_delay.append((t1, t2-t1, 0, order_id))
                    pre_cancel_alive.pop(order_id)
                if order_id in bid_order_alive.keys():
                    bid_order_alive.pop(order_id)
                if order_id in ask_order_alive.keys():
                    ask_order_alive.pop(order_id)
                if order_id in bid_quote_alive.keys():
                    bid_quote_alive.pop(order_id)
                if order_id in ask_quote_alive.keys():
                    ask_quote_alive.pop(order_id)
                    
            if i['order_status'] == order_status.exchange_order.value:
                order_id = i['internal_order_id']
                long_short = i['long_short']
                if order_id in pre_order_alive.keys():
                    t1 = pre_order_alive[order_id]['UnixStamp']
                    t2 = i['UnixStamp']
                    pre_order_delay.append((t1, t2-t1, 0, order_id))
                    pre_order_alive.pop(order_id)  

                if long_short == 0 and not order_id in bid_order_alive.keys():
                    bid_order_alive[order_id] = i
                if long_short == 1 and not order_id in ask_order_alive.keys():
                    ask_order_alive[order_id] = i 
                    
                if order_id in internal_quote_id_list:
                    if long_short == 0 and not order_id in bid_quote_alive.keys():
                        bid_quote_alive[order_id] = i
                        internal_quote_id_list.remove(order_id)
                    if long_short == 1 and not order_id in ask_quote_alive.keys():
                        ask_quote_alive[order_id] = i 
                        internal_quote_id_list.remove(order_id)
                        
                    
            if i['order_status'] in [order_status.partial_traded.value, order_status.all_traded.value]:                
                order_id = i['internal_order_id']
                if not order_id in trade_id.keys():
                    count1 += 1
                    trade_id[order_id] = i                    
                if order_id in pre_cancel_alive.keys():
                    if not order_id in cancel_slow.keys():
                        count2 += 1
                        cancel_slow[order_id] = i                    
                    t1 = pre_cancel_alive[order_id]['UnixStamp']
                    t2 = i['UnixStamp']
                    pre_cancel_delay.append((t1, t2-t1, 1, order_id)) # 1代表撤单失败
                    pre_cancel_alive.pop(order_id)    
                if i['order_status'] == order_status.all_traded.value:
                    if order_id in bid_order_alive.keys():
                        bid_order_alive.pop(order_id)
                    if order_id in ask_order_alive.keys():
                        ask_order_alive.pop(order_id)     
                    if order_id in bid_quote_alive.keys():
                        bid_quote_alive.pop(order_id)
                    if order_id in ask_quote_alive.keys():
                        ask_quote_alive.pop(order_id)    
                    
            i = next(df_order, None)


        elif i['UnixStamp'] <= j['UnixStamp']:
            if i['order_status'] == order_status.pending_add.value:
                order_id = i['internal_order_id']
                if not order_id in pre_order_alive.keys():
                    pre_order_alive[order_id] = i
                    
            if i['order_status'] == order_status.pending_delete.value:
                order_id = i['internal_order_id']
                if not order_id in pre_cancel_alive.keys():        
                    pre_cancel_alive[order_id] = i
                    
            if i['order_status'] == order_status.deleted.value:
                order_id = i['internal_order_id']
                if order_id in pre_cancel_alive.keys():
                    t1 = pre_cancel_alive[order_id]['UnixStamp']
                    t2 = i['UnixStamp']
                    pre_cancel_delay.append((t1, t2-t1, 0, order_id))
                    pre_cancel_alive.pop(order_id)
                if order_id in bid_order_alive.keys():
                    bid_order_alive.pop(order_id)
                if order_id in ask_order_alive.keys():
                    ask_order_alive.pop(order_id)
                if order_id in bid_quote_alive.keys():
                    bid_quote_alive.pop(order_id)
                if order_id in ask_quote_alive.keys():
                    ask_quote_alive.pop(order_id)
                    
            if i['order_status'] == order_status.exchange_order.value:
                order_id = i['internal_order_id']
                long_short = i['long_short']
                if order_id in pre_order_alive.keys():
                    t1 = pre_order_alive[order_id]['UnixStamp']
                    t2 = i['UnixStamp']
                    pre_order_delay.append((t1, t2-t1, 0, order_id))
                    pre_order_alive.pop(order_id)  

                if long_short == 0 and not order_id in bid_order_alive.keys():
                    bid_order_alive[order_id] = i
                if long_short == 1 and not order_id in ask_order_alive.keys():
                    ask_order_alive[order_id] = i 
                    
                if order_id in internal_quote_id_list:
                    if long_short == 0 and not order_id in bid_quote_alive.keys():
                        bid_quote_alive[order_id] = i
                        internal_quote_id_list.remove(order_id)
                    if long_short == 1 and not order_id in ask_quote_alive.keys():
                        ask_quote_alive[order_id] = i 
                        internal_quote_id_list.remove(order_id)
                    
            if i['order_status'] in [order_status.partial_traded.value, order_status.all_traded.value]:
                order_id = i['internal_order_id']
                if not order_id in trade_id.keys():
                    count1 += 1
                    trade_id[order_id] = i                    
                if order_id in pre_cancel_alive.keys():
                    if not order_id in cancel_slow.keys():
                        count2 += 1
                        cancel_slow[order_id] = i                    
                    t1 = pre_cancel_alive[order_id]['UnixStamp']
                    t2 = i['UnixStamp']
                    pre_cancel_delay.append((t1, t2-t1, 1, order_id)) # 1代表撤单失败
                    pre_cancel_alive.pop(order_id)    
                if i['order_status'] == order_status.all_traded.value:
                    if order_id in bid_order_alive.keys():
                        bid_order_alive.pop(order_id)
                    if order_id in ask_order_alive.keys():
                        ask_order_alive.pop(order_id)     
                    if order_id in bid_quote_alive.keys():
                        bid_quote_alive.pop(order_id)
                    if order_id in ask_quote_alive.keys():
                        ask_quote_alive.pop(order_id)    
                    
            i = next(df_order, None)
            
        elif i['UnixStamp'] > j['UnixStamp']:
            if j['quote_status'] == order_status.pending_add.value:
                bid_order_id = j['internal_quote_id']+2
                ask_order_id = j['internal_quote_id']+1
                internal_quote_id_list.append(bid_order_id)
                internal_quote_id_list.append(ask_order_id)
                if not bid_order_id in pre_order_alive.keys():
                    j1 = j.copy()
                    j1['internal_order_id'] = bid_order_id
                    j1['long_short'] = 0                    
                    pre_order_alive[bid_order_id] = j1   

                if not ask_order_id in pre_order_alive.keys():
                    j2 = j.copy()
                    j2['internal_order_id'] = ask_order_id
                    j2['long_short'] = 1                    
                    pre_order_alive[ask_order_id] = j2   
                    
                bid_quote = bid_quote_alive
                ask_quote = ask_quote_alive
                
                if bid_quote_alive:
                    for bid_quote in bid_quote_alive.keys():
                        if not bid_quote in pre_cancel_alive.keys():
                            pre_cancel_alive[bid_quote] = bid_order_alive[bid_quote].copy()
                            pre_cancel_alive[bid_quote]['UnixStamp'] = j['UnixStamp']
                if ask_quote_alive: 
                    for ask_quote in ask_quote_alive.keys():
                        if not ask_quote in pre_cancel_alive.keys():
                            pre_cancel_alive[ask_quote] = ask_order_alive[ask_quote].copy()
                            pre_cancel_alive[ask_quote]['UnixStamp'] = j['UnixStamp'] 
            
            if j['quote_status'] == order_status.pending_delete.value:
                bid_order_id = j['internal_quote_id']+2
                ask_order_id = j['internal_quote_id']+1   
                if not j['bid_order_status'] == order_status.all_traded.value:
                    if not bid_order_id in pre_cancel_alive.keys():
                        j1 = j.copy()
                        j1['internal_order_id'] = bid_order_id
                        j1['long_short'] = 0                    
                        pre_cancel_alive[bid_order_id] = j1                                    
                if not j['ask_order_status'] == order_status.all_traded.value:
                    if not ask_order_id in pre_cancel_alive.keys():
                        j2 = j.copy()
                        j2['internal_order_id'] = ask_order_id
                        j2['long_short'] = 1                  
                        pre_cancel_alive[ask_order_id] = j2 
            
            if j['quote_status'] == order_status.over_flow.value:
                bid_order_id = j['internal_quote_id']+2
                ask_order_id = j['internal_quote_id']+1   
                if bid_order_id in pre_order_alive.keys():
                    t1 = pre_order_alive[bid_order_id]['UnixStamp']
                    t2 = j['UnixStamp']
                    pre_order_delay.append((t1, t2-t1, 1, bid_order_id)) # 1代表发单失败                   
                if not ask_order_id in pre_order_alive.keys():
                    t1 = pre_order_alive[bid_order_id]['UnixStamp']
                    t2 = j['UnixStamp']
                    pre_order_delay.append((t1, t2-t1, 1, ask_order_id)) # 1代表发单失败     

            j = next(df_quote, None)  
    print(iid+'延迟统计完毕', time.time()-t)
    
    
    

    result[iid] = {'iid':iid, 'pre_order_delay':pre_order_delay, 'pre_order_alive':pre_order_alive, \
                   'pre_cancel_delay':pre_cancel_delay, 'pre_cancel_alive':pre_cancel_alive, \
                   'internal_quote_id_list':internal_quote_id_list, 'cancel_slow': cancel_slow, \
                   'trade_id': trade_id, 'net': net, 'pnl': pnl}

#%%
result0825 = result
result0903 = result


#%%
iid = 'pg2111'
pre_order_delay = result[iid]['pre_order_delay']
pre_cancel_delay = result[iid]['pre_cancel_delay']
trade_id = result[iid]['trade_id']
cancel_slow = result[iid]['cancel_slow']
print(iid, len(cancel_slow)/len(trade_id))


pre_order_delay = pd.DataFrame(pre_order_delay)
pre_cancel_delay = pd.DataFrame(pre_cancel_delay)

pre_order_delay.columns = ['UnixStamp', 'delay', 'type', 'order_id']
pre_cancel_delay.columns = ['UnixStamp', 'delay', 'type', 'order_id']

pre_order_delay.index = pre_order_delay.UnixStamp
pre_cancel_delay.index = pre_cancel_delay.UnixStamp

print('pre_order_delay', pre_order_delay.delay.describe())
print('pre_cancel_delay', pre_cancel_delay.delay.describe())
#%%
plt.figure()
plt.hist(pre_order_delay.delay, bins = 1000, range = (0, 0.1), density=True, label = 'pre_order_delay', alpha = 0.7)
plt.hist(pre_cancel_delay.delay, bins = 1000, range = (0, 0.1), density=True, label = 'pre_cancel_delay', alpha = 0.7)
plt.title(iid)
plt.legend()
plt.show()

#%%
plt.figure()
plt.plot(pre_order_delay.index, pre_order_delay.delay, label = 'pre_order_delay', alpha = 0.7)
plt.plot(pre_cancel_delay.index, pre_cancel_delay.delay, label = 'pre_cancel_delay', alpha = 0.7)
plt.legend()
plt.show()




#%%
result1 = result

#%%
for key in result.keys():
    print(result[key]['pre_cancel_delay']==result1[key]['pre_cancel_delay'])
    print(result[key]['pre_order_delay']==result1[key]['pre_order_delay'])
    
#%%
for iid in iid_list:
    try:
        pre_cancel_delay0825 = result0825[iid]['pre_cancel_delay']
        pre_cancel_delay0903 = result0903[iid]['pre_cancel_delay']
        trade_id0825 = result0825[iid]['trade_id']
        trade_id0903 = result0903[iid]['trade_id']
        cancel_slow0825 = result0825[iid]['cancel_slow']
        cancel_slow0903 = result0903[iid]['cancel_slow']
        print(iid, '0825', len(cancel_slow0825)/len(trade_id0825), '0903', len(cancel_slow0903)/len(trade_id0903))
    except:
        pass
#%%
iid = 'rb2203'
pre_cancel_delay0825 = result0825[iid]['pre_cancel_delay']
pre_cancel_delay0903 = result0903[iid]['pre_cancel_delay']

pre_cancel_delay0825 = pd.DataFrame(pre_cancel_delay0825)
pre_cancel_delay0903 = pd.DataFrame(pre_cancel_delay0903)

pre_cancel_delay0825.columns = ['UnixStamp', 'delay', 'type', 'order_id']
pre_cancel_delay0903.columns = ['UnixStamp', 'delay', 'type', 'order_id']

pre_cancel_delay0825.index = pre_cancel_delay0825.UnixStamp
pre_cancel_delay0903.index = pre_cancel_delay0903.UnixStamp

#%%
plt.figure()
plt.hist(pre_cancel_delay0825.delay, bins = 1000, range = (0, 0.1), density=True, label = '0825', alpha = 0.7)
plt.hist(pre_cancel_delay0903.delay, bins = 1000, range = (0, 0.1), density=True, label = '0903', alpha = 0.7)
plt.title(iid)
plt.legend()
plt.show()






