from scipy.optimize import minimize
import numpy as np
from datetime import datetime as dt
import pandas as pd

# 剔除周末，计算年化到期时间（仅计入工作日）
def count_weekdays(start, end):
    if isinstance(start,str) or isinstance(end,str):
        return np.busday_count(start.date(), end.date())
    # 直接将 end 转为 numpy 数组以提升速度，避免列表推导
    end_dates = pd.to_datetime(end).date if isinstance(end, (pd.Series, pd.Index, np.ndarray, list)) else [pd.to_datetime(e).date() for e in end]
    return np.busday_count(start.date(), end_dates)


def daytimeleft():
    now = dt.now()
    begin_time = now.replace(hour=9, minute=30, second=0, microsecond=0)
    end_time = now.replace(hour=15, minute=0, second=0, microsecond=0)
    total_time = 6
    if now>end_time:
        days_left = 0
    elif now<begin_time:
        days_left = 1
    else:
        days_left = (end_time - now).total_seconds() / (3600 * total_time)

    return days_left

def get_data(exp_data,exe_cut=100):
    # 准备输入数据
    exp_data_raw=exp_data.copy()
    cutmin = exp_data['forward']/exe_cut
    cutmax = exp_data['forward']*exe_cut
    exp_data = exp_data.loc[(exp_data['K'] >= cutmin) & (exp_data['K'] <= cutmax)]

    spread=exp_data['tv'] - exp_data['neg_tv']
    # 找到spread绝对值最小的索引（即最接近平价的行）
    atmx = spread.abs().idxmin()
    q = exp_data['rf'] - np.log((spread*np.exp(exp_data['rf']*exp_data['time2expiry'])+atmx)/exp_data['spot'])/exp_data['time2expiry']

    F = exp_data.loc[atmx, 'forward']
    K = exp_data['K']
    T = exp_data['time2expiry'].iloc[0]
    imp_vol = exp_data['market_sigma']
    x = np.log(K/F)
    x_raw = np.log(exp_data_raw['K']/F)
    totalv = np.square(imp_vol)*T
    
    # 修改权重计算，bid=ask时权重设为0
    weights = exp_data['vega'].values
    weights = np.where(exp_data['bid'] == exp_data['ask'], 0, weights)

    return F,K,T,x,totalv,imp_vol,weights,x_raw


def find_best_method(objective, initial_params, bounds,verbose=False,method=None):
    # 使用多种优化方法尝试拟合，提高成功率
    methods = ['L-BFGS-B', 'SLSQP', 'Nelder-Mead','Powell']
    best_result = None
    best_rmse = float('inf')
    
    # 尝试多种方法，选择RMSE最小的结果
    best_method = None
    if method is None:
        for method in methods:
            try:
                result = opt_method(objective, initial_params, bounds,method)
                if verbose:
                    print(method,f'{result.fun:.1e}')
                if result.success and result.fun < best_rmse:
                    best_result = result
                    best_rmse = result.fun
                    best_method = method
            except Exception as e:
                print(f"{method} 优化失败: {str(e)}")
                continue 
    else:
        if verbose:
            print('single method',method)
        result = opt_method(objective, initial_params, bounds,method)
        if result.success and result.fun < best_rmse:
            best_result = result
            best_rmse = result.fun
            best_method = method
    if verbose:
        print(f'best method: {best_method}, best rmse: {best_rmse:.1e}')
    
    return best_result, best_rmse, best_method

def opt_method(objective, initial_params, bounds,method):
    # 根据不同方法设置不同的选项
    if method == 'Nelder-Mead':
        # Nelder-Mead不支持bounds和ftol选项
        result = minimize(
            objective,
            initial_params,
            method=method,
            options={'maxiter': 1000, 'xatol': 1e-8}
        )
    elif method == 'Powell':
        # Powell支持bounds选项
        result = minimize(
            objective,
            initial_params,
            bounds=bounds,
            method=method,
            options={'maxiter': 1000, 'ftol': 1e-8}
        )
    else:
        # L-BFGS-B和SLSQP支持bounds
        result = minimize(
            objective,
            initial_params,
            bounds=bounds,
            method=method,
            options={'maxiter': 1000, 'ftol': 1e-8}
        )
    
    return result



def _get_smart_initial_params(self):
    """基于市场数据智能选择初始参数"""
    # 基于ATM波动率估计a参数
    atm_idx = np.argmin(np.abs(self.x))
    atm_totalv = self.totalv[atm_idx] if len(self.totalv) > atm_idx else np.mean(self.totalv)
    
    # a: 设为ATM总方差的80%
    a_init = 0.8 * atm_totalv
    
    # b: 基于总方差的变化范围
    b_init = min(0.1, (np.max(self.totalv) - np.min(self.totalv)) / 4)
    
    # rho: 基于偏斜的初步估计
    left_vol = np.mean(self.totalv[self.x < -0.1]) if np.any(self.x < -0.1) else atm_totalv
    right_vol = np.mean(self.totalv[self.x > 0.1]) if np.any(self.x > 0.1) else atm_totalv
    rho_init = np.clip((right_vol - left_vol) / (right_vol + left_vol), -0.5, 0.5)
    
    # m: 设为x的中位数
    m_init = np.median(self.x)
    
    # sigma: 基于x的标准差
    sigma_init = min(0.2, np.std(self.x))
    
    return [a_init, b_init, rho_init, m_init, sigma_init]