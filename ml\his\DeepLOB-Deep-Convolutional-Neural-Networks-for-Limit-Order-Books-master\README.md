# DeepLOB-Deep-Convolutional-Neural-Networks-for-Limit-Order-Books
This jupyter notebook is used to demonstrate our recent work, "DeepLOB: Deep Convolutional Neural Networks for Limit Order Books", published in IEEE Transactions on Singal Processing. We use FI-2010 dataset and present how model architecture is constructed here. The FI-2010 is publicly available and interested readers can check out their paper.

Both tensorflow (version 1 and 2) and pytorch are available. 