# -*- coding: utf-8 -*-
"""
Created on Fri Aug  6 14:21:46 2021

@author: yhw52174
"""

from influxdb import InfluxDBClient
from OmmDatabase import OmmDatabase
import datetime
import time
import numpy as np
import pandas as pd

#%%
# for i in range(10):
#     print(i)
#     time.sleep(1)

client = InfluxDBClient('localhost',8086,'','','test')

#%%
date = '2021-07-13'
db_path = 'C:/Users/<USER>/Desktop/shfe/' + date + '/shfe_' + date + '/shfe/'
testDB = OmmDatabase(db_path)

#%%
# MarketDataService
df30 = testDB.read_file(db_path + 'MarketDataService/default_mkt/HC/', date)   
df30['UnixStamp'] = df30['SystemStamp'].apply(lambda x:datetime.datetime.strptime(str(x),'%Y%m%d%H%M%S%f').timestamp())
df30['UnixStamp'] = df30['UnixStamp'] * 10**9
df30['UnixStamp'] = df30['UnixStamp'].apply(lambda x: int(x))
# df30[df30.isnull().T.any()]
df30.fillna(-1, inplace = True)


#%%


for i, row in df30[:].iterrows():
    json_body = []
    t = time.time()
    current_time = int(t*10**9)
    
    measurement = 'testHC'
    
    a_p1 = float(row['Ask1Price'])
    a_v1 = int(row['Ask1Volume'])
    a_p2 = float(row['Ask2Price'])
    a_v2 = int(row['Ask2Volume'])    
    a_p3 = float(row['Ask3Price'])
    a_v3 = int(row['Ask3Volume'])        
    a_p4 = float(row['Ask4Price'])
    a_v4 = int(row['Ask4Volume'])        
    a_p5 = float(row['Ask5Price'])
    a_v5 = int(row['Ask5Volume'])    
    
    b_p1 = float(row['Bid1Price'])
    b_v1 = int(row['Bid1Volume'])
    b_p2 = float(row['Bid2Price'])
    b_v2 = int(row['Bid2Volume'])    
    b_p3 = float(row['Bid3Price'])
    b_v3 = int(row['Bid3Volume'])    
    b_p4 = float(row['Bid4Price'])
    b_v4 = int(row['Bid4Volume'])    
    b_p5 = float(row['Bid5Price'])
    b_v5 = int(row['Bid5Volume']) 
    
    exchange_t = 0    
    insid_md = str(row['instrumentId'])
    last_p = float(row['lastPriceOnMarket'])
    local_t = row['UnixStamp']
    lower_limit_p = row['lowerLimit']
    preclose_p = 0
    presettle_p = 0
    turnover = float(row['turnover'])
    upper_limit_p = float(row['upperLimit'])
    v = int(row['tradedVolume'])
       
    body = {
            "measurement": measurement, 
            "time": current_time, 
            "tags": {
                "insid_md": insid_md
            }, 
            "fields": {
                "a_p1": a_p1, 
                "a_p2": a_p2,                 
                "a_p3": a_p3,                 
                "a_p4": a_p4,                 
                "a_p5": a_p5,                 
                "a_v1": a_v1,                 
                "a_v2": a_v2,                    
                "a_v3": a_v3,                    
                "a_v4": a_v4,                    
                "a_v5": a_v5,                    
                "b_p1": b_p1, 
                "b_p2": b_p2,                 
                "b_p3": b_p3,                 
                "b_p4": b_p4,                 
                "b_p5": b_p5,                 
                "b_v1": b_v1,                 
                "b_v2": b_v2,                    
                "b_v3": b_v3,                    
                "b_v4": b_v4,                    
                "b_v5": b_v5, 
                "exchange_t": exchange_t,               
                "last_p": last_p,
                "local_t": local_t,
                "lower_limit_p": lower_limit_p,
                "preclose_p": preclose_p,
                "presettle_p": presettle_p,
                "turnover": turnover,
                "upper_limit_p": upper_limit_p,
                "v": v
            }, 
        }
    
    
    json_body.append(body)
    res = client.write_points(json_body, batch_size = 10000)
    time.sleep(0.5+0.02*np.random.randn())
    if t > 1628559600:
        print(t)
        break
    

    
#%%
result = client.query("select * from testHC;") 
# 输出
points = result.get_points()
list1 = []
for d in points:
    list1.append(d)
ll = pd.DataFrame(list1)


#%%    
time.time()


















