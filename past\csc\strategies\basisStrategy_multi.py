# -*- coding: utf-8 -*-
 
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta
 
from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData
 
from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random
import math 
from vnpy.trader.utility import round_to, extract_vt_symbol
 
class BasisStrategy(StrategyTemplate):
    """"""
 
    author = "vnpy"
 
    buy_price = 0
    short_price = 0
 
    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'makers',
        'refer',
        'priceticks',
        'edge',
        'basis_alpha',
        'vol_alpha'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]
 
    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)
        
        #self.makers = [extract_vt_symbol(vt_symbol)[0] for vt_symbol in self.makers]
        
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair

    def Validtick(self,tick): #无效的tick不计算ewma
        if tick.ask_price_1 - tick.bid_price_1 > self.edge[tick.vt_symbol]:
            return False
        return True

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.MMTrading = False
        self.load_ticks(0)
        self.avg = 0
        self.fair_refer = 0
        self.rCount=0
        self.pauseCount=0
        self.lastRefer={"net":0, "tick":None, "timepoint":0}        
        self.lastMaker={"net":0, "tick":None, "timepoint":0}
        self.isQuoting=False
        all_ins = self.makers.copy()
        all_ins.extend([self.refer]) 
        self.lastTicks={ins:0 for ins in all_ins}
        self.Basis={}
        self.Basis_Ewma = {}
        self.Vol = {}
        self.Vol_Ewma = {}
        self.isValid = {}
        self.last_bidP = {}
        self.last_askP = {}
        self.buy_vt_orderids = {}
        self.sell_vt_orderids = {}
        self.buy_hedge_orderids = {}
        self.short_hedge_orderids = {}
        self.fair_maker_list = {}
        self.mCount = {}
        self.fair_maker={}
        self.net={}
        self.last_hedge_buy={}
        self.last_hedge_sell={}
        self.validcount = {} #记录有效次数
        for symbol in all_ins:
            self.Basis_Ewma[symbol] = math.nan
            self.Basis[symbol] = 0
            self.Vol[symbol] = 0
            self.Vol_Ewma[symbol] = math.nan
            self.mCount[symbol] = 0
            self.buy_vt_orderids[symbol] = None
            self.sell_vt_orderids[symbol] = None
            self.buy_hedge_orderids[symbol] = None
            self.short_hedge_orderids[symbol] = None
            self.last_bidP[symbol] = 0
            self.last_askP[symbol] = 99999
            self.last_hedge_buy[symbol] = 0
            self.last_hedge_sell[symbol] = 99999
            self.fair_maker[symbol] = 0
            self.fair_maker_list[symbol] = np.zeros(99999)
            self.net[symbol] = 0
            self.isValid[symbol] = False
            self.validcount[symbol] = 0 
        self.theos={}
        self.pauseCount=0
        self.isQuoting = True
        self.offer_list = []
        self.refer_hedge_buy=None
        self.refer_hedge_sell=None
 
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")
 
    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")
 
    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.refer:
                    self.on_tick_refer(ticks[key])
                else:
                    self.on_tick_maker(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick
        
    def on_tick_refer(self,tick):
        if self.MMTrading:
            self.rCount +=1  
            self.on_position() #先平delta
        
        ts = self.priceticks[self.refer]
        #net = self.net[maker]  
        self.fair_refer = (tick.bid_price_1 * tick.ask_volume_1 + tick.ask_price_1 * tick.bid_volume_1) / (tick.bid_volume_1 + tick.ask_volume_1) 
        
        shortResendFlag=False
        buyResendFlag=False
        
        #1. Filter ：非可控情景暂停 
        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
            # 持仓超限暂停 、在途订单超限暂停、
            #if abs(net)>self.maxPos:
                #self.isQuoting=False 
                #self.pauseCount=self.rCount+20      
                #print("Net Position limit pause",tick.datetime+timedelta(hours=8))
            if len(self.strategy_engine.active_limit_orders)>10:     #在途订单超限暂停 or mdDelay>mdDelayLimit or tdDelay>tdDelayLimit 
                self.isQuoting=False 
                self.pauseCount=self.rCount+10    
                print("Delay limit pause",tick.datetime+timedelta(hours=8))
            # market cross pause
            #if self.rCount>5 and (tick.ask_price_1 <= self.lastRefer["tick"].bid_price_1 -ts or tick.bid_price_1 >= self.lastRefer["tick"].ask_price_1 +ts):
                #self.isQuoting=False 
                #self.pauseCount=self.rCount+20    
                #print("maker gap limit pause",tick.datetime+timedelta(hours=8)) #   
            # near to market limit price  
            if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (
                tick.bid_price_1 > float(tick.limit_up) - 10*ts or  # 涨停附近                   
                tick.ask_price_1 < float(tick.limit_down) + 10*ts):   # 跌停附近                  
                    self.isQuoting=False
                    self.pauseCount=self.rCount + 100
                    print("price limit pause", tick.datetime+timedelta(hours=8))
        else:
            #if abs(net)<self.maxnet and self.pauseCount<self.rCount: #TODO 持仓检查
            self.isQuoting=True 
                
        # 2. Quote：报价
        for maker in self.makers:

            if self.rCount > 10 and self.isValid[maker]: #报价时间内开始定价
                self.Basis[maker] = self.fair_maker[maker] - self.fair_refer
                self.Vol[maker] = np.var(self.fair_maker_list[maker][self.mCount[maker] - 4:self.mCount[maker]+1])
                if math.isnan(self.Basis_Ewma[maker]) :
                    self.Basis_Ewma[maker] = self.Basis[maker]
                else:
                    self.Basis_Ewma[maker] = (1-self.basis_alpha) * self.Basis_Ewma[maker] + self.basis_alpha * self.Basis[maker]
                if math.isnan(self.Vol_Ewma[maker]) :
                    self.Vol_Ewma[maker] = self.Vol[maker]
                else:
                    self.Vol_Ewma[maker] = (1-self.vol_alpha) * self.Vol_Ewma[maker] + self.vol_alpha * self.Vol[maker]
                
                self.validcount[maker] +=1
            
            if self.validcount[maker] > 1/self.basis_alpha:  #开始报价 （若alpha = 0.02,则用50个tick计算初始Basis）
            
                self.theos[maker] = self.fair_refer + self.Basis_Ewma[maker]
                theo = self.theos[maker]
                vol = max(3*np.sqrt(self.Vol_Ewma[maker]),0.25*self.edge[maker])
                basisRisk = sum(self.net.values()) - self.net[self.refer]
                avgLots= sum(self.lots.values())/len(self.makers)
                pos_adjust = -0.5*(basisRisk/avgLots)*ts
                bidP = theo - vol + pos_adjust
                askP = theo + vol + pos_adjust    

                bidP = ts*round(bidP/ts) 
                askP = ts*round(askP/ts)  
    
                if self.last_askP[maker] != askP:
                    shortResendFlag=True
                if self.last_bidP[maker] != bidP:
                    buyResendFlag=True
    
                self.last_bidP[maker] = bidP
                self.last_askP[maker] = askP
                
                comments = 'MM'
                try:
                    if self.isValid[maker]:
                        if (not self.MMTrading and self.buy_vt_orderids[maker]) or (self.MMTrading and self.buy_vt_orderids[maker] and buyResendFlag):
                            cancelResult = self.cancel_order(self.buy_vt_orderids[maker][0])
                        else:
                            cancelResult = True
                        if ((self.MMTrading and not self.buy_vt_orderids[maker]) or (self.MMTrading and self.buy_vt_orderids[maker] and buyResendFlag)) and cancelResult and self.net[maker] < self.maxPos:
                            self.buy_vt_orderids[maker] = self.buy(maker,bidP, self.lots[maker],comments)
                        if (not self.MMTrading and self.sell_vt_orderids[maker]) or (self.MMTrading and self.sell_vt_orderids[maker] and shortResendFlag):
                            cancelResult = self.cancel_order(self.sell_vt_orderids[maker][0])
                        else:
                            cancelResult = True
                        if ((self.MMTrading and not self.sell_vt_orderids[maker]) or (self.MMTrading and self.sell_vt_orderids[maker] and shortResendFlag)) and cancelResult and self.net[maker] > -self.maxPos:
                            self.sell_vt_orderids[maker] = self.short(maker,askP, self.lots[maker],comments)
                except:
                    pass
    
        # 3. Save：数据存储                    
                
        self.lastRefer["timepoint"]=tick.datetime+timedelta(hours=8)
        self.lastRefer["net"]=self.net
        self.lastRefer["tick"]=tick
            
    def on_tick_maker(self, tick):
        maker = tick.vt_symbol
        self.mCount[maker] += 1
        self.isValid[maker] = self.Validtick(tick)
        self.fair_maker[maker] = self.getFairPrice(self.fair_maker[maker],tick.ask_price_1,tick.bid_price_1, self.edge[maker]) 
        self.fair_maker_list[maker][self.mCount[maker]] = self.fair_maker[maker]

        
        if (tick.datetime+timedelta(hours=8)).time() > dtime(14, 55) and (tick.datetime+timedelta(hours=8)).time() < dtime(15, 00): #influxdb时间落后8小时 ，实际是22.57到22.58清持仓
            if self.net[maker]!= 0:  #不持仓过夜
                #self.cancel_all()
                if self.net[maker] >0:
                    price = tick.bid_price_1
                    if self.short_hedge_orderids[maker] and self.last_hedge_sell[maker]==price:
                        return
                    if self.short_hedge_orderids[maker] and self.last_hedge_sell[maker]!=price:
                        self.cancel_order(self.short_hedge_orderids[maker][0])
                    volume = abs(self.net[maker])
                    self.short_hedge_orderids[maker] = self.short(maker,price,volume,'hedge')
                    self.last_hedge_sell[maker]=price
                    print( self.lastRefer["timepoint"],'close time hedge sell:',maker,price,volume)
                else:
                    price = tick.ask_price_1
                    if self.buy_hedge_orderids[maker] and self.last_hedge_buy[maker]==price:
                        return
                    if self.buy_hedge_orderids[maker] and self.last_hedge_buy[maker]!=price:
                        self.cancel_order(self.buy_hedge_orderids[maker][0])
                    volume = abs(self.net[maker])
                    self.buy_hedge_orderids[maker] = self.buy(maker,price,volume,'hedge')
                    self.last_hedge_buy[maker]=price
                    print( self.lastRefer["timepoint"],'close time hedge buy:',maker,price,volume)
                    
        # #### Algo : JoinMarket  ~~~~~~~无效价格情况暂未处理~~~~~~~~~~~~~~~~~~ 
        # spread = tick.ask_price_1 - tick.bid_price_1       
        #if self.rCount > 60:
            #self.offer_list.append([tick.datetime,self.theos[maker],bidP,self.askP])
        
    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        maker = trade.symbol+'.'+trade.exchange.value
        trade.basePrice = self.fair_refer
        trade.midPrice = self.fair_maker[maker]
        if volume > 0:
            if trade.direction.value=='多':
                if self.net[maker]>=0 :            
                     self.avg = (self.net[maker]*self.avg +volume*price)/(self.net[maker]+volume)              
                elif volume+self.net[maker]>0: # net<0 # 平仓`
                     self.avg = price         
                self.net[maker] += volume 
            #         
            elif trade.direction.value=='空':    
                if self.net[maker]<=0:
                    self.avg =(-self.net[maker]*self.avg + volume*price)/(-self.net[maker]+volume)
                elif volume-self.net[maker]>0: # net >0 # 平仓
                    self.avg=price
                self.net[maker] -= volume  
        self.on_position()
        
    def on_position(self):
        #持仓管理，delta暴露<100张
        deltaRisk = sum(self.pos.values())
        riskLimit = 20
        if deltaRisk>riskLimit:
            if self.short_hedge_orderids[self.refer] and self.short_hedge_orderids[self.refer][0] and self.lastRefer["tick"].bid_price_1==self.lastReferSellPrice:
                return
            elif self.short_hedge_orderids[self.refer] and self.short_hedge_orderids[self.refer][0] and self.lastRefer["tick"].bid_price_1!=self.lastReferSellPrice:
                self.cancel_order(self.short_hedge_orderids[self.refer][0])
            comments='referHedge'
            volume = abs(deltaRisk-riskLimit)
            price = self.lastRefer["tick"].bid_price_1
            self.short_hedge_orderids[self.refer] = self.short(self.refer, price, volume,comments)
            self.lastReferSellPrice = price
            print( self.lastRefer["timepoint"],'hedge sell:',self.refer,price,volume)
        elif deltaRisk<-riskLimit:
            if self.buy_hedge_orderids[self.refer] and self.buy_hedge_orderids[self.refer][0] and self.lastRefer["tick"].ask_price_1==self.lastReferBuyPrice:
                return 
            elif self.buy_hedge_orderids[self.refer] and self.buy_hedge_orderids[self.refer][0] and self.lastRefer["tick"].ask_price_1!=self.lastReferBuyPrice:
                self.cancel_order(self.buy_hedge_orderids[self.refer][0])
            comments='referHedge'
            volume = abs(deltaRisk-riskLimit)
            price = self.lastRefer["tick"].ask_price_1
            self.buy_hedge_orderids[self.refer] = self.buy(self.refer, price, volume,comments)
            self.lastReferBuyPrice=price
            print( self.lastRefer["timepoint"],'hedge buy:',self.refer,price,volume)
            
    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return
        maker = order.symbol+'.'+order.exchange.value
        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids[maker],
            self.sell_vt_orderids[maker],
            self.buy_hedge_orderids[maker],
            self.short_hedge_orderids[maker]
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
 
    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """ # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if (
            ((dt+timedelta(hours=8)).time() > dtime(21, 5) and (dt+timedelta(hours=8)).time() < dtime(22, 55))
            or ( (dt+timedelta(hours=8)).time() > dtime(9, 5) and (dt+timedelta(hours=8)).time() < dtime(10, 13))
            or ( (dt+timedelta(hours=8)).time() > dtime(10, 32) and (dt+timedelta(hours=8)).time() < dtime(11, 28))
            or ( (dt+timedelta(hours=8)).time() > dtime(13, 35) and (dt+timedelta(hours=8)).time() < dtime(14, 55))
        ):
            self.MMTrading = True
        else:
            self.MMTrading = False