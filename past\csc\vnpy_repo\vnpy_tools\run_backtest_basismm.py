#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
# ip.set_ip('CZCE.dev')
ip.set_ip('CZCE.dev')
import warnings
warnings.filterwarnings('ignore')
 
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.basisStrategy import BasisStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
 
#%%
# maker='RM207.CZCE'
# refer='RM205.CZCE'
 
maker='CF207.CZCE'
refer='CF209.CZCE'
 
multiplier=5
 
engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=True,duty=True,save_result=False,refer_test=True,fast_join=True,refer_late=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
 
    start=datetime.datetime(2022,4,27,21, 0), # 开始时间
    end=datetime.datetime(2022,4,28, 15, 0), # 结束时间
    rates={maker: 0,refer: 0.0001}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: 5,refer: 5}, # 一个tick大小
    capital=1_000_000, # 初始资金
)
 
# 添加回测策略，并修改内部参数
engine.clear_data()
 
# engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})
maxDelta=100
minDelta=50
redifEWMAFlag=False
engine.add_strategy(BasisStrategy, {'lots': 10,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':3,'maxPos':30,'alpha':0.03,'gamma':0,
                                    'typ':'','maxDelta':maxDelta,'minDelta':minDelta,'redifEWMAFlag':redifEWMAFlag,'x_stop':2,'net_limit':5,'referHedgeFlag':False,'minEdge':2,'floatEdgeFlag':False})
 
engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
 
engine.duty_statistics()
duty_stat = engine.duty_output
 
#%%
df_mkt = engine.get_risk(multiplier)
 
#%%
df_strategy = pd.DataFrame(engine.strategy.offer_list)
df_strategy.columns = engine.strategy.offer_list_head
 
#%%
import pandas as pd
import numpy as np
from datetime import timedelta
 
df_trades = pd.DataFrame([{'time':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
    'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
    'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'midPrice':engine.trades[x].midPrice,'basePrice':engine.trades[x].basePrice
    } for x in engine.trades])
df_orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
    'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
    } for x in engine.get_all_orders()])
 
 
df_trades['net'] = (df_trades.volume*df_trades.direction).cumsum()
df_trades['cash'] = (df_trades['direction']*(-1)*df_trades['volume']*df_trades['price'])*engine.sizes[engine.vt_symbols[0]]
df_trades['realPnl'] = df_trades['cash'].cumsum()
df_trades['pnl'] = df_trades.realPnl + df_trades.net * df_trades.price * multiplier
 
#%%
def get_trade_detail(engine):
    trades = [{'time':engine.trades[x].datetime,
        'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
        'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'midPrice':engine.trades[x].midPrice,'basePrice':engine.trades[x].basePrice
        } for x in engine.trades]  
    orders = [{'time':x.datetime,'price':x.price,
        'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
        } for x in engine.get_all_orders()]    
    strategy = engine.get_strategy().to_dict('records')
    trade_detail = []
    i = 0
    j = 0
    for trade_message in trades:
        order_id = trade_message['orderid']
        for k in range(i, len(orders)):
            if order_id == orders[k]['orderid']:
                send_time = orders[k]['time'].timestamp()
                trade_message['send_time'] = orders[k]['time']
                trade_message['send_price'] = orders[k]['price']
                i = k+1
                break
        for k in range(j, len(strategy)):
            strategy_time1 = strategy[k]['datetime'].timestamp()
            if k < len(strategy):
                strategy_time2 = strategy[k+1]['datetime'].timestamp()
                if send_time < strategy_time2 and send_time >= strategy_time1:
                    trade_message['strategy_time'] = strategy[k]['datetime']
                    trade_message['theto'] = strategy[k]['theto']
                    trade_message['bidP1'] = strategy[k]['bidP1']
                    trade_message['askP1'] = strategy[k]['askP1']
                    trade_message['my_bid'] = strategy[k]['my_bid']
                    trade_message['my_ask'] = strategy[k]['my_ask']
                    trade_message['fair_refer'] = strategy[k]['fair_refer']
                    trade_message['fair_maker'] = strategy[k]['fair_maker']
                    trade_message['pos_adjust'] = strategy[k]['pos_adjust']
                    trade_message['basis_ewma'] = strategy[k]['basis_ewma']
                    trade_message['basis'] = strategy[k]['basis']
                    
                    j = k
                    trade_detail.append(trade_message.copy())
                    break
    return trade_detail
            
#%%      
ts = 1  
trade_detail = get_trade_detail(engine)
count1 = 0
count2 = 0
count3 = 0
for trade in trade_detail:
    if trade['theto'] < trade['fair_maker']-0.5*ts:
        trade['willing'] = -1
    elif trade['theto'] > trade['fair_maker']+0.5*ts:
        trade['willing'] = 1
    else:
        trade['willing'] = 0
    if trade['willing'] == trade['direction'] and trade['comments'] == 'MM':
        trade['typ'] = 1
        count1 += 1
    if trade['willing'] != 0 and trade['willing'] != trade['direction'] and trade['comments'] == 'MM':
        trade['typ'] = 2
        count2 += 1
    if trade['willing'] == 0 and trade['willing'] != trade['direction'] and trade['comments'] == 'MM':
        trade['typ'] = 3
        count3 += 1    
print(count1, count1/(count1+count2+count3))
print(count2, count2/(count1+count2+count3))
print(count3, count3/(count1+count2+count3))
df_trade_detail = pd.DataFrame(trade_detail)
 
#%%
df_trades1 = df_trade_detail[(df_trade_detail['symbol']=='rb2209')&(df_trade_detail['typ']==3)]
df_mkt1 = engine.get_risk(multiplier=multiplier, trades = df_trades1)
    
#%%
error_list = []
for trade in trade_detail:
    if trade['comments'] == 'MM':
        if trade['direction'] == -1 and trade['price'] != trade['my_ask']:
            error_list.append(trade)
        if trade['direction'] == 1 and trade['price'] != trade['my_bid']:
            error_list.append(trade)     
 
#%%
df_strategy.index = df_strategy['datetime']
df_mkt.index = df_mkt['datetime']
df_test = pd.concat([df_strategy, df_mkt], axis = 1)
df_test.ffill(inplace=True)
 
            
#%%
all_trades={}
all_ins = list(set(df_trade_detail['symbol']))
for iid in all_ins:
    trade = df_trade_detail[df_trade_detail.symbol==iid].copy()
    trade['net'] = (trade.volume*trade.direction).cumsum()
    
    trade['spread'] = trade['direction']*(trade['midPrice']-trade['price'])
    trade['basis'] = trade['midPrice'] - trade['basePrice']
    
    cash_base = (trade['direction']*(-1)*trade['volume']*trade['basePrice'])*multiplier
    cash_basis = (trade['direction']*(-1)*trade['volume']*trade['basis'])*multiplier
    
    cash = (trade['direction']*(-1)*trade['volume']*trade['price'])*multiplier
    cashCum = np.cumsum(cash)
    
    cashCum_base = np.cumsum(cash_base)
    cashCum_basis = np.cumsum(cash_basis)
    
    trade['pnl'] = cashCum + trade.net*trade.midPrice*multiplier
    trade['delta_pnl'] = cashCum_base + trade.net*trade.basePrice*multiplier
    trade['basis_pnl'] = cashCum_basis + trade.net*trade.basis*multiplier
    trade['spread_pnl'] = np.cumsum(trade['spread']*trade.volume)*multiplier
    trade['instant_fade_pnl'] = -trade['volume']*trade['pos_adjust']*trade['direction']
    trade['fade_pnl'] = trade['instant_fade_pnl'].cumsum()
    
    all_trades[iid] = trade
 
 
 
#%%
mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
        'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
        'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
        'last_price','volume','turnover']]
mkt['time'] = mkt['datetime'].apply(lambda x : (x).strftime('%Y-%m-%d %H:%M:%S.%f'))
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))
 
mkt_main = mkt[mkt.symbol==engine.main_symbol]
mkt_main['dV'] = mkt_main.volume - mkt_main.volume.shift(1)
mkt_main['dT'] = (mkt_main.turnover - mkt_main.turnover.shift(1)).apply(lambda x:str(x))
 
#%%
# parameter_list = [(True, 30, 0, 100, 100, 'maker'), (True, 30, 0, 100, 100, 'maker2'), (False, 5, 0.3, 10, 5, 'maker')] # referHedgeFlag, maxPos, gamma, maxDelta, minDelta
# parameter_list = [(True, 30, 0, 100, 100, '')] # referHedgeFlag, maxPos, gamma, maxDelta, minDelta, typ
# parameter_list = [(True, 20, 0, 160, 80, 'maker'), (False, 20, 0.5, 16, 8, 'maker')] # referHedgeFlag, maxPos, gamma, maxDelta, minDelta
# parameter_list = [(False, 5, 0.3, 10, 5, 'maker'), (False, 5, 0.3, 10, 5, '')] # referHedgeFlag, maxPos, gamma, maxDelta, minDelta
parameter_list = [(False, 30, 0, 100, 50)] # referHedgeFlag, maxPos, gamma, maxDelta, minDelta
typ_list = ['maker', 'maker2', '', '2']
 
 
 
insid_list = [('rb2209.SHFE', 'rb2210.SHFE'), ('hc2209.SHFE', 'hc2210.SHFE')]
# insid_list = [('l2206.DCE', 'l2205.DCE')]
 
 
date_list = [(datetime.datetime(2022,2, 14+i, 21, 0), datetime.datetime(2022,2, 15+i, 15, 0)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 18, 21, 0), datetime.datetime(2022,2, 21, 15, 0))]
date_list += [(datetime.datetime(2022,2, 21+i, 21, 0), datetime.datetime(2022,2, 22+i, 15, 0)) for i in range(4)]
date_list += [(datetime.datetime(2022,2, 25, 21, 0), datetime.datetime(2022,2, 28, 15, 0)), (datetime.datetime(2022,2, 28, 21, 0), datetime.datetime(2022,3, 1, 15, 0))]
date_list += [(datetime.datetime(2022,3, 1+i, 21, 0), datetime.datetime(2022,3, 2+i, 15, 0)) for i in range(3)]
date_list += [(datetime.datetime(2022,3, 4, 21, 0), datetime.datetime(2022,3, 7, 15, 0))]
date_list += [(datetime.datetime(2022,3, 7+i, 21, 0), datetime.datetime(2022,3, 8+i, 15, 0)) for i in range(4)]
 
# date_list = [(datetime.datetime(2022,3, 7, 21, 0), datetime.datetime(2022,3, 8, 15, 0))]
 
result1 = {
          'referHedgeFlag':[],
          'maxPos':[],
          'gamma':[],
          'maxDelta':[],       
          'minDelta':[],  
          'maker':[],
          'edge':[],
          'typ':[],
          '交易盈亏':[],
          'market_time':[],
          'my_time':[],
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []
 }
number = 0
name = 'basis_strategy'
 
refer_test=True
 
alpha = 0.03
edge_list = [3, 4]
save_flag = False
save_risk = True
save_order = True
save_trade = True
save_strategy= False
 
count = 0
import time
t = time.time()
for start, end in date_list:
    for maker, refer in insid_list: 
        save_mkt = True
        for referHedgeFlag, maxPos, gamma, maxDelta, minDelta in parameter_list:
            for edge in edge_list:
                for typ in typ_list:
                    try:
                        count += 1
                    
                        multiplier=10
                        
                        parameters = {'alpha':alpha, 'edge':edge, 'maxPos':maxPos, 'gamma':gamma, 'referHedgeFlag':referHedgeFlag}
                        
                        engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=True,duty=True,save_result=False,refer_test=refer_test,fast_join=True,refer_late=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
                        engine.set_parameters(
                            vt_symbols=[maker,refer], # 回测品种
                            interval=Interval.TICK, # 回测模式的数据间隔
                        
                            start=start, # 开始时间
                            end=end, # 结束时间
                            rates={maker: 0,refer: 0.0001}, # 手续费率
                            slippages={maker: 0,refer: 0}, # 滑点
                            sizes={maker: multiplier,refer: multiplier}, # 合约规模
                            priceticks={maker: 1,refer: 1}, # 一个tick大小
                            capital=1_000_000, # 初始资金
                        )
                        
                        # 添加回测策略，并修改内部参数
                        engine.clear_data()
                        
                        # engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})
                        redifEWMAFlag=False
                        engine.add_strategy(BasisStrategy, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':maxPos,'alpha':alpha,'gamma':gamma,
                                                            'typ':typ,'maxDelta':maxDelta,'minDelta':minDelta,'redifEWMAFlag':redifEWMAFlag,'x_stop':2,'net_limit':5,'referHedgeFlag':referHedgeFlag})
                        
                        engine.load_data() # 加载历史数据
                        engine.run_backtesting() # 进行回测
                        df = engine.calculate_tick_result() # 计算逐日盯市盈亏
                        stat = engine.calculate_tick_statistics() # 统计日度策略指标 
                        engine.duty_statistics()
                        duty_stat = engine.duty_output
                        df_strategy =pd.DataFrame(engine.strategy.offer_list)
                        df_strategy.columns = engine.strategy.offer_list_head
                        for key in engine.result.keys():
                            result1[key].append(engine.result[key])  
                        result1['referHedgeFlag'].append(referHedgeFlag)
                        result1['maxPos'].append(maxPos)
                        result1['gamma'].append(gamma)
                        result1['maxDelta'].append(maxDelta)
                        result1['minDelta'].append(minDelta)
                        result1['maker'].append(maker)
                        result1['edge'].append(edge)
                        result1['typ'].append(typ)
                        result1['交易盈亏'].append(result1['总盈亏'][-1]+result1['总手续费'][-1])
                        result1['market_time'].append(duty_stat[maker]['market_time'])
                        result1['my_time'].append(duty_stat[maker]['my_time'])
                        if save_flag:
                            if save_risk:
                                df_mkt = engine.get_risk(multiplier=multiplier)
                            else:
                                df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
                            if save_strategy:
                                df_strategy = engine.get_strategy()
                            else:
                                df_strategy = pd.DataFrame([])
                            engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_mkt = save_mkt, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade)
                            save_mkt = False
                        print(count, maker, referHedgeFlag, time.time()-t, start, 'success')
                    except Exception as err:
                        print(count, maker, referHedgeFlag, time.time()-t, start, err)
                    try:
                        result2 = pd.DataFrame(result1)
                        result2.to_csv('result_basis_strategy.csv')
                    except Exception as err:
                        print(err)
 
 
#%%
result3 = result2.copy()
                
#%%
result2 = pd.read_csv('result1.csv', index_col=None)          
result_list = {}
for i, row in result2.iterrows():
    maker = row['maker']
    flag = row['referHedgeFlag']
    if not (maker, flag) in result_list.keys():
        result_list[(maker, flag)] = [row[['referHedgeFlag','maker', '日期', '总盈亏', '总手续费', '总成交量']]]
    else:
        result_list[(maker, flag)].append(row[['referHedgeFlag','maker', '日期', '总盈亏', '总手续费', '总成交量']])
for key in result_list.keys():
    df = pd.DataFrame(result_list[key])
    df['累计盈亏'] = df['总盈亏'].cumsum()
    df.index = df['日期']
    result_list[key] = df
    df.to_csv('others/'+key[0].split('.')[0]+'_'+str(key[1])+'.csv')
 
#%%
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.figure()
for key in result_list.keys():
    plt.plot(result_list[key]['累计盈亏'], label = str(key[0]) + '-' + str(key[1]))
plt.legend()
plt.title('累计盈亏')
# plt.savefig('others/我是图.png')
plt.show()
    
        
#%%
df_basis_strategy = pd.read_csv('others/refer_test/result_basis_strategy.csv', encoding='utf-8')    
df_basis_trading = pd.read_csv('others/refer_test/result_basis_trading.csv', encoding='gbk')    
df_basis_strategy.fillna('', inplace = True)
#%%
para_list1 = [(i, j, k) for i in [3, 4] for k in ['rb2209.SHFE', 'hc2209.SHFE'] for j in ['maker', 'maker2', '', '2']]
para_list2 = [(i, j, k) for i in [3, 4] for k in ['rb2209.SHFE', 'hc2209.SHFE'] for j in [1, 2]]
 
multiplier=10
df_result = []
for edge, typ, maker in para_list1:
    df1 = df_basis_strategy[(df_basis_strategy['edge']==edge)&(df_basis_strategy['typ']==typ)&(df_basis_strategy['maker']==maker)]
    vol = np.sum(df1['总成交量'])
    pnl = np.sum(df1['总盈亏'])
    pnl_per = pnl/vol/multiplier
    df_result.append(('basis_strategy', edge, typ, maker, vol/df1.shape[0], pnl_per))
for edge, typ, maker in para_list2:
    df2 = df_basis_trading[(df_basis_trading['edge']==edge)&(df_basis_trading['typ']==typ)&(df_basis_trading['maker']==maker)]
    vol = np.sum(df2['总成交量'])
    pnl = np.sum(df2['总盈亏'])
    pnl_per = pnl/vol/multiplier
    df_result.append(('basis_trading', edge, typ, maker, vol/df1.shape[0], pnl_per))
df_result = pd.DataFrame(df_result)
df_result.columns = ['name', 'edge', 'typ', 'maker', '平均成交量', '逐笔盈亏']
df_result.to_csv('others/refer_test/compare.csv')     