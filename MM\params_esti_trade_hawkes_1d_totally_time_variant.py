import numpy as np
import pandas as pd
import warnings
import matplotlib.pyplot as plt
import matplotlib
import scipy.stats as stats
import scipy.optimize as opt
from datetime import datetime, timedelta, time
import math

matplotlib.use('TkAgg')
warnings.filterwarnings("ignore")

# 定义做市商订单量常量
V0 = 100  # 做市商订单量，单位：股
DELTA = 0.001  # 最小价格变动单位
# 分段时间段定义（8个时段，每个约30分钟）
TRADING_PERIODS = [
    (time(9, 30), time(10, 0)),    # 开盘前30分钟
    (time(10, 0), time(10, 30)),
    (time(10, 30), time(11, 0)),
    (time(11, 0), time(11, 30)),   # 上午收盘前30分钟
    (time(13, 0), time(13, 30)),   # 下午开盘前30分钟
    (time(13, 30), time(14, 0)),
    (time(14, 0), time(14, 30)),
    (time(14, 30), time(14, 57))   # 收盘前27分钟
]
N_PERIODS = len(TRADING_PERIODS)


def assign_period(t):
    """为时间分配时间段索引（0-7）"""
    for i, (start, end) in enumerate(TRADING_PERIODS):
        if start <= t < end:
            return i
    # 处理边界情况（收盘时间）
    if t >= time(14, 57):
        return N_PERIODS - 1
    return None


def Lee_Ready_Direction(df_tick):
    df_tick = df_tick.drop_duplicates()
    df_tick['Trade_Direction'] = np.nan
    df_tick['Trade_Volume'] = df_tick['Volume'].diff()
    df_tick['Trade_Volume'].iloc[0] = df_tick['Volume'].iloc[0]
    df_tick['Trade_Value'] = df_tick['TotalValueTraded'].diff()
    df_tick['Trade_Value'].iloc[0] = df_tick['TotalValueTraded'].iloc[0]

    df_tick['AvgTradePrice'] = df_tick['Trade_Value']/df_tick['Trade_Volume']
    # 检查条件并更新列的值
    mask_1 = (df_tick['AvgTradePrice'] > (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2)
    mask_2 = (df_tick['AvgTradePrice'] < (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2)
    mask_3 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (df_tick['AvgTradePrice'] > df_tick['AvgTradePrice'].shift(1))
    mask_4 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (df_tick['AvgTradePrice'] < df_tick['AvgTradePrice'].shift(1))
    mask_5 = (df_tick['AvgTradePrice'] == (df_tick['AskPrice1'] + df_tick['BidPrice1']) / 2) & (df_tick['AvgTradePrice'] == df_tick['AvgTradePrice'].shift(1))

    df_tick.loc[mask_1, 'Trade_Direction'] = 1
    df_tick.loc[mask_2, 'Trade_Direction'] = -1
    df_tick.loc[mask_3, 'Trade_Direction'] = 1
    df_tick.loc[mask_4, 'Trade_Direction'] = -1

    #df_tick.loc[mask_5, 'Trade_Direction'] = df_tick['Trade_Direction'].shift(1)
    # 修复 mask_5 的赋值逻辑：每行独立向前查找最近的非NaN值
    if mask_5.any():  # 确保有满足条件的行
        # 对每一行满足mask_5的位置，向前查找最近的非NaN值
        for idx in df_tick[mask_5].index:
            if idx > 0:  # 跳过第一行，因为无法向前查找
                prev_values = df_tick.loc[:idx-1, 'Trade_Direction']
                if not prev_values.dropna().empty:
                    df_tick.loc[idx, 'Trade_Direction'] = prev_values.dropna().iloc[-1]

    return df_tick


def create_time_buckets():
    """创建时间桶（每15秒）"""
    buckets = []
    # 处理所有交易时段
    for period in TRADING_PERIODS:
        start, end = period
        current = datetime.combine(datetime.today(), start)
        end_dt = datetime.combine(datetime.today(), end)

        while current < end_dt:
            buckets.append(current.time())
            current += timedelta(seconds=15)

    return buckets


def discretize_spread(spread_val):
    """离散化价差（四舍五入到最小变动单位的倍数）"""
    # 将价差四舍五入到最接近的0.001倍数
    discrete_val = round(spread_val ,4)
    # 限制在0.001-0.004之间
    return discrete_val


def simulate_trade_events(df, V0=1000):
    """模拟成交事件"""
    # 识别价差变动事件
    df['spread_chg_flag'] = df['spread_chg'] != 0
    spread_chg_indices = df[df['spread_chg_flag']].index.tolist()

    # 存储模拟结果
    results = []

    # 处理每个价差变动区间
    for i in range(len(spread_chg_indices) - 1):
        start_idx = spread_chg_indices[i]
        end_idx = spread_chg_indices[i + 1]

        # 获取区间数据
        interval_df = df.loc[start_idx:end_idx]
        if interval_df.empty:
            continue

        # 获取区间开始时的订单簿状态
        start_row = interval_df.iloc[0]
        bid_vol = start_row['BidVol1']
        ask_vol = start_row['AskVol1']
        spread_val = start_row['spread']
        bid_price1 = start_row['BidPrice1']
        ask_price1 = start_row['AskPrice1']

        # 离散化价差
        discrete_spread = discretize_spread(spread_val)

        # 计算区间内的主动买卖量（考虑撤单比率修正因子）
        cancellation_ratio_factor = 0  # 撤单率
        buy_volume = interval_df[(interval_df['Trade_Direction'] == 1) & (interval_df['AvgTradePrice'] >= ask_price1)]['Trade_Volume'].sum() * (1 - cancellation_ratio_factor)
        sell_volume = interval_df[(interval_df['Trade_Direction'] == -1) & (interval_df['AvgTradePrice'] <= bid_price1)]['Trade_Volume'].sum() * (1 - cancellation_ratio_factor)

        # 模拟成交事件
        # 1. 最优买价订单成交
        bid_best_trade = 1 if sell_volume > (bid_vol + V0) else 0

        # 2. 高于最优买价一个单位的订单成交
        bid_higher_trade = 1 if sell_volume > V0 else 0

        # 3. 最优卖价订单成交
        ask_best_trade = 1 if buy_volume > (ask_vol + V0) else 0

        # 4. 低于最优卖价一个单位的订单成交
        ask_lower_trade = 1 if buy_volume > V0 else 0

        # 记录结果
        results.append({
            'start_time': interval_df.iloc[0]['timestamp_str'],
            'end_time': interval_df.iloc[-1]['timestamp_str'],
            'discrete_spread': discrete_spread,
            'bid_best_trade': bid_best_trade,
            'bid_higher_trade': bid_higher_trade,
            'ask_best_trade': ask_best_trade,
            'ask_lower_trade': ask_lower_trade
        })

    return pd.DataFrame(results)


def assign_events_to_buckets(sim_results, buckets):
    """将模拟结果分配到时间桶和价差组合"""
    # 离散价差列表
    spread_levels = [round(DELTA * i,4) for i in range(1, 5)]  # 0.001, 0.002, 0.003, 0.004
    event_types = ['bid_best', 'bid_higher', 'ask_best', 'ask_lower']

    # 初始化结果字典
    bucket_data = {}
    for spread in spread_levels:
        for event_type in event_types:
            bucket_data[(spread, event_type)] = np.zeros(len(buckets) - 1)

    # 初始化时间段索引列表
    period_indices = []

    # 处理每个时间桶
    for i in range(len(buckets) - 1):
        bucket_start = buckets[i]
        bucket_end = buckets[i + 1]

        # 确定时间段索引
        period_idx = assign_period(bucket_start)
        period_indices.append(period_idx)

        # 检查所有模拟事件是否在当前桶内
        for _, row in sim_results.iterrows():
            event_time = row['end_time']

            if bucket_start <= event_time < bucket_end:
                spread_val = row['discrete_spread']

                # 只处理在spread_levels范围内的价差
                if spread_val in spread_levels:
                    # 处理四种事件类型
                    for event_type in event_types:
                        if row[event_type + '_trade'] == 1:
                            bucket_data[(spread_val, event_type)][i] += 1

    return bucket_data, period_indices


def time_varying_hawkes_likelihood(params, events, period_indices):
    """计算时变Hawkes过程的似然函数"""
    # 解析参数：8个μ值 + 8个α值 + 8个β值
    mu_period = params[:N_PERIODS]
    alpha_period = params[N_PERIODS:2*N_PERIODS]
    beta_period = params[2*N_PERIODS:]

    # 检查参数约束
    if any(m <= 0 for m in mu_period) or any(a <= 0 for a in alpha_period) or any(b <= a for a, b in zip(alpha_period, beta_period)):
        return 1e10  # 返回一个大数表示不可行解

    T = len(events)
    lambda_vals = np.zeros(T)
    log_likelihood = 0.0

    for t in range(T):
        # 获取当前时间段对应的μ、α、β值
        period_idx = int(period_indices[t]) if not np.isnan(period_indices[t]) else 0
        mu = mu_period[period_idx]
        alpha = alpha_period[period_idx]
        beta = beta_period[period_idx]

        if t == 0:
            lambda_vals[t] = mu
        else:
            # 计算历史事件的影响
            history_effect = 0.0
            for i in range(t):
                time_diff = t - i
                history_effect += alpha * math.exp(-beta * time_diff) * events[i]
            lambda_vals[t] = mu + history_effect

        # 避免数值问题
        if lambda_vals[t] <= 0:
            lambda_vals[t] = 1e-10

        # 累加似然
        log_likelihood += -lambda_vals[t] + events[t] * math.log(lambda_vals[t])

    return -log_likelihood  # 返回负对数似然用于最小化


def estimate_time_varying_hawkes(events, period_indices):
    """估计时变Hawkes过程参数"""
    # 计算每个时间段的初始μ值（事件率）
    period_events = {i: [] for i in range(N_PERIODS)}
    for i, idx in enumerate(period_indices):
        if not np.isnan(idx) and idx < N_PERIODS:  # 确保索引有效
            period_events[int(idx)].append(events[i])

    # 计算每个时间段的平均事件率作为初始μ值
    mu_init = [
        np.mean(period_events[i]) if period_events[i] else 1
        for i in range(N_PERIODS)
    ]

    # 设置初始参数值：8个μ + 8个α + 8个β
    alpha_init = [1]*2 + [0.5]*2 + [0.2]*4   #[1] * N_PERIODS
    beta_init = [2]*2 + [1]*2 + [0.4]*4  #[2] * N_PERIODS
    init_params = mu_init + alpha_init + beta_init

    # 设置参数边界
    bounds = [(1e-5, None)] * (3 * N_PERIODS)

    # 定义约束：beta > alpha
    constraints = [{'type': 'ineq', 'fun': lambda x: x[2*N_PERIODS + i] - x[N_PERIODS + i] - 1e-5} for i in range(N_PERIODS)]

    # 最小化负对数似然
    result = opt.minimize(
        time_varying_hawkes_likelihood,
        init_params,
        args=(events, period_indices),
        bounds=bounds,
        constraints=constraints,
        method='SLSQP'
    )

    if result.success:
        return result.x
    else:
        print(f"Optimization failed: {result.message}")
        return [np.nan] * (3 * N_PERIODS)


def params_trade_esti_main():
    # 读取数据
    trade_path = 'E:\\Internship\\huatai_fin_inno\\Optimal_Market_Making\\data\\ETF_trade\\'
    trade_date = '20250603'
    etf_trade_data = pd.read_parquet(trade_path + 'md_' + trade_date + '_udp_receiver_1_50072.parquet')

    # 转换时间格式并过滤
    etf_trade_data['timestamp_str'] = pd.to_datetime(etf_trade_data['timestamp_str'], format='%H:%M:%S.%f').dt.time

    # 只保留交易时段数据
    time_condition = False
    for start, end in TRADING_PERIODS:
        time_condition |= ((etf_trade_data['timestamp_str'] >= start) &(etf_trade_data['timestamp_str'] < end))

    etf_trade_data = etf_trade_data[time_condition].reset_index(drop=True)

    # 计算中间价和价差
    etf_trade_data['mid_price'] = (etf_trade_data['AskPrice1'] + etf_trade_data['BidPrice1']) / 2
    etf_trade_data = Lee_Ready_Direction(etf_trade_data)
    etf_trade_data['spread'] = etf_trade_data['AskPrice1'] - etf_trade_data['BidPrice1']
    etf_trade_data['spread'] = etf_trade_data['spread'].round(4)
    etf_trade_data['spread_chg'] = etf_trade_data['spread'].diff()
    etf_trade_data['spread_chg'].iloc[0] = etf_trade_data['spread'].iloc[0]

    print(etf_trade_data)
    print(etf_trade_data.columns)
    print("Data loaded and preprocessed")
    print(f"Total records: {len(etf_trade_data)}")

    # 创建时间桶 (每15秒)
    all_buckets = create_time_buckets()
    n_buckets = len(all_buckets) - 1
    print(f"Created {n_buckets} time buckets")

    # 模拟成交事件
    sim_results = simulate_trade_events(etf_trade_data, V0)
    print(f"Trade events simulated: {len(sim_results)} intervals")

    # 将模拟结果分配到时间桶和价差组合
    bucket_data, period_indices = assign_events_to_buckets(sim_results, all_buckets)
    print("Events assigned to time buckets and spread levels")

    # 存储估计结果
    estimates = {}

    # 为每个价差和报价类型组合估计Hawkes参数
    spread_levels = [round(DELTA * i,4) for i in range(1, 5)]
    #spread_levels = [0.001]
    event_types = ['bid_best', 'bid_higher', 'ask_best', 'ask_lower']

    for spread in spread_levels:
        for event_type in event_types:
            events = bucket_data[(spread, event_type)]
            total_events = np.sum(events)

            # 只对事件数足够的组合进行估计
            if total_events > 10:
                print(f"\nEstimating parameters for spread={spread:.4f}, {event_type} (events={total_events})...")
                params = estimate_time_varying_hawkes(events, period_indices)

                estimates[(spread, event_type)] = {
                    'mu_period': params[:N_PERIODS],
                    'alpha_period': params[N_PERIODS:2*N_PERIODS],
                    'beta_period': params[2*N_PERIODS:]
                }

                # 打印每个时间段的μ值
                for i in range(N_PERIODS):
                    start, end = TRADING_PERIODS[i]
                    print(f"  {start}-{end}: μ={params[i]:.6f}, α={params[N_PERIODS + i]:.6f}, β={params[2*N_PERIODS + i]:.6f}")
            else:
                estimates[(spread, event_type)] = {
                    'mu_period': np.zeros(N_PERIODS),
                    'alpha_period': np.zeros(N_PERIODS),
                    'beta_period': np.zeros(N_PERIODS)
                }
                print(f"Skipping spread={spread:.4f}, {event_type} (only {total_events} events)")

    # 可视化结果
    plt.figure(figsize=(16, 12))
    plot_idx = 1

    for spread in spread_levels:
        for event_type in event_types:
            if (spread, event_type) in estimates:
                events = bucket_data[(spread, event_type)]
                params = estimates[(spread, event_type)]

                plt.subplot(4, 4, plot_idx)
                plt.plot(events, 'b-', label='Actual events', alpha=0.7)

                # 使用估计参数计算预测强度
                mu_period = params['mu_period']
                alpha_period = params['alpha_period']
                beta_period = params['beta_period']

                T = len(events)
                lambda_vals = np.zeros(T)

                for t in range(T):
                    # 获取当前时间段对应的μ、α、β值
                    period_idx = int(period_indices[t]) if not np.isnan(period_indices[t]) else 0
                    mu = mu_period[period_idx]
                    alpha = alpha_period[period_idx]
                    beta = beta_period[period_idx]

                    if t == 0:
                        lambda_vals[t] = mu
                    else:
                        history_effect = 0
                        for j in range(t):
                            time_diff = t - j
                            history_effect += alpha * np.exp(-beta * time_diff) * events[j]
                        lambda_vals[t] = mu + history_effect

                plt.plot(lambda_vals, 'r-', label='Hawkes intensity')
                #  这里把强度值也记载下来
                estimates[(spread, event_type)]['lambda_vals'] = lambda_vals

                # 标记不同时间段
                for j in range(1, N_PERIODS):
                    # 查找时间段切换点
                    switch_idx = next((idx for idx, p_idx in enumerate(period_indices) if p_idx == j), None)
                    if switch_idx is not None and switch_idx < T:
                        plt.axvline(x=switch_idx, color='g', linestyle='--', alpha=0.3)

                plt.title(f'spread={spread:.4f}\n{event_type}')
                plt.xlabel('Time Bucket')
                plt.ylabel('Intensity/Events')
                plt.legend()
                plt.grid(True, alpha=0.3)
                plot_idx += 1


    plt.tight_layout()
    #plt.savefig(f'pics_testing\\all_params_time_variant_1d_hawkes_intensity_by_spread_{trade_date}.png', dpi=300)
    plt.show()

    return estimates


if __name__ == '__main__':
    params_trade_estimation_results = params_trade_esti_main()
    print(params_trade_estimation_results[(0.001, 'bid_best')])