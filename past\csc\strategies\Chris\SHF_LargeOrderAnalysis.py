# -*- coding: utf-8 -*-
"""
Created on Fri Jan 21 14:56:01 2022
@author: <PERSON>
"""
from datetime import timedelta
import pandas as pd
import datetime
import numpy as np
from influxdb import InfluxDBClient

#convert dataframe result to dataframe
def df_convert(result):
    points = result.get_points()
    l = []
    for d in points:
        l.append(d)
    return pd.DataFrame(l)

if __name__=='__main__':
    
    # client = InfluxDBClient('10.17.88.168',9001,'reader','iamreader','testbase') #东坝
    client = InfluxDBClient('192.168.203.11',8989,'reader','iamreader','testbase') #上期所prod
    # endStr = '2022-1-27T15:10:00.0Z'
    params = {
        'contract':'rb2204',
        'beginStr':'2022-2-14T21:00:00.0Z',
        'endStr':'2022-2-15T15:00:00.0Z',
        'mkt_vol_min':200, #定义市场上的大单
        'my_vol_min':10 #我们自己的成交量超过这个数算大单
        }
    begin = datetime.datetime.strptime(params['beginStr'],'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    end = datetime.datetime.strptime(params['endStr'],'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000

    
    #获取成交回报
    #---------------------------------------------------------------------------
    # result = client.query("select * from testdalian where time >= %d and time <= %d and insid_md='l2203';"%(begin,end)) 
    # result = client.query("select * from shfe_future_trade where time >= %d and time <= %d and insid='hc2204';"%(begin,end)) 
    # df _trade_report = df_convert(client.query("select * from shfe_future_trade where time >= %d and time <= %d;"%(begin,end)))
    df_trade_report = df_convert(client.query("select * from shfe_future_trade where time >= %d and time <= %d and insid='%s';"%(begin,end,params['contract'])))
    df_trade_report = df_trade_report[df_trade_report['volume_traded']>params['my_vol_min']] #filter volume
    df_trade_report = df_trade_report[df_trade_report['comments']=='MM']
    df_trade_report.set_index('local_time',inplace=True)
    df_trade_report = df_trade_report[['time','trade_price','volume_traded','long_short']]
    
    #获取行情数据
    df_market_data = df_convert(client.query("select * from test10 where time >= %d and time <= %d and insid_md='%s';"%(begin,end,params['contract'])) )
    df_market_data['v'] = df_market_data['v'].diff().fillna(0)
    df_market_data.set_index('local_t',inplace=True)

    
    #拼接成交回报和行情数据
    df = pd.concat([df_trade_report,df_market_data],axis=1)
    
    df.reset_index(inplace=True)
    trade_index = df[df['trade_price']>0].index.tolist()
    slice_list  = []
    for i in trade_index:
        _slice =  (np.array(range(-5,5))+i).tolist()
        slice_list+=_slice
    df_merge = df.iloc[slice_list]
    
