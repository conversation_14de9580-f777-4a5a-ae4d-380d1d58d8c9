"""
因子分析系统
整合因子分析和因子测试功能，提供全面的因子评估和分析能力
@author: lining
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from tqdm import tqdm

from utils.utils import log_print
from core import config
from utils.utils import calculate_win_rate_threshold
import shap
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import partial

class FactorAnalysisSystem:
    """因子分析系统"""

    def __init__(self):
        self.analysis_results = {}
        self.test_results = {}
        self.shap_values = {}
        self.output_dir = config.OUTDIR + "/features"
        self.threshold_percent = config.THRESHOLD_PERCENT
        self.quantile_list = config.QUANTILE_LIST

    def analyze_factor(self, data: pd.Series) -> Dict:
        """分析单个因子的统计特性"""
        # 确保数据为数值类型
        if not pd.api.types.is_numeric_dtype(data):
            print(data.name, data.dtype,data.shape,data.head(5))
            raise ValueError("数据类型错误，必顨为数值类型")
        analysis = {
            'basic_stats': {
                'mean': data.mean(),
                'std': data.std(),
                'min': data.min(),
                'max': data.max(),
                'skew': data.skew(),
                'kurtosis': data.kurtosis()
            },
            'quantiles': {
                f"{q*100}%": data.quantile(q) for q in self.quantile_list
            },
            'missing_stats': {
                'total': data.isna().sum(),
                'percentage': data.isna().mean() * 100
            },
            'unique_values': data.nunique()
        }
        
        # 计算自相关性
        for lag in [1, 5, 10, 20]:
            analysis[f'autocorr_{lag}'] = data.autocorr(lag=lag)
        
        return analysis
    
    def analyze_factor_set(self, data: pd.DataFrame, factors: List[str]) -> Dict:
        """分析因子集合"""
        results = {}
        for factor in factors:
            if factor in data.columns:
                try:
                    results[factor] = self.analyze_factor(data[factor])
                except Exception as e:
                    log_print(f"因子 {factor} 分析失败: {e}")
        self.analysis_results = results
        return results

    def calculate_ic(self, factor: pd.Series, returns: pd.Series) -> float:
        """计算信息系数(IC)"""
        return factor.corr(returns)
    
    def calculate_rank_ic(self, factor: pd.Series, returns: pd.Series) -> float:
        """计算秩相关系数(Rank IC)"""
        return factor.rank().corr(returns.rank())

    def calculate_ic_series(self, factor: pd.Series, returns: pd.Series, window: int = 20) -> pd.Series:
        """计算滚动信息系数"""
        return factor.rolling(window=window).corr(returns)

    def calculate_ir(self, factor: pd.Series, returns: pd.Series, window: int = 200) -> float:
        """计算信息比率(IR)"""
        ic_series = []
        for i in range(window, len(factor), window):
            window_factor = factor.iloc[i - window:i]
            window_returns = returns.iloc[i - window:i]
            if len(window_factor) > 1 and window_factor.std() != 0 and window_returns.std() != 0:
                window_ic = window_factor.corr(window_returns)
                ic_series.append(window_ic)

        ic_series = pd.Series(ic_series).dropna()
        if len(ic_series) > 0 and ic_series.std() != 0:
            return ic_series.mean() / ic_series.std()
        return 0

    def calculate_win_loss_ratio(self, factor: pd.Series, returns: pd.Series) -> float:
        """计算盈亏比"""
        win_avg = factor[returns > 0].abs().mean()
        loss_avg = factor[returns < 0].abs().mean()
        return win_avg / loss_avg

    def test_factors(self, std_data: pd.DataFrame, target_data: pd.DataFrame, 
                     feature_cols: List[str], target_cols: List[str], 
                     model: Optional[Any] = None,
                     is_shap:bool=True) -> pd.DataFrame:
        """
        进行因子测试
        
        参数:
            data: 数据集
            feature_cols: 特征列列表
            target_cols: 目标列列表
            model: 训练好的模型，用于SHAP分析
            enable_shap: 是否进行SHAP分析
        """
        log_print(f"开始因子测试...数据量: {std_data.shape[0]}")

        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 初始化结果存储
        ic_values = {}
        win_rates = {}
        win_rates_threshold = {}
        non_zero_count = {}
        ir_values = {}
        rank_ic_values = {}
        win_loss_ratio = {}

        # 计算每个因子的指标
        target = target_cols[0]
        valid_features = []
        
        data = pd.concat([std_data, target_data], axis=1)
        # 预先筛选有效特征，避免重复计算
        for feature in feature_cols:
            valid_data = data[[feature, target]].dropna()
            if len(valid_data) > 1 and valid_data[feature].std() != 0 and valid_data[target].std() != 0:
                valid_features.append((feature, valid_data))
        
        # 使用并行计算加速因子指标计算
        def process_feature(self, feature_data):
            feature, valid_data = feature_data
            feature_series = valid_data[feature]
            target_series = valid_data[target]
            
            # 一次性计算基本指标
            results = {
                'ic': self.calculate_ic(feature_series, target_series),
                'rank_ic': self.calculate_rank_ic(feature_series, target_series),
                'win_rate': calculate_win_rate_threshold(target_series, feature_series)[0],
                'win_loss_ratio': self.calculate_win_loss_ratio(feature_series, target_series),
                'ir': self.calculate_ir(feature_series, target_series),
                'win_rates_threshold': {},
                'non_zero_count': {}
            }
            
            # 批量计算阈值相关指标
            for threshold_percent in self.quantile_list:
                results['win_rates_threshold'][threshold_percent], results['non_zero_count'][threshold_percent] = \
                    calculate_win_rate_threshold(target_series, feature_series, threshold_percent)
            
            return feature, results
        
        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=min(os.cpu_count(), len(valid_features))) as executor:
            process_func = partial(process_feature, self)
            futures = {executor.submit(process_func, feature_data): feature_data[0] for feature_data in valid_features}
            
            for future in tqdm(as_completed(futures), total=len(futures), desc="计算因子指标"):
                feature, results = future.result()
                ic_values[feature] = results['ic']
                rank_ic_values[feature] = results['rank_ic']
                win_rates[feature] = results['win_rate']
                win_loss_ratio[feature] = results['win_loss_ratio']
                ir_values[feature] = results['ir']
                win_rates_threshold[feature] = results['win_rates_threshold']
                non_zero_count[feature] = results['non_zero_count']
        
        # 记录无效特征
        invalid_features = set(feature_cols) - set(f for f, _ in valid_features)
        if invalid_features:
            log_print(f"警告: {len(invalid_features)}个特征无法计算有效指标，已赋默认值")

        # 创建结果DataFrame
        factor_test_results = pd.DataFrame({
            'feature': feature_cols,
            'ic': [ic_values.get(feature, 0) for feature in feature_cols],
            'abs_ic': [abs(ic_values.get(feature, 0)) for feature in feature_cols],
            'rank_ic': [rank_ic_values.get(feature, 0) for feature in feature_cols],
            'ir': [ir_values.get(feature, 0) for feature in feature_cols],
            'win_rate': [win_rates.get(feature, 0.5) for feature in feature_cols],
            'win_loss_ratio': [win_loss_ratio.get(feature, 0) for feature in feature_cols],
            'win_rate_threshold': [win_rates_threshold.get(feature, {}) for feature in feature_cols],
            'non_zero_count': [non_zero_count.get(feature, {}) for feature in feature_cols]
        })
        self.test_results = factor_test_results.sort_values('abs_ic', ascending=False)

        # 输出统计信息
        self._print_statistics(self.test_results)

        # 执行SHAP分析
        if is_shap:
            self.analyze_shap(model, config.MODEL_TYPE, std_data, feature_cols)

        return self.test_results
    

    def generate_analysis_report(self, data: pd.DataFrame, factors: List[str]) -> str:
        """生成分析报告"""
        results = self.analyze_factor_set(data, factors)
        report = "# 因子分析报告\n\n"

        # 添加总体统计信息
        report += "## 总体统计信息\n\n"
        report += f"- 分析因子总数     : {len(factors):>6d}\n"
        report += f"- 有效因子数量     : {len(results):>6d}\n"
        report += f"- 数据样本量       : {len(data):>6d}\n\n"

        # 添加因子相关性热图描述
        report += "## 因子相关性分析\n\n"
        report += "因子间相关性热图见附件。高度相关的因子可能存在信息冗余，建议在模型中选择代表性因子。\n\n"

        # 添加IC分析总结
        if self.test_results is not None and 'ic' in self.test_results.columns:
            report += "## IC值分析\n\n"
            report += f"- 平均IC值        : {self.test_results['ic'].mean():>8.4f}\n"
            report += f"- 平均Rank IC值    : {self.test_results['rank_ic'].mean():>8.4f}\n"
            report += f"- 平均IR值        : {self.test_results['ir'].mean():>8.4f}\n"
            report += f"- 平均胜率        : {self.test_results['win_rate'].mean():>8.4f}\n\n"

        # 各因子详细分析
        report += "## 各因子详细分析\n\n"
        for factor, analysis in results.items():
            report += f"### {factor}\n\n"

            # 基本统计量
            report += "#### 基本统计量\n"
            for stat, value in analysis['basic_stats'].items():
                report += f"- {stat:<15} : {value:>8.4f}\n"

            # 分位数
            report += "\n#### 分位数\n"
            for q, value in analysis['quantiles'].items():
                report += f"- {q:<15} : {value:>8.4f}\n"

            # 缺失值统计
            report += "\n#### 缺失值统计\n"
            for stat, value in analysis['missing_stats'].items():
                report += f"- {stat:<15} : {value:>8.4f}\n"

            # 自相关性
            report += "\n#### 自相关性\n"
            for lag in [1, 5, 10, 20]:
                report += f"- lag_{lag:<11} : {analysis[f'autocorr_{lag}']:>8.4f}\n"

            # 添加因子与目标变量相关性分析
            if hasattr(self, 'test_results') and self.test_results is not None and 'ic' in self.test_results.columns:
                factor_result = self.test_results[self.test_results['feature'] == factor]
                if not factor_result.empty:
                    report += "\n#### 预测能力分析\n"
                    report += f"- IC值           : {factor_result['ic'].iloc[0]:>8.4f}\n"
                    report += f"- IR值           : {factor_result.get('ir', pd.Series([0])).iloc[0]:>8.4f}\n"
                    report += f"- 胜率           : {factor_result.get('win_rate', pd.Series([0])).iloc[0]:>8.4f}\n"
                    
                    if 'win_rate_threshold' in factor_result.columns and not factor_result['win_rate_threshold'].isna().all():
                        for threshold_percent in self.quantile_list:
                            win_rate_threshold = factor_result['win_rate_threshold'].iloc[0]
                            non_zero_count = factor_result['non_zero_count'].iloc[0]
                            if isinstance(win_rate_threshold, dict) and threshold_percent in win_rate_threshold:
                                report += f"- 胜率 {threshold_percent*100:>3.0f}%     : {win_rate_threshold[threshold_percent]:>8.4f}\n"
                            if isinstance(non_zero_count, dict) and threshold_percent in non_zero_count:
                                report += f"- 非0数量        : {non_zero_count[threshold_percent]:>8.0f}\n"

                    # 添加因子评级
                    if 'ic' in factor_result:
                        rating = "一般"
                        ic_abs = abs(factor_result['ic'].iloc[0])
                        if ic_abs > 0.02:
                            rating = "良好"
                        if ic_abs > 0.05:
                            rating = "优秀"
                        if ic_abs > 0.10:
                            rating = "极好"
                        report += f"- 因子评级        : {rating:>8}\n"
                
                # 添加SHAP重要性排名（如果可用）
                if 'shap_importance_rank' in self.test_results.columns:
                    shap_importance = factor_result['shap_importance_rank']
                    if not shap_importance.empty:
                        report += f"- SHAP重要性排名      : {shap_importance.iloc[0]:>8.0f}\n"
                    if 'shap_importance' in self.test_results.columns:
                        shap_importance_value = factor_result['shap_importance']
                        if not shap_importance_value.empty:
                            report += f"- SHAP重要性      : {shap_importance_value.iloc[0]:>8.2e}\n"

            # 添加稳定性评估
            stability = analysis.get('stability', 0)
            if stability:
                report += f"- 稳定性指标      : {stability:>8.4f}\n"

            report += "\n"
        
        # 添加因子列表
        report += "## 因子列表\n\n"
        report += "factor_list = [\n"
        for factor in results.keys():
            report += f"    {factor},\n"
        report += "]"

        return report

    def _print_statistics(self, results: pd.DataFrame):
        """输出统计信息"""
        log_print("\n=== 因子检验结果 ===")
        log_print(f"因子检验完成，共检验 {len(results)} 个因子")
        log_print(f"平均IC值: {results['ic'].mean():.4f}")
        log_print(f"平均Rank IC值: {results['rank_ic'].mean():.4f}")
        log_print(f"平均IR值: {results['ir'].mean():.4f}")
        log_print(f"平均胜率: {results['win_rate'].mean():.4f}")

        good_factors = len(results[results['abs_ic'] > 0.02])
        excellent_factors = len(results[results['abs_ic'] > 0.05])
        outstanding_factors = len(results[results['abs_ic'] > 0.10])

        log_print("\n=== 因子质量分布 ===")
        log_print(f"良好因子数量 (|IC| > 0.02): {good_factors}")
        log_print(f"优秀因子数量 (|IC| > 0.05): {excellent_factors}")
        log_print(f"极好因子数量 (|IC| > 0.10): {outstanding_factors}")

        log_print("\n=== Top 5因子 ===")
        log_print(f"因子数量: {len(results)}")
        for idx, row in results.head().iterrows():
            log_print(f"因子: {row['feature']}, IC值: {row['ic']:.2f}, Rank IC值: {row['rank_ic']:.2f}, IR值: {row['ir']:.2f}, 胜率: {row['win_rate']:.2f}, 分位胜率: "+" ".join([f"{threshold_percent}: {row['win_rate_threshold'][threshold_percent]*100:.2f}% {row['non_zero_count'][threshold_percent]} " for threshold_percent in self.quantile_list]))

    def analyze_shap(self, model, model_type, X_train: pd.DataFrame, feature_cols: List[str], background_sample_size: int = 100) -> Dict:
        """
        计算SHAP值并分析特征重要性
        
        参数:
            model: 训练好的模型
            X_train: 训练数据
            feature_cols: 特征列名
            background_sample_size: 用于计算SHAP值的背景样本大小
            
        返回:
            Dict: 包含SHAP分析结果的字典
        """
        log_print("开始SHAP值分析...")
        # 创建SHAP解释器
        try:
            # 尝试使用适合的解释器类型
            if model_type in ['xgboost', 'lightgbm']:
                # 对于树模型如XGBoost、LightGBM等
                explainer = shap.TreeExplainer(model)
            elif model_type == 'linear':
                # 对于线性模型
                explainer = shap.LinearExplainer(model, X_train[feature_cols])
            else:
                # 对于其他模型类型
                background = shap.sample(X_train[feature_cols], background_sample_size)
                explainer = shap.KernelExplainer(model.predict, background)
            
            # 计算SHAP值
            shaps=explainer(X_train[feature_cols])
            shap_values = shaps.values
            
            # 如果是多分类问题，取第一个类别的SHAP值
            if isinstance(shap_values, list):
                shap_values = shap_values[0]
                
            # 计算SHAP重要性
            shap_importance = np.abs(shap_values).mean(axis=0)
            shap_importance_df = pd.DataFrame({
                'feature': feature_cols,
                'importance': shap_importance
            }).sort_values('importance', ascending=True)
            
            # 保存SHAP值和特征数据用于后续可视化
            self.shap_values = {'shap': shaps,'expected_value': explainer.expected_value, 'values': shap_values, 'data': X_train[feature_cols], 'feature_names': feature_cols, 'importance': shap_importance_df}
            
            log_print(f"SHAP分析完成，分析了{len(feature_cols)}个特征")

            # 将SHAP重要性添加到因子测试结果中
            if self.test_results is not None and 'importance' in self.shap_values:
                # 添加SHAP重要性列
                shap_importance_dict = dict(zip(
                    self.shap_values['importance']['feature'], 
                    self.shap_values['importance']['importance']
                ))
                self.test_results['shap_importance'] = [
                    shap_importance_dict.get(feature, 0) for feature in self.test_results['feature']
                ]
                # 计算排名
                self.test_results['shap_importance_rank'] = self.test_results['shap_importance'].rank(ascending=False)
                
                # 按SHAP重要性排序的结果
                
                shap_sorted = self.test_results.sort_values('shap_importance', ascending=False)
                # 输出SHAP重要性排名前5的因子信息
                log_print(f"按SHAP重要性排序的Top 5因子: {shap_sorted['feature'][:5].tolist()}")

        except Exception as e:
            log_print(f"SHAP分析失败: {e}")

# 创建全局因子分析系统实例
factor_analysis_system = FactorAnalysisSystem()


