
"""
可视化模块
@author: lining
"""
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import seaborn as sns
from scipy import stats
from utils.utils import log_print
from core import config
import os
import matplotlib.pyplot as plt
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
import base64
import glob
import datetime
from bs4 import BeautifulSoup


class OrderBookVisualizer:
    

    def __init__(self, use_chinese=True, figsize=(12, 8)):
        
        self.figsize = figsize
        self.use_chinese = use_chinese
        self.plots_dir = os.path.join(config.OUTDIR, 'plots')
        if not os.path.exists(self.plots_dir):
            os.makedirs(self.plots_dir)

        
        sns.set(style="whitegrid")


    def plot_feature_correlation(self, train_data, y_train_dict, features=None, target=None, title=None):
        
        data = train_data[target]
        y_train = y_train_dict[target]
        data = pd.concat([data, y_train], axis=1)
        if data is None or len(data) == 0:
            print("数据为空，无法绘制特征相关性图")
            return

        
        if features is None:
            
            features = data.select_dtypes(include=[np.number]).columns.tolist()

        if target is not None:
            features.append(target)

        
        if len(features) > 1000:
            print(f"特征数量过多 ({len(features)})，只显示前1000个")
            features = features[:1000]

        
        corr = data[features].corr()
        
        corrcolumns = corr.abs().sort_values(by=target, ascending=True).index
        corr = corr.loc[corrcolumns, corrcolumns]

        
        fig = go.Figure(data=go.Heatmap(
            z=corr,
            x=corr.columns,
            y=corr.columns,
            colorscale='RdBu',
            zmid=0,
            text=np.round(corr, 2),
            texttemplate='%{text}',
        ))

        
        title = title or '特征相关性热图'
        fig.update_layout(
            title_text=title,
        )
        fig.show()

        correlation_path = os.path.join(self.plots_dir, 'feature_correlation.html')
        fig.write_html(correlation_path)

    def plot_feature_importance(self, model, feature_cols, title=None, top_n=config.VISUALIZE_TOP_N):
        

        try:
            
            feature_importances = model.feature_importances_
            model_feature_cols = model.feature_names_in_
            del_feature_cols = [col for col in model_feature_cols if col not in feature_cols]
            feature_importance = pd.DataFrame({
                'feature': model_feature_cols,
                'importance': feature_importances
            }).sort_values('importance', ascending=True)
        except:
            log_print("特征重要性数据为空，无法绘制特征重要性图", 'warning')
            return

        
        required_cols = ['feature', 'importance']
        missing_cols = [col for col in required_cols if col not in feature_importance.columns]
        if missing_cols:
            print(f"数据缺少必要的列: {missing_cols}")
            return

        
        if len(feature_importance) > top_n:
            feature_importance = feature_importance.head(top_n)

        
        fig = go.Figure()

        
        fig.add_trace(
            go.Bar(x=feature_importance['importance'],
                   y=feature_importance['feature'],
                   orientation='h',
                   name='特征重要性')
        )

        
        fig.update_layout(
            title_text=title or '特征重要性',
            xaxis_title="重要性",
            yaxis_title="特征",
            showlegend=False
        )

        fig.show()

        importance_path = os.path.join(self.plots_dir, 'feature_importance.html')
        fig.write_html(importance_path)

    def plot_IC_IR_win_rate(self, results: pd.DataFrame):
        
        
        top_50_features = results.head(50)
        fig_top_50 = make_subplots(specs=[[{"secondary_y": True}]])

        fig_top_50.add_trace(
            go.Bar(x=top_50_features['feature'], y=top_50_features['ic'],
                   name='IC值', marker_color='blue', width=0.4, offset=-0.2),
            secondary_y=False
        )

        fig_top_50.add_trace(
            go.Bar(x=top_50_features['feature'], y=top_50_features['ir'],
                   name='IR值', marker_color='red', width=0.4, offset=0.2),
            secondary_y=True
        )

        
        fig_top_50.add_trace(
            go.Scatter(x=top_50_features['feature'], y=top_50_features['win_rate'],
                       name='胜率', mode='lines+markers', marker_color='green', line=dict(width=2)),
            secondary_y=True
        )

        
        fig_top_50.add_hline(y=0.5, line_dash="dash", line_color="green", secondary_y=True)

        ic_max = max(abs(top_50_features['ic'].max()), abs(top_50_features['ic'].min()))
        ir_max = max(abs(top_50_features['ir'].max()), abs(top_50_features['ir'].min()))

        fig_top_50.update_layout(
            title='Top 50因子IC值和IR值对比',
            xaxis_tickangle=45,
            showlegend=True
        )

        fig_top_50.update_yaxes(title_text="IC值", secondary_y=False, range=[-ic_max, ic_max])
        fig_top_50.update_yaxes(title_text="IR值", secondary_y=True, range=[-ir_max, ir_max])

        fig_top_50.show()

    def analyze_factor_distribution(self, factor_series):
        
        
        factor = factor_series.replace([np.inf, -np.inf], np.nan).dropna()
        print(f'无效值比例: {(len(factor_series) - len(factor))/len(factor_series)}')
        
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('直方图 + KDE', '箱线图', 'Q-Q图', ''),
            specs=[[{"colspan": 2}, None], [{}, {}]],
            vertical_spacing=0.1,
            horizontal_spacing=0.1
        )
        
        
        hist_data = [factor]
        group_labels = ['factor']
        
        
        counts, bins = np.histogram(factor, bins=50)
        bins_center = (bins[:-1] + bins[1:]) / 2
        
        
        fig.add_trace(
            go.Bar(x=bins_center, y=counts/sum(counts)/(bins[1]-bins[0]), 
                   name='直方图', marker_color='skyblue', opacity=0.7),
            row=1, col=1
        )
        
        
        
        
        kde = stats.gaussian_kde(factor)
        x_kde = np.linspace(factor.min(), factor.max(), 1000)
        y_kde = kde(x_kde)
        
        
        fig.add_trace(
            go.Scatter(x=x_kde, y=y_kde, mode='lines', name='KDE', 
                      line=dict(color='blue', width=2)),
            row=1, col=1
        )
        
        
        x = np.linspace(factor.min(), factor.max(), 1000)
        normal_pdf = stats.norm.pdf(x, loc=factor.mean(), scale=factor.std())
        fig.add_trace(
            go.Scatter(x=x, y=normal_pdf, mode='lines', name='正态分布', 
                      line=dict(color='red', width=2, dash='dash')),
            row=1, col=1
        )
        
        
        
        
        
        
        
        
        
        fig.add_trace(
            go.Box(y=factor, name='factor', marker_color='lightgreen', 
                  boxpoints='outliers', jitter=0.3, pointpos=-1.8),
            row=2, col=1  
        )
        
        
        quantiles = np.linspace(0.01, 0.99, 100)
        empirical_quantiles = factor.quantile(quantiles)
        theoretical_quantiles = stats.norm.ppf(quantiles, loc=factor.mean(), scale=factor.std())
        
        
        fig.add_trace(
            go.Scatter(x=theoretical_quantiles, y=empirical_quantiles, 
                      mode='markers', name='Q-Q点', 
                      marker=dict(color='blue', size=6)),
            row=2, col=2
        )
        
        
        min_val = min(theoretical_quantiles.min(), empirical_quantiles.min())
        max_val = max(theoretical_quantiles.max(), empirical_quantiles.max())
        fig.add_trace(
            go.Scatter(x=[min_val, max_val], y=[min_val, max_val], 
                      mode='lines', name='参考线', 
                      line=dict(color='red', width=2, dash='dash')),
            row=2, col=2
        )
        
        
        fig.update_layout(
            title=f'因子分布分析 (N={len(factor)})',
            showlegend=True
        )
        
        
        fig.update_xaxes(title_text='因子值', row=1, col=1)
        fig.update_yaxes(title_text='密度', row=1, col=1)
        fig.update_xaxes(title_text='因子值', row=2, col=1)
        fig.update_xaxes(title_text='理论分位数', row=2, col=2)
        fig.update_yaxes(title_text='实际分位数', row=2, col=2)
        
        
        stats_dict = {
            '观测数': len(factor),
            '均值': factor.mean(),
            '标准差': factor.std(),
            '偏度': factor.skew(),
            '峰度': factor.kurtosis(),
            'Jarque-Bera检验': stats.jarque_bera(factor)[0],
            'JB检验P值': stats.jarque_bera(factor)[1],
        }
        
        legend_text = ""
        for key, value in stats_dict.items():
            if value>1:
                legend_text += f"{key}: {int(value)}  "  # 添加两个空格作为分隔
            else:
                legend_text += f"{key}: {value:.4f}  "  # 添加两个空格作为分隔
        
        fig.add_annotation(
            xref="paper",
            yref="paper",
            x=0.5,
            y=-0.08,  
            text=legend_text,
            showarrow=False,
            font=dict(size=12),
            align="center",
            bgcolor="white",
            opacity=0.8
        )

        
        fig.show()
        
        
        if hasattr(self, 'plots_dir'):
            factor_name = getattr(factor_series, 'name', 'factor')
            factor_path = os.path.join(self.plots_dir, f'{factor_name}_distribution.html')
            fig.write_html(factor_path)
        
        return fig

    def plot_backtest_results(self, results, title=None):
        
        if results is None or len(results) == 0:
            log_print("数据为空，无法绘制回测结果", 'warning')
            return None

        
        fig = make_subplots(
            rows=4, cols=1, 
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=('价格和交易信号', '累计收益曲线', '回撤百分比', '持仓'),
            specs=[[{"type": "xy"}], 
                   [{"type": "xy", "secondary_y": True}],  
                   [{"type": "xy", "secondary_y": True}], 
                   [{"type": "xy", "secondary_y": True}]]
        )

        
        price_col = 'mid' if 'mid' in results.columns else 'close'
        if price_col in results.columns:
            
            valid_data = results[results[price_col].notna()]

            
            fig.add_trace(
                go.Scatter(x=valid_data.index, y=valid_data[price_col],
                           name=price_col, line=dict(color='blue'),
                           connectgaps=False),
                row=1, col=1
            )

            
            if 'position' in results.columns:
                buy_points = valid_data[valid_data['position'] > 0]
                sell_points = valid_data[valid_data['position'] < 0]

                if len(buy_points) > 0:
                    fig.add_trace(
                        go.Scatter(x=buy_points.index, y=buy_points[price_col],
                                   mode='markers',
                                   marker=dict(symbol='triangle-up', size=10, color='green'),
                                   name='买入'),
                        row=1, col=1
                    )
                if len(sell_points) > 0:
                    fig.add_trace(
                        go.Scatter(x=sell_points.index, y=sell_points[price_col],
                                   mode='markers',
                                   marker=dict(symbol='triangle-down', size=10, color='red'),
                                   name='卖出'),
                        row=1, col=1
                    )

            
            if 'exit_price' in results.columns:
                exit_points = valid_data[valid_data['exit_price'].notna()]
                if len(exit_points) > 0:
                    fig.add_trace(
                        go.Scatter(x=exit_points.index, y=exit_points['exit_price'],
                                   mode='markers',
                                   marker=dict(symbol='x', size=10, color='black'),
                                   name='平仓'),
                        row=1, col=1
                    )

        
        valid_returns = results[results['cum_return'].notna()]
        
        
        fig.add_trace(
            go.Scatter(x=valid_returns.index, y=valid_returns['cum_return'],
                       name='累计收益', line=dict(color='blue'),
                       connectgaps=False),
            row=2, col=1,
            secondary_y=False
        )
        
        
        valid_net_returns = results[results['net_return'].notna()]
        fig.add_trace(
            go.Scatter(x=valid_net_returns.index, y=valid_net_returns['net_return'],
                       name='扣除手续费', line=dict(color='red'),
                       connectgaps=False),
            row=2, col=1,
            secondary_y=True
        )

        
        fig.add_trace(
            go.Scatter(x=valid_net_returns.index, y=1+valid_net_returns['cum_return']-valid_net_returns['net_return'],
                       name='手续费', line=dict(color='orange', dash='dash', width=1),
                       connectgaps=False),
            row=2, col=1,
        )

        
        fig.update_yaxes(tickformat='.1%', row=2, col=1, secondary_y=False)
        fig.update_yaxes(tickformat='.1%', row=2, col=1, secondary_y=True)
        
        
        fig.update_yaxes(title_text="累计收益", row=2, col=1, secondary_y=False)
        fig.update_yaxes(title_text="扣除手续费收益", row=2, col=1, secondary_y=True)

        
        drawdown = (valid_returns['cum_return'] / valid_returns['cum_return'].cummax() - 1) * 100
        drawdown2 = (valid_net_returns['net_return'] / valid_net_returns['net_return'].cummax() - 1) * 100
        fig.add_trace(
            go.Scatter(x=valid_returns.index, y=drawdown,
                       fill='tozeroy',
                       fillcolor='blue',
                       name='回撤',
                       opacity=0.3, 
                       line=dict(color='red'),
                       connectgaps=False),
            row=3, col=1
        )

        
        fig.add_trace(
            go.Scatter(x=valid_net_returns.index, y=drawdown2,
                       fill='tozeroy',
                       fillcolor='green',
                       opacity=0.3, 
                       name='扣除手续费回撤',
                       line=dict(color='red'),
                       connectgaps=False),
            row=3, col=1,
            secondary_y=True
        )
        
        fig.update_yaxes(title_text="回撤 (%)", row=3, col=1, secondary_y=False)
        fig.update_yaxes(title_text="扣除手续费回撤 (%)", row=3, col=1, secondary_y=True)

        
        if 'position' in results.columns:
            fig.add_trace(
                go.Scatter(x=results.index, y=results['position'].ffill(),
                           name='持仓', line=dict(color='blue')),
                row=4, col=1
            )
            
            fig.add_trace(
                go.Scatter(x=results.index, y=results['total_trades'].ffill(),
                           name='总成交次数', line=dict(color='green', dash='dash', width=1)),
                row=4, col=1,
                secondary_y=True
            )
            
            fig.update_yaxes(title_text="持仓", row=4, col=1, secondary_y=False)
            fig.update_yaxes(title_text="总成交次数", row=4, col=1, secondary_y=True)

        
        fig.update_layout(
            title_text=title or '回测结果',
            showlegend=True,
            grid={'rows': 4, 'columns': 1, 'pattern': "independent"}
        )

        
        fig.update_xaxes(title_text="时间", row=4, col=1)
        fig.update_yaxes(title_text="价格", row=1, col=1)

        fig.show()
        backtest_path = os.path.join(self.plots_dir, 'backtest_results.html')
        fig.write_html(backtest_path)

    def plot_trade_analysis(self, trades_data, returns, standardize_qq=True, title=None):
        
        if trades_data is None or len(trades_data) == 0:
            print("数据为空，无法绘制交易分析图")
            return
        
        close_trades = trades_data[trades_data['type'] == 'close']

        
        required_cols = ['entry_time', 'exit_time', 'return']
        missing_cols = [col for col in required_cols if col not in close_trades.columns]
        if missing_cols:
            print(f"数据缺少必要的列: {missing_cols}")
            return

        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('交易收益分布', 'QQ图', '持仓时间分布', '盈亏交易数量')
        )

        
        non_zero_returns = returns[returns != 0]

        if len(non_zero_returns) == 0:
            log_print("没有交易记录，无法绘制图表", 'warning')
            return None

        
        fig.add_trace(
            go.Histogram(
                x=non_zero_returns,
                name='收益率',
                nbinsx=50,
                histnorm='probability density',
                marker_color='rgba(0, 114, 178, 0.6)',
                marker_line=dict(width=1, color='rgb(0, 0, 0)')
            ),
            row=1, col=1
        )

        
        fig.add_vline(x=0, line_dash="dash", line_color="red", row=1, col=1)

        
        mu = non_zero_returns.mean()
        sigma = non_zero_returns.std()
        x = np.linspace(mu - 4 * sigma, mu + 4 * sigma, 200)
        y = 1 / (sigma * np.sqrt(2 * np.pi)) * np.exp(-(x - mu) ** 2 / (2 * sigma ** 2))

        fig.add_trace(
            go.Scatter(
                x=x,
                y=y,
                name='正态分布拟合',
                line=dict(color='red', width=2)
            ),
            row=1, col=1
        )

        
        
        n = len(non_zero_returns)
        
        if standardize_qq:
            
            plot_data = (non_zero_returns - mu) / sigma
        else:
            
            plot_data = non_zero_returns

        
        sorted_data = np.sort(plot_data)
        
        quantiles = np.arange(1, n + 1) / (n + 1)
        
        theoretical_quantiles = stats.norm.ppf(quantiles)

        fig.add_trace(
            go.Scatter(
                x=theoretical_quantiles,
                y=sorted_data,
                mode='markers',
                name='QQ图',
                marker=dict(size=6, color='blue', opacity=0.7)
            ),
            row=1, col=2
        )

        
        min_val = min(theoretical_quantiles.min(), sorted_data.min())
        max_val = max(theoretical_quantiles.max(), sorted_data.max())

        fig.add_trace(
            go.Scatter(
                x=[min_val, max_val],
                y=[min_val, max_val],
                mode='lines',
                name='正态参考线',
                line=dict(color='red', dash='dash', width=2)
            ),
            row=1, col=2
        )

        
        mean_return = non_zero_returns.mean()
        median_return = non_zero_returns.median()
        std_return = non_zero_returns.std()
        skew = stats.skew(non_zero_returns)
        kurtosis = stats.kurtosis(non_zero_returns)
        
        
        close_trades['profit'] = close_trades['return'] > 0
        profit_count = close_trades['profit'].sum()
        loss_count = len(close_trades) - profit_count
        win_rate = profit_count / len(close_trades)
        
        
        win_p=close_trades[close_trades['return']>0]['return'].abs().mean()
        loss_p=close_trades[close_trades['return']<0]['return'].abs().mean()
        win_loss_ratio=win_p/loss_p

        
        stats_text = (
            f"平均收益率: {mean_return:.2%} | "
            f"中位收益率: {median_return:.2%} | "
            f"收益率标准差: {std_return:.2%} | "
            f"偏度: {skew:.2f} | "
            f"峰度: {kurtosis:.2f}<br>"
            f"胜率: {win_rate:.2%} | "
            f"平均盈利: {win_p:.2%} | "
            f"平均亏损: {loss_p:.2%} | "
            f"盈亏比: {win_loss_ratio:.2f} | "
            f"总平仓次数: {len(close_trades)} | "
            f"总交易次数: {len(trades_data)}"
        )

        fig.add_annotation(
            xref="paper", yref="paper",
            text=stats_text,
            x=0.5,
            y=0.5,  
            showarrow=False,
            font=dict(size=12),
            align="center",
        )
        
        
        close_trades['holding_period_group'] = pd.cut(close_trades['holding_period'], bins=5, labels=['<5s', '5-10s', '10-15s', '15-20s', '>20s'])
        
        holding_period_groups = close_trades.groupby('holding_period_group')
        
        win_rate_by_group = holding_period_groups['profit'].mean()
        
        count_by_group = holding_period_groups['return'].count()

        
        avg_profit_by_group = holding_period_groups.apply(
            lambda x: x[x['return'] > 0]['return'].abs().mean() if len(x[x['return'] > 0]) > 0 else 0
        )
        avg_loss_by_group = holding_period_groups.apply(
            lambda x: x[x['return'] < 0]['return'].abs().mean() if len(x[x['return'] < 0]) > 0 else float('inf')
        )
        
        
        win_loss_ratio_by_group = avg_profit_by_group / avg_loss_by_group.replace(0, float('inf'))
        win_loss_ratio_by_group = win_loss_ratio_by_group.replace(float('inf'), 0)
        
        fig.add_trace(
            go.Bar(x=win_rate_by_group.index, y=count_by_group,
                   name='持仓分布',
                   text=[f'数量{count_by_group[i]}<br>胜率{win_rate_by_group[i]:.2%}<br>盈亏比{win_loss_ratio_by_group[i]:.2f}' for i in range(len(win_loss_ratio_by_group))],
                   textposition='auto'),
            row=2, col=1
        )
        
        
        fig.add_trace(
            go.Bar(x=[f'盈利 {win_p:.2}', f'亏损 {loss_p:.2}'],
                   y=[profit_count, loss_count],
                   marker_color=['green', 'red'],
                   name='盈亏平仓次数',
                   text=[f'{profit_count} ({win_rate:.1%})', f'{loss_count} ({1 - win_rate:.1%})'],
                   textposition='auto'),
            row=2, col=2
        )

        
        fig.update_layout(
            title_text=title or '交易分析',
            showlegend=False,
            grid={'rows': 2, 'columns': 2, 'pattern': "independent"}
        )

        
        fig.update_xaxes(title_text="收益", row=1, col=1)
        fig.update_xaxes(title_text="理论分位数", row=1, col=2)
        fig.update_xaxes(title_text="持仓时间 (s)", row=2, col=1)
        fig.update_xaxes(title_text="交易类型", row=2, col=2)

        fig.update_yaxes(title_text="概率密度", row=1, col=1)
        fig.update_yaxes(title_text="样本分位数", row=1, col=2)
        fig.update_yaxes(title_text="频率", row=2, col=1)
        fig.update_yaxes(title_text="平仓次数", row=2, col=2)

        fig.show()
        trade_path = os.path.join(self.plots_dir, 'trade_analysis.html')
        fig.write_html(trade_path)

    def plot_shap_values(self, shap_data, title=None, max_display=config.VISUALIZE_TOP_N):
        
        if not SHAP_AVAILABLE:
            log_print("缺少shap库，无法绘制SHAP值图表", 'warning')
            return

        log_print("绘制SHAP值图表...")
        try:
            shap_values = shap_data['values']
            feature_data = shap_data['data']
            feature_names = shap_data['feature_names']
            
            
            plt.figure()
            shap.summary_plot(shap_values, feature_data, feature_names=feature_names, 
                              max_display=max_display, show=False)
            
            
            title = title or 'SHAP值分析'
            plt.title(title)
            plt.tight_layout()
            
            
            shap_path = os.path.join(self.plots_dir, 'shap_values.png')
            plt.savefig(shap_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            log_print(f"SHAP值图表已保存至 {shap_path}")

        except Exception as e:
            log_print(f"绘制SHAP值图表失败: {e}")
    
    def plot_shap_summary(self, shap_importance_df, title=None, top_n=config.VISUALIZE_TOP_N):
        
        
        required_cols = ['feature', 'importance']
        missing_cols = [col for col in required_cols if col not in shap_importance_df.columns]
        if missing_cols:
            log_print(f"数据缺少必要的列: {missing_cols}", 'warning')
            return
        
        log_print("绘制SHAP重要性条形图...")
        
        
        df = shap_importance_df.copy()
        if len(df) > top_n:
            df = df.head(top_n)
        
        
        fig = go.Figure()
        
        
        df = df.sort_values(by='importance', ascending=True)
        fig.add_trace(
            go.Bar(x=df['importance'],
                   y=df['feature'],
                   orientation='h',
                   marker_color='#00AFBB',
                   name='SHAP重要性')
        )
        
        
        fig.update_layout(
            title_text=title or 'SHAP特征重要性',
            xaxis_title="平均|SHAP值|",
            yaxis_title="特征",
            showlegend=False
        )
        
        fig.show()
        
        
        importance_path = os.path.join(self.plots_dir, 'shap_importance.html')
        fig.write_html(importance_path)
        
        log_print(f"SHAP重要性条形图已保存至 {importance_path}")
    
    def plot_shap_dependence(self, shap_data, feature,
                           interaction_idx="auto", title=None):
        
        if not SHAP_AVAILABLE:
            log_print("缺少shap库，无法绘制SHAP依赖图", 'warning')
            return
        
        shap_values = shap_data['values']
        feature_data = shap_data['data']
        feature_names = shap_data['feature_names']
        feature_idx = feature_names.index(feature)
        
        
        log_print(f"绘制特征'{feature_idx}'的SHAP依赖图...")
        
        try:
            
            
            feature_name = feature_names[feature_idx] if feature_names is not None else str(feature_idx)
            
            
            
            shap.dependence_plot(
                feature_idx, 
                shap_values, 
                feature_data, 
                feature_names=feature_names, 
                interaction_index=interaction_idx,
                show=True
            )
            
            
            title = title or f'特征"{feature_name}"的SHAP依赖分析'
            plt.title(title)
            
            
            plt.tight_layout()
            
            
            safe_feature_name = "".join([c if c.isalnum() else "_" for c in feature_name])
            shap_path = os.path.join(self.plots_dir, f'shap_dependence_{safe_feature_name}.png')
            plt.savefig(shap_path, dpi=300, bbox_inches='tight')
            plt.close()
            log_print(f"SHAP依赖图已保存至 {shap_path}")
            
        except Exception as e:
            log_print(f"绘制SHAP依赖图失败: {e}")