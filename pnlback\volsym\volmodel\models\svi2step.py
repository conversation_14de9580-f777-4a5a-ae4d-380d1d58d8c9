import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
import pandas as pd
import scipy.stats as stats
from scipy.integrate import quad
from scipy.optimize import minimize,linprog
import scipy.optimize as opt
from scipy.interpolate import interp1d
import warnings
warnings.filterwarnings("ignore")
plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.rcParams['axes.unicode_minus']=False #正常显示负号
pd.set_option('display.max_columns',None)
pd.set_option('display.max_rows',None)


class svi_2step:
    def __init__(self, F, K, T, imp_vol, opt_paras):
        self.x = np.log(K / F)  # 远期在值程度
        self.T = T  # 到期时间
        self.imp_vol = imp_vol  # 隐含波动率
        self.w = imp_vol ** 2 * T  # 总方差
        # opt_paras：优化的a,d,c,m,sigma参数，需要字典形式如，{"a":0.0005,"d":-0.005,"c":0.01,"m":0.1,"sigma":0.1}
        # 初始传入的作为优化的初始值，随着优化算法迭代更新为最优值
        self.opt_paras = opt_paras

    def outer_func(self, m_sigma):
        m, sigma = m_sigma
        y = (self.x - m) / sigma

        def inner_func(adc):
            a, d, c = adc
            err = np.sum((a + d * y + c * np.sqrt(y ** 2 + 1) - self.w) ** 2)
            return err

        init_adc = (self.opt_paras["a"], self.opt_paras["d"], self.opt_paras["c"])
        bnds = ([1e-6, np.max(self.w)], [-4 * sigma, 4 * sigma], [1e-6, 4 * sigma])
        cons = (
            {"type": "ineq", "fun": lambda x: x[2] - np.abs(x[1])},
            {"type": "ineq", "fun": lambda x: 4 * sigma - x[2] - np.abs(x[1])}
        )
        a_star, d_star, c_star = minimize(fun=inner_func, x0=init_adc, method="SLSQP", bounds=bnds, constraints=cons).x

        self.opt_paras["a"], self.opt_paras["d"], self.opt_paras["c"] = a_star, d_star, c_star

        err = np.sum((a_star + d_star * y + c_star * np.sqrt(y ** 2 + 1) - self.w) ** 2)
        return err

    def fit(self):
        init_m_sigma = (self.opt_paras["m"], self.opt_paras["sigma"])
        m_star, sigma_star = minimize(fun=self.outer_func, x0=init_m_sigma, method="Nelder-Mead",
                                      bounds=((2 * min(self.x.min(), 0), 2 * max(self.x.max(), 0)), (1e-6, 1))).x
        self.opt_paras["m"], self.opt_paras["sigma"] = m_star, sigma_star
        return self.opt_paras

    def eval(self, x, output):
        y = (x - self.opt_paras["m"]) / self.opt_paras["sigma"]
        svi_w = self.opt_paras["a"] + self.opt_paras["d"] * y + self.opt_paras["c"] * np.sqrt(y ** 2 + 1)
        svi_imp_vol = np.sqrt(svi_w / self.T)
        if output == "w":
            return svi_w
        elif output == "imp_vol":
            return svi_imp_vol


def fit_svi_model(config, exp_data, exp, last_exp_data):
    """使用SVI模型拟合波动率曲线

    Args:
        config: 配置参数
        exp_data: 期权数据
        exp: 到期日
        last_exp_data: 上一次的拟合结果

    Returns:
        tuple: (sigma_fit, voltime, derivatives)
    """
    # 准备输入数据

    # 计算vega权重
    vega = exp_data['vega']

    opt_method=svi_2step

    fit_result = []

    # 循环每个月份，获得相应的拟合函数，返回一个包含svi实例的列表
    F = exp_data['forward'].iloc[0]
    K = exp_data['K']
    x = np.log(K / F)
    T = exp_data['time2expiry'].iloc[0]
    imp_vol = exp_data['market_sigma']
    opt_paras = {"a": 0.0005, "d": -0.005, "c": 0.01, "m": 0.1, "sigma": 0.1}  # 推荐的初始值，根据数据的情况调整
    svi = opt_method(F, K, T, imp_vol, opt_paras)
    svi.fit()

    sigma_fit = svi.eval(svi.x, output="imp_vol")

    atm_features = {'atm_vol': 0, 'skew': 0, 'convexity': 0,
                    'otm_slope': 0,
                    'itm_slope': 0}

    rmse_error = np.sqrt(np.mean((vega * (sigma_fit - exp_data['market_sigma'])) ** 2))

    r2 = 1 - np.sum((exp_data['market_sigma'] - sigma_fit) ** 2) / np.sum(
        (exp_data['market_sigma'] - np.mean(exp_data['market_sigma'])) ** 2)

    # 创建包含所有数据的字典
    voltime = {
        **svi.opt_paras,  # SVI参数
        **atm_features,  # ATM特征
        'rmse_error': rmse_error,
        'r2': r2
    }

    return exp, sigma_fit, voltime