[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600004', 'instrumentName': '白云机场', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600061', 'instrumentName': '国投资本', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600118', 'instrumentName': '中国卫星', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600153', 'instrumentName': '建发股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600176', 'instrumentName': '中国巨石', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600177', 'instrumentName': '雅戈尔', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600256', 'instrumentName': '广汇能源', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600435', 'instrumentName': '北方导航', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600460', 'instrumentName': '士兰微', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600497', 'instrumentName': '驰宏锌锗', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600549', 'instrumentName': '厦门钨业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600737', 'instrumentName': '中粮糖业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600895', 'instrumentName': '张江高科', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600977', 'instrumentName': '中国电影', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601068', 'instrumentName': '中铝国际', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': '          554.000', 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601555', 'instrumentName': '东吴证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 4700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601869', 'instrumentName': '长飞光纤', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601919', 'instrumentName': '中远海控', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 4700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': '            0.000', 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': None, 'quantity': 0, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 6400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600025', 'instrumentName': '华能水电', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 9600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 7400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 5100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600208', 'instrumentName': '新湖中宝', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600466', 'instrumentName': '蓝光发展', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 8500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 4700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600926', 'instrumentName': '杭州银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 10400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 10600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 5100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 27500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 19700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601377', 'instrumentName': '兴业证券', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 5400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 15500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 5100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 15100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 5500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 11400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 15100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601992', 'instrumentName': '金隅集团', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601997', 'instrumentName': '贵阳银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603858', 'instrumentName': '步长制药', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 5600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 11800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 6300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 4900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 6900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 18200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 5100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 13000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 10200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 10000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 7500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 10000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 5700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 8600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 6600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 6000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600176', 'instrumentName': '中国巨石', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600392', 'instrumentName': '盛和资源', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600760', 'instrumentName': '中航沈飞', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 27300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 19900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': '          550.000', 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 13500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 4900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 6200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601788', 'instrumentName': '光大证券', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 12600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 5200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 6700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 19100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 5900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600079', 'instrumentName': '人福医药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600155', 'instrumentName': '华创阳安', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600177', 'instrumentName': '雅戈尔', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600208', 'instrumentName': '新湖中宝', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600256', 'instrumentName': '广汇能源', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600291', 'instrumentName': '西水股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600346', 'instrumentName': '恒力石化', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600352', 'instrumentName': '浙江龙盛', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600446', 'instrumentName': '金证股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600487', 'instrumentName': '亨通光电', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600521', 'instrumentName': '华海药业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600570', 'instrumentName': '恒生电子', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600699', 'instrumentName': '均胜电子', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600816', 'instrumentName': '安信信托', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600884', 'instrumentName': '杉杉股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601012', 'instrumentName': '隆基股份', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601216', 'instrumentName': '君正集团', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601233', 'instrumentName': '桐昆股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601360', 'instrumentName': '三六零', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601615', 'instrumentName': '明阳智能', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603160', 'instrumentName': '汇顶科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603260', 'instrumentName': '合盛硅业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603444', 'instrumentName': '吉比特', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603799', 'instrumentName': '华友钴业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603986', 'instrumentName': '兆易创新', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': None, 'quantity': 2600, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '600004', 'instrumentName': '白云机场', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600021', 'instrumentName': '上海电力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': None, 'quantity': 2300, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600056', 'instrumentName': '中国医药', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600062', 'instrumentName': '华润双鹤', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600064', 'instrumentName': '南京高科', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600123', 'instrumentName': '兰花科创', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600138', 'instrumentName': '中青旅', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600141', 'instrumentName': '兴发集团', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600153', 'instrumentName': '建发股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600161', 'instrumentName': '天坛生物', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600176', 'instrumentName': '中国巨石', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600177', 'instrumentName': '雅戈尔', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600195', 'instrumentName': '中牧股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600266', 'instrumentName': '北京城建', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600325', 'instrumentName': '华发股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600335', 'instrumentName': '国机汽车', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600373', 'instrumentName': '中文传媒', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600376', 'instrumentName': '首开股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600422', 'instrumentName': '昆药集团', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600458', 'instrumentName': '时代新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600502', 'instrumentName': '安徽建工', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600511', 'instrumentName': '国药股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600549', 'instrumentName': '厦门钨业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600582', 'instrumentName': '天地科技', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600597', 'instrumentName': '光明乳业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600663', 'instrumentName': '陆家嘴', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': '         2046.000', 'instrumentId': '600685', 'instrumentName': '中船防务', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600704', 'instrumentName': '物产中大', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600708', 'instrumentName': '光明地产', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600718', 'instrumentName': '东软集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600748', 'instrumentName': '上实发展', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600750', 'instrumentName': '江中药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600755', 'instrumentName': '厦门国贸', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600835', 'instrumentName': '上海机电', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600875', 'instrumentName': '东方电气', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600879', 'instrumentName': '航天电子', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600990', 'instrumentName': '四创电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600993', 'instrumentName': '马应龙', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': None, 'quantity': 3300, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': None, 'quantity': 2400, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': None, 'quantity': 6200, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601555', 'instrumentName': '东吴证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': None, 'quantity': 700, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601611', 'instrumentName': '中国核建', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': None, 'quantity': 4700, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601869', 'instrumentName': '长飞光纤', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603018', 'instrumentName': '中设集团', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603365', 'instrumentName': '水星家纺', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603444', 'instrumentName': '吉比特', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603515', 'instrumentName': '欧普照明', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603708', 'instrumentName': '家家悦', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603883', 'instrumentName': '老百姓', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600009', 'instrumentName': '上海机场', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600010', 'instrumentName': '包钢股份', 'premiumRate': '10%', 'quantity': 4700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 6400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601012', 'instrumentName': '隆基股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601108', 'instrumentName': '财通证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601162', 'instrumentName': '天风证券', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 9900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601298', 'instrumentName': '青岛港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 7100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 5600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 4100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 5500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603799', 'instrumentName': '华友钴业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600025', 'instrumentName': '华能水电', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600038', 'instrumentName': '中直股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600118', 'instrumentName': '中国卫星', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600153', 'instrumentName': '建发股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600160', 'instrumentName': '巨化股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600297', 'instrumentName': '广汇汽车', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600298', 'instrumentName': '安琪酵母', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600346', 'instrumentName': '恒力石化', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600352', 'instrumentName': '浙江龙盛', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600372', 'instrumentName': '中航电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600409', 'instrumentName': '三友化工', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600426', 'instrumentName': '华鲁恒升', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600435', 'instrumentName': '北方导航', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600446', 'instrumentName': '金证股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600482', 'instrumentName': '中国动力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600487', 'instrumentName': '亨通光电', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600521', 'instrumentName': '华海药业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600536', 'instrumentName': '中国软件', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600570', 'instrumentName': '恒生电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600584', 'instrumentName': '长电科技', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600674', 'instrumentName': '川投能源', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600699', 'instrumentName': '均胜电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600718', 'instrumentName': '东软集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600733', 'instrumentName': '北汽蓝谷', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600739', 'instrumentName': '辽宁成大', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600760', 'instrumentName': '中航沈飞', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600809', 'instrumentName': '山西汾酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600879', 'instrumentName': '航天电子', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600884', 'instrumentName': '杉杉股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600895', 'instrumentName': '张江高科', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600977', 'instrumentName': '中国电影', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601216', 'instrumentName': '君正集团', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601233', 'instrumentName': '桐昆股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601360', 'instrumentName': '三六零', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601615', 'instrumentName': '明阳智能', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 5500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603019', 'instrumentName': '中科曙光', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603156', 'instrumentName': '养元饮品', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': '        19705.000', 'instrumentId': '603160', 'instrumentName': '汇顶科技', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': '         8220.000', 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': '         3052.000', 'instrumentId': '603260', 'instrumentName': '合盛硅业', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603369', 'instrumentName': '今世缘', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': '         2614.000', 'instrumentId': '603712', 'instrumentName': '七一二', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603858', 'instrumentName': '步长制药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': '        14190.000', 'instrumentId': '603986', 'instrumentName': '兆易创新', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}]
[{'cashAmount': None, 'instrumentId': '600004', 'instrumentName': '白云机场', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600009', 'instrumentName': '上海机场', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600010', 'instrumentName': '包钢股份', 'premiumRate': '10%', 'quantity': 10000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600025', 'instrumentName': '华能水电', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600038', 'instrumentName': '中直股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600061', 'instrumentName': '国投资本', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600111', 'instrumentName': '北方稀土', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600118', 'instrumentName': '中国卫星', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600176', 'instrumentName': '中国巨石', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600208', 'instrumentName': '新湖中宝', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600291', 'instrumentName': '西水股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600297', 'instrumentName': '广汇汽车', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600298', 'instrumentName': '安琪酵母', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600352', 'instrumentName': '浙江龙盛', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600426', 'instrumentName': '华鲁恒升', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600466', 'instrumentName': '蓝光发展', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600482', 'instrumentName': '中国动力', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600487', 'instrumentName': '亨通光电', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600570', 'instrumentName': '恒生电子', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600643', 'instrumentName': '爱建集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600699', 'instrumentName': '均胜电子', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600733', 'instrumentName': '北汽蓝谷', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600760', 'instrumentName': '中航沈飞', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 6500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600809', 'instrumentName': '山西汾酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600816', 'instrumentName': '安信信托', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600872', 'instrumentName': '中炬高新', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600884', 'instrumentName': '杉杉股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600895', 'instrumentName': '张江高科', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600901', 'instrumentName': '江苏租赁', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600903', 'instrumentName': '贵州燃气', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600909', 'instrumentName': '华安证券', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600926', 'instrumentName': '杭州银行', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600977', 'instrumentName': '中国电影', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601012', 'instrumentName': '隆基股份', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601099', 'instrumentName': '太平洋', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601108', 'instrumentName': '财通证券', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601162', 'instrumentName': '天风证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 8100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601198', 'instrumentName': '东兴证券', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601233', 'instrumentName': '桐昆股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601298', 'instrumentName': '青岛港', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601360', 'instrumentName': '三六零', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601377', 'instrumentName': '兴业证券', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601555', 'instrumentName': '东吴证券', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601577', 'instrumentName': '长沙银行', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601615', 'instrumentName': '明阳智能', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601788', 'instrumentName': '光大证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601860', 'instrumentName': '紫金银行', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601878', 'instrumentName': '浙商证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 6600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601901', 'instrumentName': '方正证券', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601990', 'instrumentName': '南京证券', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601992', 'instrumentName': '金隅集团', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601997', 'instrumentName': '贵阳银行', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603013', 'instrumentName': '亚普股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603019', 'instrumentName': '中科曙光', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603156', 'instrumentName': '养元饮品', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603160', 'instrumentName': '汇顶科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603260', 'instrumentName': '合盛硅业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603369', 'instrumentName': '今世缘', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603486', 'instrumentName': '科沃斯', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603589', 'instrumentName': '口子窖', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603799', 'instrumentName': '华友钴业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603858', 'instrumentName': '步长制药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603986', 'instrumentName': '兆易创新', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600056', 'instrumentName': '中国医药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600062', 'instrumentName': '华润双鹤', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600138', 'instrumentName': '中青旅', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600161', 'instrumentName': '天坛生物', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600177', 'instrumentName': '雅戈尔', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600201', 'instrumentName': '生物股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600216', 'instrumentName': '浙江医药', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600258', 'instrumentName': '首旅酒店', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600297', 'instrumentName': '广汇汽车', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600298', 'instrumentName': '安琪酵母', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600299', 'instrumentName': '安迪苏', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600315', 'instrumentName': '上海家化', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600373', 'instrumentName': '中文传媒', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600380', 'instrumentName': '健康元', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600511', 'instrumentName': '国药股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600521', 'instrumentName': '华海药业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600559', 'instrumentName': '老白干酒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600623', 'instrumentName': '华谊集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600655', 'instrumentName': '豫园股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600682', 'instrumentName': '南京新百', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600699', 'instrumentName': '均胜电子', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600702', 'instrumentName': '舍得酒业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600729', 'instrumentName': '重庆百货', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600733', 'instrumentName': '北汽蓝谷', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600737', 'instrumentName': '中粮糖业', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600754', 'instrumentName': '锦江酒店', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600763', 'instrumentName': '通策医疗', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600771', 'instrumentName': '广誉远', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600779', 'instrumentName': '水井坊', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600789', 'instrumentName': '鲁抗医药', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600809', 'instrumentName': '山西汾酒', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600839', 'instrumentName': '四川长虹', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600859', 'instrumentName': '王府井', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600872', 'instrumentName': '中炬高新', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 6200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600929', 'instrumentName': '湖南盐业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600977', 'instrumentName': '中国电影', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601098', 'instrumentName': '中南传媒', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603013', 'instrumentName': '亚普股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603156', 'instrumentName': '养元饮品', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603369', 'instrumentName': '今世缘', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603486', 'instrumentName': '科沃斯', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603515', 'instrumentName': '欧普照明', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603587', 'instrumentName': '地素时尚', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603589', 'instrumentName': '口子窖', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603596', 'instrumentName': '伯特利', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603658', 'instrumentName': '安图生物', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603816', 'instrumentName': '顾家家居', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603833', 'instrumentName': '欧派家居', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603858', 'instrumentName': '步长制药', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603883', 'instrumentName': '老百姓', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603939', 'instrumentName': '益丰药房', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600009', 'instrumentName': '上海机场', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600010', 'instrumentName': '包钢股份', 'premiumRate': '10%', 'quantity': 6200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 4100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 8500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600025', 'instrumentName': '华能水电', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 10500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600061', 'instrumentName': '国投资本', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600208', 'instrumentName': '新湖中宝', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600297', 'instrumentName': '广汇汽车', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600352', 'instrumentName': '浙江龙盛', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600482', 'instrumentName': '中国动力', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 13100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600926', 'instrumentName': '杭州银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': '         2247.000', 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601233', 'instrumentName': '桐昆股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601298', 'instrumentName': '青岛港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 4900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601577', 'instrumentName': '长沙银行', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 4100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601788', 'instrumentName': '光大证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 6100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601901', 'instrumentName': '方正证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601992', 'instrumentName': '金隅集团', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601997', 'instrumentName': '贵阳银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': '        11296.000', 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 7600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600111', 'instrumentName': '北方稀土', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600160', 'instrumentName': '巨化股份', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600219', 'instrumentName': '南山铝业', 'premiumRate': '10%', 'quantity': 8400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600226', 'instrumentName': '瀚叶股份', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600230', 'instrumentName': '沧州大化', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600256', 'instrumentName': '广汇能源', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600273', 'instrumentName': '嘉化能源', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600277', 'instrumentName': '亿利洁能', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600348', 'instrumentName': '阳泉煤业', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600392', 'instrumentName': '盛和资源', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600409', 'instrumentName': '三友化工', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600426', 'instrumentName': '华鲁恒升', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600486', 'instrumentName': '扬农化工', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600490', 'instrumentName': '鹏欣资源', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600497', 'instrumentName': '驰宏锌锗', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600549', 'instrumentName': '厦门钨业', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600596', 'instrumentName': '新安股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600673', 'instrumentName': '东阳光', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600737', 'instrumentName': '中粮糖业', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600740', 'instrumentName': '山西焦化', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600759', 'instrumentName': '洲际油气', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600777', 'instrumentName': '新潮能源', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601011', 'instrumentName': '宝泰隆', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601069', 'instrumentName': '西部黄金', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601168', 'instrumentName': '西部矿业', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601212', 'instrumentName': '白银有色', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601216', 'instrumentName': '君正集团', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 7700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 5700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601898', 'instrumentName': '中煤能源', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 12300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601958', 'instrumentName': '金钼股份', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603077', 'instrumentName': '和邦生物', 'premiumRate': '10%', 'quantity': 6200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603260', 'instrumentName': '合盛硅业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603733', 'instrumentName': '仙鹤股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603799', 'instrumentName': '华友钴业', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603876', 'instrumentName': '鼎胜新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 8300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600004', 'instrumentName': '白云机场', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600009', 'instrumentName': '上海机场', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600010', 'instrumentName': '包钢股份', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 5200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600025', 'instrumentName': '华能水电', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600038', 'instrumentName': '中直股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600061', 'instrumentName': '国投资本', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600111', 'instrumentName': '北方稀土', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600118', 'instrumentName': '中国卫星', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600176', 'instrumentName': '中国巨石', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600208', 'instrumentName': '新湖中宝', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600291', 'instrumentName': '西水股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600297', 'instrumentName': '广汇汽车', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600298', 'instrumentName': '安琪酵母', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600352', 'instrumentName': '浙江龙盛', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600426', 'instrumentName': '华鲁恒升', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600466', 'instrumentName': '蓝光发展', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600482', 'instrumentName': '中国动力', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600487', 'instrumentName': '亨通光电', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600570', 'instrumentName': '恒生电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600643', 'instrumentName': '爱建集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600699', 'instrumentName': '均胜电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600733', 'instrumentName': '北汽蓝谷', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600760', 'instrumentName': '中航沈飞', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600809', 'instrumentName': '山西汾酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600816', 'instrumentName': '安信信托', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600872', 'instrumentName': '中炬高新', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600884', 'instrumentName': '杉杉股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600895', 'instrumentName': '张江高科', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600901', 'instrumentName': '江苏租赁', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600903', 'instrumentName': '贵州燃气', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600909', 'instrumentName': '华安证券', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600926', 'instrumentName': '杭州银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600977', 'instrumentName': '中国电影', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601012', 'instrumentName': '隆基股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601099', 'instrumentName': '太平洋', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601108', 'instrumentName': '财通证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601162', 'instrumentName': '天风证券', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601198', 'instrumentName': '东兴证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601233', 'instrumentName': '桐昆股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 8000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601298', 'instrumentName': '青岛港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 5700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601360', 'instrumentName': '三六零', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601377', 'instrumentName': '兴业证券', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601555', 'instrumentName': '东吴证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601577', 'instrumentName': '长沙银行', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601615', 'instrumentName': '明阳智能', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601788', 'instrumentName': '光大证券', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601860', 'instrumentName': '紫金银行', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601878', 'instrumentName': '浙商证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601901', 'instrumentName': '方正证券', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601990', 'instrumentName': '南京证券', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601992', 'instrumentName': '金隅集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601997', 'instrumentName': '贵阳银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603013', 'instrumentName': '亚普股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603019', 'instrumentName': '中科曙光', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603156', 'instrumentName': '养元饮品', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603160', 'instrumentName': '汇顶科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603260', 'instrumentName': '合盛硅业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603369', 'instrumentName': '今世缘', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603486', 'instrumentName': '科沃斯', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603589', 'instrumentName': '口子窖', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603799', 'instrumentName': '华友钴业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603858', 'instrumentName': '步长制药', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603986', 'instrumentName': '兆易创新', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 4100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600258', 'instrumentName': '首旅酒店', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600315', 'instrumentName': '上海家化', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600373', 'instrumentName': '中文传媒', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600390', 'instrumentName': '五矿资本', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600487', 'instrumentName': '亨通光电', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': '       114400.000', 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600570', 'instrumentName': '恒生电子', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600584', 'instrumentName': '长电科技', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600655', 'instrumentName': '豫园股份', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600729', 'instrumentName': '重庆百货', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600739', 'instrumentName': '辽宁成大', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600754', 'instrumentName': '锦江酒店', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600755', 'instrumentName': '厦门国贸', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600763', 'instrumentName': '通策医疗', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600816', 'instrumentName': '安信信托', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600859', 'instrumentName': '王府井', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600977', 'instrumentName': '中国电影', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 5700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601360', 'instrumentName': '三六零', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603019', 'instrumentName': '中科曙光', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603160', 'instrumentName': '汇顶科技', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603708', 'instrumentName': '家家悦', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603833', 'instrumentName': '欧派家居', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603986', 'instrumentName': '兆易创新', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600006', 'instrumentName': '东风汽车', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600007', 'instrumentName': '中国国贸', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600008', 'instrumentName': '首创股份', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600020', 'instrumentName': '中原高速', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 6700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600061', 'instrumentName': '国投资本', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600062', 'instrumentName': '华润双鹤', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600075', 'instrumentName': '新疆天业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600108', 'instrumentName': '亚盛集团', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600111', 'instrumentName': '北方稀土', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600116', 'instrumentName': '三峡水利', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600118', 'instrumentName': '中国卫星', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600143', 'instrumentName': '金发科技', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600206', 'instrumentName': '有研新材', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600208', 'instrumentName': '新湖中宝', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600220', 'instrumentName': '江苏阳光', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600221', 'instrumentName': '海航控股', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600230', 'instrumentName': '沧州大化', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600233', 'instrumentName': '圆通速递', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600236', 'instrumentName': '桂冠电力', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600258', 'instrumentName': '首旅酒店', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600273', 'instrumentName': '嘉化能源', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600279', 'instrumentName': '重庆港九', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600305', 'instrumentName': '恒顺醋业', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600307', 'instrumentName': '酒钢宏兴', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600326', 'instrumentName': '西藏天路', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600337', 'instrumentName': '美克家居', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600343', 'instrumentName': '航天动力', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600346', 'instrumentName': '恒力石化', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600350', 'instrumentName': '山东高速', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600352', 'instrumentName': '浙江龙盛', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600360', 'instrumentName': '华微电子', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600369', 'instrumentName': '西南证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600372', 'instrumentName': '中航电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600435', 'instrumentName': '北方导航', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600459', 'instrumentName': '贵研铂业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600483', 'instrumentName': '福能股份', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600487', 'instrumentName': '亨通光电', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600490', 'instrumentName': '鹏欣资源', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600503', 'instrumentName': '华丽家族', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600537', 'instrumentName': '亿晶光电', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600557', 'instrumentName': '康缘药业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600568', 'instrumentName': '中珠医疗', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600575', 'instrumentName': '皖江物流', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600583', 'instrumentName': '海油工程', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600589', 'instrumentName': '广东榕泰', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600616', 'instrumentName': '金枫酒业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600624', 'instrumentName': '复旦复华', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600663', 'instrumentName': '陆家嘴', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600664', 'instrumentName': '哈药股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600674', 'instrumentName': '川投能源', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600682', 'instrumentName': '南京新百', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600688', 'instrumentName': '上海石化', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600697', 'instrumentName': '欧亚集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600715', 'instrumentName': '文投控股', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600728', 'instrumentName': '佳都科技', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600758', 'instrumentName': '红阳能源', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600770', 'instrumentName': '综艺股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600779', 'instrumentName': '水井坊', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600798', 'instrumentName': '宁波海运', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': '         4690.000', 'instrumentId': '600803', 'instrumentName': '新奥股份', 'premiumRate': None, 'quantity': 500, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600814', 'instrumentName': '杭州解百', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600820', 'instrumentName': '隧道股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600834', 'instrumentName': '申通地铁', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600838', 'instrumentName': '上海九百', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600850', 'instrumentName': '华东电脑', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600862', 'instrumentName': '中航高科', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600863', 'instrumentName': '内蒙华电', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600909', 'instrumentName': '华安证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600960', 'instrumentName': '渤海汽车', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600975', 'instrumentName': '新五丰', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600979', 'instrumentName': '广安爱众', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600987', 'instrumentName': '航民股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600988', 'instrumentName': '赤峰黄金', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600998', 'instrumentName': '九州通', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601012', 'instrumentName': '隆基股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601016', 'instrumentName': '节能风电', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601018', 'instrumentName': '宁波港', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601069', 'instrumentName': '西部黄金', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601098', 'instrumentName': '中南传媒', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601128', 'instrumentName': '常熟银行', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601158', 'instrumentName': '重庆水务', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601168', 'instrumentName': '西部矿业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601179', 'instrumentName': '中国西电', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601212', 'instrumentName': '白银有色', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601231', 'instrumentName': '环旭电子', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 21100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601368', 'instrumentName': '绿城水务', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601375', 'instrumentName': '中原证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601377', 'instrumentName': '兴业证券', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': '          550.000', 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '601515', 'instrumentName': '东风股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601555', 'instrumentName': '东吴证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601579', 'instrumentName': '会稽山', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601588', 'instrumentName': '北辰实业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601678', 'instrumentName': '滨化股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601801', 'instrumentName': '皖新传媒', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601811', 'instrumentName': '新华文轩', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 4100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 10700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601890', 'instrumentName': '亚星锚链', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601901', 'instrumentName': '方正证券', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 16000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601992', 'instrumentName': '金隅集团', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603003', 'instrumentName': '龙宇燃油', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603058', 'instrumentName': '永吉股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603060', 'instrumentName': '国检集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603159', 'instrumentName': '上海亚虹', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603328', 'instrumentName': '依顿电子', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603393', 'instrumentName': '新天然气', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603528', 'instrumentName': '多伦科技', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603559', 'instrumentName': '中通国脉', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603599', 'instrumentName': '广信股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603628', 'instrumentName': '清源股份', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603789', 'instrumentName': '星光农机', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603859', 'instrumentName': '能科股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603878', 'instrumentName': '武进不锈', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603901', 'instrumentName': '永创智能', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603968', 'instrumentName': '醋化股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603969', 'instrumentName': '银龙股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600004', 'instrumentName': '白云机场', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600006', 'instrumentName': '东风汽车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600008', 'instrumentName': '首创股份', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600009', 'instrumentName': '上海机场', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600010', 'instrumentName': '包钢股份', 'premiumRate': '10%', 'quantity': 7200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600017', 'instrumentName': '日照港', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600020', 'instrumentName': '中原高速', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600022', 'instrumentName': '山东钢铁', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600025', 'instrumentName': '华能水电', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600026', 'instrumentName': '中远海能', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600027', 'instrumentName': '华电国际', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600038', 'instrumentName': '中直股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600039', 'instrumentName': '四川路桥', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600056', 'instrumentName': '中国医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600057', 'instrumentName': '厦门象屿', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600061', 'instrumentName': '国投资本', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600062', 'instrumentName': '华润双鹤', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600064', 'instrumentName': '南京高科', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600072', 'instrumentName': '中船科技', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600073', 'instrumentName': '上海梅林', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600086', 'instrumentName': '东方金钰', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600093', 'instrumentName': '易见股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600096', 'instrumentName': '云天化', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600108', 'instrumentName': '亚盛集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600110', 'instrumentName': '诺德股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600111', 'instrumentName': '北方稀土', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600116', 'instrumentName': '三峡水利', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600118', 'instrumentName': '中国卫星', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600120', 'instrumentName': '浙江东方', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600122', 'instrumentName': '宏图高科', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600123', 'instrumentName': '兰花科创', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600125', 'instrumentName': '铁龙物流', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600138', 'instrumentName': '中青旅', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600151', 'instrumentName': '航天机电', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600153', 'instrumentName': '建发股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600155', 'instrumentName': '华创阳安', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600160', 'instrumentName': '巨化股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600161', 'instrumentName': '天坛生物', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600166', 'instrumentName': '福田汽车', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600170', 'instrumentName': '上海建工', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600171', 'instrumentName': '上海贝岭', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600172', 'instrumentName': '黄河旋风', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600175', 'instrumentName': '美都能源', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600176', 'instrumentName': '中国巨石', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600177', 'instrumentName': '雅戈尔', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600179', 'instrumentName': 'ST安通', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600183', 'instrumentName': '生益科技', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600185', 'instrumentName': '格力地产', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600187', 'instrumentName': '国中水务', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600200', 'instrumentName': '江苏吴中', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600201', 'instrumentName': '生物股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600206', 'instrumentName': '有研新材', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600208', 'instrumentName': '新湖中宝', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600210', 'instrumentName': '紫江企业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600216', 'instrumentName': '浙江医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600219', 'instrumentName': '南山铝业', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600221', 'instrumentName': '海航控股', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600226', 'instrumentName': '瀚叶股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600230', 'instrumentName': '沧州大化', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600231', 'instrumentName': '凌钢股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600233', 'instrumentName': '圆通速递', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600236', 'instrumentName': '桂冠电力', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600256', 'instrumentName': '广汇能源', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600258', 'instrumentName': '首旅酒店', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600259', 'instrumentName': '广晟有色', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600260', 'instrumentName': '凯乐科技', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600267', 'instrumentName': '海正药业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600269', 'instrumentName': '赣粤高速', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600273', 'instrumentName': '嘉化能源', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600277', 'instrumentName': '亿利洁能', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600280', 'instrumentName': '中央商场', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600282', 'instrumentName': '南钢股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600291', 'instrumentName': '西水股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600297', 'instrumentName': '广汇汽车', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600298', 'instrumentName': '安琪酵母', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600299', 'instrumentName': '安迪苏', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600307', 'instrumentName': '酒钢宏兴', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600312', 'instrumentName': '平高电气', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600315', 'instrumentName': '上海家化', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600316', 'instrumentName': '洪都航空', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600317', 'instrumentName': '营口港', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600320', 'instrumentName': '振华重工', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600323', 'instrumentName': '瀚蓝环境', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600325', 'instrumentName': '华发股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600329', 'instrumentName': '中新药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600330', 'instrumentName': '天通股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600335', 'instrumentName': '国机汽车', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600338', 'instrumentName': '西藏珠峰', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600339', 'instrumentName': '中油工程', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600343', 'instrumentName': '航天动力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600346', 'instrumentName': '恒力石化', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600348', 'instrumentName': '阳泉煤业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600350', 'instrumentName': '山东高速', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600352', 'instrumentName': '浙江龙盛', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600366', 'instrumentName': '宁波韵升', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600369', 'instrumentName': '西南证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600372', 'instrumentName': '中航电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600373', 'instrumentName': '中文传媒', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600377', 'instrumentName': '宁沪高速', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600380', 'instrumentName': '健康元', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600388', 'instrumentName': '龙净环保', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600390', 'instrumentName': '五矿资本', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600391', 'instrumentName': '航发科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600392', 'instrumentName': '盛和资源', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600409', 'instrumentName': '三友化工', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600410', 'instrumentName': '华胜天成', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600415', 'instrumentName': '小商品城', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600416', 'instrumentName': '湘电股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600418', 'instrumentName': '江淮汽车', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600422', 'instrumentName': '昆药集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600426', 'instrumentName': '华鲁恒升', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600428', 'instrumentName': '中远海特', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600435', 'instrumentName': '北方导航', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600446', 'instrumentName': '金证股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600458', 'instrumentName': '时代新材', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600460', 'instrumentName': '士兰微', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600466', 'instrumentName': '蓝光发展', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600477', 'instrumentName': '杭萧钢构', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600478', 'instrumentName': '科力远', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600482', 'instrumentName': '中国动力', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600486', 'instrumentName': '扬农化工', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600487', 'instrumentName': '亨通光电', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600490', 'instrumentName': '鹏欣资源', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600495', 'instrumentName': '晋西车轴', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600497', 'instrumentName': '驰宏锌锗', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600499', 'instrumentName': '科达洁能', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600500', 'instrumentName': '中化国际', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600502', 'instrumentName': '安徽建工', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600507', 'instrumentName': '方大特钢', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600511', 'instrumentName': '国药股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600515', 'instrumentName': '海航基础', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600517', 'instrumentName': '置信电气', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600521', 'instrumentName': '华海药业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600522', 'instrumentName': '中天科技', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600525', 'instrumentName': '长园集团', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600536', 'instrumentName': '中国软件', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600549', 'instrumentName': '厦门钨业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600557', 'instrumentName': '康缘药业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600559', 'instrumentName': '老白干酒', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600562', 'instrumentName': '国睿科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600563', 'instrumentName': '法拉电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600567', 'instrumentName': '山鹰纸业', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600568', 'instrumentName': '中珠医疗', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600569', 'instrumentName': '安阳钢铁', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600570', 'instrumentName': '恒生电子', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600571', 'instrumentName': '信雅达', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600580', 'instrumentName': '卧龙电驱', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600581', 'instrumentName': '八一钢铁', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600582', 'instrumentName': '天地科技', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600583', 'instrumentName': '海油工程', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600584', 'instrumentName': '长电科技', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600588', 'instrumentName': '用友网络', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600594', 'instrumentName': '益佰制药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600596', 'instrumentName': '新安股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600597', 'instrumentName': '光明乳业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600604', 'instrumentName': '市北高新', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600611', 'instrumentName': '大众交通', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600621', 'instrumentName': '华鑫股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600623', 'instrumentName': '华谊集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600633', 'instrumentName': '浙数文化', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600635', 'instrumentName': '大众公用', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600640', 'instrumentName': '号百控股', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600642', 'instrumentName': '申能股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600643', 'instrumentName': '爱建集团', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600648', 'instrumentName': '外高桥', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600649', 'instrumentName': '城投控股', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600655', 'instrumentName': '豫园股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600664', 'instrumentName': '哈药股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600667', 'instrumentName': '太极实业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600673', 'instrumentName': '东阳光', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600674', 'instrumentName': '川投能源', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600677', 'instrumentName': '航天通信', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600682', 'instrumentName': '南京新百', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600685', 'instrumentName': '中船防务', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600694', 'instrumentName': '大商股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600699', 'instrumentName': '均胜电子', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600702', 'instrumentName': '舍得酒业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600711', 'instrumentName': '盛屯矿业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600715', 'instrumentName': '文投控股', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600717', 'instrumentName': '天津港', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600718', 'instrumentName': '东软集团', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600728', 'instrumentName': '佳都科技', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600729', 'instrumentName': '重庆百货', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600733', 'instrumentName': '北汽蓝谷', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600737', 'instrumentName': '中粮糖业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600739', 'instrumentName': '辽宁成大', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600740', 'instrumentName': '山西焦化', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600745', 'instrumentName': '闻泰科技', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600750', 'instrumentName': '江中药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600751', 'instrumentName': '海航科技', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600754', 'instrumentName': '锦江酒店', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600755', 'instrumentName': '厦门国贸', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600756', 'instrumentName': '浪潮软件', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600757', 'instrumentName': '长江传媒', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600758', 'instrumentName': '红阳能源', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600759', 'instrumentName': '洲际油气', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600760', 'instrumentName': '中航沈飞', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600763', 'instrumentName': '通策医疗', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600765', 'instrumentName': '中航重机', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600771', 'instrumentName': '广誉远', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600776', 'instrumentName': '东方通信', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600777', 'instrumentName': '新潮能源', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600779', 'instrumentName': '水井坊', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600782', 'instrumentName': '新钢股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600783', 'instrumentName': '鲁信创投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600787', 'instrumentName': '中储股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600789', 'instrumentName': '鲁抗医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600797', 'instrumentName': '浙大网新', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600801', 'instrumentName': '华新水泥', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600803', 'instrumentName': '新奥股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600808', 'instrumentName': '马钢股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600809', 'instrumentName': '山西汾酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600811', 'instrumentName': '东方集团', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600816', 'instrumentName': '安信信托', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600820', 'instrumentName': '隧道股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600823', 'instrumentName': '世茂股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600835', 'instrumentName': '上海机电', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600839', 'instrumentName': '四川长虹', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600845', 'instrumentName': '宝信软件', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600846', 'instrumentName': '同济科技', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600848', 'instrumentName': '上海临港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600855', 'instrumentName': '航天长峰', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600856', 'instrumentName': 'ST中天', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600859', 'instrumentName': '王府井', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600862', 'instrumentName': '中航高科', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600863', 'instrumentName': '内蒙华电', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600864', 'instrumentName': '哈投股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600868', 'instrumentName': '梅雁吉祥', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600872', 'instrumentName': '中炬高新', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600874', 'instrumentName': '创业环保', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600875', 'instrumentName': '东方电气', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600879', 'instrumentName': '航天电子', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600881', 'instrumentName': '亚泰集团', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600884', 'instrumentName': '杉杉股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600885', 'instrumentName': '宏发股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600895', 'instrumentName': '张江高科', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600901', 'instrumentName': '江苏租赁', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600903', 'instrumentName': '贵州燃气', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600908', 'instrumentName': '无锡银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600909', 'instrumentName': '华安证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600917', 'instrumentName': '重庆燃气', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600926', 'instrumentName': '杭州银行', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600929', 'instrumentName': '湖南盐业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600963', 'instrumentName': '岳阳林纸', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600967', 'instrumentName': '内蒙一机', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600970', 'instrumentName': '中材国际', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600971', 'instrumentName': '恒源煤电', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600977', 'instrumentName': '中国电影', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600988', 'instrumentName': '赤峰黄金', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600993', 'instrumentName': '马应龙', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600996', 'instrumentName': '贵广网络', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601000', 'instrumentName': '唐山港', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601001', 'instrumentName': '大同煤业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601002', 'instrumentName': '晋亿实业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601003', 'instrumentName': '柳钢股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601011', 'instrumentName': '宝泰隆', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601012', 'instrumentName': '隆基股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601016', 'instrumentName': '节能风电', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601018', 'instrumentName': '宁波港', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601019', 'instrumentName': '山东出版', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601020', 'instrumentName': '华钰矿业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601021', 'instrumentName': '春秋航空', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601068', 'instrumentName': '中铝国际', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601069', 'instrumentName': '西部黄金', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601098', 'instrumentName': '中南传媒', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601099', 'instrumentName': '太平洋', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601100', 'instrumentName': '恒立液压', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601106', 'instrumentName': '中国一重', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601108', 'instrumentName': '财通证券', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601117', 'instrumentName': '中国化学', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601118', 'instrumentName': '海南橡胶', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601128', 'instrumentName': '常熟银行', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601139', 'instrumentName': '深圳燃气', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601162', 'instrumentName': '天风证券', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601168', 'instrumentName': '西部矿业', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 5800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601198', 'instrumentName': '东兴证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601200', 'instrumentName': '上海环境', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601212', 'instrumentName': '白银有色', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601216', 'instrumentName': '君正集团', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601222', 'instrumentName': '林洋能源', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601228', 'instrumentName': '广州港', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601231', 'instrumentName': '环旭电子', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601233', 'instrumentName': '桐昆股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601238', 'instrumentName': '广汽集团', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601258', 'instrumentName': '庞大集团', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601298', 'instrumentName': '青岛港', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601311', 'instrumentName': '骆驼股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601326', 'instrumentName': '秦港股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601330', 'instrumentName': '绿色动力', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601333', 'instrumentName': '广深铁路', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601360', 'instrumentName': '三六零', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601375', 'instrumentName': '中原证券', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601377', 'instrumentName': '兴业证券', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601519', 'instrumentName': '大智慧', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601555', 'instrumentName': '东吴证券', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601577', 'instrumentName': '长沙银行', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601588', 'instrumentName': '北辰实业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601606', 'instrumentName': '长城军工', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601608', 'instrumentName': '中信重工', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601611', 'instrumentName': '中国核建', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601615', 'instrumentName': '明阳智能', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601619', 'instrumentName': '嘉泽新能', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601633', 'instrumentName': '长城汽车', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601636', 'instrumentName': '旗滨集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601666', 'instrumentName': '平煤股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601689', 'instrumentName': '拓普集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601717', 'instrumentName': '郑煤机', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601777', 'instrumentName': '力帆股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601788', 'instrumentName': '光大证券', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601808', 'instrumentName': '中海油服', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601828', 'instrumentName': '美凯龙', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601858', 'instrumentName': '中国科传', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601860', 'instrumentName': '紫金银行', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601866', 'instrumentName': '中远海发', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601869', 'instrumentName': '长飞光纤', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601877', 'instrumentName': '正泰电器', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601878', 'instrumentName': '浙商证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601880', 'instrumentName': '大连港', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601886', 'instrumentName': '江河集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601890', 'instrumentName': '亚星锚链', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601898', 'instrumentName': '中煤能源', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601901', 'instrumentName': '方正证券', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601908', 'instrumentName': '京运通', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601919', 'instrumentName': '中远海控', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601949', 'instrumentName': '中国出版', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601952', 'instrumentName': '苏垦农发', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601958', 'instrumentName': '金钼股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601966', 'instrumentName': '玲珑轮胎', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601990', 'instrumentName': '南京证券', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601991', 'instrumentName': '大唐发电', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601992', 'instrumentName': '金隅集团', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601997', 'instrumentName': '贵阳银行', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603000', 'instrumentName': '人民网', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': '         1410.000', 'instrumentId': '603013', 'instrumentName': '亚普股份', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603019', 'instrumentName': '中科曙光', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603025', 'instrumentName': '大豪科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603056', 'instrumentName': '德邦股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603077', 'instrumentName': '和邦生物', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603103', 'instrumentName': '横店影视', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603105', 'instrumentName': '芯能科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603113', 'instrumentName': '金能科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603156', 'instrumentName': '养元饮品', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603160', 'instrumentName': '汇顶科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603198', 'instrumentName': '迎驾贡酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603220', 'instrumentName': '中贝通信', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603225', 'instrumentName': '新凤鸣', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603228', 'instrumentName': '景旺电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603260', 'instrumentName': '合盛硅业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603323', 'instrumentName': '苏农银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603328', 'instrumentName': '依顿电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603369', 'instrumentName': '今世缘', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603377', 'instrumentName': '东方时尚', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': '        26688.000', 'instrumentId': '603444', 'instrumentName': '吉比特', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': '         2490.000', 'instrumentId': '603486', 'instrumentName': '科沃斯', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603501', 'instrumentName': '韦尔股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603515', 'instrumentName': '欧普照明', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603517', 'instrumentName': '绝味食品', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603533', 'instrumentName': '掌阅科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603568', 'instrumentName': '伟明环保', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603587', 'instrumentName': '地素时尚', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603589', 'instrumentName': '口子窖', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': '         1460.000', 'instrumentId': '603596', 'instrumentName': '伯特利', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603603', 'instrumentName': '博天环境', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603612', 'instrumentName': '索通发展', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603619', 'instrumentName': '中曼石油', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603650', 'instrumentName': '彤程新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603658', 'instrumentName': '安图生物', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603659', 'instrumentName': '璞泰来', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603693', 'instrumentName': '江苏新能', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603708', 'instrumentName': '家家悦', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603712', 'instrumentName': '七一二', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603733', 'instrumentName': '仙鹤股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603766', 'instrumentName': '隆鑫通用', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603799', 'instrumentName': '华友钴业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603816', 'instrumentName': '顾家家居', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603833', 'instrumentName': '欧派家居', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603858', 'instrumentName': '步长制药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': '         3649.000', 'instrumentId': '603868', 'instrumentName': '飞科电器', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603876', 'instrumentName': '鼎胜新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': '         1418.000', 'instrumentId': '603877', 'instrumentName': '太平鸟', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603881', 'instrumentName': '数据港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603883', 'instrumentName': '老百姓', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603885', 'instrumentName': '吉祥航空', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603888', 'instrumentName': '新华网', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603899', 'instrumentName': '晨光文具', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603939', 'instrumentName': '益丰药房', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603980', 'instrumentName': '吉华集团', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603986', 'instrumentName': '兆易创新', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 9600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 20300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 6400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 8400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600061', 'instrumentName': '国投资本', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600109', 'instrumentName': '国金证券', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600291', 'instrumentName': '西水股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600643', 'instrumentName': '爱建集团', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600816', 'instrumentName': '安信信托', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 6600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600901', 'instrumentName': '江苏租赁', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600909', 'instrumentName': '华安证券', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 5700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600926', 'instrumentName': '杭州银行', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 4900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601099', 'instrumentName': '太平洋', 'premiumRate': '10%', 'quantity': 5600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601108', 'instrumentName': '财通证券', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601162', 'instrumentName': '天风证券', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 11900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 12100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601198', 'instrumentName': '东兴证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 5800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 31300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 5300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 22500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601377', 'instrumentName': '兴业证券', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 17600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601555', 'instrumentName': '东吴证券', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601577', 'instrumentName': '长沙银行', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601788', 'instrumentName': '光大证券', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 13000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601860', 'instrumentName': '紫金银行', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601878', 'instrumentName': '浙商证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601901', 'instrumentName': '方正证券', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 5500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 17200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601990', 'instrumentName': '南京证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601997', 'instrumentName': '贵阳银行', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600009', 'instrumentName': '上海机场', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600010', 'instrumentName': '包钢股份', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600111', 'instrumentName': '北方稀土', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600115', 'instrumentName': '东方航空', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600160', 'instrumentName': '巨化股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600176', 'instrumentName': '中国巨石', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600271', 'instrumentName': '航天信息', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600392', 'instrumentName': '盛和资源', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600705', 'instrumentName': '中航资本', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600739', 'instrumentName': '辽宁成大', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600760', 'instrumentName': '中航沈飞', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600809', 'instrumentName': '山西汾酒', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600886', 'instrumentName': '国投电力', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600893', 'instrumentName': '航发动力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600909', 'instrumentName': '华安证券', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600926', 'instrumentName': '杭州银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601108', 'instrumentName': '财通证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601162', 'instrumentName': '天风证券', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 8900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601298', 'instrumentName': '青岛港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 5600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601377', 'instrumentName': '兴业证券', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 5900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601577', 'instrumentName': '长沙银行', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601618', 'instrumentName': '中国中冶', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601669', 'instrumentName': '中国电建', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601788', 'instrumentName': '光大证券', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601838', 'instrumentName': '成都银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601860', 'instrumentName': '紫金银行', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601878', 'instrumentName': '浙商证券', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601881', 'instrumentName': '中国银河', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601985', 'instrumentName': '中国核电', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': '          362.000', 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601990', 'instrumentName': '南京证券', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601992', 'instrumentName': '金隅集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601997', 'instrumentName': '贵阳银行', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600007', 'instrumentName': '中国国贸', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600012', 'instrumentName': '皖通高速', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600020', 'instrumentName': '中原高速', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600021', 'instrumentName': '上海电力', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600026', 'instrumentName': '中远海能', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600027', 'instrumentName': '华电国际', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600039', 'instrumentName': '四川路桥', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600053', 'instrumentName': '九鼎投资', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600056', 'instrumentName': '中国医药', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600062', 'instrumentName': '华润双鹤', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600072', 'instrumentName': '中船科技', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600073', 'instrumentName': '上海梅林', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600075', 'instrumentName': '新疆天业', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600093', 'instrumentName': '易见股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600094', 'instrumentName': '大名城', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600105', 'instrumentName': '永鼎股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600114', 'instrumentName': '东睦股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600116', 'instrumentName': '三峡水利', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600120', 'instrumentName': '浙江东方', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600122', 'instrumentName': '宏图高科', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600123', 'instrumentName': '兰花科创', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600125', 'instrumentName': '铁龙物流', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600132', 'instrumentName': '重庆啤酒', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600136', 'instrumentName': '当代明诚', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600138', 'instrumentName': '中青旅', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600141', 'instrumentName': '兴发集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600153', 'instrumentName': '建发股份', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600160', 'instrumentName': '巨化股份', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600161', 'instrumentName': '天坛生物', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600162', 'instrumentName': '香江控股', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600167', 'instrumentName': '联美控股', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600170', 'instrumentName': '上海建工', 'premiumRate': '10%', 'quantity': 5800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600171', 'instrumentName': '上海贝岭', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600172', 'instrumentName': '黄河旋风', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600179', 'instrumentName': 'ST安通', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600180', 'instrumentName': '瑞茂通', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600183', 'instrumentName': '生益科技', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600185', 'instrumentName': '格力地产', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600197', 'instrumentName': '伊力特', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600201', 'instrumentName': '生物股份', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600206', 'instrumentName': '有研新材', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600210', 'instrumentName': '紫江企业', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600216', 'instrumentName': '浙江医药', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600219', 'instrumentName': '南山铝业', 'premiumRate': '10%', 'quantity': 9400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600230', 'instrumentName': '沧州大化', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600231', 'instrumentName': '凌钢股份', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600233', 'instrumentName': '圆通速递', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600236', 'instrumentName': '桂冠电力', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600239', 'instrumentName': '云南城投', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600252', 'instrumentName': '中恒集团', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600256', 'instrumentName': '广汇能源', 'premiumRate': '10%', 'quantity': 5400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600258', 'instrumentName': '首旅酒店', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600260', 'instrumentName': '凯乐科技', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600261', 'instrumentName': '阳光照明', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600273', 'instrumentName': '嘉化能源', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600277', 'instrumentName': '亿利洁能', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600282', 'instrumentName': '南钢股份', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600284', 'instrumentName': '浦东建设', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600285', 'instrumentName': '羚锐制药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600295', 'instrumentName': '鄂尔多斯', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600299', 'instrumentName': '安迪苏', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600305', 'instrumentName': '恒顺醋业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600310', 'instrumentName': '桂东电力', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600312', 'instrumentName': '平高电气', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600315', 'instrumentName': '上海家化', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600323', 'instrumentName': '瀚蓝环境', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600325', 'instrumentName': '华发股份', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600326', 'instrumentName': '西藏天路', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600329', 'instrumentName': '中新药业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600335', 'instrumentName': '国机汽车', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600337', 'instrumentName': '美克家居', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600338', 'instrumentName': '西藏珠峰', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600343', 'instrumentName': '航天动力', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600345', 'instrumentName': '长江通信', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600346', 'instrumentName': '恒力石化', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600348', 'instrumentName': '阳泉煤业', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600350', 'instrumentName': '山东高速', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600363', 'instrumentName': '联创光电', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600366', 'instrumentName': '宁波韵升', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600369', 'instrumentName': '西南证券', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600372', 'instrumentName': '中航电子', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600373', 'instrumentName': '中文传媒', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600376', 'instrumentName': '首开股份', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600377', 'instrumentName': '宁沪高速', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600380', 'instrumentName': '健康元', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600387', 'instrumentName': '海越能源', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600388', 'instrumentName': '龙净环保', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600392', 'instrumentName': '盛和资源', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600393', 'instrumentName': '粤泰股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600395', 'instrumentName': '盘江股份', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600409', 'instrumentName': '三友化工', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600410', 'instrumentName': '华胜天成', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600415', 'instrumentName': '小商品城', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600418', 'instrumentName': '江淮汽车', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600420', 'instrumentName': '现代制药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600422', 'instrumentName': '昆药集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600439', 'instrumentName': '瑞贝卡', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600446', 'instrumentName': '金证股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600452', 'instrumentName': '涪陵电力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600459', 'instrumentName': '贵研铂业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600460', 'instrumentName': '士兰微', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600477', 'instrumentName': '杭萧钢构', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600483', 'instrumentName': '福能股份', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600486', 'instrumentName': '扬农化工', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600490', 'instrumentName': '鹏欣资源', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600491', 'instrumentName': '龙元建设', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600495', 'instrumentName': '晋西车轴', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600498', 'instrumentName': '烽火通信', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600499', 'instrumentName': '科达洁能', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600500', 'instrumentName': '中化国际', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600502', 'instrumentName': '安徽建工', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600507', 'instrumentName': '方大特钢', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600511', 'instrumentName': '国药股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600521', 'instrumentName': '华海药业', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600525', 'instrumentName': '长园集团', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600531', 'instrumentName': '豫光金铅', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600536', 'instrumentName': '中国软件', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600545', 'instrumentName': '卓郎智能', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600548', 'instrumentName': '深高速', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600549', 'instrumentName': '厦门钨业', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600557', 'instrumentName': '康缘药业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600559', 'instrumentName': '老白干酒', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600563', 'instrumentName': '法拉电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600565', 'instrumentName': '迪马股份', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600567', 'instrumentName': '山鹰纸业', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600577', 'instrumentName': '精达股份', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600580', 'instrumentName': '卧龙电驱', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600582', 'instrumentName': '天地科技', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600583', 'instrumentName': '海油工程', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600584', 'instrumentName': '长电科技', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600587', 'instrumentName': '新华医疗', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600596', 'instrumentName': '新安股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600597', 'instrumentName': '光明乳业', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600611', 'instrumentName': '大众交通', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600612', 'instrumentName': '老凤祥', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600622', 'instrumentName': '光大嘉宝', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600623', 'instrumentName': '华谊集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600626', 'instrumentName': '申达股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600639', 'instrumentName': '浦东金桥', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600641', 'instrumentName': '万业企业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600642', 'instrumentName': '申能股份', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600648', 'instrumentName': '外高桥', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600655', 'instrumentName': '豫园股份', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600657', 'instrumentName': '信达地产', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600661', 'instrumentName': '昂立教育', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600663', 'instrumentName': '陆家嘴', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600664', 'instrumentName': '哈药股份', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600667', 'instrumentName': '太极实业', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600668', 'instrumentName': '尖峰集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600673', 'instrumentName': '东阳光', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600674', 'instrumentName': '川投能源', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600675', 'instrumentName': '中华企业', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600681', 'instrumentName': '百川能源', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600682', 'instrumentName': '南京新百', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600688', 'instrumentName': '上海石化', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600693', 'instrumentName': '东百集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600694', 'instrumentName': '大商股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600702', 'instrumentName': '舍得酒业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600704', 'instrumentName': '物产中大', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600708', 'instrumentName': '光明地产', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600711', 'instrumentName': '盛屯矿业', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600717', 'instrumentName': '天津港', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600720', 'instrumentName': '祁连山', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600728', 'instrumentName': '佳都科技', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600729', 'instrumentName': '重庆百货', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600738', 'instrumentName': '兰州民百', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600739', 'instrumentName': '辽宁成大', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600742', 'instrumentName': '一汽富维', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600748', 'instrumentName': '上实发展', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600750', 'instrumentName': '江中药业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600754', 'instrumentName': '锦江酒店', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600755', 'instrumentName': '厦门国贸', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600756', 'instrumentName': '浪潮软件', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600757', 'instrumentName': '长江传媒', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600763', 'instrumentName': '通策医疗', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600765', 'instrumentName': '中航重机', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600777', 'instrumentName': '新潮能源', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600779', 'instrumentName': '水井坊', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600781', 'instrumentName': 'ST辅仁', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600782', 'instrumentName': '新钢股份', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600787', 'instrumentName': '中储股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600801', 'instrumentName': '华新水泥', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600803', 'instrumentName': '新奥股份', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600808', 'instrumentName': '马钢股份', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600811', 'instrumentName': '东方集团', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600814', 'instrumentName': '杭州解百', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600820', 'instrumentName': '隧道股份', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600823', 'instrumentName': '世茂股份', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600826', 'instrumentName': '兰生股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600828', 'instrumentName': '茂业商业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600835', 'instrumentName': '上海机电', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600845', 'instrumentName': '宝信软件', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600846', 'instrumentName': '同济科技', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600848', 'instrumentName': '上海临港', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600850', 'instrumentName': '华东电脑', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600859', 'instrumentName': '王府井', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600863', 'instrumentName': '内蒙华电', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600874', 'instrumentName': '创业环保', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600879', 'instrumentName': '航天电子', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600885', 'instrumentName': '宏发股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600897', 'instrumentName': '厦门空港', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600917', 'instrumentName': '重庆燃气', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600933', 'instrumentName': '爱柯迪', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600965', 'instrumentName': '福成股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600967', 'instrumentName': '内蒙一机', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600970', 'instrumentName': '中材国际', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600971', 'instrumentName': '恒源煤电', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600973', 'instrumentName': '宝胜股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600975', 'instrumentName': '新五丰', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600978', 'instrumentName': '宜华生活', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600985', 'instrumentName': '淮北矿业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600986', 'instrumentName': '科达股份', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600987', 'instrumentName': '航民股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600990', 'instrumentName': '四创电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600993', 'instrumentName': '马应龙', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600996', 'instrumentName': '贵广网络', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600998', 'instrumentName': '九州通', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601000', 'instrumentName': '唐山港', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601001', 'instrumentName': '大同煤业', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601002', 'instrumentName': '晋亿实业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601003', 'instrumentName': '柳钢股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601015', 'instrumentName': '陕西黑猫', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601016', 'instrumentName': '节能风电', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601018', 'instrumentName': '宁波港', 'premiumRate': '10%', 'quantity': 5200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601019', 'instrumentName': '山东出版', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601020', 'instrumentName': '华钰矿业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601021', 'instrumentName': '春秋航空', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601058', 'instrumentName': '赛轮轮胎', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601069', 'instrumentName': '西部黄金', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601098', 'instrumentName': '中南传媒', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601100', 'instrumentName': '恒立液压', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601127', 'instrumentName': '小康股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601128', 'instrumentName': '常熟银行', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601139', 'instrumentName': '深圳燃气', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601158', 'instrumentName': '重庆水务', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601168', 'instrumentName': '西部矿业', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601200', 'instrumentName': '上海环境', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601216', 'instrumentName': '君正集团', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601222', 'instrumentName': '林洋能源', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601231', 'instrumentName': '环旭电子', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601311', 'instrumentName': '骆驼股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601330', 'instrumentName': '绿色动力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601333', 'instrumentName': '广深铁路', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601567', 'instrumentName': '三星医疗', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601588', 'instrumentName': '北辰实业', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601619', 'instrumentName': '嘉泽新能', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601636', 'instrumentName': '旗滨集团', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601666', 'instrumentName': '平煤股份', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601678', 'instrumentName': '滨化股份', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601689', 'instrumentName': '拓普集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601717', 'instrumentName': '郑煤机', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601789', 'instrumentName': '宁波建工', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601799', 'instrumentName': '星宇股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601801', 'instrumentName': '皖新传媒', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601811', 'instrumentName': '新华文轩', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601858', 'instrumentName': '中国科传', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601869', 'instrumentName': '长飞光纤', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601872', 'instrumentName': '招商轮船', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601908', 'instrumentName': '京运通', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601958', 'instrumentName': '金钼股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601966', 'instrumentName': '玲珑轮胎', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603007', 'instrumentName': '花王股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603008', 'instrumentName': '喜临门', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603025', 'instrumentName': '大豪科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603027', 'instrumentName': '千禾味业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603039', 'instrumentName': '泛微网络', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603043', 'instrumentName': '广州酒家', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603056', 'instrumentName': '德邦股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603060', 'instrumentName': '国检集团', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603077', 'instrumentName': '和邦生物', 'premiumRate': '10%', 'quantity': 7000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603080', 'instrumentName': '新疆火炬', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603098', 'instrumentName': '森特股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603103', 'instrumentName': '横店影视', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603108', 'instrumentName': '润达医疗', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603113', 'instrumentName': '金能科技', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603118', 'instrumentName': '共进股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603121', 'instrumentName': '华培动力', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603126', 'instrumentName': '中材节能', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603127', 'instrumentName': '昭衍新药', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603128', 'instrumentName': '华贸物流', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603165', 'instrumentName': '荣晟环保', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': '         5865.000', 'instrumentId': '603180', 'instrumentName': '金牌厨柜', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603185', 'instrumentName': '上机数控', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603198', 'instrumentName': '迎驾贡酒', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603220', 'instrumentName': '中贝通信', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603225', 'instrumentName': '新凤鸣', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603228', 'instrumentName': '景旺电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603233', 'instrumentName': '大参林', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603283', 'instrumentName': '赛腾股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603298', 'instrumentName': '杭叉集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603305', 'instrumentName': '旭升股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603328', 'instrumentName': '依顿电子', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603332', 'instrumentName': '苏州龙杰', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603337', 'instrumentName': '杰克股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603338', 'instrumentName': '浙江鼎力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603339', 'instrumentName': '四方科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': '         3497.000', 'instrumentId': '603351', 'instrumentName': '威尔药业', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603355', 'instrumentName': '莱克电气', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603357', 'instrumentName': '设计总院', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603358', 'instrumentName': '华达科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603363', 'instrumentName': '傲农生物', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603367', 'instrumentName': '辰欣药业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603368', 'instrumentName': '柳药股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603387', 'instrumentName': '基蛋生物', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603393', 'instrumentName': '新天然气', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603421', 'instrumentName': '鼎信通讯', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603444', 'instrumentName': '吉比特', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603466', 'instrumentName': '风语筑', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603501', 'instrumentName': '韦尔股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603505', 'instrumentName': '金石资源', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603515', 'instrumentName': '欧普照明', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603517', 'instrumentName': '绝味食品', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603533', 'instrumentName': '掌阅科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603556', 'instrumentName': '海兴电力', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603568', 'instrumentName': '伟明环保', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603569', 'instrumentName': '长久物流', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603579', 'instrumentName': '荣泰健康', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603583', 'instrumentName': '捷昌驱动', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603585', 'instrumentName': '苏利股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603587', 'instrumentName': '地素时尚', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603588', 'instrumentName': '高能环境', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603590', 'instrumentName': '康辰药业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603595', 'instrumentName': '东尼电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603596', 'instrumentName': '伯特利', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603599', 'instrumentName': '广信股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603600', 'instrumentName': '永艺股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603603', 'instrumentName': '博天环境', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603609', 'instrumentName': '禾丰牧业', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603612', 'instrumentName': '索通发展', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603619', 'instrumentName': '中曼石油', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603626', 'instrumentName': '科森科技', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603638', 'instrumentName': '艾迪精密', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603639', 'instrumentName': '海利尔', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603650', 'instrumentName': '彤程新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603658', 'instrumentName': '安图生物', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603659', 'instrumentName': '璞泰来', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603660', 'instrumentName': '苏州科达', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': '         6488.000', 'instrumentId': '603666', 'instrumentName': '亿嘉和', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603669', 'instrumentName': '灵康药业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603680', 'instrumentName': '今创集团', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603686', 'instrumentName': '龙马环卫', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603707', 'instrumentName': '健友股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603708', 'instrumentName': '家家悦', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603711', 'instrumentName': '香飘飘', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603712', 'instrumentName': '七一二', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603713', 'instrumentName': '密尔克卫', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603728', 'instrumentName': '鸣志电器', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603730', 'instrumentName': '岱美股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603733', 'instrumentName': '仙鹤股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603737', 'instrumentName': '三棵树', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603757', 'instrumentName': '大元泵业', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603766', 'instrumentName': '隆鑫通用', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': '         3123.000', 'instrumentId': '603773', 'instrumentName': '沃格光电', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603801', 'instrumentName': '志邦家居', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603803', 'instrumentName': '瑞斯康达', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603806', 'instrumentName': '福斯特', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603808', 'instrumentName': '歌力思', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603816', 'instrumentName': '顾家家居', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603833', 'instrumentName': '欧派家居', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603848', 'instrumentName': '好太太', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603866', 'instrumentName': '桃李面包', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603868', 'instrumentName': '飞科电器', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603871', 'instrumentName': '嘉友国际', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603876', 'instrumentName': '鼎胜新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603877', 'instrumentName': '太平鸟', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603881', 'instrumentName': '数据港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603882', 'instrumentName': '金域医学', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603883', 'instrumentName': '老百姓', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603885', 'instrumentName': '吉祥航空', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603888', 'instrumentName': '新华网', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': '         2170.000', 'instrumentId': '603895', 'instrumentName': '天永智能', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603897', 'instrumentName': '长城科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603898', 'instrumentName': '好莱客', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603899', 'instrumentName': '晨光文具', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603929', 'instrumentName': '亚翔集成', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603939', 'instrumentName': '益丰药房', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603959', 'instrumentName': '百利科技', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603989', 'instrumentName': '艾华集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603997', 'instrumentName': '继峰股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 5800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600111', 'instrumentName': '北方稀土', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600123', 'instrumentName': '兰花科创', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600172', 'instrumentName': '黄河旋风', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600188', 'instrumentName': '兖州煤业', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600206', 'instrumentName': '有研新材', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600219', 'instrumentName': '南山铝业', 'premiumRate': '10%', 'quantity': 7700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600256', 'instrumentName': '广汇能源', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600259', 'instrumentName': '广晟有色', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600338', 'instrumentName': '西藏珠峰', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600339', 'instrumentName': '中油工程', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600348', 'instrumentName': '阳泉煤业', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600362', 'instrumentName': '江西铜业', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600392', 'instrumentName': '盛和资源', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600489', 'instrumentName': '中金黄金', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600490', 'instrumentName': '鹏欣资源', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600497', 'instrumentName': '驰宏锌锗', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600516', 'instrumentName': '方大炭素', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600547', 'instrumentName': '山东黄金', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600549', 'instrumentName': '厦门钨业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600583', 'instrumentName': '海油工程', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600673', 'instrumentName': '东阳光', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600740', 'instrumentName': '山西焦化', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600759', 'instrumentName': '洲际油气', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600777', 'instrumentName': '新潮能源', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600856', 'instrumentName': 'ST中天', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600971', 'instrumentName': '恒源煤电', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600988', 'instrumentName': '赤峰黄金', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601001', 'instrumentName': '大同煤业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601011', 'instrumentName': '宝泰隆', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601069', 'instrumentName': '西部黄金', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601168', 'instrumentName': '西部矿业', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601212', 'instrumentName': '白银有色', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601225', 'instrumentName': '陕西煤业', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601600', 'instrumentName': '中国铝业', 'premiumRate': '10%', 'quantity': 7000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601666', 'instrumentName': '平煤股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601808', 'instrumentName': '中海油服', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601898', 'instrumentName': '中煤能源', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601899', 'instrumentName': '紫金矿业', 'premiumRate': '10%', 'quantity': 9300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601958', 'instrumentName': '金钼股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603113', 'instrumentName': '金能科技', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603260', 'instrumentName': '合盛硅业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603619', 'instrumentName': '中曼石油', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603799', 'instrumentName': '华友钴业', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603876', 'instrumentName': '鼎胜新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 7600, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 5900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 5300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': '            0.000', 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': None, 'quantity': 0, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 4100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 7300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 9400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': '            0.000', 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': None, 'quantity': 0, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 7400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 5600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 7100, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600006', 'instrumentName': '东风汽车', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600008', 'instrumentName': '首创股份', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600017', 'instrumentName': '日照港', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600021', 'instrumentName': '上海电力', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600022', 'instrumentName': '山东钢铁', 'premiumRate': '10%', 'quantity': 5500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600026', 'instrumentName': '中远海能', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600037', 'instrumentName': '歌华有线', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600039', 'instrumentName': '四川路桥', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600053', 'instrumentName': '九鼎投资', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600056', 'instrumentName': '中国医药', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600058', 'instrumentName': '五矿发展', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600060', 'instrumentName': '海信电器', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600062', 'instrumentName': '华润双鹤', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600064', 'instrumentName': '南京高科', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600073', 'instrumentName': '上海梅林', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600079', 'instrumentName': '人福医药', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600094', 'instrumentName': '大名城', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600098', 'instrumentName': '广州发展', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600120', 'instrumentName': '浙江东方', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600125', 'instrumentName': '铁龙物流', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600126', 'instrumentName': '杭钢股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600138', 'instrumentName': '中青旅', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600141', 'instrumentName': '兴发集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600143', 'instrumentName': '金发科技', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600151', 'instrumentName': '航天机电', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600155', 'instrumentName': '华创阳安', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600158', 'instrumentName': '中体产业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600160', 'instrumentName': '巨化股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600161', 'instrumentName': '天坛生物', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600166', 'instrumentName': '福田汽车', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600167', 'instrumentName': '联美控股', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600169', 'instrumentName': '太原重工', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600171', 'instrumentName': '上海贝岭', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600179', 'instrumentName': 'ST安通', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600183', 'instrumentName': '生益科技', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600195', 'instrumentName': '中牧股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600201', 'instrumentName': '生物股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600216', 'instrumentName': '浙江医药', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600258', 'instrumentName': '首旅酒店', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600259', 'instrumentName': '广晟有色', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600260', 'instrumentName': '凯乐科技', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600266', 'instrumentName': '北京城建', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600267', 'instrumentName': '海正药业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600277', 'instrumentName': '亿利洁能', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600280', 'instrumentName': '中央商场', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600282', 'instrumentName': '南钢股份', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600291', 'instrumentName': '西水股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600298', 'instrumentName': '安琪酵母', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600307', 'instrumentName': '酒钢宏兴', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600312', 'instrumentName': '平高电气', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600315', 'instrumentName': '上海家化', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600316', 'instrumentName': '洪都航空', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600317', 'instrumentName': '营口港', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600325', 'instrumentName': '华发股份', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600329', 'instrumentName': '中新药业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600335', 'instrumentName': '国机汽车', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600338', 'instrumentName': '西藏珠峰', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600348', 'instrumentName': '阳泉煤业', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600350', 'instrumentName': '山东高速', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600373', 'instrumentName': '中文传媒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600376', 'instrumentName': '首开股份', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600380', 'instrumentName': '健康元', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600388', 'instrumentName': '龙净环保', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600392', 'instrumentName': '盛和资源', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600393', 'instrumentName': '粤泰股份', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600409', 'instrumentName': '三友化工', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600410', 'instrumentName': '华胜天成', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600416', 'instrumentName': '湘电股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600418', 'instrumentName': '江淮汽车', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600426', 'instrumentName': '华鲁恒升', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600428', 'instrumentName': '中远海特', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600435', 'instrumentName': '北方导航', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600458', 'instrumentName': '时代新材', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600460', 'instrumentName': '士兰微', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600466', 'instrumentName': '蓝光发展', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600478', 'instrumentName': '科力远', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600486', 'instrumentName': '扬农化工', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600497', 'instrumentName': '驰宏锌锗', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600499', 'instrumentName': '科达洁能', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600500', 'instrumentName': '中化国际', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600507', 'instrumentName': '方大特钢', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600511', 'instrumentName': '国药股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600515', 'instrumentName': '海航基础', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600521', 'instrumentName': '华海药业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600525', 'instrumentName': '长园集团', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600528', 'instrumentName': '中铁工业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600536', 'instrumentName': '中国软件', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600545', 'instrumentName': '卓郎智能', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600549', 'instrumentName': '厦门钨业', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600557', 'instrumentName': '康缘药业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600563', 'instrumentName': '法拉电子', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600565', 'instrumentName': '迪马股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600567', 'instrumentName': '山鹰纸业', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600575', 'instrumentName': '皖江物流', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600580', 'instrumentName': '卧龙电驱', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600582', 'instrumentName': '天地科技', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600584', 'instrumentName': '长电科技', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600597', 'instrumentName': '光明乳业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600611', 'instrumentName': '大众交通', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600623', 'instrumentName': '华谊集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600633', 'instrumentName': '浙数文化', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600639', 'instrumentName': '浦东金桥', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600640', 'instrumentName': '号百控股', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600642', 'instrumentName': '申能股份', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600643', 'instrumentName': '爱建集团', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600645', 'instrumentName': '中源协和', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600648', 'instrumentName': '外高桥', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600649', 'instrumentName': '城投控股', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600657', 'instrumentName': '信达地产', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600664', 'instrumentName': '哈药股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600673', 'instrumentName': '东阳光', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600694', 'instrumentName': '大商股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600699', 'instrumentName': '均胜电子', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600707', 'instrumentName': '彩虹股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600717', 'instrumentName': '天津港', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600718', 'instrumentName': '东软集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600729', 'instrumentName': '重庆百货', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600737', 'instrumentName': '中粮糖业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600739', 'instrumentName': '辽宁成大', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600745', 'instrumentName': '闻泰科技', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600748', 'instrumentName': '上实发展', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600750', 'instrumentName': '江中药业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600751', 'instrumentName': '海航科技', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600754', 'instrumentName': '锦江酒店', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600755', 'instrumentName': '厦门国贸', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600757', 'instrumentName': '长江传媒', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600759', 'instrumentName': '洲际油气', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600763', 'instrumentName': '通策医疗', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600765', 'instrumentName': '中航重机', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600770', 'instrumentName': '综艺股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600777', 'instrumentName': '新潮能源', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600779', 'instrumentName': '水井坊', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600782', 'instrumentName': '新钢股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600787', 'instrumentName': '中储股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600801', 'instrumentName': '华新水泥', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600804', 'instrumentName': '鹏博士', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600808', 'instrumentName': '马钢股份', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600811', 'instrumentName': '东方集团', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600820', 'instrumentName': '隧道股份', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600823', 'instrumentName': '世茂股份', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600827', 'instrumentName': '百联股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600835', 'instrumentName': '上海机电', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600839', 'instrumentName': '四川长虹', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600845', 'instrumentName': '宝信软件', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600848', 'instrumentName': '上海临港', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600859', 'instrumentName': '王府井', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600862', 'instrumentName': '中航高科', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600863', 'instrumentName': '内蒙华电', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600869', 'instrumentName': '智慧能源', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600872', 'instrumentName': '中炬高新', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600874', 'instrumentName': '创业环保', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600875', 'instrumentName': '东方电气', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600879', 'instrumentName': '航天电子', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600881', 'instrumentName': '亚泰集团', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600884', 'instrumentName': '杉杉股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600885', 'instrumentName': '宏发股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600895', 'instrumentName': '张江高科', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600901', 'instrumentName': '江苏租赁', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600903', 'instrumentName': '贵州燃气', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600908', 'instrumentName': '无锡银行', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600909', 'instrumentName': '华安证券', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600917', 'instrumentName': '重庆燃气', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600939', 'instrumentName': '重庆建工', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600959', 'instrumentName': '江苏有线', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600967', 'instrumentName': '内蒙一机', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600970', 'instrumentName': '中材国际', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600971', 'instrumentName': '恒源煤电', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600978', 'instrumentName': '宜华生活', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600985', 'instrumentName': '淮北矿业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600993', 'instrumentName': '马应龙', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600996', 'instrumentName': '贵广网络', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601000', 'instrumentName': '唐山港', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601001', 'instrumentName': '大同煤业', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601003', 'instrumentName': '柳钢股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601005', 'instrumentName': '重庆钢铁', 'premiumRate': '10%', 'quantity': 4900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601016', 'instrumentName': '节能风电', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601019', 'instrumentName': '山东出版', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601068', 'instrumentName': '中铝国际', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601098', 'instrumentName': '中南传媒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601100', 'instrumentName': '恒立液压', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601106', 'instrumentName': '中国一重', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601118', 'instrumentName': '海南橡胶', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601127', 'instrumentName': '小康股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601128', 'instrumentName': '常熟银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601139', 'instrumentName': '深圳燃气', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601168', 'instrumentName': '西部矿业', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601179', 'instrumentName': '中国西电', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601200', 'instrumentName': '上海环境', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601231', 'instrumentName': '环旭电子', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601311', 'instrumentName': '骆驼股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601326', 'instrumentName': '秦港股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601333', 'instrumentName': '广深铁路', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601608', 'instrumentName': '中信重工', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601611', 'instrumentName': '中国核建', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601615', 'instrumentName': '明阳智能', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601678', 'instrumentName': '滨化股份', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601689', 'instrumentName': '拓普集团', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601699', 'instrumentName': '潞安环能', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601717', 'instrumentName': '郑煤机', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601718', 'instrumentName': '际华集团', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601777', 'instrumentName': '力帆股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601801', 'instrumentName': '皖新传媒', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601811', 'instrumentName': '新华文轩', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601866', 'instrumentName': '中远海发', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601869', 'instrumentName': '长飞光纤', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601872', 'instrumentName': '招商轮船', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601880', 'instrumentName': '大连港', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601928', 'instrumentName': '凤凰传媒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601958', 'instrumentName': '金钼股份', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601966', 'instrumentName': '玲珑轮胎', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601969', 'instrumentName': '海南矿业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603000', 'instrumentName': '人民网', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603025', 'instrumentName': '大豪科技', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603056', 'instrumentName': '德邦股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603077', 'instrumentName': '和邦生物', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603198', 'instrumentName': '迎驾贡酒', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603225', 'instrumentName': '新凤鸣', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603228', 'instrumentName': '景旺电子', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603233', 'instrumentName': '大参林', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603328', 'instrumentName': '依顿电子', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603355', 'instrumentName': '莱克电气', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603369', 'instrumentName': '今世缘', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603377', 'instrumentName': '东方时尚', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603444', 'instrumentName': '吉比特', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603486', 'instrumentName': '科沃斯', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603501', 'instrumentName': '韦尔股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603515', 'instrumentName': '欧普照明', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603517', 'instrumentName': '绝味食品', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603556', 'instrumentName': '海兴电力', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603568', 'instrumentName': '伟明环保', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603569', 'instrumentName': '长久物流', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603650', 'instrumentName': '彤程新材', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603659', 'instrumentName': '璞泰来', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603712', 'instrumentName': '七一二', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603766', 'instrumentName': '隆鑫通用', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603806', 'instrumentName': '福斯特', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603816', 'instrumentName': '顾家家居', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603866', 'instrumentName': '桃李面包', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603868', 'instrumentName': '飞科电器', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603877', 'instrumentName': '太平鸟', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603883', 'instrumentName': '老百姓', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603885', 'instrumentName': '吉祥航空', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603888', 'instrumentName': '新华网', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603939', 'instrumentName': '益丰药房', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 6100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': '         8220.000', 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600132', 'instrumentName': '重庆啤酒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600298', 'instrumentName': '安琪酵母', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600315', 'instrumentName': '上海家化', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600438', 'instrumentName': '通威股份', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600559', 'instrumentName': '老白干酒', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600597', 'instrumentName': '光明乳业', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600598', 'instrumentName': '北大荒', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600600', 'instrumentName': '青岛啤酒', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600702', 'instrumentName': '舍得酒业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600737', 'instrumentName': '中粮糖业', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600779', 'instrumentName': '水井坊', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600809', 'instrumentName': '山西汾酒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600811', 'instrumentName': '东方集团', 'premiumRate': '10%', 'quantity': 7300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600827', 'instrumentName': '百联股份', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600872', 'instrumentName': '中炬高新', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600873', 'instrumentName': '梅花生物', 'premiumRate': '10%', 'quantity': 5300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 6700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600929', 'instrumentName': '湖南盐业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601933', 'instrumentName': '永辉超市', 'premiumRate': '10%', 'quantity': 9400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601952', 'instrumentName': '苏垦农发', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603043', 'instrumentName': '广州酒家', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603156', 'instrumentName': '养元饮品', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603198', 'instrumentName': '迎驾贡酒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603288', 'instrumentName': '海天味业', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603369', 'instrumentName': '今世缘', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603517', 'instrumentName': '绝味食品', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603589', 'instrumentName': '口子窖', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603708', 'instrumentName': '家家悦', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603866', 'instrumentName': '桃李面包', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600015', 'instrumentName': '华夏银行', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 7600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600919', 'instrumentName': '江苏银行', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600999', 'instrumentName': '招商证券', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601155', 'instrumentName': '新城控股', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 11700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 8200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 6500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 4800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 6400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600056', 'instrumentName': '中国医药', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600062', 'instrumentName': '华润双鹤', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600079', 'instrumentName': '人福医药', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600085', 'instrumentName': '同仁堂', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600161', 'instrumentName': '天坛生物', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600201', 'instrumentName': '生物股份', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600216', 'instrumentName': '浙江医药', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600252', 'instrumentName': '中恒集团', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600267', 'instrumentName': '海正药业', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600299', 'instrumentName': '安迪苏', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600329', 'instrumentName': '中新药业', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600332', 'instrumentName': '白云山', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600380', 'instrumentName': '健康元', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600420', 'instrumentName': '现代制药', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600436', 'instrumentName': '片仔癀', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600511', 'instrumentName': '国药股份', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600521', 'instrumentName': '华海药业', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600535', 'instrumentName': '天士力', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600566', 'instrumentName': '济川药业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600572', 'instrumentName': '康恩贝', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600664', 'instrumentName': '哈药股份', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600763', 'instrumentName': '通策医疗', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600771', 'instrumentName': '广誉远', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600781', 'instrumentName': 'ST辅仁', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600867', 'instrumentName': '通化东宝', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600998', 'instrumentName': '九州通', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603233', 'instrumentName': '大参林', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603367', 'instrumentName': '辰欣药业', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603658', 'instrumentName': '安图生物', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603707', 'instrumentName': '健友股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603858', 'instrumentName': '步长制药', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603882', 'instrumentName': '金域医学', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603883', 'instrumentName': '老百姓', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603939', 'instrumentName': '益丰药房', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 2000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 6600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 4700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': '            0.000', 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': None, 'quantity': 0, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1200, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 4700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': None, 'quantity': 1500, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': None, 'quantity': 2000, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': None, 'quantity': 600, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': None, 'quantity': 1200, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': None, 'quantity': 2800, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': None, 'quantity': 7300, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': None, 'quantity': 2100, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': None, 'quantity': 5200, 'substituteFlag': '禁止'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 4100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 5200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600009', 'instrumentName': '上海机场', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 11400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600073', 'instrumentName': '上海梅林', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600097', 'instrumentName': '开创国际', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600170', 'instrumentName': '上海建工', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': '          806.000', 'instrumentId': '600272', 'instrumentName': '开开实业', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '600278', 'instrumentName': '东方创业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600284', 'instrumentName': '浦东建设', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600597', 'instrumentName': '光明乳业', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600602', 'instrumentName': '云赛智联', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600604', 'instrumentName': '市北高新', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 7800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600612', 'instrumentName': '老凤祥', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600616', 'instrumentName': '金枫酒业', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600618', 'instrumentName': '氯碱化工', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600619', 'instrumentName': '海立股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600621', 'instrumentName': '华鑫股份', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600623', 'instrumentName': '华谊集团', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600626', 'instrumentName': '申达股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600628', 'instrumentName': '新世界', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600629', 'instrumentName': '华建集团', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600630', 'instrumentName': '龙头股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600637', 'instrumentName': '东方明珠', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600639', 'instrumentName': '浦东金桥', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600642', 'instrumentName': '申能股份', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600648', 'instrumentName': '外高桥', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600649', 'instrumentName': '城投控股', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600650', 'instrumentName': '锦江投资', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600651', 'instrumentName': '飞乐音响', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600662', 'instrumentName': '强生控股', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600663', 'instrumentName': '陆家嘴', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600675', 'instrumentName': '中华企业', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600676', 'instrumentName': '交运股份', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': '         1264.000', 'instrumentId': '600679', 'instrumentName': '上海凤凰', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '600692', 'instrumentName': '亚通股份', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600708', 'instrumentName': '光明地产', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600748', 'instrumentName': '上实发展', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600754', 'instrumentName': '锦江酒店', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600773', 'instrumentName': '西藏城投', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600819', 'instrumentName': '耀皮玻璃', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600820', 'instrumentName': '隧道股份', 'premiumRate': '10%', 'quantity': 2400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600822', 'instrumentName': '上海物贸', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600824', 'instrumentName': '益民集团', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600825', 'instrumentName': '新华传媒', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600826', 'instrumentName': '兰生股份', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600827', 'instrumentName': '百联股份', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600833', 'instrumentName': '第一医药', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600834', 'instrumentName': '申通地铁', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600835', 'instrumentName': '上海机电', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600838', 'instrumentName': '上海九百', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600841', 'instrumentName': '上柴股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600843', 'instrumentName': '上工申贝', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600848', 'instrumentName': '上海临港', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600895', 'instrumentName': '张江高科', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600958', 'instrumentName': '东方证券', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601200', 'instrumentName': '上海环境', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601595', 'instrumentName': '上海电影', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601607', 'instrumentName': '上海医药', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601727', 'instrumentName': '上海电气', 'premiumRate': '10%', 'quantity': 7400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603648', 'instrumentName': '畅联股份', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603881', 'instrumentName': '数据港', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600000', 'instrumentName': '浦发银行', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600016', 'instrumentName': '民生银行', 'premiumRate': '10%', 'quantity': 7800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600029', 'instrumentName': '南方航空', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600030', 'instrumentName': '中信证券', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600031', 'instrumentName': '三一重工', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 3200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600050', 'instrumentName': '中国联通', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600196', 'instrumentName': '复星医药', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600276', 'instrumentName': '恒瑞医药', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600309', 'instrumentName': '万华化学', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600340', 'instrumentName': '华夏幸福', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600519', 'instrumentName': '贵州茅台', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600690', 'instrumentName': '海尔智家', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600703', 'instrumentName': '三安光电', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600837', 'instrumentName': '海通证券', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601066', 'instrumentName': '中信建投', 'premiumRate': '10%', 'quantity': 100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601111', 'instrumentName': '中国国航', 'premiumRate': '10%', 'quantity': 900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601138', 'instrumentName': '工业富联', 'premiumRate': '10%', 'quantity': 400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601186', 'instrumentName': '中国铁建', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601211', 'instrumentName': '国泰君安', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601229', 'instrumentName': '上海银行', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 12000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 3400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601319', 'instrumentName': '中国人保', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 8600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601336', 'instrumentName': '新华保险', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601390', 'instrumentName': '中国中铁', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 6800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601601', 'instrumentName': '中国太保', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601628', 'instrumentName': '中国人寿', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 6600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601688', 'instrumentName': '华泰证券', 'premiumRate': '10%', 'quantity': 1100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601766', 'instrumentName': '中国中车', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601800', 'instrumentName': '中国交建', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601857', 'instrumentName': '中国石油', 'premiumRate': '10%', 'quantity': 2500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601888', 'instrumentName': '中国国旅', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 6600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601989', 'instrumentName': '中国重工', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': '         8220.000', 'instrumentId': '603259', 'instrumentName': '药明康德', 'premiumRate': None, 'quantity': 100, 'substituteFlag': '必须'}, {'cashAmount': None, 'instrumentId': '603993', 'instrumentName': '洛阳钼业', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600011', 'instrumentName': '华能国际', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600019', 'instrumentName': '宝钢股份', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600023', 'instrumentName': '浙能电力', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600028', 'instrumentName': '中国石化', 'premiumRate': '10%', 'quantity': 6800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600033', 'instrumentName': '福建高速', 'premiumRate': '10%', 'quantity': 7100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600066', 'instrumentName': '宇通客车', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600162', 'instrumentName': '香江控股', 'premiumRate': '10%', 'quantity': 11500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600177', 'instrumentName': '雅戈尔', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600183', 'instrumentName': '生益科技', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600269', 'instrumentName': '赣粤高速', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600312', 'instrumentName': '平高电气', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600325', 'instrumentName': '华发股份', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600350', 'instrumentName': '山东高速', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600376', 'instrumentName': '首开股份', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600377', 'instrumentName': '宁沪高速', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600507', 'instrumentName': '方大特钢', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600585', 'instrumentName': '海螺水泥', 'premiumRate': '10%', 'quantity': 500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600642', 'instrumentName': '申能股份', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600664', 'instrumentName': '哈药股份', 'premiumRate': '10%', 'quantity': 14300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600674', 'instrumentName': '川投能源', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600688', 'instrumentName': '上海石化', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600704', 'instrumentName': '物产中大', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600795', 'instrumentName': '国电电力', 'premiumRate': '10%', 'quantity': 6800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600873', 'instrumentName': '梅花生物', 'premiumRate': '10%', 'quantity': 7900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600887', 'instrumentName': '伊利股份', 'premiumRate': '10%', 'quantity': 700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601009', 'instrumentName': '南京银行', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601088', 'instrumentName': '中国神华', 'premiumRate': '10%', 'quantity': 5000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601158', 'instrumentName': '重庆水务', 'premiumRate': '10%', 'quantity': 4400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 7600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 4600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601566', 'instrumentName': '九牧王', 'premiumRate': '10%', 'quantity': 2800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601636', 'instrumentName': '旗滨集团', 'premiumRate': '10%', 'quantity': 7000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601818', 'instrumentName': '光大银行', 'premiumRate': '10%', 'quantity': 5100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 7200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 3800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603001', 'instrumentName': '奥康国际', 'premiumRate': '10%', 'quantity': 2300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603328', 'instrumentName': '依顿电子', 'premiumRate': '10%', 'quantity': 3600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603766', 'instrumentName': '隆鑫通用', 'premiumRate': '10%', 'quantity': 5800, 'substituteFlag': '允许'}]
[{'cashAmount': None, 'instrumentId': '600018', 'instrumentName': '上港集团', 'premiumRate': '10%', 'quantity': 2600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600020', 'instrumentName': '中原高速', 'premiumRate': '10%', 'quantity': 4000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600033', 'instrumentName': '福建高速', 'premiumRate': '10%', 'quantity': 6900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600036', 'instrumentName': '招商银行', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600048', 'instrumentName': '保利地产', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600068', 'instrumentName': '葛洲坝', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600089', 'instrumentName': '特变电工', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600104', 'instrumentName': '上汽集团', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600114', 'instrumentName': '东睦股份', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600153', 'instrumentName': '建发股份', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600210', 'instrumentName': '紫江企业', 'premiumRate': '10%', 'quantity': 3900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600236', 'instrumentName': '桂冠电力', 'premiumRate': '10%', 'quantity': 5500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600269', 'instrumentName': '赣粤高速', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600273', 'instrumentName': '嘉化能源', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600376', 'instrumentName': '首开股份', 'premiumRate': '10%', 'quantity': 4200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600377', 'instrumentName': '宁沪高速', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600383', 'instrumentName': '金地集团', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600398', 'instrumentName': '海澜之家', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600406', 'instrumentName': '国电南瑞', 'premiumRate': '10%', 'quantity': 600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600563', 'instrumentName': '法拉电子', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600606', 'instrumentName': '绿地控股', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600612', 'instrumentName': '老凤祥', 'premiumRate': '10%', 'quantity': 300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600657', 'instrumentName': '信达地产', 'premiumRate': '10%', 'quantity': 2700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600660', 'instrumentName': '福耀玻璃', 'premiumRate': '10%', 'quantity': 1000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600674', 'instrumentName': '川投能源', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600688', 'instrumentName': '上海石化', 'premiumRate': '10%', 'quantity': 4900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600741', 'instrumentName': '华域汽车', 'premiumRate': '10%', 'quantity': 1500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600742', 'instrumentName': '一汽富维', 'premiumRate': '10%', 'quantity': 1400, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600743', 'instrumentName': '华远地产', 'premiumRate': '10%', 'quantity': 5900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600820', 'instrumentName': '隧道股份', 'premiumRate': '10%', 'quantity': 1900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600828', 'instrumentName': '茂业商业', 'premiumRate': '10%', 'quantity': 3300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600835', 'instrumentName': '上海机电', 'premiumRate': '10%', 'quantity': 800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600900', 'instrumentName': '长江电力', 'premiumRate': '10%', 'quantity': 1800, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '600987', 'instrumentName': '航民股份', 'premiumRate': '10%', 'quantity': 2100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601006', 'instrumentName': '大秦铁路', 'premiumRate': '10%', 'quantity': 2900, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601098', 'instrumentName': '中南传媒', 'premiumRate': '10%', 'quantity': 1700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601166', 'instrumentName': '兴业银行', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601169', 'instrumentName': '北京银行', 'premiumRate': '10%', 'quantity': 3000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601216', 'instrumentName': '君正集团', 'premiumRate': '10%', 'quantity': 5300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601288', 'instrumentName': '农业银行', 'premiumRate': '10%', 'quantity': 7300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601318', 'instrumentName': '中国平安', 'premiumRate': '10%', 'quantity': 200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601328', 'instrumentName': '交通银行', 'premiumRate': '10%', 'quantity': 4300, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601398', 'instrumentName': '工商银行', 'premiumRate': '10%', 'quantity': 4500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601598', 'instrumentName': '中国外运', 'premiumRate': '10%', 'quantity': 3100, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601668', 'instrumentName': '中国建筑', 'premiumRate': '10%', 'quantity': 2200, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601939', 'instrumentName': '建设银行', 'premiumRate': '10%', 'quantity': 3500, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601988', 'instrumentName': '中国银行', 'premiumRate': '10%', 'quantity': 7000, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '601998', 'instrumentName': '中信银行', 'premiumRate': '10%', 'quantity': 3700, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603167', 'instrumentName': '渤海轮渡', 'premiumRate': '10%', 'quantity': 1600, 'substituteFlag': '允许'}, {'cashAmount': None, 'instrumentId': '603198', 'instrumentName': '迎驾贡酒', 'premiumRate': '10%', 'quantity': 1300, 'substituteFlag': '允许'}]
