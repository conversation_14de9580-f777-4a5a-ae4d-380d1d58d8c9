# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
import numpy as np
from scipy import interpolate
import pandas as pd
import datetime
import os
import sys
import plotly.graph_objects as go
from plotly.subplots import make_subplots

sys.path.extend(
    [os.path.abspath(os.path.join(os.path.abspath(__file__) if '__file__' in globals() else os.getcwd(), *(['..'] * i)))
     for i in range(5)])

from db_solve.parreader import readmd, loadtrade, load_orders, loadvol


def cubicspline(z, target):
    z = z.sort_values(by=['delta'])
    z1 = z.loc[z.delta > target].head(2)
    z2 = z.loc[z.delta < target].tail(2)
    z = pd.concat([z2, z1])
    x = z.delta
    y = z.sigma
    try:
        s = interpolate.CubicSpline(x, y)
        return (s(target))
    except:
        return (-99)


tradetime00 = [['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']]

def vol_cal(path):
    data = loadvol(path, [], tradetime00, all=True)
    
    # 使用'min'替代'T'
    data = (data.groupby('code')
                .resample('1min')
                .last()
                .drop('code', axis=1)
                .reset_index()
                .query('delta > 0'))
    
    # 使用apply方法简化计算
    output = data.groupby(['exp', 'time']).apply(lambda group: pd.Series({
        'Delta50': cubicspline(group, 0.50),
        'Slope': cubicspline(group, 0.25)-cubicspline(group, 0.75),
        'time2expiry': group.time2expiry.iloc[0],
        'spot': group.spot.iloc[0],
        'forward': group.spot.iloc[0] * np.exp((group.rf.iloc[0] - group.br.iloc[0]) * group.time2expiry.iloc[0])
    }),include_groups=False).reset_index()
    
    output['forward_vol'] = output.Delta50
    
    # 计算dt和dvol
    output['dt'] = output.groupby('time').time2expiry.diff()
    output['dvol'] = output.groupby('time').forward_vol.diff()
    
    return output

# 使用更pythonic的路径处理
input_path = r'G:\DATA\SH300\vols_20240930.parquet'
output_path = r'G:\DATA\SH300\vols_20240930.csv'
output = vol_cal(input_path)
output.to_csv(output_path)
