
# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import importlib.util
from inspect import signature

# ===== 设置路径与导入 factorMidPrice.py =====
module_path = "factorMidPrice.py"
spec = importlib.util.spec_from_file_location("factorMidPrice", module_path)
factor = importlib.util.module_from_spec(spec)
spec.loader.exec_module(factor)

# ===== 参数设置 =====
INPUT_CSV = "sample.csv"
OUTPUT_CSV = "kline_with_all_factors.csv"
SCALE = 3  # 所有 period/window 参数统一放大倍数

# ===== 读取 snapshot 数据并生成K线 =====
df = pd.read_csv(INPUT_CSV)
df['time'] = pd.to_datetime(df['time'])
df['mid'] = (df['BidPrice1'] + df['AskPrice1']) / 2
df['open'] = df['mid']
df['high'] = df['mid']
df['low'] = df['mid']
df['close'] = df['mid']
df['volume'] = df['Volume'] if 'Volume' in df.columns else 0

kline = df[['time', 'open', 'high', 'low', 'close', 'volume']].copy()

# ===== 技术指标计算 =====
price_inputs = {
    'high': kline['high'],
    'low': kline['low'],
    'close': kline['close'],
    'open_p': kline['open'],
    'volume': kline['volume']
}

window_keys = [
    'period', 'window', 'atr_period', 'fast_window', 'slow_window',
    'base', 'conversion', 'leading_span', 'lookback',
    'stoch_period', 'rsi_period', 'apc_period', 'ema_period', 'lag'
]

special_params = {
    'mult': 2,
    'r': 0.6,
    'af_start': 0.02,
    'af_step': 0.02,
    'af_max': 0.2,
    'volume_factor': 0.7,
    'numerator_coeffs': [1.0],
    'denominator_coeffs': [1.0],
    'weights': None,
    'window_length': 5,
    'poly_order': 2,
    'polyorder': 2
}

for name in dir(factor):
    if name.startswith("_"):
        continue
    func = getattr(factor, name)
    if not callable(func):
        continue

    try:
        sig = signature(func)
        params = sig.parameters
        kwargs = {}

        for p in params:
            if p in price_inputs:
                kwargs[p] = price_inputs[p]
            elif p in window_keys:
                kwargs[p] = int(SCALE * 10)
            elif p == 'series':
                kwargs[p] = kline['close']
            elif p in special_params:
                kwargs[p] = special_params[p]
            else:
                kwargs[p] = kline['close']

        result = func(**kwargs)
        if isinstance(result, pd.Series):
            kline[name] = result
        elif isinstance(result, pd.DataFrame):
            for col in result.columns:
                kline[f"{name}_{col}"] = result[col]
        elif isinstance(result, tuple):
            for i, r in enumerate(result):
                if isinstance(r, (pd.Series, pd.DataFrame)):
                    if isinstance(r, pd.Series):
                        kline[f"{name}_{i}"] = r
                    else:
                        for subcol in r.columns:
                            kline[f"{name}_{i}_{subcol}"] = r[subcol]
    except Exception as e:
        print(f"跳过函数 {name}: {e}")

# ===== 输出结果 =====
kline.to_csv(OUTPUT_CSV, index=False)
print(f"成功输出到 {OUTPUT_CSV}")
