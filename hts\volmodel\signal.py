# -*- coding: utf-8 -*-
"""
Created on Fri Mar 10 17:41:49 2023

@author: Lenovo
"""
import numpy as np

import pyqtgraph as pg
from pyqtgraph.Qt import QtCore
import pandas as pd

import threading
import time
import pandas as pd
import cal300
import cal50

num = 0
resdata = pd.DataFrame()


def threading1():
    global resdata, num
    while True:

        num = num + 1
        time.sleep(1)
        res300 = cal300.rescal()
        res50 = cal50.rescal()
        resdata = resdata.append(pd.DataFrame(res300 + res50).T)

        if num == 10000:
            break


th1 = threading.Thread(target=threading1)
th1.start()

app = pg.mkQApp("Plotting Example")
# mw = QtWidgets.QMainWindow()
# mw.resize(800,800)

win = pg.GraphicsLayoutWidget(show=True, title="signal")
win.resize(1000, 600)
win.setWindowTitle('signal_version_1.0')

# Enable antialiasing for prettier plots
pg.setConfigOptions(antialias=True)
pg.setConfigOption('background', 'w')
pg.setConfigOption('foreground', 'w')

p1 = win.addPlot(title="vol_510300")
curve11 = p1.plot(pen=pg.mkPen('w', width=3))
curve12 = p1.plot(pen=pg.mkPen('y', width=3))

p2 = win.addPlot(title="y2_510300")
curve2 = p2.plot(pen=pg.mkPen('w', width=3))

p3 = win.addPlot(title="calendar300")
curve3 = p3.plot(pen=pg.mkPen('w', width=3))
win.nextRow()

p4 = win.addPlot(title="vol_510050")
curve41 = p4.plot(pen=pg.mkPen('w', width=3))
curve42 = p4.plot(pen=pg.mkPen('y', width=3))

p5 = win.addPlot(title="y2_510050")
curve5 = p5.plot(pen=pg.mkPen('w', width=3))

p6 = win.addPlot(title="calendar50")
curve6 = p6.plot(pen=pg.mkPen('w', width=3))
win.nextRow()
p7 = win.addPlot(title="vol50-vol300")
curve7 = p7.plot(pen=pg.mkPen('w', width=3))

ptr = 100


def update1():
    global curve11, ptr, p1
    curve11.setData(np.array(resdata.iloc[:, 0]))
    curve12.setData(np.array(resdata.iloc[:, 1]))
    curve2.setData(np.array(resdata.iloc[:, 2]))
    curve3.setData(
        np.array(resdata.iloc[:, 0] * np.sqrt(resdata.iloc[:, 3]) - resdata.iloc[:, 1] * np.sqrt(resdata.iloc[:, 4])))
    curve41.setData(np.array(resdata.iloc[:, 5]))
    curve42.setData(np.array(resdata.iloc[:, 6]))
    curve5.setData(np.array(resdata.iloc[:, 7]))
    curve6.setData(
        np.array(resdata.iloc[:, 5] * np.sqrt(resdata.iloc[:, 8]) - resdata.iloc[:, 6] * np.sqrt(resdata.iloc[:, 9])))
    curve7.setData(np.array(resdata.iloc[:, 0] - resdata.iloc[:, 5]))


"""
    p1.enableAutoRange('xy', True)  ## stop auto-scaling after the first data set is plotted
    p2.enableAutoRange('xy', True)
    p3.enableAutoRange('xy', True)
    p4.enableAutoRange('xy', True)
    p5.enableAutoRange('xy', True)
    p6.enableAutoRange('xy', True)
    p7.enableAutoRange('xy', True)
"""

timer = QtCore.QTimer()
timer.timeout.connect(update1)
timer.start(500)

x2 = np.linspace(-100, 100, 1000)
data2 = np.sin(x2) / x2
p8 = win.addPlot(title="Region Selection")
p8.plot(data2, pen=(255, 255, 255, 200))
lr = pg.LinearRegionItem([400, 700])
lr.setZValue(-10)
p8.addItem(lr)

p9 = win.addPlot(title="Zoom on selected region")
p9.plot(data2)


def updatePlot():
    p9.setXRange(*lr.getRegion(), padding=0)


def updateRegion():
    lr.setRegion(p9.getViewBox().viewRange()[0])


lr.sigRegionChanged.connect(updatePlot)
p9.sigXRangeChanged.connect(updateRegion)
updatePlot()

if __name__ == '__main__':
    pg.exec()
