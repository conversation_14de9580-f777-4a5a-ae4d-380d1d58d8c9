
"""
Created on Sun Mar 23 17:28:55 2025

@author: lining
"""









import numpy as np
import pandas as pd
from ..factor_manager import factor_manager, Factor, FactorCategory
from scipy.signal import savgol_filter
from scipy.linalg import inv
from scipy.signal import filtfilt


COLUMN_MAPPING = {
    'high': ['High', 'high'],
    'low': ['Low', 'low'],
    'close': ['Close', 'close', 'LastPrice'],
    'open': ['Open', 'open'],
    'volume': ['Volume', 'volume', 'TotalValueTraded']
}

def get_column(df, col_name):
    
    for name in COLUMN_MAPPING.get(col_name.lower(), [col_name]):
        if name in df.columns:
            return df[name]
    raise ValueError(f"找不到与'{col_name}'对应的列，可用列: {df.columns.tolist()}")



def SMA(series, window):
    
    return series.rolling(window).mean()

def EMA(series, window):
    
    return series.ewm(span=window, adjust=False).mean()

def WMA(series, window):
    
    weights = np.arange(1, window+1)
    return series.rolling(window).apply(lambda x: np.dot(x, weights)/weights.sum(), raw=True)



np.random.seed(42)  


base_prices = np.cumsum(np.random.normal(0.1, 0.5, 40)) + 50

data = pd.DataFrame({
    'open': base_prices,
    'high': base_prices + np.abs(np.random.normal(0.5, 0.3, 40)),
    'low': base_prices - np.abs(np.random.normal(0.5, 0.3, 40)),
    'close': base_prices + np.random.normal(0, 0.2, 40),
    'volume': (np.abs(np.random.normal(10000, 3000, 40)) * (1 + 0.5 * np.sin(np.arange(40)/5)))})


data['high'] = data[['open', 'close']].max(axis=1) + np.random.uniform(0, 0.5, 40)
data['low'] = data[['open', 'close']].min(axis=1) - np.random.uniform(0, 0.5, 40)
data['volume'] = data['volume'].astype(int)


data = data.round(2)





def Accumulation_Distribution(df):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    volume = get_column(df, 'volume')
    
    money_flow_multiplier = ((close - low) - (high - close)) / (high - low).replace(0, 1e-5)
    money_flow_volume = money_flow_multiplier * volume
    return money_flow_volume.cumsum()

factor_manager.register_factor(Factor(
    name="Accumulation_Distribution",
    category=FactorCategory.TECHNICAL,
    description="累积/分配指标",
    calculation=Accumulation_Distribution,
    dependencies=['high', 'low', 'close', 'volume'],
    parameters={}
))

def Awesome_Oscillator(df, window1=5, window2=34):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    mid_price = (high + low) / 2
    return SMA(mid_price, window1) - SMA(mid_price, window2)

factor_manager.register_factor(Factor(
    name="Awesome_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="Awesome Oscillator",
    calculation=Awesome_Oscillator,
    dependencies=['high', 'low'],
    parameters={'window1': 5, 'window2': 34}
))

def calculate_adx(df, period: int = 14):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    
    high_diff = high.diff()
    low_diff = -low.diff()
    
    
    plus_dm = pd.Series(0, index=high.index, dtype=float)
    minus_dm = pd.Series(0, index=high.index, dtype=float)
    
    valid_plus = (high_diff > low_diff) & (high_diff > 0)
    valid_minus = (low_diff > high_diff) & (low_diff > 0)
    
    
    plus_dm.loc[valid_plus] = high_diff.loc[valid_plus].astype(float)
    minus_dm.loc[valid_minus] = low_diff.loc[valid_minus].astype(float)
    
    
    prev_close = close.shift(1)
    tr = pd.DataFrame({
        'hl': high - low,
        'hc': (high - prev_close).abs(),
        'lc': (low - prev_close).abs()
    }).max(axis=1)
    
    
    tr14 = tr.ewm(alpha=1/period, adjust=False).mean()
    plus_dm14 = plus_dm.ewm(alpha=1/period, adjust=False).mean()
    minus_dm14 = minus_dm.ewm(alpha=1/period, adjust=False).mean()
    
    
    plus_di14 = 100 * (plus_dm14 / tr14.replace(0, 1e-5))
    minus_di14 = 100 * (minus_dm14 / tr14.replace(0, 1e-5))
    
    
    di_diff = (plus_di14 - minus_di14).abs()
    di_sum = plus_di14 + minus_di14
    dx = 100 * (di_diff / di_sum.replace(0, 1e-5))
    
    
    adx = dx.ewm(alpha=1/period, adjust=False).mean()
    
    
    adxr = (adx + adx.shift(period)) / 2
    
    
    atr = tr.ewm(alpha=1/period, adjust=False).mean()
    
    return pd.DataFrame({
        '+DI14': plus_di14,
        '-DI14': minus_di14,
        'DX': dx,
        'ADX': adx,
        'ADXR': adxr,
        'ATR': atr
    })

def calculate_adx_adapter(df, period=14):
    
    return calculate_adx(df, period)['ADX']

def calculate_adx_di_plus_adapter(df, period=14):
    
    return calculate_adx(df, period)['+DI14']

def calculate_adx_di_minus_adapter(df, period=14):
    
    return calculate_adx(df, period)['-DI14']

def calculate_adx_dx_adapter(df, period=14):
    
    return calculate_adx(df, period)['DX']

def calculate_adx_adxr_adapter(df, period=14):
    
    return calculate_adx(df, period)['ADXR']

factor_manager.register_factor(Factor(
    name="ADX",
    category=FactorCategory.TECHNICAL,
    description="平均方向指数（Average Directional Index）",
    calculation=calculate_adx_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADX_DI_Plus",
    category=FactorCategory.TECHNICAL,
    description="ADX正向动向指标（+DI）",
    calculation=calculate_adx_di_plus_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADX_DI_Minus",
    category=FactorCategory.TECHNICAL,
    description="ADX负向动向指标（-DI）",
    calculation=calculate_adx_di_minus_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADX_DX",
    category=FactorCategory.TECHNICAL,
    description="方向指数（DX）",
    calculation=calculate_adx_dx_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

factor_manager.register_factor(Factor(
    name="ADXR",
    category=FactorCategory.TECHNICAL,
    description="ADX平均值（ADXR）",
    calculation=calculate_adx_adxr_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))
def ATR(df, period: int = 14):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    
    prev_close = close.shift(1)
    
    
    tr = pd.DataFrame({
        'hl': high - low,  
        'hpc': (high - prev_close).abs(),  
        'lpc': (low - prev_close).abs()  
    }).max(axis=1)
    
    
    
    
    atr = pd.Series(index=tr.index, dtype=float)
    atr.iloc[:period] = tr.iloc[:period].rolling(window=period, min_periods=1).mean()
    
    
    if len(tr) > period:
        atr.iloc[period:] = tr.iloc[period:].ewm(alpha=1/period, adjust=False).mean()
    
    return atr

def ATR_adapter(df, period=14):
    
    return ATR(df, period)

factor_manager.register_factor(Factor(
    name="ATR",
    category=FactorCategory.TECHNICAL,
    description="平均真实波幅（Average True Range）",
    calculation=ATR_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 14}
))

def Alligator(df, jaw=13, teeth=8, lips=5):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    mid_price = (high + low) / 2
    return {
        'jaw': SMA(mid_price, jaw),
        'teeth': SMA(mid_price, teeth),
        'lips': SMA(mid_price, lips)
    }

def Alligator_adapter(df, jaw=13, teeth=8, lips=5):
    
    result = Alligator(df, jaw, teeth, lips)
    return result['jaw']

def Alligator_teeth_adapter(df, jaw=13, teeth=8, lips=5):
    
    result = Alligator(df, jaw, teeth, lips)
    return result['teeth']

def Alligator_lips_adapter(df, jaw=13, teeth=8, lips=5):
    
    result = Alligator(df, jaw, teeth, lips)
    return result['lips']

factor_manager.register_factor(Factor(
    name="Alligator",
    category=FactorCategory.TECHNICAL,
    description="威廉姆斯鳄鱼指标（Williams Alligator Indicator）- 颚线",
    calculation=Alligator_adapter,
    dependencies=['high', 'low'],
    parameters={'jaw': 13, 'teeth': 8, 'lips': 5}
))

factor_manager.register_factor(Factor(
    name="Alligator_teeth",
    category=FactorCategory.TECHNICAL,
    description="威廉姆斯鳄鱼指标（Williams Alligator Indicator）- 牙齿线",
    calculation=Alligator_teeth_adapter,
    dependencies=['high', 'low'],
    parameters={'jaw': 13, 'teeth': 8, 'lips': 5}
))

factor_manager.register_factor(Factor(
    name="Alligator_lips",
    category=FactorCategory.TECHNICAL,
    description="威廉姆斯鳄鱼指标（Williams Alligator Indicator）- 唇线",
    calculation=Alligator_lips_adapter,
    dependencies=['high', 'low'],
    parameters={'jaw': 13, 'teeth': 8, 'lips': 5}
))

def absolute_price_oscillator(df, fast_window=5, slow_window=13):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    mid_price = (high + low) / 2
    apo = EMA(mid_price, fast_window) - EMA(mid_price, slow_window)
    return apo

factor_manager.register_factor(Factor(
    name="APO",
    category=FactorCategory.TECHNICAL,
    description="绝对价格振荡器（Absolute Price Oscillator）",
    calculation=absolute_price_oscillator,
    dependencies=['high', 'low'],
    parameters={'fast_window': 5, 'slow_window': 13}
))

def aroon_indicator(df, window=20):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    
    window_f = float(window)
    scale_factor = 100.0 / window_f
    
    
    high_idx = high.rolling(window).apply(lambda x: window - np.argmax(x) - 1, raw=True)
    low_idx = low.rolling(window).apply(lambda x: window - np.argmin(x) - 1, raw=True)
    
    
    aroon_up = high_idx * scale_factor
    aroon_down = low_idx * scale_factor
    
    
    result = pd.DataFrame({
        'Aroon_Up': aroon_up,
        'Aroon_Down': aroon_down
    })
    
    return result

factor_manager.register_factor(Factor(
    name="Aroon_Indicator",
    category=FactorCategory.TECHNICAL,
    description="Aroon指标（Aroon Indicator）",
    calculation=aroon_indicator,
    dependencies=['high', 'low'],
    parameters={'window': 20}
))

def aroon_oscillator(df, window=14):
    
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    
    window_f = float(window)
    scale_factor = 100.0 / window_f
    
    
    high_idx = high.rolling(window).apply(lambda x: window - np.argmax(x) - 1, raw=True)
    low_idx = low.rolling(window).apply(lambda x: window - np.argmin(x) - 1, raw=True)
    
    
    return (high_idx - low_idx) * scale_factor

factor_manager.register_factor(Factor(
    name="Aroon_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="Aroon振荡器（Aroon Oscillator）",
    calculation=aroon_oscillator,
    dependencies=['high', 'low', 'close'],
    parameters={'window': 14}
))

def Bollinger_Bands(df, window=20, std_dev=2):
    
    close = get_column(df, 'close')
    sma = SMA(close, window)
    std = close.rolling(window).std()
    
    upper = sma + std_dev * std
    middle = sma
    lower = sma - std_dev * std
    
    
    bandwidth = (upper - lower) / middle
    
    
    signal = pd.Series(0.0, index=close.index)  
    signal[close > upper] = close[close > upper] - upper[close > upper]  
    signal[close < lower] = close[close < lower] - lower[close < lower]  
    
    
    bandwidth_pct_change = bandwidth.pct_change(periods=5)
    
    return {
        'upper': upper,
        'middle': middle,
        'lower': lower,
        'signal': signal,
        'bandwidth': bandwidth,
        'bandwidth_change': bandwidth_pct_change
    }

def Bollinger_Bands_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['middle']

def Bollinger_Bands_upper_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['upper']

def Bollinger_Bands_lower_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['lower']

factor_manager.register_factor(Factor(
    name="Bollinger_Bands",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 中轨",
    calculation=Bollinger_Bands_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))

factor_manager.register_factor(Factor(
    name="Bollinger_Bands_Upper",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 上轨",
    calculation=Bollinger_Bands_upper_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))

factor_manager.register_factor(Factor(
    name="Bollinger_Bands_Lower",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 下轨",
    calculation=Bollinger_Bands_lower_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))

def Bollinger_Bands_signal_adapter(df, window=20, std_dev=2):
    result = Bollinger_Bands(df, window, std_dev)
    return result['signal']

factor_manager.register_factor(Factor(
    name="Bollinger_Bands_Signal",
    category=FactorCategory.TECHNICAL,
    description="布林带（Bollinger Bands）- 信号",
    calculation=Bollinger_Bands_signal_adapter,
    dependencies=['close'],
    parameters={'window': 20, 'std_dev': 2}
))


def Ichimoku_Cloud(df, conversion=9, base=26, leading_span=52):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    conversion_line = (high.rolling(conversion).max() + low.rolling(conversion).min()) / 2
    base_line = (high.rolling(base).max() + low.rolling(base).min()) / 2
    leading_span_a = (conversion_line + base_line) / 2
    leading_span_b = (high.rolling(leading_span).max() + low.rolling(leading_span).min()) / 2
    return {
        'conversion': conversion_line,
        'base': base_line,
        'leading_a': leading_span_a,
        'leading_b': leading_span_b
    }

def Ichimoku_Cloud_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['leading_a']

def Ichimoku_Cloud_conversion_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['conversion']

def Ichimoku_Cloud_base_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['base']

def Ichimoku_Cloud_leading_b_adapter(df, conversion=9, base=26, leading_span=52):
    result = Ichimoku_Cloud(df, conversion, base, leading_span)
    return result['leading_b']

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 前导跨度A",
    calculation=Ichimoku_Cloud_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud_Conversion",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 转换线",
    calculation=Ichimoku_Cloud_conversion_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud_Base",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 基准线",
    calculation=Ichimoku_Cloud_base_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

factor_manager.register_factor(Factor(
    name="Ichimoku_Cloud_Leading_B",
    category=FactorCategory.TECHNICAL,
    description="一目均衡表（Ichimoku Cloud）- 前导跨度B",
    calculation=Ichimoku_Cloud_leading_b_adapter,
    dependencies=['high', 'low'],
    parameters={'conversion': 9, 'base': 26, 'leading_span': 52}
))

def Chande_Momentum_Oscillator(df, period=19):
    
    close = get_column(df, 'close')
    delta = close.diff()
    su = delta.where(delta > 0, 0).rolling(window=period).sum()
    sd = (-delta.where(delta < 0, 0)).rolling(window=period).sum()
    cmo = 100 * (su - sd) / (su + sd).replace(0, 1e-5)
    return cmo

factor_manager.register_factor(Factor(
    name="CMO",
    category=FactorCategory.TECHNICAL,
    description="Chande动量振荡器（Chande Momentum Oscillator）",
    calculation=Chande_Momentum_Oscillator,
    dependencies=['close'],
    parameters={'period': 19}
))

def Chaikin_Oscillator(df):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    volume = get_column(df, 'volume')
    
    
    mfm = ((close - low) - (high - close)) / (high - low).replace(0, 1e-5)
    mfv = mfm * volume
    adl = mfv.cumsum()
    
    
    ema3 = adl.ewm(span=3, adjust=False).mean()
    ema10 = adl.ewm(span=10, adjust=False).mean()
    return ema3 - ema10

factor_manager.register_factor(Factor(
    name="Chaikin_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="Chaikin振荡器（Chaikin Oscillator）",
    calculation=Chaikin_Oscillator,
    dependencies=['high', 'low', 'close', 'volume'],
    parameters={}
))


def Chandelier_Exit(df, atr_period=22, mult=3):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    
    h22 = high.rolling(window=atr_period).max()
    l22 = low.rolling(window=atr_period).min()
    
    
    tr = pd.DataFrame({
        'hl': high - low,
        'hc': (high - get_column(df, 'close').shift(1)).abs(),
        'lc': (low - get_column(df, 'close').shift(1)).abs()
    }).max(axis=1)
    
    
    atr = tr.ewm(alpha=1/atr_period, adjust=False).mean()
    
    
    long_exit = h22 - atr * mult
    short_exit = l22 + atr * mult
    
    return long_exit, short_exit

def Chandelier_Exit_adapter(df, atr_period=22, mult=3):
    
    long_exit, _ = Chandelier_Exit(df, atr_period, mult)
    return long_exit

def Chandelier_Exit_short_adapter(df, atr_period=22, mult=3):
    
    _, short_exit = Chandelier_Exit(df, atr_period, mult)
    return short_exit

factor_manager.register_factor(Factor(
    name="Chandelier_Exit",
    category=FactorCategory.TECHNICAL,
    description="Chandelier离场指标（Chandelier Exit）- 长线离场值",
    calculation=Chandelier_Exit_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'atr_period': 22, 'mult': 3}
))

factor_manager.register_factor(Factor(
    name="Chandelier_Exit_Short",
    category=FactorCategory.TECHNICAL,
    description="Chandelier离场指标（Chandelier Exit）- 短线离场值",
    calculation=Chandelier_Exit_short_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'atr_period': 22, 'mult': 3}
))

def Donchian_Channels(df, period=20):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    upper = high.rolling(window=period).max()
    lower = low.rolling(window=period).min()
    middle = (upper + lower) / 2
    return upper, middle, lower

def Donchian_Channels_upper_adapter(df, period=20):
    upper, _, _ = Donchian_Channels(df, period)
    return upper

def Donchian_Channels_middle_adapter(df, period=20):
    _, middle, _ = Donchian_Channels(df, period)
    return middle

def Donchian_Channels_lower_adapter(df, period=20):
    _, _, lower = Donchian_Channels(df, period)
    return lower

factor_manager.register_factor(Factor(
    name="Donchian_Channels_Upper",
    category=FactorCategory.TECHNICAL,
    description="唐奇安通道上轨（Donchian Channels Upper）",
    calculation=Donchian_Channels_upper_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

factor_manager.register_factor(Factor(
    name="Donchian_Channels_Middle",
    category=FactorCategory.TECHNICAL,
    description="唐奇安通道中轨（Donchian Channels Middle）",
    calculation=Donchian_Channels_middle_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

factor_manager.register_factor(Factor(
    name="Donchian_Channels_Lower",
    category=FactorCategory.TECHNICAL,
    description="唐奇安通道下轨（Donchian Channels Lower）",
    calculation=Donchian_Channels_lower_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

def Center_of_Gravity_Oscillator(df, r=0.6):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    
    M_t = (high + low) / 2
    M_t_1 = M_t.shift(1)  
    
    
    numerator = -(M_t + r * M_t_1)
    denominator = (M_t + M_t_1).replace(0, 1e-5)  
    cog = numerator / denominator
    
    return cog

factor_manager.register_factor(Factor(
    name="COG",
    category=FactorCategory.TECHNICAL,
    description="重心振荡器（Center of Gravity Oscillator）",
    calculation=Center_of_Gravity_Oscillator,
    dependencies=['high', 'low'],
    parameters={'r': 0.6}
))


def DEMA(df, period=20):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    M_t = (high + low) / 2  
    ema1 = M_t.ewm(span=period, adjust=False).mean()
    ema2 = ema1.ewm(span=period, adjust=False).mean()
    return 2 * ema1 - ema2

factor_manager.register_factor(Factor(
    name="DEMA",
    category=FactorCategory.TECHNICAL,
    description="双指数移动平均（Double Exponential Moving Average）",
    calculation=DEMA,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))


def DPO(df, period=10):
    
    high = get_column(df, 'high')
    close = get_column(df, 'close')
    
    H_high = high.rolling(window=period).max()
    SMA_close = close.rolling(window=period).mean()
    return (H_high / (period + 2)) - SMA_close

factor_manager.register_factor(Factor(
    name="DPO",
    category=FactorCategory.TECHNICAL,
    description="去趋势价格振荡器（Detrended Price Oscillator）",
    calculation=DPO,
    dependencies=['high', 'close'],
    parameters={'period': 10}
))

def Heikin_Ashi(df):
    
    open_p = get_column(df, 'open')
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    ha_close = (open_p + high + low + close) / 4
    ha_open = (open_p.shift(1) + close.shift(1)) / 2
    ha_open.iloc[0] = open_p.iloc[0]  
    
    ha_high = pd.concat([high, ha_open, ha_close], axis=1).max(axis=1)
    ha_low = pd.concat([low, ha_open, ha_close], axis=1).min(axis=1)
    
    return pd.DataFrame({
        'HA_open': ha_open,
        'HA_high': ha_high,
        'HA_low': ha_low,
        'HA_close': ha_close
    })

factor_manager.register_factor(Factor(
    name="Heikin_Ashi",
    category=FactorCategory.TECHNICAL,
    description="平均K线（Heikin-Ashi Candles）",
    calculation=Heikin_Ashi,
    dependencies=['open', 'high', 'low', 'close'],
    parameters={}
))

def Highest_High_Lowest_Low(df, period=20):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    return (
        high.rolling(window=period).max(),
        low.rolling(window=period).min()
    )

def Highest_High_adapter(df, period=20):
    highest, _ = Highest_High_Lowest_Low(df, period)
    return highest

def Lowest_Low_adapter(df, period=20):
    _, lowest = Highest_High_Lowest_Low(df, period)
    return lowest

factor_manager.register_factor(Factor(
    name="Highest_High",
    category=FactorCategory.TECHNICAL,
    description="最高高价（Highest High）",
    calculation=Highest_High_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

factor_manager.register_factor(Factor(
    name="Lowest_Low",
    category=FactorCategory.TECHNICAL,
    description="最低低价（Lowest Low）",
    calculation=Lowest_Low_adapter,
    dependencies=['high', 'low'],
    parameters={'period': 20}
))

def Hull_MA(df, period=10):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    AHL = (high + low) / 2  
    wma_half_period = WMA(AHL, period//2)
    wma_full = WMA(AHL, period)
    wma_half = WMA(2 * wma_half_period - wma_full, int(np.sqrt(period)))
    
    return wma_half

factor_manager.register_factor(Factor(
    name="HMA",
    category=FactorCategory.TECHNICAL,
    description="赫尔移动平均线（Hull Moving Average）",
    calculation=Hull_MA,
    dependencies=['high', 'low'],
    parameters={'period': 10}
))

def IBS(df):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    return (close - low) / (high - low).replace(0, 1e-5)

factor_manager.register_factor(Factor(
    name="IBS",
    category=FactorCategory.TECHNICAL,
    description="内部柱强度（Internal Bar Strength）",
    calculation=IBS,
    dependencies=['high', 'low', 'close'],
    parameters={}
))

def Keltner_Channels(df, ema_period=20, atr_period=10, multiplier=2):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    
    middle = close.ewm(span=ema_period, adjust=False).mean()
    
    
    atr = ATR(df, atr_period)
    
    upper = middle + multiplier * atr
    lower = middle - multiplier * atr
    return upper, middle, lower

def Keltner_Channels_upper_adapter(df, ema_period=20, atr_period=10, multiplier=2):
    upper, _, _ = Keltner_Channels(df, ema_period, atr_period, multiplier)
    return upper

def Keltner_Channels_middle_adapter(df, ema_period=20, atr_period=10, multiplier=2):
    _, middle, _ = Keltner_Channels(df, ema_period, atr_period, multiplier)
    return middle

def Keltner_Channels_lower_adapter(df, ema_period=20, atr_period=10, multiplier=2):
    _, _, lower = Keltner_Channels(df, ema_period, atr_period, multiplier)
    return lower

factor_manager.register_factor(Factor(
    name="Keltner_Channels_Upper",
    category=FactorCategory.TECHNICAL,
    description="凯尔特纳通道上轨（Keltner Channels Upper）",
    calculation=Keltner_Channels_upper_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'ema_period': 20, 'atr_period': 10, 'multiplier': 2}
))

factor_manager.register_factor(Factor(
    name="Keltner_Channels_Middle",
    category=FactorCategory.TECHNICAL,
    description="凯尔特纳通道中轨（Keltner Channels Middle）",
    calculation=Keltner_Channels_middle_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'ema_period': 20, 'atr_period': 10, 'multiplier': 2}
))

factor_manager.register_factor(Factor(
    name="Keltner_Channels_Lower",
    category=FactorCategory.TECHNICAL,
    description="凯尔特纳通道下轨（Keltner Channels Lower）",
    calculation=Keltner_Channels_lower_adapter,
    dependencies=['high', 'low', 'close'],
    parameters={'ema_period': 20, 'atr_period': 10, 'multiplier': 2}
))


def MACD_Oscillator(df, fast_period=12, slow_period=26):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    ahl = (high + low) / 2  
    macd = ahl.ewm(span=fast_period, adjust=False).mean() - ahl.ewm(span=slow_period, adjust=False).mean()
    return macd

factor_manager.register_factor(Factor(
    name="MACD_Oscillator",
    category=FactorCategory.TECHNICAL,
    description="MACD振荡器（基于高低均价）",
    calculation=MACD_Oscillator,
    dependencies=['high', 'low'],
    parameters={'fast_period': 12, 'slow_period': 26}
))

def Median_Price(df):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    return (high + low) / 2

factor_manager.register_factor(Factor(
    name="Median_Price",
    category=FactorCategory.TECHNICAL,
    description="中间价（Median Price）",
    calculation=Median_Price,
    dependencies=['high', 'low'],
    parameters={}
))


def Momentum(df, period=1):
    
    close = get_column(df, 'close')
    
    return close.diff(periods=period)

factor_manager.register_factor(Factor(
    name="Momentum",
    category=FactorCategory.TECHNICAL,
    description="动量指标（Momentum）",
    calculation=Momentum,
    dependencies=['close'],
    parameters={'period': 1}
))

def Variable_MA(df, period=3):
    
    close = get_column(df, 'close')
    
    
    direction = abs(close - close.shift(period))
    volatility = abs(close.diff()).rolling(window=period).sum()
    er = direction / volatility.replace(0, 1e-5)
    
    
    alpha = 2 / (period + 1)
    weights = alpha * er
    
    
    vma = pd.Series(index=close.index, dtype=float)
    for i in range(period, len(close)):
        window = close.iloc[i-period:i]
        window_weights = weights.iloc[i-period:i]
        vma.iloc[i] = np.sum(window_weights * window) / np.sum(window_weights)
    
    return vma

factor_manager.register_factor(Factor(
    name="VMA",
    category=FactorCategory.TECHNICAL,
    description="可变移动平均线（Variable Moving Average）",
    calculation=Variable_MA,
    dependencies=['close'],
    parameters={'period': 3}
))

def NATR(df, atr_period=14):
    
    close = get_column(df, 'close')
    atr = ATR(df, atr_period)
    
    return (atr / close) * 100

factor_manager.register_factor(Factor(
    name="NATR",
    category=FactorCategory.TECHNICAL,
    description="标准化平均真实波幅（Normalized ATR）",
    calculation=NATR,
    dependencies=['high', 'low', 'close'],
    parameters={'atr_period': 14}
))


def PPO(df, fast_period=12, slow_period=26):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    ahl = (high + low) / 2
    macd = ahl.ewm(span=fast_period, adjust=False).mean() - ahl.ewm(span=slow_period, adjust=False).mean()
    slow_ema = ahl.ewm(span=slow_period, adjust=False).mean()
    return (macd / slow_ema.replace(0, 1e-5)) * 100

factor_manager.register_factor(Factor(
    name="PPO",
    category=FactorCategory.TECHNICAL,
    description="百分比价格振荡器（Percentage Price Oscillator）",
    calculation=PPO,
    dependencies=['high', 'low'],
    parameters={'fast_period': 12, 'slow_period': 26}
))

def ROC(df, period=12):  
    
    close = get_column(df, 'close')
    
    return ((close - close.shift(period)) / close.shift(period).replace(0, 1e-5)) * 100

factor_manager.register_factor(Factor(
    name="ROC",
    category=FactorCategory.TECHNICAL,
    description="变动率指标（Rate of Change）",
    calculation=ROC,
    dependencies=['close'],
    parameters={'period': 12}
))

def RSI(df, period=14):
    
    close = get_column(df, 'close')
    
    delta = close.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss.replace(0, 1e-5)
    return 100 - (100 / (1 + rs))

factor_manager.register_factor(Factor(
    name="RSI",
    category=FactorCategory.TECHNICAL,
    description="相对强弱指数（Relative Strength Index）",
    calculation=RSI,
    dependencies=['close'],
    parameters={'period': 14}
))

def Stochastic_RSI(df, rsi_period=14, stoch_period=10):
    
    rsi_values = RSI(df, rsi_period)
    min_rsi = rsi_values.rolling(window=stoch_period).min()
    max_rsi = rsi_values.rolling(window=stoch_period).max()
    return (rsi_values - min_rsi) / (max_rsi - min_rsi).replace(0, 1e-5)

factor_manager.register_factor(Factor(
    name="Stochastic_RSI",
    category=FactorCategory.TECHNICAL,
    description="随机相对强弱指数（Stochastic RSI）",
    calculation=Stochastic_RSI,
    dependencies=['close'],
    parameters={'rsi_period': 14, 'stoch_period': 10}
))

def Rising_Falling_SAR(df, af_start=0.02, af_step=0.02, af_max=0.2):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    
    rising_sar = pd.Series(index=high.index, dtype=float)
    falling_sar = pd.Series(index=high.index, dtype=float)
    
    
    rising_sar.iloc[0] = low.iloc[0]
    falling_sar.iloc[0] = high.iloc[0]
    
    
    rising_ep = high.iloc[0]  
    rising_af = af_start      
    
    
    falling_ep = low.iloc[0]  
    falling_af = af_start
    
    for i in range(1, len(high)):
        
        rising_sar.iloc[i] = rising_sar.iloc[i-1] + rising_af * (rising_ep - rising_sar.iloc[i-1])
        
        if high.iloc[i] > rising_ep:
            rising_ep = high.iloc[i]
            rising_af = min(rising_af + af_step, af_max)
        
        rising_sar.iloc[i] = min(rising_sar.iloc[i], low.iloc[i-1], low.iloc[i])
        
        
        falling_sar.iloc[i] = falling_sar.iloc[i-1] - falling_af * (falling_sar.iloc[i-1] - falling_ep)
        
        if low.iloc[i] < falling_ep:
            falling_ep = low.iloc[i]
            falling_af = min(falling_af + af_step, af_max)
        
        falling_sar.iloc[i] = max(falling_sar.iloc[i], high.iloc[i-1], high.iloc[i])
    
    return rising_sar, falling_sar

def Rising_SAR_adapter(df, af_start=0.02, af_step=0.02, af_max=0.2):
    rising_sar, _ = Rising_Falling_SAR(df, af_start, af_step, af_max)
    return rising_sar

def Falling_SAR_adapter(df, af_start=0.02, af_step=0.02, af_max=0.2):
    _, falling_sar = Rising_Falling_SAR(df, af_start, af_step, af_max)
    return falling_sar

factor_manager.register_factor(Factor(
    name="Rising_SAR",
    category=FactorCategory.TECHNICAL,
    description="上升抛物线转向指标（Rising Parabolic SAR）",
    calculation=Rising_SAR_adapter,
    dependencies=['high', 'low'],
    parameters={'af_start': 0.02, 'af_step': 0.02, 'af_max': 0.2}
))

factor_manager.register_factor(Factor(
    name="Falling_SAR",
    category=FactorCategory.TECHNICAL,
    description="下降抛物线转向指标（Falling Parabolic SAR）",
    calculation=Falling_SAR_adapter,
    dependencies=['high', 'low'],
    parameters={'af_start': 0.02, 'af_step': 0.02, 'af_max': 0.2}
))

def Standard_Deviation(df, period=10):
    
    close = get_column(df, 'close')
    
    sma = close.rolling(window=period).mean()
    deviation = close - sma
    squared_deviation = deviation.pow(2)
    std = np.sqrt(squared_deviation.rolling(window=period).mean())
    return deviation, std

def Deviation_adapter(df, period=10):
    deviation, _ = Standard_Deviation(df, period)
    return deviation

def Std_adapter(df, period=10):
    _, std = Standard_Deviation(df, period)
    return std

factor_manager.register_factor(Factor(
    name="Deviation",
    category=FactorCategory.TECHNICAL,
    description="价格偏差（Price Deviation）",
    calculation=Deviation_adapter,
    dependencies=['close'],
    parameters={'period': 10}
))

factor_manager.register_factor(Factor(
    name="Standard_Deviation",
    category=FactorCategory.TECHNICAL,
    description="标准偏差（Standard Deviation）",
    calculation=Std_adapter,
    dependencies=['close'],
    parameters={'period': 10}
))

def Fractals(df):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    
    
    buy_fractals = pd.Series(False, index=high.index)
    sell_fractals = pd.Series(False, index=low.index)
    
    
    high_shifted = pd.DataFrame({
        'i-2': high.shift(2),
        'i-1': high.shift(1),
        'i': high,
        'i+1': high.shift(-1),
        'i+2': high.shift(-2)
    })
    
    low_shifted = pd.DataFrame({
        'i-2': low.shift(2),
        'i-1': low.shift(1),
        'i': low,
        'i+1': low.shift(-1),
        'i+2': low.shift(-2)
    })
    
    
    buy_fractals = (high_shifted['i'] > high_shifted['i-2']) & \
                   (high_shifted['i'] > high_shifted['i-1']) & \
                   (high_shifted['i'] > high_shifted['i+1']) & \
                   (high_shifted['i'] > high_shifted['i+2'])
    
    
    sell_fractals = (low_shifted['i'] < low_shifted['i-2']) & \
                    (low_shifted['i'] < low_shifted['i-1']) & \
                    (low_shifted['i'] < low_shifted['i+1']) & \
                    (low_shifted['i'] < low_shifted['i+2'])
                    
    
    buy_fractals = buy_fractals.astype(float)
    sell_fractals = sell_fractals.astype(float)
    
    
    return buy_fractals, sell_fractals

def Fractals_buy_adapter(df):
    
    buy_fractals, _ = Fractals(df)
    return buy_fractals.astype(float)

def Fractals_sell_adapter(df):
    
    _, sell_fractals = Fractals(df)
    return sell_fractals.astype(float)

factor_manager.register_factor(Factor(
    name="Fractals",
    category=FactorCategory.TECHNICAL,
    description="分形指标（Fractals）- 买分形",
    calculation=Fractals_buy_adapter,
    dependencies=['high', 'low'],
    parameters={}
))

factor_manager.register_factor(Factor(
    name="Fractals_sell",
    category=FactorCategory.TECHNICAL,
    description="分形指标（Fractals）- 卖分形",
    calculation=Fractals_sell_adapter,
    dependencies=['high', 'low'],
    parameters={}
))

def Linear_Regression_Line(df, period=10):
    
    close = get_column(df, 'close')
    
    
    x = np.arange(period)
    x_mean = (period - 1) / 2  
    x_minus_mean = x - x_mean
    x_minus_mean_squared_sum = np.sum(x_minus_mean ** 2)  
    
    
    y_rolling_sum = np.convolve(close, np.ones(period), mode='valid')
    y_rolling_mean = y_rolling_sum / period
    
    
    result = pd.Series(index=close.index, dtype=float)
    valid_indices = close.index[period-1:]
    
    if len(valid_indices) > 0:
        
        y_windows = np.array([close.iloc[i-(period-1):i+1].values for i in range(period-1, len(close))])
        y_means = y_rolling_mean
        
        
        y_minus_mean = y_windows - y_means[:, np.newaxis]
        covs = np.sum(x_minus_mean * y_minus_mean, axis=1)
        
        
        slopes = np.divide(covs, x_minus_mean_squared_sum, out=np.zeros_like(covs), where=x_minus_mean_squared_sum!=0)
        intercepts = y_means - slopes * x_mean
        
        
        predictions = intercepts + slopes * (period - 1)
        
        
        result.loc[valid_indices] = predictions
    
    return result

factor_manager.register_factor(Factor(
    name="Linear_Regression_Line",
    category=FactorCategory.TECHNICAL,
    description="线性回归线（Linear Regression Line）",
    calculation=Linear_Regression_Line,
    dependencies=['close'],
    parameters={'period': 10}
))

def Rational_Transfer_Filter(df, numerator_coeffs=[1.0], denominator_coeffs=[1.0], lookback=10):
    
    from scipy.signal import lfilter
    close = get_column(df, 'close')
    output = pd.Series(index=close.index, dtype=float)
    
    for i in range(lookback, len(close)):
        window = close.iloc[i-lookback:i]
        
        filtered = lfilter(numerator_coeffs, denominator_coeffs, window)
        output.iloc[i] = filtered[-1]  
    
    return output

factor_manager.register_factor(Factor(
    name="Rational_Transfer_Filter",
    category=FactorCategory.TECHNICAL,
    description="有理传递函数滤波器（Rational Transfer Function）",
    calculation=Rational_Transfer_Filter,
    dependencies=['close'],
    parameters={'numerator_coeffs': [1.0], 'denominator_coeffs': [1.0], 'lookback': 10}
))

def Savitzky_Golay_Filter(df, window_length=5, poly_order=2, weights=None):
    
    series = get_column(df, 'close')
    
    if window_length % 2 == 0:
        raise ValueError("窗口长度必须是奇数")
    if poly_order >= window_length:
        raise ValueError("多项式阶数必须小于窗口长度")
    
    half_window = (window_length - 1) // 2
    n = poly_order
    filtered = pd.Series(index=series.index, dtype=float)
    
    
    if weights is None:
        weights = np.ones(window_length)
    else:
        weights = np.array(weights)
    
    
    for i in range(half_window, len(series)-half_window):
        
        window_indices = np.arange(i-half_window, i+half_window+1)
        y = series.iloc[window_indices].values
        x = np.arange(-half_window, half_window+1)
        
        
        A = np.zeros((n+1, n+1))
        B = np.zeros(n+1)
        
        
        for k in range(n+1):
            for r in range(n+1):
                A[k, r] = np.sum(weights * x**(k + r))
            B[k] = np.sum(weights * y * x**k)
        
        
        try:
            P = inv(A) @ B  
        except np.linalg.LinAlgError:
            P = np.linalg.pinv(A) @ B  
        
        
        filtered.iloc[i] = np.sum(P * x**0)  
    
    
    filtered.iloc[:half_window] = filtered.iloc[half_window]
    filtered.iloc[-half_window:] = filtered.iloc[-half_window-1]
    return filtered

factor_manager.register_factor(Factor(
    name="Savitzky_Golay_Filter",
    category=FactorCategory.TECHNICAL,
    description="Savitzky-Golay数字滤波器（Savitzky-Golay Filter）",
    calculation=Savitzky_Golay_Filter,
    dependencies=['close'],
    parameters={'window_length': 5, 'poly_order': 2, 'weights': None}
))

def Zero_Phase_Filter(df, window_length=5, polyorder=2):
    
    series = get_column(df, 'close')
    
    
    if window_length % 2 == 0:
        raise ValueError("窗口长度必须是奇数")
    if polyorder >= window_length:
        raise ValueError("多项式阶数必须小于窗口长度")
    
    
    y = series.values.astype(float)
    
    
    b = savgol_filter(np.eye(window_length), window_length, polyorder, axis=0)[window_length//2]
    
    
    filtered = filtfilt(
        b,                
        [1.0],           
        y,               
        padtype='odd',   
        padlen=window_length-1  
    )
    
    return pd.Series(filtered, index=series.index)

factor_manager.register_factor(Factor(
    name="Zero_Phase_Filter",
    category=FactorCategory.TECHNICAL,
    description="零相位滤波器（Zero Phase Filter）",
    calculation=Zero_Phase_Filter,
    dependencies=['close'],
    parameters={'window_length': 5, 'polyorder': 2}
))

def Remove_Offset(df, period=14):
    
    close = get_column(df, 'close')
    offset = close - close.rolling(window=period).mean()
    return offset

factor_manager.register_factor(Factor(
    name="Remove_Offset",
    category=FactorCategory.TECHNICAL,
    description="去除偏移（Remove Offset）",
    calculation=Remove_Offset,
    dependencies=['close'],
    parameters={'period': 14}
))

def Detrend_Least_Squares(df, period=10):
    
    close = get_column(df, 'close')
    def linear_detrend(window):
        x = np.arange(len(window))  
        y = window.values
        X = np.vstack([x, np.ones(len(x))]).T
        a, b = np.linalg.lstsq(X, y, rcond=None)[0]
        return y[-1] - (a * x[-1] + b)  
    
    return close.rolling(window=period).apply(linear_detrend, raw=False)

factor_manager.register_factor(Factor(
    name="Detrend_Least_Squares",
    category=FactorCategory.TECHNICAL,
    description="最小二乘去趋势（Linear Detrending）",
    calculation=Detrend_Least_Squares,
    dependencies=['close'],
    parameters={'period': 10}
))

def Beta_Calculation(df, period=10):
    
    close = get_column(df, 'close')
    
    index_cl = close.pct_change() + 1
    index_av = close.rolling(window=2).mean().pct_change() + 1
    
    
    dev_cl = index_cl - index_cl.rolling(window=period).mean()
    dev_av = index_av - index_av.rolling(window=period).mean()
    
    
    cov = dev_cl.rolling(window=period).cov(dev_av)
    var = dev_av.rolling(window=period).var()
    
    return cov / var.replace(0, 1e-5)

factor_manager.register_factor(Factor(
    name="Beta_Calculation",
    category=FactorCategory.TECHNICAL,
    description="Beta类似指标（Beta Calculation）",
    calculation=Beta_Calculation,
    dependencies=['close'],
    parameters={'period': 10}
))

def Support_Resistance(df, period=20, threshold=0.05):
    
    high = get_column(df, 'high')
    low = get_column(df, 'low')
    close = get_column(df, 'close')
    
    
    sr_score = pd.Series(0.0, index=close.index)
    
    
    high_prev = high.shift(1)
    low_prev = low.shift(1)
    high_points = high > high_prev
    low_points = low < low_prev
    
    
    
    prices = close.values
    high_values = high.values
    low_values = low.values
    
    
    for i in range(period, len(close)):
        current_price = prices[i]
        
        
        window_start = max(0, i-period)
        window_high = high_values[window_start:i]
        window_low = low_values[window_start:i]
        window_high_points = high_points.iloc[window_start:i].values
        window_low_points = low_points.iloc[window_start:i].values
        
        
        valid_highs = window_high[window_high_points]
        valid_lows = window_low[window_low_points]
        
        
        if len(valid_highs) == 0 and len(valid_lows) == 0:
            continue
            
        
        min_high_dist = float('inf')
        if len(valid_highs) > 0:
            high_dists = np.abs((valid_highs - current_price) / current_price)
            near_highs = high_dists[high_dists < threshold]
            if len(near_highs) > 0:
                min_high_dist = np.min(near_highs)
        
        min_low_dist = float('inf')
        if len(valid_lows) > 0:
            low_dists = np.abs((valid_lows - current_price) / current_price)
            near_lows = low_dists[low_dists < threshold]
            if len(near_lows) > 0:
                min_low_dist = np.min(near_lows)
        
        
        if min_high_dist < min_low_dist:
            sr_score.iloc[i] = min_high_dist * 100  
        elif min_low_dist < float('inf'):
            sr_score.iloc[i] = -min_low_dist * 100  
    
    return sr_score

factor_manager.register_factor(Factor(
    name="Support_Resistance",
    category=FactorCategory.TECHNICAL,
    description="支撑阻力指标（Support Resistance）",
    calculation=Support_Resistance,
    dependencies=['high', 'low', 'close'],
    parameters={'period': 20, 'threshold': 0.05}
))