# -*- coding: utf-8 -*-
import re
import pandas as pd
import datetime
import numpy as np
from datetime import time as dtime
import codecs
import csv
import os

from cqtrade.strategy.template import BaseStrategy, EXCHANGE_NAME_TO_ENUM
from cqtrade.trade.model import SendOrderData, OrderData, SendOrderResponse
from cqtrade.trade.constant import OrderDirection


class QueueStrategy8(BaseStrategy):
    """"""

    #author = "ChrisZ"

    #策略账号(每天会改变，需要手动修改)
    ice_algoid = 1206130048
    vt_symbol = 'i2209.DCE'
    lots = 1
    hold = 800
    max_QT = 300  #单边最大交易量s
    skew_pos = 5
    ioc_price = 1
    ioc_flag = False
    ioc_type = 'on_trade'
    active_mode = True
    upper = 1.5
    lower = 0.9
    minsize = 10
    active_pos = 0
    ontrade_cancel_flag = True
    ontrade_cancel_layer = 6
    Grid_Start = 2  #从四档开始铺单
    Grid_Interval = 2  #每2个tick挂一个单
    Grid_Layer = 3  #铺几层单
    net = 0
    alone_order = 2
    position_long = 0
    position_short = 0
    active_limit_orders = {}  #系统返回发单成功的orders
    send_orders = []  #自身维护的orders
    max_cancel_count = 400
    cancel_count = 0
    QT_buy = 0
    QT_sell = 0
    totalPnl = 0
    hedge_mode = True
    hedge_nb = 3
    hedge_spread = 1
    cancel_layer = 3

    parameters = [
        'vt_symbol',
        'lots',
        'upper',
        'lower',
        'minsize',
        'skew_pos',
        'ioc_flag',
        'ioc_price',
        'Grid_Start',
        'Grid_Interval',
        'Grid_Layer',
        'cancel_layer',
        'ontrade_cancel_flag',
        'ontrade_cancel_layer',
        'active_mode',
        'active_pos',
        'hedge_mode',  #如果勾选，超过skew_pos对价对冲
        'hedge_nb',  #对价对冲手数
        'hedge_spread',  #对冲宽度保护
        'alone_order',
        'position_long',
        'position_short',
        'hold',  #最大单边持仓
        'max_QT',  #最大交易量
        'ice_algoid',
        'max_cancel_count'
    ]

    variables = [
        'net',
        'cancel_count',
        'QT_buy',
        'QT_sell',
        'totalPnl'
    ]

    def process_tick(self, tick):  #处理非五档行情的tick数据
        ts = self.pricetick

        if len(tick.bid_prices) != 5 or tick.bid_prices[1] == 0:  #非五档行情
            bid_price_1 = tick.bid_prices[0]
            bid_price_2 = tick.bid_prices[0] - ts
            bid_price_3 = tick.bid_prices[0] - 2 * ts
            bid_price_4 = tick.bid_prices[0] - 3 * ts
            bid_price_5 = tick.bid_prices[0] - 4 * ts
            ask_price_1 = tick.ask_prices[0]
            ask_price_2 = tick.ask_prices[0] + ts
            ask_price_3 = tick.ask_prices[0] + 2 * ts
            ask_price_4 = tick.ask_prices[0] + 3 * ts
            ask_price_5 = tick.ask_prices[0] + 4 * ts
            tick.bid_prices = [bid_price_1, bid_price_2, bid_price_3, bid_price_4, bid_price_5]
            tick.ask_prices = [ask_price_1, ask_price_2, ask_price_3, ask_price_4, ask_price_5]

        tick.bid_price_1, tick.bid_price_2, tick.bid_price_3, tick.bid_price_4, tick.bid_price_5 = tick.bid_prices
        tick.ask_price_1, tick.ask_price_2, tick.ask_price_3, tick.ask_price_4, tick.ask_price_5 = tick.ask_prices
        tick.bid_volume_1, tick.bid_volume_2, tick.bid_volume_3, tick.bid_volume_4, tick.bid_volume_5 = tick.bid_volumes
        tick.ask_volume_1, tick.ask_volume_2, tick.ask_volume_3, tick.ask_volume_4, tick.ask_volume_5 = tick.ask_volumes
        tick.last_price = tick.last
        return tick

    def sendorder(self, ice_algoid, order, comments):
        self.send_order(ice_algoid, order, comments)
        self.send_orders.append(order)  #本地订单维护
        # self.log_info(f"发出下单指令，方向:{order.direction.value}, 价格:{order.price},数量:{order.quantity},发单时间：{datetime.datetime.now()}")

    def buy(self, vt_symbol, price, qty, comments):  #买
        symbol, exchange = vt_symbol.split('.')
        exchange = EXCHANGE_NAME_TO_ENUM[exchange]
        order = SendOrderData(
            symbol=symbol,
            exchange=exchange,
            direction=OrderDirection.BUY_OPEN if (
                        self.position_short <= self.hold or self.short_available <= self.position_cushion) else OrderDirection.BUY_CLOSE,
            quantity=qty,
            price=price
        )
        if self.QT_buy <= self.max_QT:
            self.sendorder(self.ice_algoid, order, comments)
        else:
            self.log_info('买成交量超限！')

    def short(self, vt_symbol, price, qty, comments):  #卖
        symbol, exchange = vt_symbol.split('.')
        exchange = EXCHANGE_NAME_TO_ENUM[exchange]
        order = SendOrderData(
            symbol=symbol,
            exchange=exchange,
            direction=OrderDirection.SELL_OPEN if (
                        self.position_long <= self.hold or self.long_available <= self.position_cushion) else OrderDirection.SELL_CLOSE,
            quantity=qty,
            price=price
        )
        if self.QT_sell <= self.max_QT:
            self.sendorder(self.ice_algoid, order, comments)
        else:
            self.log_info('卖成交量超限！')

    def cancel_order(self, orderid):
        self.cancel_one_order_id(self.ice_algoid, orderid)
        self.pre_cancel_list.append([str(orderid), datetime.datetime.now()])
        # order = self.active_limit_orders[orderid]
        # order_price = order.price
        # order_direction = order.direction
        # for _order in self.send_orders: #一旦撤单，则本地维护的同价位同方向的单撤掉
        #     if _order.price == order_price and _order.direction == order_direction:
        #         self.send_orders.remove(_order)
        #         break
        # self.log_info(f"发出撤单指令，方向:{order.direction.value}, 价格:{order.price},数量:{order.quantity},信息：{orderid},撤单时间：{datetime.datetime.now()}")

    def cancel_signal(self, signal):  #暂时先不要用
        self.cancel_one_signal_id(self.ice_algoid, signal)

    def cancel_all(self):
        orderids = list(self.active_limit_orders.keys())
        for orderid in orderids:
            self.cancel_order(orderid)

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.subscribe_tick(self.vt_symbol)
        df = pd.read_csv('Y:/YSP-NFS/cscquant-pythonplugin/cscquant/zhanghongchi/.trader/config.csv', index_col=0,
                         error_bad_lines=False)
        config = df.to_dict()
        symbol, exchange = self.vt_symbol.split('.')
        contract = symbol[:re.search(r'\d', symbol).start()].upper()
        exchange = exchange.upper()
        if exchange == 'ZCE':
            exchange = 'CZC'
        if exchange == 'SHFE':
            exchange = 'SHF'
        Ticker = contract + '.' + exchange
        self.pricetick = config['pricetick'][Ticker]
        self.size = config['size'][Ticker]
        self.log_info('策略初始化')

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.log_info("策略启动")
        self.mCount = 0
        self.fairPrice = 0
        self.trade_price = 0
        self.last_net = 0
        self.last_tick = None
        self.trade_price = 0
        self.buy_price = 0
        self.short_price = 0
        self.MMTrading = False
        self.buy_price = 0
        self.short_price = 0
        self.last_cancel_count = 0
        self.net = self.position_long - self.position_short
        self.end_flag = False
        self.long_available = self.position_long  # 多头可用
        self.short_available = self.position_short  #空头可用
        self.position_cushion = 20  #安全垫
        self.subscribe_tick(self.vt_symbol)
        self.net = self.position_long - self.position_short
        self.log_info(f"初始持仓信息：多头{self.position_long},空头{self.position_short}")
        self.pre_quote_list = []
        self.quote_list = []
        self.pre_cancel_list = []
        self.cancel_list = []
        self.through_delay = 0
        self.update_data()

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.log_info("策略停止！")

    def on_pause(self):
        self.log_info("策略暂停，撤单！")
        self.cancel_all()
        self.cal_delay()

    def on_end(self, tick):  #收盘清仓,只发一次对价清仓指令
        dt = datetime.datetime.now()
        if ((dtime(22, 55) < dt.time() < dtime(23, 00)) \
                or (dtime(11, 28) < dt.time() < dtime(11, 30)) \
                or (dtime(14, 55) < dt.time() < dtime(15, 00))):
            if not self.end_flag:
                self.log_info('time to clear net')
                self.log_info(f'remaining net:{self.net}')
                self.cancel_all()
                if self.net > 0:
                    self.short(self.vt_symbol, tick.bid_price_1, abs(self.net), 'end')
                if self.net < 0:
                    self.buy(self.vt_symbol, tick.ask_price_1, abs(self.net), 'end')
            self.end_flag = True
        else:
            self.end_flag = False

    def on_time(self):
        dt = datetime.datetime.now()
        if not (
                (dtime(21, 5) < dt.time() < dtime(22, 55))
                or (dtime(9, 5) < dt.time() < dtime(10, 15))
                or (dtime(10, 30) < dt.time() < dtime(11, 25))
                or (dtime(13, 32) < dt.time() < dtime(14, 55))
        ):
            self.MMTrading = False
        else:
            self.MMTrading = True
        return self.MMTrading

    def edge_b(self, tick, last_tick):  #计算量比
        if (tick.bid_volume_1 / tick.ask_volume_1 > self.upper and tick.bid_volume_1 > self.minsize) or (
                tick.ask_price_1 > last_tick.ask_price_1 > tick.bid_price_1):
            edge = 1
        elif tick.bid_volume_1 / tick.ask_volume_1 < self.lower:
            edge = -1
        else:
            edge = 0
        return edge

    def edge_s(self, tick, last_tick):  #计算量比
        if (tick.ask_volume_1 / tick.bid_volume_1 > self.upper and tick.ask_volume_1 > self.minsize) or (
                tick.bid_price_1 < last_tick.bid_price_1 < tick.ask_price_1):
            edge = 1
        elif tick.ask_volume_1 / tick.bid_volume_1 < self.lower:
            edge = -1
        else:
            edge = 0
        return edge

    def getFairPrice(self, lastP, askP, bidP, edgeP):
        fair = lastP
        if askP > 0 and bidP > 0:
            if askP - bidP <= edgeP * self.pricetick:  # 有流动性，中间价
                fair = 0.5 * (askP + bidP)
            else:  # 流动性不足, 不变
                if lastP > askP:
                    fair = askP
                if lastP < bidP:
                    fair = bidP
        return fair

    def getPosPnL(self, pos):
        totalPnl = 0  # tick 数   更改为总盈亏
        price = self.fairPrice
        current_volume_long = pos.today_amount_long_open + pos.today_amount_long_close  #多头交易量
        current_volume_short = pos.today_amount_short_open + pos.today_amount_short_close  #
        current_value_long = pos.today_value_long_open
        current_value_short = pos.today_value_short_open
        if pos.current_amount_long > 0 and current_volume_long > 0:  # 多头持仓手数大于0
            position_long_avg = current_value_long / current_volume_long / self.size
            totalPnl += current_volume_long * (price - position_long_avg) * self.size
        if pos.current_amount_short > 0 and current_volume_short > 0:  # 空头持仓手数
            position_short_avg = current_value_short / current_volume_short / self.size
            totalPnl += current_volume_short * (position_short_avg - price) * self.size
        return totalPnl

    def on_tick(self, tick):
        self.mCount += 1
        start_t = datetime.datetime.now()
        # self.log_info(f"行情推送：{tick}") 
        tick = self.process_tick(tick)
        ts = self.pricetick
        lots = self.lots
        last_tick = self.last_tick
        self.MMTrading = self.on_time()
        orders = list(self.active_limit_orders.items())
        cancel_count = self.cancel_count + len(orders)
        # if self.cancel_count != self.last_cancel_count:
        #     self.log_info(f"当前撤单次数：{self.cancel_count}")
        #     self.log_info(f"市场订单数：{len(orders)}")

        #更新多头和空头可用
        bid_orders = np.sum([order.direction.value == 'BuyClose' for order in self.send_orders])  #记录在途买平单
        ask_orders = np.sum([order.direction.value == 'SellClose' for order in self.send_orders])  #记录在途卖平单
        self.long_available = self.position_long - ask_orders
        self.short_available = self.position_short - bid_orders

        if cancel_count > self.max_cancel_count:
            self.cancel_all()
            self.log_info("撤单次数超限，停止自营！")
            self.cancel_all()
            return

        if self.mCount > 2:

            for vt_orderid, order in orders:
                #skew_pos内反edge则盘口撤单
                if self.skew_pos >= self.net >= - self.skew_pos:
                    if order.price == tick.bid_price_1 and self.edge_b(tick, last_tick) == -1 and self.net >= 0:
                        self.cancel_order(vt_orderid)
                        # print(f"反edge撤单,净持仓：{self.net},订单：{order}")
                    if order.price == tick.ask_price_1 and self.edge_s(tick, last_tick) == -1 and self.net <= 0:
                        self.cancel_order(vt_orderid)
                        #超过skewpos撤单
                if self.net > self.skew_pos:
                    if order.price >= tick.__getattribute__(
                            f'bid_price_{self.cancel_layer}') and order.direction.value in ['BuyOpen', 'BuyClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过skewpos撤单,净持仓：{self.net},订单：{order}")
                if self.net < -self.skew_pos:
                    if order.price <= tick.__getattribute__(
                            f'ask_price_{self.cancel_layer}') and order.direction.value in ['SellOpen', 'SellClose']:
                        self.cancel_order(vt_orderid)
                    # print(f"超过skewpos撤单,净持仓：{self.net},订单：{order}")
                # 增加持仓 edge=0 唯一单撤单
                if order.price == tick.bid_price_1 and self.edge_b(tick,
                                                                   last_tick) == 0 and self.net > 0 and tick.bid_volume_1 <= self.minsize:
                    self.cancel_order(vt_orderid)
                    # print(f"唯一单撤单,净持仓：{self.net},订单：{order}")
                if order.price == tick.ask_price_1 and self.edge_s(tick,
                                                                   last_tick) == 0 and self.net < 0 and tick.ask_volume_1 <= self.minsize:
                    self.cancel_order(vt_orderid)
                    # print(f"唯一单撤单,净持仓：{self.net},订单：{order}")

                #持仓范围内,盘口唯一单撤单
                if self.skew_pos > self.net > - self.skew_pos:
                    if order.price == tick.bid_price_1 and self.net < 0 and tick.bid_volume_1 < self.alone_order:
                        self.cancel_order(vt_orderid)
                    if order.price == tick.ask_price_1 and self.net > 0 and tick.ask_volume_1 < self.alone_order:
                        self.cancel_order(vt_orderid)

            if self.MMTrading:  #规定报价时间才报价
                #先判断是否激进对冲，否则才进入balance
                if self.hedge_mode and tick.ask_price_1 - tick.bid_price_1 <= self.hedge_spread * ts:
                    hedge_size = min(abs(self.net), self.hedge_nb)
                    if self.net > self.skew_pos:
                        self.short(self.vt_symbol, tick.bid_price_1, hedge_size, 'hedge')
                    if self.net < -self.skew_pos:
                        self.buy(self.vt_symbol, tick.ask_price_1, hedge_size, 'hedge')

                else:
                    # 之后判断是否需要balance
                    bid1_orders = np.sum([order.price == tick.bid_price_1 for order in self.send_orders])
                    ask1_orders = np.sum([order.price == tick.ask_price_1 for order in self.send_orders])
                    # bid1_orders = np.sum([(order.price == tick.bid_price_1 and order.direction.value in ['BuyOpen','BuyClose']) for order in self.send_orders])
                    # ask1_orders = np.sum([(order.price == tick.ask_price_1 and order.direction.value in ['SellOpen','SellClose']) for order in self.send_orders])
                    if self.net > self.skew_pos and ask1_orders == 0:
                        self.short(self.vt_symbol, tick.ask_price_1, lots, 'balance')
                    if self.net < -self.skew_pos and bid1_orders == 0:
                        self.buy(self.vt_symbol, tick.bid_price_1, lots, 'balance')

                bid1_orders = np.sum([order.price == tick.bid_price_1 for order in self.send_orders])
                ask1_orders = np.sum([order.price == tick.ask_price_1 for order in self.send_orders])
                quote_prices = [order.price for order in self.send_orders]
                #如果勾选active_mode主动盘口铺单(edge=1)
                if self.active_mode and self.net <= self.skew_pos and self.net >= - self.skew_pos:
                    if self.edge_b(tick, last_tick) == 1 and bid1_orders == 0 and self.net < self.active_pos:
                        self.buy(self.vt_symbol, tick.bid_price_1, lots, 'MM')
                    if self.edge_s(tick, last_tick) == 1 and ask1_orders == 0 and self.net > -self.active_pos:
                        self.short(self.vt_symbol, tick.ask_price_1, lots, 'MM')

                # 挂单
                my_bid_1 = np.floor(
                    (tick.bid_price_1 - (self.Grid_Start - 1) * ts) / self.Grid_Interval / ts) * self.Grid_Interval * ts
                my_ask_1 = np.ceil(
                    (tick.ask_price_1 + (self.Grid_Start - 1) * ts) / self.Grid_Interval / ts) * self.Grid_Interval * ts
                for i in range(self.Grid_Layer):
                    my_bid = my_bid_1 - i * self.Grid_Interval * ts
                    my_ask = my_ask_1 + i * self.Grid_Interval * ts
                    if my_bid not in quote_prices and self.net <= self.skew_pos:
                        self.buy(self.vt_symbol, my_bid, lots, 'MM')
                    if my_ask not in quote_prices and self.net >= -self.skew_pos:
                        self.short(self.vt_symbol, my_ask, lots, 'MM')

        self.on_end(tick)
        self.last_net = self.net
        self.last_tick = tick
        self.last_cancel_count = self.cancel_count
        end_t = datetime.datetime.now()
        self.through_delay += (end_t - start_t).total_seconds()
        # self.cal_delay()

    def on_order(self, order: OrderData):  #订单状态发生变化，则调用一次on_order,不记录首次发单
        current_sid = order.sid.split('.')[-1]  # order.sid形如cq094551636018.AT0708.AuctionTrade
        if current_sid.isdigit():
            return  # 过滤算法单回报
        if order.order_status.value in ['Filled', 'Cancelled', 'Invalid', 'InvalidCancel', 'LocalCancelled', 'Unsend']:
            # if order.error_code !='':
            #     self.log_info(f"订单状态变化，订单状态：{order.order_status.value}, 信息:{order.order_id},错误代码:{order.error_code},错误信息：{order.error_message}")
            orderid = order.order_id
            if orderid in self.active_limit_orders:
                self.active_limit_orders.pop(orderid)  #全成、撤单或发单失败则移除
            for send_order in self.send_orders:
                if send_order.price == order.order_price:
                    self.send_orders.remove(send_order)
        if order.order_status.value in ['Filled', 'PartialFilled']:
            volume_traded = order.filled_qty - order.original_filled_qty
            price_traded = (order.filled_price - order.original_filled_price) / self.size / volume_traded
            self.trade_price = price_traded
            self.log_info(
                f"收到成交回报, 方向:{order.direction.value}, 价格:{price_traded},数量:{volume_traded}, 信息:{order.order_id},成交时间：{datetime.datetime.now()}")
            if order.direction.value in ['BuyOpen', 'BuyClose']:
                self.QT_buy += volume_traded
            if order.direction.value in ['SellOpen', 'SellClose']:
                self.QT_sell += volume_traded
            if order.direction.value in ['BuyOpen']:
                self.position_long += volume_traded
            if order.direction.value in ['SellOpen']:
                self.position_short += volume_traded
            if order.direction.value in ['BuyClose']:
                self.position_short -= volume_traded
            if order.direction.value in ['SellClose']:
                self.position_long -= volume_traded
                # self.log_info(f"买量：{self.QT_buy},卖量：{self.QT_sell}")
            self.net = self.position_long - self.position_short
            lots = self.lots
            #bid1_orders = np.sum([order.price == self.last_tick.bid_price_1 for order in self.send_orders]) 
            #ask1_orders = np.sum([order.price == self.last_tick.ask_price_1 for order in self.send_orders]) 

            #---------------------------------------------on_trade逻辑---------------------------------------------------------------------------------------------------
            active_orders = list(self.active_limit_orders.items())
            #超过持仓风控on_trade撤单
            if self.ontrade_cancel_flag:
                for vt_orderid, active_order in active_orders:
                    #超过skewpos撤单
                    if self.net >= self.skew_pos:
                        if active_order.price >= self.trade_price - self.ontrade_cancel_layer * self.pricetick and active_order.direction.value in [
                            'BuyOpen', 'BuyClose']:
                            self.cancel_order(vt_orderid)
                            self.log_info("OnTrade撤买单")
                    if self.net <= -self.skew_pos:
                        if active_order.price <= self.trade_price + self.ontrade_cancel_layer * self.pricetick and active_order.direction.value in [
                            'SellOpen', 'SellClose']:
                            self.cancel_order(vt_orderid)
                            self.log_info("OnTrade撤卖单")

            if self.ioc_flag:
                #if self.net <= self.skew_pos and self.net >= - self.skew_pos:                
                pos_chg = self.net - self.last_net
                if pos_chg > 0 and self.net > 0 and self.trade_price != 0:
                    ioc_sellprice = self.trade_price + self.ioc_price * self.pricetick
                    nb_sell_orders = np.sum([order.price == ioc_sellprice for order in self.send_orders])
                    if nb_sell_orders == 0:
                        self.short(self.vt_symbol, ioc_sellprice, volume_traded, 'reverse')
                if pos_chg < 0 and self.net < 0 and self.trade_price != 0:
                    ioc_buyprice = self.trade_price - self.ioc_price * self.pricetick
                    nb_buy_orders = np.sum([order.price == ioc_buyprice for order in self.send_orders])
                    if nb_buy_orders == 0:
                        self.buy(self.vt_symbol, ioc_buyprice, volume_traded, 'reverse')

            self.update_data()
        if order.order_status.value in ['Invalid', 'Unsend']:
            self.log_info(
                f"废单，订单状态：{order.order_status.value}, 信息:{order.order_id},错误代码:{order.error_code},错误信息：{order.error_message}")
        if order.order_status.value in ['Cancelled', 'PartialCancelled']:
            # self.log_info(f"收到撤单回报, 方向:{order.direction.value}, 价格:{order.order_price},数量:{order.order_qty},信息:{order.order_id},撤单时间：{datetime.datetime.now()}")
            self.cancel_list.append([str(order.order_id), datetime.datetime.now()])
        if order.order_status.value in ['Cancelled', 'PartialCancelled']:
            self.cancel_count += 1
        if order.order_status.value in ['Open']:
            # self.log_info(f"收到下单回报, 方向:{order.direction.value}, 价格:{order.order_price}, 数量:{order.order_qty}, 信息:{order.order_id},下单时间：{datetime.datetime.now()}")
            self.quote_list.append([str(order.order_id), datetime.datetime.now()])

    def on_send_order(self, response: SendOrderResponse):  #记录首次发单返回的数据（包括是否发单成功）
        if not response.success:
            self.log_info(f"下单失败:{response}")
        else:
            current_sid = response.sid.split('.')[-1]
            if current_sid.isdigit():
                return  # 过滤算法单回报
            for orderid, order in zip(response.orderid_list, response.order_list):
                cq_symbol = f"{order.symbol}.{order.exchange.value}"
                if isinstance(orderid, str):  # 失败
                    self.log_info(
                        f"下单标的{cq_symbol}失败, 方向:{order.direction.value}, 价格:{order.price}, 数量:{order.quantity}, 信息:{orderid},下单时间：{datetime.datetime.now()}")
                else:
                    # self.log_info(f"收到预下单回报, 方向:{order.direction.value}, 价格:{order.price}, 数量:{order.quantity}, 信息:{orderid},下单时间：{datetime.datetime.now()}")
                    self.active_limit_orders[str(orderid)] = order  #记录成功发单的订单
                    self.pre_quote_list.append([str(orderid), datetime.datetime.now()])

    def cal_delay(self):
        quote_delay = 0
        quote_delay_count = 0
        for i in dict(self.quote_list):
            if i in dict(self.pre_quote_list):
                quote_delay += (dict(self.quote_list)[i] - dict(self.pre_quote_list)[i]).total_seconds()
                quote_delay_count += 1
        if quote_delay_count != 0:
            average_quote_delay = quote_delay / quote_delay_count

        cancel_delay = 0
        cancel_delay_count = 0
        for i in dict(self.cancel_list):
            if i in dict(self.pre_cancel_list):
                cancel_delay += (dict(self.cancel_list)[i] - dict(self.pre_cancel_list)[i]).total_seconds()
                cancel_delay_count += 1
        if cancel_delay_count != 0:
            average_cancel_delay = cancel_delay / cancel_delay_count

        dt = datetime.datetime.now()

        self.log_info(f"发单延迟：{average_quote_delay * 1000}ms,撤单延迟：{average_cancel_delay * 1000}ms")
        self.log_info(f"内部穿透:{self.through_delay / self.mCount * 1000}ms")
