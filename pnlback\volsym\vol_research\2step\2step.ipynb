{"cells": [{"cell_type": "code", "execution_count": 1, "id": "530d1c68f508d6fe", "metadata": {"ExecuteTime": {"end_time": "2024-12-31T02:33:19.331569Z", "start_time": "2024-12-31T02:33:19.310565Z"}}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'sse50_option_data_processed_20231110.csv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 21\u001b[0m\n\u001b[0;32m     18\u001b[0m data_raw \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_pickle(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msse50_option_data_processed_20231110.pkl\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     19\u001b[0m data_raw \u001b[38;5;241m=\u001b[39m data_raw\u001b[38;5;241m.\u001b[39msort_values([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontract_month\u001b[39m\u001b[38;5;124m\"\u001b[39m,\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mexerciseprice\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m---> 21\u001b[0m data_raw2\u001b[38;5;241m=\u001b[39mpd\u001b[38;5;241m.\u001b[39mread_csv(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msse50_option_data_processed_20231110.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[1;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[0;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[0;32m   1014\u001b[0m     dialect,\n\u001b[0;32m   1015\u001b[0m     delimiter,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[0;32m   1023\u001b[0m )\n\u001b[0;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[1;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m _read(filepath_or_buffer, kwds)\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[0m, in \u001b[0;36m_read\u001b[1;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[0;32m    617\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[0;32m    619\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[1;32m--> 620\u001b[0m parser \u001b[38;5;241m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    622\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[0;32m    623\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[1;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[0;32m   1617\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m   1619\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m-> 1620\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_engine(f, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mengine)\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[1;34m(self, f, engine)\u001b[0m\n\u001b[0;32m   1878\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[0;32m   1879\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m-> 1880\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m get_handle(\n\u001b[0;32m   1881\u001b[0m     f,\n\u001b[0;32m   1882\u001b[0m     mode,\n\u001b[0;32m   1883\u001b[0m     encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mencoding\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[0;32m   1884\u001b[0m     compression\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcompression\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[0;32m   1885\u001b[0m     memory_map\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmemory_map\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m),\n\u001b[0;32m   1886\u001b[0m     is_text\u001b[38;5;241m=\u001b[39mis_text,\n\u001b[0;32m   1887\u001b[0m     errors\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mencoding_errors\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstrict\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[0;32m   1888\u001b[0m     storage_options\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstorage_options\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[0;32m   1889\u001b[0m )\n\u001b[0;32m   1890\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1891\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\pandas\\io\\common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(\n\u001b[0;32m    874\u001b[0m             handle,\n\u001b[0;32m    875\u001b[0m             ioargs\u001b[38;5;241m.\u001b[39mmode,\n\u001b[0;32m    876\u001b[0m             encoding\u001b[38;5;241m=\u001b[39mioargs\u001b[38;5;241m.\u001b[39mencoding,\n\u001b[0;32m    877\u001b[0m             errors\u001b[38;5;241m=\u001b[39merrors,\n\u001b[0;32m    878\u001b[0m             newline\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    879\u001b[0m         )\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'sse50_option_data_processed_20231110.csv'"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib as mpl\n", "import numba\n", "import pandas as pd\n", "import scipy.stats as stats\n", "from scipy.integrate import quad\n", "from scipy.optimize import minimize,linprog\n", "import scipy.optimize as opt\n", "from scipy.interpolate import interp1d\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus']=False #正常显示负号\n", "pd.set_option('display.max_columns',None)\n", "pd.set_option('display.max_rows',None)\n", "\n", "data_raw = pd.read_pickle(\"sse50_option_data_processed_20231110.pkl\")\n", "data_raw = data_raw.sort_values([\"contract_month\",\"exerciseprice\"])\n", "\n", "data_raw2=pd.read_csv(\"sse50_option_data_processed_20231110.csv\")\n", "# print(data_raw)"]}, {"cell_type": "code", "execution_count": 5, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-12-30T06:50:37.039354Z", "start_time": "2024-12-30T06:50:36.608190Z"}, "collapsed": true}, "outputs": [{"data": {"image/png": "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***************************/*********************************************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", "text/plain": ["<Figure size 800x2000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "class svi_2step:\n", "    def __init__(self, F, K, T, imp_vol, opt_paras):\n", "        self.x = np.log(K / F)  # 远期在值程度\n", "        self.T = T  # 到期时间\n", "        self.imp_vol = imp_vol  # 隐含波动率\n", "        self.w = imp_vol ** 2 * T  # 总方差\n", "        # opt_paras：优化的a,d,c,m,sigma参数，需要字典形式如，{\"a\":0.0005,\"d\":-0.005,\"c\":0.01,\"m\":0.1,\"sigma\":0.1}\n", "        # 初始传入的作为优化的初始值，随着优化算法迭代更新为最优值\n", "        self.opt_paras = opt_paras\n", "\n", "    def outer_func(self, m_sigma):\n", "        m, sigma = m_sigma\n", "        y = (self.x - m) / sigma\n", "\n", "        def inner_func(adc):\n", "            a, d, c = adc\n", "            err = np.sum((a + d * y + c * np.sqrt(y ** 2 + 1) - self.w) ** 2)\n", "            return err\n", "\n", "        init_adc = (self.opt_paras[\"a\"], self.opt_paras[\"d\"], self.opt_paras[\"c\"])\n", "        bnds = ([1e-6, np.max(self.w)], [-4 * sigma, 4 * sigma], [1e-6, 4 * sigma])\n", "        cons = (\n", "            {\"type\": \"ineq\", \"fun\": lambda x: x[2] - np.abs(x[1])},\n", "            {\"type\": \"ineq\", \"fun\": lambda x: 4 * sigma - x[2] - np.abs(x[1])}\n", "        )\n", "        a_star, d_star, c_star = minimize(fun=inner_func, x0=init_adc, method=\"SLSQP\", bounds=bnds, constraints=cons).x\n", "\n", "        self.opt_paras[\"a\"], self.opt_paras[\"d\"], self.opt_paras[\"c\"] = a_star, d_star, c_star\n", "\n", "        err = np.sum((a_star + d_star * y + c_star * np.sqrt(y ** 2 + 1) - self.w) ** 2)\n", "        return err\n", "\n", "    def fit(self):\n", "        init_m_sigma = (self.opt_paras[\"m\"], self.opt_paras[\"sigma\"])\n", "        m_star, sigma_star = minimize(fun=self.outer_func, x0=init_m_sigma, method=\"Nelder-Mead\",\n", "                                      bounds=((2 * min(self.x.min(), 0), 2 * max(self.x.max(), 0)), (1e-6, 1))).x\n", "        self.opt_paras[\"m\"], self.opt_paras[\"sigma\"] = m_star, sigma_star\n", "        return self.opt_paras\n", "\n", "    def eval(self, x, output):\n", "        y = (x - self.opt_paras[\"m\"]) / self.opt_paras[\"sigma\"]\n", "        svi_w = self.opt_paras[\"a\"] + self.opt_paras[\"d\"] * y + self.opt_paras[\"c\"] * np.sqrt(y ** 2 + 1)\n", "        svi_imp_vol = np.sqrt(svi_w / self.T)\n", "        if output == \"w\":\n", "            return svi_w\n", "        elif output == \"imp_vol\":\n", "            return svi_imp_vol\n", "\n", "\n", "class svi_surface:\n", "    def __init__(self, data, opt_method):\n", "        self.data = data\n", "        self.opt_method = opt_method\n", "\n", "    def get_fit_curve(self):\n", "        fit_result = []\n", "\n", "        # 循环每个月份，获得相应的拟合函数，返回一个包含svi实例的列表\n", "        for month in self.data[\"contract_month\"].unique():\n", "            fit_option = self.data[(self.data[\"实虚值\"] == \"虚值\") & (self.data[\"contract_month\"] == month)]\n", "            F = fit_option[\"F\"].values[0]\n", "            K = fit_option[\"exerciseprice\"].values\n", "            T = fit_option[\"maturity\"].values[0]\n", "            imp_vol = fit_option[\"market_imp_vol\"].values\n", "            opt_paras = {\"a\": 0.0005, \"d\": -0.005, \"c\": 0.01, \"m\": 0.1, \"sigma\": 0.1}  # 推荐的初始值，根据数据的情况调整\n", "            svi = self.opt_method(F, K, T, imp_vol, opt_paras)\n", "            svi.fit()\n", "            fit_result.append(svi)\n", "\n", "        return fit_result\n", "\n", "    def plot_fit_curve(self):\n", "        fit_result = self.get_fit_curve()\n", "        fig, ax = plt.subplots(nrows=len(self.data[\"contract_month\"].unique()), ncols=1, figsize=(8, 20))\n", "        for i, month in enumerate(self.data[\"contract_month\"].unique()):\n", "            fit_option = self.data[(self.data[\"实虚值\"] == \"虚值\") & (self.data[\"contract_month\"] == month)]\n", "            svi = fit_result[i]\n", "            fit_option[\"svi_vol\"] = svi.eval(svi.x, output=\"imp_vol\")\n", "\n", "            ax[i].scatter(x=fit_option[\"exerciseprice\"], y=fit_option[\"market_imp_vol\"], marker='+', c=\"r\")\n", "            ax[i].plot(fit_option[\"exerciseprice\"], fit_option[\"svi_vol\"])\n", "            ax[i].set_title(month)\n", "\n", "    #  根据拟合的SVI函数，和平远期插值，生成100*100的隐含波动率网格\n", "    def gen_imp_vol_grid(self):\n", "        x = np.log(self.data[\"exerciseprice\"] / self.data[\"F\"])\n", "        t_array = np.linspace(self.data[\"maturity\"].min(), self.data[\"maturity\"].max(), 100)\n", "        x_array = np.linspace(x.min(), x.max(), 100)\n", "        t, x = np.meshgrid(t_array, x_array)\n", "\n", "        # 计算4个期限上的svi拟合的总方差，并存储在100*4的矩阵里\n", "        fit_result = self.get_fit_curve()\n", "        w = np.zeros((100, len(fit_result)))\n", "        for m in range(len(fit_result)):\n", "            w[:, m] = fit_result[m].eval(x_array, output=\"w\")\n", "\n", "        # 在x的维度上循环100次，每次循环在t维度上平远期插值计算\n", "        v = np.zeros_like(t)\n", "\n", "        for n in range(100):\n", "            f = interp1d(x=self.data[\"maturity\"].unique(), y=w[n], kind=\"linear\")\n", "            v[n] = np.sqrt(f(t[n]) / t[n])  # 返回的还是隐含波动率而不是总方差\n", "\n", "        return t, x, v\n", "\n", "    def plot_surface(self):\n", "        fig = plt.figure(figsize=(12, 7))\n", "        ax = plt.axes(projection='3d')\n", "        norm = mpl.colors.Normalize(vmin=0.1, vmax=0.2)\n", "        # 绘图主程序\n", "        t, x, v = self.gen_imp_vol_grid()\n", "        surf = ax.plot_surface(t, x, v, rstride=1, cstride=1,\n", "                               cmap=plt.cm.coolwarm, norm=norm, linewidth=0.5, antialiased=True)\n", "        # 设置坐标轴\n", "        ax.set_xlabel('maturity')\n", "        ax.set_ylabel('strike')\n", "        ax.set_zlabel('market_imp_vol')\n", "        ax.set_zlim((0.1, 0.25))\n", "        fig.colorbar(surf, shrink=0.25, aspect=5)\n", "\n", "\n", "class svi_2step_45(svi_2step):\n", "    def outer_func(self, m_sigma):\n", "        m, sigma = m_sigma\n", "        y = (self.x - m) / sigma\n", "\n", "        bnd = ((0, 0, 0), (max(self.w.max(), 1e-6), 2 * np.sqrt(2) * sigma, 2 * np.sqrt(2) * sigma))\n", "        z = np.sqrt(np.square(y) + 1)\n", "\n", "        # 换元法，等价于d，c旋转45°，还原后除了约束条件变化外，这里的优化函数A也会相应变化\n", "\n", "        A = np.column_stack([np.ones(len(y)), np.sqrt(2) / 2 * (y + z), np.sqrt(2) / 2 * (-y + z)])\n", "\n", "        a_2, d_2, c_2 = opt.lsq_linear(A, self.w, bnd, tol=1e-12, verbose=False).x\n", "        a_star, d_star, c_star = a_2, np.sqrt(2) / 2 * (d_2 - c_2), np.sqrt(2) / 2 * (d_2 + c_2)\n", "\n", "        self.opt_paras[\"a\"], self.opt_paras[\"d\"], self.opt_paras[\"c\"] = a_star, d_star, c_star\n", "\n", "        err = np.sum((a_star + d_star * y + c_star * np.sqrt(y ** 2 + 1) - self.w) ** 2)\n", "        return err\n", "\n", "svi_fit = svi_surface(data=data_raw, opt_method=svi_2step)\n", "svi_fit.plot_fit_curve()"]}, {"cell_type": "code", "execution_count": 2, "id": "fc213dd740583f1b", "metadata": {"ExecuteTime": {"end_time": "2024-12-30T06:06:00.609883Z", "start_time": "2024-12-30T06:06:00.147807Z"}}, "outputs": [{"data": {"image/png": "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**************************/fwex/079LI3VrEqG/vrdOq3fn6K/vrdeyrYf1zM0dFRbIzWHe4miBQxMXrdXKXUclSROuaKn/+3NbWX0Y8QJA7cA1s4CnOtfwW7/9aJeVO/Xmjzv16nfbVe401Diyjl69rZvim0bWQEiYaX1mriYsSNWBvGIF+1v178Fd9JdODc2OBQDnxDWzACr4Wn10/59a6/3xF6txZB3tyynSrW+v1PTfyi1qp/dXZ2rw2yt1IK9YLeoG69N7L6HIAqiVKLOApyooOD5lZ5+Yl519Yv7/iG8aqS8f6KubuzZSudPQtG+26bZ3VmpfDjeH1SaOsnI9/slGPfLRBpWUOXX1RTH6dOIlah0TanY0AHAJLjMAPJ3dLoWEHP+6oKBSg9l/snafnvx0kwocZQoN9NWUAZ3Uv0sjFweFq2XlFWvCwlSt3Zsri0V66Oo2uvfKVvLh+lgAHobLDACc1YBujfXl/X3VrUmE8ovLdN+7a/V/H6xXgaPM7GioouSMY7rhteVauzdXYYG+mj0yQff9qTVFFkCtR5kFPF1w8PGbvQzjvB4x2iQ6SO+Pv1j3X9VKPhbpw9R96jf9Z63LzHVdVlQ7wzA055cM3T7zVx0pcKhdg1B9dt+lurJdfbOjAUCNoMwCXszP6qOH/txWi++6WI3CA7XnaKEGvbVCb/64g5vDPEBRSbkefn+9Jn+WrjKnoRu7NNLH9/RR0+jK/08NAHg6yiwA9WwepaUPXKZ+nRqqzGnoX19t1R2zftXBvCKzo3m2Pzyd7aTxgKtB5rFC3fLWCn28dr+sPhY90e8ivXpbVwX5M3w4AO9CmQUgSQoP8tPrt3fTvwZ1VpC/Vb/uOqbrXvlZX6UdNDsa/sdP2w7rhteWK/2gTdHB/lowtpfG9W0hy7nGHgaAWogyC6CCxWLRrT3i9MX9fdW5cbjyikp194I1euzjDSos4eawSrPbT0xnm3eeDMPQGz/s0Mg5ycorKlWXxuH67L5LdXHL6GoIDQCeiaG5AJxWSZlT077Zprd/2inDkFrUC9b027qpY2y42dHcXyWfznY+Chxlevj9dfp60/FxhW9LiNPTN3ZQoJ+1KgkBwK2dT1+jzAI4qxU7juiv769Tts0hP6tFj1zbTmMvbc6QT2dTzWV2x6ECjZ+fop2H7fKzWjT5xo66vVeTCwgIAO6NcWYBVJs+rerqqwcu05/bx6i03NA/v9yskXOSdchWbHY093WeT2c7m683ZenmN37RzsN2NQgL1HvjL6bIAsAf1EiZTUtLU0JCgiIjIzVp0iRV9mTwihUr1LZt21PmT548WVFRUQoICNCAAQOUn59f3ZEB/EFksL/eHh6vKQM6KdDPRz9vP6LrXv1Z36Znn3tjbxQcfGI627yzKHcaevHrLRo/P1UFjjL1bB6lz+67VN2bRLooNAB4JpeXWYfDof79+ys+Pl4pKSlKT09XUlLSObdLTU3VgAED5HA4Tpq/cOFCLVy4UF999ZU2bdqkzZs36/nnn3dRegC/s1gsur1XE31+36Vq3zBMx+wlGjcvRU8tSVNxabnZ8WqV3MISjUlarTd+2ClJGnNJcy0c10v1QgNMTgYA7sflZXbp0qXKy8vTtGnT1LJlS02ZMkWJiYln3cZut2vgwIGaOHHiKcsyMzM1d+5c9ezZU61atdKQIUO0du1aV8UH8D9a1Q/VJ/f20bhLm0uS5q3coxtfX67NB20mJ3NDVXg6W/oBm/q/vlzLth1WoJ+PXhnSVU/1by8/K1eFAcDpuPy34/r169W7d28FBQVJkjp37qz09PSzbuPn56cVK1aob9++pyx79NFHdfHFF1e83rp1q1q3bn3GfTkcDtlstpMmABcmwNeqJ25or3ljeqpuSIC2ZRfopjd+0ZxfMip9GRFOtWTdfg186xdlHitSXFQdfTShj27uFmt2LABway4vszabTc2bN694bbFYZLValZOTc8Zt/P39FRt77l/g27Zt0yeffKK77rrrjOtMnTpV4eHhFVNcXNz5fQAAZ3RZm3r66sG+uqpdfZWUOTX5s3SNSVqtIwWOc298Ji58apa7Ki13avJnm/TA4nUqLnXqsjb19NnES9WhEcOgAcC5uLzM+vr6KiDg5Ou8AgMDVVhYeEH7dTqdGjNmjMaNG6cOHTqccb3HHntMeXl5FVNmZuYFvS+Ak9UNCVDiyB565qYO8vf10Q9bD+u6V37Sj1sPmR3NIxzOd+iOWas055fdkqSJV7bSnFEJigjyNzcYAHgIlz/EOyoqSmlpaSfNy8/Pl7//hf2ifvbZZ3Xs2DG9+OKLZ10vICDglDINoHpZLBaNuLiZejWP1v3vrtXW7HyNmrNaYy5prkeua1u5gf1/Pwv7v0/N+l0lrzn1JGv35mjCgjXKshUrJMBX/x7cRdd1bGB2LADwKC4/M5uQkKCVK1dWvM7IyJDD4VBUVFSV9/nZZ59p2rRp+uijjyquxQVgvrYNQrVk4iUa1aeZJGn2Lxm6+Y1ftD27EsPnhYQcn2JiTsyLiTkxv5ZZtGqvhrz9q7JsxWpZL1if3nsJRRYAqsDlZfayyy6TzWbTnDlzJElTpkzR1VdfLavVqtzcXJWXn9+QPps3b9bQoUP12muvKS4uTgUFBRd8yQKA6hPoZ9XTN3ZQ4sgeigr215asfN3w2nLN/***************************************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", "text/plain": ["<Figure size 800x2000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["svi_fit = svi_surface(data = data_raw,opt_method=svi_2step_45)\n", "svi_fit.plot_fit_curve()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}