import numpy as np
from dataclasses import dataclass
from typing import Callable, Optional, Tuple, List
from scipy.optimize import minimize
from scipy.stats import norm

@dataclass
class RawSVIParams:
    a: float      # Level parameter
    b: float      # Controls wing slopes
    rho: float    # Correlation
    m: float      # Location parameter
    sigma: float  # Smoothness parameter

@dataclass
class NaturalSVIParams:
    Delta: float  # Level parameter
    omega: float  # Scale parameter
    rho: float    # Correlation 
    mu: float     # Location parameter
    zeta: float   # Shape parameter

@dataclass
class SVIJWParams:
    vt: float     # ATM variance w(0)/t
    psi_t: float  # ATM skew ∂√w(k)/∂k|k=0
    pt: float     # Put wing slope
    ct: float     # Call wing slope
    v_tilde: float # Minimum variance

class SVIVolatilitySurface:
    def __init__(self):
        self.N = norm.cdf
        self.N_inv = norm.ppf
        
    # Roger <PERSON>'s Moment Formula Functions
    def q_star(self, beta: float) -> float:
        """q* = (1/2)((1/√β*) - (√β*/2))²"""
        beta_sqrt = np.sqrt(beta)
        return 0.5 * ((1/beta_sqrt) - (beta_sqrt/2))**2
    
    def beta_star(self, x: float) -> float:
        """β* = g(x) = 2 - 4[√(x² + x) - x]"""
        return 2 - 4 * (np.sqrt(x*x + x) - x)
    
    # SVI Parameterizations
    def raw_svi(self, k: float, params: RawSVIParams) -> float:
        """w(k; χR) = a + b{ρ(k-m) + √[(k-m)² + σ²]}"""
        km = k - params.m
        return (params.a + params.b * 
                (params.rho * km + np.sqrt(km*km + params.sigma**2)))
    
    def natural_svi(self, k: float, params: NaturalSVIParams) -> float:
        """w(k,θt) = Δ + (ω/2){1 + ρφ(θt)k + √[(φ(θt)k + ρ)² + (1-ρ²)]}"""
        return (params.Delta + (params.omega/2) * 
                (1 + params.rho*params.zeta*(k - params.mu) + 
                np.sqrt((params.zeta*(k - params.mu) + params.rho)**2 + 
                       (1 - params.rho**2))))
    
    def square_root_phi(self, theta: float) -> float:
        """φ(θ) = η/√θ"""
        eta = 1.0  # Parameter to be calibrated
        return eta/np.sqrt(theta)
    
    # Arbitrage Checks
    def calendar_spread_check(self, k: float, t1: float, t2: float, w1: Callable, w2: Callable) -> bool:
        """∂tw(k,t) ≥ 0"""
        return w2(k) >= w1(k)
    
    def butterfly_arbitrage_function(self, k: float, w: Callable, dw: Callable, d2w: Callable) -> float:
        """g(k) = (1 - kw'(k)/2w(k))² - (w'(k)²/4)(1/w(k) + 1/4) + w''(k)/2"""
        w_k = w(k)
        w_prime = dw(k)
        w_double_prime = d2w(k)
        
        term1 = (1 - k*w_prime/(2*w_k))**2
        term2 = (w_prime**2/4) * (1/w_k + 1/4)
        term3 = w_double_prime/2
        
        return term1 - term2 + term3
    
    def density_function(self, k: float, w: Callable, dw: Callable, d2w: Callable) -> float:
        """d+(k) = -∞ for no butterfly arbitrage"""
        # Implementation of density function check
        pass
    
    # Benaim and Friz Extensions
    def right_wing_asymptotic(self, k: float, F: Callable) -> float:
        """σBS(k,T)²T/k ~ g(-1 - log[1-F(k)]/k)"""
        return self.g_function(-1 - np.log(1 - F(k))/k)
    
    def left_wing_asymptotic(self, k: float, F: Callable) -> float:
        """σBS(-k,T)²T/k ~ g(-log F(-k)/k)"""
        return self.g_function(-np.log(F(-k))/k)
    
    def g_function(self, x: float) -> float:
        return 2 - 4 * (np.sqrt(x*x + x) - x)
    
    # Local Variance
    def local_variance(self, k: float, T: float, C: Callable) -> float:
        """vloc(k,T) = ∂TC/(1/2)K²∂K,KC"""
        K = np.exp(k)
        dC_dT = self.partial_T(C, k, T)
        d2C_dK2 = self.partial_KK(C, k, T)
        return dC_dT/(0.5 * K*K * d2C_dK2)
    
    # SVI-JW Parameters
    def compute_svijw_params(self, k: np.ndarray, w: np.ndarray, t: float) -> SVIJWParams:
        """Compute all SVI-JW parameters"""
        # ATM variance
        vt = np.interp(0, k, w)/t
        
        # ATM skew
        idx_atm = np.argmin(np.abs(k))
        dk = k[idx_atm+1] - k[idx_atm]
        sqrt_w = np.sqrt(w)
        psi_t = (sqrt_w[idx_atm+1] - sqrt_w[idx_atm])/(dk)
        
        # Wing slopes
        dw = np.gradient(w, k)
        w0 = np.interp(0, k, w)
        pt = -np.min(dw)/np.sqrt(w0)
        ct = np.max(dw)/np.sqrt(w0)
        
        # Minimum variance
        v_tilde = np.min(w)/t
        
        return SVIJWParams(vt, psi_t, pt, ct, v_tilde)
    
    # Surface Construction
    def construct_surface(self, k: np.ndarray, t: np.ndarray, market_data: np.ndarray) -> dict:
        """Build complete arbitrage-free SVI surface"""
        # Step 1: Initial square-root SVI fit
        initial_fit = self.fit_square_root_svi(k, t, market_data)
        
        # Step 2: QR optimization with arbitrage constraints
        final_fit = self.optimize_with_constraints(initial_fit)
        
        # Step 3: Verify no-arbitrage conditions
        self.verify_arbitrage_free(final_fit)
        
        return final_fit
    
    def fit_square_root_svi(self, k: np.ndarray, t: np.ndarray, 
                           market_data: np.ndarray) -> dict:
        """Initial fit using square root parameterization"""
        params = []
        for i in range(len(t)):
            slice_params = self.fit_single_slice(k, market_data[:,i], t[i])
            params.append(slice_params)
        return {"t": t, "params": params}
    
    def optimize_with_constraints(self, initial_fit: dict) -> dict:
        """Optimize fit with no-arbitrage constraints"""
        def objective(params):
            return self.compute_error(params, initial_fit)
        
        constraints = [
            {"type": "ineq", "fun": self.calendar_spread_constraint},
            {"type": "ineq", "fun": self.butterfly_arbitrage_constraint}
        ]
        
        result = minimize(objective, x0=self.flatten_params(initial_fit),
                        constraints=constraints)
        return self.unflatten_params(result.x)
    
    def verify_arbitrage_free(self, fit: dict) -> bool:
        """Verify all no-arbitrage conditions are satisfied"""
        # Check calendar spread arbitrage
        cal_spread_ok = self.verify_calendar_spread(fit)
        
        # Check butterfly arbitrage
        butterfly_ok = self.verify_butterfly_arbitrage(fit)
        
        # Check moment formula bounds
        moment_bounds_ok = self.verify_moment_bounds(fit)
        
        return cal_spread_ok and butterfly_ok and moment_bounds_ok

    # Utility Functions
    def partial_T(self, f: Callable, k: float, T: float, eps: float = 1e-7) -> float:
        """Compute partial derivative with respect to T"""
        return (f(k, T + eps) - f(k, T))/eps
    
    def partial_KK(self, f: Callable, k: float, T: float, eps: float = 1e-7) -> float:
        """Compute second partial derivative with respect to K"""
        return (f(k + eps, T) - 2*f(k, T) + f(k - eps, T))/(eps*eps)
    
    def vega_weight(self, k: float, T: float, sigma: float) -> float:
        """Compute vega for weighting: vega = √T K exp(-d₂²/2)/√(2π)"""
        K = np.exp(k)
        d2 = -k/(2*sigma*np.sqrt(T)) - sigma*np.sqrt(T)/2
        return np.sqrt(T) * K * np.exp(-d2*d2/2) / np.sqrt(2*np.pi)

def example():
    # Create synthetic market data
    k = np.linspace(-2, 2, 100)
    t = np.array([0.1, 0.5, 1.0])
    
    # Initialize model
    svi = SVIVolatilitySurface()
    
    # Example parameters
    params = RawSVIParams(a=0.04, b=0.4, rho=-0.4, m=0.1, sigma=0.1)
    
    # Generate synthetic surface
    surface = np.zeros((len(k), len(t)))
    for i in range(len(t)):
        surface[:,i] = svi.raw_svi(k, params)
    
    # Add noise to create market data
    market_data = surface + np.random.normal(0, 0.001, surface.shape)
    
    # Fit surface
    fit = svi.construct_surface(k, t, market_data)
    
    return fit

if __name__ == "__main__":
    result = example()
    print("Surface fitting complete")