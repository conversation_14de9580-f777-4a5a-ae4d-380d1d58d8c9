# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-23
from WindPy import w
import pyodbc
from datetime import datetime, timedelta

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = "2017-12-13"
sectorcodelist = ["000300.SH", "000016.SH", "000905.SH"]
# beginDate = "2017-06-24"
w.start()

conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user, autocommit=True)
cursor = conn.cursor()


def tosql(sectorcode):
    if sectorcode == "000300.SH":
        table = "StkType300_New2"
    elif sectorcode == "000016.SH":
        table = "StkType50_New"
    elif sectorcode == "000905.SH":
        table = "StkType800_New"

    s=("""
     IF OBJECT_ID( '%s' , 'U') IS NOT NULL
        DROP TABLE %s
     CREATE TABLE %s (
        STKID varchar(20) not null,
        StkName nchar(10),
        Wgt float,
        RefreshTime datetime,
        Classification nchar(10)
        )
     """)
    cursor.execute(s % (table, table, table))
    sql = "INSERT INTO %s VALUES (?, ?,?,?,?)" % (table)
    stocks = []


    print('\n\n' + '-----通过wset来取%s数据集数据,获取%s代码列表-----' % (dt,table) + '\n')
    wsetdata1 = w.wset("indexconstituent","date= %s ;windcode= %s " % (dt, sectorcode))
    print(wsetdata1)

    # stocks = list(set(stocks)+(set(wsetdata1.Data[1])))
    # sqllist=stocks
    for i in range(0, len(wsetdata1.Data[1])):
        sqllist = []
        for k in range(1, len(wsetdata1.Fields)):
            sqllist.append(wsetdata1.Data[k][i])
        sqllist.append(wsetdata1.Data[0][i])
        sqllist.append("")
        sqltuple = tuple(sqllist)
        cursor.execute(sql, sqltuple)
        conn.commit()


for sectorcode in sectorcodelist:
    tosql(sectorcode)
conn.close()