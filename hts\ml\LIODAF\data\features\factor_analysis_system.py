
"""
因子分析系统
整合因子分析和因子测试功能，提供全面的因子评估和分析能力
@author: lining
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
from tqdm import tqdm

from utils.utils import log_print
from core import config
from utils.utils import calculate_win_rate,calculate_win_rate_threshold
import shap

class FactorAnalysisSystem:
    

    def __init__(self):
        self.analysis_results = {}
        self.test_results = {}
        self.shap_values = {}
        self.output_dir = config.OUTDIR + "/features"
        self.threshold_percent = config.THRESHOLD_PERCENT
        self.quantile_list = config.QUANTILE_LIST

    def analyze_factor(self, data: pd.Series) -> Dict:
        
        
        if not pd.api.types.is_numeric_dtype(data):
            print(data.name, data.dtype,data.shape,data.head(5))
            raise ValueError("数据类型错误，必顨为数值类型")
        analysis = {
            'basic_stats': {
                'mean': data.mean(),
                'std': data.std(),
                'min': data.min(),
                'max': data.max(),
                'skew': data.skew(),
                'kurtosis': data.kurtosis()
            },
            'quantiles': {
                f"{q*100}%": data.quantile(q) for q in self.quantile_list
            },
            'missing_stats': {
                'total': data.isna().sum(),
                'percentage': data.isna().mean() * 100
            },
            'unique_values': data.nunique()
        }
        
        
        for lag in [1, 5, 10, 20]:
            analysis[f'autocorr_{lag}'] = data.autocorr(lag=lag)
        
        return analysis
    
    def analyze_factor_set(self, data: pd.DataFrame, factors: List[str]) -> Dict:
        
        results = {}
        for factor in factors:
            if factor in data.columns:
                try:
                    results[factor] = self.analyze_factor(data[factor])
                except Exception as e:
                    log_print(f"因子 {factor} 分析失败: {e}")
        self.analysis_results = results
        return results

    def calculate_ic(self, factor: pd.Series, returns: pd.Series) -> float:
        
        return factor.corr(returns)
    
    def calculate_rank_ic(self, factor: pd.Series, returns: pd.Series) -> float:
        
        return factor.rank().corr(returns.rank())

    def calculate_ic_series(self, factor: pd.Series, returns: pd.Series, window: int = 20) -> pd.Series:
        
        return factor.rolling(window=window).corr(returns)

    def calculate_ir(self, factor: pd.Series, returns: pd.Series, window: int = 200) -> float:
        
        ic_series = []
        for i in range(window, len(factor), window):
            window_factor = factor.iloc[i - window:i]
            window_returns = returns.iloc[i - window:i]
            if len(window_factor) > 1 and window_factor.std() != 0 and window_returns.std() != 0:
                window_ic = window_factor.corr(window_returns)
                ic_series.append(window_ic)

        ic_series = pd.Series(ic_series).dropna()
        if len(ic_series) > 0 and ic_series.std() != 0:
            return ic_series.mean() / ic_series.std()
        return 0

    def calculate_win_loss_ratio(self, factor: pd.Series, returns: pd.Series) -> float:
        
        win_avg = factor[returns > 0].abs().mean()
        loss_avg = factor[returns < 0].abs().mean()
        return win_avg / loss_avg

    def test_factors(self, std_train_data: pd.DataFrame, target_train_data: pd.DataFrame, feature_cols: List[str], target_cols: List[str], 
                     model: Optional[Any] = None) -> pd.DataFrame:
        
        log_print("开始因子测试...")

        
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        
        ic_values = {}
        win_rates = {}
        win_rates_threshold = {}
        non_zero_count = {}
        ir_values = {}
        rank_ic_values = {}
        win_loss_ratio = {}

        
        target = target_cols[0]
        valid_features = []
        
        data = pd.concat([std_train_data, target_train_data], axis=1)
        
        for feature in feature_cols:
            valid_data = data[[feature, target]].dropna()
            if len(valid_data) > 1 and valid_data[feature].std() != 0 and valid_data[target].std() != 0:
                valid_features.append((feature, valid_data))
        
        
        from concurrent.futures import ThreadPoolExecutor, as_completed
        from functools import partial
        
        def process_feature(self, feature_data):
            feature, valid_data = feature_data
            feature_series = valid_data[feature]
            target_series = valid_data[target]
            
            
            results = {
                'ic': self.calculate_ic(feature_series, target_series),
                'rank_ic': self.calculate_rank_ic(feature_series, target_series),
                'win_rate': calculate_win_rate(feature_series, target_series),
                'win_loss_ratio': self.calculate_win_loss_ratio(feature_series, target_series),
                'ir': self.calculate_ir(feature_series, target_series),
                'win_rates_threshold': {},
                'non_zero_count': {}
            }
            
            
            for threshold_percent in self.quantile_list:
                results['win_rates_threshold'][threshold_percent], results['non_zero_count'][threshold_percent] = \
                    calculate_win_rate_threshold(feature_series, target_series, threshold_percent)
            
            return feature, results
        
        
        with ThreadPoolExecutor(max_workers=min(os.cpu_count(), len(valid_features))) as executor:
            process_func = partial(process_feature, self)
            futures = {executor.submit(process_func, feature_data): feature_data[0] for feature_data in valid_features}
            
            for future in tqdm(as_completed(futures), total=len(futures), desc="计算因子指标"):
                feature, results = future.result()
                ic_values[feature] = results['ic']
                rank_ic_values[feature] = results['rank_ic']
                win_rates[feature] = results['win_rate']
                win_loss_ratio[feature] = results['win_loss_ratio']
                ir_values[feature] = results['ir']
                win_rates_threshold[feature] = results['win_rates_threshold']
                non_zero_count[feature] = results['non_zero_count']
        
        
        invalid_features = set(feature_cols) - set(f for f, _ in valid_features)
        if invalid_features:
            log_print(f"警告: {len(invalid_features)}个特征无法计算有效指标，已赋默认值")

        
        factor_test_results = pd.DataFrame({
            'feature': feature_cols,
            'ic': [ic_values.get(feature, 0) for feature in feature_cols],
            'abs_ic': [abs(ic_values.get(feature, 0)) for feature in feature_cols],
            'rank_ic': [rank_ic_values.get(feature, 0) for feature in feature_cols],
            'ir': [ir_values.get(feature, 0) for feature in feature_cols],
            'win_rate': [win_rates.get(feature, 0.5) for feature in feature_cols],
            'win_loss_ratio': [win_loss_ratio.get(feature, 0) for feature in feature_cols],
            'win_rate_threshold': [win_rates_threshold.get(feature, {}) for feature in feature_cols],
            'non_zero_count': [non_zero_count.get(feature, {}) for feature in feature_cols]
        })
        factor_test_results = factor_test_results.sort_values('abs_ic', ascending=False)

        
        self._print_statistics(factor_test_results)

        
        if config.ENABLE_SHAP and model is not None:
            log_print("开始SHAP因子分析...")
            
            X_for_shap = data[feature_cols].dropna()
            if len(X_for_shap) > 0:
                
                shap_results = self.analyze_shap(model, config.MODEL_TYPE, X_for_shap, feature_cols)
                
                
                if shap_results and 'importance' in shap_results:
                    shap_importance_dict = dict(zip(
                        shap_results['importance']['feature'], 
                        shap_results['importance']['importance']
                    ))
                    
                    
                    factor_test_results['shap_importance'] = [
                        shap_importance_dict.get(feature, 0) for feature in factor_test_results['feature']
                    ]
                    
                    
                    log_print("按SHAP重要性排序的Top 5因子:")
                    shap_sorted = factor_test_results.sort_values('shap_importance', ascending=False)
                    
                    for idx, row in shap_sorted.head().iterrows():
                        log_print(f"因子: {row['feature']}, SHAP重要性: {row['shap_importance']:.4e}, IC值: {row['ic']:.2e}")


        self.test_results = factor_test_results
        return factor_test_results
    

    def generate_analysis_report(self, data: pd.DataFrame, factors: List[str]) -> str:
        
        results = self.analyze_factor_set(data, factors)
        report = "# 因子分析报告\n\n"

        
        report += "## 总体统计信息\n\n"
        report += f"- 分析因子总数: {len(factors)}\n"
        report += f"- 有效因子数量: {len(results)}\n"
        report += f"- 数据样本量: {len(data)}\n\n"

        
        report += "## 因子相关性分析\n\n"
        report += "因子间相关性热图见附件。高度相关的因子可能存在信息冗余，建议在模型中选择代表性因子。\n\n"

        
        ic_values = [analysis.get('ic', 0) for analysis in results.values() if 'ic' in analysis]
        if ic_values:
            report += "## IC值分析\n\n"
            report += f"- 平均IC值: {np.mean(ic_values):.4f}\n"
            report += f"- IC值标准差: {np.std(ic_values):.4f}\n"
            report += f"- 最大IC值: {np.max(ic_values):.4f}\n"
            report += f"- 最小IC值: {np.min(ic_values):.4f}\n\n"
        
        
        if hasattr(self, 'shap_values') and self.shap_values and 'importance' in self.shap_values:
            report += "## SHAP重要性分析\n\n"
            report += "SHAP值分析可以帮助理解每个因子对模型预测的贡献。SHAP图表见附件。\n\n"
            
            
            shap_importance_df = self.shap_values['importance'].copy()
            top_shap_factors = shap_importance_df.head(20)
            
            report += "### SHAP重要性Top 20因子\n\n"
            for i, (_, row) in enumerate(top_shap_factors.iterrows(), 1):
                report += f"{i}. {row['feature']}: {row['importance']:.4f}\n"
            report += "\n"
            
            
            if self.test_results is not None and 'shap_importance' in self.test_results.columns:
                report += "### SHAP重要性与IC值比较\n\n"
                report += "SHAP重要性与IC值可能存在差异，这表明某些因子虽然与目标变量的线性相关性较低，但在模型中可能通过非线性方式发挥重要作用。\n\n"
                
                
                test_results_sorted_by_shap = self.test_results.sort_values('shap_importance', ascending=False).head(10)
                report += "#### SHAP重要性Top 10因子的IC值\n\n"
                for i, row in enumerate(test_results_sorted_by_shap.iterrows(), 1):
                    idx, data = row
                    report += f"{i}. {data['feature']}: SHAP重要性 = {data['shap_importance']:.4f}, IC值 = {data['ic']:.4f}\n"
                report += "\n"
                
                test_results_sorted_by_ic = self.test_results.sort_values('abs_ic', ascending=False).head(10)
                report += "#### IC值Top 10因子的SHAP重要性\n\n"
                for i, row in enumerate(test_results_sorted_by_ic.iterrows(), 1):
                    idx, data = row
                    report += f"{i}. {data['feature']}: IC值 = {data['ic']:.4f}, SHAP重要性 = {data.get('shap_importance', 0):.4f}\n"
                report += "\n"

        
        report += "## 各因子详细分析\n\n"
        for factor, analysis in results.items():
            report += f"### {factor}\n\n"

            
            report += "#### 基本统计量\n"
            for stat, value in analysis['basic_stats'].items():
                report += f"- {stat}: {value:.4f}\n"

            
            report += "\n#### 分位数\n"
            for q, value in analysis['quantiles'].items():
                report += f"- {q}: {value:.4f}\n"

            
            report += "\n#### 缺失值统计\n"
            for stat, value in analysis['missing_stats'].items():
                report += f"- {stat}: {value:.4f}\n"

            
            report += "\n#### 自相关性\n"
            for lag in [1, 5, 10, 20]:
                report += f"- lag_{lag}: {analysis[f'autocorr_{lag}']:.4f}\n"

            
            if 'ic' in analysis:
                report += "\n#### 预测能力分析\n"
                report += f"- IC值: {analysis['ic']:.4f}\n"
                report += f"- IR值: {analysis.get('ir', 0):.4f}\n"
                report += f"- 胜率: {analysis.get('win_rate', 0):.4f}\n"
                for threshold_percent in self.quantile_list:
                    report += f"- 胜率{threshold_percent*100}%: {analysis.get('win_rate_threshold', 0)[threshold_percent]:.4f}\n"
                    report += f"- 非0数量: {analysis.get('non_zero_count', 0)[threshold_percent]:.4f}\n"

                
                rating = "一般"
                ic_abs = abs(analysis['ic'])
                if ic_abs > 0.02:
                    rating = "良好"
                if ic_abs > 0.05:
                    rating = "优秀"
                if ic_abs > 0.10:
                    rating = "极好"
                report += f"- 因子评级: {rating}\n"
                
                
                if self.test_results is not None and 'shap_importance' in self.test_results.columns:
                    shap_importance = self.test_results.loc[self.test_results['feature'] == factor, 'shap_importance']
                    if not shap_importance.empty:
                        report += f"- SHAP重要性: {shap_importance.iloc[0]:.4f}\n"

            
            stability = analysis.get('stability', 0)
            if stability:
                report += f"- 稳定性指标: {stability:.4f}\n"

            report += "\n"
        
        
        report += "## 因子列表\n\n"
        report += "factor_list = ["
        for factor in results.keys():
            report += f"{factor},\n"
        report += "]"

        return report

    def _print_statistics(self, results: pd.DataFrame):
        
        log_print("\n=== 因子检验结果 ===")
        log_print(f"因子检验完成，共检验 {len(results)} 个因子")
        log_print(f"平均IC值: {results['ic'].mean():.4f}")
        log_print(f"平均Rank IC值: {results['rank_ic'].mean():.4f}")
        log_print(f"平均IR值: {results['ir'].mean():.4f}")
        log_print(f"平均胜率: {results['win_rate'].mean():.4f}")

        good_factors = len(results[results['abs_ic'] > 0.02])
        excellent_factors = len(results[results['abs_ic'] > 0.05])
        outstanding_factors = len(results[results['abs_ic'] > 0.10])

        log_print("\n=== 因子质量分布 ===")
        log_print(f"良好因子数量 (|IC| > 0.02): {good_factors}")
        log_print(f"优秀因子数量 (|IC| > 0.05): {excellent_factors}")
        log_print(f"极好因子数量 (|IC| > 0.10): {outstanding_factors}")

        log_print("\n=== Top 5因子 ===")
        log_print(f"因子数量: {len(results)}")
        for idx, row in results.head().iterrows():
            log_print(f"因子: {row['feature']}, IC值: {row['ic']:.2f}, Rank IC值: {row['rank_ic']:.2f}, IR值: {row['ir']:.2f}, 胜率: {row['win_rate']:.2f}, 分位胜率: "+" ".join([f"{threshold_percent}: {row['win_rate_threshold'][threshold_percent]*100:.2f}% {row['non_zero_count'][threshold_percent]} " for threshold_percent in self.quantile_list]))

    def analyze_shap(self, model, model_type, X_train: pd.DataFrame, feature_cols: List[str], background_sample_size: int = 100) -> Dict:
        
        log_print("开始SHAP值分析...")
        
        
        try:
            
            if model_type in ['xgboost', 'lightgbm']:
                
                explainer = shap.TreeExplainer(model)
            elif model_type == 'linear':
                
                explainer = shap.LinearExplainer(model, X_train[feature_cols])
            else:
                
                background = shap.sample(X_train[feature_cols], background_sample_size)
                explainer = shap.KernelExplainer(model.predict, background)
            
            
            shap_values = explainer.shap_values(X_train[feature_cols])
            
            
            if isinstance(shap_values, list):
                shap_values = shap_values[0]
                
            
            shap_importance = np.abs(shap_values).mean(axis=0)
            shap_importance_df = pd.DataFrame({
                'feature': feature_cols,
                'importance': shap_importance
            }).sort_values('importance', ascending=True)
            
            
            self.shap_values = {'values': shap_values, 'data': X_train[feature_cols], 'feature_names': feature_cols, 'importance': shap_importance_df}
            
            log_print(f"SHAP分析完成，分析了{len(feature_cols)}个特征")
            return self.shap_values
            
        except Exception as e:
            log_print(f"SHAP分析失败: {e}")
            return {}


factor_analysis_system = FactorAnalysisSystem()

