#%% 
'''IP SETTING FOR INFLUXDB'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('DCE.prod')
import warnings
import re
warnings.filterwarnings('ignore')
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机""
    'DCE.dev'代表大商所生产机    
"""

"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.basisTrading_position import BasisTrading
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np
 
maker='pp2211.DCE'
refer='pp2209.DCE'
symbol,exchange = maker.split('.')
contract = symbol[:re.search(r'\d',symbol).start()].upper()
exchange = exchange.upper()
df = pd.read_csv("contract_config.csv",index_col=0,error_bad_lines=False)
config = df.to_dict()
if exchange =='CZCE':
    exchange = 'CZC'
if exchange == 'SHFE':
    exchange = 'SHF'
Ticker = contract + '.'+exchange
multiplier = config['size'][Ticker]
ts = config['pricetick'][Ticker]

engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,
                           duty=False,fast_join=True,refer_late = True,refer_test=False) #Counter代表是否只统计对价成交，duty代表是否统计义务

engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔

    start=datetime.datetime(2022, 5,26, 21, 0), # 开始时间
    end=datetime.datetime(2022, 5,27,15,00), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: ts,refer: ts}, # 一个tick大小
    capital=1_000_000, # 初始资金
)
# 添加回测策略，并修改内部参数

engine.clear_data()

engine.add_strategy(BasisTrading, {'lots': 1,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,
                                    'maxPos':150,'path':500,'eta':0,'x_stop':1,'net_limit':10,'position_flag':True,'eta_cancel':0,'cancel_layer':1})
engine.load_data() # 加载历史数据W
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
duty = engine.duty_statistics()
#engine.show_tick_chart() # 显示图表
trades = pd.DataFrame([{'datetime':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
           'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
           'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments,'basePrice':engine.trades[x].basePrice,
           'midPrice':engine.trades[x].midPrice} for x in engine.trades])
orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
           'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
           } for x in engine.get_all_orders()])
 
total_pnl = df["net_pnl"].sum()
print('总盈亏:',total_pnl)
print('逐笔盈亏:',total_pnl/len(trades[trades.symbol==maker.split('.')[0]]))
orders_main = orders[orders.symbol==refer.split('.')[0]]
print('主力撤单次数：',len(orders_main[orders_main.traded==0]))
orders_maker = orders[orders.symbol==maker.split('.')[0]]
print('做市撤单次数：',len(orders_maker[orders_maker.traded==0]))
print('成交量：',len(trades[trades.symbol==maker.split('.')[0]]))
 
trades_maker = trades[trades.symbol == maker.split('.')[0]]
print('做市合约总成交：',len(trades_maker))
trades_maker['willing'] = trades_maker.comments.apply(lambda x: 1 if x=='down_buy' else -1)
trades_maker_unwilling = trades_maker[trades_maker.direction != trades_maker.willing]
print('做市合约不符合预期的成交：',len(trades_maker_unwilling))

from vnpy_tools.PostTradeAnalysis import PostTradeAnalysis
Analysis = PostTradeAnalysis(engine,multiplier)
_df = Analysis.plotbacktestresults_meanreversion() 
