import os
import sys

import polars as pl
from tqdm import tqdm

from db_solve import try_encodings


def convert_csv_to_parquet(current_dir):
    if current_dir.endswith('.csv'):
        files = [current_dir, ]
    else:
        files = os.listdir(current_dir)

    for file in tqdm(files):
        if file.endswith('.csv'):
            print(f'Converting {file} to Parquet...')

            # 构建 CSV 文件的完整路径
            csv_file_path = os.path.join(current_dir, file)

            # 构建 Parquet 文件的名称和路径
            parquet_file_name = os.path.splitext(file)[0] + '.parquet'
            parquet_file_path = os.path.join(current_dir, parquet_file_name)

            encoding = try_encodings(csv_file_path)
            if "TradeRecords" in file:
                separator = '\t'
            else:
                separator = ','
            my_csv = pl.read_csv(csv_file_path, infer_schema_length=10_000_000, encoding=encoding, separator=separator)
            # my_csv = my_csv[[s.name for s in my_csv if not (s.null_count() == my_csv.height)]]
            my_csv.write_parquet(
                parquet_file_path,
                compression="zstd",  # 使用的压缩算法
            )
            print(f'Successfully converted {file} to {parquet_file_name}')


def load_parquet_file_path():
    # Parquet 文件路径
    parquet_file_path = r'G:\\DATA\\SH500\\TradeRecords20240522.parquet'

    # 读取 Parquet 文件
    df = pl.read_parquet(parquet_file_path)

    # 显示前几行数据
    print(df.head())


# 调用函数以批量转换 CSV 文件
# current_dir = os.getcwd()
current_dir = r'G:\\DATA\\SH500'
convert_csv_to_parquet(current_dir)
print(f'Successfully converted all')
sys.exit()
