{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"pygments_lexer": "ipython3", "nbconvert_exporter": "python", "version": "3.6.4", "file_extension": ".py", "codemirror_mode": {"name": "ipython", "version": 3}, "name": "python", "mimetype": "text/x-python"}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": ["from IPython.core.display import display, HTML\n", "\n", "import pandas as pd\n", "import numpy as np # linear algebra\n", "import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)\n", "import glob\n", "import os\n", "import gc\n", "\n", "from joblib import Parallel, delayed\n", "\n", "from sklearn import preprocessing, model_selection\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.preprocessing import QuantileTransformer\n", "from sklearn.metrics import r2_score\n", "\n", "import matplotlib.pyplot as plt \n", "import seaborn as sns\n", "import numpy.matlib\n", "\n", "\n", "path_submissions = '/'\n", "\n", "target_name = 'target'\n", "scores_folds = {}"], "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "execution": {"iopub.status.busy": "2021-08-26T03:55:40.361388Z", "iopub.execute_input": "2021-08-26T03:55:40.362092Z", "iopub.status.idle": "2021-08-26T03:55:41.741631Z", "shell.execute_reply.started": "2021-08-26T03:55:40.361982Z", "shell.execute_reply": "2021-08-26T03:55:41.740401Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# data directory\n", "data_dir = '../input/optiver-realized-volatility-prediction/'\n", "\n", "# Function to calculate first WAP\n", "def calc_wap1(df):\n", "    wap = (df['bid_price1'] * df['ask_size1'] + df['ask_price1'] * df['bid_size1']) / (df['bid_size1'] + df['ask_size1'])\n", "    return wap\n", "\n", "# Function to calculate second WAP\n", "def calc_wap2(df):\n", "    wap = (df['bid_price2'] * df['ask_size2'] + df['ask_price2'] * df['bid_size2']) / (df['bid_size2'] + df['ask_size2'])\n", "    return wap\n", "\n", "def calc_wap3(df):\n", "    wap = (df['bid_price1'] * df['bid_size1'] + df['ask_price1'] * df['ask_size1']) / (df['bid_size1'] + df['ask_size1'])\n", "    return wap\n", "\n", "def calc_wap4(df):\n", "    wap = (df['bid_price2'] * df['bid_size2'] + df['ask_price2'] * df['ask_size2']) / (df['bid_size2'] + df['ask_size2'])\n", "    return wap\n", "\n", "# Function to calculate the log of the return\n", "# Remember that logb(x / y) = logb(x) - logb(y)\n", "def log_return(series):\n", "    return np.log(series).diff()\n", "\n", "# Calculate the realized volatility\n", "def realized_volatility(series):\n", "    return np.sqrt(np.sum(series**2))\n", "\n", "# Function to count unique elements of a series\n", "def count_unique(series):\n", "    return len(np.unique(series))\n", "\n", "# Function to read our base train and test set\n", "def read_train_test():\n", "    train = pd.read_csv('../input/optiver-realized-volatility-prediction/train.csv')\n", "    test = pd.read_csv('../input/optiver-realized-volatility-prediction/test.csv')\n", "    # Create a key to merge with book and trade data\n", "    train['row_id'] = train['stock_id'].astype(str) + '-' + train['time_id'].astype(str)\n", "    test['row_id'] = test['stock_id'].astype(str) + '-' + test['time_id'].astype(str)\n", "    print(f'Our training set has {train.shape[0]} rows')\n", "    return train, test\n", "\n", "# Function to preprocess book data (for each stock id)\n", "def book_preprocessor(file_path):\n", "    df = pd.read_parquet(file_path)\n", "    # Calculate Wap\n", "    df['wap1'] = calc_wap1(df)\n", "    df['wap2'] = calc_wap2(df)\n", "    df['wap3'] = calc_wap3(df)\n", "    df['wap4'] = calc_wap4(df)\n", "    # Calculate log returns\n", "    df['log_return1'] = df.groupby(['time_id'])['wap1'].apply(log_return)\n", "    df['log_return2'] = df.groupby(['time_id'])['wap2'].apply(log_return)\n", "    df['log_return3'] = df.groupby(['time_id'])['wap3'].apply(log_return)\n", "    df['log_return4'] = df.groupby(['time_id'])['wap4'].apply(log_return)\n", "    # Calculate wap balance\n", "    df['wap_balance'] = abs(df['wap1'] - df['wap2'])\n", "    # Calculate spread\n", "    df['price_spread'] = (df['ask_price1'] - df['bid_price1']) / ((df['ask_price1'] + df['bid_price1']) / 2)\n", "    df['price_spread2'] = (df['ask_price2'] - df['bid_price2']) / ((df['ask_price2'] + df['bid_price2']) / 2)\n", "    df['bid_spread'] = df['bid_price1'] - df['bid_price2']\n", "    df['ask_spread'] = df['ask_price1'] - df['ask_price2']\n", "    df[\"bid_ask_spread\"] = abs(df['bid_spread'] - df['ask_spread'])\n", "    df['total_volume'] = (df['ask_size1'] + df['ask_size2']) + (df['bid_size1'] + df['bid_size2'])\n", "    df['volume_imbalance'] = abs((df['ask_size1'] + df['ask_size2']) - (df['bid_size1'] + df['bid_size2']))\n", "    \n", "    # Dict for aggregations\n", "    create_feature_dict = {\n", "        'wap1': [np.sum, np.std],\n", "        'wap2': [np.sum, np.std],\n", "        'wap3': [np.sum, np.std],\n", "        'wap4': [np.sum, np.std],\n", "        'log_return1': [realized_volatility],\n", "        'log_return2': [realized_volatility],\n", "        'log_return3': [realized_volatility],\n", "        'log_return4': [realized_volatility],\n", "        'wap_balance': [np.sum, np.max],\n", "        'price_spread':[np.sum, np.max],\n", "        'price_spread2':[np.sum, np.max],\n", "        'bid_spread':[np.sum, np.max],\n", "        'ask_spread':[np.sum, np.max],\n", "        'total_volume':[np.sum, np.max],\n", "        'volume_imbalance':[np.sum, np.max],\n", "        \"bid_ask_spread\":[np.sum,  np.max],\n", "    }\n", "    create_feature_dict_time = {\n", "        'log_return1': [realized_volatility],\n", "        'log_return2': [realized_volatility],\n", "        'log_return3': [realized_volatility],\n", "        'log_return4': [realized_volatility],\n", "    }\n", "    \n", "    # Function to get group stats for different windows (seconds in bucket)\n", "    def get_stats_window(fe_dict,seconds_in_bucket, add_suffix = False):\n", "        # Group by the window\n", "        df_feature = df[df['seconds_in_bucket'] >= seconds_in_bucket].groupby(['time_id']).agg(fe_dict).reset_index()\n", "        # Rename columns joining suffix\n", "        df_feature.columns = ['_'.join(col) for col in df_feature.columns]\n", "        # Add a suffix to differentiate windows\n", "        if add_suffix:\n", "            df_feature = df_feature.add_suffix('_' + str(seconds_in_bucket))\n", "        return df_feature\n", "    \n", "    # Get the stats for different windows\n", "    df_feature = get_stats_window(create_feature_dict,seconds_in_bucket = 0, add_suffix = False)\n", "    df_feature_500 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 500, add_suffix = True)\n", "    df_feature_400 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 400, add_suffix = True)\n", "    df_feature_300 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 300, add_suffix = True)\n", "    df_feature_200 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 200, add_suffix = True)\n", "    df_feature_100 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 100, add_suffix = True)\n", "\n", "    # Merge all\n", "    df_feature = df_feature.merge(df_feature_500, how = 'left', left_on = 'time_id_', right_on = 'time_id__500')\n", "    df_feature = df_feature.merge(df_feature_400, how = 'left', left_on = 'time_id_', right_on = 'time_id__400')\n", "    df_feature = df_feature.merge(df_feature_300, how = 'left', left_on = 'time_id_', right_on = 'time_id__300')\n", "    df_feature = df_feature.merge(df_feature_200, how = 'left', left_on = 'time_id_', right_on = 'time_id__200')\n", "    df_feature = df_feature.merge(df_feature_100, how = 'left', left_on = 'time_id_', right_on = 'time_id__100')\n", "    # Drop unnecesary time_ids\n", "    df_feature.drop(['time_id__500','time_id__400', 'time_id__300', 'time_id__200','time_id__100'], axis = 1, inplace = True)\n", "    \n", "    \n", "    # Create row_id so we can merge\n", "    stock_id = file_path.split('=')[1]\n", "    df_feature['row_id'] = df_feature['time_id_'].apply(lambda x: f'{stock_id}-{x}')\n", "    df_feature.drop(['time_id_'], axis = 1, inplace = True)\n", "    return df_feature\n", "\n", "# Function to preprocess trade data (for each stock id)\n", "def trade_preprocessor(file_path):\n", "    df = pd.read_parquet(file_path)\n", "    df['log_return'] = df.groupby('time_id')['price'].apply(log_return)\n", "    df['amount']=df['price']*df['size']\n", "    # Dict for aggregations\n", "    create_feature_dict = {\n", "        'log_return':[realized_volatility],\n", "        'seconds_in_bucket':[count_unique],\n", "        'size':[np.sum, np.max, np.min],\n", "        'order_count':[np.sum,np.max],\n", "        'amount':[np.sum,np.max,np.min],\n", "    }\n", "    create_feature_dict_time = {\n", "        'log_return':[realized_volatility],\n", "        'seconds_in_bucket':[count_unique],\n", "        'size':[np.sum],\n", "        'order_count':[np.sum],\n", "    }\n", "    # Function to get group stats for different windows (seconds in bucket)\n", "    def get_stats_window(fe_dict,seconds_in_bucket, add_suffix = False):\n", "        # Group by the window\n", "        df_feature = df[df['seconds_in_bucket'] >= seconds_in_bucket].groupby(['time_id']).agg(fe_dict).reset_index()\n", "        # Rename columns joining suffix\n", "        df_feature.columns = ['_'.join(col) for col in df_feature.columns]\n", "        # Add a suffix to differentiate windows\n", "        if add_suffix:\n", "            df_feature = df_feature.add_suffix('_' + str(seconds_in_bucket))\n", "        return df_feature\n", "    \n", "\n", "    # Get the stats for different windows\n", "    df_feature = get_stats_window(create_feature_dict,seconds_in_bucket = 0, add_suffix = False)\n", "    df_feature_500 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 500, add_suffix = True)\n", "    df_feature_400 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 400, add_suffix = True)\n", "    df_feature_300 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 300, add_suffix = True)\n", "    df_feature_200 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 200, add_suffix = True)\n", "    df_feature_100 = get_stats_window(create_feature_dict_time,seconds_in_bucket = 100, add_suffix = True)\n", "    \n", "    def tendency(price, vol):    \n", "        df_diff = np.diff(price)\n", "        val = (df_diff/price[1:])*100\n", "        power = np.sum(val*vol[1:])\n", "        return(power)\n", "    \n", "    lis = []\n", "    for n_time_id in df['time_id'].unique():\n", "        df_id = df[df['time_id'] == n_time_id]        \n", "        tendencyV = tendency(df_id['price'].values, df_id['size'].values)      \n", "        f_max = np.sum(df_id['price'].values > np.mean(df_id['price'].values))\n", "        f_min = np.sum(df_id['price'].values < np.mean(df_id['price'].values))\n", "        df_max =  np.sum(np.diff(df_id['price'].values) > 0)\n", "        df_min =  np.sum(np.diff(df_id['price'].values) < 0)\n", "        # new\n", "        abs_diff = np.median(np.abs( df_id['price'].values - np.mean(df_id['price'].values)))        \n", "        energy = np.mean(df_id['price'].values**2)\n", "        iqr_p = np.percentile(df_id['price'].values,75) - np.percentile(df_id['price'].values,25)\n", "        \n", "        # vol vars\n", "        \n", "        abs_diff_v = np.median(np.abs( df_id['size'].values - np.mean(df_id['size'].values)))        \n", "        energy_v = np.sum(df_id['size'].values**2)\n", "        iqr_p_v = np.percentile(df_id['size'].values,75) - np.percentile(df_id['size'].values,25)\n", "        \n", "        lis.append({'time_id':n_time_id,'tendency':tendencyV,'f_max':f_max,'f_min':f_min,'df_max':df_max,'df_min':df_min,\n", "                   'abs_diff':abs_diff,'energy':energy,'iqr_p':iqr_p,'abs_diff_v':abs_diff_v,'energy_v':energy_v,'iqr_p_v':iqr_p_v})\n", "    \n", "    df_lr = pd.DataFrame(lis)\n", "        \n", "   \n", "    df_feature = df_feature.merge(df_lr, how = 'left', left_on = 'time_id_', right_on = 'time_id')\n", "    \n", "    # Merge all\n", "    df_feature = df_feature.merge(df_feature_500, how = 'left', left_on = 'time_id_', right_on = 'time_id__500')\n", "    df_feature = df_feature.merge(df_feature_400, how = 'left', left_on = 'time_id_', right_on = 'time_id__400')\n", "    df_feature = df_feature.merge(df_feature_300, how = 'left', left_on = 'time_id_', right_on = 'time_id__300')\n", "    df_feature = df_feature.merge(df_feature_200, how = 'left', left_on = 'time_id_', right_on = 'time_id__200')\n", "    df_feature = df_feature.merge(df_feature_100, how = 'left', left_on = 'time_id_', right_on = 'time_id__100')\n", "    # Drop unnecesary time_ids\n", "    df_feature.drop(['time_id__500','time_id__400', 'time_id__300', 'time_id__200','time_id','time_id__100'], axis = 1, inplace = True)\n", "    \n", "    \n", "    df_feature = df_feature.add_prefix('trade_')\n", "    stock_id = file_path.split('=')[1]\n", "    df_feature['row_id'] = df_feature['trade_time_id_'].apply(lambda x:f'{stock_id}-{x}')\n", "    df_feature.drop(['trade_time_id_'], axis = 1, inplace = True)\n", "    return df_feature\n", "\n", "# Function to get group stats for the stock_id and time_id\n", "def get_time_stock(df):\n", "    vol_cols = ['log_return1_realized_volatility', 'log_return2_realized_volatility', 'log_return1_realized_volatility_400', 'log_return2_realized_volatility_400', \n", "                'log_return1_realized_volatility_300', 'log_return2_realized_volatility_300', 'log_return1_realized_volatility_200', 'log_return2_realized_volatility_200', \n", "                'trade_log_return_realized_volatility', 'trade_log_return_realized_volatility_400', 'trade_log_return_realized_volatility_300', 'trade_log_return_realized_volatility_200']\n", "\n", "\n", "    # Group by the stock id\n", "    df_stock_id = df.groupby(['stock_id'])[vol_cols].agg(['mean', 'std', 'max', 'min', ]).reset_index()\n", "    # Rename columns joining suffix\n", "    df_stock_id.columns = ['_'.join(col) for col in df_stock_id.columns]\n", "    df_stock_id = df_stock_id.add_suffix('_' + 'stock')\n", "\n", "    # Group by the stock id\n", "    df_time_id = df.groupby(['time_id'])[vol_cols].agg(['mean', 'std', 'max', 'min', ]).reset_index()\n", "    # Rename columns joining suffix\n", "    df_time_id.columns = ['_'.join(col) for col in df_time_id.columns]\n", "    df_time_id = df_time_id.add_suffix('_' + 'time')\n", "    \n", "    # Merge with original dataframe\n", "    df = df.merge(df_stock_id, how = 'left', left_on = ['stock_id'], right_on = ['stock_id__stock'])\n", "    df = df.merge(df_time_id, how = 'left', left_on = ['time_id'], right_on = ['time_id__time'])\n", "    df.drop(['stock_id__stock', 'time_id__time'], axis = 1, inplace = True)\n", "    return df\n", "    \n", "# Funtion to make preprocessing function in parallel (for each stock id)\n", "def preprocessor(list_stock_ids, is_train = True):\n", "    \n", "    # Parrallel for loop\n", "    def for_joblib(stock_id):\n", "        # Train\n", "        if is_train:\n", "            file_path_book = data_dir + \"book_train.parquet/stock_id=\" + str(stock_id)\n", "            file_path_trade = data_dir + \"trade_train.parquet/stock_id=\" + str(stock_id)\n", "        # Test\n", "        else:\n", "            file_path_book = data_dir + \"book_test.parquet/stock_id=\" + str(stock_id)\n", "            file_path_trade = data_dir + \"trade_test.parquet/stock_id=\" + str(stock_id)\n", "    \n", "        # Preprocess book and trade data and merge them\n", "        df_tmp = pd.merge(book_preprocessor(file_path_book), trade_preprocessor(file_path_trade), on = 'row_id', how = 'left')\n", "        \n", "        # Return the merge dataframe\n", "        return df_tmp\n", "    \n", "    # Use parallel api to call paralle for loop\n", "    df = Parallel(n_jobs = -1, verbose = 1)(delayed(for_joblib)(stock_id) for stock_id in list_stock_ids)\n", "    # Concatenate all the dataframes that return from Parallel\n", "    df = pd.concat(df, ignore_index = True)\n", "    return df\n", "\n", "# Function to calculate the root mean squared percentage error\n", "def rmspe(y_true, y_pred):\n", "    return np.sqrt(np.mean(np.square((y_true - y_pred) / y_true)))\n", "\n", "# Function to early stop with root mean squared percentage error\n", "def feval_rmspe(y_pred, lgb_train):\n", "    y_true = lgb_train.get_label()\n", "    return 'RMSP<PERSON>', rmspe(y_true, y_pred), False"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T03:55:41.743631Z", "iopub.execute_input": "2021-08-26T03:55:41.744042Z", "iopub.status.idle": "2021-08-26T03:55:41.820543Z", "shell.execute_reply.started": "2021-08-26T03:55:41.744001Z", "shell.execute_reply": "2021-08-26T03:55:41.818751Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Read train and test\n", "train, test = read_train_test()\n", "\n", "# Get unique stock ids \n", "train_stock_ids = train['stock_id'].unique()\n", "# Preprocess them using Parallel and our single stock id functions\n", "train_ = preprocessor(train_stock_ids, is_train = True)\n", "train = train.merge(train_, on = ['row_id'], how = 'left')\n", "\n", "# Get unique stock ids \n", "test_stock_ids = test['stock_id'].unique()\n", "# Preprocess them using Parallel and our single stock id functions\n", "test_ = preprocessor(test_stock_ids, is_train = False)\n", "test = test.merge(test_, on = ['row_id'], how = 'left')\n", "\n", "# Get group stats of time_id and stock_id\n", "train = get_time_stock(train)\n", "test = get_time_stock(test)\n"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T03:55:41.825593Z", "iopub.execute_input": "2021-08-26T03:55:41.826226Z", "iopub.status.idle": "2021-08-26T04:39:00.806326Z", "shell.execute_reply.started": "2021-08-26T03:55:41.826159Z", "shell.execute_reply": "2021-08-26T04:39:00.805015Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# replace by order sum (tau)\n", "train['size_tau'] = np.sqrt( 1/ train['trade_seconds_in_bucket_count_unique'] )\n", "test['size_tau'] = np.sqrt( 1/ test['trade_seconds_in_bucket_count_unique'] )\n", "#train['size_tau_450'] = np.sqrt( 1/ train['trade_seconds_in_bucket_count_unique_450'] )\n", "#test['size_tau_450'] = np.sqrt( 1/ test['trade_seconds_in_bucket_count_unique_450'] )\n", "train['size_tau_400'] = np.sqrt( 1/ train['trade_seconds_in_bucket_count_unique_400'] )\n", "test['size_tau_400'] = np.sqrt( 1/ test['trade_seconds_in_bucket_count_unique_400'] )\n", "train['size_tau_300'] = np.sqrt( 1/ train['trade_seconds_in_bucket_count_unique_300'] )\n", "test['size_tau_300'] = np.sqrt( 1/ test['trade_seconds_in_bucket_count_unique_300'] )\n", "#train['size_tau_150'] = np.sqrt( 1/ train['trade_seconds_in_bucket_count_unique_150'] )\n", "#test['size_tau_150'] = np.sqrt( 1/ test['trade_seconds_in_bucket_count_unique_150'] )\n", "train['size_tau_200'] = np.sqrt( 1/ train['trade_seconds_in_bucket_count_unique_200'] )\n", "test['size_tau_200'] = np.sqrt( 1/ test['trade_seconds_in_bucket_count_unique_200'] )"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:00.808911Z", "iopub.execute_input": "2021-08-26T04:39:00.809267Z", "iopub.status.idle": "2021-08-26T04:39:00.837634Z", "shell.execute_reply.started": "2021-08-26T04:39:00.80923Z", "shell.execute_reply": "2021-08-26T04:39:00.836386Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["train['size_tau2'] = np.sqrt( 1/ train['trade_order_count_sum'] )\n", "test['size_tau2'] = np.sqrt( 1/ test['trade_order_count_sum'] )\n", "#train['size_tau2_450'] = np.sqrt( 0.25/ train['trade_order_count_sum'] )\n", "#test['size_tau2_450'] = np.sqrt( 0.25/ test['trade_order_count_sum'] )\n", "train['size_tau2_400'] = np.sqrt( 0.33/ train['trade_order_count_sum'] )\n", "test['size_tau2_400'] = np.sqrt( 0.33/ test['trade_order_count_sum'] )\n", "train['size_tau2_300'] = np.sqrt( 0.5/ train['trade_order_count_sum'] )\n", "test['size_tau2_300'] = np.sqrt( 0.5/ test['trade_order_count_sum'] )\n", "#train['size_tau2_150'] = np.sqrt( 0.75/ train['trade_order_count_sum'] )\n", "#test['size_tau2_150'] = np.sqrt( 0.75/ test['trade_order_count_sum'] )\n", "train['size_tau2_200'] = np.sqrt( 0.66/ train['trade_order_count_sum'] )\n", "test['size_tau2_200'] = np.sqrt( 0.66/ test['trade_order_count_sum'] )\n", "\n", "# delta tau\n", "train['size_tau2_d'] = train['size_tau2_400'] - train['size_tau2']\n", "test['size_tau2_d'] = test['size_tau2_400'] - test['size_tau2']"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:00.839025Z", "iopub.execute_input": "2021-08-26T04:39:00.839354Z", "iopub.status.idle": "2021-08-26T04:39:00.876876Z", "shell.execute_reply.started": "2021-08-26T04:39:00.839322Z", "shell.execute_reply": "2021-08-26T04:39:00.875798Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["colNames = [col for col in list(train.columns)\n", "            if col not in {\"stock_id\", \"time_id\", \"target\", \"row_id\"}]\n", "len(colNames)"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:00.878259Z", "iopub.execute_input": "2021-08-26T04:39:00.878585Z", "iopub.status.idle": "2021-08-26T04:39:00.888398Z", "shell.execute_reply.started": "2021-08-26T04:39:00.878555Z", "shell.execute_reply": "2021-08-26T04:39:00.887375Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from sklearn.cluster import KMeans\n", "# making agg features\n", "\n", "train_p = pd.read_csv('../input/optiver-realized-volatility-prediction/train.csv')\n", "train_p = train_p.pivot(index='time_id', columns='stock_id', values='target')\n", "\n", "corr = train_p.corr()\n", "\n", "ids = corr.index\n", "\n", "kmeans = KMeans(n_clusters=7, random_state=0).fit(corr.values)\n", "print(kmeans.labels_)\n", "\n", "l = []\n", "for n in range(7):\n", "    l.append ( [ (x-1) for x in ( (ids+1)*(kmeans.labels_ == n)) if x > 0] )\n", "    \n", "\n", "mat = []\n", "matTest = []\n", "\n", "n = 0\n", "for ind in l:\n", "    print(ind)\n", "    newDf = train.loc[train['stock_id'].isin(ind) ]\n", "    newDf = newDf.groupby(['time_id']).agg(np.nanmean)\n", "    newDf.loc[:,'stock_id'] = str(n)+'c1'\n", "    mat.append ( newDf )\n", "    \n", "    newDf = test.loc[test['stock_id'].isin(ind) ]    \n", "    newDf = newDf.groupby(['time_id']).agg(np.nanmean)\n", "    newDf.loc[:,'stock_id'] = str(n)+'c1'\n", "    matTest.append ( newDf )\n", "    \n", "    n+=1\n", "    \n", "mat1 = pd.concat(mat).reset_index()\n", "mat1.drop(columns=['target'],inplace=True)\n", "\n", "mat2 = pd.concat(matTest).reset_index()"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:00.889758Z", "iopub.execute_input": "2021-08-26T04:39:00.890063Z", "iopub.status.idle": "2021-08-26T04:39:03.223369Z", "shell.execute_reply.started": "2021-08-26T04:39:00.890036Z", "shell.execute_reply": "2021-08-26T04:39:03.222153Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["mat2 = pd.concat([mat2,mat1.loc[mat1.time_id==5]])\n", "mat1 = mat1.pivot(index='time_id', columns='stock_id')\n", "mat1.columns = [\"_\".join(x) for x in mat1.columns.ravel()]\n", "mat1.reset_index(inplace=True)\n", "\n", "mat2 = mat2.pivot(index='time_id', columns='stock_id')\n", "mat2.columns = [\"_\".join(x) for x in mat2.columns.ravel()]\n", "mat2.reset_index(inplace=True)"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:03.2273Z", "iopub.execute_input": "2021-08-26T04:39:03.227658Z", "iopub.status.idle": "2021-08-26T04:39:03.430722Z", "shell.execute_reply.started": "2021-08-26T04:39:03.227624Z", "shell.execute_reply": "2021-08-26T04:39:03.429488Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["nnn = ['time_id',\n", "     'log_return1_realized_volatility_0c1',\n", "     'log_return1_realized_volatility_1c1',     \n", "     'log_return1_realized_volatility_3c1',\n", "     'log_return1_realized_volatility_4c1',     \n", "     'log_return1_realized_volatility_6c1',\n", "     'total_volume_sum_0c1',\n", "     'total_volume_sum_1c1', \n", "     'total_volume_sum_3c1',\n", "     'total_volume_sum_4c1', \n", "     'total_volume_sum_6c1',\n", "     'trade_size_sum_0c1',\n", "     'trade_size_sum_1c1', \n", "     'trade_size_sum_3c1',\n", "     'trade_size_sum_4c1', \n", "     'trade_size_sum_6c1',\n", "     'trade_order_count_sum_0c1',\n", "     'trade_order_count_sum_1c1',\n", "     'trade_order_count_sum_3c1',\n", "     'trade_order_count_sum_4c1',\n", "     'trade_order_count_sum_6c1',      \n", "     'price_spread_sum_0c1',\n", "     'price_spread_sum_1c1',\n", "     'price_spread_sum_3c1',\n", "     'price_spread_sum_4c1',\n", "     'price_spread_sum_6c1',   \n", "     'bid_spread_sum_0c1',\n", "     'bid_spread_sum_1c1',\n", "     'bid_spread_sum_3c1',\n", "     'bid_spread_sum_4c1',\n", "     'bid_spread_sum_6c1',       \n", "     'ask_spread_sum_0c1',\n", "     'ask_spread_sum_1c1',\n", "     'ask_spread_sum_3c1',\n", "     'ask_spread_sum_4c1',\n", "     'ask_spread_sum_6c1',   \n", "     'volume_imbalance_sum_0c1',\n", "     'volume_imbalance_sum_1c1',\n", "     'volume_imbalance_sum_3c1',\n", "     'volume_imbalance_sum_4c1',\n", "     'volume_imbalance_sum_6c1',       \n", "     'bid_ask_spread_sum_0c1',\n", "     'bid_ask_spread_sum_1c1',\n", "     'bid_ask_spread_sum_3c1',\n", "     'bid_ask_spread_sum_4c1',\n", "     'bid_ask_spread_sum_6c1',\n", "     'size_tau2_0c1',\n", "     'size_tau2_1c1',\n", "     'size_tau2_3c1',\n", "     'size_tau2_4c1',\n", "     'size_tau2_6c1'] \n", "train = pd.merge(train,mat1[nnn],how='left',on='time_id')\n", "test = pd.merge(test,mat2[nnn],how='left',on='time_id')"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:03.433374Z", "iopub.execute_input": "2021-08-26T04:39:03.433828Z", "iopub.status.idle": "2021-08-26T04:39:12.053638Z", "shell.execute_reply.started": "2021-08-26T04:39:03.433782Z", "shell.execute_reply": "2021-08-26T04:39:12.052384Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import gc\n", "del mat1,mat2\n", "gc.collect()"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:12.055401Z", "iopub.execute_input": "2021-08-26T04:39:12.055894Z", "iopub.status.idle": "2021-08-26T04:39:12.229921Z", "shell.execute_reply.started": "2021-08-26T04:39:12.055844Z", "shell.execute_reply": "2021-08-26T04:39:12.228792Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from sklearn.model_selection import KFold\n", "import lightgbm as lgb\n", "\n", "seed0=2021\n", "params0 = {\n", "    'objective': 'rmse',\n", "    'boosting_type': 'gbdt',\n", "    'max_depth': -1,\n", "    'max_bin':100,\n", "    'min_data_in_leaf':500,\n", "    'learning_rate': 0.05,\n", "    'subsample': 0.72,\n", "    'subsample_freq': 4,\n", "    'feature_fraction': 0.5,\n", "    'lambda_l1': 0.5,\n", "    'lambda_l2': 1.0,\n", "    'categorical_column':[0],\n", "    'seed':seed0,\n", "    'feature_fraction_seed': seed0,\n", "    'bagging_seed': seed0,\n", "    'drop_seed': seed0,\n", "    'data_random_seed': seed0,\n", "    'n_jobs':-1,\n", "    'verbose': -1}\n", "seed1=42\n", "params1 = {\n", "        'learning_rate': 0.1,        \n", "        'lambda_l1': 2,\n", "        'lambda_l2': 7,\n", "        'num_leaves': 800,\n", "        'min_sum_hessian_in_leaf': 20,\n", "        'feature_fraction': 0.8,\n", "        'feature_fraction_bynode': 0.8,\n", "        'bagging_fraction': 0.9,\n", "        'bagging_freq': 42,\n", "        'min_data_in_leaf': 700,\n", "        'max_depth': 4,\n", "        'categorical_column':[0],\n", "        'seed': seed1,\n", "        'feature_fraction_seed': seed1,\n", "        'bagging_seed': seed1,\n", "        'drop_seed': seed1,\n", "        'data_random_seed': seed1,\n", "        'objective': 'rmse',\n", "        'boosting': 'gbdt',\n", "        'verbosity': -1,\n", "        'n_jobs':-1,\n", "    }\n", "# Function to early stop with root mean squared percentage error\n", "def rmspe(y_true, y_pred):\n", "    return np.sqrt(np.mean(np.square((y_true - y_pred) / y_true)))\n", "\n", "def feval_rmspe(y_pred, lgb_train):\n", "    y_true = lgb_train.get_label()\n", "    return 'RMSP<PERSON>', rmspe(y_true, y_pred), False\n", "\n", "def train_and_evaluate_lgb(train, test, params):\n", "    # Hyperparammeters (just basic)\n", "    \n", "    features = [col for col in train.columns if col not in {\"time_id\", \"target\", \"row_id\"}]\n", "    y = train['target']\n", "    # Create out of folds array\n", "    oof_predictions = np.zeros(train.shape[0])\n", "    # Create test array to store predictions\n", "    test_predictions = np.zeros(test.shape[0])\n", "    # Create a KFold object\n", "    kfold = KFold(n_splits = 5, random_state = 2021, shuffle = True)\n", "    # Iterate through each fold\n", "    for fold, (trn_ind, val_ind) in enumerate(kfold.split(train)):\n", "        print(f'Training fold {fold + 1}')\n", "        x_train, x_val = train.iloc[trn_ind], train.iloc[val_ind]\n", "        y_train, y_val = y.iloc[trn_ind], y.iloc[val_ind]\n", "        # Root mean squared percentage error weights\n", "        train_weights = 1 / np.square(y_train)\n", "        val_weights = 1 / np.square(y_val)\n", "        train_dataset = lgb.Dataset(x_train[features], y_train, weight = train_weights)\n", "        val_dataset = lgb.Dataset(x_val[features], y_val, weight = val_weights)\n", "        model = lgb.train(params = params,\n", "                          num_boost_round=1000,\n", "                          train_set = train_dataset, \n", "                          valid_sets = [train_dataset, val_dataset], \n", "                          verbose_eval = 250,\n", "                          early_stopping_rounds=50,\n", "                          feval = feval_rmspe)\n", "        # Add predictions to the out of folds array\n", "        oof_predictions[val_ind] = model.predict(x_val[features])\n", "        # Predict the test set\n", "        test_predictions += model.predict(test[features]) / 5\n", "    rmspe_score = rmspe(y, oof_predictions)\n", "    print(f'Our out of folds RMSPE is {rmspe_score}')\n", "    lgb.plot_importance(model,max_num_features=20)\n", "    # Return test predictions\n", "    return test_predictions\n", "# Traing and evaluate\n", "predictions_lgb= train_and_evaluate_lgb(train, test,params0)\n", "test['target'] = predictions_lgb\n", "test[['row_id', 'target']].to_csv('submission.csv',index = False)"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:39:12.231672Z", "iopub.execute_input": "2021-08-26T04:39:12.232289Z", "iopub.status.idle": "2021-08-26T04:48:53.091933Z", "shell.execute_reply.started": "2021-08-26T04:39:12.232241Z", "shell.execute_reply": "2021-08-26T04:48:53.090612Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["train.shape[1]"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:48:53.093404Z", "iopub.execute_input": "2021-08-26T04:48:53.093736Z", "iopub.status.idle": "2021-08-26T04:48:53.1003Z", "shell.execute_reply.started": "2021-08-26T04:48:53.093706Z", "shell.execute_reply": "2021-08-26T04:48:53.099474Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["\n", "from numpy.random import seed\n", "seed(42)\n", "import tensorflow as tf\n", "tf.random.set_seed(42)\n", "from tensorflow import keras\n", "import numpy as np\n", "from keras import backend as K\n", "def root_mean_squared_per_error(y_true, y_pred):\n", "         return K.sqrt(K.mean(K.square( (y_true - y_pred)/ y_true )))\n", "    \n", "es = tf.keras.callbacks.EarlyStopping(\n", "    monitor='val_loss', patience=20, verbose=0,\n", "    mode='min',restore_best_weights=True)\n", "\n", "plateau = tf.keras.callbacks.ReduceLROnPlateau(\n", "    monitor='val_loss', factor=0.2, patience=7, verbose=0,\n", "    mode='min')\n"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T04:48:53.101647Z", "iopub.execute_input": "2021-08-26T04:48:53.101939Z", "iopub.status.idle": "2021-08-26T04:48:58.989273Z", "shell.execute_reply.started": "2021-08-26T04:48:53.10191Z", "shell.execute_reply": "2021-08-26T04:48:58.988009Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# kfold based on the knn++ algorithm\n", "\n", "out_train = pd.read_csv('../input/optiver-realized-volatility-prediction/train.csv')\n", "out_train = out_train.pivot(index='time_id', columns='stock_id', values='target')\n", "\n", "#out_train[out_train.isna().any(axis=1)]\n", "out_train = out_train.fillna(out_train.mean())\n", "out_train.head()\n", "\n", "# code to add the just the read data after first execution\n", "\n", "# data separation based on knn ++\n", "nfolds = 5 # number of folds\n", "index = []\n", "totDist = []\n", "values = []\n", "# generates a matriz with the values of \n", "mat = out_train.values\n", "\n", "scaler = MinMaxScaler(feature_range=(-1, 1))\n", "mat = scaler.fit_transform(mat)\n", "\n", "nind = int(mat.shape[0]/nfolds) # number of individuals\n", "\n", "# adds index in the last column\n", "mat = np.c_[mat,np.arange(mat.shape[0])]\n", "\n", "\n", "lineNumber = np.random.choice(np.array(mat.shape[0]), size=nfolds, replace=False)\n", "\n", "lineNumber = np.sort(lineNumber)[::-1]\n", "\n", "for n in range(nfolds):\n", "    totDist.append(np.zeros(mat.shape[0]-nfolds))\n", "\n", "# saves index\n", "for n in range(nfolds):\n", "    \n", "    values.append([lineNumber[n]])    \n", "\n", "\n", "s=[]\n", "for n in range(nfolds):\n", "    s.append(mat[lineNumber[n],:])\n", "    \n", "    mat = np.delete(mat, obj=lineNumber[n], axis=0)\n", "\n", "for n in range(nind-1):    \n", "\n", "    luck = np.random.uniform(0,1,nfolds)\n", "    \n", "    for cycle in range(nfolds):\n", "         # saves the values of index           \n", "\n", "        s[cycle] = np.matlib.repmat(s[cycle], mat.shape[0], 1)\n", "\n", "        sumDist = np.sum( (mat[:,:-1] - s[cycle][:,:-1])**2 , axis=1)   \n", "        totDist[cycle] += sumDist        \n", "                \n", "        # probabilities\n", "        f = totDist[cycle]/np.sum(totDist[cycle]) # normalizing the totdist\n", "        j = 0\n", "        kn = 0\n", "        for val in f:\n", "            j += val        \n", "            if (j > luck[cycle]): # the column was selected\n", "                break\n", "            kn +=1\n", "        lineNumber[cycle] = kn\n", "        \n", "        # delete line of the value added    \n", "        for n_iter in range(nfolds):\n", "            \n", "            totDist[n_iter] = np.delete(totDist[n_iter],obj=lineNumber[cycle], axis=0)\n", "            j= 0\n", "        \n", "        s[cycle] = mat[lineNumber[cycle],:]\n", "        values[cycle].append(int(mat[lineNumber[cycle],-1]))\n", "        mat = np.delete(mat, obj=lineNumber[cycle], axis=0)\n", "\n", "\n", "for n_mod in range(nfolds):\n", "    values[n_mod] = out_train.index[values[n_mod]]"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:02:23.37514Z", "iopub.execute_input": "2021-08-26T05:02:23.375749Z", "iopub.status.idle": "2021-08-26T05:02:35.226386Z", "shell.execute_reply.started": "2021-08-26T05:02:23.375706Z", "shell.execute_reply": "2021-08-26T05:02:35.225183Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#colNames.remove('row_id')\n", "train.replace([np.inf, -np.inf], np.nan,inplace=True)\n", "test.replace([np.inf, -np.inf], np.nan,inplace=True)\n", "qt_train = []\n", "train_nn=train[colNames].copy()\n", "test_nn=test[colNames].copy()\n", "for col in colNames:\n", "    #print(col)\n", "    qt = QuantileTransformer(random_state=21,n_quantiles=2000, output_distribution='normal')\n", "    train_nn[col] = qt.fit_transform(train_nn[[col]])\n", "    test_nn[col] = qt.transform(test_nn[[col]])    \n", "    qt_train.append(qt)\n"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:04:30.177805Z", "iopub.execute_input": "2021-08-26T05:04:30.178272Z", "iopub.status.idle": "2021-08-26T05:04:59.296125Z", "shell.execute_reply.started": "2021-08-26T05:04:30.178233Z", "shell.execute_reply": "2021-08-26T05:04:59.294904Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["train_nn[['stock_id','time_id','target']]=train[['stock_id','time_id','target']]\n", "test_nn[['stock_id','time_id']]=test[['stock_id','time_id']]"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:04:59.298139Z", "iopub.execute_input": "2021-08-26T05:04:59.298607Z", "iopub.status.idle": "2021-08-26T05:04:59.315895Z", "shell.execute_reply.started": "2021-08-26T05:04:59.298556Z", "shell.execute_reply": "2021-08-26T05:04:59.314788Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# making agg features\n", "from sklearn.cluster import KMeans\n", "train_p = pd.read_csv('../input/optiver-realized-volatility-prediction/train.csv')\n", "train_p = train_p.pivot(index='time_id', columns='stock_id', values='target')\n", "\n", "corr = train_p.corr()\n", "\n", "ids = corr.index\n", "\n", "kmeans = KMeans(n_clusters=7, random_state=0).fit(corr.values)\n", "print(kmeans.labels_)\n", "\n", "l = []\n", "for n in range(7):\n", "    l.append ( [ (x-1) for x in ( (ids+1)*(kmeans.labels_ == n)) if x > 0] )\n", "    \n", "\n", "mat = []\n", "matTest = []\n", "\n", "n = 0\n", "for ind in l:\n", "    print(ind)\n", "    newDf = train_nn.loc[train_nn['stock_id'].isin(ind) ]\n", "    newDf = newDf.groupby(['time_id']).agg(np.nanmean)\n", "    newDf.loc[:,'stock_id'] = str(n)+'c1'\n", "    mat.append ( newDf )\n", "    \n", "    newDf = test_nn.loc[test_nn['stock_id'].isin(ind) ]    \n", "    newDf = newDf.groupby(['time_id']).agg(np.nanmean)\n", "    newDf.loc[:,'stock_id'] = str(n)+'c1'\n", "    matTest.append ( newDf )\n", "    \n", "    n+=1\n", "    \n", "mat1 = pd.concat(mat).reset_index()\n", "mat1.drop(columns=['target'],inplace=True)\n", "\n", "mat2 = pd.concat(matTest).reset_index()\n", "mat2 = pd.concat([mat2,mat1.loc[mat1.time_id==5]])"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:04:59.317783Z", "iopub.execute_input": "2021-08-26T05:04:59.318095Z", "iopub.status.idle": "2021-08-26T05:05:01.1562Z", "shell.execute_reply.started": "2021-08-26T05:04:59.318065Z", "shell.execute_reply": "2021-08-26T05:05:01.155105Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["nnn = ['time_id',\n", "     'log_return1_realized_volatility_0c1',\n", "     'log_return1_realized_volatility_1c1',     \n", "     'log_return1_realized_volatility_3c1',\n", "     'log_return1_realized_volatility_4c1',     \n", "     'log_return1_realized_volatility_6c1',\n", "     'total_volume_sum_0c1',\n", "     'total_volume_sum_1c1', \n", "     'total_volume_sum_3c1',\n", "     'total_volume_sum_4c1', \n", "     'total_volume_sum_6c1',\n", "     'trade_size_sum_0c1',\n", "     'trade_size_sum_1c1', \n", "     'trade_size_sum_3c1',\n", "     'trade_size_sum_4c1', \n", "     'trade_size_sum_6c1',\n", "     'trade_order_count_sum_0c1',\n", "     'trade_order_count_sum_1c1',\n", "     'trade_order_count_sum_3c1',\n", "     'trade_order_count_sum_4c1',\n", "     'trade_order_count_sum_6c1',      \n", "     'price_spread_sum_0c1',\n", "     'price_spread_sum_1c1',\n", "     'price_spread_sum_3c1',\n", "     'price_spread_sum_4c1',\n", "     'price_spread_sum_6c1',   \n", "     'bid_spread_sum_0c1',\n", "     'bid_spread_sum_1c1',\n", "     'bid_spread_sum_3c1',\n", "     'bid_spread_sum_4c1',\n", "     'bid_spread_sum_6c1',       \n", "     'ask_spread_sum_0c1',\n", "     'ask_spread_sum_1c1',\n", "     'ask_spread_sum_3c1',\n", "     'ask_spread_sum_4c1',\n", "     'ask_spread_sum_6c1',   \n", "     'volume_imbalance_sum_0c1',\n", "     'volume_imbalance_sum_1c1',\n", "     'volume_imbalance_sum_3c1',\n", "     'volume_imbalance_sum_4c1',\n", "     'volume_imbalance_sum_6c1',       \n", "     'bid_ask_spread_sum_0c1',\n", "     'bid_ask_spread_sum_1c1',\n", "     'bid_ask_spread_sum_3c1',\n", "     'bid_ask_spread_sum_4c1',\n", "     'bid_ask_spread_sum_6c1',\n", "     'size_tau2_0c1',\n", "     'size_tau2_1c1',\n", "     'size_tau2_3c1',\n", "     'size_tau2_4c1',\n", "     'size_tau2_6c1'] "], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:05:08.044273Z", "iopub.execute_input": "2021-08-26T05:05:08.044733Z", "iopub.status.idle": "2021-08-26T05:05:08.05285Z", "shell.execute_reply.started": "2021-08-26T05:05:08.044692Z", "shell.execute_reply": "2021-08-26T05:05:08.051508Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["mat1 = mat1.pivot(index='time_id', columns='stock_id')\n", "mat1.columns = [\"_\".join(x) for x in mat1.columns.ravel()]\n", "mat1.reset_index(inplace=True)\n", "\n", "mat2 = mat2.pivot(index='time_id', columns='stock_id')\n", "mat2.columns = [\"_\".join(x) for x in mat2.columns.ravel()]\n", "mat2.reset_index(inplace=True)"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:05:10.836914Z", "iopub.execute_input": "2021-08-26T05:05:10.837825Z", "iopub.status.idle": "2021-08-26T05:05:11.029051Z", "shell.execute_reply.started": "2021-08-26T05:05:10.83776Z", "shell.execute_reply": "2021-08-26T05:05:11.027714Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import gc\n", "train_nn = pd.merge(train_nn,mat1[nnn],how='left',on='time_id')\n", "test_nn = pd.merge(test_nn,mat2[nnn],how='left',on='time_id')\n", "del mat1,mat2\n", "del train,test\n", "gc.collect()"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:05:13.956795Z", "iopub.execute_input": "2021-08-26T05:05:13.95725Z", "iopub.status.idle": "2021-08-26T05:05:19.768697Z", "shell.execute_reply.started": "2021-08-26T05:05:13.957209Z", "shell.execute_reply": "2021-08-26T05:05:19.767736Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#https://bignerdranch.com/blog/implementing-swish-activation-function-in-keras/\n", "from keras.backend import sigmoid\n", "def swish(x, beta = 1):\n", "    return (x * sigmoid(beta * x))\n", "\n", "from keras.utils.generic_utils import get_custom_objects\n", "from keras.layers import Activation\n", "get_custom_objects().update({'swish': Activation(swish)})\n", "\n", "hidden_units = (128,64,32)\n", "stock_embedding_size = 24\n", "\n", "cat_data = train_nn['stock_id']\n", "\n", "def base_model():\n", "    \n", "    # Each instance will consist of two inputs: a single user id, and a single movie id\n", "    stock_id_input = keras.Input(shape=(1,), name='stock_id')\n", "    num_input = keras.Input(shape=(244,), name='num_data')\n", "\n", "\n", "    #embedding, flatenning and concatenating\n", "    stock_embedded = keras.layers.Embedding(max(cat_data)+1, stock_embedding_size, \n", "                                           input_length=1, name='stock_embedding')(stock_id_input)\n", "    stock_flattened = keras.layers.Flatten()(stock_embedded)\n", "    out = keras.layers.Concatenate()([stock_flattened, num_input])\n", "    \n", "    # Add one or more hidden layers\n", "    for n_hidden in hidden_units:\n", "\n", "        out = keras.layers.Dense(n_hidden, activation='swish')(out)\n", "        \n", "\n", "    #out = keras.layers.Concatenate()([out, num_input])\n", "\n", "    # A single output: our predicted rating\n", "    out = keras.layers.Dense(1, activation='linear', name='prediction')(out)\n", "    \n", "    model = keras.Model(\n", "    inputs = [stock_id_input, num_input],\n", "    outputs = out,\n", "    )\n", "    \n", "    return model\n"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:07:40.604384Z", "iopub.execute_input": "2021-08-26T05:07:40.605014Z", "iopub.status.idle": "2021-08-26T05:07:40.61753Z", "shell.execute_reply.started": "2021-08-26T05:07:40.604977Z", "shell.execute_reply": "2021-08-26T05:07:40.616228Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Function to calculate the root mean squared percentage error\n", "def rmspe(y_true, y_pred):\n", "    return np.sqrt(np.mean(np.square((y_true - y_pred) / y_true)))\n", "\n", "# Function to early stop with root mean squared percentage error\n", "def feval_rmspe(y_pred, lgb_train):\n", "    y_true = lgb_train.get_label()\n", "    return 'RMSP<PERSON>', rmspe(y_true, y_pred), False"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:07:19.618262Z", "iopub.execute_input": "2021-08-26T05:07:19.618705Z", "iopub.status.idle": "2021-08-26T05:07:19.623787Z", "shell.execute_reply.started": "2021-08-26T05:07:19.618669Z", "shell.execute_reply": "2021-08-26T05:07:19.622906Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["target_name='target'\n", "scores_folds = {}\n", "model_name = 'NN'\n", "pred_name = 'pred_{}'.format(model_name)\n", "\n", "n_folds = 5\n", "kf = model_selection.KFold(n_splits=n_folds, shuffle=True, random_state=2020)\n", "scores_folds[model_name] = []\n", "counter = 1\n", "\n", "features_to_consider = list(train_nn)\n", "\n", "features_to_consider.remove('time_id')\n", "features_to_consider.remove('target')\n", "try:\n", "    features_to_consider.remove('pred_NN')\n", "except:\n", "    pass\n", "\n", "\n", "train_nn[features_to_consider] = train_nn[features_to_consider].fillna(train_nn[features_to_consider].mean())\n", "test_nn[features_to_consider] = test_nn[features_to_consider].fillna(train_nn[features_to_consider].mean())\n", "\n", "train_nn[pred_name] = 0\n", "test_nn[target_name] = 0\n", "test_predictions_nn = np.zeros(test_nn.shape[0])\n", "\n", "for n_count in range(n_folds):\n", "    print('CV {}/{}'.format(counter, n_folds))\n", "    \n", "    indexes = np.arange(nfolds).astype(int)    \n", "    indexes = np.delete(indexes,obj=n_count, axis=0) \n", "    \n", "    indexes = np.r_[values[indexes[0]],values[indexes[1]],values[indexes[2]],values[indexes[3]]]\n", "    \n", "    X_train = train_nn.loc[train_nn.time_id.isin(indexes), features_to_consider]\n", "    y_train = train_nn.loc[train_nn.time_id.isin(indexes), target_name]\n", "    X_test = train_nn.loc[train_nn.time_id.isin(values[n_count]), features_to_consider]\n", "    y_test = train_nn.loc[train_nn.time_id.isin(values[n_count]), target_name]\n", "    \n", "    #############################################################################################\n", "    # NN\n", "    #############################################################################################\n", "    \n", "    model = base_model()\n", "    \n", "    model.compile(\n", "        keras.optimizers.<PERSON>(learning_rate=0.006),\n", "        loss=root_mean_squared_per_error\n", "    )\n", "    \n", "    try:\n", "        features_to_consider.remove('stock_id')\n", "    except:\n", "        pass\n", "    \n", "    num_data = X_train[features_to_consider]\n", "    \n", "    scaler = MinMaxScaler(feature_range=(-1, 1))         \n", "    num_data = scaler.fit_transform(num_data.values)    \n", "    \n", "    cat_data = X_train['stock_id']    \n", "    target =  y_train\n", "    \n", "    num_data_test = X_test[features_to_consider]\n", "    num_data_test = scaler.transform(num_data_test.values)\n", "    cat_data_test = X_test['stock_id']\n", "\n", "    model.fit([cat_data, num_data], \n", "              target,               \n", "              batch_size=2048,\n", "              epochs=1000,\n", "              validation_data=([cat_data_test, num_data_test], y_test),\n", "              callbacks=[es, plateau],\n", "              validation_batch_size=len(y_test),\n", "              shuffle=True,\n", "             verbose = 1)\n", "\n", "    preds = model.predict([cat_data_test, num_data_test]).reshape(1,-1)[0]\n", "    \n", "    score = round(rmspe(y_true = y_test, y_pred = preds),5)\n", "    print('Fold {} {}: {}'.format(counter, model_name, score))\n", "    scores_folds[model_name].append(score)\n", "    \n", "    tt =scaler.transform(test_nn[features_to_consider].values)\n", "    #test_nn[target_name] += model.predict([test_nn['stock_id'], tt]).reshape(1,-1)[0].clip(0,1e10)\n", "    test_predictions_nn += model.predict([test_nn['stock_id'], tt]).reshape(1,-1)[0].clip(0,1e10)/n_folds\n", "    #test[target_name] += model.predict([test['stock_id'], test[features_to_consider]]).reshape(1,-1)[0].clip(0,1e10)\n", "       \n", "    counter += 1\n", "    features_to_consider.append('stock_id')"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:07:43.746494Z", "iopub.execute_input": "2021-08-26T05:07:43.746928Z", "iopub.status.idle": "2021-08-26T05:22:01.397153Z", "shell.execute_reply.started": "2021-08-26T05:07:43.746892Z", "shell.execute_reply": "2021-08-26T05:22:01.395923Z"}, "trusted": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["test_nn[\"row_id\"] = test_nn[\"stock_id\"].astype(str) + \"-\" + test_nn[\"time_id\"].astype(str) \n", "test_nn[target_name] = (test_predictions_nn+predictions_lgb)/2\n", "\n", "score = round(rmspe(y_true = train_nn[target_name].values, y_pred = train_nn[pred_name].values),5)\n", "print('RMSPE {}: {} - Folds: {}'.format(model_name, score, scores_folds[model_name]))\n", "\n", "display(test_nn[['row_id', target_name]].head(3))\n", "test_nn[['row_id', target_name]].to_csv('submission.csv',index = False)\n", "#test[['row_id', target_name]].to_csv('submission.csv',index = False)\n", "#kmeans N=5 [0.2101, 0.21399, 0.20923, 0.21398, 0.21175]"], "metadata": {"execution": {"iopub.status.busy": "2021-08-26T05:24:19.998917Z", "iopub.execute_input": "2021-08-26T05:24:19.999694Z", "iopub.status.idle": "2021-08-26T05:24:20.025722Z", "shell.execute_reply.started": "2021-08-26T05:24:19.99965Z", "shell.execute_reply": "2021-08-26T05:24:20.024614Z"}, "trusted": true}, "execution_count": null, "outputs": []}]}