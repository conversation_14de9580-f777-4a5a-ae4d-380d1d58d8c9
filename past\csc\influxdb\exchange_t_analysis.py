# -*- coding: utf-8 -*-
"""
Created on Mon Oct 18 13:15:19 2021

@author: dell
"""
from influxdb import InfluxDBClient
import datetime
import time
import numpy as np
import pandas as pd
# from OmmDatabase import OmmDatabase

import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['KaiTi']
plt.rcParams['font.serif'] = ['KaiTi']
plt.rcParams['axes.unicode_minus'] = False 

#%%
client = InfluxDBClient('202.0.3.209',8989,'','','testbase') #大商所
# client = InfluxDBClient('202.0.3.200',8989,'','','testbase') #郑商所

import datetime
beginStr = '2021-10-27T13:30:00.0Z'
endStr = '2021-10-27T15:10:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000


# #%%
points = result.get_points()
l=[]
for d in points:
    l.append(d)
print(len(l))
    
import pandas as pd
import datetime
df = pd.DataFrame(l)

#%%






#%%

# df_insid_md = pd.DataFrame(df_mkt['insid_md'].drop_duplicates())
# df_insid_md.to_csv('insid_md.csv')

df_insid_md = pd.read_csv('insid_md.csv')
insid_md_list = list(df_insid_md['insid_md'])

#%%
l=[]
for iid in insid_md_list:
    result = client.query("select * from testdalian where insid_md='"+iid+"' and time >= %d and time <= %d;"%(begin,end))
    points = result.get_points()     
    for d in points:
        l.append(d)
    print(iid+'读取完毕')
df = pd.DataFrame(l)
df['UnixStamp'] = df['local_t']/10**9
#%%
df.index = df['UnixStamp']
df.sort_index(inplace = True)
    
#%%
df_mkt = df.copy()
df_mkt_time = df_mkt[['time', 'local_t', 'exchange_t', 'insid_md', 'UnixStamp']]



#%%
df_test = df_mkt_time.to_dict('records')
row = df_test[0]
UnixStamp0 = row['UnixStamp']
exchange_t0 = row['exchange_t']
UnixStamp_l = row['UnixStamp']
exchange_t_l = row['exchange_t']

l = []
l0 = []

# 分成一坨一坨
for row in df_test:
    UnixStamp_n = row['UnixStamp']
    exchange_t_n = row['exchange_t']
    if UnixStamp_n - UnixStamp0 > 0.2:
        UnixStamp0 = UnixStamp_n
        exchange_t0 = exchange_t_n
        l.append(l0.copy())
        l0 = []
    l0.append(row)
    if UnixStamp_n ==  df_test[-1]['UnixStamp']:
        UnixStamp0 = UnixStamp_n
        exchange_t0 = exchange_t_n
        l.append(l0.copy())    

#%%
# 记录每一簇中的最大值
exchange_t_max = []
for l0 in l:
    t_max = 0
    for row in l0:
        if row['exchange_t'] > t_max:
            t_max = row['exchange_t']
    t_max = t_max%250
    exchange_t_max.append(t_max)

#%%
plt.figure()
plt.plot(exchange_t_max)
plt.show()


#%%
# basic_t = 75600178-250
basic_t = 75601347
l_n = []
df_new = []
for l0 in l:
    t_max = 0
    for row in l0:
        if row['exchange_t'] > t_max:
            t_max = row['exchange_t']    
    i = int((t_max-basic_t)/250)
    l_n.append((i, l0))

#%%
delay_min = []
delta_t0 = l_n[0][1][0]['UnixStamp'] - basic_t/1000
for i, l0 in l_n:
    basic_exchange_t = basic_t + 250*(i+1)
    delta_t = l0[0]['UnixStamp']-basic_exchange_t/1000
    if np.abs(delta_t-delta_t0) < 0.2:
        delay_min.append((l0[0]['UnixStamp'], delta_t))
    delta_t0 = delta_t

    
delay_min = pd.DataFrame(delay_min)
delay_min.columns = ['UnixStamp', 'delta_t']    
delay_min.index = delay_min['UnixStamp']

#%%
delay_min_pg = []
for i, l0 in l_n:
    basic_exchange_t = basic_t + 250*(i+1)
    for row in l0:
        if row['insid_md'] == 'pg2112':
            delay_min_pg.append((row['UnixStamp'], row['UnixStamp']-basic_exchange_t/1000))
            break
    
    
delay_min_pg = pd.DataFrame(delay_min_pg)
delay_min_pg.columns = ['UnixStamp', 'delta_t']    
delay_min_pg.index = delay_min_pg['UnixStamp']

#%%
delay_max = []
for i, l0 in l_n:
    basic_exchange_t = basic_t + 250*(i+1)
    delay_max.append((l0[-1]['UnixStamp'], l0[-1]['UnixStamp']-basic_exchange_t/1000))
    


    
delay_max = pd.DataFrame(delay_max)
delay_max.columns = ['UnixStamp', 'delta_t']    
delay_max.index = delay_max['UnixStamp']
    
    
#%%
max_min = []
for i, l0 in l_n:
    max_min.append(l0[-1]['UnixStamp']-l0[0]['UnixStamp'])
    
    
    
#%%
delta_t = []
t0 = l[0][0]['UnixStamp']
for row in l[1:]:
    t1 = row[0]['UnixStamp']
    delta_t.append(t1-t0)
    if t1-t0>0.3:
        print(row[0]['time'], t1-t0)
    
    t0 = t1

#%%
plt.figure()
plt.plot(delta_t)
plt.show()

#%%
df_test1 = df_mkt[df_mkt.insid_md=='pp2201']



#%%
plt.figure()
plt.plot(delay_min['delta_t'], label = 'min', alpha = 0.7)
# plt.plot(delay_min_pg['delta_t'], label = 'pg', alpha = 0.7)
# plt.plot(delay_max['delta_t'], label = 'max', alpha = 0.7)

plt.legend()
plt.show()

#%%
plt.figure()
plt.plot(max_min)
plt.show()    
        
    
    
