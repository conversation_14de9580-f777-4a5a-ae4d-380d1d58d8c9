# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-24
from WindPy import w
import pymssql
from datetime import datetime

server = '***********'
user = 'Alex'
password = '789456'
dt = datetime.now()
# beginDate = "2017-06-24"
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')
cursor = conn.cursor()

n = 0
list2 = []


def tosql(zzcode):
    # sql = "INSERT INTO ZZStks_IPO VALUES (%s,%d, %s, %d, %d, %d, %d, %d, %d,%d)"

    # 通过wset来取数据集数据
    print('\n\n' + '-----通过wset来取数据集数据,获取全部%s代码列表-----' % zzcode + '\n')
    stockcode = w.wset("sectorconstituent", "date=2017-08-23;windcode=%s" % zzcode)
    print(stockcode)

    for j in range(0, len(stockcode.Data[1])):

        sql = "select 1 from ZZStks_IPO where STKID=%s"
        cursor.execute(sql, (str(stockcode.Data[1][j], )))

        reslist = cursor.fetchall()
        print j
        if reslist:
            pass
        else:
            list2 = list2.append(str(stockcode.Data[1][j]))
            n = n + 1
        conn.commit()


tosql(zzcode="000905.SH")  # 中证500
tosql(zzcode="000300.SH")  # 沪深300

print(list2, n)

conn.close()
