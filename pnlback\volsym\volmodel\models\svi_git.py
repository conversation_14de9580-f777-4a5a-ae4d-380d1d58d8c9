import numpy as np
import scipy as sp
import scipy.optimize as opt
from scipy.optimize import minimize
import time
import pandas as pd
import matplotlib.pyplot as plt
import sys
import os
sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])
from pnlback.volsym.volmodel.utils import get_data,find_best_method

class VolatilityModel:
    params_name = ['a','d','c','m','sigma']
    def __init__(self,params=None):
        self.params = params

    def calculate(self, x):
        # iv=sqrt(totalv)/sqrt(t)
        if self.params is None:
            raise ValueError("params is not set")
        a,d,c,m,sigma = self.params
        y = (x-m)/sigma
        return a+d*y+c*np.sqrt(np.square(y)+1)

    # 计算a,d,c
    def calc_adc(self,totalv,x,_m,_sigma):
        y = (x-_m)/_sigma
        s = max(_sigma,1e-6)
        bnd = ((0,0,0),(max(totalv.max(),1e-6),2*np.sqrt(2)*s,2*np.sqrt(2)*s))
        z = np.sqrt(np.square(y)+1)
        
        # 此处等价于坐标轴旋转45°，这样写运行更快
        A = np.column_stack([np.ones(len(totalv)),np.sqrt(2)/2*(y+z),np.sqrt(2)/2*(-y+z)])
        
        a,d,c = opt.lsq_linear(A,totalv,bnd,tol=1e-12,verbose=False).x
        return a,np.sqrt(2)/2*(d-c),np.sqrt(2)/2*(d+c)
    
    def calc_iv(self,t,totalv):
        return np.sqrt(totalv/t)

def calc_rmse(model,x,weights,totalv,t):
    return np.sum(np.square(weights*(model.calculate(x) - totalv)))

class fits:
    bounds = [
        (1e-5, np.inf),  # a: amin到最大方差
        (1e-3, np.inf),  # b: bmin到1
        (-50, 50),  # rho: -1到1
        (-np.inf, np.inf),  # m: 2倍最小k到2倍最大k
        (0, np.inf)  # sigma: 0.01到1
    ]
    initial_params = ([0.04, 0.1, 0.1, 0.1, 0.1])
    def fit(self, x, weights, totalv, t, maxiter=10, exit=1e-12, verbose=False,method=None):
        """拟合波动率模型并计算特征值"""
        # 创建模型
        model = VolatilityModel()
        
        # 设置参数优化边界
        x_min = x.min() if hasattr(x, 'min') else min(x)
        x_max = x.max() if hasattr(x, 'max') else max(x)
        bounds = ((2*min(x_min, 0), 2*x_max), (1e-6, 1))

        initial_params = [0.05, 0.1]

        # 定义目标函数
        def objective(msigma):
            _m, _sigma = msigma
            _a, _d, _c = model.calc_adc(totalv, x, _m, _sigma)
            model.params = [_a, _d, _c, _m, _sigma]
            return calc_rmse(model,x,weights,totalv,t)

        # 执行拟合
        opt_rmse = 1
        for i in range(1, 1000):
            best_result, best_rmse, best_method = find_best_method(objective, initial_params, bounds,verbose=verbose,method=method)
            m_star, sigma_star = best_result.x
            a_star, d_star, c_star = model.calc_adc(totalv, x, m_star, sigma_star)
            model.params = [a_star, d_star, c_star, m_star, sigma_star]
            opt_rmse1 = calc_rmse(model,x,weights,totalv,t)
            if i > 1 and opt_rmse - opt_rmse1 < exit:
                break
            opt_rmse = opt_rmse1
            initial_params = [m_star, sigma_star]

        sigma_fit = model.calculate(x)
        results = 0

        return sigma_fit, model, best_rmse, best_method,0


def fit_svi_model(config, exp_data, exp, last_exp_data):
    """使用SVI模型拟合波动率曲线

    Args:
        config: 配置参数
        exp_data: 期权数据
        exp: 到期日
        last_exp_data: 上一次的拟合结果

    Returns:
        tuple: (sigma_fit, voltime, derivatives)
    """
    # 准备输入数据
    F,K,T,x,totalv,imp_vol,weights,x_raw = get_data(exp_data,exe_cut=config.get('exe_cut',np.exp(1)))
    
    totalv_fit, model,opt_rmse,best_method,results = fits().fit(x, weights,totalv,T,verbose=False,method="Powell")
    fit_totalv=model.calculate(np.log(exp_data['K']/F))
    fit_imp_vol=np.sqrt(fit_totalv)/np.sqrt(T)

    atm_features = {'atm_vol': 0, 'skew': 0, 'convexity': 0, 'otm_slope': 0, 'itm_slope': 0}

    voltime = {
        **dict(zip(model.params_name, model.params)),  # SVI参数
        **atm_features,  # ATM特征
        'rmse_error': opt_rmse,
        'best_method': best_method,
    }

    return exp, fit_imp_vol, voltime
