# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-29

import math
from datetime import datetime, timedelta

import os

import numpy as np
import pandas as pd
import xlwings as xw

import pyodbc

from WindPy import w


def volminlist(t):
    # # Specifying the ODBC driver, server name, database, etc. directly
    # conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
    #
    # sql = 'SELECT [DateTime],[Code],[OpenPrice],[High],[Low],[ClosePrice],[Volume],[Amount],[oi] ' \
    #       'FROM [Alex].[dbo].[M_Future_min]'
    #
    # pf = pd.read_sql(sql, conn, index_col='DateTime', coerce_float=True, params=None, parse_dates=True, columns=None,
    #                  chunksize=None)
    # conn.commit()
    # conn.close()

    wsd_data = w.wsi(code, "open,high,low,close", begin, dt, "")
    pf = pd.DataFrame(wsd_data.Data, index=wsd_data.Fields, columns=wsd_data.Times)
    pf = pf.T  # 将矩阵转置
    print(pf)

    # pf2 = pf.ix['2017-07-24', :]
    # print(pf2)
    pf['date'] = 0
    pf['time'] = 0
    pf['log_return'] = 0
    pf['daily_vol'] = 0
    pf['ann_vol'] = 0

    pf.index = pd.DatetimeIndex(pf.index)

    asd = pf.index
    pydate_array = asd.to_pydatetime()
    time_only_array = np.vectorize(lambda s: s.strftime('%H:%M:%S'))(pydate_array)
    time_only_series = pd.Series(time_only_array)
    pf.insert(0, 'Time1', time_only_series.values)
    pf['Time1'] = pd.to_datetime(pf['Time1'], format='%H:%M:%S')
    # pf = pf[pf['Time1'] > "1900-01-01 09:30:00"]
    pf = pf[pf['Time1'] != "1900-01-01 13:00:00"]
    for ti in t:
        orderbyt(pf, ti)
    return sqllist2


def orderbyt(pf, t):
    pf0 = pf.resample(t).asfreq()
    pf0 = pf0.dropna(axis=0, how='any')  # drop all rows that have any NaN values
    pf1 = pf0.to_period('D')

    # pf0 = pf[pf['DateTime'] == '0']
    pf12 = pf1.index.tolist()
    codedata = list(set(pf12))
    codedata.sort()

    print(len(codedata))

    if not sqllist2:
        num = 0

        wdata = w.wsd(code, "pre_close,high,low,close", str(codedata[19]), str(codedata[-1]), "")

        for ii in range(19, len(codedata)):
            i = codedata[ii]
            sqllist = []
            print(i)
            num += 1

            sqllist.append(str(i))
            for iii in range(0, len(wdata.Fields)):
                sqllist.append(wdata.Data[iii][ii - 19])
            # sqllist.append(stan)
            # sqllist.append(stan * math.sqrt(252 * len(aarray)))

            sqllist2.append(sqllist)

    num = 0

    for j in range(1, len(pf0.index)):
        index2 = pf0.index[j]
        num += 1
        print(num, "%.2f%%" % (num / len(pf0.index) * 100))
        b = float(str(pf0.loc[pf0.index[j], ['close']].values[0]))
        c = float(str(pf0.loc[pf0.index[j - 1], ['close']].values[0]))
        a = b / c
        a = math.log(a)
        pf0.loc[index2, ['log_return']] = a
    num = 0
    for ii in range(19, len(codedata)):
        i = codedata[ii]
        print(i)
        num += 1
        pf5 = pf0[str(codedata[ii - 19]):str(i)]

        print(num)

        aarray = pf5.loc[:, 'log_return']
        stan = np.std(aarray)
        # sqllist2[ii].append(stan)
        sqllist2[ii - 19].append(stan * math.sqrt(252 * 60 / int(t[:-1]) * 4))
        # sqllist2[ii-19].append(stan * math.sqrt(252 * len(aarray)))


def handlefee(server,user,password,month,month2):
    # # Specifying the ODBC driver, server name, database, etc. directly
    conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Li', PWD=password, UID=user)
    
    sql = u"select [DATE],[手续费] from [Li].[dbo].[opt_syb] where acctid = '************' and  date >= '%s' order by date" % (month+'-01')
    
    pf0 = pd.read_sql(sql, conn, index_col='DATE', coerce_float=True, params=None, parse_dates=True, columns=None,
                     chunksize=None)
    # pf0 = pf0[month]
    conn.commit()
    # conn.close()

    sql2 = u"SELECT Date,handlingfee  FROM [Li].[dbo].[OptionHandlingFeeMonthly] where Account = '************' order by [Date]" 
    
    pf2 = pd.read_sql(sql2, conn, index_col='Date', coerce_float=True, params=None, parse_dates=True, columns=None,
                     chunksize=None)

    pf0.iloc[-1,0]=pf0.iloc[-1,0]-pf2.iloc[-1,0]

    print()

    conn.commit()
    conn.close()
    # pf['DATE'] = pd.to_datetime(pf['DATE'])
    # pf = pf.set_index('DATE')
    # pf0 = pf0[month]

    return pf0


if __name__ == "__main__":
    server = '10.36.18.61'
    user = 'sam'
    password = '1qazxsw2'

    dt = datetime.now()
    ##########################################################
    # # 日期设置
    dt ="2019-06-01"
    dt = datetime.strptime(dt,"%Y-%m-%d")
    ##########################################################
    dtt = dt - timedelta(days=10)
    month = datetime.strftime(dtt,"%Y-%m")
    month2 = datetime.strftime(dt,"%Y-%m")

    begin = dt - timedelta(days=90)
    code = "IH.CFE"
    t = ['5T', '30T', '60T']
    filePath = u'D:\\onedrive\\中泰衍生\\做市组业务工作\\数据报告\\月报\\月报数据.xlsx'
    plpath = u'D:\\onedrive\\中泰衍生\\做市组业务工作\\数据报告\\盘后损益\\盘后损益-Rt.xlsm'

    hflist=handlefee(server,user,password,month,month2)

    w.start()

    sqllist2 = []
    sqllist2 = volminlist(t)

    pf = pd.DataFrame(sqllist2, index=None, columns=['DATE', 'pre', 'high', 'low', 'close', '5', '30', '60'])
    pf['mean'] = pf.loc[:, ['5', '30', '60']].mean(axis=1)

    num = 0
    pf.loc[:, 'zd'] = pf.loc[:, 'hl'] = 0
    for j in range(0, len(pf.index)):
        index2 = pf.index[j]
        num += 1
        print(num, "%.2f%%" % (num / len(pf.index) * 100))
        b = float(str(pf.loc[pf.index[j], ['close']].values[0]))
        c = float(str(pf.loc[pf.index[j], ['pre']].values[0]))
        a = b / c
        a = a - 1
        pf.loc[index2, 'zd'] = a
        pf.loc[index2, 'hl'] = (pf.loc[index2, 'high'] - pf.loc[index2, 'low']) / c

    pf['DATE'] = pd.to_datetime(pf['DATE'])
    pf = pf.set_index('DATE')
    pf = pf[month]
    # pf.to_excel('vofutmin20.xls')

    result = pd.concat([pf, hflist], axis=1, join='inner')

    app = xw.App(visible=False, add_book=False)
    app.display_alerts = False

    wb = app.books.open(filePath)
    app.calculate()

    # sht0 = wb.sheets[0]
    sht1 = wb.sheets['HV']

    sht1.range('A1').expand().clear_contents()

    sht1.range('A1').value = result
    app.calculate()

    wb2 = app.books.open(filePath)
    sht2 = wb.sheets['HV']

    wb.save()
    app.quit()
    app.kill()
