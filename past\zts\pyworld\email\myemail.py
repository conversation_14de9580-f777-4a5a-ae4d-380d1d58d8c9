# !/usr/bin/python
# -*- coding: utf-8 -*-
import smtplib
from email import encoders
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from email.utils import parseaddr, formataddr
from email.header import Header
from email import encoders
from datetime import datetime
# sender是邮件发送人邮箱，passWord是服务器授权码，mail_host是服务器地址（这里是QQsmtp服务器）
import os

sender = '<EMAIL>'  #
passWord = 'cwffwmegabqcbjed'
mail_host = 'smtp.qq.com'


def _format_addr(s):
    name, addr = parseaddr(s)
    # return formataddr(
    #     (Header(name, 'utf-8').encode(), addr.encode('utf-8') if isinstance(addr, str) else addr))
    return formataddr((Header(name, 'utf-8').encode(), addr))


def sendemail(sendPath1, receivers):
    # 设置email信息
    msg = MIMEMultipart()
    # 邮件主题
    msg['Subject'] = '中泰证券50ETF期权做市业务2018年第一季度压力测试报告'  # input(u"{'请输入邮件主题：'}")
    # 发送方信息
    msg['From'] = _format_addr(u'李宁 <%s>' % sender)
    # 邮件正文是MIMEText:
    msg_content = 'Hello,\n\n\n    附件为中泰证券50ETF期权做市业务数据报送 \n\n\n柯正修\n'  # input(u"{'请输入邮件主内容:'}")
    msg.attach(MIMEText(msg_content, 'plain', 'utf-8'))
    # 添加附件就是加上一个MIMEBase，从本地读取一个图片:
    with open(sendPath1, 'rb') as f:
        # 设置附件的MIME和文件名，这里是jpg类型,可以换png或其他类型:
        mime = MIMEBase("application", 'xlsx')
        # 加上必要的头信息:
        mime.add_header('Content-Disposition', 'attachment', filename=('gbk', '', os.path.split(sendPath1)[-1]))
        mime.add_header('Content-ID', os.path.split(sendPath1)[-1])
        # mime.add_header('X-Attachment-Id', os.path.split(sendPath1)[-1])

        # 把附件的内容读进来:
        mime.set_payload(f.read())
        # 用Base64编码:
        encoders.encode_base64(mime)
        # 添加到MIMEMultipart:
        msg.attach(mime)

    # 首先是xlsx类型的附件
    # xlsxpart = MIMEApplication(open(sendPath1, 'rb').read())
    # a = os.path.split(sendPath1)[-1]
    # xlsxpart.add_header('Content-Disposition', 'attachment', filename=a)
    # msg.attach(xlsxpart)

    # mp3类型的附件
    # mp3part = MIMEApplication(open('kenny.mp3', 'rb').read())
    # mp3part.add_header('Content-Disposition', 'attachment', filename='benny.mp3')
    # msg.attach(mp3part)

    # pdf类型附件
    # part = MIMEApplication(open('foo.pdf', 'rb').read())
    # part.add_header('Content-Disposition', 'attachment', filename="foo.pdf")
    # msg.attach(part)

    # 登录并发送邮件
    try:
        # QQsmtp服务器的端口号为465或587
        s = smtplib.SMTP_SSL(mail_host, 465)
        s.set_debuglevel(1)
        s.login(sender, passWord)
        # 给receivers列表中的联系人逐个发送邮件
        for i in range(len(receivers)):
            to = receivers[i]
            msg['To'] = to
            s.sendmail(sender, to, msg.as_string())
            print('Success!')
        s.quit()
        print("All emails have been sent over!")
    except smtplib.SMTPException as e:
        print("Falied,%s", e)


if __name__ == "__main__":
    # receivers是邮件接收人，用列表保存，可以添加多个
    receivers = [ '<EMAIL>', ]

    sendPath1 = u'D:\\onedrive\\中泰衍生\\做市组日常工作\\业务数据报送\\HIS\\中泰证券做市业务数据报送--%s.xlsx' % '20180207'
    sendemail(sendPath1, receivers)
