# -*- coding: utf-8 -*-

import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random


class QueueStrategy(StrategyTemplate):
    """"""

    author = "vnpy"

    buy_price = 0
    short_price = 0

    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'upper',
        'lower',
        'minsize',
        'skew_pos',
        'alone_order',
        'ioc_flag',
        'max_QT_buy',
        'max_QT_sell',
        'order_layer',
        'Q2_flag'

    ]
    variables = [
        'buy_price',
        'short_price'
    ]

    def __init__(
            self,
            strategy_engine: StrategyEngine,
            strategy_name: str,
            vt_symbol: str,
            setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        self.buy_refer_orderids = None
        self.short_refer_orderids = None

    def getFairPrice(self, lastP, askP, bidP, edgeP):
        fair = lastP
        if askP > 0 and bidP > 0:
            if askP - bidP <= edgeP:  # 有流动性，中间价
                fair = 0.5 * (askP + bidP)
            else:  # 流动性不足, 不变
                if lastP > askP:
                    fair = askP
                if lastP < bidP:
                    fair = bidP
        return fair

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net = 0
        self.fair_maker = 0
        self.fair_refer = 0
        self.rCount = 0
        self.mCount = 0
        self.pauseCount = 0
        self.isQuoting = False
        self.lastTicks = {self.maker: 0, self.refer: 0}
        self.comments = None
        self.ewma = 0
        self.Basis = 0
        self.isQuoting = True
        self.offer_list = []
        self.offer_list_head = []
        self.basisCount = 0
        self.basislist = np.zeros(99999999)
        self.maker_buy_price = 0
        self.maker_short_price = 0
        self.refer_buy_price = 0
        self.refer_short_price = 0
        self.maker_fair_order = 0
        self.refer_fair_order = 0
        self.last_net = 0
        self.trade_price = 0
        self.last_tick = None
        self.QT_b = 0
        self.QT_s = 0

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        # 对比tick
        for key in ticks:
            if ticks[key] != self.lastTicks[key]:
                if key == self.maker:
                    self.on_tick_maker(ticks[key])
                if key == self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        # 决定on_tick
        # 储存之前的tick

    def edge_b(self, tick, last_tick):  # 计算量比
        if (tick.bid_volume_1 / tick.ask_volume_1 > self.upper and tick.bid_volume_1 > self.minsize) or (
                tick.ask_price_1 > last_tick.ask_price_1 > tick.bid_price_1):
            edge = 1
        elif tick.bid_volume_1 / tick.ask_volume_1 < self.lower:
            edge = -1
        else:
            edge = 0
        return edge

    def edge_s(self, tick, last_tick):  # 计算量比
        if (tick.ask_volume_1 / tick.bid_volume_1 > self.upper and tick.ask_volume_1 > self.minsize) or (
                tick.bid_price_1 < last_tick.bid_price_1 < tick.ask_price_1):
            edge = 1
        elif tick.ask_volume_1 / tick.bid_volume_1 < self.lower:
            edge = -1
        else:
            edge = 0
        return edge

    def on_tick_refer(self, tick):
        self.rCount += 1
        return

    def on_end(self, tick):
        dt = tick.datetime
        if (dtime(22, 55) < dt.time() < dtime(22, 56)) or (dtime(11, 28) < dt.time() < dtime(11, 29)) or (
                dtime(14, 55) < dt.time() < dtime(14, 56)):
            self.cancel_all()
            if self.net > 0:
                self.short(self.maker, tick.bid_price_1, abs(self.net), 'end')
            if self.net < 0:
                self.buy(self.maker, tick.ask_price_1, abs(self.net), 'end')

    def on_tick_maker(self, tick):

        last_tick = self.last_tick
        self.mCount += 1
        ts = self.priceticks[self.refer]
        net = self.net
        lots = self.lots

        if self.mCount > 10:

            orders = list(self.strategy_engine.active_limit_orders.items())
            for vt_orderid, order in orders:
                # skew_pos内反edge则盘口撤单
                if self.skew_pos >= self.net >= - self.skew_pos:
                    if order.price == tick.bid_price_1 and self.edge_b(tick, last_tick) == -1:
                        self.cancel_order(vt_orderid)
                    if order.price == tick.ask_price_1 and self.edge_s(tick, last_tick) == -1:
                        self.cancel_order(vt_orderid)
                    # 超过skewpos反向订单只能edge=1不撤
                if self.net > self.skew_pos and order.price == tick.bid_price_1:
                    self.cancel_order(vt_orderid)
                if self.net < -self.skew_pos and order.price == tick.ask_price_1:
                    self.cancel_order(vt_orderid)
                # 增加持仓 edge=0 唯一单撤单
                if (order.price == tick.bid_price_1 and self.edge_b(tick, last_tick) == 0
                        and self.net >= 0 and tick.bid_volume_1 <= self.alone_order):
                    self.cancel_order(vt_orderid)
                if (order.price == tick.ask_price_1 and self.edge_s(tick, last_tick) == 0
                        and self.net <= 0 and tick.ask_volume_1 <= self.alone_order):
                    self.cancel_order(vt_orderid)

            if self.MMTrading:
                # 先判断是否需要balance
                orders = list(self.strategy_engine.active_limit_orders.items())
                bid1_orders = np.sum([order.price == tick.bid_price_1 for vt_orderid, order in orders])
                ask1_orders = np.sum([order.price == tick.ask_price_1 for vt_orderid, order in orders])
                if self.net > self.skew_pos and ask1_orders == 0:
                    self.short(self.maker, tick.ask_price_1, lots, 'balance')
                if self.net < -self.skew_pos and bid1_orders == 0:
                    self.buy(self.maker, tick.bid_price_1, lots, 'balance')

                    # 判断反手
                orders = list(self.strategy_engine.active_limit_orders.items())
                bid1_orders = np.sum([order.price == tick.bid_price_1 for vt_orderid, order in orders])
                ask1_orders = np.sum([order.price == tick.ask_price_1 for vt_orderid, order in orders])
                if self.ioc_flag:
                    if self.skew_pos >= self.net >= - self.skew_pos:
                        pos_chg = self.net - self.last_net
                        if pos_chg > 0 and self.net > 0 and ask1_orders == 0 and tick.ask_price_1 > self.trade_price:
                            self.short(self.maker, tick.ask_price_1, lots, 'reverse')
                        if pos_chg < 0 and self.net < 0 and bid1_orders == 0 and tick.bid_price_1 < self.trade_price:
                            self.buy(self.maker, tick.bid_price_1, lots, 'reverse')

                            #主动挂单
                orders = list(self.strategy_engine.active_limit_orders.items())
                bid1_orders = np.sum([order.price == tick.bid_price_1 for vt_orderid, order in orders])
                ask1_orders = np.sum([order.price == tick.ask_price_1 for vt_orderid, order in orders])
                if self.Q2_flag:
                    if bid1_orders == 0:
                        if self.edge_b(tick, last_tick) == 1:
                            self.buy(self.maker, tick.bid_price_1, lots, 'Q2')
                    if ask1_orders == 0:
                        if self.edge_s(tick, last_tick) == 1:
                            self.short(self.maker, tick.ask_price_1, lots, 'Q2')

                quote_prices = []
                orders = list(self.strategy_engine.active_limit_orders.items())
                for vt_orderid, order in orders:
                    quote_prices.append(order.price)

                    # 挂单
                for i in range(self.order_layer, 6):
                    if tick.__getattribute__(f'bid_price_{i}') not in quote_prices:
                        self.buy(self.maker, tick.__getattribute__(f'bid_price_{i}'), lots, 'MM')
                    if tick.__getattribute__(f'ask_price_{i}') not in quote_prices:
                        self.short(self.maker, tick.__getattribute__(f'ask_price_{i}'), lots, 'MM')

        self.last_net = self.net
        self.last_tick = tick
        self.on_end(tick)

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume = trade.volume
        price = trade.price
        if volume > 0:
            if trade.direction.value == '多':
                if self.net >= 0:
                    self.avg = (self.net * self.avg + volume * self.fair_refer) / (self.net + volume)
                elif volume + self.net > 0:  # net<0 # 平仓
                    self.avg = self.fair_refer
                self.net += volume
                self.QT_b += volume
                #
            elif trade.direction.value == '空':
                if self.net <= 0:
                    self.avg = (-self.net * self.avg + volume * self.fair_refer) / (-self.net + volume)
                elif volume - self.net > 0:  # net >0 # 平仓
                    self.avg = self.fair_refer
                self.net -= volume
                self.QT_s += volume
        if trade.vt_symbol == self.maker:
            trade.basePrice = self.fair_refer
            trade.midPrice = self.fair_maker
        self.trade_basis = trade.midPrice - trade.basePrice
        self.trade_price = price

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids,
            self.buy_refer_orderids,
            self.short_refer_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """  # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if not (
                (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 55))
                or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 14))
                or (dt.time() > dtime(10, 31) and dt.time() < dtime(11, 28))
                or (dt.time() > dtime(13, 32) and dt.time() < dtime(14, 55))
        ):
            self.MMTrading = False
        else:
            self.MMTrading = True
