# -*- coding: utf-8 -*-
"""
Created on Thu Mar 25 15:18:34 2021

@author: Lenovo
"""
from matplotlib import pyplot
import matplotlib.pyplot as plt
import numpy as np
from scipy import optimize as op
import pandas as pd
import datetime
from pulp import LpProblem,LpVariable,LpMinimize,LpInteger,LpContinuous
from scipy import interpolate
import cx_Oracle
import os
import scipy.stats as st
from scipy.optimize import fsolve

rf=0.025
dangyue=20230222
ciyue=20230222
#当月
callprice=3.8
putprice=3.7
#次月
callprice1=3.9
putprice1=3.6
ATMprice=4.2
vvK=[4,4.2,4.4]








os.environ['path']=r'D:\Oracle\Instant Client\bin'

oracle_tns=cx_Oracle.makedsn('************',1521,'prdthetf')
con=cx_Oracle.connect('hs_asset','hundsun',oracle_tns)
sql_cmd='SELECT * FROM HS_USER.HT_EXCALIBUR'
data=pd.read_sql(sql_cmd,con)




sql_cmd="SELECT * FROM HS_ASSET.HT_VOY_EXPIRY_FITPARAMS"
expiry=pd.read_sql(sql_cmd,con)



oracle_tns=cx_Oracle.makedsn('************',1521,'prdszjc')
con=cx_Oracle.connect('hs_asset','hundsun',oracle_tns)
sql_cmd='SELECT * FROM HS_USER.HT_EXCALIBUR'
datasz=pd.read_sql(sql_cmd,con)
datasz.OPTIONCODE=datasz.OPTIONCODE.astype('int')
datasz.SPOT_REFF=datasz.SPOT_REFF.astype('float')




trading=pd.read_csv('D:\\data\\trading300.csv')
data.OPTIONCODE=data.OPTIONCODE.astype('int')
data.SPOT_REFF=data.SPOT_REFF.astype('float')
data=pd.merge(data, trading,left_on='OPTIONCODE',right_on='code')
data['ID']=data.exp
#data=pd.merge(data,expiry[['ID','TIME2EXPIRY']].astype('float'),left_on='exp',right_on='ID')
#data.TIME2EXPIRY=data.TIME2EXPIRY






def vanna(z):
    S=z.SPOT_REFF
    br=z.br
    K=z.K
    sigma=z.GEN2FITVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    vanna=st.norm.pdf(d1,loc=0,scale=1)*(-d2/sigma)*np.exp(-br*t)
    return (vanna)
def cgamma(z):
    S=z.SPOT_REFF
    br=z.br
    K=z.K
    sigma=z.GEN2FITVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    gamma=st.norm.pdf(d1,loc=0,scale=1)/(S*np.exp(br*t)*sigma*np.sqrt(t))
    return (gamma)

def dvanna(z):
    S=z.SPOT_REFF
    K=z.K
    sigma=z.GEN2FITVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    br=z.br
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    #Nd1=st.norm.cdf(d1,loc=0,scale=1)
    #Nd2=st.norm.cdf(d2,loc=0,scale=1)
    #Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    #Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    dvanna=st.norm.pdf(d1,loc=0,scale=1)*(-1+d1*d2)*np.exp(-br*t)/(S*sigma*sigma*np.sqrt(t))
    return (dvanna)

    
def volga(z):
    S=z.SPOT_REFF
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    volga=z.GEN2VEGA/sigma*d1*d2*100
    return (volga)

def thetac(z):
    t=z.TIME2EXPIRY
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    S=z.SPOT_REFF*np.exp((rf-br)*t)
    #vega=0.00214585
    
    d2= np.log(S/K)+(-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    if z.Call==1:
        theta=S*st.norm.pdf(d1,loc=0,scale=1)*z.GEN2FITVOL/(-2*np.sqrt(t))-rf*K*np.exp(-rf*t)*st.norm.cdf(d2,loc=0,scale=1)
    else:
        theta=S*st.norm.pdf(d1,loc=0,scale=1)*z.GEN2FITVOL/(-2*np.sqrt(t))+rf*K*np.exp(-rf*t)*st.norm.cdf(-d2,loc=0,scale=1)

    return (theta)



def dvolga(z):
    S=z.SPOT_REFF
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    dvolga=z.GEN2VEGA*100*(d1+d2-d1*d2*d2)/(sigma*sigma*S*np.sqrt(t))

    return (dvolga)

def ATMdelta(z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=z.GEN2ATMVOL
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)

    if z.Call==1:
        return(np.exp(-br*t)*Nd1)
    else:
        return(np.exp(-br*t)*(Nd1-1))

def ATMvega(z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=z.GEN2ATMVOL
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    vega=st.norm.pdf(d1,loc=0,scale=1)*S*np.sqrt(t)*np.exp(-br*t)*100
    return vega


def charm(z):
    t=z.TIME2EXPIRY
    K=z.K
    sigma=z.GEN2FITVOL
    br=z.br
    S=z.SPOT_REFF*np.exp((rf-br)*t)
    #vega=0.00214585
    
    d2= np.log(S/K)+(-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    charm=np.exp(-rf*t)*(-rf*Nd1+st.norm.pdf(d1,loc=0,scale=1)*(np.log(S/K)/-2/sigma*np.power(t,-1.5)+sigma/4/np.sqrt(t)))

    if z.Call==1:
        return(charm)
    else:
        return(charm+rf*np.exp(-rf*t))







def ATMvolprice(z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=z.GEN2ATMVOL
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.Call==1:
        return(Call)
    else:
        return(Put)


def bs_cal(est_sigma,z):
    S=z.SPOT_REFF
    K=z.K
    br=z.br
    sigma=est_sigma
    #vega=0.00214585
    t=z.TIME2EXPIRY
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.Call==1:
        return(Call)
    else:
        return(Put)
def bs_diff(est_sigma,z):
    est_price=bs_cal(est_sigma,z)
    return est_price-z.vvprice

def bs_diff2(est_sigma,z):
    est_price=bs_cal(est_sigma,z)
    return est_price-z.vvprice2
    
def vv(z):
    test=z.copy()
    b=test.tv-test.ATMvolprice
    test.loc[:,'vega']=test.loc[:,'vega']*100
    A=test[['vega','vanna','volga']].values
    try:
        x=np.linalg.inv(A)@b
    except:
        x=[None,None,None]
    return(x)


def cubicspline(z,target):
    z=z.sort_values(by=['GEN2DELTA'])
    z1=z.loc[z.GEN2DELTA>target].head(3)
    z2=z.loc[z.GEN2DELTA<target].tail(1)
    z=pd.concat([z2,z1])
 #   x=z.GEN2DELTA
  #  y=z.GEN2FITVOL
    x=z.GEN2DELTA.append(pd.Series(0.5*np.sign(target)))
    y=z.GEN2FITVOL.append(pd.Series(z.GEN2ATMVOL.iloc[0]))
    z=pd.concat([x,y],axis=1).sort_values(by=[0])
    s=interpolate.CubicSpline(z.iloc[:,0], z.iloc[:,1])
    """      
    arr=np.arange(np.amin(x), np.amax(x), 0.01)    
    fig,ax = plt.subplots(1, 1)
    ax.plot(x, y, 'bo', label='Data Point')

    ax.plot(arr, s(arr), 'k-', label='Cubic Spline', lw=1)
    
   """  
    return(s(target))

def br_cal(z):
    time2expiry=z.TIME2EXPIRY.iloc[0]
    exp0=z.ID.iloc[0]
    E=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==1)].code.iloc[0]
    F=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==0)].code.iloc[0]
    f=data[data.OPTIONCODE==E].GEN2TV.iloc[0]-data[data.OPTIONCODE==F].GEN2TV.iloc[0]+ATMprice*np.exp(-rf*time2expiry)
    s=data[data.OPTIONCODE==E].SPOT_REFF.iloc[0]
    br=-np.log(f/s)/time2expiry
    return(br)


def yparams_cal(z,vvK):
    test=z[z.K.isin(vvK)]
    test=test[test.GEN2TV.isin(test.groupby('K').GEN2TV.min().values)]
    b=test.GEN2TV-test.ATMvolprice

    A=test[['GEN2VEGA','vanna','volga']].values
    x=np.linalg.inv(A)@b
    
    x[0]=x[0]/100
    return(x)
def t_cal(t_est,z,vvK,):
    
    #vega=0.00214585
    time2expiry=t_est
    exp0=z.exp.iloc[0]
    E=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==1)].code.iloc[0]
    F=trading.loc[(trading.K==ATMprice) &(trading.exp==exp0)&(trading.Call==0)].code.iloc[0]
    f=data[data.OPTIONCODE==E].GEN2TV.iloc[0]-data[data.OPTIONCODE==F].GEN2TV.iloc[0]+ATMprice*np.exp(-rf*time2expiry)
    s=data[data.OPTIONCODE==E].SPOT_REFF.iloc[0]
    br=-np.log(f/s)/time2expiry
    
    
    
    test=z.copy()
    test=test[test.GEN2TV.isin(test.groupby('K').GEN2TV.min().values)]
    test=test[test.GEN2TV==test.GEN2TV.max()]
    z=test.iloc[0,:]
    S=z.SPOT_REFF
    K=z.K
    
    sigma=z.GEN2FITVOL
    
    t=t_est
    d2= np.log(S/K)+(rf-br-sigma*sigma/2)*t
    d2=d2/(sigma*np.sqrt(t))
    d1=d2+sigma*np.sqrt(t)
    Nd1=st.norm.cdf(d1,loc=0,scale=1)
    Nd2=st.norm.cdf(d2,loc=0,scale=1)
    Call=S*np.exp(-br*t)*Nd1-K*np.exp(-rf*t)*Nd2
    Put=Call+K*np.exp(-rf*t)-S*np.exp(-br*t)
    if z.Call==1:
        res=Call
    else:
        res=Put    
    return(res-z.GEN2TV)



trading.loc[(trading.K==callprice) &(trading.exp==dangyue)&(trading.Call==1)].code.iloc[0]

data.loc[:,'OPTIONCODE']=pd.to_numeric(data.OPTIONCODE)   


"""
A=trading.loc[(trading.K==callprice) &(trading.exp==dangyue)&(trading.Call==1)].code.iloc[0]
B=trading.loc[(trading.K==putprice) &(trading.exp==dangyue)&(trading.Call==0)].code.iloc[0]
C=trading.loc[(trading.K==callprice1) &(trading.exp==ciyue)&(trading.Call==1)].code.iloc[0]
D=trading.loc[(trading.K==putprice1) &(trading.exp==ciyue)&(trading.Call==0)].code.iloc[0]




cd1=data[data.OPTIONCODE==A].GEN2DELTA.iloc[0]
pd1=data[data.OPTIONCODE==B].GEN2DELTA.iloc[0]
cd2=data[data.OPTIONCODE==C].GEN2DELTA.iloc[0]
pd2=data[data.OPTIONCODE==D].GEN2DELTA.iloc[0]
cv1=data[data.OPTIONCODE==A].GEN2VEGA.iloc[0]
pv1=data[data.OPTIONCODE==B].GEN2VEGA.iloc[0]
cv2=data[data.OPTIONCODE==C].GEN2VEGA.iloc[0]
pv2=data[data.OPTIONCODE==D].GEN2VEGA.iloc[0]



prob=LpProblem('kuayue',LpMinimize)
x1=LpVariable('x1',1,cat=LpInteger)
x2=LpVariable('x2',1,cat=LpInteger)
x3=LpVariable('x3',1,cat=LpInteger)
x4=LpVariable('x4',1,cat=LpInteger)
s1=LpVariable('delta1正',0)
s2=LpVariable('delta1负 ',0)
s3=LpVariable('delta2正',0)
s4=LpVariable('delta2负',0)
s5=LpVariable('Tdelta正',0)
s6=LpVariable('Tdelta负',0)
s7=LpVariable('TVega正',0)
s8=LpVariable('TVega负',0)

prob += 0.5*(s1+s2+s3+s4)+s5+s6+s7+s8

prob+= cd1*x1 + pd1*x2 - s1 + s2 ==0
prob+= -cd2*x3 - pd2*x4 - s3 + s4 ==0
prob+= cd1*x1 + pd1*x2 - cd2*x3 - pd2*x4 - s5 + s6 ==0
prob+= cv1*x1 + pv1*x2 - cv2*x3 - pv2*x4 - s7 + s8 ==0
prob+= x1 +x2 >=10
#prob+= s5 +s6 <=0.08
prob+= s7 +s8 <=0.02
prob+= x1+x2<=15
prob+= x3==2

prob.solve()
for v in prob.variables():
    print(v.name,'==',v.varValue)

z=data[data.OPTIONCODE.isin([A,B,C,D])]
theta=[x1.varValue,x2.varValue,-x3.varValue,-x4.varValue]@z.GEN2THETA
#theta=[x1.varValue,x2.varValue,0,0]@z.GEN2THETA
print('theta=',theta,theta*0.0035)
gamma=[x1.varValue,x2.varValue,-x3.varValue,-x4.varValue]@z.GEN2GAMMA
#gamma=[x1.varValue,x2.varValue,0,0]@z.GEN2GAMMA
print('gamma=',gamma,gamma*0.25)
vega1=[x1.varValue,x2.varValue,0,0]@z.GEN2VEGA
print('vega=',vega1)
print([cd1,pd1,cd2,pd2])

print('跨月',[cd1,pd1,cd2,pd2]@pd.DataFrame([10,10,-6,-6]))




"""







print('-------vv-dangyue-----')


"""
time2expiry=expiry.loc[expiry.ID.astype('float')==dangyue,'TIME2EXPIRY'].astype('float')

E=trading.loc[(trading.K==ATMprice) &(trading.exp==dangyue)&(trading.Call==1)].code.iloc[0]
F=trading.loc[(trading.K==ATMprice) &(trading.exp==dangyue)&(trading.Call==0)].code.iloc[0]

f=data[data.OPTIONCODE==E].GEN2TV.iloc[0]-data[data.OPTIONCODE==F].GEN2TV.iloc[0]+ATMprice
s=data[data.OPTIONCODE==E].SPOT_REFF.iloc[0]
br=rf-np.log(f/s)/time2expiry
"""




#data=pd.merge(data,data.groupby('exp').apply(lambda z: br_cal(z)).rename('br'),left_on='exp',right_index=True)

time2expiry=data.groupby('exp').apply(lambda z: fsolve(t_cal,0.007,args=(z,vvK))[0])
data=data.set_index('exp')
data['TIME2EXPIRY']=time2expiry
br=data.groupby('exp').apply(lambda z: br_cal(z))
data['br']=br
data=data.reset_index()

data['vanna']=data.apply(lambda z: vanna(z),axis=1)
data['theta']=data.apply(lambda z: thetac(z),axis=1)
data['gamma']=data.apply(lambda z: cgamma(z),axis=1)
data['charm']=data.apply(lambda z: charm(z),axis=1)
data['dvanna']=data.apply(lambda z: dvanna(z),axis=1)
data['volga']=data.apply(lambda z: volga(z),axis=1)
data['dvolga']=data.apply(lambda z: dvolga(z),axis=1)
data['ATMvolprice']=data.apply(lambda z: ATMvolprice(z),axis=1)
data['ATMdelta']=data.apply(lambda z: ATMdelta(z),axis=1)
data['ATMvega']=data.apply(lambda z: ATMvega(z),axis=1)







dat=pd.DataFrame(data.groupby('exp').apply(lambda z: yparams_cal(z,vvK)).rename('yparams'))
dat['y1']=dat.apply(lambda z: z.yparams[0],axis=1)
dat['y2']=dat.apply(lambda z: z.yparams[1],axis=1)
dat['y3']=dat.apply(lambda z: z.yparams[2],axis=1)
dat=dat.drop('yparams',axis=1)
data=pd.merge(data,dat,left_on='exp',right_index=True)
print(dat)

data['adjustdelta']=data['ATMdelta']+data.vanna*data.y1+data.dvanna*data.y2+data.dvolga*data.y3
data['vvprice']=data.GEN2VEGA*data.y1*100+data.vanna*data.y2+data.volga*data.y3+data.ATMvolprice







"________________________________skew___________________________________________________________"



cal=list(trading.loc[(trading.exp==dangyue)&(trading.Call==1)].code)
callDelta=data[data.OPTIONCODE.isin(cal)]
Delta25=cubicspline(callDelta,0.25)
Delta50=cubicspline(callDelta,0.50)


put=list(trading.loc[(trading.exp==dangyue)&(trading.Call==0)].code)
putDelta=data[data.OPTIONCODE.isin(put)]
Delta75=cubicspline(putDelta,-0.25)


print('put skew  ',Delta75-Delta50)
print('call_skew  ',Delta25-Delta50)    






compare=data[['K','exp','Call','GEN2TV','vvprice','sigma']]





"""

print('--------vanna 3800-----')
C1=10004260
C2=10004262
C3=10004267
C4=10004269

z=data[data.code.isin([C1,C2,C3,C4])].sort_values('code')

A=z[['adjustdelta','GEN2VEGA','vanna','volga']].T.values

G=pd.DataFrame([0,0,100,0])
w=np.linalg.inv(A)@G
w=w.values
print(w)

size=[-3,8,-8,1]
print(np.around((A@size*100)*[1,10000,1,1]))
A=z[['GEN2DELTA','GEN2VEGA','vanna','GEN2TV']].T.values
print(np.around((A@size*100)*[1,10000,1,1]))

"""



a=9-4
b=17+4

est_sigma=0.20
data['vvvol']=data.apply(lambda z: fsolve(bs_diff,est_sigma,args=z)[0],axis=1)
#data['vvvol2']=data.apply(lambda z: fsolve(bs_diff2,est_sigma,args=z)[0],axis=1)
compare=data[['K','exp','Call','GEN2TV','vvprice','GEN2VEGA','adjustdelta','GEN2FITVOL','vvvol','vanna','volga']]

plt.close()
fig,ax = plt.subplots(1, 1)
compare0=compare[(compare.exp==dangyue) & (compare.Call==1)].sort_values('K')
compare0=compare0.iloc[a:b,]
s=interpolate.CubicSpline(compare0.K, compare0.GEN2FITVOL)
arr=np.arange(np.amin(compare0.K), np.amax(compare0.K), 0.01)    
ax.plot(compare0.K, compare0.GEN2FITVOL, 'bo')
ax.plot(arr, s(arr), 'red', label='fitvol', lw=1)

s=interpolate.CubicSpline(compare0.K, compare0.vvvol)
arr=np.arange(np.amin(compare0.K), np.amax(compare0.K), 0.01)    
ax.plot(compare0.K,  compare0.vvvol, 'bo')
ax.plot(arr, s(arr), 'green', label='vv_vol', lw=1)
ax.legend()



data2=pd.read_csv(r'D:\data\vol\vol\vols_20230213.csv',header=None)
#data=pd.concat([data1,data2])
data2.columns=['time', 'spot', 'code', 'tv', 'bid', 'ask', 'sigma', 'bs', 'ss', 'a.3','a.4', 'a.5', 'delta', 'vega', 'gamma', 'theta', 'rf', 'br', 'time2expiry','K', 'Z', 'Call', 'exp', 'a.8', 'a.9', 'a.10', 'a.11', 'a.12']
data2.time=pd.to_datetime(data2.time)
data2=data2.set_index(data2.time)
data2=data2.drop('time',axis=1)
exp=dangyue
t=data2.index.unique()[-10]
data0=data2[data2.index==t]
data0=data0[data0.exp==exp].sort_values('Z')


z=data0[data0.delta>0].copy()
x=z.K
y=z.sigma
x=x[a:b]
y=y[a:b]
s=interpolate.CubicSpline(x, y)
arr=np.arange(np.amin(x), np.amax(x), 0.01)    
ax.plot(x, y, 'bo')
ax.plot(arr, s(arr), 'black', label=str(t), lw=1)




xvol=pd.read_excel(r'E:\sepro\0819\Release300new - 副本 - 副本\Config\OptGroupConfig.xlsx')
xvol=xvol.iloc[:,0:3]
xvol=pd.merge(xvol,data,left_on='OptCode',right_on='code')
group=pd.DataFrame()
group['adjustdelta']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>)
group['delta']=xvol.groupby('GroupName').apply(lambda z:z.Position@z.GEN2DELTA)

#group['gamma']=xvol.groupby('GroupName').apply(lambda z:z.Position@z.GEN2GAMMA)
group['vanna']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>)
#group['volga']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>)
group['theta']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>*0.0035)
group['charm']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>*-0.0035)

group.loc['Total',:]=group.sum(axis=0)
print(group)
print (-1822*dat[dat.index==20221228].y2+3710*dat[dat.index==20221228].y3)



group=pd.DataFrame()

group['ATMdelta']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>)
group['ATMvega']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>)
group['vega']=xvol.groupby('GroupName').apply(lambda z:z.Position@z.GEN2VEGA)
group['volga']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>)
group['theta']=xvol.groupby('GroupName').apply(lambda z:<EMAIL>*0.0035)


group.loc['Total',:]=group.sum(axis=0)
print(group)








"""
data0=compare[compare.exp==exp].sort_values('GEN2Z')
data0=data0[data0.K.isin([4.2,4.3,4.4,4.5,4.6])]

z=data0[data.GEN2TV.isin(data0.groupby('K').GEN2TV.min())]
x=z.GEN2Z
y=z.GEN2FITVOL
x=x[0:]
y=y[0:]
s=interpolate.CubicSpline(x, y)
arr=np.arange(np.amin(x), np.amax(x), 0.01)    
fig,ax = plt.subplots(1, 1)
ax.plot(x, y, 'bo', label='Data Point')
ax.plot(arr, s(arr), 'red', label='Cubic Spline', lw=1)



z=data0[data.GEN2TV.isin(data0.groupby('K').GEN2TV.min())]
x=z.GEN2Z
y=z.vvvol
x=x[0:]
y=y[0:]
s=interpolate.CubicSpline(x, y)
arr=np.arange(np.amin(x), np.amax(x), 0.01)    

ax.plot(x, y, 'bo', label='Data Point')
ax.plot(arr, s(arr), 'black', label='Cubic Spline', lw=1)


MM=pd.read_csv('D:\\data\\ribao\\持仓.csv')
MM.columns=['code','num']



MM=pd.read_csv('D:\\data\\20221226_position_300.csv',header=None).fillna(0)
MM.columns=['code','a1','a2','a3','a4','a5','a6','a7','a8','a9','st']
MM=MM[MM.st==8]
MM=MM[MM.code!='IF2301']

MM['code']=MM.code.astype('int')
MM['num']=MM.a7-MM.a8


MM=pd.merge(MM,data.drop('code',axis=1),left_on='code',right_on='OPTIONCODE',how='left')
(MM.num*MM.GEN2VEGA).sum()
(MM.num*MM.GEN2DELTA).sum()
(MM.num*MM.GEN2GAMMA).sum()
MM=MM.set_index('code')





os.chdir('D:\\data\\vol\\vol')
# 近月
data=pd.read_csv('vols_20221226.csv',header=None)
data.columns=['time', 'spot', 'code', 'tv', 'bid', 'ask', 'GEN2FITVOL', 'bs', 'ss', 'a.3','a.4', 'a.5', 'delta', 'vega', 'gamma', 'theta', 'rf', 'br', 'time2expiry','K', 'Z', 'Call', 'exp', 'a.8', 'a.9', 'a.10', 'a.11', 'a.12']
data.time=pd.to_datetime(data.time)
data=data.set_index(data.time)
data=data.drop('time',axis=1)
data0=data[data.index.time==datetime.time(14,59,30)].set_index('code')



MM['gamma']=data0.gamma

MM['vega']=data0.vega
MM.groupby('code').apply(lambda z: z.num*z.gamma).sum()

MM['lastvol']=data0.GEN2FITVOL
MM['lasttv']=data0.tv
vegaPNL=MM.groupby('code').apply(lambda z: z.num*z.GEN2VEGA*(z.GEN2FITVOL-z.lastvol))*100
PosPNL=MM.groupby('code').apply(lambda z: z.num*(z.GEN2TV-z.lasttv))

vegaPNL.sum()
PosPNL.sum()
"""