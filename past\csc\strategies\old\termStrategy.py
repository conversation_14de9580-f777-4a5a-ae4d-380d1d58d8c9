# -*- coding: utf-8 -*-
"""
Created on Tue Jul 20 14:52:36 2021

@author: humy2
"""

""""""
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np

import pandas as pd
from scipy.optimize import curve_fit

import warnings
warnings.filterwarnings('ignore')


class TermStrategy(StrategyTemplate):
    """"""

    author = "vnpy"

    boll_window = 20
    boll_dev = 2
    trailing_long = 0.5
    trailing_short = 0.5
    lots = 1

    boll_up = 0
    boll_down = 0
    trading_size = 0
    intra_trade_high = 0
    intra_trade_low = 0
    long_stop = 0
    short_stop = 0

    buy_price = 0
    short_price = 0

    parameters = [
        'boll_window',
        'boll_dev',
        'trailing_long',
        'trailing_short',
        'lots',
        'edge',
        'minEdge',
        'gamma',
        'eta',
        'maxPos',
        'sizes',
        'loss',
        'maker',
        'refer',
        'priceticks',
        'validVolume',
        'safeVolume',
        'loss',
        'referHedgeFlag',
        'neverStopFlag',
        'useReverseOrder',
        'useReverseOrderTargetPosition',
        'reverseOrderTargetPosition',
        'adjust',
        'max_error'
    ]
    variables = [
        'boll_up',
        'boll_down',
        'trading_size',
        'intra_trade_high',
        'intra_trade_low',
        'long_stop',
        'short_stop',
        'buy_price',
        'short_price'
    ]

    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        #self.sbg = SecondBarGenerator(self.on_bar)
        #self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        #self.am = ArrayManager(size=300)

        self.buy_vt_orderids = {}
        self.short_vt_orderids = {}
        self.buy_reverse_orderids = {}
        self.short_reverse_orderids = {}
        self.buy_term_orderids = {}
        self.short_term_orderids = {}
        for i in self.vt_symbols:
            self.buy_vt_orderids[i] = []
            self.short_vt_orderids[i] = []
            self.buy_reverse_orderids[i] = []
            self.short_reverse_orderids[i] = []
            self.buy_term_orderids[i] = []
            self.short_term_orderids[i] = []

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = {}
        self.net={}
        self.realPnl={}
        self.fair = {}
        self.cumPnl={}
        length=99999
        self.mFair = {}
        self.mAsk={}
        self.mBid={}
        self.mCount={}
        self.rCount={}
        self.pauseCount={}
        self.lastMaker={}
        self.maxLoss = {}
        self.lastTicks = {} 
        self.refer_oid=0
        self.isQuoting={}
        self.reverseOrderModeFlag={}
        self.termOrderModeFlag={}
        
        for i in self.vt_symbols:
            self.mFair[i] = np.zeros(length)      
            self.mBid[i]=np.zeros(length)
            self.mAsk[i]=np.zeros(length)
            self.avg[i]=0
            self.net[i]=0
            self.realPnl[i]=0
            self.fair[i]= 0
            self.cumPnl[i]=0
            self.lastMaker[i] = [0,0,None]
            self.mCount[i]=0
            self.rCount[i]=0
            self.pauseCount[i]=0
            self.maxLoss[i] = 0
            self.lastTicks[i] = 0
            self.isQuoting[i]=False
            self.reverseOrderModeFlag[i]=False
            self.termOrderModeFlag[i]=False            

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def getAsk5(self,tick,ask_volume,away_price):
        volume=0
        for i in range(1,6):
            volume += tick.__getattribute__('ask_volume_%d'%i)
            if volume>=ask_volume:
                short_price = tick.__getattribute__('ask_price_%d'%i)
                break
        if volume<ask_volume:
            short_price = tick.__getattribute__('ask_price_%d'%i)+away_price
        return short_price
        
    def getBid5(self,tick,buy_volume,away_price):
        volume=0
        for i in range(1,6):
            volume += tick.__getattribute__('bid_volume_%d'%i)
            if volume>=buy_volume:
                buy_price = tick.__getattribute__('bid_price_%d'%i)
                break
        if volume<buy_volume:
            buy_price = tick.__getattribute__('bid_price_%d'%i) - away_price
        return buy_price
    
    def getFairPrice(self,lastP,askP,bidP,volume,turnover,size,ts):
        if volume > 0:
            fair = round(turnover/volume/size)  #平均价格
        else:
            fair= lastP
            if askP>0 and bidP>0:
                if  askP-bidP<=2: # 有流动性，中间价
                    fair=0.5*(askP+bidP)
                else:   # 流动性不足, 不变 
                    if lastP>askP:
                        fair=askP
                    if lastP<bidP:
                        fair=bidP
        fair = ts * round(fair/ts)
        return fair 

    def get_future_maturity_month(self,tick):
        num = [i for i in tick.vt_symbol if i.isdigit()]
        if len(num) == 3:
            year = int('202' + num[0])
        elif len(num) == 4:
            year = int('20' + num[0] + num[1])
        else:
            raise ValueError("check vt_symbol")
        date_mat = pd.Period(freq = 'M', year = year, month = int(''.join(num[-2:])))
        date_current = pd.Period(tick.datetime,freq='M')
        date_to_mat = (date_mat - date_current).n
        return date_to_mat

    def fit(self,pass_pc, fit_pc, weight = 'liqidity'):
        
        start_point = min(pass_pc['months_to_mat'].min(), fit_pc['months_to_mat'].min())
        end_point = max(pass_pc['months_to_mat'].max(), fit_pc['months_to_mat'].max())
        
        if weight == 'liqidity':
            if fit_pc['volume'].sum() != 0:
                fit_pc['weights'] = fit_pc['volume'] / fit_pc['volume'].sum()
            else:
                print('traded volume of %s is 0, please select liquid contracts to calibrate'%(fit_pc.index.values))
            
            fit_pc = fit_pc[fit_pc['weights']!=0]
            W = fit_pc['weights'].to_dict()
            fit_pc['weights'] = 1 / fit_pc['weights'] # liquidity  = 1 / sigma
        else:
            if len(weight)!=len(fit_pc):
                raise ValueError('weights must match the number of contracts')
            W = {i:j for i,j in zip(fit_pc.index, weight)}
            fit_pc['weights'] = [1 / j for j in weight]
                    

        if len(pass_pc) == 0:
            def _MyModel(x, a, b, c):
                return a * x**2 + b * x + c
            popt, pcov = curve_fit(_MyModel, fit_pc['months_to_mat'], fit_pc['fair'], sigma = fit_pc['weights'])
            popt = popt.tolist()
            
        elif len(pass_pc) == 1 or len(pass_pc) == 2:
            months_to_mat = pass_pc.iloc[0,:]['months_to_mat']
            pc = pass_pc.iloc[0,:]['fair']
            if len(pass_pc) == 1:
                def _MyModel(x, a, b):
                    return a * x**2 + b * x + pc - a * months_to_mat**2 - b * months_to_mat
                popt, pcov = curve_fit(_MyModel, fit_pc['months_to_mat'], fit_pc['fair'], sigma = fit_pc['weights'])
                popt = popt.tolist()
                
            if len(pass_pc) == 2:
                diff_months = pass_pc['months_to_mat'].diff().iloc[-1]
                diff_pcs = pass_pc['fair'].diff().iloc[-1]
                sum_months = sum(pass_pc['months_to_mat'])
                def _MyModel(x, a):
                    b = (diff_pcs - a * diff_months * sum_months) / diff_months
                    c = pc - a * months_to_mat**2 - b * months_to_mat
                    return a * x**2 + b * x + c
                popt, pcov = curve_fit(_MyModel, fit_pc['months_to_mat'], fit_pc['fair'], sigma = fit_pc['weights'])
                popt = popt.tolist()
                popt.append((diff_pcs - popt[0] * diff_months * sum_months) / diff_months)
                
            popt.append(pc - popt[0] * months_to_mat**2 - popt[1] * months_to_mat)
                
        elif len(pass_pc) == 3:
            pass_pc['const'] = 1
            pass_pc['square'] = pass_pc['months_to_mat'] ** 2
            x = np.linalg.inv(pass_pc[['square','months_to_mat','const']])
            y = pass_pc['fair'].values.T
            popt = x.dot(y).tolist()
        else:
            raise ValueError("In quadratic model, You can only fix THREE POINTS")
                        
        if len(fit_pc) > 0:
            error = np.sqrt((fit_pc['fair'] - (popt[0] * fit_pc['months_to_mat']**2 + popt[1] * fit_pc['months_to_mat'] + popt[2])).abs().sum()/fit_pc.shape[0])
        else:
            error = 0
            
        fitted_model = {'parameter': popt, 'start_point': start_point, 'end_point': end_point, 'weights': W, 'error' : error}
        
        return fitted_model

    # position fade volume
    def getFadeVolume(self,net,gamma): # 
        bidFade=0
        askFade=0    
        if net>0:
            if net>=0.6*self.maxPos:            
                askFade= -round(gamma*net)      # <0
                bidFade= round(3*gamma*net)     # >0
            else:
                askFade= -round(0.5*gamma*net)  # <0
                bidFade= round(gamma*net)       # >0
        if net<0:
            if net<=-0.6*self.maxPos:
                askFade= -round(3*gamma*net)    # >0
                bidFade= round(gamma*net)       # <0            
            else:
                askFade= -round(gamma*net)      # >0
                bidFade= round(0.5*gamma*net)   # <0     
        return bidFade,askFade

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        last_ticks = self.lastTicks
        
        if len(last_ticks) != len(self.vt_symbols):
            return
        #对比tick

        for key in ticks:
            if ticks[key]!=last_ticks[key]:
                if key in self.maker:
                    self.on_tick_maker(ticks[key],last_ticks[key])
                elif key in self.refer:
                    self.on_tick_refer(ticks[key],last_ticks[key])
                self.lastTicks[key] = ticks[key]

        #决定on_tick
        
        #储存之前的tick
        
    def on_tick_refer(self,tick,last_tick):

        pass
        # if self.referHedgeFlag:
        #     if self.get_pos(self.maker)+self.get_pos(self.refer)!=0:
        #         if self.refer_oid!=0:
        #             self.cancel_order(self.refer_oid[0])
        #         if self.get_pos(self.maker)+self.get_pos(self.refer)<0:
        #             self.refer_oid = self.buy(self.refer,tick.ask_price_1, abs(self.get_pos(self.maker)+self.get_pos(self.refer))) 
        #         else:
        #             self.refer_oid = self.short(self.refer,tick.bid_price_1, abs(self.get_pos(self.refer)+self.get_pos(self.maker)))

    def on_tick_maker(self, tick,last_tick):
        
        stopFlag = False
        if (tick.datetime+timedelta(hours=8)).timestamp()>1629082537: #datetime.datetime(2021, 8, 16, 10, 4, 59).timestamp()
            tmpA=0
        if self.reverseOrderModeFlag[tick.vt_symbol] : #优先处理
            self.reverseOrderModeFlag[tick.vt_symbol]=False
            return 
        elif self.short_reverse_orderids[tick.vt_symbol]!=[] or self.buy_reverse_orderids[tick.vt_symbol]!=[] : #默认反手单只持续一个tick
            self.cancel_symbol(tick.vt_symbol)
            self.short_reverse_orderids[tick.vt_symbol]=[]
            self.buy_reverse_orderids[tick.vt_symbol]=[]      
            
        if self.termOrderModeFlag[tick.vt_symbol]:
            self.termOrderModeFlag[tick.vt_symbol]=False
            return
        elif self.short_term_orderids[tick.vt_symbol]!=[] or self.buy_term_orderids[tick.vt_symbol]!=[] : #默认反手单只持续一个tick
            self.cancel_symbol(tick.vt_symbol)
            self.short_term_orderids[tick.vt_symbol]=[]
            self.buy_term_orderids[tick.vt_symbol]=[]      

        ts = self.priceticks[tick.vt_symbol]
        size = self.sizes[tick.vt_symbol]
        minEdge = self.minEdge
        edge=self.edge
        net = self.net[tick.vt_symbol]
        lots = self.lots
        eta = self.eta
        stdRange=0.5
        maxV=50
        shortResendFlag=False
        buyResendFlag=False
        adjust=1
        validVolume = self.validVolume
        mCount = self.mCount[tick.vt_symbol]
        isBetter=True           #  是否自动更优报价， 某些条件满足选择更积极报价 
        
        # 1. Filter ：非可控情景暂停 
        if self.isQuoting[tick.vt_symbol]:  # 最好时间驱动， 否则主力和做市合约都要运行 
            # 持仓超限暂停 、在途订单超限暂停、
            if self.isQuoting[tick.vt_symbol] and abs(net)>self.maxPos:
                self.isQuoting[tick.vt_symbol]=False 
                self.pauseCount[tick.vt_symbol]=self.rCount[tick.vt_symbol]+100      
                print("Net Position limit pause",tick.datetime+timedelta(hours=8))
    
            if self.isQuoting[tick.vt_symbol] and self.cumPnl[tick.vt_symbol]< -self.maxLoss[tick.vt_symbol]: # 亏损超限暂停
                self.isQuoting[tick.vt_symbol]=False 
                self.pauseCount[tick.vt_symbol]=self.rCount[tick.vt_symbol]+100
                self.maxLoss[tick.vt_symbol] += self.loss  # 
                print("maker Loss limit pause",tick.datetime+timedelta(hours=8))
            # market cross pause
            if self.isQuoting[tick.vt_symbol] and mCount>5 and (tick.ask_price_1 < self.lastMaker[tick.vt_symbol][2].bid_price_1 -ts or tick.bid_price_1 > self.lastMaker[tick.vt_symbol][2].ask_price_1 +ts):
                self.isQuoting[tick.vt_symbol]=False 
                self.pauseCount[tick.vt_symbol]=self.rCount[tick.vt_symbol]+10      
                print("maker gap limit pause",tick.datetime+timedelta(hours=8)) #   

            if (tick.limit_up and tick.limit_down) and self.isQuoting[tick.vt_symbol] and (tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (
                tick.bid_price_1 > float(tick.limit_up) - 5*ts or  # 涨停附近                   
                tick.ask_price_1 < float(tick.limit_down) + 5*ts):   # 跌停附近                  
                    self.isQuoting[tick.vt_symbol]=False
                    self.pauseCount[tick.vt_symbol]=self.rCount[tick.vt_symbol] + 100
                    print("price limit pause", tick.datetime+timedelta(hours=8))

        #check resume quoting............................................... 
        else:
            if abs(net)<self.maxPos and self.pauseCount[tick.vt_symbol]<self.rCount[tick.vt_symbol]:
                self.isQuoting[tick.vt_symbol]=True 
            
        #### Algo : JoinMarket  ~~~~~~~无效价格情况暂未处理~~~~~~~~~~~~~~~~~~ 
        turnover = tick.turnover - last_tick.turnover if last_tick else 0
        volume = tick.volume - last_tick.turnover if last_tick else 0
        spread = tick.ask_price_1 - tick.bid_price_1
        away_price = ts*max(1, adjust*eta*max(edge/3, spread/2, 3*stdRange)) 
        bidP = self.getBid5(tick,validVolume, away_price)
        askP = self.getAsk5(tick,validVolume, away_price)         #   L2行情更稳定、L1行情偏积极     
        bidP_safe = self.getBid5(tick,self.safeVolume, away_price)         #   
        askP_safe = self.getAsk5(tick,self.safeVolume, away_price)         #   
        self.fair[tick.vt_symbol] = self.getFairPrice(tick.last_price,askP,bidP, volume,turnover,size,ts)                            #   有效量价对应的中间价 
        pnl= net*(self.fair[tick.vt_symbol]-self.avg[tick.vt_symbol])/(abs(net)*ts) if net!=0 and self.fair[tick.vt_symbol]>0 else 0
        bidFade,askFade = self.getFadeVolume(net,self.gamma) 
        
        if pnl<ts: 
            askP = self.getAsk5(tick, eta*validVolume + askFade, away_price)   # askFade 
            bidP = self.getBid5(tick, eta*validVolume + bidFade, away_price)   # bidFade          
        # price fade : not best if hold positions  
        # 增仓 fade price # 尽量不连续增仓 
        if net<self.lastMaker[tick.vt_symbol][1]<1:
             askP +=2*away_price 
        if net>self.lastMaker[tick.vt_symbol][1]>-1: 
             bidP -=2*away_price 
        # safety 做市单避免被慌偏 not cross history + only better 1 tick of last & safe price             
        if mCount>10:                        
            askP=max(askP, self.mBid[tick.vt_symbol][mCount-6:mCount-1].max(), self.mAsk[tick.vt_symbol][mCount-1]-ts, askP_safe -ts) #  
            bidP=min(bidP, self.mAsk[tick.vt_symbol][mCount-6:mCount-1].min(), self.mBid[tick.vt_symbol][mCount-1]+ts, bidP_safe +ts) #  not cross history + only better 1 tick   
            stdRange= self.mFair[tick.vt_symbol][mCount-8:mCount-1].std()/ts # 
        # try better price next ： Best+1 or adjust volume 


        loss=100000
        if isBetter and (mCount>150 and  self.rCount[tick.vt_symbol]>30+self.pauseCount[tick.vt_symbol] and self.cumPnl[tick.vt_symbol]>-2*loss and 
                         abs(net)<0.7*self.maxPos):              
             adjust=0.5  # 下一笔 Tick 尝试缩小 有效量 
        else:
             adjust=1   # 收盘前 adjust=3 , 有最新成交 adjust=3  
        # 最小价宽限制   
             
        if minEdge>1:
            gg=(ts*minEdge -(askP-bidP))
            if gg>0:
                smallerSide = int(gg / 2 / ts) *ts
                biggerSide = gg - smallerSide
                if self.useReverseOrder:
                    if  net>=0: 
                        askP += biggerSide
                        bidP -= smallerSide
                    else:
                        askP += smallerSide
                        bidP -= biggerSide
                else:
                    if  net>=0: 
                        askP += smallerSide
                        bidP -= biggerSide
                    else:
                        askP += biggerSide
                        bidP -= smallerSide

        bidP = ts*round(bidP/ts) 
        askP = ts*round(askP/ts)            

        stdEdge=round((askP-bidP)/ts)   #  标准价差tick数  
        #### Algo End ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        self.cumPnl[tick.vt_symbol]=round((net*self.fair[tick.vt_symbol] + self.realPnl[tick.vt_symbol])*self.sizes[tick.vt_symbol])  # 累计盈亏 ？？  
        if bidP>0 and askP>0:               
            self.mBid[tick.vt_symbol][mCount]=bidP
            self.mAsk[tick.vt_symbol][mCount]=askP
            self.mFair[tick.vt_symbol][mCount]=self.fair[tick.vt_symbol] # mid price  save 
            self.mCount[tick.vt_symbol] +=1  
            #TODO rCount refer tick
            self.rCount[tick.vt_symbol]+=1
            
        # save 
        self.lastMaker[tick.vt_symbol]=[mCount,net,tick]
        stop=max(3*edge,2*stdRange)*(lots/(abs(net)+lots)) # stop 根据行情变化动态调整
        if self.neverStopFlag:
            stop=99999
        if net>0 and 0<spread<=max(1, 0.8*edge)*ts and stdRange<1 and stdEdge<1.2*edge: 
              if pnl>=stop:  
                  stopFlag = True
                  self.cancel_symbol(tick.vt_symbol) # 避免自成交
                  VV=min(maxV-1,max(1,round(abs(net/1.3))))
                  if VV==lots:
                       VV +=1   # 不是好办法， 只是为了区分 做市单和 其他订单，保证其他订单量不为 lots   
                  self.short_vt_orderids[tick.vt_symbol] = self.short(tick.vt_symbol,tick.bid_price_1, VV,'stop_profit')
              if pnl<=-stop:    
                  stopFlag = True
                  self.cancel_symbol(tick.vt_symbol) # 避免自成交
                  self.short_vt_orderids[tick.vt_symbol] = self.short(tick.vt_symbol,tick.bid_price_1, abs(net),'stop_loss')
                  
        # 仅Maker价格稳定、有流动性情况对冲： 其他情况考虑主力对冲（暂未实现）        
        #if not hedgeOrder and mCount>35 and net<0 and 0<spread<=max(1, 0.8*edge)*ts and stdRange<1 and stdEdge<1.2*edge: 
        if net<0 and 0<spread<=max(1, 0.8*edge)*ts and stdRange<1 and stdEdge<1.2*edge: 
              if pnl>=stop:  
                   stopFlag = True
                   self.cancel_symbol(tick.vt_symbol) # 避免自成交
                   VV=min(maxV-1,max(1,round(abs(net/1.3))))  
                   if VV==lots:
                       VV +=1   # 不是好办法， 只是为了区分 做市单和 其他订单，保证其他订单量不为 lots                      
                   self.buy_vt_orderids[tick.vt_symbol] = self.buy(tick.vt_symbol,tick.ask_price_1, VV,'stop_profit') 
              if pnl<=-stop: 
                   stopFlag = True
                   self.cancel_symbol(tick.vt_symbol) # 避免自成交
                   self.buy_vt_orderids[tick.vt_symbol] = self.buy(tick.vt_symbol,tick.ask_price_1, abs(net),'stop_loss')  

        if askP!=self.short_price:
            shortResendFlag=True
        if bidP!=self.buy_price:
            buyResendFlag=True
            
        self.short_price = askP
        self.buy_price = bidP
        if mCount>35 and not stopFlag:
            if not self.buy_vt_orderids[tick.vt_symbol]:
                self.buy_vt_orderids[tick.vt_symbol] = self.buy(tick.vt_symbol,self.buy_price, self.lots,'MM')
            elif buyResendFlag and self.buy_vt_orderids[tick.vt_symbol][0]:
                self.cancel_order(self.buy_vt_orderids[tick.vt_symbol][0])  #cancel完会跳转至onOrder，然后自动发一个单
                self.buy_vt_orderids[tick.vt_symbol] = self.buy(tick.vt_symbol,self.buy_price, self.lots,'MM')
            
            if not self.short_vt_orderids[tick.vt_symbol]:
                self.short_vt_orderids[tick.vt_symbol] = self.short(tick.vt_symbol,self.short_price, self.lots,'MM')
            elif shortResendFlag and self.short_vt_orderids[tick.vt_symbol][0]:
                self.cancel_order(self.short_vt_orderids[tick.vt_symbol][0]) #cancel完会跳转至onOrder，然后自动发一个单
                self.short_vt_orderids[tick.vt_symbol] = self.short(tick.vt_symbol,self.short_price, self.lots,'MM')
            

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if volume == 0:
            return 
        if trade.direction.value=='多':
            if self.net[trade.vt_symbol]>=0 :            
                 self.avg[trade.vt_symbol] = (self.net[trade.vt_symbol]*self.avg[trade.vt_symbol] +volume*price)/(self.net[trade.vt_symbol]+volume)              
            elif volume+self.net[trade.vt_symbol]>0: # net<0 # 平仓
                 self.avg[trade.vt_symbol] = price         
            self.net[trade.vt_symbol] += volume 
            self.realPnl[trade.vt_symbol] -=volume*price
        #         
        if trade.direction.value=='空':    
            if self.net[trade.vt_symbol]<=0:
                self.avg[trade.vt_symbol] =(-self.net[trade.vt_symbol]*self.avg[trade.vt_symbol] + volume*price)/(-self.net[trade.vt_symbol]+volume)
            elif volume-self.net[trade.vt_symbol]>0: # net[trade.vt_symbol] >0 # 平仓
                self.avg[trade.vt_symbol]=price
            self.net[trade.vt_symbol] -= volume 
            self.realPnl[trade.vt_symbol] +=volume*price
            
        # 进行term structure对冲
        if len(self.strategy_engine.last_ticks) == len(self.vt_symbols):
            fair_dict = {}
            bid_dict = {}
            ask_dict = {}
            volume_dict = {}
            date_dict = {}
            net_dict = {}
            for i in self.strategy_engine.last_ticks:
                tick = self.strategy_engine.ticks[i]
                last_tick = self.strategy_engine.last_ticks[i]
                turnover = tick.turnover - last_tick.turnover
                volume = tick.volume - last_tick.turnover
                ts = self.priceticks[i]
                size = self.sizes[i]
                fair_dict[i] = self.getFairPrice(tick.last_price,tick.ask_price_1,tick.bid_price_1,volume,turnover,size,ts)
                bid_dict[i] = tick.bid_price_1
                ask_dict[i] = tick.ask_price_1
                volume_dict[i] = tick.volume
                date_dict[i] = self.get_future_maturity_month(tick)
                net_dict[i] = self.net[tick.vt_symbol]
            fitting_pc = pd.DataFrame([fair_dict,bid_dict,ask_dict,volume_dict,date_dict,net_dict]).T
            fitting_pc.columns = ['fair','bid','ask','volume','months_to_mat','net']
            passed_points = [self.refer]
            pass_pc = fitting_pc[fitting_pc.index.isin(passed_points)]
            fit_pc = fitting_pc[~fitting_pc.index.isin(passed_points)]
            weight = ['liquidity']
            fitted_model = self.fit(pass_pc, fit_pc, weight = 'liqidity')  
            error = fitted_model['error']
            params = fitted_model['parameter']
            expected_value = fitting_pc['months_to_mat'].apply(lambda x:  params[0]* x**2 + params[1] * x + params[2])
            fitting_pc['e_pc'] = round(expected_value)
            fitting_pc['offset'] = fitting_pc['fair'] - fitting_pc['e_pc']
            fitting_pc['cost'] = fitting_pc.apply(lambda x: x.ask - x.e_pc if x.offset < 0 else x.bid - x.e_pc if x.offset>0 else 0,axis=1) + fitting_pc['net'] * self.adjust
        
        best_bid = fitting_pc.index[fitting_pc.cost.argmin()]
        best_ask = fitting_pc.index[fitting_pc.cost.argmax()]

        net_sum = fitting_pc.net.sum()
        
        if error < self.max_error: #选择最优合约进行平仓
            self.termOrderModeFlag[trade.vt_symbol]=True
            self.cancel_symbol(trade.vt_symbol)   #撤销对应合约的所有报价
            self.buy_term_orderids[trade.vt_symbol]=[]
            self.short_term_orderids[trade.vt_symbol]=[]
            if net_sum > 0:
                self.short_term_orderids[trade.vt_symbol] =  self.short(best_ask, self.strategy_engine.ticks[best_ask].bid_price_1,  abs(net_sum),'termHedge')
            elif net_sum < 0:
                self.buy_term_orderids[trade.vt_symbol] =  self.buy(best_bid, self.strategy_engine.ticks[best_bid].ask_price_1,  abs(net_sum),'termHedge')

        else: #按照反手策略进行报价
            if trade.volume==self.lots and self.useReverseOrder:
                if self.useReverseOrderTargetPosition:
                    self.reverseOrderModeFlag[trade.vt_symbol]=True
                    self.cancel_symbol(trade.vt_symbol)  #cancel完会跳转至onOrder，然后自动发一个单
                    self.buy_reverse_orderids[trade.vt_symbol]=[]
                    self.short_reverse_orderids[trade.vt_symbol]=[]
                    if self.net[trade.vt_symbol]>self.reverseOrderTargetPosition:
                        self.short_reverse_orderids[trade.vt_symbol] = self.short(trade.vt_symbol, price, min(self.net[trade.vt_symbol]-self.reverseOrderTargetPosition,self.lots*2),'short_reverse')
                    elif self.net[trade.vt_symbol]<self.reverseOrderTargetPosition:
                        self.buy_reverse_orderids[trade.vt_symbol] = self.buy(trade.vt_symbol, price,  min(self.reverseOrderTargetPosition-self.net[trade.vt_symbol],self.lots*2),'buy_reverse')
    
                else:
                    
                    self.reverseOrderModeFlag[trade.vt_symbol]=True
                    if trade.direction.value=='多':
                        self.cancel_symbol(trade.vt_symbol)  #cancel完会跳转至onOrder，然后自动发一个单
                        self.buy_reverse_orderids[trade.vt_symbol]=[]
                        self.short_reverse_orderids[trade.vt_symbol] = self.short(trade.vt_symbol, price, self.lots*2,'short_reverse')
                    elif trade.direction.value=='空':
                        self.cancel_symbol(trade.vt_symbol)  #cancel完会跳转至onOrder，然后自动发一个单
                        self.short_reverse_orderids[trade.vt_symbol]=[]
                        self.buy_reverse_orderids[trade.vt_symbol] = self.buy(trade.vt_symbol, price, self.lots*2,'buy_reverse')

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids[order.vt_symbol],
            self.short_vt_orderids[order.vt_symbol],
        ]:
            if order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
        