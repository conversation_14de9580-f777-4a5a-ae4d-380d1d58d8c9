# -*- coding:utf-8 -*-
# Author:Lining
# Editdate:2017-9-18

from __future__ import print_function
from WindPy import w
import pyodbc
import datetime
import pandas as pd

server = '10.25.18.36'
user = 'Alex'
password = '789456'
table = "[<PERSON>].[dbo].[IndexNew]"

w.start()

import sys
sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')
#
# reload(sys)
# sys.setdefaultencoding('utf-8')

# 连接数据库
# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)
cursor = conn.cursor()

from SQL.windtosql.update import datemakerMin
indexnum = 4
dt, beginDate = datemakerMin.datemaker(table, conn, cursor, indexnum)


def tosql(zzcode):
    sql = "INSERT INTO  [<PERSON>].[dbo].[IndexNew] VALUES (?,?,?,?,?,?,?,?,?,?)"
    n = int((dt - beginDate).days / 10)
    for j in range(0, n + 1):
        startDate = beginDate + datetime.timedelta(days=10 * j)
        endDate = startDate + datetime.timedelta(days=10, hours=7)
        if dt <= endDate:
            endDate = dt

        sqllist2 = []
        # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
        print(u"\n\n-----第 %i 次通过wsi来提取 %s - %s - %s 开高低收成交量分钟数据-----\n" % (j, zzcode, str(startDate), str(endDate)))

        wsddata1 = w.wsi(zzcode, "open,high,low,close,volume,amt", startDate,
                         endDate, "PriceAdj=F")

        # wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
        if wsddata1.ErrorCode != 0:
            print(wsddata1.ErrorCode)
            continue
        print(len(wsddata1.Data[1]))

        for i in range(0, len(wsddata1.Data[1])):
            sqllist = []

            if len(wsddata1.Times) > 1:
                sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
                sqllist.append(wsddata1.Times[i].strftime('%H:%M:%S'))

            sqllist.append(str(wsddata1.Data[1][i]))

            for k in range(2, len(wsddata1.Fields)):
                sqllist.append(wsddata1.Data[k][i])

            sqllist.append(-1)

            sqltuple = tuple(sqllist)

            try:
                cursor.execute(sql, sqltuple)
            except:
                print(sqllist)

            conn.commit()

            # sqllist2.append(sqltuple)

            # cursor.executemany(sql, sqllist2)


tosql(zzcode="399001.SZ,399006.SZ,000001.SH,000016.SH")

conn.close()
