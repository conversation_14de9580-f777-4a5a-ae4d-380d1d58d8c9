"""
市场微观结构因子模块
包含深度加权价差、主动性买卖量、交易方向、信号特征等微观结构因子
@author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory
from .basic_factors import calculate_mid_price,  calculate_tradedVol, calculate_avg_prc

# 合约乘数，用于价格计算
# 注意：这里假设合约乘数为1，实际使用时应根据具体品种设置
CONTRACT_MULTIPLIER = 1

# 因子列表，用于记录所有注册的因子
market_microstructure_factors = []

#################################
# 中间价格因子
#################################

def calculate_midprcdiff(data: pd.DataFrame, level1: int, level2: int) -> pd.Series:
    """计算中间价格不平衡度"""
    midpx1 = calculate_mid_price(data, level1)
    midpx2 = calculate_mid_price(data, level2)
    return midpx1 - midpx2


def calculate_midgap(data: pd.DataFrame, gap: int) -> pd.Series:
    """计算中间价格间隔差异"""
    if 'mid_price' not in data.columns:
        midpx1 = calculate_mid_price(data, 1)
    else:
        midpx1 = data['mid_price']
    return midpx1.diff(gap).fillna(0)


# 注册中间价格组合特征
for j in range(1, 6):
    for i in range(j+1, 6):
        factor_manager.register_factor(Factor(
            name=f"midprcdiff_{i}_{j}",
            category=FactorCategory.ADVANCED,
            description=f"{i}档和{j}档中间价格的不平衡度",
            calculation=lambda data, l1=i, l2=j: calculate_midprcdiff(data, l1, l2),
            dependencies=[f"AskPrice{i}", f"BidPrice{i}", f"AskPrice{j}", f"BidPrice{j}"],
            parameters={"level1": i, "level2": j},
            source="market_microstructure_factors"
        ))

# 注册不同窗口的价格差异因子
for window in range(2, 10, 20):
    factor_manager.register_factor(Factor(
        name=f"midgap{window}",
        category=FactorCategory.ADVANCED,
        description=f"中间价格{window}期间隔差异",
        calculation=lambda data, g=window: calculate_midgap(data, g),
        dependencies=["mid_price"],
        parameters={"window": window},
        source="market_microstructure_factors"
    ))
    
###########################################
# 深度加权价差因子
###########################################

def calculate_depth_weighted_spread(data: pd.DataFrame, side: str) -> pd.Series:
    """
    计算深度加权价差，side为'ask'或'bid'
    ask: dwsa = ∑[(AskPrice_{j+1} - AskPrice_j) / AskVol_j] / ∑[1/AskVol_j]
    bid: dwsb = -∑[(BidPrice_j - BidPrice_{j+1}) / BidVol_j] / ∑[1/BidVol_j]
    """
    if side == 'ask':
        num = sum((data[f'AskPrice{j+1}'] - data[f'AskPrice{j}']) / data[f'AskVol{j}'] for j in range(1, 5))
        denom = sum(1 / data[f'AskVol{j}'] for j in range(1, 5))
    else:
        num = -sum((data[f'BidPrice{j}'] - data[f'BidPrice{j+1}']) / data[f'BidVol{j}'] for j in range(1, 5))
        denom = sum(1 / data[f'BidVol{j}'] for j in range(1, 5))
    return num / denom.replace(0, np.nan).fillna(1)

factor_manager.register_factor(Factor(
    name="vol_weighted_spread_ask",
    category=FactorCategory.ADVANCED,
    description="卖方深度加权价差",
    calculation=lambda data: calculate_depth_weighted_spread(data, 'ask'),
    dependencies=[f'AskPrice{j}' for j in range(1, 6)] + [f'AskVol{j}' for j in range(1, 5)],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="vol_weighted_spread_bid",
    category=FactorCategory.ADVANCED,
    description="买方深度加权价差",
    calculation=lambda data: calculate_depth_weighted_spread(data, 'bid'),
    dependencies=[f'BidPrice{j}' for j in range(1, 6)] + [f'BidVol{j}' for j in range(1, 5)],
    source="market_microstructure_factors"
))

def calculate_depth_weighted_spread_diff(data: pd.DataFrame) -> pd.Series:
    return calculate_depth_weighted_spread(data, 'ask') + calculate_depth_weighted_spread(data, 'bid')

factor_manager.register_factor(Factor(
    name="vol_weighted_spread_diff",
    category=FactorCategory.ADVANCED,
    description="深度加权价差差异",
    calculation=calculate_depth_weighted_spread_diff,
    dependencies=["vol_weighted_spread_ask", "vol_weighted_spread_bid"],
))

###########################################
# 主动性买卖量因子
###########################################

def calculate_ordflow_and_level(data: pd.DataFrame, side: str) -> (pd.Series, pd.Series):
    """
    计算主动性买/卖单量及深度指标
    
    参数:
        data: 市场数据DataFrame
        side: 'bid' 表示主动性买单，'ask' 表示主动性卖单
    
    返回:
        ordflow: 主动性买/卖单量
        ordflowlevel: 主动性买/卖单深度指标
    """
    # 设置价格和成交量前缀及比较操作
    if side == 'bid':
        price_prefix = 'BidPrice'
        vol_prefix = 'BidVol'
        cmp_op = np.greater      # 价格是否高于前值
        rev_cmp_op = np.less     # 价格是否低于前值
    elif side == 'ask':
        price_prefix = 'AskPrice'
        vol_prefix = 'AskVol'
        cmp_op = np.less         # 价格是否低于前值
        rev_cmp_op = np.greater  # 价格是否高于前值
    else:
        raise ValueError("side must be 'bid' or 'ask'")
    
    # 获取数据
    price1_prev = data[f'{price_prefix}1'].shift(1).values
    vol1_prev = data[f'{vol_prefix}1'].shift(1).values
    prices = np.column_stack([data[f'{price_prefix}{j}'].values for j in range(1, 6)])
    vols = np.column_stack([data[f'{vol_prefix}{j}'].values for j in range(1, 6)])
    
    # 创建价格比较掩码
    price_cmp_mask = cmp_op(prices, price1_prev[:, None])        # 价格优于前值的掩码
    price_equal_mask = (prices == price1_prev[:, None])          # 价格等于前值的掩码
    price_higher_mask = rev_cmp_op(prices, price1_prev[:, None]) # 价格差于前值的掩码
    
    # 初始化结果数组
    n = len(data)
    ordflow = np.zeros(n)
    ordflowlevel = np.zeros(n)
    
    #====== 计算主动性买/卖单量 ======#
    
    # 计算主动性价差部分：价格优于上一时刻的档位成交量
    indices = np.where(price_cmp_mask)
    np.add.at(ordflow, indices[0], vols[indices])
    
    # 价格相同但量增加部分（只检查前3档）
    for j in range(min(3, prices.shape[1])):
        equal_mask = price_equal_mask[:, j]
        vol_diff = np.where(equal_mask, vols[:, j] - vol1_prev, 0)
        positive_diff_mask = (vol_diff > 0)
        ordflow[positive_diff_mask] += vol_diff[positive_diff_mask]
    
    #====== 计算深度指标 ======#
    
    # 根据买卖方向计算深度指标
    if side == 'bid':
        has_higher = np.any(price_higher_mask, axis=1)
        first_lower = np.argmax(price_cmp_mask, axis=1)
        no_lower = ~np.any(price_cmp_mask, axis=1)
        first_lower[no_lower] = 5
        ordflowlevel[has_higher] = first_lower[has_higher]
    else:
        has_lower = np.any(price_cmp_mask, axis=1)
        first_higher = np.argmax(price_higher_mask, axis=1)
        no_higher = ~np.any(price_higher_mask, axis=1)
        first_higher[no_higher] = 5
        ordflowlevel[has_lower] = -first_higher[has_lower]
    
    # 第一个元素设为0（没有前一时刻的数据）
    ordflow[0] = 0
    ordflowlevel[0] = 0
    
    return pd.Series(ordflow, index=data.index), pd.Series(ordflowlevel, index=data.index)

def calculate_ordflowbid(data: pd.DataFrame) -> pd.Series:
    """主动性买单量"""
    return calculate_ordflow_and_level(data, 'bid')[0]

def calculate_ordflowask(data: pd.DataFrame) -> pd.Series:
    """主动性卖单量"""
    return -calculate_ordflow_and_level(data, 'ask')[0]

def calculate_outbidlevel(data: pd.DataFrame) -> pd.Series:
    """主动性买单的深度指标"""
    return calculate_ordflow_and_level(data, 'bid')[1]

def calculate_outasklevel(data: pd.DataFrame) -> pd.Series:
    """主动性卖单的深度指标"""
    return -calculate_ordflow_and_level(data, 'ask')[1]

# 注册主动性买卖量因子
factor_manager.register_factor(Factor(
    name="ordflowbid",
    category=FactorCategory.ADVANCED,
    description="主动性买单量",
    calculation=calculate_ordflowbid,
    dependencies=[f'BidPrice{j}' for j in range(1, 6)] + [f'BidVol{j}' for j in range(1, 6)],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="ordflowask",
    category=FactorCategory.ADVANCED,
    description="主动性卖单量",
    calculation=calculate_ordflowask,
    dependencies=[f'AskPrice{j}' for j in range(1, 6)] + [f'AskVol{j}' for j in range(1, 6)],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="outbidlevel",
    category=FactorCategory.ADVANCED,
    description="主动性买单的深度指标",
    calculation=calculate_outbidlevel,
    dependencies=[f'BidPrice{j}' for j in range(1, 6)],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="outasklevel",
    category=FactorCategory.ADVANCED,
    description="主动性卖单的深度指标",
    calculation=calculate_outasklevel,
    dependencies=[f'AskPrice{j}' for j in range(1, 6)],
    source="market_microstructure_factors"
))

def calculate_outlevel_sum(data: pd.DataFrame, window: int) -> pd.Series:
    """计算主动性买单的深度指标滚动和"""
    return calculate_outbidlevel(data) + calculate_outasklevel(data)

factor_manager.register_factor(Factor(
    name="outlevel_sum",
    category=FactorCategory.ADVANCED,
    description="主动性买单的深度指标滚动和",
    calculation=lambda data, w=10: calculate_outlevel_sum(data, w),
    dependencies=["outbidlevel", "outasklevel"],
    source="market_microstructure_factors"
))

###########################################
# 交易方向和交易量特征因子
###########################################

def calculate_trdflowsign(data: pd.DataFrame) -> pd.Series:
    """
    计算交易方向
    
    公式说明：
    1. 买方交易条件：
       - 条件1：当前均价 >= (上一时刻卖一价 - 上一时刻价差的25%) 且 交易额变化 > 0
       - 条件2：当前均价 > 上一时刻均价 且 交易额变化 > 10
       - 满足任一条件时，交易方向为买入(1)
    
    2. 卖方交易条件：
       - 条件1：当前均价 <= (上一时刻买一价 + 上一时刻价差的25%) 且 交易额变化 > 0
       - 条件2：当前均价 < 上一时刻均价 且 交易额变化 > 10
       - 满足任一条件时，交易方向为卖出(-1)
    
    3. 其他情况：交易方向为中性(0)
    """
    # 初始化结果序列
    result = pd.Series(0, index=data.index)
    
    # 提取需要的数据列，避免重复访问
    tradedVol = data['Volume'].diff().values
    ask_price1 = data['AskPrice1'].values
    bid_price1 = data['BidPrice1'].values
    avgpx = calculate_avg_prc(data).values

    # 预计算价差的25%
    spread_quarter = 0.25 * (ask_price1[:-1] - bid_price1[:-1])
    
    # 买方交易条件向量化计算
    buy_cond1 = (avgpx[1:] >= (ask_price1[:-1] - spread_quarter)) & (tradedVol[1:] > 0)
    buy_cond2 = (avgpx[1:] > avgpx[:-1]) & (tradedVol[1:] > 10)
    buy_mask = buy_cond1 | buy_cond2
    
    # 卖方交易条件向量化计算
    sell_cond1 = (avgpx[1:] <= (bid_price1[:-1] + spread_quarter)) & (tradedVol[1:] > 0)
    sell_cond2 = (avgpx[1:] < avgpx[:-1]) & (tradedVol[1:] > 10)
    sell_mask = sell_cond1 | sell_cond2
    
    # 使用numpy数组操作设置结果
    result_values = result.values
    result_values[1:][buy_mask] = 1
    result_values[1:][sell_mask] = -1
    
    return result

def calculate_trdflowsign_sum(data: pd.DataFrame, window: int) -> pd.Series:
    """计算交易方向滚动和"""
    if 'trdflowsign' not in data.columns:
        trdflowsign = calculate_trdflowsign(data)
    else:
        trdflowsign = data['trdflowsign']
    return trdflowsign.rolling(window).sum()

def calculate_trdflowva(data: pd.DataFrame) -> pd.Series:
    """计算卖方交易量"""
    if 'tradedVol' not in data.columns:
        tradedVol = calculate_tradedVol(data)
    else:
        tradedVol = data['tradedVol']
    
    if 'trdflowsign' not in data.columns:
        trdflowsign = calculate_trdflowsign(data)
    else:
        trdflowsign = data['trdflowsign']
    
    return -tradedVol * (trdflowsign == -1)

def calculate_trdflowvb(data: pd.DataFrame) -> pd.Series:
    """计算买方交易量"""
    if 'tradedVol' not in data.columns:
        tradedVol = calculate_tradedVol(data)
    else:
        tradedVol = data['tradedVol']
    
    if 'trdflowsign' not in data.columns:
        trdflowsign = calculate_trdflowsign(data)
    else:
        trdflowsign = data['trdflowsign']
    
    return tradedVol * (trdflowsign == 1)

def calculate_trdimb(data: pd.DataFrame) -> pd.Series:
    """计算交易量不平衡度"""
    if 'trdflowvb' not in data.columns:
        trdflowvb = calculate_trdflowvb(data)
    else:
        trdflowvb = data['trdflowvb']
    
    if 'trdflowva' not in data.columns:
        trdflowva = calculate_trdflowva(data)
    else:
        trdflowva = data['trdflowva']
    
    return trdflowvb + trdflowva

def calculate_ordimb(data: pd.DataFrame) -> pd.Series:
    """计算订单不平衡度"""
    if 'ordflowbid' not in data.columns:
        ordflowbid = calculate_ordflowbid(data)
    else:
        ordflowbid = data['ordflowbid']
    
    if 'ordflowask' not in data.columns:
        ordflowask = calculate_ordflowask(data)
    else:
        ordflowask = data['ordflowask']
    
    return ordflowbid + ordflowask

def calculate_toflow_bid(data: pd.DataFrame) -> pd.Series:
    """计算买方突破量"""
    if 'ordflowbid' not in data.columns:
        ordflowbid = calculate_ordflowbid(data)
    else:
        ordflowbid = data['ordflowbid']
    
    if 'trdflowvb' not in data.columns:
        trdflowvb = calculate_trdflowvb(data)
    else:
        trdflowvb = data['trdflowvb']
    
    return ordflowbid + trdflowvb

def calculate_toflow_ask(data: pd.DataFrame) -> pd.Series:
    """计算卖方突破量"""
    if 'trdflowva' not in data.columns:
        trdflowva = calculate_trdflowva(data)
    else:
        trdflowva = data['trdflowva']
    
    if 'ordflowask' not in data.columns:
        ordflowask = calculate_ordflowask(data)
    else:
        ordflowask = data['ordflowask']
    
    return trdflowva + ordflowask

# 注册交易方向和交易量特征因子
factor_manager.register_factor(Factor(
    name="avg_prc_diff",
    category=FactorCategory.ADVANCED,
    description="平均价格变化",
    calculation=lambda data: data['avg_prc'].diff().fillna(0),
    dependencies=["avg_prc"],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="trdflowsign",
    category=FactorCategory.ADVANCED,
    description="交易方向",
    calculation=calculate_trdflowsign,
    dependencies=["avg_prc", "tradedVol", "AskPrice1", "BidPrice1","tradedValue"],
    source="market_microstructure_factors"
))

# 注册交易方向滚动和因子
for window in range(10, 20):
    factor_manager.register_factor(Factor(
        name=f"trdflowsign_{window}_sum",
        category=FactorCategory.ADVANCED,
        description=f"交易方向{window}期滚动和",
        calculation=lambda data, w=window: calculate_trdflowsign_sum(data, w),
        dependencies=["trdflowsign"],
        parameters={"window": window},
        source="market_microstructure_factors"
    ))

factor_manager.register_factor(Factor(
    name="trdflowva",
    category=FactorCategory.ADVANCED,
    description="卖方交易量",
    calculation=calculate_trdflowva,
    dependencies=["tradedVol", "trdflowsign"],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="trdflowvb",
    category=FactorCategory.ADVANCED,
    description="买方交易量",
    calculation=calculate_trdflowvb,
    dependencies=["tradedVol", "trdflowsign"],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="trdimb",
    category=FactorCategory.ADVANCED,
    description="交易量不平衡度",
    calculation=calculate_trdimb,
    dependencies=["trdflowvb", "trdflowva"],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="ordimb",
    category=FactorCategory.ADVANCED,
    description="订单不平衡度",
    calculation=calculate_ordimb,
    dependencies=["ordflowbid", "ordflowask"],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="toflowBid",
    category=FactorCategory.ADVANCED,
    description="买方突破量",
    calculation=calculate_toflow_bid,
    dependencies=["ordflowbid", "trdflowvb"],
    source="market_microstructure_factors"
))

factor_manager.register_factor(Factor(
    name="toflowAsk",
    category=FactorCategory.ADVANCED,
    description="卖方突破量",
    calculation=calculate_toflow_ask,
    dependencies=["trdflowva", "ordflowask"],
    source="market_microstructure_factors"
))

def calculate_toflow(data: pd.DataFrame) -> pd.Series:
    """计算突破量"""
    if 'toflowBid' not in data.columns:
        toflowBid = calculate_toflow_bid(data)
    else:
        toflowBid = data['toflowBid']
    
    if 'toflowAsk' not in data.columns:
        toflowAsk = calculate_toflow_ask(data)
    else:
        toflowAsk = data['toflowAsk']
    
    return toflowBid + toflowAsk

factor_manager.register_factor(Factor(
    name="toflow",
    category=FactorCategory.ADVANCED,
    description="突破量",
    calculation=calculate_toflow,
    dependencies=["toflowBid", "toflowAsk"],
    source="market_microstructure_factors"
))

# 计算累计价差
def calculate_accumulated_price_spread(data: pd.DataFrame) -> pd.Series:
    """计算累计价差"""
    return sum(data[f"AskPrice{i}"] - data[f"BidPrice{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_price_spread",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖价差的累计值",
    calculation=calculate_accumulated_price_spread,
    dependencies=[f"AskPrice{i}" for i in range(1, 6)] + [f"BidPrice{i}" for i in range(1, 6)],
    source="orderbook-dynamic-features v5"
))


###########################################
# 趋势特征因子
###########################################

def calculate_midtrend(data: pd.DataFrame, decay: float) -> pd.Series:
    """计算中间价格趋势"""
    # 初始化结果序列
    result = pd.Series(0, index=data.index)
    
    # 获取中间价格差异
    if 'middiff' not in data.columns:
        middiff = calculate_mid_price(data)
    else:
        middiff = data['middiff']
    
    # 计算趋势
    for i in range(1, len(data)):
        result.iloc[i] = result.iloc[i-1] * decay + middiff.iloc[i]
    
    return result

def calculate_trenddiff(data: pd.DataFrame, decay: float) -> pd.Series:
    """计算趋势差异"""
    # 获取中间价格趋势
    if f'midtrend-{decay}' not in data.columns:
        midtrend = calculate_midtrend(data, decay)
    else:
        midtrend = data[f'midtrend-{decay}']
    
    return midtrend.diff().fillna(0)

def calculate_trendoftrend(data: pd.DataFrame, decay: float) -> pd.Series:
    """计算趋势的趋势"""
    # 初始化结果序列
    result = pd.Series(0, index=data.index)
    
    # 获取趋势差异
    if f'trenddiff-{decay}' not in data.columns:
        trenddiff = calculate_trenddiff(data, decay)
    else:
        trenddiff = data[f'trenddiff-{decay}']
    
    # 计算趋势的趋势
    for i in range(1, len(data)):
        result.iloc[i] = result.iloc[i-1] * decay + trenddiff.iloc[i]
    
    return result

# 注册趋势特征因子
for decay in [0.9, 0.95]:
    factor_manager.register_factor(Factor(
        name=f"midtrend-{decay}",
        category=FactorCategory.ADVANCED,
        description=f"中间价格趋势，衰减因子为{decay}",
        calculation=lambda data, d=decay: calculate_midtrend(data, d),
        dependencies=["middiff"],
        parameters={"decay": decay},
        source="market_microstructure_factors"
    ))
    
    factor_manager.register_factor(Factor(
        name=f"trenddiff-{decay}",
        category=FactorCategory.ADVANCED,
        description=f"趋势差异，衰减因子为{decay}",
        calculation=lambda data, d=decay: calculate_trenddiff(data, d),
        dependencies=[f"midtrend-{decay}"],
        parameters={"decay": decay},
        source="market_microstructure_factors"
    ))
    
    factor_manager.register_factor(Factor(
        name=f"trendoftrend-{decay}",
        category=FactorCategory.ADVANCED,
        description=f"趋势的趋势，衰减因子为{decay}",
        calculation=lambda data, d=decay: calculate_trendoftrend(data, d),
        dependencies=[f"trenddiff-{decay}"],
        parameters={"decay": decay},
        source="market_microstructure_factors"
    ))

