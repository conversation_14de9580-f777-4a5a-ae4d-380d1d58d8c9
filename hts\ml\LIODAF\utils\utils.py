
"""
工具函数
@author: lining
"""
import os
import time
import pandas as pd
import numpy as np
import datetime

from core import config

log_file = os.path.join(config.OUTDIR, 'log.log')


def clear_log():
    
    if os.path.exists(log_file):
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write('')


def log_print(message, level='info', LOG_CONFIG=None, prefix=None):
    
    
    default_config = {
        'enabled': True,
        'levels': {
            'debug': False,
            'info': True,
            'warning': True,
            'error': True
        },
        'use_color': True  
    }

    
    config_to_use = LOG_CONFIG or default_config

    
    level_str = str(level) if not isinstance(level, str) else level
    if not config_to_use['enabled'] or level_str not in config_to_use['levels'] or not config_to_use['levels'][level_str]:
        write_log(message, level_str)
        return

    
    color_map = {
        'debug': '\033[36m',    
        'info': '\033[32m',     
        'warning': '\033[33m',  
        'error': '\033[31m',    
        'success': '\033[32m',  
        'reset': '\033[0m'      
    }
    
    
    color = color_map.get(level_str, color_map[level]) if config_to_use.get('use_color', True) else ''
    reset = color_map['reset'] if config_to_use.get('use_color', True) else ''
    
    
    if prefix:
        
        log_prefix = f"[{prefix}]"
    else:
        
        prefix_map = {
            'debug': 'DEBUG', 
            'info': 'INFO', 
            'warning': 'WARN', 
            'error': 'ERROR',
            'success': 'SUCCESS'
        }
        log_prefix = f"[{prefix_map.get(level_str, 'INFO')}]"
    
    
    timestamp = datetime.datetime.now().strftime('%H:%M:%S')
    
    
    print(f"{color}{timestamp} {log_prefix} {message}{reset}")
    
    
    write_log(f"{timestamp} {log_prefix} {message}", level_str)


def write_log(message, level):
    
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"{timestamp} {level} {message}\n")


def calculate_win_rate_threshold(pred: pd.Series, actual: pd.Series, threshold_percent: float = None,
                                 is_direction: bool = True) -> float:
    
    threshold = pred.abs().quantile(threshold_percent)
    
    pred_direction = pd.Series(0, index=pred.index)
    pred_direction[pred > threshold] = 1
    pred_direction[pred < -threshold] = -1

    
    if is_direction:
        
        actual_direction = np.sign(actual)
    else:
        
        threshold = actual.abs().quantile(threshold_percent)
        actual_direction = pd.Series(0, index=actual.index)
        actual_direction[actual > threshold] = 1
        actual_direction[actual < -threshold] = -1

    
    non_zero_predictions = pred_direction != 0
    correct_predictions = (pred_direction == actual_direction) & non_zero_predictions

    
    if non_zero_predictions.sum() > 0:
        win_rate = float(format(correct_predictions.sum() / non_zero_predictions.sum(), '.4f')) 
        signal_ratio = float(format(non_zero_predictions.sum() / len(pred_direction), '.4f')) 
        non_zero_count = float(format(non_zero_predictions.sum(), '.4f')) 
        return win_rate, non_zero_count
    return -1, -1  


def calculate_win_rate(factor: pd.Series, returns: pd.Series) -> float:
    
    factor_direction = (factor > 0).astype(int)
    actual_direction = (returns > 0).astype(int)
    return (factor_direction == actual_direction).mean()


def search_days(date_str, daysnum, data_path):
    
    days = []
    date_str = datetime.datetime.strptime(date_str, '%Y%m%d')
    for i in range(0, 300):
        last_date_str = date_str + datetime.timedelta(days=-i)
        last_date_str = last_date_str.strftime('%Y%m%d')
        if not os.path.exists(data_path % last_date_str):
            log_print(f"数据不存在，使用前一天数据: {last_date_str}", 'debug')
        else:
            days.append(last_date_str)

        if len(days) == daysnum + 1:
            days = sorted(days)
            break
    return days