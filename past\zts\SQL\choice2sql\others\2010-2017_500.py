# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-23
from WindPy import w
import pymssql
from datetime import datetime, timedelta

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = datetime.now() # "2017-08-23"
sectorcode="000905.SH"
#beginDate = "2017-06-24"
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')
cursor = conn.cursor()
cursor.execute("""
 IF OBJECT_ID('stock500', 'U') IS NOT NULL
    DROP TABLE stock500
 CREATE TABLE stock500 (
    DateTime DATE NOT NULL,
    STKID VARCHAR(20) NOT NULL,
    STKName VARCHAR(20) NOT NULL,
    )
 """)
sql = "INSERT INTO stock500 VALUES (%s, %s,%s)"
stocks=[]

for ii in range(16):
    print(ii)
    if ii % 2 == 1 :
        days=183*ii
    else:
        days=182.5 * ii

    date = dt - timedelta(days)
    # 通过wset来取数据集数据
    print('\n\n' + '-----通过wset来取%s数据集数据,获取沪深500代码列表-----' % date + '\n')
    wsetdata1 = w.wset("sectorconstituent", "date=%s;windcode=%s" % (date,sectorcode))
    print(wsetdata1)

    # stocks = list(set(stocks)+(set(wsetdata1.Data[1])))
    # sqllist=stocks
    for i in range(0, len(wsetdata1.Data[1])):
        sqllist=[]
        for k in range(0, len(wsetdata1.Fields)):
            if k == 0:
                sqllist.append(wsetdata1.Data[k][i].strftime('%Y-%m-%d'))
            else:
                sqllist.append(wsetdata1.Data[k][i])
        sqltuple = tuple(sqllist)
        cursor.execute(sql, sqltuple)
        conn.commit()

conn.close()
