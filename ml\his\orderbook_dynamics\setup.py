from setuptools import setup, find_packages
import os

# Read the README from the current directory
readme_path = os.path.join(os.path.dirname(__file__), "README.md")
with open(readme_path, "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="orderbook_dynamics",
    version="0.1.0",
    author="Python Port Author",
    author_email="<EMAIL>",
    description="A library for analyzing high-frequency limit order book dynamics",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/orderbook-dynamics",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Topic :: Office/Business :: Financial",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.7",
    install_requires=[
        "numpy>=1.19.0",
        "pandas>=1.0.0",
        "scikit-learn>=0.24.0",
        "matplotlib>=3.0.0",
    ],
    entry_points={
        "console_scripts": [
            "orderbook-dt=orderbook_dynamics.src.decision_tree_dynamics:main",
        ],
    },
) 