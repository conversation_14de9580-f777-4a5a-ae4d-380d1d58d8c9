"""
回测模块
@author: lining
"""
import pandas as pd
import numpy as np
import datetime
from tqdm import tqdm
from core import config
from utils.utils import log_print
import sys
sys.path.insert(0, sys.path[0]+"/../")

from ml.LIODAF.utils.vwap_decompose import decompose_trade_prices

class OrderBookBacktester:
    """订单簿回测器"""

    def __init__(self, data=None, predictions=None, train_pred_quantile_dict=None):
        """
        初始化回测器
        
        参数:
            data (pandas.DataFrame): 原始数据
            predictions (pandas.DataFrame): 预测结果
        """
        self.data = data
        self.predictions = predictions
        self.results = None
        self.trades = []
        self.position = 0
        self.entry_price = 0
        self.holding_period = 0
        self.entry_time = None
        self.exit_time = None
        self.avg_price = 0
        self.spread = 0
        self.max_pnl = 0
        self.false_trades = 0
        self.true_trades = 0
        self.next_trades = None

        # 获取参数
        self.threshold = train_pred_quantile_dict[config.THRESHOLD_PERCENT]
        self.closing_threshold = train_pred_quantile_dict[config.CLOSING_THRESHOLD]
        self.max_holding_period = config.HOLDING_PERIOD
        self.commission = config.COMMISSION
        self.max_position = config.MAX_POSITION
        self.order_size = config.ORDER_SIZE
        self.stop_loss = config.STOP_LOSS
        self.take_profit = config.TAKE_PROFIT
        self.use_mid_price = config.USE_MID_PRICE
        self.min_profit = config.MIN_PROFIT
        self.spread_threshold = config.SPREAD_THRESHOLD
        self.exe_next_flag = config.EXE_NEXT

    def set_data(self, data, predictions=None):
        """
        设置数据
        
        参数:
            data (pandas.DataFrame): 原始数据
            predictions (pandas.DataFrame): 预测结果
        """
        self.data = data
        if predictions is not None:
            self.predictions = predictions

    def on_trade(self, df,i,type,reason):
        """
        交易回调函数
        """
        if self.exe_next_flag:
            exe_next=self.next_trade(df,i)
            if exe_next is not None:
                # 开仓操作检查
                sell_next=exe_next['sell_vol'] < 2 and exe_next['can_sell_next']==0 and exe_next['sell_orders']<=1
                buy_next=exe_next['buy_vol'] < 2 and exe_next['can_buy_next']==0 and exe_next['buy_orders']<=1
                if type == 'open':
                    # 多头信号但不能买入，或空头信号但不能卖出时，直接返回
                    if (self.current_signal > 0 and buy_next) or \
                    (self.current_signal < 0 and sell_next):
                        log_print(f"开仓失败 {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S.%f')} 方向: {self.current_signal} 持仓: {self.position} 价格: {self.current_price:.2f} avg: {self.avg_price:.2f} 盈亏: {df['return'].iloc[i]:.6f} 持仓时间: {self.holding_period:.2f} 秒",'debug')
                        log_print(f"下一笔行情: {self.next_trades},发单价格: {self.current_price:.2f},买成交量: {exe_next['buy_vol']},卖成交量: {exe_next['sell_vol']}",'debug')
                        self.false_trades += 1
                        return
                # 平仓操作检查
                elif type == 'close':
                    # 持有多头但不能卖出，或持有空头但不能买入时，直接返回
                    if (self.position > 0 and sell_next) or \
                    (self.position < 0 and buy_next):
                        log_print(f"平仓失败 {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S.%f')} 方向: {self.current_signal} 持仓: {self.position} 价格: {self.current_price:.2f} avg: {self.avg_price:.2f} 盈亏: {df['return'].iloc[i]:.6f} 持仓时间: {self.holding_period:.2f} 秒",'debug')
                        log_print(f"下一笔行情: {self.next_trades},发单价格: {self.current_price:.2f},买成交量: {exe_next['buy_vol']},卖成交量: {exe_next['sell_vol']}",'debug')
                        self.false_trades += 1
                        return

        df['trade_type'].iloc[i] = type
        df['reason'].iloc[i] = reason
        if type == 'close':
            df['exit_price'].iloc[i] = self.current_price
            df['tradevol'].iloc[i] = abs(self.position)
            df['return'].iloc[i] = self.position * (self.current_price / self.avg_price - 1)
            self.holding_period = (self.current_time - self.entry_time).total_seconds()
            self.position = 0
        elif type == 'open':
            self.position = self.position+self.current_signal * self.order_size
            df['tradevol'].iloc[i] = abs(self.order_size)
            self.entry_price = self.current_price
            self.avg_price = (self.avg_price * (self.position-self.current_signal*self.order_size) + self.current_price * self.current_signal*self.order_size) / (self.position)
            self.entry_time = self.current_time
            df['entry_price'].iloc[i] = self.current_price
        
        df['spread'].iloc[i] = abs(self.spread*df['tradevol'].iloc[i])
        df['position'].iloc[i] = self.position
        df['commission'].iloc[i] = abs(self.commission*df['tradevol'].iloc[i])
        
        trade = {
            'reason': reason,
            'type': type,
            'entry_time': self.entry_time,
            'exit_time': self.current_time,
            'position': self.position,
            'tradevol': df['tradevol'].iloc[i],
            'entry_price': self.avg_price.round(2),
            'exit_price': self.current_price,
            'return': df['return'].iloc[i],
            'commission': df['commission'].iloc[i],
            'spread': df['spread'].iloc[i],
            'holding_period': (self.current_time - self.entry_time).total_seconds()   # 秒
        }
        self.trades.append(trade)

        self.true_trades += 1

        log_print(f"{reason} {type}: {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S.%f')} 方向: {self.current_signal} 持仓: {self.position} 价格: {self.current_price:.2f} avg: {self.avg_price:.2f} 盈亏: {df['return'].iloc[i]:.6f} 持仓时间: {self.holding_period:.2f} 秒",'debug')

        if type == 'close':
            self.entry_price = 0
            self.entry_time = None
            self.avg_price = 0
            self.holding_period = 0
            self.max_pnl = 0

    def run_backtest(self, target_col=None):
        """
        运行回测
        
        参数:
            target_col (str): 目标列名
            threshold_percent (float): 信号阈值分位数
            holding_period (int): 持仓周期
            commission (float): 手续费率
            max_position (float): 最大仓位
            stop_loss (float): 止损比例
            take_profit (float): 止盈比例
            use_mid_price (bool): 是否使用中间价
            
        返回:
            tuple: (回测结果, 摘要, 交易记录)
        """
        if self.data is None:
            raise ValueError("数据未设置")

        if self.predictions is None:
            raise ValueError("预测结果未设置")


        # 检查预测列是否存在
        pred_col = f"{target_col}_pred"
        if pred_col not in self.predictions.columns:
            raise ValueError(f"预测结果中不存在列 {pred_col}")

        # 合并数据
        df = self.data.copy()
        df[pred_col] = self.predictions[pred_col]

        # 检查价格列
        price_col = 'mid' if self.use_mid_price else 'close'
        if price_col not in df.columns:
            if price_col == 'mid' and 'AskPrice1' in df.columns and 'BidPrice1' in df.columns:
                df['mid'] = (df['AskPrice1'] + df['BidPrice1']) / 2
                log_print(f"已创建 {price_col} 列")
            else:
                raise ValueError(f"数据中不存在价格列 {price_col}")
            
        # 生成信号
        # threshold = df[pred_col].abs().quantile(self.threshold_percent)
        threshold = self.threshold
        # 信号绝对值的分布
        df['signal'] = 0
        df.loc[df[pred_col] > threshold, 'signal'] = 1
        df.loc[df[pred_col] < -threshold, 'signal'] = -1

        # 初始化结果列
        df['position'] = np.nan
        df['entry_price'] = np.nan
        df['exit_price'] = np.nan
        df['return'] = 0
        df['commission'] = np.nan
        df['spread'] = np.nan
        df['tradevol'] = np.nan
        df['cum_return'] = 1.0
        df['trade_type'] = np.nan
        df['reason'] = np.nan

        # 回测逻辑
        is_close=False

        # 简化后的回测主循环
        df_index = df.index

        mids = df['mid'].values
        pred_values = self.predictions[pred_col].values
        signals = df['signal'].values
        length = len(df)
        spread_threshold = self.spread_threshold
        closing_threshold = self.closing_threshold
        max_holding_period = self.max_holding_period
        time_range = config.TIME_RANGE
        spread_arr=(df['AskPrice1']-df['BidPrice1'])/ df['mid'] / 2
        spread_threshold_percent_arr = spread_threshold / df['mid']
        abs_pred_arr = abs(pred_values)

        # 计算收盘和午休时间
        close_time = (datetime.datetime.strptime(time_range[-1][1], '%H:%M:%S') - datetime.timedelta(minutes=3)).time()
        noon_close_time = (datetime.datetime.strptime(time_range[0][1], '%H:%M:%S') - datetime.timedelta(minutes=3)).time()
        noon_open_time = datetime.datetime.strptime(time_range[-1][0], '%H:%M:%S').time()

        for i in tqdm(range(length), desc="回测进度"):
            self.current_time = df_index[i]
            self.current_price = mids[i] if price_col == 'mid' else df[price_col].iloc[i]
            self.current_signal = signals[i]
            pnl = (self.current_price / self.avg_price - 1) * self.position
            self.max_pnl = max(pnl, self.max_pnl)
            self.spread = spread_arr[i]

            # 跳过最后一条预测
            if i == length - 1:
                continue

            spread_threshold_percent = spread_threshold_percent_arr[i]
            abs_pred_val = abs_pred_arr[i]

            # 信号与价差判断
            def is_bigsignal(mult): 
                return abs_pred_val >= self.spread * mult
            is_closing_signal = abs_pred_val >= closing_threshold
            is_smallspread = self.spread <= spread_threshold_percent
            is_pnlspread = pnl >= self.spread * 2 * self.position

            # 收盘或午休前平仓
            now = self.current_time.time()
            if now >= close_time or (now >= noon_close_time and now <= noon_open_time):
                if self.position != 0:
                    self.on_trade(df, i, 'close', 'end of trading')
                continue
            
            # 检查是否需要平仓
            if self.position != 0:
                # 检查持仓时间
                holding_period = (self.current_time - self.entry_time).total_seconds()
                if self.entry_time is not None and holding_period >= self.max_holding_period:
                    # 平仓
                    if (is_smallspread and is_pnlspread) or holding_period >= self.max_holding_period*5:
                        is_close=True
                        reason='time out'

                # 检查止损
                elif self.stop_loss is not None and pnl <= -self.stop_loss:
                    # 止损平仓
                    is_close=True
                    reason='stop loss'

                # 检查止盈
                elif self.take_profit is not None :
                    is_take_profit = pnl - self.max_pnl <= -self.take_profit
                    is_min_profit = pnl >= self.min_profit
                    is_take_profit_small = pnl - self.max_pnl <= -config.MIN_TICK/self.current_price
                    # 止盈平仓
                    if is_min_profit and is_take_profit:
                        is_close=True                    
                        reason='take profit'                
            
                if is_closing_signal:
                    if self.position*self.current_signal < 0:
                        # 平仓
                        if is_bigsignal(1) :
                            is_close=True
                            reason='close signal big signal'
                        elif is_smallspread:
                            is_close=True
                            reason='close signal small spread'
                        elif is_pnlspread:
                            is_close=True
                            reason='close signal take profit'
                        else:
                            log_print(f"平仓失败 {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S.%f')} 价差: {self.spread:.6f} 阈值: {spread_threshold_percent:.6f} 价格: {self.current_price:.2f} 信号: {self.current_signal} 持仓: {self.position}",'debug')
                
                if is_close:  # 当有平仓时记录交易
                    self.on_trade(df,i,'close',reason)
                    is_close=False
                    continue


            # 检查是否需要开仓
            if self.current_signal != 0:
                    # 开仓
                    if abs(self.position) < config.MAX_POSITION:
                        if is_bigsignal(2):
                            self.on_trade(df,i,'open','open big signal')
                        elif is_smallspread:
                            self.on_trade(df,i,'open','open small spread')
                        else:
                            log_print(f"开仓失败 {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S.%f')} 价差: {self.spread:.6f} 阈值: {spread_threshold_percent:.6f} 价格: {self.current_price:.2f} 信号: {self.current_signal} 持仓: {self.position}",'debug')
                    else:
                        log_print(f"开仓失败 {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S.%f')} 价差: {self.spread:.6f} 阈值: {spread_threshold_percent:.6f} 价格: {self.current_price:.2f} 信号: {self.current_signal} 持仓: {self.position}",'debug')
        
        # 将交易记录转换为DataFrame并保存
        self.trades = pd.DataFrame(self.trades)

        # 添加统计信息
        self.trades['profit'] = self.trades['return'] > 0

        # 计算累计收益
        df['cum_return'] = (1 + df['return']).cumprod()
        df['net_return'] = (1 + df['return'] - df['commission'] - df['spread']).cumprod()
        df['total_trades'] = df['tradevol'].cumsum()

        # 计算回测指标
        total_return = df['cum_return'].iloc[-1] - 1
        annual_return = (1 + total_return) ** (252 / len(df)) - 1
        daily_returns = df['return'].resample('D').sum()
        sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() != 0 else 0
        max_drawdown = (df['cum_return'] / df['cum_return'].cummax() - 1).min()
        win_rate = len(df[df['return'] > 0]) / len(df[df['return'] != 0]) if len(df[df['return'] != 0]) > 0 else 0
        win_loss_ratio = abs(df[df['return'] > 0]['return'].mean() / df[df['return'] < 0]['return'].mean()) if len(df[df['return'] < 0]) > 0 else 0

        # 保存结果
        self.results = df

        # 创建回测摘要
        summary = {
            '总收益': total_return,
            '年化收益': annual_return,
            '夏普比率': sharpe_ratio,
            '最大回撤': max_drawdown,
            '胜率': win_rate,
            '盈亏比': win_loss_ratio,
            '手续费': df['commission'].sum(),
            '价差': df['spread'].sum(),
            '阈值': threshold,
            '持仓周期': self.max_holding_period,
            '交易量': df['tradevol'].sum(),
            '信号次数': len(df[df['signal']!=0]),
            '交易次数': len(df[df['tradevol'].notna()]),
            '开仓次数': len(df[df['trade_type'] == 'open']),
            '平仓次数': len(df[df['trade_type'] == 'close']),
            '成交概率': self.true_trades/(self.false_trades+self.true_trades),
        }
        reasons = df['reason'].unique()
        for reason in reasons:
            summary[reason] = len(df[df['reason'] == reason])

        log_print("回测完成，结果摘要:")
        for key, value in summary.items():
            log_print(f"  {key}: {value:.4f}" if isinstance(value, float) else f"  {key}: {value}")

        return df, summary, self.trades
    
    def next_trade(self,df,i):
        # 下一笔行情判断
        exe_next = None
        length = len(df)

        if i + 1 < length:
            # 简化：去除冗余变量和重复赋值，保留核心行情提取逻辑
            bid_prices = df['BidPrice1'].values
            ask_prices = df['AskPrice1'].values
            last_prices = df['LastPrice'].values
            traded_vols = df['Volume'].diff().values
            turnovers = df['TotalValueTraded'].diff() / config.MULT
            bid_orders = {df[f'BidPrice{level}'].iloc[i]: df[f'BidVol{level}'].iloc[i] for level in range(1, 6)}
            ask_orders = {df[f'AskPrice{level}'].iloc[i]: df[f'AskVol{level}'].iloc[i] for level in range(1, 6)}
            bid_orders_next = {df[f'BidPrice{level}'].iloc[i+1]: df[f'BidVol{level}'].iloc[i+1] for level in range(1, 6)}
            ask_orders_next = {df[f'AskPrice{level}'].iloc[i+1]: df[f'AskVol{level}'].iloc[i+1] for level in range(1, 6)}

            curr_bid = bid_prices[i]
            curr_ask = ask_prices[i]
            next_bid = bid_prices[i + 1]
            next_ask = ask_prices[i + 1]
            next_last = last_prices[i + 1]
            next_traded_vol = traded_vols[i + 1]
            next_turnover = turnovers[i + 1]
            next_avgprc = round(next_turnover / next_traded_vol, 2) if next_traded_vol != 0 else 0.0

            min_price = min(curr_bid, next_bid)
            max_price = max(curr_ask, next_ask)

            buy_price=curr_ask
            sell_price=curr_bid

            # VWAP分解成交量
            next_trades, *_ = decompose_trade_prices(
                next_last, next_traded_vol, next_turnover,
                min_price=min_price, max_price=max_price,
                tick_size=config.MIN_TICK, method='simple'
            )

            # 统计买卖方向成交量
            sell_vol = sum(v for k, v in next_trades.items() if k >= sell_price)
            buy_vol = sum(v for k, v in next_trades.items() if k <= buy_price)

            sell_orders=sum(v for k, v in bid_orders.items() if k >= sell_price)
            buy_orders=sum(v for k, v in ask_orders.items() if k <= buy_price)

            sell_orders_next=sum(v for k, v in bid_orders_next.items() if k >= sell_price)
            buy_orders_next=sum(v for k, v in ask_orders_next.items() if k <= buy_price)
            
            exe_next = {
                'can_buy_next': buy_orders_next,
                'can_sell_next': sell_orders_next,
                'buy_orders': buy_orders,
                'sell_orders': sell_orders,
                'buy_vol': buy_vol,
                'sell_vol': sell_vol,
            }

            # 合并行情与成交分布信息
            self.next_trades = {
                'next_last': next_last,
                'next_traded_vol': next_traded_vol,
                'next_avgprc': next_avgprc,
                'curr_bid_orders': bid_orders,
                'curr_ask_orders': ask_orders,
                'bid_orders_next': bid_orders_next,
                'ask_orders_next': ask_orders_next,
                **exe_next,
                **next_trades
            }


        
        return exe_next
