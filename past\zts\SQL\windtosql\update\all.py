# -*- coding:utf-8 -*-
from __future__ import print_function
from datetime import datetime
from WindPy import w

import os
import time
import sys
import logging

reload(sys)
sys.setdefaultencoding('utf-8')

i = 0
path = 'D:\\onedrive\\中泰衍生\\mine_python\\quantpy\\SQL\\windtosql\\update'
newpath = unicode(path, 'utf8').encode('gbk')

import subprocess
import traceback
import tempfile


def subexe(cmd):
    # this method is used for monitoring

    import time
    import subprocess
    import locale
    import codecs

    mylist = []
    ps = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, shell=True)
    while True:
        data = ps.stdout.readline()
        mylist.append(data)
        print(u"%s" % data, end='')
        # if data == b'':
        #     if ps.poll() is not None:
        #         break
        # else:
        #     mylist.append(data)
        #     newlist = []
        #     for i in mylist:
        #         if i.find('192.168') > 0:
        #             newlist.append(i)
        #     newlist.sort()
        #     print('Sum of requests from LAN:', len(newlist))

    return mylist


def exeCmd(path2):
    r = os.popen(path2)
    text = r.read()
    r.close()
    return text


def writeFile(data):
    print(u"%s" % data, end='')
    logging.info(data)
    print('logging successful')


def auto_exit(timer, tt):
    for _ in range(0, timer, tt):
        print("\r", end="")
        print(u"\r程序将在 %d秒 内自动关闭" % timer, end="")
        time.sleep(tt)
        timer -= tt


def linetime(timer, tt):
    lineLength = timer
    delaySeconds = tt
    frontSymbol = '='
    frontSymbol2 = ['—', '\\', '|', '/']
    backSymbol = ' '

    lineTmpla = u"{:%s<%s} {} {:<10}" % (backSymbol, lineLength)
    print(u"本次更新将在 %d秒 内自动退出" % timer, end="")
    for _ in range(0, timer, delaySeconds):
        tmpSymbol = frontSymbol2[timer % (len(frontSymbol2))]
        sys.stdout.write("\r")
        #print(lineTmpla.format(frontSymbol * timer, tmpSymbol, str(timer) + u"秒后自动关闭"), end='')
        sys.stdout.write(lineTmpla.format(frontSymbol * timer, tmpSymbol, str(timer) + "秒后自动关闭"))
        sys.stdout.flush()
        time.sleep(delaySeconds)
        timer -= delaySeconds


logging.basicConfig(level=logging.DEBUG,
                format='%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s',
                datefmt='%Y-%m-%d %a %H:%M:%S',
                filename='%s\\myapp.log' % newpath,
                filemode='a')

now = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
writeFile(now)
w.start()
if w.isconnected():
    t = '\nwind opened successfully'
else:
    t = '\nwind api false'
writeFile(t)

i = i + 1
writeFile(u"\n\n             %s\n*\n*\n*开始更新399001.SZ,399006.SZ,000001.SH,000016.SH指数分钟数据\n*\n*" % i)
text = exeCmd('%s\\Index2SQLUpdate.py' % newpath)
writeFile(text)
i = i + 1
writeFile(u"\n\n             %s\n*\n*\n*开始更新棉花期货主力合约数据\n*\n*" % i)
text = exeCmd("%s\\MfuturesSQLup.py" % newpath)
writeFile(text)
i = i + 1
writeFile(u"\n\n             %s\n*\n*\n*开始更新白糖期货主力合约数据\n*\n*" % i)
text = exeCmd("%s\\SRfuturesSQLup.py" % newpath)
writeFile(text)
i = i + 1
writeFile(u"\n\n             %s\n*\n*\n*开始更新豆粕期权日数据\n*\n*" % i)
text = exeCmd("%s\\opt_M_UP.py" % newpath)
writeFile(text)

week = datetime.now().weekday()+1
i = i + 1
if week == 5 and ((datetime.now().hour >= 15 and datetime.now().minute >= 30) or datetime.now().hour >= 16):
    writeFile(u"\n\n             %s\n*\n*\n*\n*\n*现在是北京时间 星期%s %s点 %s分 \n开始更新300指数分钟数据\n*\n*"
          % (i, week, datetime.now().hour, datetime.now().minute))
    # text=exeCmd("%s\\300Min2sqlUp.py" % newpath)
    os.system(u"更新完毕")
    writeFile(text)
    # writeFile(text)
else:
    writeFile(u"\n\n             %s\n*\n*\n*今日不需要更新300指数分钟数据\n*\n*" % i)

now = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
writeFile(now)
writeFile(u"*********数据库更新结束\n\n\n\n\n")

# import msvcrt
# print(ord(msvcrt.getch()))

auto_exit(150, 1)
