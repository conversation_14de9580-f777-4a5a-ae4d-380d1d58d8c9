# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import os
import sys
from matplotlib.widgets import Slider, But<PERSON>
from functools import partial
import matplotlib.colors as mcolors
from scipy import interpolate

from py_statistics1 import MarketDataManager, login  # 添加新的导入

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

from db_solve.parreader import loadvol

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
tradetime00 = [['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']]


def cubicspline(z, target):
    z = z.sort_values(by=['delta'])
    z1 = z.loc[z.delta > target].head(2)
    z2 = z.loc[z.delta < target].tail(2)
    z = pd.concat([z2, z1])
    x = z.delta
    y = z.sigma
    try:
        s = interpolate.CubicSpline(x, y)
        return (s(target))
    except:
        return (-99)


#    arr=np.arange(np.amin(x), np.amax(x), 0.01)
#   fig,ax = plt.subplots(1, 1)
#  ax.plot(x, y, 'bo', label='Data Point')

# ax.plot(arr, s(arr), 'k-', label='Cubic Spline', lw=1)


def vol_cal(path, live_mode=False):
    """
    获取波动率数据
    Args:
        path: 数据文件路径
        live_mode: 是否使用实时模式(从py_statistics1获取数据)
    Returns:
        处理后的波动率数据DataFrame
    """
    if live_mode:
        data = MDManager.get_options_by_key('underlyer', '510500')  # 获取实时波动率数据
        # 确保数据格式与loadvol输出一致
        # 转为DataFrame
        data = pd.DataFrame(data)
        # 行和列反了
        data = data.T
        if data.empty:
            return None
        else:
            # Update numeric conversion to handle errors explicitly
            for column in data.columns:
                try:
                    data[column] = data[column].astype(float)
                except (ValueError, TypeError):
                    continue

            data.rename(
                columns={'optioncode': 'code', 'timestamp': 'time', 'expiry': 'exp', 'strike': 'K', 'spotprice': 'spot',
                         'callPut': 'call', 'z': 'Z', 'bidvol': 'bid_vol', 'askvol': 'ask_vol', 'fitvol': 'sigma'},
                inplace=True)
            data['exp'] = data['exp'].astype(int)
            data['vol_spread'] = data['ask_vol'] - data['bid_vol']
            data['bid'] = data['tv'] - data['vol_spread'] / 2 * data['vega'] * 100
            data['ask'] = data['tv'] + data['vol_spread'] / 2 * data['vega'] * 100
            data['code'] = data['code'].astype(str)
            data['rf'] = 0
            data['forward'] = data['spot']
            endtime = pd.to_datetime(data['exp'].astype(str) + ' 15:00:00')
            data['time'] = pd.to_datetime(data['time'].iloc[0])  # Convert time column to datetime
            data['time2expiry'] = (endtime - data[
                'time']).dt.total_seconds() / 365 / 24 / 3600  # 年化,data['exp']是str，日期，转换为当日15点
    else:
        data = loadvol(path, [], tradetime00, all=True)
        data['forward'] = data['spot'] * np.exp((data['rf'] - data['br']) * data['time2expiry'])
    return data.reset_index()


def combine_delta_data(data):
    # 分离正delta和负delta的数据
    positive_delta = data[data['delta'] > 0].copy()
    negative_delta = data[data['delta'] < 0].copy()

    # 重命名负delta的列
    negative_delta = negative_delta.rename(columns={
        'code': 'neg_code',
        'tv': 'neg_tv',
        'bid': 'neg_bid',
        'ask': 'neg_ask',
        'bid_vol': 'neg_bid_vol',
        'ask_vol': 'neg_ask_vol',
        'sigma': 'neg_sigma',
        'delta': 'neg_delta'
    })

    # 合并数据
    combined_data = pd.merge(
        positive_delta,
        negative_delta[['time', 'exp', 'K', 'neg_code', 'neg_tv', 'neg_bid', 'neg_ask', 'neg_bid_vol', 'neg_ask_vol',
                        'neg_delta']],
        on=['time', 'exp', 'K'],
        how='left'
    )
    # 修改 forward 计算，使用 PCP (Put-Call Parity) 公式
    # C - P = F - K * e^(-r*T)
    # 因此 F = C - P + K * e^(-r*T)
    combined_data['forward'] = (combined_data['tv'] - combined_data['neg_tv'] +
                                combined_data['K'] * np.exp(-combined_data['rf'] * combined_data['time2expiry']))
    # combined_data = combined_data.drop(columns=['rf', 'br'])
    return combined_data


def plot_volatility_curves_interactive(data):
    fig, ax = plt.subplots(figsize=(12, 8))
    plt.subplots_adjust(bottom=0.3)

    unique_times = sorted(data['time'].unique())
    vol_min, vol_max = data['sigma'].min(), data['sigma'].max()

    def update_plot(time):
        ax.clear()
        current_data = data[data['time'] == time]
        current_spot = current_data['spot'].iloc[0]

        for exp in current_data['exp'].unique():
            exp_data = current_data[current_data['exp'] == exp].sort_values('delta')
            line, = ax.plot(exp_data['K'], exp_data['sigma'], label=f'到期时间: {exp}')
            color = line.get_color()

            # 正delta的bid_vol和ask_vol
            ax.scatter(exp_data['K'], exp_data['bid_vol'], marker='^', color=color, s=30, alpha=0.7)
            ax.scatter(exp_data['K'], exp_data['ask_vol'], marker='v', color=color, s=30, alpha=0.7)

            # 负delta的bid_vol和ask_vol（使用空心三角形）
            ax.scatter(exp_data['K'], exp_data['neg_bid_vol'], marker='^', edgecolors=color, facecolors='none', s=30,
                       linewidth=0.3)
            ax.scatter(exp_data['K'], exp_data['neg_ask_vol'], marker='v', edgecolors=color, facecolors='none', s=30,
                       linewidth=0.)

        ax.axvline(x=current_spot, color='r', linestyle='--', label=f'当前Spot: {current_spot:.2f}')

        ax.set_title(f'波动率曲线 - 时间点: {time}\nSpot: {current_spot:.2f}')
        ax.set_xlabel('行权价 (K)')
        ax.set_ylabel('波动率 (Sigma)')
        ax.set_ylim(vol_min, vol_max)
        ax.legend()
        ax.grid(True)
        fig.canvas.draw_idle()

    def format_time(x, p):
        return unique_times[int(x)].strftime('%H:%M:%S')

    ax_time = plt.axes([0.1, 0.15, 0.8, 0.03])
    time_slider = Slider(ax_time, '时间', 0, len(unique_times) - 1, valinit=0, valstep=1, valfmt='%s')
    time_slider.valtext.set_text(format_time(0, None))
    ax_time.xaxis.set_major_formatter(plt.FuncFormatter(format_time))

    def update(val):
        time_index = int(val)
        update_plot(unique_times[time_index])
        time_slider.valtext.set_text(format_time(time_index, None))

    time_slider.on_changed(update)

    def create_button(position, label):
        ax = plt.axes(position)
        return Button(ax, label)

    def adjust_time(delta_seconds, event):
        current_index = int(time_slider.val)
        new_time = unique_times[current_index] + pd.Timedelta(seconds=delta_seconds)
        new_index = min(range(len(unique_times)), key=lambda i: abs(unique_times[i] - new_time))
        time_slider.set_val(new_index)

    buttons = [
        create_button([0.2, 0.05, 0.1, 0.04], '-1秒'),
        create_button([0.31, 0.05, 0.1, 0.04], '+1秒'),
        create_button([0.6, 0.05, 0.1, 0.04], '-1分钟'),
        create_button([0.71, 0.05, 0.1, 0.04], '+1分钟')
    ]

    for button, delta in zip(buttons, [-1, 1, -60, 60]):
        button.on_clicked(partial(adjust_time, delta))

    update_plot(unique_times[0])
    plt.show()


if __name__ == "__main__":
    live_mode = True  # 默认使用实时模式
    input_path = r'G:\DATA\SH300\vols_20240930.parquet'
    output_path = r'G:\DATA\SH300\vols_20240930_combined.csv'

    data = vol_cal(input_path, live_mode=live_mode)
    data = combine_delta_data(data)
    plot_volatility_curves_interactive(data)

    # 仅在非实时模式下保存数据
    if not live_mode:
        data.to_csv(output_path, index=False)
