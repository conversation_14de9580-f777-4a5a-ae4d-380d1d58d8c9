
"""
时间序列因子模块
包含所有与时间序列相关的因子定义
-- author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory
from ..factor_manager import calculate_imbalance


for window in [5, 10, 20]:
    
    factor_manager.register_factor(Factor(
        name=f"mid_return_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期中间价收益率",
        calculation=lambda data, w=window: data['mid'].pct_change(w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining"
    ))

    
    factor_manager.register_factor(Factor(
        name=f"trade_price_return_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期成交价收益率",
        calculation=lambda data, w=window: data['LastPrice'].pct_change(w),
        dependencies=["LastPrice"],
        parameters={"window": window},
        source="lining"
    ))


    
    def calculate_mid_ma(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        return mid.rolling(window=window).mean()


    factor_manager.register_factor(Factor(
        name=f"mid_ma_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期中间价移动平均",
        calculation=lambda data, w=window: calculate_mid_ma(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining"
    ))


    
    def calculate_volatility(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        return mid.pct_change().rolling(window=window).std()


    factor_manager.register_factor(Factor(
        name=f"volatility_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"中间价{window}周期收益率的滚动标准差",
        calculation=lambda data, w=window: calculate_volatility(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining"
    ))


    
    def calculate_vol_ma(data: pd.DataFrame, window: int) -> pd.Series:
        
        return data['tradedVol'].rolling(window=window).mean()


    factor_manager.register_factor(Factor(
        name=f"vol_ma_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期成交量移动平均",
        calculation=lambda data, w=window: calculate_vol_ma(data, w),
        dependencies=["tradedVol"],
        parameters={"window": window},
        source="lining"
    ))


    
    def calculate_vol_std(data: pd.DataFrame, window: int) -> pd.Series:
        
        return data['tradedVol'].rolling(window=window).std()


    factor_manager.register_factor(Factor(
        name=f"vol_std_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期成交量标准差",
        calculation=lambda data, w=window: calculate_vol_std(data, w),
        dependencies=["tradedVol"],
        parameters={"window": window},
        source="lining"
    ))


    
    def calculate_imbalance_ma(data: pd.DataFrame, window: int) -> pd.Series:
        
        if 'imbalance_1' not in data.columns:
            imbalance = calculate_imbalance(data, 1)
        else:
            imbalance = data['imbalance_1']
        return imbalance.rolling(window=window).mean()


    factor_manager.register_factor(Factor(
        name=f"imbalance_ma_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期一档不平衡度移动平均",
        calculation=lambda data, w=window: calculate_imbalance_ma(data, w),
        dependencies=["BidVol1", "AskVol1"],
        parameters={"window": window},
        source="lining"
    ))


    
    def calculate_realized_absvar(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        
        abs_returns = np.abs(log_return)
        sum_abs_returns = abs_returns.rolling(window=window).sum()
        count = log_return.rolling(window=window).count()
        
        
        result = np.sqrt(np.pi / (2 * count)) * sum_abs_returns
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_absvar_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现绝对变异",
        calculation=lambda data, w=window: calculate_realized_absvar(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    
    def calculate_realized_bipowvar(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        
        abs_returns = np.abs(log_return)
        abs_returns_shifted = np.abs(log_return.shift(1))
        
        
        product_sum = (abs_returns * abs_returns_shifted).rolling(window=window).sum()
        
        
        count = log_return.rolling(window=window).count()
        
        
        result = (np.pi / 2) * (count / (count - 2)) * product_sum
        
        
        result = result.where(count >= 3, np.nan)
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_bipowvar_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现双幂变异",
        calculation=lambda data, w=window: calculate_realized_bipowvar(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    
    def calculate_realized_skew(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        
        log_return_squared = log_return ** 2
        log_return_cubed = log_return ** 3
        
        
        rv = log_return_squared.rolling(window=window).sum().pow(0.5)
        sum_cubed = log_return_cubed.rolling(window=window).sum()
        count_sqrt = log_return.rolling(window=window).count().pow(0.5)
        
        
        result = count_sqrt * sum_cubed / rv.pow(3)
        
        
        result = result.where(rv != 0, np.nan)
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_skew_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现偏度",
        calculation=lambda data, w=window: calculate_realized_skew(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    
    def calculate_realized_kurtosis(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        
        log_return_squared = log_return ** 2
        log_return_quad = log_return ** 4
        
        
        rv_squared = log_return_squared.rolling(window=window).sum().pow(2)
        sum_quad = log_return_quad.rolling(window=window).sum()
        count = log_return.rolling(window=window).count()
        
        
        result = count * sum_quad / rv_squared
        
        
        result = result.where(rv_squared != 0, np.nan)
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_kurtosis_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现峰度",
        calculation=lambda data, w=window: calculate_realized_kurtosis(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    
    def calculate_realized_quarticity(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        
        log_return_quad = log_return ** 4
        sum_quad = log_return_quad.rolling(window=window).sum()
        count = log_return.rolling(window=window).count()
        
        
        result = (count / 3) * sum_quad
        
        return result


    factor_manager.register_factor(Factor(
        name=f"realized_quarticity_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期实现四次方",
        calculation=lambda data, w=window: calculate_realized_quarticity(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))


    
    def calculate_bpv_jump(data: pd.DataFrame, window: int) -> pd.Series:
        
        mid = (data['AskPrice1'] + data['BidPrice1']) / 2
        log_return = np.log(mid / mid.shift(1))
        
        
        log_return_squared = log_return ** 2
        rv = np.sqrt(log_return_squared.rolling(window=window).sum())
        
        
        abs_returns = np.abs(log_return)
        abs_returns_shifted = np.abs(log_return.shift(1))
        abs_product = abs_returns * abs_returns_shifted
        
        
        count = log_return.rolling(window=window).count()
        valid_count_mask = count >= 3
        
        
        rbv = pd.Series(np.nan, index=log_return.index)
        rbv_values = (np.pi / 2) * (count / (count - 2)) * abs_product.rolling(window=window).sum()
        rbv.loc[valid_count_mask] = rbv_values.loc[valid_count_mask]
        
        
        return np.maximum(rv - rbv, 0)


    factor_manager.register_factor(Factor(
        name=f"BPV_jump_{window}",
        category=FactorCategory.TIME_SERIES,
        description=f"{window}周期跳跃过程",
        calculation=lambda data, w=window: calculate_bpv_jump(data, w),
        dependencies=["AskPrice1", "BidPrice1"],
        parameters={"window": window},
        source="lining-zh/291948456/2486930660"
    ))



def calculate_autocorrelation(data: pd.DataFrame, window: int, lag: int) -> pd.Series:
    
    mid = (data['AskPrice1'] + data['BidPrice1']) / 2
    returns = mid.pct_change()
    
    
    returns_lag = returns.shift(lag)
    
    
    
    returns_mean = returns.rolling(window=window).mean()
    returns_lag_mean = returns_lag.rolling(window=window).mean()
    
    
    cov = (returns - returns_mean) * (returns_lag - returns_lag_mean)
    cov = cov.rolling(window=window).sum()
    
    
    returns_std = returns.rolling(window=window).std()
    returns_lag_std = returns_lag.rolling(window=window).std()
    
    
    denominator = returns_std * returns_lag_std
    
    autocorr = cov / denominator.where(denominator != 0, np.nan)
    
    return autocorr



for window in [5, 10, 20]:
    for lag in [1, 2, 3]:
        factor_manager.register_factor(Factor(
            name=f"autocorr_{window}_{lag}",
            category=FactorCategory.TIME_SERIES,
            description=f"{window}周期{lag}阶自相关",
            calculation=lambda data, w=window, l=lag: calculate_autocorrelation(data, w, l),
            dependencies=["AskPrice1", "BidPrice1"],
            parameters={"window": window, "lag": lag},
            source="lining-orderbook-dynamic-features v7"
        ))



for i in range(1, 6):
    factor_manager.register_factor(Factor(
        name=f"dAskPrice{i}_dt",
        category=FactorCategory.TIME_SERIES,
        description=f"AskPrice{i}的时间导数",
        calculation=lambda data, i=i: data[f'AskPrice{i}'].diff(),
        dependencies=[f"AskPrice{i}"],
        source="lining-orderbook-dynamic-features v6"
    ))
    factor_manager.register_factor(Factor(
        name=f"dBidPrice{i}_dt",
        category=FactorCategory.TIME_SERIES,
        description=f"BidPrice{i}的时间导数",
        calculation=lambda data, i=i: data[f'BidPrice{i}'].diff(),
        dependencies=[f"BidPrice{i}"],
        source="lining-orderbook-dynamic-features v6"
    ))
    factor_manager.register_factor(Factor(
        name=f"dAskVol{i}_dt",
        category=FactorCategory.TIME_SERIES,
        description=f"AskVol{i}的时间导数",
        calculation=lambda data, i=i: data[f'AskVol{i}'].diff(),
        dependencies=[f"AskVol{i}"],
        source="lining-orderbook-dynamic-features v6"
    ))
    factor_manager.register_factor(Factor(
        name=f"dBidVol{i}_dt",
        category=FactorCategory.TIME_SERIES,
        description=f"BidVol{i}的时间导数",
        calculation=lambda data, i=i: data[f'BidVol{i}'].diff(),
        dependencies=[f"BidVol{i}"],
        source="lining-orderbook-dynamic-features v6"
    ))