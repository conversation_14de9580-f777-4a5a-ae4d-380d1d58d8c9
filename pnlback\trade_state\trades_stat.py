import datetime
import math
import os
import sys

import numpy as np
import pandas as pd

sys.path.extend(
    [os.path.abspath(os.path.join(os.path.abspath(__file__) if '__file__' in globals() else os.getcwd(), *(['..'] * i)))
     for i in range(5)])

from db_solve.parreader import readmd, loadtrade, load_orders, loadvol
from db_solve.utility import drop_col_nan, time_resh
from pnlback.signal.signals import Signal

# 设置 Pandas 显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)
pd.set_option('display.expand_frame_repr', False)


def main(config):
    print('load fut md')

    fut1_data = readmd(config['key1'], [config['fut']], config['datetoday'], config['tradetime00'],
                       source=4, coltype='all', minnum=6, cal_sig=False, sig=config['siglist'])
    fut1_data['dsvol'] = fut1_data['Volume'].diff(1)
    fut1_data['dsstate'] = fut1_data['State'].diff(1)
    fut1_data['dropcol'] = abs(fut1_data['dsmid']) + abs(fut1_data['dsvol'])
    fut1_data['avg_prc'] = fut1_data['avg_prc'].round(2) - fut1_data['mid']
    fut1_data['dsavg'] = fut1_data['avg_prc'].diff(1)
    fut1_data['LastPrice'] = fut1_data['LastPrice'] - fut1_data['mid']
    fut1_data['dmidminum'] = fut1_data['mid_minnum'].diff(1)
    fut1_data['emamin'] = np.round(fut1_data['dmidminum'].ewm(alpha=0.5).mean(), 2)
    fut1_data['p20'] = np.round(fut1_data['mid'] - fut1_data['mid'].rolling(window=20).mean(), 2)

    print('md done')

    fut1_data = fut1_data.add_suffix('_x')

    colmix1 = list(map(lambda a: a + '_x',
                       ['LastPrice', 'dsvol', 'avg_prc', 'BidVol1', 'AskVol1', 'emamin', 'p20'] + config['siglist'] +
                       ['edge', 'mid', 'dsmid', ]))

    colmix7 = []
    colmix72 = []

    colmix2 = []
    if config['mix2'] == 1:
        print('load fut2 md')
        md_data1 = readmd(config['key2'], [config['fut2']], config['datetoday'], config['tradetime00'], cal_sig=False)
        md_data1['fut'] = md_data1['mid']
        md_data1['dsvol'] = md_data1['Volume'].diff(1)
        md_data1['edge'] = (md_data1['AskPrice1'] - md_data1['BidPrice1'])

        md_data1 = pd.merge_asof(md_data1, fut1_data[colmix1],
                                 left_index=True, right_index=True, direction='backward',
                                 tolerance=pd.Timedelta('100000000s'), allow_exact_matches=True)

        md_data1['basis'] = md_data1['mid_x'] - md_data1['mid']
        md_data1['dsbasis'] = md_data1['basis'].diff(1)
        md_data1['basisema'] = np.round(md_data1['dsbasis'].ewm(alpha=0.2).mean(), 2)

        md_data1 = md_data1.add_suffix('_f')
        colmix2 = list(map(lambda a: a + '_f',
                           [config['col2'], 'dsmid', 'basisema', 'dsvol', 'edge'] + colmix72))

        fut1_data = fut1_data.join(md_data1[colmix2], lsuffix='_x', rsuffix='_y', how='outer')
        fut1_data = fut1_data.sort_index()

    colmix3 = []
    if config['mix3'] == 1:
        print('load fut3 md2')
        md_data2 = readmd(config['key3'], [config['fut3']], config['datetoday'], config['tradetime00'], cal_sig=False)
        md_data2['fut'] = md_data2['mid']
        if config['col3'] == 'ForQuoteSysID':
            md_data2 = md_data2[md_data2['Source'] == 7]
        elif config['fut3'][0:2] == 'SH':
            md_data2 = md_data2[md_data2['Source'] == 4]
        md_data2['dslast'] = md_data2[config['col3']].diff(1)
        md_data2 = drop_col_nan(md_data2, 'dslast')
        md_data2['dsvol'] = md_data2['Volume'].diff(1)
        md_data2 = md_data2.add_suffix('_z')

        colmix3 = list(map(lambda a: a + '_z', ['dslast', 'dsvol']))

        fut1_data = fut1_data.join(md_data2[colmix3], how='outer')
        fut1_data = fut1_data.sort_index()

    colmix_trade = []
    if config['mixtrd'] == 1:
        print('load trade')
        trade_data = loadtrade(config['datetoday'], config['tradepath'], config['tradetime00'], multi=config['multi'],
                               multi2=config['multi2'])
        trade_data['Spot'] = (trade_data['Spot'] * config['s_mult']).round(1)
        print('done trade')

        trade_data['Code'] = trade_data['Code'].astype(str)
        trade_data['Edge'] = trade_data['Edge'].round(int(math.log10(config['s_mult'])) + 2) * config['s_mult']

        trade_data = pd.merge_asof(trade_data, fut1_data[['mid_x']].rename(columns={'mid_x': 'fut'}).dropna(),
                                   left_index=True, right_index=True, direction='backward',
                                   tolerance=pd.Timedelta('1s'), allow_exact_matches=True)

        trade_data['sdiff'] = trade_data['Spot'] - trade_data['fut']
        # trade_data['dpnldiff'] = trade_data['Spot'].diff(1)
        trade_data['dpnldiff'] = trade_data['PDeltaU'].shift(1)*trade_data['Spot'].diff(1)/trade_data['Spot'].round(0)

        colmix_trade = ['Code', u'类型', u'数量', u'价格', 'Spot', 'sdiff', 'Vol', 'Basis', 'Tv', 'AdjTv', 'Delta',
                        'PDeltaU', 'Vega', 'monthPVega', 'PNL', 'TradePNL', 'Edge', 'ordertime', 'per_delta',
                        'dpnldiff']

        if config['mix7'] == 1:
            ordersdata = load_orders(config['str_ord'], [], config['datetoday'], config['tradetime00'])

            ordersdata = ordersdata[(ordersdata['stateStr'] == 'BeforSending')]
            ordersdata.index = ordersdata['insertTime']
            ordersdata = ordersdata.sort_index()

            mdlong = ordersdata[ordersdata['directionStr'].isin(config['longdir'])]
            mdshort = ordersdata[ordersdata['directionStr'].isin(config['shortdir'])]

            trade_data = pd.merge_asof(trade_data, mdlong[['insertTime', 'Symbol', 'price']],
                                       left_index=True, right_index=True, direction='backward',
                                       left_by='Code', right_by='Symbol',
                                       tolerance=pd.Timedelta('100000000s'), allow_exact_matches=True)
            trade_data = pd.merge_asof(trade_data,
                                       mdshort[['insertTime', 'Symbol', 'price', 'DeltaAdj', 'vegaAdj']],
                                       left_index=True, right_index=True, direction='backward',
                                       left_by='Code', right_by='Symbol',
                                       tolerance=pd.Timedelta('100000000s'), allow_exact_matches=True)

            trade_data = trade_data.sort_index()

            trade_data['bid_e'] = -(trade_data['price_x'] - trade_data['AdjTv']).round(
                int(math.log10(config['s_mult'])) + 2) * config['s_mult']
            trade_data['ask_e'] = (trade_data['price_y'] - trade_data['AdjTv']).round(
                int(math.log10(config['s_mult'])) + 2) * config['s_mult']
            trade_data['Edge2'] = np.maximum(trade_data['bid_e'].fillna(0), trade_data['ask_e'].fillna(0))
            trade_data['REdge_mult'] = (trade_data['Edge'] / np.maximum(trade_data['Edge2'],
                                                                        config['mintick'] * config['s_mult'])).round(1)
            trade_data['edge_diff'] = -(trade_data['bid_e'] - trade_data['ask_e']).round(2)
            trade_data['diff/delta'] = (
                    trade_data['edge_diff'] / abs(trade_data['per_delta'])).round(2)
            trade_data['spotPAdj'] = (
                    trade_data['DeltaAdj'] / (trade_data['per_delta']) * config['s_mult']).round(2)
            trade_data['volPAdj'] = (
                    trade_data['vegaAdj'] / ((trade_data['Vega']) / trade_data['multi'] / trade_data[u'数量'])).round(2)

            fut1_data = fut1_data.join(trade_data, lsuffix='_x', rsuffix='_y', how='outer')

            colmix7 = ['REdge_mult', 'insertTime_x', 'insertTime_y', 'bid_e', 'price_x', 'price_y', 'ask_e',
                       'edge_diff', 'diff/delta', 'spotPAdj', 'volPAdj', 'DeltaAdj', 'vegaAdj', ]

            fut1_data = pd.merge_asof(fut1_data,
                                      mdlong[mdlong.Symbol.isin(config['optcodes'])][
                                          ['insertTime', 'Symbol', 'price', 'volume']].add_suffix(
                                          '_l'),
                                      left_index=True, right_index=True, direction='forward',
                                      tolerance=pd.Timedelta('20ms'), allow_exact_matches=True)
            fut1_data = pd.merge_asof(fut1_data,
                                      mdshort[mdshort.Symbol.isin(config['optcodes'])][
                                          ['insertTime', 'Symbol', 'price', 'volume']].add_suffix(
                                          '_r'),
                                      left_index=True, right_index=True, direction='forward',
                                      tolerance=pd.Timedelta('20ms'), allow_exact_matches=True)

            fut1_data['edge_or'] = -(fut1_data['price_l'] - fut1_data['price_r']) / 2 * config['s_mult']
            fut1_data['time_or'] = (
                    (np.where(fut1_data['insertTime_l'].notnull(), fut1_data['insertTime_l'], fut1_data['insertTime_r'])
                     - fut1_data.index.values) / np.timedelta64(1, 's') * config['s_mult'])
            fut1_data['mid_or'] = ((fut1_data['price_l'] + fut1_data['price_r']) / 2).round(
                int(math.log10(config['multi'])))
            colmix72 = ['time_or', 'volume_l', 'price_l', 'price_r', 'volume_r', 'edge_or', 'mid_or', ]

            fut1_data['drop'] = np.where(fut1_data['insertTime_l'].notnull(), fut1_data['insertTime_l'].diff(-1),
                                         fut1_data['insertTime_r'].diff(-1))
            fut1_data.loc[fut1_data['drop'] == pd.Timedelta(0), colmix72] = np.nan
        else:
            fut1_data = fut1_data.join(trade_data, lsuffix='_x', rsuffix='_y', how='outer')

    colmix4 = []
    if config['mix4'] == 1:
        print('load fut3 md2')
        md_data2 = readmd(config['key4'], [config['fut4']], config['datetoday'], config['tradetime00'], cal_sig=False)
        md_data2['fut'] = md_data2['mid']
        md_data2 = md_data2[md_data2['Source'] == 7]
        md_data2['dslast'] = md_data2[config['col3']].diff(1)
        md_data2['dd'] = abs(md_data2['dslast']) + abs(md_data2['dsmid'])

        md_data2['dvol'] = md_data2['Volume'].diff(1)
        md_data2 = drop_col_nan(md_data2, 'dvol')
        md_data2 = md_data2.add_suffix('_k')

        colmix4 = [config['col3'] + '_k', 'dsmid_k', 'dslast_k', 'dvol_k', ]

        fut1_data = fut1_data.join(md_data2[colmix4], how='outer')
        fut1_data = fut1_data.sort_index()

    colmix44 = []
    if config['mix44'] == 1:
        print('load fut44 md2')
        md_data2 = readmd(config['key44'], [config['fut44']], config['datetoday'], config['tradetime00'], cal_sig=False)
        md_data2['fut'] = md_data2[config['col44']]
        md_data2 = md_data2[md_data2['Source'] == 7]
        md_data2['dslast'] = md_data2[config['col44']].diff(1)
        md_data2 = md_data2.add_suffix('_i')

        colmix44 = [config['col44'] + '_i', 'dslast_i']

        fut1_data = fut1_data.join(md_data2[colmix44], how='outer')
        fut1_data = fut1_data.sort_index()

    colmix5 = []
    if config['mix5'] == 1:
        print('load vol')
        md_data2 = loadvol(config['str_vol'] % config['datetoday'], config['optcodes'], config['tradetime00'],
                           all=False)

        md_data2['forward'] = md_data2['spot'] * np.exp(
            md_data2.rf * md_data2.time2expiry - md_data2['br'] * md_data2['time2expiry'])
        md_data2['basis'] = md_data2['forward'] - md_data2['spot']
        md_data2['mid'] = md_data2['bid'] / 2 + md_data2['ask'] / 2

        md_data2['dsspot'] = md_data2['spot'].diff(1).round(int(math.log10(config['s_mult'])) + 2)
        md_data2['dsmid'] = md_data2['mid'].diff(1).round(int(math.log10(config['s_mult'])) + 2)
        md_data2['dsbasis'] = md_data2['basis'].diff(1).round(int(math.log10(config['s_mult'])) + 2)
        md_data2['dsbasis_M'] = (md_data2['dsmid'] / md_data2['delta'] - md_data2['dsspot']).round(
            int(math.log10(config['s_mult'])) + 2)

        md_data2['s'] = (md_data2['spot'].diff(1) * md_data2['delta']).round(int(math.log10(config['s_mult'])) + 2)
        md_data2['b'] = (md_data2['basis'].diff(1) * md_data2['delta']).round(int(math.log10(config['s_mult'])) + 2)
        md_data2['v'] = (md_data2['sigma'].diff(1) * md_data2['vega'] * 100).round(
            int(math.log10(config['s_mult'])) + 2)
        md_data2['dtv'] = (md_data2['tv'].diff(1)).round(int(math.log10(config['s_mult'])) + 2)

        voldata = md_data2.add_suffix('_v')

        colmix5 = ['spot_v', 'tv_v', 'sigma_v', 'mid_v', 'dsmid_v', 'dsbasis_v', 'dsspot_v', 'dsbasis_M_v']

        fut1_data['mid_xv'] = (fut1_data['mid_x'].fillna(0) * 10).astype(int)
        voldata['spot_v'] = (voldata['spot_v'] * config['s_mult'] * 10).astype(int)
        fut1_data = fut1_data.join(voldata[colmix5], how='outer')
        fut1_data['spot_v'] = fut1_data['spot_v'].astype(float) / 10
        fut1_data = fut1_data.sort_index()

    colmix6 = []
    colmix66 = []
    if config['mix6'] == 1:
        md_data2 = readmd(config['str_optmd'], None, config['datetoday'], config['tradetime00'], mult=config['multi'],
                          coltype='all', cal_sig=False)

        fut1_data = pd.merge_asof(fut1_data,
                                  md_data2[['timestamp_str', 'Symbol', 'BidVol1', 'BidPrice1', 'AskPrice1', 'AskVol1',
                                            'LastPrice', 'Volume', 'tradedVol', 'avg_prc']].add_suffix('_tm'),
                                  left_index=True, right_index=True, direction='forward',
                                  left_by='Code', right_by='Symbol_tm',
                                  tolerance=pd.Timedelta('600ms'), allow_exact_matches=True)
        fut1_data['avg_prc_tm'] = fut1_data['avg_prc_tm'] * config['multi'] / fut1_data['multi']

        fut1_data['avg_edge2'] = np.round(abs(fut1_data['avg_prc_tm'] - fut1_data['AdjTv']) * config['s_mult'] /
                                          abs(np.maximum(fut1_data['Edge2'], config['mintick'] * config['s_mult'])), 1)
        fut1_data['avg_edge'] = np.round(abs(fut1_data['avg_prc_tm'] - fut1_data['AdjTv']) * config['s_mult'] /
                                         (np.maximum(abs(config['edgedelta'] * fut1_data['per_delta']),
                                                     config['mintick']) * config['s_mult']), 1)

        colmix66 = (list(
            map(lambda a: a + '_tm',
                ['timestamp_str', 'BidVol1', 'BidPrice1', 'AskPrice1', 'AskVol1', 'LastPrice', 'tradedVol', 'avg_prc']))
                    + ['avg_edge'])

        md_data2 = md_data2[md_data2['Symbol'].isin(config['optcodes'])].drop_duplicates(
            subset=['timestamp_str', 'Symbol'])
        sss = Signal(md_data2, minnum=20, roundnum=config['s_mult'], sig=['edge_minnum'])
        sss.sigall()
        md_data2 = sss.data_md

        md_data2['dsmid'] = (md_data2['mid'].diff(1) * config['s_mult']).round(2)
        md_data2['edge'] = (md_data2['AskPrice1'] - md_data2['BidPrice1']) * config['s_mult']

        md_data2 = md_data2.add_suffix('_m')

        colmix6 = ['BidVol1_m', 'BidPrice1_m', 'AskPrice1_m', 'AskVol1_m', 'mid_m', 'dsmid_m',
                   'LastPrice_m', 'tradedVol_m', 'avg_prc_m']

        fut1_data = fut1_data.join(md_data2[colmix6], how='outer')
        fut1_data = fut1_data.sort_index()

    fut1_data = fut1_data.sort_index()

    fut1_data = time_resh(fut1_data)

    fut1_data[
        colmix1 + colmix2 + colmix3 + colmix72 + colmix6 + colmix4 + colmix44 + colmix5 +
        colmix_trade + colmix7 + colmix66].round(config['roundnum']).to_csv(
        config['outdirs'] + (config['datetoday'] + config['under'] + 'trades_stat_mix2'
                             + datetime.datetime.now().strftime("_%m%d_%H%M%S") + '.csv'),
        encoding='utf-16', sep='\t')

    print('all done')


if __name__ == '__main__':
    from pnlback.trade_state.configs import update_dynamic_config, DEFAULT_CONFIG

    config = DEFAULT_CONFIG.copy()
    config.update({
        'under': ['SH500', 'SH300', 'SZ300'][0],
        'optcodes': ['10009551'],
        'datetoday': '20250805',
        'month': '2508',
        'month2': '2509',
        'mix2': 1, # 期货
        'mix3': 1, # a50
        'mix4': 0, # etf 逐笔
        'mix44': 0, # etf
        'mix5': 1, # vol
        'mix6': 1, # 期权
        'mix7': 1, # orders
        'mixtrd': 1, # 成交
        'fut3': 'CN2508',
        'fut4': '510500_tick',
        'fut44': '159915',
    })
    DEFAULT_CONFIG = update_dynamic_config(config)
    main(config)
    print('success', config['datetoday'], datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'))
    os.startfile(config['outdirs'])
    sys.exit()
