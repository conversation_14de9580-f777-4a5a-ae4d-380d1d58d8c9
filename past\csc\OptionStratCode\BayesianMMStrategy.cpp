#include <chrono>
#include <sstream>
#include <iostream>
#include <cmath>
#include <cstring>
#include <chrono>
#include <vectorclass.h>
#include <vectormath_lib.h>

#include "BayesianMMStrategy.h"
#include "OrderService.h"
#include "../InstrumentManager.h"
#include "MarketDataService.h"
#include "RiskService.h"
#include "DatabaseManager.h"
#include "OptionPricingUtility.h"
#include "VolatilityService.h"


using namespace std;
using namespace std::chrono;

BayesianMMStrategy::BayesianMMStrategy(int id_, PriceSuggestor priceSuggester_, int portfolioId_,
        StrategyTypeId strategyTypeId_, bool isAlive_, StrategyStatus strategyStatus_, std::string const& parameters_,
        std::set<int> const& appliedInstruments_, std::string const& comments_, int level_, bool isQuotingStrategy_,
        bool useQuoteService_, bool isQuoteDeleteRequired_, bool beGrouped_, int groupId_) :
    Strategy(id_, priceSuggester_, portfolioId_, strategyTypeId_, isAlive_, strategyStatus_, parameters_, appliedInstruments_, comments_, level_,
            isQuotingStrategy_, useQuoteService_, isQuoteDeleteRequired_)
{
    setParameters(parameters_);
}

std::string BayesianMMStrategy::getParameters() const
{
    stringstream ss;
    ss << _maxSpreadThreshold
        << _delimeter << _deltaHedgeLowerBound
        << _delimeter << _deltaHedgeUpperBound
        << _delimeter << _maxOrderVolume
        << _delimeter << _vegaFadeLowerBound
        << _delimeter << _vegaFadeUpperBound
        << _delimeter << _maxVolOffset
        << _delimeter << _targetDelta
        << _delimeter << _targetVega
        << _delimeter << _hedgingSpreadLimitTicks
        << _delimeter << _faderBaseVolume
        << _delimeter << _futureHedgeReturnIntercept
        << _delimeter << _futureHedgeReturnSlope
        << _delimeter << _futureHedgeMargin
        << _delimeter << _futureHedgeMaxPosition
        << _delimeter << _futureHedgeMinInterceptTicks
        << _delimeter << _singleSideHedgeDeltaThreshold
        << _delimeter << _activeVegaHedge
        << _delimeter << _vegaHedgeInterval.count()
        << _delimeter << _vegaHedgeProtectBand
        << _delimeter << _Q
        << _delimeter << _k
        << _delimeter << _A
        << _delimeter << _gamma
        << _delimeter << _sigma
        << _delimeter << _ksi
        << _delimeter << _terminateSec
        << _delimeter << _boundaryConditionTicks
        << _delimeter << _useBasePrice
        << _delimeter << _QVol
        << _delimeter << _kVol
        << _delimeter << _AVol
        << _delimeter << _gammaVol
        << _delimeter << _sigmaVol
        << _delimeter << _ksiVol
        << _delimeter << _baseOffsetCycle
        << _delimeter << _baseMultiplier
        << _delimeter << _usePredictedBase
        << _delimeter << _bigChangeThreshold
        << _delimeter << _bigVolumeThreshold
        << _delimeter << _bigChangeRestoreSec
        << _delimeter << _bigChangeExtraBaseTicks
        << _delimeter << _lltFactor
        << _delimeter << _quickBSCalculation
        << _delimeter << _cpuId
        << _delimeter << _deltaFadePerTick
        << _delimeter << _maxDeltaFadeTicks
        << _delimeter << _vegaFadePerTick
        << _delimeter << _maxVegaFadeTicks
        << _delimeter << _deltaSpreadTicksBase
        << _delimeter << _vegaSpreadTicksBase
        << _delimeter << _spreadMultiplier
        << _delimeter << _dynamicBaseRefvolWindow
        << _delimeter << _dynamicBaseAlpha
        << _delimeter << _dynamicBaseBeta
        << _delimeter << _dynamicBaseWeight
        << _delimeter << _dynamicBaseMinvol
        << _delimeter << _hitSpreadRatio
		
        << _delimeter << _responseLatestStartTime
		<< _delimeter << _responseDelaySec
		<< _delimeter << _responseKeepSec
		<< _delimeter << _obligationSec
		<< _delimeter << _obligationLots
		<< _delimeter << _maxSpreadAlpha;
    return ss.str();
}
void BayesianMMStrategy::setParameters(std::string const& paramStr_)
{
    try
    {
        stringstream ss;
        ss.str(paramStr_);
        string value;
        if(!ss) return;
        if(getline(ss, value, _delimeter))
            _maxSpreadThreshold = stoi(value);
        if(getline(ss, value, _delimeter))
            _deltaHedgeLowerBound = stod(value);
        if (getline(ss, value, _delimeter))
            _deltaHedgeUpperBound = stod(value);
        if (getline(ss, value, _delimeter))
            _maxOrderVolume = stoi(value);
        if (getline(ss, value, _delimeter))
            _vegaFadeLowerBound = stod(value);
        if (getline(ss, value, _delimeter))
            _vegaFadeUpperBound = stod(value);
        if (getline(ss, value, _delimeter))
            _maxVolOffset = stod(value);
        if (getline(ss, value, _delimeter))
            _targetDelta = stod(value);
        if (getline(ss, value, _delimeter))
            _targetVega = stod(value);
        if (getline(ss, value, _delimeter))
            _hedgingSpreadLimitTicks = stoi(value);
        if (getline(ss, value, _delimeter))
            _faderBaseVolume = stoi(value);
        if (getline(ss, value, _delimeter))
            _futureHedgeReturnIntercept = stod(value);
        if (getline(ss, value, _delimeter))
            _futureHedgeReturnSlope = stod(value);
        if (getline(ss, value, _delimeter))
            _futureHedgeMargin = stod(value);
        if (getline(ss, value, _delimeter))
            _futureHedgeMaxPosition = stoi(value);
        if (getline(ss, value, _delimeter))
            _futureHedgeMinInterceptTicks = stoi(value);
        if (getline(ss, value, _delimeter))
            _singleSideHedgeDeltaThreshold = stod(value);
        if (getline(ss, value, _delimeter))
            _activeVegaHedge = stoi(value);
        if (getline(ss, value, _delimeter))
            _vegaHedgeInterval = std::chrono::milliseconds{stoi(value)};
        if (getline(ss, value, _delimeter))
            _vegaHedgeProtectBand = stod(value);
        if( getline(ss, value, _delimeter))
            _Q = stoi(value);
        if( getline(ss, value, _delimeter))
            _k = stod(value);
        if( getline(ss, value, _delimeter))
            _A = stod(value);
        if( getline(ss, value, _delimeter))
            _gamma = stod(value);
        if( getline(ss, value, _delimeter))
            _sigma = stod(value);
        if( getline(ss, value, _delimeter))
            _ksi = stod(value);
        if( getline(ss, value, _delimeter))
            _terminateSec = stoi(value);
        if( getline(ss, value, _delimeter))
            _boundaryConditionTicks = stoi(value);
        if( getline(ss, value, _delimeter))
            _useBasePrice = stoi(value);
        if( getline(ss, value, _delimeter))
            _QVol = stoi(value);
        if( getline(ss, value, _delimeter))
            _kVol = stod(value);
        if( getline(ss, value, _delimeter))
            _AVol = stod(value);
        if( getline(ss, value, _delimeter))
            _gammaVol = stod(value);
        if( getline(ss, value, _delimeter))
            _sigmaVol = stod(value);
        if( getline(ss, value, _delimeter))
            _ksiVol = stod(value);
        if( getline(ss, value, _delimeter))
            _baseOffsetCycle = stoi(value);
        if( getline(ss, value, _delimeter))
            _baseMultiplier = stoi(value);
        if( getline(ss, value, _delimeter))
            _usePredictedBase = stoi(value);
        if( getline(ss, value, _delimeter))
            _bigChangeThreshold = stod(value);
        if( getline(ss, value, _delimeter))
            _bigVolumeThreshold = stoi(value);
        if( getline(ss, value, _delimeter))
            _bigChangeRestoreSec = stoi(value);
        if( getline(ss, value, _delimeter))
            _bigChangeExtraBaseTicks = stoi(value);
        if( getline(ss, value, _delimeter))
            _lltFactor = stod(value);
        if( getline(ss, value, _delimeter))
            _quickBSCalculation = stoi(value);
        if( getline(ss, value, _delimeter))
            _cpuId = stoi(value);
        if( getline(ss, value, _delimeter))
            _deltaFadePerTick = stod(value);
        if( getline(ss, value, _delimeter))
            _maxDeltaFadeTicks = stoi(value);
        if( getline(ss, value, _delimeter))
            _vegaFadePerTick = stod(value);
        if( getline(ss, value, _delimeter))
            _maxVegaFadeTicks = stoi(value);
        if( getline(ss, value, _delimeter))
            _deltaSpreadTicksBase = stoi(value);
        if( getline(ss, value, _delimeter))
            _vegaSpreadTicksBase = stoi(value);
        if( getline(ss, value, _delimeter))
            _spreadMultiplier = stod(value);
        if (getline(ss, value, _delimeter))
            _dynamicBaseRefvolWindow = stoi(value);
        if (getline(ss, value, _delimeter))
            _dynamicBaseAlpha = stof(value);
        if (getline(ss, value, _delimeter))
            _dynamicBaseBeta = stof(value);
        if (getline(ss, value, _delimeter))
            _dynamicBaseWeight = stof(value);
        if (getline(ss, value, _delimeter))
            _dynamicBaseMinvol = stoi(value);
        if (getline(ss, value, _delimeter))
            _hitSpreadRatio = stod(value);
		
        if (getline(ss, value, _delimeter))
			_responseLatestStartTime = stoi(value);
		if (getline(ss, value, _delimeter))
			_responseDelaySec = stoi(value);
		if (getline(ss, value, _delimeter))
			_responseKeepSec = stoi(value);
		if (getline(ss, value, _delimeter))
			_obligationSec = stoi(value);
		if (getline(ss, value, _delimeter))
			_obligationLots = stoi(value);
		if (getline(ss, value, _delimeter))
			_maxSpreadAlpha = stod(value);
    }
    catch (exception const& e)
    {
        LOG4CPLUS_ERROR(_logger, "读取参数时出错:" << e.what());
    }
    catch (...)
    {
        LOG4CPLUS_ERROR(_logger, "读取参数时出错:无");
    }
}

//注意只有atomic类型可以快捷更新
void BayesianMMStrategy::setQuickParameters(std::string const& paramStr_)
{
	try
	{
		stringstream ss;
		ss.str(paramStr_);
		string value;
		if (!ss) return;
		if (getline(ss, value, _delimeter))
			_maxSpreadThreshold = stoi(value);
		if (getline(ss, value, _delimeter))
			_deltaHedgeLowerBound = stod(value);
		if (getline(ss, value, _delimeter))
			_deltaHedgeUpperBound = stod(value);
		if (getline(ss, value, _delimeter))
			_maxOrderVolume = stoi(value);
		if (getline(ss, value, _delimeter))
			_vegaFadeLowerBound = stod(value);
		if (getline(ss, value, _delimeter))
			_vegaFadeUpperBound = stod(value);
		if (getline(ss, value, _delimeter))
			_maxVolOffset = stod(value);
		if (getline(ss, value, _delimeter))
			_targetDelta = stod(value);
		if (getline(ss, value, _delimeter))
			_targetVega = stod(value);
		if (getline(ss, value, _delimeter))
			_hedgingSpreadLimitTicks = stoi(value);
        if (getline(ss, value, _delimeter))
            _faderBaseVolume = stoi(value);
	}
	catch (exception const& e)
	{
		LOG4CPLUS_ERROR(_logger, "读取参数时出错:" << e.what());
	}
	catch (...)
	{
		LOG4CPLUS_ERROR(_logger, "读取参数时出错:无");
	}
}

bool BayesianMMStrategy::initStrategyJobs()
{
    LOG4CPLUS_INFO(_logger, "Start init strategy...");
    if (_cpuId >= 0)
    {
        cpu_set_t mySet;
        CPU_ZERO(&mySet);
        CPU_SET(_cpuId, &mySet);
        if(sched_setaffinity(0, sizeof(cpu_set_t), &mySet)) 
        {
            LOG4CPLUS_WARN(_logger, "Set cpu affinity failed");
        }
    }
    for (int i = 0; i < _nMaxStrikes; ++i)
    {
        _strikeArray[i] = NAN;
        _volArray[i] = NAN;
        _callPrices[i] = NAN;
        _putPrices[i] = NAN;
        _callPtrArray[i] = nullptr;
        _putPtrArray[i] = nullptr;
    }
    clearAllQueues();
    OrderService::getInstance().registerTradeListener(this);
    Instrument* instruments = InstrumentManager::getInstance().getInstruments();
    auto appliedInstruments = _appliedInstruments;

    for (auto index : appliedInstruments)
    {
        auto &instrument = instruments[index];
        switch(instrument.instrumentType)
        {
            case InstrumentType::Options:
                if (!instrument.pBaseInstrument) continue;
                _pBaseInstrument = instrument.pBaseInstrument;
                _pAppliedOptionInstrument = &instrument;
                _timeToMaturity = static_cast<double>(instrument.pBaseInstrument->daysToExpiry) / 365;
                _pCurve = instrument.pCurve;
                break;
            case InstrumentType::Futures:
                if (!instrument.pBaseInstrument) continue;
                _pBaseInstrument = instrument.pBaseInstrument;
                _timeToMaturity = static_cast<double>(instrument.pBaseInstrument->daysToExpiry) / 365;
                _pHedgeFutures = &instrument;
                break;
            default:
                _appliedInstruments.erase(index);
                continue;

        }
        _positions.emplace(std::piecewise_construct, forward_as_tuple(instrument.instrumentId), forward_as_tuple(&instrument, _level));
    }
    //_pBaseInstrument->baseOffset = _pAppliedOptionInstrument->pExpiry->baseOffset/ (double)_pAppliedOptionInstrument->minTickMultiplier; 
    if (!_pBaseInstrument)
    {
        LOG4CPLUS_ERROR(_logger, "No base instrument");
        return false;
    }
    _baseMarketDataCount = 0;
    RiskService::getInstance().registerRiskListener(this);
    processRisks();
    RiskService::getInstance().unregisterRiskListener(this);
    for (auto pair : _riskMapCache)
    {
        LOG4CPLUS_DEBUG(_logger, ::toString(pair.second));
    }
    auto pBaseInstrument = _pBaseInstrument;
    pBaseInstrument->suggestedBidPrice[0].reset();
    pBaseInstrument->suggestedAskPrice[0].reset();
    auto pConsolidator = dynamic_pointer_cast<OptionSyntheticConsolidator>(pBaseInstrument->pInstrumentConsolidator);
    if (pConsolidator)
    {
        pConsolidator->subscribeOrderBook(this);
        _pConsolidator = pConsolidator;
        FutureHedgeParamUpdateReq req;
        req.futureHedgeReturnIntercept = _futureHedgeReturnIntercept;
        req.futureHedgeReturnSlope = _futureHedgeReturnSlope;
        req.futureHedgeMargin = _futureHedgeMargin;
        req.futureHedgeMaxPosition = _futureHedgeMaxPosition;
        req.futureHedgeMinInterceptTicks = _futureHedgeMinInterceptTicks;
        pConsolidator->requestFutureHedgeParamUpdate(req);
    }
    for (auto &pair : _positions)
    {
        auto& positionInfo = pair.second;
        auto pInstrument = pair.second.pInstrument;
        SubscribeRfqReq req;
        strcpy(req.instrumentId, pInstrument->instrumentId);
        req.pSubscriber = this;
        OrderService::getInstance().pushMessage(req);
        auto const& risk = _riskMapCache[pInstrument->instrumentId];
        positionInfo.position = risk.longPosition - risk.shortPosition;
        positionInfo.strike = pInstrument->strike;
        pInstrument->volatilityAdjustment = 0;
        switch(pInstrument->instrumentType)
        {
            case InstrumentType::Options:
                if (auto it = _positions.find(pInstrument->pPairInstrument->instrumentId); it != _positions.end())
                {
                    positionInfo.pConterpart = &it->second;
                }
                break;
            default:
                break;
        }
        LOG4CPLUS_INFO(_logger, "Instrument Initialized[" << "pInstrument=" << pInstrument->instrumentId << "|" << positionInfo.toString() << "]");
    }

    Feed feed;
    LOG4CPLUS_INFO(_logger, "Start querying orders...");
    OrderService::getInstance().pushMessage(QueryActiveOrdersReq{this});
    while(true)
    {
        if(_feedQueue.try_dequeue(feed))
        {
            if (holds_alternative<Order>(feed))
            {
                auto& order = get<Order>(feed);
                if(order.internalId != UNINITIALIZED_INT)
                {
                    if (auto it = _positions.find(order.instrumentId); it != _positions.end())
                    {
                        switch(order.orderMotive)
                        {
                            case OrderMotive::MarketMaking:
                                LOG4CPLUS_INFO(_logger, "Order[" << order << "]");
                                switch(order.longShort)
                                {
                                    case LONG_:
                                        it->second.bidOrder = order;
                                        break;
                                    case SHORT_:
                                        it->second.askOrder = order;
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
                else
                {
                    LOG4CPLUS_INFO(_logger, "End querying orders...");
                    break;
                }
            }
        }
    }
    LOG4CPLUS_INFO(_logger, "Start querying quotes...");
    OrderService::getInstance().pushMessage(QueryActiveQuotesReq{this});
    while(true)
    {
        if(_feedQueue.try_dequeue(feed))
        {
            if (holds_alternative<Quote>(feed))
            {
                auto& quote = get<Quote>(feed);
                if(quote.internalId != UNINITIALIZED_INT)
                {
                    if (auto it = _positions.find(quote.instrumentId); it != _positions.end())
                    {
                        LOG4CPLUS_INFO(_logger, "Quote[" << quote << "]");
                        it->second.quote = quote;
                    }
                }
                else
                {
                    LOG4CPLUS_INFO(_logger, "End querying quotes...");
                    break;
                }
            }
        }
    }
    
    std::map<PriceType, std::pair<InstrumentInfo*, InstrumentInfo*>> optionsPairByStrike;
    for (auto it = _positions.begin(); it != _positions.end(); ++it)
    {
        auto& instrumentInfo = it->second;
        switch(instrumentInfo.pInstrument->isCall)
        {
            case CALL:
                optionsPairByStrike[instrumentInfo.pInstrument->strike].first = &instrumentInfo;
                break;
            case PUT:
                optionsPairByStrike[instrumentInfo.pInstrument->strike].second = &instrumentInfo;
                break;
            default:
                break;
        }
    }
    if (!_pCurve) return false;
    VolatilityMessage volMsg;
    while(_volatilityQueue.try_dequeue(volMsg));
    SubscribeVolatilityMessage subVolMsg;
    subVolMsg.curveName = _pCurve->getName();
    subVolMsg.pSubscriber = this;
    VolatilityService::getInstance().pushMessage(subVolMsg);
    auto start = steady_clock::now();
    while(!_volatilityQueue.try_dequeue(volMsg))
    {
        if (steady_clock::now() - start > 2s)
        {
            LOG4CPLUS_ERROR(_logger, "Subscribe volatility timeout:" << subVolMsg.curveName);
            return false;
        }
    }
    LOG4CPLUS_INFO(_logger, "Volatility snapshot:" << volMsg);
    if (volMsg.nPoints > _nMaxStrikes)
    {
        LOG4CPLUS_ERROR(_logger, "nStrikes[" << volMsg.nPoints << "] in vol messge exceed nMaxStrikes[" << _nMaxStrikes << "]");
        return false;
    }
    _nStrikes = volMsg.nPoints;
    for (int i = 0; i < volMsg.nPoints; ++i)
    {
        auto const& point = volMsg.points[i];
        _strikeArray[i] = point.strike;
        _volArray[i] = point.quoteVolatility;
        if (auto it = optionsPairByStrike.find(point.strike); it != optionsPairByStrike.end())
        {
            _callPtrArray[i] = it->second.first;
            _putPtrArray[i] = it->second.second;
        }
    }
    _T = volMsg.timeToExpiry;

    recalcRisk();
    fadeDelta();
    fadeVega();
    fadeAllPosition();
    
    _marketDataReader.readLast();

    LOG4CPLUS_INFO(_logger, "Finish init strategy...");
   
    /*
    询价测试代码
    QuoteRequest quoteRequest;
    quoteRequest.receiveTime = steady_clock::now();
    static int id = 100;
    id++;
    STRCPY(quoteRequest.quoteRequestId,to_string(id).c_str());
    STRCPY(quoteRequest.internalQuoteRequestId, to_string(id).c_str());
    STRCPY(quoteRequest.instrumentId,_pAppliedOptionInstrument->instrumentId);
    quoteRequest.responsedFlag = false;
    OrderService::getInstance().pushMessage(quoteRequest);
    */
    return true;
}

bool BayesianMMStrategy::cleanUpStrategyJobs()
{
    LOG4CPLUS_INFO(_logger, "Start cleaning up strategy...");
    if (!_pCurve)
    {
        LOG4CPLUS_WARN(_logger, "no pCurve");
    }
    else
    {
        UnsubscribeVolatilityMessage unsubVolMsg;
        unsubVolMsg.curveName = _pCurve->getName();
        unsubVolMsg.pSubscriber = this;
        VolatilityService::getInstance().pushMessage(unsubVolMsg);
    }

    auto pBaseInstrument = _pBaseInstrument;
    if (pBaseInstrument)
    {
        auto pConsolidator = dynamic_pointer_cast<OptionSyntheticConsolidator>(pBaseInstrument->pInstrumentConsolidator);
        if (pConsolidator)
        {
            pConsolidator->unsubscribeOrderBook(this);
        }
        pBaseInstrument->suggestedBidPrice[0].reset();
        pBaseInstrument->suggestedAskPrice[0].reset();
        pBaseInstrument->volatilityBidSpread[_level] = -0.01;
        pBaseInstrument->volatilityAskSpread[_level] = 0.01;
    }

    _baseBid = NAN;
    _baseAsk = NAN;
    _baseMid = NAN;
    _baseMidOld = NAN;
    _midBuffer.clear();
    _lltBuffer.clear();
    _cashDelta = 0;
    _vega = 0;
    _baseMarketDataCount = 0;

    bool isDeleteAll = true;
    for (int i = 0; i < 2; i++)
    {
        processFeed();

        isDeleteAll = true;
        for (auto &pair : _positions)
        {
            auto& instrumentInfo = pair.second;
            UnsubscribeRfqReq req;
            strcpy(req.instrumentId, instrumentInfo.pInstrument->instrumentId);
            req.pSubscriber = this;
            OrderService::getInstance().pushMessage(req);
            // MarketDataService::getInstance().unsubscribeMarketData(this, instrumentInfo.pInstrument->instrumentId);
            instrumentInfo.position = 0;
            if(instrumentInfo.pConterpart) instrumentInfo.pConterpart->position = 0;
            instrumentInfo.pInstrument->volatilityAdjustment = 0;
            switch(instrumentInfo.pInstrument->tradingStatus)
            {
                case InstrumentTradingStatus::OpenAuction:
                case InstrumentTradingStatus::CloseAuction:
                case InstrumentTradingStatus::Auction:
                    isDeleteAll = false;
                    break;
                default:
                    if (instrumentInfo.bidOrder)
                    {
                        LOG4CPLUS_INFO(_logger, "bidOrder:" << *instrumentInfo.bidOrder);
                        switch(instrumentInfo.bidOrder->orderStatus)
                        {
                            case INSERT_FAILED:
                            case FILLED:
                            case DELETED:
                            case READY:
                                break;
                            case PENDING_DELETE:
                            case PENDING_ADD:
                            case EXCHANGE_ORDER:
                            case DELETE_FAILED:
                                deleteOrderByInstrument(instrumentInfo, LongShort::LONG_);
                                isDeleteAll = false;
                                break;
                            default:
                                break;  
                        }
                    }
                    if (instrumentInfo.askOrder)
                    {
                        LOG4CPLUS_INFO(_logger, "askOrder:" << *instrumentInfo.askOrder);
                        switch(instrumentInfo.askOrder->orderStatus)
                        {
                            case INSERT_FAILED:
                            case FILLED:
                            case DELETED:
                            case READY:
                                break;
                            case PENDING_DELETE:
                            case PENDING_ADD:
                            case EXCHANGE_ORDER:
                            case DELETE_FAILED:
                                deleteOrderByInstrument(instrumentInfo, LongShort::SHORT_);
                                isDeleteAll = false;
                                break;
                            default:
                                break;  
                        }
                    }
                    if (instrumentInfo.quote)
                    {
                        LOG4CPLUS_INFO(_logger, "quote:" << *instrumentInfo.quote);
                        if (!instrumentInfo.quote->terminated())
                        {
                            deleteQuoteByInstrument(instrumentInfo);
                            isDeleteAll = false;
                        }
                    }
                    break;
            }
            DatabaseManager::getInstance().updateInstrument(*instrumentInfo.pInstrument);
        }

        if (!isDeleteAll)
        {
            LOG4CPLUS_WARN(_logger, "有单子未删除");
            if (1 != i)
            {
                std::this_thread::sleep_for(10ms);
            }
        }
        else
        {
            break;
        }
    }

    if (isDeleteAll)
    {
        for (auto &pair : _positions)
        {
            auto& positionInfo = pair.second;
            positionInfo.position = 0;
            positionInfo.pInstrument->volatilityAdjustment = 0;
            if (positionInfo.pConterpart) positionInfo.pConterpart->position = 0;
            DatabaseManager::getInstance().updateInstrument(*positionInfo.pInstrument);
        }

        _pBaseInstrument = nullptr;
        _pHedgeFutures = nullptr;
        _pConsolidator.reset();
        _orderBook = OrderBook{};
        _orderBookUpdated = false;
        _cashDelta = 0;
        _vega = 0;
        _hasNewTrade = false;
        _printOrderBook = false;
        _positions.clear();
        _hedgeOrders.clear();
        _timeToMaturity = 1;

        OrderService::getInstance().unregisterTradeListener(this);
        LOG4CPLUS_INFO(_logger, "Finish cleaning up strategy...");
    }
    LOG4CPLUS_WARN(_logger, "bayes cleanUpStrategyJobs return:" << isDeleteAll);
    return isDeleteAll;
}

void BayesianMMStrategy::processFeed() {
    Feed feed;
    while (_feedQueue.try_dequeue(feed))
    {
        if (holds_alternative<Order>(feed))
        {
            Order& order = get<Order>(feed);
            processOrderFeed(order);
        }
        else if (holds_alternative<Quote>(feed))
        {
            Quote& quote = get<Quote>(feed);
            processQuoteFeed(quote);
        }
    }
}

void BayesianMMStrategy::processQuoteFeed(Quote const& quote_)
{
    {
        if(quote_.strategyId == _strategyId)
        {
            if (auto it = _positions.find(quote_.instrumentId); it != _positions.end())
            {
                LOG4CPLUS_INFO(_logger, "QuoteFeed[" << quote_ << "]");
                auto& position = it->second;
                if (position.quote && position.quote->strategyLocalId == quote_.strategyLocalId)
                {
                    position.quote = quote_;
                }
            }
        }
    }
}

void BayesianMMStrategy::processOrderFeed(Order const& order_)
{
    {
        if(order_.strategyId == _strategyId)
        {
            if (auto it = _positions.find(order_.instrumentId); it != _positions.end())
            {
                LOG4CPLUS_INFO(_logger, "OrderFeed[" << order_ << "]");
                auto& position = it->second;
                switch(order_.orderMotive)
                {
                    case OrderMotive::MarketMaking:
                        switch(order_.longShort)
                        {
                            case LONG_:
                                if (position.bidOrder && position.bidOrder->strategyLocalId == order_.strategyLocalId)
                                {
                                    switch(order_.orderStatus)
                                    {
                                        case FILLED:
                                        case DELETED:
                                        case INSERT_FAILED:
                                            position.bidOrder.reset();
                                            break;
                                        default:
                                            position.bidOrder = order_;
                                            break;
                                    }
                                }
                                break;
                            case SHORT_:
                                if (position.askOrder && position.askOrder->strategyLocalId == order_.strategyLocalId)
                                {
                                    switch(order_.orderStatus)
                                    {
                                        case FILLED:
                                        case DELETED:
                                        case INSERT_FAILED:
                                            position.askOrder.reset();
                                            break;
                                        default:
                                            position.askOrder = order_;
                                            break;
                                    }
                                }
                                break;
                            default:
                                assert(false);
                                break;
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }
}

void BayesianMMStrategy::doStrategyJobs()
{
    // bool md_found = false;
    while (auto marketData = _marketDataReader.read())
    {
        // md_found = processMessage(marketData);
        processMessage(marketData);
        // if (md_found) break;
    }
    VolatilityMessage volatility;
    while (_volatilityQueue.try_dequeue(volatility))
    {
        processMessage(volatility);
    }

    Feed feed;
    while (_feedQueue.try_dequeue(feed))
    {
        std::visit([this](auto&& arg){processMessage(arg);}, feed);
    }


    OrderBook orderBook;
    if (_orderBookQueue.try_dequeue(orderBook))
    {
        processMessage(orderBook);
    }
    QuoteRequest rfq;
    if (_quoteRequestQueue.try_dequeue(rfq))
    {
        processMessage(rfq);
    }
}

void BayesianMMStrategy::processMessage(MarketData const* marketData_)
{
    // 迁移权益的写法
    if (!marketData_ || !marketData_->pInstrument) return;
    MarketData const& marketData = *marketData_->pInstrument->pMarketData;

    _eventTriggerTime = steady_clock::now();

    // bool md_found = false;
    if (_pBaseInstrument && marketData.pInstrument == _pBaseInstrument->pBaseInstrument)
    {
        // md_found = true;
        onBaseMarketData(marketData);
    }
    if (_pHedgeFutures && marketData.pInstrument == _pHedgeFutures)
    {
        onHedgeFuturesMarketData(marketData);
        // return true;
    }
    else
    {
        onOptionsMarketData(marketData);
        // return (md_found || onOptionsMarketData(marketData));
    }
}

void BayesianMMStrategy::processMessage(Trade const& trade_)
{
    _eventTriggerTime = steady_clock::now();
    if (auto it = _positions.find(trade_.instrumentId); it != _positions.end())
    {
        LOG4CPLUS_INFO(_logger, "New Trade:" << trade_);
        auto& position = it->second;
        _hasNewTrade = true;
        switch(trade_.orderMotive)
        {
            case OrderMotive::MarketMaking:
            case OrderMotive::Hit:
            case OrderMotive::Manual:
            case OrderMotive::Dime:
                switch(trade_.longShort)
                {
                    case LONG_:
                        position.position += trade_.volumeTraded;
                        break;
                    case SHORT_:
                        position.position -= trade_.volumeTraded;
                        break;
                    default:
                        assert(false);
                }
                LOG4CPLUS_DEBUG(_logger, "PositionInfo[" << position.toString() << "]");
                break;
            case OrderMotive::Hedge:
                switch(trade_.longShort)
                {
                    case LONG_:
                        position.position += trade_.volumeTraded;
                        if (trade_.strategyId == _strategyId){
                            position.longHedgingPosition -= trade_.volumeTraded;
                        }
                        break;
                    case SHORT_:
                        position.position -= trade_.volumeTraded;
                        if (trade_.strategyId == _strategyId){
                            position.shortHedgingPosition -= trade_.volumeTraded;
                        }
                        break;
                    default:
                        assert(false);
                }
                LOG4CPLUS_DEBUG(_logger, "PositionInfo[" << position.toString() << "]");
            default:
                LOG4CPLUS_ERROR(_logger, "Unknown OrderMotive:" << trade_ );
                break;
        }
        fadePosition(position);
    }
}

void BayesianMMStrategy::processMessage(Order const& order_)
{
    _eventTriggerTime = steady_clock::now();
    if(order_.strategyId == _strategyId)
    {
        if (auto it = _positions.find(order_.instrumentId); it != _positions.end())
        {
            LOG4CPLUS_INFO(_logger, "OrderFeed[" << order_ << "]");
            auto& position = it->second;
            switch(order_.orderMotive)
            {
                case OrderMotive::MarketMaking:
                    switch(order_.longShort)
                    {
                        case LONG_:
                            if (position.bidOrder && position.bidOrder->strategyLocalId == order_.strategyLocalId)
                            {
                                // switch(order_.orderStatus)
                                // {
                                //     case INSERT_FAILED:
                                //         ++position.pInstrument->retries[_level];
                                //         break;
                                //     default:
                                //         break;
                                // }
                                switch(order_.orderStatus)
                                {
                                    case FILLED:
                                        position.bidOrder.reset();
                                        break;
                                    case DELETED:
                                    case INSERT_FAILED:
                                        position.bidOrder.reset();
                                        //TODO
                                        //quoteBidSideByOrder(position);
                                        break;
                                    default:
                                        position.bidOrder = order_;
                                        break;
                                }
                                return;
                            }
                            break;
                        case SHORT_:
                            if (position.askOrder && position.askOrder->strategyLocalId == order_.strategyLocalId)
                            {
                                // switch(order_.orderStatus)
                                // {
                                //     case INSERT_FAILED:
                                //         ++position.pInstrument->retries[_level];
                                //         break;
                                //     default:
                                //         break;
                                // }
                                switch(order_.orderStatus)
                                {
                                    case FILLED:
                                    case DELETED:
                                    case INSERT_FAILED:
                                        position.askOrder.reset();
                                        break;
                                    default:
                                        position.askOrder = order_;
                                        break;
                                }
                                return;
                            }
                            break;
                        default:
                            assert(false);
                            break;
                    }
                    break;
                case OrderMotive::Hedge:
                    if (auto it = _hedgeOrders.find(order_.strategyLocalId); it != _hedgeOrders.end())
                    {
                        auto& localOrder = it->second;
                        switch(order_.orderStatus)
                        {
                            case PENDING_ADD:
                            case EXCHANGE_ORDER:
                                localOrder = order_;
                                break;
                            case INSERT_FAILED:
                                LOG4CPLUS_INFO(_logger, "Order failed:" << order_);
                                switch(order_.longShort)
                                {
                                    case LONG_:
                                        position.longHedgingPosition -= order_.volumeOriginalTotal;
                                        break;
                                    case SHORT_:
                                        position.shortHedgingPosition -= order_.volumeOriginalTotal;
                                        break;
                                    default:
                                        assert(false);
                                }
                                _hedgeOrders.erase(it);
                                LOG4CPLUS_INFO(_logger, "PositionInfo[" << position.toString() << "]");
                                break;
                            case DELETED:
                                LOG4CPLUS_INFO(_logger, "Order deleted:" << order_);
                                _orderBookUpdated = false;
                                switch(order_.longShort)
                                {
                                    case LONG_:
                                        position.longHedgingPosition -= (order_.volumeOriginalTotal - order_.volumeTraded);
                                        break;
                                    case SHORT_:
                                        position.shortHedgingPosition -= (order_.volumeOriginalTotal - order_.volumeTraded);
                                        break;
                                    default:
                                        assert(false);
                                }
                                _hedgeOrders.erase(it);
                                LOG4CPLUS_INFO(_logger, "PositionInfo[" << position.toString() << "]");
                                break;
                            case FILLED:
                                LOG4CPLUS_INFO(_logger, "Order filled:" << order_);
                                _hedgeOrders.erase(it);
                                LOG4CPLUS_INFO(_logger, "PositionInfo[" << position.toString() << "]");
                                break;
                            default:
                                assert(false);
                                break;
                        }
                    }
                    break;
                case OrderMotive::Hit:
                    switch(order_.longShort)
                    {
                        case LONG_:
                            if (!position.bidHitOrder.terminated() && position.bidHitOrder.strategyLocalId == order_.strategyLocalId)
                            {
                                // switch(order_.orderStatus)
                                // {
                                //     case INSERT_FAILED:
                                //         ++position.pInstrument->retries[_level];
                                //         break;
                                //     default:
                                //         break;
                                // }
                                position.bidHitOrder = order_;
                            }
                            break;
                        case SHORT_:
                            if (!position.askHitOrder.terminated() && position.askHitOrder.strategyLocalId == order_.strategyLocalId)
                            {
                                // switch(order_.orderStatus)
                                // {
                                //     case INSERT_FAILED:
                                //         ++position.pInstrument->retries[_level];
                                //         break;
                                //     default:
                                //         break;
                                // }
                                position.askHitOrder = order_;
                            }
                            break;
                        default:
                            assert(false);
                            break;
                    }
                    break;
                case OrderMotive::Manual:
                case OrderMotive::Dime:
                    break;
            }
        }
    }
}
void BayesianMMStrategy::processMessage(Quote const& quote_)
{
    _eventTriggerTime = steady_clock::now();
    if(quote_.strategyId == _strategyId)
    {
        if (auto it = _positions.find(quote_.instrumentId); it != _positions.end())
        {
            LOG4CPLUS_DEBUG(_logger, "QuoteFeed[" << quote_ << "]");
            auto& position = it->second;
            if (position.quote && position.quote->strategyLocalId == quote_.strategyLocalId)
            {
                position.quote = quote_;
                //if (quote_.terminated())
                //{
                //    position.quotePendingDelete = false;
                //}
                switch(position.rfqStatus)
                {
                    case RfqStatus::WaitingForRequest:
                        if (quote_.terminated())
                        {
                            if (quote_.bidSide.orderStatus == INSERT_FAILED || quote_.askSide.orderStatus == INSERT_FAILED)
                            {
                                if (position.prevQuote && !position.prevQuote->terminated())
                                {
                                    position.quote = *position.prevQuote;
                                }
                                // position.pInstrument->retries[_level]++;
                            }
                            quoteBothSideByQuote(position);
                        }
                        break;
                    case RfqStatus::QuotePendingAdd:
                        if (quote_.onMarket())
                        {
                            deleteQuoteByInstrument(position);
                        }
                        else if (quote_.terminated())
                        {
                            respondBothSideByQuote(position);
                        }
                        break;
                    case RfqStatus::QuotePendingDelete:
                        if (quote_.bidSide.orderStatus == DELETE_FAILED || quote_.askSide.orderStatus == DELETE_FAILED)
                        {
                            deleteQuoteByInstrument(position);
                        }
                        else if (quote_.terminated())
                        {
                            respondBothSideByQuote(position);
                        }
                        break;
                    case RfqStatus::ResponsePendingAdd:
                        if (quote_.bidSide.volumeTraded > 0 || quote_.askSide.volumeTraded > 0)
                        {
                            deleteQuoteByInstrument(position);
                            position.rfqStatus = RfqStatus::ResponsePendingDelete;
                        }
                        else
                        {
                            if (quote_.onMarket())
                            {
                                position.rfqStatus = RfqStatus::ResponseOnMarket;
                                position.responseExpiryTime = _eventTriggerTime + position.responseRemainingSec;
                            }
                            else if (quote_.terminated())
                            {
                                respondBothSideByQuote(position);
                            }
                        }
                        break;
                    case RfqStatus::ResponseOnMarket:
                        if (quote_.bidSide.volumeTraded > 0 || quote_.askSide.volumeTraded > 0)
                        {
                            deleteQuoteByInstrument(position);
                            position.rfqStatus = RfqStatus::ResponsePendingDelete;
			                QuoteRequest quoteRequest;
                            quoteRequest.receiveTime = position.quoteRequestGetTime;
                            STRCPY(quoteRequest.quoteRequestId,position.quoteRequestId);
                            STRCPY(quoteRequest.internalQuoteRequestId, position.internalQuoteRequestId);
                            STRCPY(quoteRequest.instrumentId,position.pInstrument->instrumentId);
                            quoteRequest.responsedFlag = true;
			                OrderService::getInstance().pushMessage(quoteRequest);
                            LOG4CPLUS_INFO(_logger, "Rfq: Responding Trade Success: " << quoteRequest.instrumentId << ", requestId=" << quoteRequest.quoteRequestId);
                        }
                        break;
                    case RfqStatus::ResponsePendingDelete:
                        if (quote_.terminated())
                        {
                            position.rfqStatus = RfqStatus::WaitingForRequest;
                        }
                        else if(quote_.bidSide.orderStatus == DELETE_FAILED || quote_.askSide.orderStatus == DELETE_FAILED)
                        {
                            deleteQuoteByInstrument(position);
                        }
                        break;
                    default:
                        break;
                }
                LOG4CPLUS_DEBUG(_logger, "PositionInfo[" << position.toString() << "]");
            }
            else if (position.prevQuote && position.prevQuote->strategyLocalId == quote_.strategyLocalId)
            {
                position.prevQuote = quote_;
            }
        }
    }
}
/* TODO
void BayesianMMStrategy::processMessage(CreateQuoteResponse const& rsp_)
{
    _eventTriggerTime = steady_clock::now();
    Quote const& quote = rsp_.quote;
    LOG4CPLUS_INFO(_logger, "CreateQuoteResponse[" << quote << "]");
    if (auto it = _positions.find(quote.instrumentId); it != _positions.end())
    {
        auto& position = it->second;
        if (position.quote && position.quote->strategyLocalId == quote.strategyLocalId)
        {
            position.quote = quote;
            switch(rsp_.rejectReason)
            {
                case RejectReason::Error:
                case RejectReason::BidError:
                case RejectReason::AskError:
                case RejectReason::TransientError:
                    ++position.pInstrument->retries[_level];
                    switch(position.rfqStatus)
                    {
                        case RfqStatus::WaitingForRequest:
                            quoteBothSideByQuote(position);
                            break;
                        case RfqStatus::QuotePendingAdd:
                        case RfqStatus::ResponsePendingAdd:
                            respondBothSideByQuote(position);
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
        }
    }
}
void BayesianMMStrategy::processMessage(CancelQuoteResponse const& rsp_)
{
    _eventTriggerTime = steady_clock::now();
    LOG4CPLUS_INFO(_logger, "CancelQuoteResponse[" << "instrument=" << rsp_.instrumentId << "|internalId=" << rsp_.internalId << "|rejectReason=" << static_cast<int>(rsp_.rejectReason) << "|errMsg=" << rsp_.errorMessage << "]");
    switch(rsp_.rejectReason)
    {
        case RejectReason::Error:
        case RejectReason::BidError:
        case RejectReason::AskError:
        case RejectReason::TransientError:
            if (auto it = _positions.find(rsp_.instrumentId); it != _positions.end())
            {
                auto& position = it->second;
                if (position.quote && position.quote->strategyLocalId == rsp_.strategyLocalId)
                {
                    position.quotePendingDelete = false;
                    switch(position.rfqStatus)
                    {
                        case RfqStatus::WaitingForRequest:
                        case RfqStatus::QuotePendingDelete:
                        case RfqStatus::ResponsePendingDelete:
                            deleteQuoteByInstrument(position);
                            break;
                        default:
                            break;
                    }
                }
            }
            break;
        default:
            break;
    }
}
*/
void BayesianMMStrategy::processMessage(OrderBook const& orderBook_)
{
    //LOG4CPLUS_INFO(_logger, "OrderBook:" << orderBook_.auctionPrice);
    _eventTriggerTime = steady_clock::now();
    _orderBook = orderBook_;
    _orderBookUpdated = true;
    _printOrderBook = true;
}
void BayesianMMStrategy::processMessage(QuoteRequest const& rfq_)
{
    _eventTriggerTime = steady_clock::now();
    LOG4CPLUS_INFO(_logger, "Rfq:" << rfq_);
    if (auto it = _positions.find(rfq_.instrumentId); it != _positions.end())
    {
        auto& info = it->second;
        switch(info.pInstrument->instrumentType)
        {
            case InstrumentType::Options:
                if (strcmp(info.quoteRequestId, rfq_.quoteRequestId) != 0)
                {
                    switch(info.rfqStatus)
                    {
                        case RfqStatus::WaitingForRequest:
                            info.quoteRequestGetTime = rfq_.receiveTime;
                            strcpy(info.quoteRequestId, rfq_.quoteRequestId);
                            strcpy(info.internalQuoteRequestId, rfq_.internalQuoteRequestId);
                            if (steady_clock::now() - info.quoteRequestGetTime > 20s)
                            {
                                info.rfqStatus = RfqStatus::WaitingForRequest;
                                LOG4CPLUS_INFO(_logger,"Quote Request Time Out!");
                                break;
                            }
                            if (info.quote && info.quote->bidSide.orderStatus == PENDING_ADD)
                            {
                                info.rfqStatus = RfqStatus::QuotePendingAdd;
                                LOG4CPLUS_INFO(_logger, "Leftover Quote, PendingAdd: " << info.pInstrument->instrumentId << ", requestId=" << info.quoteRequestId);
                            }
                            else if (info.quote && info.quote->onMarket())
                            {
                                info.rfqStatus = RfqStatus::QuotePendingDelete;
                                deleteQuoteByInstrument(info);
                                LOG4CPLUS_INFO(_logger, "Leftover Quote, Deleting: " << info.pInstrument->instrumentId << ", requestId=" << info.quoteRequestId);
                            }
                            else
                            {
                                info.responseBeginTime = info.quoteRequestGetTime + chrono::seconds(_responseDelaySec);
                                info.rfqStatus = RfqStatus::WaitingForQuote;
                                LOG4CPLUS_INFO(_logger, "Rfq: WaitingForQuote: " << info.pInstrument->instrumentId << ", requestId=" << info.quoteRequestId);
                                //respondBothSideByQuote(info);
                            }
                            break;
                        default:
                            break;
                    }
                }
                break;
            default:
                break;
        }
    }
}

void BayesianMMStrategy::processMessage(VolatilityMessage const& message_)
{
    _eventTriggerTime = steady_clock::now();
    LOG4CPLUS_INFO(_logger, message_);
    assert(message_.nPoints == _nStrikes);
    for (int i = 0; i < _nStrikes; ++i)
    {
        _volArray[i] = message_.points[i].quoteVolatility;
    }
    _T = message_.timeToExpiry;
    recalcRisk();
    activeFutureHedge(_cashDelta);
    recalcRisk();
    fadeAllPosition();
    updateTheoreticalPrice();
    if(_baseMarketDataCount >= _baseOffsetCycle)
    {
        if (useQuoteService())
        {
            quoteByQuote();
        }
        else
        {
            quoteByOrder();
        }
    }
}

void BayesianMMStrategy::onBaseMarketData(MarketData const& marketData_)
{
    LOG4CPLUS_INFO(_logger, "BaseMarketData[" << ::toString(marketData_) << "]");
    if (marketData_.bids[0]->volume != 0 && marketData_.asks[0]->volume != 0)
    {
        updateDynamicBase(marketData_);
        _baseMarketDataCount += 1;
        PriceType midPrice = _dynamicBaseMid / _baseMultiplier;
        

        if (isfinite(_pBaseInstrument->offsetAdjustment))
        {
            _baseBid = marketData_.bids[0]->price / _baseMultiplier + _pBaseInstrument->offsetAdjustment + _pBaseInstrument->baseOffset;
            _baseAsk = marketData_.asks[0]->price / _baseMultiplier + _pBaseInstrument->offsetAdjustment + _pBaseInstrument->baseOffset;
            auto newBaseMid = _dynamicBaseMid + _pBaseInstrument->offsetAdjustment + _pBaseInstrument->baseOffset;
            PriceType absChange = abs(_baseMid - newBaseMid) / _baseMultiplier;
            //PriceType baseSpread = (_baseAsk - _baseBid) / _baseMultiplier;
            if ((isfinite(_baseMid) && isfinite(_baseMidOld) && absChange > _bigChangeThreshold))
            {
                LOG4CPLUS_INFO(_logger, "Big change! newBaseMid:" << newBaseMid << ", oldBaseMid:" << _baseMid);
                _panicBaseSpread = absChange;
            }
            else
            {
                _panicBaseSpread = 0;
                _baseMidOld = _baseMid;
            }
            _baseMid = newBaseMid;
        }
        else
        {
            _baseBid = NAN;
            _baseAsk = NAN;
            _baseMid = NAN;
        }
        LOG4CPLUS_INFO(_logger, "baseIns offsetAdjust:" << _pBaseInstrument->offsetAdjustment << ", baseBid:" << _baseBid << ", baseAsk:" << _baseAsk << ", baseMid:" << _baseMid << ", orderBook.auctionPrice:" << _orderBook.auctionPrice << ",midPrice:" << midPrice << " baseOffset:" << _pBaseInstrument->baseOffset << " _dynamicBaseMid:" << _dynamicBaseMid << " tickCount:" << _baseMarketDataCount );
        
        PriceType baseOffset = _orderBook.auctionPrice - midPrice;
        double alpha = 2. / (1 + _baseOffsetCycle);
        if (isfinite(baseOffset) && _orderBook.auctionPrice > 0)
        {
            if (isfinite(_pBaseInstrument->baseOffset))
            {
                _pBaseInstrument->baseOffset = baseOffset * alpha  + _pBaseInstrument->baseOffset * (1 - alpha);
            }
            else
            {
                _pBaseInstrument->baseOffset = baseOffset;
            }
            //_pAppliedOptionInstrument->pExpiry->baseOffset = _pBaseInstrument->baseOffset * _pAppliedOptionInstrument->minTickMultiplier;
        }
    }
    else
    {
        _baseBid = NAN;
        _baseAsk = NAN;
        _baseMid = NAN;
    }
    updateTheoreticalPrice();
    if(_baseMarketDataCount >= _baseOffsetCycle)
    {
        sendHitOrders();
        if (useQuoteService())
        {
            quoteByQuote();
        }
        else
        {
            quoteByOrder();
        }
    }   
    for (auto& p : _positions)
    {
        InstrumentInfo& insInfo = p.second;
        switch(insInfo.rfqStatus)
        {
            case RfqStatus::WaitingForQuote:
                if (_eventTriggerTime > insInfo.responseBeginTime && steady_clock::now() - insInfo.quoteRequestGetTime <= 20s)
                {
                    respondBothSideByQuote(insInfo);   
                }
                else if (steady_clock::now() - insInfo.quoteRequestGetTime > 20s)
                {
                    insInfo.rfqStatus = RfqStatus::WaitingForRequest;
                    LOG4CPLUS_INFO(_logger, "Rfq: Responding  Time Out: " << insInfo.pInstrument->instrumentId << ", requestId=" << insInfo.quoteRequestId);
                }
                break;
            case RfqStatus::ResponseOnMarket:
    
                if (_eventTriggerTime > insInfo.responseExpiryTime)
                {
                    deleteQuoteByInstrument(insInfo);
                    insInfo.rfqStatus = RfqStatus::ResponsePendingDelete;
			        QuoteRequest quoteRequest;
                    quoteRequest.receiveTime = insInfo.quoteRequestGetTime;
                    STRCPY(quoteRequest.quoteRequestId,insInfo.quoteRequestId);
                    STRCPY(quoteRequest.internalQuoteRequestId, insInfo.internalQuoteRequestId);
                    STRCPY(quoteRequest.instrumentId,insInfo.pInstrument->instrumentId);
                    quoteRequest.responsedFlag = true;
			        OrderService::getInstance().pushMessage(quoteRequest);
                    LOG4CPLUS_INFO(_logger, "Rfq: Responding  Time Success: " << quoteRequest.instrumentId << ", requestId=" << quoteRequest.quoteRequestId);
                }
                break;
            default:
                break;
        }
    }
}

void BayesianMMStrategy::onHedgeFuturesMarketData(MarketData const& marketData_)
{
    LOG4CPLUS_INFO(_logger, "HedgeFuturesMarketData[" << ::toString(marketData_) << "]");
    _pHedgeFuturesMarketData = &marketData_;
    activeFutureHedge(_cashDelta);
}

void BayesianMMStrategy::hedgeVega()
{
}

void BayesianMMStrategy::activeFutureHedge(double remainingDelta_)
{
    if(_pAppliedOptionInstrument && _pAppliedOptionInstrument->pExpiry && _pAppliedOptionInstrument->pExpiry->isContinuous == false)
    {
        return;
    }
	int maxOrderVolume = _maxOrderVolume;
	double deltaHedgeLowerBound = _deltaHedgeLowerBound;
    auto pBase = _pBaseInstrument;
    if (pBase && pBase->pBaseInstrument)
    {
        float S = _baseMid - _deltaFadeTicks * pBase->pBaseInstrument->minimumTick;
        if (!_pHedgeFuturesMarketData || !_pHedgeFuturesMarketData->pInstrument) {
            return;
        }

        switch(_pHedgeFuturesMarketData->pInstrument->tradingStatus)
        {
            case InstrumentTradingStatus::Trading:
                break;
            default:
                return;
        }
	    if (_pHedgeFuturesMarketData->pInstrument->retries[_level] >= _pHedgeFuturesMarketData->pInstrument->maxRetries[_level]) return;
        PriceType suggestedBid = NAN;
        PriceType suggestedAsk = NAN;
        suggestedBid = S;
        suggestedAsk = S;
        PriceType S0 = pBase->suggestedMidPrice;
        if (remainingDelta_ > deltaHedgeLowerBound)
        {
            if (!isfinite(suggestedAsk))
            {
                LOG4CPLUS_WARN(_logger, "Invalid suggested ask, active hedge abort");
                return;
            }
            for (auto const& entry : _pHedgeFuturesMarketData->bids)
            {
                if (entry->volume == 0) break;
                PriceType price = entry->price;
                VolumeType volume = entry->volume;
                auto pInstrument = _pHedgeFutures;
                if (S0 * pInstrument->instrumentMultiplier > abs(remainingDelta_))
                {
                    continue;
                }
                if (auto it = _positions.find(pInstrument->instrumentId); it != _positions.end())
                {
                    auto& position = it->second;
                    if (position.position - position.shortHedgingPosition > -_futureHedgeMaxPosition)
                    {
                        PriceType intercept = round2Tick(max(_futureHedgeMargin * _futureHedgeReturnIntercept * _timeToMaturity / pInstrument->instrumentMultiplier, position.minimumTick * _futureHedgeMinInterceptTicks), position.minimumTick, RoundPolicy::RoundUp);
                        PriceType slope = round2Tick(_futureHedgeMargin * _futureHedgeReturnSlope * abs(position.position) / 100 * _timeToMaturity / pInstrument->instrumentMultiplier, position.minimumTick);
                        PriceType basisToOpen = intercept + slope;
                        PriceType basisToClose = slope;
                        LOG4CPLUS_INFO(_logger, "SuggestedAsk=" << suggestedAsk << "|FutureBidPrice=" << price << "|FutureShortOpenBasis:" << basisToOpen << "|FutureShortCloseBasis:" << basisToClose << "|position=" << position.position);
                        if (price - basisToOpen > suggestedAsk
                                || (position.position > 0 && price + basisToClose > suggestedAsk))
                        {
                            auto unitDelta = S0 * pInstrument->instrumentMultiplier;
                            while(volume != 0)
                            {
                                if (position.shortHedgingPosition + pInstrument->askSideTraded[_level] >= pInstrument->askSideTradesAllowed[_level])
                                {
                                    LOG4CPLUS_DEBUG(_logger, "Futures ask allowed exceeded:" << pInstrument->instrumentId);
                                    break;
                                }
                                auto delta = unitDelta * volume;
                                int hedgeVolume = min<int>(floor(min(delta, abs(remainingDelta_)) / unitDelta), maxOrderVolume);
                                if (hedgeVolume <= 0)
                                {
                                    return;
                                }
                                if (!isnan(price) && price > EPSILON_FOR_EQUALITY_COMPARISON)
                                {
                                    auto strategyLocalId = orderIdGenerator++;
                                    Order &order = _hedgeOrders[strategyLocalId];
                                    strcpy(order.instrumentId, pInstrument->instrumentId);
                                    order.pInstrument = pInstrument;
                                    order.price = price; 
                                    order.volumeOriginalTotal = hedgeVolume;
                                    order.longShort = SHORT_;
                                    order.orderType = LIMIT;
                                    order.matchCondition = FAK;
                                    switch(pInstrument->exchangeId)
                                    {
                                        case CFFEX:
                                            if (pInstrument->totalClosableLongPosition >= hedgeVolume)
                                            {
                                                if (pInstrument->todayLongPosition > 0)
                                                {
                                                    order.openClose = OPEN;
                                                }
                                                else
                                                {
                                                    order.openClose = CLOSE;
                                                }
                                            }
                                            else
                                            {
                                                order.openClose = OPEN;
                                            }
                                            break;
                                        default:
                                            order.openClose = AUTO_OPEN_CLOSE;
                                            break;
                                    }
                                    order.exchangeId = pInstrument->exchangeId;
                                    order.strategyLocalId = strategyLocalId;
                                    order.basis = pBase->baseOffset + pBase->offsetAdjustment;
                                    order.basePrice = S0 - order.basis;
                                    order.volatility = 0;
                                    order.theoreticalPrice = S0;
                                    order.delta = position.delta;
                                    order.vega = position.vega;
                                    // order.orderMotive = OrderMotive::Hit;
                                    position.shortHedgingPosition += hedgeVolume;
                                    remainingDelta_ -= (unitDelta * hedgeVolume) * abs(position.delta);
                                    order.orderMotive = OrderMotive::Hedge;
                                    strcpy(order.comments, "Hedge");
                                    sendOrder(order);
                                    LOG4CPLUS_INFO(_logger, "FuturePositionInfo[" << position.toString() << "]");
                                    LOG4CPLUS_INFO(_logger, "ActiveFutureOrder[Future:" << order << "]");
                                    volume -= hedgeVolume;
                                }

                            }
                        }
                    }
                    else
                    {
                        LOG4CPLUS_DEBUG(_logger, "Future position reached limit[" << "position=" << position.position << ", shortHedgingPosition=" << position.shortHedgingPosition << ", futureHedgemaxPosition=" << _futureHedgeMaxPosition << "]");
                    }
                }
            }
        }
        else if (remainingDelta_ < -deltaHedgeLowerBound)
        {
            if (!isfinite(suggestedBid))
            {
                LOG4CPLUS_WARN(_logger, "Invalid suggested bid, active hedge abort");
                return;
            }
            for (auto const& entry : _pHedgeFuturesMarketData->asks)
            {
                if (entry->volume == 0) break;
                PriceType price = entry->price;
                VolumeType volume = entry->volume;
                auto pInstrument = _pHedgeFutures;
                if (S0 * pInstrument->instrumentMultiplier > abs(remainingDelta_))
                {
                    continue;
                }
                if (auto it = _positions.find(pInstrument->instrumentId); it != _positions.end())
                {
                    auto& position = it->second;
                    if (position.position + position.longHedgingPosition < _futureHedgeMaxPosition)
                    {
                        PriceType intercept = round2Tick(max(_futureHedgeMargin * _futureHedgeReturnIntercept * _timeToMaturity / pInstrument->instrumentMultiplier, pInstrument->minimumTick * _futureHedgeMinInterceptTicks), pInstrument->minimumTick, RoundPolicy::RoundUp);
                        PriceType slope = round2Tick(_futureHedgeMargin * _futureHedgeReturnSlope * abs(position.position) / 100 * _timeToMaturity / pInstrument->instrumentMultiplier, pInstrument->minimumTick);
                        PriceType basisToOpen = intercept + slope;
                        PriceType basisToClose = slope;
                        LOG4CPLUS_INFO(_logger, "SuggestedBid=" << suggestedBid << "|FutureAskPrice=" << price << "|FutureLongOpenBasis:" << basisToOpen << "|FutureLongCloseBasis:" << basisToClose << "|position:" << position.position);
                        if (price + basisToOpen < suggestedBid
                                || (position.position < 0 && price - basisToClose < suggestedBid))
                        {
                            auto unitDelta = S0 * pInstrument->instrumentMultiplier;
                            while(volume != 0)
                            {
                                if (position.longHedgingPosition + pInstrument->bidSideTraded[_level] >= pInstrument->bidSideTradesAllowed[_level])
                                {
                                    LOG4CPLUS_DEBUG(_logger, "Futures bid allowed exceeded:" << pInstrument->instrumentId);
                                    break;
                                }
                                auto delta = unitDelta * volume;
                                int hedgeVolume = min<int>(floor(min(delta, abs(remainingDelta_)) / unitDelta), maxOrderVolume);
                                if (hedgeVolume <= 0)
                                {
                                    return;
                                }
                                if (!isnan(price) && price > EPSILON_FOR_EQUALITY_COMPARISON)
                                {
                                    auto strategyLocalId = orderIdGenerator++;
                                    Order &order = _hedgeOrders[strategyLocalId];
                                    strcpy(order.instrumentId, pInstrument->instrumentId);
                                    order.pInstrument = pInstrument;
                                    order.price = price;
                                    order.volumeOriginalTotal = hedgeVolume;
                                    order.longShort = LONG_;
                                    order.orderType = LIMIT;
                                    order.matchCondition = FAK;
                                    switch(pInstrument->exchangeId)
                                    {
                                        case CFFEX:
                                            if (pInstrument->totalClosableShortPosition > hedgeVolume)
                                            {
                                                if (pInstrument->todayShortPosition > 0)
                                                {
                                                    order.openClose = OPEN;
                                                }
                                                else
                                                {
                                                    order.openClose = CLOSE;
                                                }
                                            }
                                            else
                                            {
                                                order.openClose = OPEN;
                                            }
                                            break;
                                        default:
                                            order.openClose = AUTO_OPEN_CLOSE;
                                            break;
                                    }
                                    order.exchangeId = pInstrument->exchangeId;
                                    order.strategyLocalId = strategyLocalId;
                                    order.basis = pBase->baseOffset + pBase->offsetAdjustment;
                                    order.basePrice = S0 - order.basis;
                                    order.volatility = 0;
                                    order.theoreticalPrice = S0;
                                    order.delta = position.delta;
                                    order.vega = position.vega;
                                    // order.orderMotive = OrderMotive::Hit;
                                    position.longHedgingPosition += hedgeVolume;
                                    order.orderMotive = OrderMotive::Hedge;
                                    strcpy(order.comments, "Hedge");
                                    remainingDelta_ += (unitDelta * hedgeVolume) * abs(position.delta);
                                    sendOrder(order);
                                    LOG4CPLUS_INFO(_logger, "FuturePositionInfo[" << position.toString() << "]");
                                    LOG4CPLUS_INFO(_logger, "ActiveFutureHedgeOrder[Future:" << order << "]");
                                    volume -= hedgeVolume;
                                }

                            }
                        }
                    }
                    else
                    {
                        LOG4CPLUS_DEBUG(_logger, "Future position reached limit[" << "position=" << position.position << ", longHedgingPosition=" << position.longHedgingPosition << ", futureHedgemaxPosition=" << _futureHedgeMaxPosition << "]");
                    }
                }
            }
        }
    }
}

void BayesianMMStrategy::hedgeDelta(double& remainingDelta_)
{
}

void BayesianMMStrategy::onMessage(OrderBook const& orderBook_)
{
    if (!_orderBookQueue.try_enqueue(orderBook_))
    {
        LOG4CPLUS_ERROR(_logger, "OrderBook enqueue failed");
    }
}

BayesianMMStrategy::InstrumentInfo::InstrumentInfo(Instrument* pInstrument_, int level_) : 
    pInstrument(pInstrument_),
    minimumTick(pInstrument_->minimumTick),
    upperLimit(pInstrument->upperLimit.load()),
    lowerLimit(pInstrument->lowerLimit.load())
{
}
void BayesianMMStrategy::quoteByOrder()
{
    for (auto &pair : _positions)
    {
        auto& instrumentInfo = pair.second;
        auto& instrument = *instrumentInfo.pInstrument;
        switch(instrument.tradingStatus)
        {
            case InstrumentTradingStatus::PreStart:
            case InstrumentTradingStatus::MidBreak:
            case InstrumentTradingStatus::Close:
            case InstrumentTradingStatus::Unknown:
                instrument.errorId[_level] = ERROR_ID_MARKET_CLOSED;
                break;
            default:
                if (instrument.retries[_level] < instrument.maxRetries[_level])
                {
                    if (instrument.bidSideTradingFlag[_level] && instrument.askSideTradingFlag[_level]  && instrument.pExpiry->isContinuous)
                    {
                        quoteBothSideByOrder(instrumentInfo);
                    }
                    else
                    {
                        if (instrument.bidSideTradingFlag[_level] && instrument.pExpiry->isContinuous)
                        {
                            quoteBidSideByOrder(instrumentInfo);
                        }
                        else
                        {
                            if (instrumentInfo.bidOrder)
                            {
                                switch(instrumentInfo.bidOrder->orderStatus)
                                {
                                    case PENDING_DELETE:
                                    case INSERT_FAILED:
                                    case FILLED:
                                    case DELETED:
                                    case READY:
                                        break;
                                    case PENDING_ADD:
                                        deleteOrderByInstrument(instrumentInfo, LongShort::LONG_);
                                        break;
                                    case EXCHANGE_ORDER:
                                        if (isAuction(instrument))
                                        {
                                            break;
                                        }
                                        deleteOrderByInstrument(instrumentInfo, LongShort::LONG_);
                                        break;
                                    case DELETE_FAILED:
                                    default:
                                        LOG4CPLUS_FATAL(_logger, "Unknown Status When deleting Bid Order[" << ::toString(instrument) << "]");
                                        break;  
                                }
                            }
                        }
                        if (instrument.askSideTradingFlag[_level] && instrument.pExpiry->isContinuous)
                        {
                            quoteAskSideByOrder(instrumentInfo);
                        }
                        else
                        {
                            if (instrumentInfo.askOrder)
                            {
                                switch(instrumentInfo.askOrder->orderStatus)
                                {
                                    case PENDING_DELETE:
                                    case INSERT_FAILED:
                                    case FILLED:
                                    case DELETED:
                                    case READY:
                                        break;
                                    case PENDING_ADD:
                                        deleteOrderByInstrument(instrumentInfo, LongShort::SHORT_);
                                        break;
                                    case EXCHANGE_ORDER:
                                        if (isAuction(instrument))
                                        {
                                            break;
                                        }
                                        deleteOrderByInstrument(instrumentInfo, LongShort::SHORT_);
                                        break;
                                    case DELETE_FAILED:
                                    default:
                                        LOG4CPLUS_FATAL(_logger, "Unknown Status When deleting Ask Order[" << ::toString(instrument) << "]");
                                        break;  
                                }
                            }
                        }
                    }
                }
                break;
        }
    }
}
void BayesianMMStrategy::quoteByQuote()
{
    for (auto &pair : _positions)
    {
        auto& instrumentInfo = pair.second;
        auto& instrument = *instrumentInfo.pInstrument;
        switch(instrument.instrumentType)
        {
            case InstrumentType::Options:
                switch(instrumentInfo.rfqStatus)
                {
                    case RfqStatus::WaitingForRequest:
                        quoteBothSideByQuote(instrumentInfo);
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }
}

bool BayesianMMStrategy::deltaHedgeNeeded(double totalCashDelta_)
{
    return abs(totalCashDelta_) >= _deltaHedgeLowerBound;
}

void BayesianMMStrategy::quoteBidSideByOrder(InstrumentInfo& instrumentInfo_)
{
    auto& instrument = *instrumentInfo_.pInstrument;
    if (!instrumentInfo_.bidOrder)
    {
        switch(instrument.tradingStatus)
        {
            case InstrumentTradingStatus::Trading:
            case InstrumentTradingStatus::OpenAuction:
            case InstrumentTradingStatus::CloseAuction:
            case InstrumentTradingStatus::Auction:
            case InstrumentTradingStatus::SupervisedStop:
                if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level])
                {
                    instrumentInfo_.suggestedBid = suggestBidPrice(instrumentInfo_);
                    sendOrderByInstrument(instrumentInfo_, LongShort::LONG_);
                }
                break;
            default:
                break;
        }
    }
    else
    {
        switch (instrumentInfo_.bidOrder->orderStatus)
        {
            case PENDING_DELETE:
                break;
            case PENDING_ADD:
            case EXCHANGE_ORDER:
                if (isAuction(instrument))
                {
                    break;
                }
                instrumentInfo_.suggestedBid = suggestBidPrice(instrumentInfo_);
                if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level])
                {
                    if (bidRequoteNeeded(instrumentInfo_)) 
                    {
                        deleteOrderByInstrument(instrumentInfo_, LONG_);
                    }
                }
                else
                {
                    deleteOrderByInstrument(instrumentInfo_, LONG_);
                }
                break;  
            case INSERT_FAILED:
            case FILLED:
            case DELETED:
            case READY:
                switch(instrument.tradingStatus)
                {
                    case InstrumentTradingStatus::Trading:
                    case InstrumentTradingStatus::OpenAuction:
                    case InstrumentTradingStatus::CloseAuction:
                    case InstrumentTradingStatus::Auction:
                    case InstrumentTradingStatus::SupervisedStop:
                        if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level])
                        {
                            instrumentInfo_.suggestedBid = suggestBidPrice(instrumentInfo_);
                            sendOrderByInstrument(instrumentInfo_, LongShort::LONG_);
                        }
                        break;
                    default:
                        break;
                }
                break;
            case DELETE_FAILED:
            default:
                LOG4CPLUS_FATAL(_logger, "Unknown Bid Order Status:[" << ::toString(instrument) << "]");
                break;  
        }
    }
}
void BayesianMMStrategy::quoteAskSideByOrder(InstrumentInfo& instrumentInfo_)
{
    auto& instrument = *instrumentInfo_.pInstrument;
    if (!instrumentInfo_.askOrder)
    {
        switch(instrument.tradingStatus)
        {
            case InstrumentTradingStatus::Trading:
                if (instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                {
                    instrumentInfo_.suggestedAsk = suggestAskPrice(instrumentInfo_);
                    sendOrderByInstrument(instrumentInfo_, LongShort::SHORT_);
                }
                break;
            case InstrumentTradingStatus::OpenAuction:
            case InstrumentTradingStatus::CloseAuction:
            case InstrumentTradingStatus::Auction:
            case InstrumentTradingStatus::SupervisedStop:
                if (instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                {
                    instrumentInfo_.suggestedAsk = suggestAskPrice(instrumentInfo_);
                    sendOrderByInstrument(instrumentInfo_, LongShort::SHORT_);
                }
                break;
            default:
                break;
        }
    }
    else
    {
        switch (instrumentInfo_.askOrder->orderStatus)
        {
            case PENDING_DELETE:
                break;
            case PENDING_ADD:
            case EXCHANGE_ORDER:
                if (isAuction(instrument))
                {
                    break;
                }
                instrumentInfo_.suggestedAsk = suggestAskPrice(instrumentInfo_);
                if (instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                {
                    if (askRequoteNeeded(instrumentInfo_))
                    {
                        deleteOrderByInstrument(instrumentInfo_, SHORT_);
                    }
                }
                else
                {
                    deleteOrderByInstrument(instrumentInfo_, SHORT_);
                }
                break;  
            case INSERT_FAILED:
            case FILLED:
            case DELETED:
            case READY:
                switch(instrument.tradingStatus)
                {
                    case InstrumentTradingStatus::Trading:
                        if (instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                        {
                            instrumentInfo_.suggestedAsk = suggestAskPrice(instrumentInfo_);
                            sendOrderByInstrument(instrumentInfo_, LongShort::SHORT_);
                        }
                        break;
                    case InstrumentTradingStatus::OpenAuction:
                    case InstrumentTradingStatus::CloseAuction:
                    case InstrumentTradingStatus::Auction:
                    case InstrumentTradingStatus::SupervisedStop:
                        if (instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                        {
                            instrumentInfo_.suggestedAsk = suggestAskPrice(instrumentInfo_);
                            sendOrderByInstrument(instrumentInfo_, LongShort::SHORT_);
                        }
                        break;
                    default:
                        break;
                }
                break;  
            case DELETE_FAILED:
            default:
                LOG4CPLUS_FATAL(_logger, "Unknown Ask Order Status:[" << ::toString(instrument) << "]");
                break;  
        }
    }
}
void BayesianMMStrategy::quoteBothSideByOrder(InstrumentInfo& instrumentInfo_)
{
    auto& instrument = *instrumentInfo_.pInstrument;
    auto bidStatus = translateOrderStatus(instrumentInfo_.bidOrder);
    auto askStatus = translateOrderStatus(instrumentInfo_.askOrder);
    switch (quoteStatusSwitch(bidStatus, askStatus))
    {
        case quoteStatusSwitch(SimpleOrderStatus::Terminated, SimpleOrderStatus::Terminated):
            switch(instrument.tradingStatus)
            {
                case InstrumentTradingStatus::Trading:
                    if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level]
                            && instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                    {
                        tie(instrumentInfo_.suggestedBid, instrumentInfo_.suggestedAsk) = suggestPrice(instrumentInfo_, false);
                        sendOrderByInstrument(instrumentInfo_, LongShort::LONG_);
                        sendOrderByInstrument(instrumentInfo_, LongShort::SHORT_);
                    }
                    break;
                case InstrumentTradingStatus::OpenAuction:
                case InstrumentTradingStatus::CloseAuction:
                case InstrumentTradingStatus::Auction:
                case InstrumentTradingStatus::SupervisedStop:
                    if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level]
                            && instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                    {
                        tie(instrumentInfo_.suggestedBid, instrumentInfo_.suggestedAsk) = suggestPrice(instrumentInfo_, false);
                        sendOrderByInstrument(instrumentInfo_, LongShort::LONG_);
                        sendOrderByInstrument(instrumentInfo_, LongShort::SHORT_);
                    }
                    break;
                default:
                    break;
            }
            break;  
        case quoteStatusSwitch(SimpleOrderStatus::ExchangeOrder, SimpleOrderStatus::ExchangeOrder):
            if (isAuction(instrument))
            {
                break;
            }
            if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level]
                    && instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
            {
                tie(instrumentInfo_.suggestedBid, instrumentInfo_.suggestedAsk) = suggestPrice(instrumentInfo_, false);
                if (requoteNeeded(instrumentInfo_))
                {
                    deleteOrderByInstrument(instrumentInfo_, LONG_);
                    deleteOrderByInstrument(instrumentInfo_, SHORT_);
                }
            }
            else
            {
                deleteOrderByInstrument(instrumentInfo_, LONG_);
                deleteOrderByInstrument(instrumentInfo_, SHORT_);
            }
            break;
        case quoteStatusSwitch(SimpleOrderStatus::PendingDelete, SimpleOrderStatus::PendingDelete):
        case quoteStatusSwitch(SimpleOrderStatus::PendingDelete, SimpleOrderStatus::Terminated):
        case quoteStatusSwitch(SimpleOrderStatus::Terminated, SimpleOrderStatus::PendingDelete):
            break;
        case quoteStatusSwitch(SimpleOrderStatus::ExchangeOrder, SimpleOrderStatus::PendingDelete):
        case quoteStatusSwitch(SimpleOrderStatus::ExchangeOrder, SimpleOrderStatus::Terminated):
            deleteOrderByInstrument(instrumentInfo_, LONG_);
            break;
        case quoteStatusSwitch(SimpleOrderStatus::PendingDelete, SimpleOrderStatus::ExchangeOrder):
        case quoteStatusSwitch(SimpleOrderStatus::Terminated, SimpleOrderStatus::ExchangeOrder):
            deleteOrderByInstrument(instrumentInfo_, SHORT_);
            break;
        default:
            //LOG4CPLUS_FATAL(_logger, "Unhandled switch case, bid status: " << static_cast<int>(bidStatus)
            //        << ", ask status:" << static_cast<int>(askStatus));
            //assert(false);
            break;
    }
}

void BayesianMMStrategy::quoteBothSideByQuote(InstrumentInfo& instrumentInfo_)
{
    auto& instrument = *instrumentInfo_.pInstrument;
    if (instrument.retries[_level] <= instrument.maxRetries[_level])
    {
        if (!instrumentInfo_.quote || instrumentInfo_.quote->terminated())
        {
            if (instrument.bidSideTradingFlag[_level] && instrument.askSideTradingFlag[_level] && !instrument.isResponding && instrument.pExpiry->isContinuous )
            {
                switch(instrument.tradingStatus)
                {
                    case InstrumentTradingStatus::Trading:
                    case InstrumentTradingStatus::OpenAuction:
                    case InstrumentTradingStatus::CloseAuction:
                    case InstrumentTradingStatus::Auction:
                    case InstrumentTradingStatus::SupervisedStop:
                        if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level]
                                && instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                        {
                            tie(instrumentInfo_.suggestedBid, instrumentInfo_.suggestedAsk) = suggestPrice(instrumentInfo_, false);
                            sendQuoteByInstrument(instrumentInfo_);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        else if (instrumentInfo_.quote->onMarket())
        {
            if (isAuction(instrument))
            {
                return;
            }
            if (instrument.bidSideTradingFlag[_level] && instrument.askSideTradingFlag[_level] && !instrument.isResponding && instrument.pExpiry->isContinuous)
            {
                if (instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level]
                        && instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level])
                {
                    tie(instrumentInfo_.suggestedBid, instrumentInfo_.suggestedAsk) = suggestPrice(instrumentInfo_, false);
                    if (requoteNeeded(instrumentInfo_))
                    {
                        if (isQuoteDeleteRequired())
                        {
                            deleteQuoteByInstrument(instrumentInfo_);
                        }
                        else if (!isfinite(instrumentInfo_.suggestedBid)
                                || !isfinite(instrumentInfo_.suggestedAsk)
                                || instrumentInfo_.suggestedBid < instrumentInfo_.lowerLimit
                                || instrumentInfo_.suggestedAsk > instrumentInfo_.upperLimit
                                )
                        {
                            LOG4CPLUS_DEBUG(_logger, "Deleting quote due to invalid suggest price,Bid:" << instrumentInfo_.suggestedBid << " Ask:" << instrumentInfo_.suggestedAsk << " " << instrumentInfo_.pInstrument->instrumentId << " lowerLimit:" << instrumentInfo_.lowerLimit << " upperLimit:" << instrumentInfo_.upperLimit );
                            deleteQuoteByInstrument(instrumentInfo_);
                        }
                        else
                        {
                            sendQuoteByInstrument(instrumentInfo_);
                        }
                    }
                }
                else if (instrumentInfo_.quote->bidSide.volumeTotal > 0 && instrumentInfo_.quote->askSide.volumeTotal > 0)
                {
                    deleteQuoteByInstrument(instrumentInfo_);
                }
            }
            else
            {
                deleteQuoteByInstrument(instrumentInfo_);
            }
        }
    }
}
void BayesianMMStrategy::respondBothSideByQuote(InstrumentInfo& instrumentInfo_)
{
    Instrument& instrument = *instrumentInfo_.pInstrument;
    if (
            instrument.bidSideTradingFlag[_level]
            && instrument.askSideTradingFlag[_level]
            && instrument.bidSideTraded[_level] < instrument.bidSideTradesAllowed[_level]
            && instrument.askSideTraded[_level] < instrument.askSideTradesAllowed[_level]
            && instrument.retries[_level] < instrument.maxRetries[_level]
       )
    {
        instrumentInfo_.responseRemainingSec = chrono::seconds(_responseKeepSec);
        tie(instrumentInfo_.suggestedBid, instrumentInfo_.suggestedAsk) = suggestPrice(instrumentInfo_, true);
        sendQuoteByInstrument(instrumentInfo_,true);
        instrumentInfo_.rfqStatus = RfqStatus::ResponsePendingAdd;
        LOG4CPLUS_INFO(_logger, "Rfq: Start Responding: " << instrument.instrumentId << ", requestId=" << instrumentInfo_.quoteRequestId);
    }
    else
    {
        instrumentInfo_.rfqStatus = RfqStatus::WaitingForRequest;
        LOG4CPLUS_INFO(_logger, "Rfq: Responding Fail: " << instrument.instrumentId << ", requestId=" << instrumentInfo_.quoteRequestId);
    }
}
void BayesianMMStrategy::sendOrderByInstrument(InstrumentInfo & instrumentInfo_, LongShort longShort_)
{
    PriceType halfTick = instrumentInfo_.minimumTick * 0.5;
    auto& instrument = *instrumentInfo_.pInstrument;
    Order order;
    order.delta = instrumentInfo_.delta;
    order.vega = instrumentInfo_.vega;
    order.orderMotive = OrderMotive::MarketMaking;
    order.orderType = LIMIT;
    order.matchCondition = GFD;
    strcpy(order.instrumentId, instrument.instrumentId);
    order.pInstrument = instrumentInfo_.pInstrument;
    order.exchangeId = instrument.exchangeId;
    order.basis = _pBaseInstrument->baseOffset + _pBaseInstrument->offsetAdjustment;
    order.basePrice = _pBaseInstrument->suggestedMidPrice - order.basis;
    order.volatility = instrument.quoteVolatility + (_pBaseInstrument->volatilityBidSpread[_level] + _pBaseInstrument->volatilityAskSpread[_level]) / 2.0;
    order.theoreticalPrice = instrumentInfo_.suggestedMid;
    switch(longShort_)
    {
        case LONG_:
            if (!isfinite(instrumentInfo_.suggestedBid)) 
            {
                instrument.errorId[_level] = ERROR_ID_INVALID_PRICE;
                return;
            }
            else if (instrumentInfo_.suggestedBid < instrument.lowerLimit - halfTick)
            {
                instrument.bidSideTradingFlag[_level] = 0;
                return;
            }
            order.price = instrumentInfo_.suggestedBid;
            order.longShort = LONG_;
            if (isAuction(instrument))
            {
                order.volumeOriginalTotal = 1;
                PriceType tick = instrumentInfo_.minimumTick;
                order.price = min<PriceType>(max<PriceType>(order.price, instrument.lowerLimit), instrument.upperLimit - tick);
            }
            else
            {
                order.volumeOriginalTotal = instrument.lots[_level];
            }
            order.strategyLocalId = orderIdGenerator++;
            order.orderMotive = OrderMotive::MarketMaking;
            strcpy(order.comments, "MM");
            order.timeNew = std::chrono::steady_clock::now();
            order.openClose = getOpenClose(instrument, order);
            sendOrder(order);
            instrumentInfo_.bidOrder = order;
            break;
        case SHORT_:
            if (!isfinite(instrumentInfo_.suggestedAsk))
            {
                instrument.errorId[_level] = ERROR_ID_INVALID_PRICE;
                return;
            }
            else if (instrumentInfo_.suggestedAsk > instrumentInfo_.upperLimit + halfTick)
            {
                instrument.askSideTradingFlag[_level] = 0;
                return;
            }
            order.price = instrumentInfo_.suggestedAsk;
            order.longShort = SHORT_;
            if (isAuction(instrument))
            {
                order.volumeOriginalTotal = 1;
                PriceType tick = instrumentInfo_.minimumTick;
                order.price = max<PriceType>(min<PriceType>(order.price, instrument.upperLimit), instrument.lowerLimit  + tick); 
            }
            else
            {
                order.volumeOriginalTotal = instrument.lots[_level];
            }
            order.volumeTotal = order.volumeOriginalTotal;
            order.strategyLocalId = orderIdGenerator++;
            order.orderMotive = OrderMotive::MarketMaking;
            strcpy(order.comments, "MM");
            order.timeNew = std::chrono::steady_clock::now();
            order.openClose = getOpenClose(instrument, order);
            sendOrder(order);
            instrumentInfo_.askOrder = order;
            break;
        default:
            return;
    }
}
inline
void BayesianMMStrategy::deleteOrderByInstrument(InstrumentInfo & instrumentInfo_, LongShort longShort_)
{
    switch(longShort_)
    {
        case LONG_:
            if (instrumentInfo_.bidOrder)
            {
                auto& order = *instrumentInfo_.bidOrder;
                deleteOrder(order);
            }
            break;
        case SHORT_:
            if (instrumentInfo_.askOrder)
            {
                auto& order = *instrumentInfo_.askOrder;
                deleteOrder(order);
            }
            break;
        default:
            assert(false);
            break;
    }
}
void BayesianMMStrategy::sendQuoteByInstrument(InstrumentInfo & instrumentInfo_, bool isResponse)
{
    PriceType halfTick = instrumentInfo_.minimumTick * 0.5;
    auto& instrument = *instrumentInfo_.pInstrument;
    Quote quote;
    quote.bidSide.orderStatus = PENDING_ADD;
    quote.askSide.orderStatus = PENDING_ADD;
    quote.delta = instrumentInfo_.delta;
    quote.vega = instrumentInfo_.vega;
    strcpy(quote.instrumentId, instrument.instrumentId);
    quote.pInstrument = instrumentInfo_.pInstrument;
    quote.exchangeId = instrument.exchangeId;
    //LOG4CPLUS_DEBUG(_logger, "Instrument=" << instrumentInfo_.pInstrument->instrumentId 
    //        << "|suggestedBid=" << instrumentInfo_.suggestedBid
    //        << "|suggestedAsk=" << instrumentInfo_.suggestedAsk);
    if (!isfinite(instrumentInfo_.suggestedAsk))
    {
        instrument.errorId[_level] = ERROR_ID_INVALID_PRICE;
        return;
    }
    quote.askSide.price = instrumentInfo_.suggestedAsk; 
    if (isAuction(instrument))
    {
        quote.askSide.volumeOriginalTotal = 1;
        PriceType tick = instrumentInfo_.minimumTick;
        quote.askSide.price = max<PriceType>(min<PriceType>(quote.askSide.price, instrument.upperLimit), instrument.lowerLimit + tick); 
    }
    else
    {
        if(isResponse)
        {
	        STRCPY(quote.quoteRequestId, instrumentInfo_.quoteRequestId);
            quote.askSide.volumeOriginalTotal = _obligationLots;
        }
        else
        {
            quote.askSide.volumeOriginalTotal = instrument.lots[_level];
        }
    }
    if (instrumentInfo_.suggestedAsk > instrumentInfo_.upperLimit + halfTick)
    {
        quote.askSide.volumeOriginalTotal = 0;
        LOG4CPLUS_ERROR(_logger, "AskVolume=0 " << instrument.instrumentId << " instrumentInfo_.suggestedAsk:" << instrumentInfo_.suggestedAsk << " instrument.upperLimit:" << instrument.upperLimit);
        instrument.errorId[_level] = ERROR_ID_INVALID_PRICE;
        return;
    }
    quote.askSide.volumeTotal = quote.askSide.volumeOriginalTotal;
    if (!isfinite(instrumentInfo_.suggestedBid)) 
    {
        instrument.errorId[_level] = ERROR_ID_INVALID_PRICE;
        return;
    }
    quote.bidSide.price = instrumentInfo_.suggestedBid;
    if (isAuction(instrument))
    {
        quote.bidSide.volumeOriginalTotal = 1;
        PriceType tick = instrumentInfo_.minimumTick;
        quote.bidSide.price = min<PriceType>(max<PriceType>(quote.bidSide.price, instrument.lowerLimit), instrument.upperLimit  - tick);
    }
    else
    {
        if(isResponse)
            quote.bidSide.volumeOriginalTotal = _obligationLots;
        else
            quote.bidSide.volumeOriginalTotal = instrument.lots[_level];
    }
    if (instrumentInfo_.suggestedBid < instrument.lowerLimit - halfTick)
    {
        quote.bidSide.volumeOriginalTotal = 0;
        LOG4CPLUS_ERROR(_logger, "BidVolume=0 " << instrument.instrumentId << " instrumentInfo_.suggestedBid:" << instrumentInfo_.suggestedBid << " instrument.lowerLimit:" << instrument.lowerLimit);
        instrument.errorId[_level] = ERROR_ID_INVALID_PRICE;
        return;
    }
    quote.bidSide.volumeTotal = quote.bidSide.volumeOriginalTotal;

    quote.basis = _pBaseInstrument->baseOffset + _pBaseInstrument->offsetAdjustment;
    quote.basePrice = _pBaseInstrument->suggestedMidPrice - quote.basis;
    quote.volatility = instrument.quoteVolatility + (_pBaseInstrument->volatilityBidSpread[_level] + _pBaseInstrument->volatilityAskSpread[_level]) / 2;
    quote.theoreticalPrice = instrumentInfo_.suggestedMid;

    quote.strategyLocalId = orderIdGenerator++;
    std::tie(quote.bidSide.openClose, quote.askSide.openClose) = getOpenClose(instrument, quote);
    quote.createTriggerTime = _eventTriggerTime;
    quote.createSendTime = steady_clock::now();
    if (instrumentInfo_.quote && !instrumentInfo_.quote->terminated())
    {
        instrumentInfo_.prevQuote = *instrumentInfo_.quote;
        quote.prevInternalId = instrumentInfo_.prevQuote->internalId; 
    }
    quote.sender = this;
    instrumentInfo_.quote = quote;
    strcpy(quote.comments, "MM");
    //sendQuote(quote);
	if (sendQuote(quote))
	{
		if (quote.bidSide.openClose == CLOSE)
		{
			instrument.totalClosableShortPosition -= quote.bidSide.volumeOriginalTotal;
		}
		else if (quote.bidSide.openClose == CLOSE_TODAY)
		{
			instrument.totalClosableShortPosition -= quote.bidSide.volumeOriginalTotal;
			instrument.todayClosableShortPosition -= quote.bidSide.volumeOriginalTotal;
		}
		if (quote.askSide.openClose == CLOSE)
		{
			instrument.totalClosableLongPosition -= quote.askSide.volumeOriginalTotal;
		}
		else if (quote.askSide.openClose == CLOSE_TODAY)
		{
			instrument.totalClosableLongPosition -= quote.askSide.volumeOriginalTotal;
			instrument.todayClosableLongPosition -= quote.askSide.volumeOriginalTotal;
        }
    }
    instrument.errorId[_level] = ERROR_ID_NO_ERROR; 
}
inline
void BayesianMMStrategy::deleteQuoteByInstrument(InstrumentInfo & instrumentInfo_)
{
    switch(instrumentInfo_.pInstrument->tradingStatus)
    {
        case InstrumentTradingStatus::PreStart:
        case InstrumentTradingStatus::MidBreak:
        case InstrumentTradingStatus::Close:
        case InstrumentTradingStatus::OpenAuction:
        case InstrumentTradingStatus::CloseAuction:
        case InstrumentTradingStatus::Auction:
        case InstrumentTradingStatus::Unknown:
            break;
        default:
            if (instrumentInfo_.quote)
            {
                auto& quote = *instrumentInfo_.quote;
                quote.cancelTriggerTime = _eventTriggerTime;
                quote.cancelSendTime = steady_clock::now();
                deleteQuote(quote.internalId);
                //instrumentInfo_.quotePendingDelete = true;
            }
            break;
    }
}
inline
bool BayesianMMStrategy::isAuction(Instrument const& instrument_)
{
    switch(instrument_.tradingStatus)
    {
        case InstrumentTradingStatus::OpenAuction:
        case InstrumentTradingStatus::SupervisedStop:
        case InstrumentTradingStatus::CloseAuction:
        case InstrumentTradingStatus::Auction:
            return true;
        default:
            return false;
    }
}

inline
bool BayesianMMStrategy::requoteNeeded(InstrumentInfo const& instrumentInfo_)
{
    return bidRequoteNeeded(instrumentInfo_) || askRequoteNeeded(instrumentInfo_);
}

void BayesianMMStrategy::excludeUnwantedOrder(MarketData & marketData_)
{
    auto instruments = InstrumentManager::getInstance().getInstruments();
    auto instrumentsSize = InstrumentManager::getInstance().getInstrumentsSize();
	if (!marketData_.pInstrument) return;
    auto index = hashInstrumentId(marketData_.pInstrument->instrumentId);
    if (index < 0 || index >= instrumentsSize)
    {
        return;
    }
    auto& instrument = instruments[index];
    // PriceType tick = instrument.minimumTick;
    for (int level = 0; level < MAX_LEVEL; ++level)
    {
        if (marketData_.bids[0])
        {
            auto bidPrice = instrument.bidOrderPrice[level];
            auto bidVolume = instrument.bidVolume[level];
            if (abs(bidPrice - marketData_.bids[0]->price) < EPSILON_FOR_EQUALITY_COMPARISON)
            {
                marketData_.bids[0]->volume -= bidVolume;
            }
            if (marketData_.bids[0]->volume <= 0)
            {
                if (marketData_.bids[1])
                {
                    marketData_.bids[0] = marketData_.bids[1];
                    marketData_.bids[1] = marketData_.bids[2];
                    marketData_.bids[2] = marketData_.bids[3];
                    marketData_.bids[3] = marketData_.bids[4];
                    marketData_.bids[4].reset();
                }
            }
        }
        if (marketData_.asks[0])
        {
            auto askPrice = instrument.askOrderPrice[level];
            auto askVolume = instrument.askVolume[level];
            if (abs(askPrice - marketData_.asks[0]->price) < EPSILON_FOR_EQUALITY_COMPARISON)
            {
                marketData_.asks[0]->volume -= askVolume;
            }
            if (marketData_.asks[0]->volume <= 0)
            {
                if (marketData_.asks[1])
                {
                    marketData_.asks[0] = marketData_.asks[1];
                    marketData_.asks[1] = marketData_.asks[2];
                    marketData_.asks[2] = marketData_.asks[3];
                    marketData_.asks[3] = marketData_.asks[4];
                    marketData_.asks[4].reset();
                }
            }
        }
    }
}

inline
bool BayesianMMStrategy::bidRequoteNeeded(InstrumentInfo const& instrumentInfo_)
{
    auto suggestedMid = instrumentInfo_.suggestedMid;
    if (isnan(suggestedMid))
    {
        return true;
    }
    auto& instrument = *instrumentInfo_.pInstrument;
    PriceType halfTickSize = instrument.minimumTick / 2;
    PriceType currentPrice = NAN;
    VolumeType currentVolume = 0;
    if (_useQuote)
    {
        if (instrumentInfo_.quote)
        {
            currentPrice = instrumentInfo_.quote->bidSide.price;
            currentVolume = instrumentInfo_.quote->bidSide.volumeTotal;
        }
    }
    else
    {
        if (instrumentInfo_.bidOrder)
        {
            currentPrice = instrumentInfo_.bidOrder->price;
            currentVolume = instrumentInfo_.bidOrder->volumeTotal;
        }
    }
    if (currentVolume < instrument.minVolume)
    {
        return true;
    }
    if (!isfinite(currentPrice))
    {
        return true;
    }
    PriceType minBidSpread = instrumentInfo_.minBidSpread;
    PriceType maxBidSpread = minBidSpread * (1 + _spreadMultiplier);
    PriceType minSpreadBid = suggestedMid - minBidSpread;
    PriceType maxSpreadBid = suggestedMid - maxBidSpread;
    
    // LOG4CPLUS_INFO(_logger, "bidRequoteNeeded Instrument=" << instrumentInfo_.pInstrument->instrumentId << " currentPrice:" << currentPrice << " currentVolume:" << currentVolume << " minBidSpread:" << minBidSpread << " maxBidSpread:" << maxBidSpread << " suggestedMid:" << suggestedMid << " minSpreadBid:" << minSpreadBid << " maxSpreadBid:" << maxSpreadBid << " marketData.bids[0]->price:" << marketData.bids[0]->price);

    PriceSuggestor p = _priceSuggestor;
    if (instrument.isJoinFlag) {
        p = THEORETICAL_PRICE_WITH_JOIN;
    }
    switch(p)
    {
        case BLACK_SCHOLES:
            if (currentPrice < minSpreadBid + halfTickSize
                    && currentPrice > maxSpreadBid - halfTickSize)
            {
                return false;
            }
            else
            {
                return true;
            }
            break;
        case THEORETICAL_PRICE_WITH_JOIN:
        {
            if (!instrument.pMarketData) return false;
            // MarketData m = *instrument.pMarketData;
            // excludeUnwantedOrder(m);

            if (currentPrice < minSpreadBid + halfTickSize
                    && currentPrice > maxSpreadBid - halfTickSize)
            {
                if (instrument.pMarketData->bids[0]->price >= (currentPrice - halfTickSize)) 
                {
                    return false;
                }
                else
                {
                    // LOG4CPLUS_INFO(_logger, "bidRequoteNeeded Instrument=" << instrumentInfo_.pInstrument->instrumentId << " currentPrice:" << currentPrice << " currentVolume:" << currentVolume << " minBidSpread:" << minBidSpread << " maxBidSpread:" << maxBidSpread << " suggestedMid:" << suggestedMid << " minSpreadBid:" << minSpreadBid << " maxSpreadBid:" << maxSpreadBid << " marketData.bids[0]->price:" << instrument.pMarketData->bids[0]->price);
                    return true;
                }
            }
            else
            {
                if (abs(currentPrice - instrument.pMarketData->bids[0]->price) < halfTickSize
                    && instrument.pMarketData->bids[0]->price <= maxSpreadBid - halfTickSize)
                {
                    return false;
                }
                else
                {
                    // LOG4CPLUS_INFO(_logger, "bidRequoteNeeded Instrument=" << instrumentInfo_.pInstrument->instrumentId << " currentPrice:" << currentPrice << " currentVolume:" << currentVolume << " minBidSpread:" << minBidSpread << " maxBidSpread:" << maxBidSpread << " suggestedMid:" << suggestedMid << " minSpreadBid:" << minSpreadBid << " maxSpreadBid:" << maxSpreadBid << " marketData.bids[0]->price:" << instrument.pMarketData->bids[0]->price);
                    return true;
                }
            }
            break;
        }
        // case THEORETICAL_PRICE_WITH_JOIN:
        //     if (!instrument.pMarketData) return false;
        //     if (instrument.pMarketData->bids[0]->volume == 0)
        //     {
        //         if (abs(currentPrice - instrument.lowerLimit) < halfTickSize)
        //         {
        //             return false;
        //         }
        //         else
        //         {
        //             return true;
        //         }
        //     }
        //     else
        //     {
        //         if (instrument.pMarketData->bids[0]->price < minSpreadBid + halfTickSize)
        //         {
        //             if (currentPrice > instrument.pMarketData->bids[0]->price - halfTickSize
        //                     && currentPrice < minSpreadBid + halfTickSize)
        //             {
        //                 return false;
        //             }
        //             else
        //             {
        //                 return true;
        //             }
        //         }
        //         else
        //         {
        //             if (currentPrice < minSpreadBid + halfTickSize
        //                     && currentPrice > maxSpreadBid - halfTickSize)
        //             {
        //                 return false;
        //             }
        //             else
        //             {
        //                 return true;
        //             }
        //         }
        //     }
        //     break;
        case DEPTH_FIVE_DIMMING_JOIN:
            if (!instrument.pMarketData) return false;
            if (instrument.pMarketData->bids[4]->volume == 0)
            {
                if (instrument.lowerLimit < minSpreadBid + halfTickSize)
                {
                    if (currentPrice > instrument.lowerLimit - halfTickSize
                            && currentPrice < minSpreadBid + halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                {
                    if (currentPrice < minSpreadBid + halfTickSize
                            && currentPrice > maxSpreadBid - halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
            }
            else
            {
                if (instrument.pMarketData->bids[4]->price < minSpreadBid + halfTickSize)
                {
                    if (currentPrice > instrument.pMarketData->bids[4]->price - halfTickSize
                            && currentPrice < minSpreadBid + halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                {
                    if (currentPrice < minSpreadBid + halfTickSize
                            && currentPrice > maxSpreadBid - halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
            }
            break;
        default:
            break;
    }
    return true;
}

inline
bool BayesianMMStrategy::askRequoteNeeded(InstrumentInfo const& instrumentInfo_)
{
    auto suggestedMid = instrumentInfo_.suggestedMid;
    if (isnan(suggestedMid))
    {
        return true;
    }
    auto& instrument = *instrumentInfo_.pInstrument;
    PriceType halfTickSize = instrument.minimumTick / 2;
    PriceType currentPrice = NAN;
    VolumeType currentVolume = 0;
    if (_useQuote)
    {
        if (instrumentInfo_.quote)
        {
            currentPrice = instrumentInfo_.quote->askSide.price;
            currentVolume = instrumentInfo_.quote->askSide.volumeTotal;
        }
    }
    else
    {
        if (instrumentInfo_.askOrder)
        {
            currentPrice = instrumentInfo_.askOrder->price;
            currentVolume = instrumentInfo_.askOrder->volumeTotal;
        }
    }
    if (currentVolume < instrument.minVolume)
    {
        return true;
    }
    if (!isfinite(currentPrice))
    {
        return true;
    }
    PriceType minAskSpread = instrumentInfo_.minAskSpread;
    PriceType maxAskSpread = minAskSpread * (1 + _spreadMultiplier);
    PriceType minSpreadAsk = suggestedMid + minAskSpread;
    PriceType maxSpreadAsk = suggestedMid + maxAskSpread;
    //LOG4CPLUS_INFO(_logger, "askRequoteNeeded Instrument=" << instrumentInfo_.pInstrument->instrumentId << " currentPrice:" << currentPrice << " currentVolume:" << currentVolume << " minAskSpread:" << minAskSpread << " maxAskSpread:" << maxAskSpread << " suggestedMid:" << suggestedMid);
    PriceSuggestor p = _priceSuggestor;
    if (instrument.isJoinFlag) {
        p = THEORETICAL_PRICE_WITH_JOIN;
    }
    switch(p)
    {
        case BLACK_SCHOLES:
            if (currentPrice > minSpreadAsk - halfTickSize
                    && currentPrice < maxSpreadAsk + halfTickSize)
            {
                return false;
            }
            else
            {
                return true;
            }
            break;
        case THEORETICAL_PRICE_WITH_JOIN:
        {
            if (!instrument.pMarketData) return false;
            // MarketData m = *instrument.pMarketData;
            // excludeUnwantedOrder(m);
            if (currentPrice > minSpreadAsk - halfTickSize
                    && currentPrice < maxSpreadAsk + halfTickSize)
            {
                if (instrument.pMarketData->asks[0]->price <= (currentPrice + halfTickSize))
                {
                    return false;
                }
                else
                {
                    // LOG4CPLUS_INFO(_logger, "askRequoteNeeded Instrument=" << instrumentInfo_.pInstrument->instrumentId << " currentPrice:" << currentPrice << " currentVolume:" << currentVolume << " minAskSpread:" << minAskSpread << " maxAskSpread:" << maxAskSpread << " suggestedMid:" << suggestedMid << " minSpreadAsk:" << minSpreadAsk << " maxSpreadAsk:" << maxSpreadAsk << " marketData.asks[0]->price:" << instrument.pMarketData->asks[0]->price);
                    return true;
                }
            }
            else
            {
                if (abs(currentPrice - instrument.pMarketData->asks[0]->price) < halfTickSize
                    && instrument.pMarketData->asks[0]->price >= (maxSpreadAsk + halfTickSize))
                {
                    return false;
                }
                else
                {
                    // LOG4CPLUS_INFO(_logger, "askRequoteNeeded Instrument=" << instrumentInfo_.pInstrument->instrumentId << " currentPrice:" << currentPrice << " currentVolume:" << currentVolume << " minAskSpread:" << minAskSpread << " maxAskSpread:" << maxAskSpread << " suggestedMid:" << suggestedMid << " minSpreadAsk:" << minSpreadAsk << " maxSpreadAsk:" << maxSpreadAsk << " marketData.asks[0]->price:" << instrument.pMarketData->asks[0]->price);
                    return true;
                }
                
            }
            break;
        }
        // case THEORETICAL_PRICE_WITH_JOIN:
        //     if (!instrument.pMarketData) return false;
        //     if (instrument.pMarketData->asks[0]->volume == 0)
        //     {
        //         if (abs(currentPrice - instrument.upperLimit) < halfTickSize)
        //         {
        //             return false;
        //         }
        //         else
        //         {
        //             return true;
        //         }
        //     }
        //     else
        //     {
        //         if (instrument.pMarketData->asks[0]->price > minSpreadAsk - halfTickSize)
        //         {
        //             if (currentPrice < instrument.pMarketData->asks[0]->price + halfTickSize
        //                     && currentPrice > minSpreadAsk - halfTickSize)
        //             {
        //                 return false;
        //             }
        //             else
        //             {
        //                 return true;
        //             }
        //         }
        //         else
        //         {
        //             if (currentPrice > minSpreadAsk - halfTickSize
        //                     && currentPrice < maxSpreadAsk + halfTickSize)
        //             {
        //                 return false;
        //             }
        //             else
        //             {
        //                 return true;
        //             }
        //         }
        //     }
        //     break;
        case DEPTH_FIVE_DIMMING_JOIN:
            if (!instrument.pMarketData) return false;
            if (instrument.pMarketData->asks[4]->volume == 0)
            {
                if (instrument.upperLimit > minSpreadAsk - halfTickSize)
                {
                    if (currentPrice < instrument.upperLimit + halfTickSize
                            && currentPrice > minSpreadAsk - halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                {
                    if (currentPrice > minSpreadAsk - halfTickSize
                            && currentPrice < maxSpreadAsk + halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
            }
            else
            {
                if (instrument.pMarketData->asks[4]->price > minSpreadAsk - halfTickSize)
                {
                    if (currentPrice < instrument.pMarketData->asks[4]->price + halfTickSize
                            && currentPrice > minSpreadAsk - halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                {
                    if (currentPrice > minSpreadAsk - halfTickSize
                            && currentPrice < maxSpreadAsk + halfTickSize)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
            }
            break;
        default:
            break;
    }
    return true;
}

inline
std::pair<PriceType, PriceType> BayesianMMStrategy::suggestPrice(InstrumentInfo const& instrumentInfo_, bool isResponding_)
{
    Instrument& instrument = *instrumentInfo_.pInstrument;
    if (!isResponding_)
    {
        auto suggestedBid = suggestBidPrice(instrumentInfo_);
        auto suggestedAsk = suggestAskPrice(instrumentInfo_);
        switch(instrument.tradingStatus)
        {
            case InstrumentTradingStatus::OpenAuction:
            case InstrumentTradingStatus::CloseAuction:
            case InstrumentTradingStatus::Auction:
            case InstrumentTradingStatus::SupervisedStop:
                {
                    if(isnan(instrument.suggestedMidPrice))
                    {
                        return make_pair(NAN,NAN);
                    }
                    auto midPrice = instrument.suggestedMidPrice;//TODO 理论价是真实价钱
                    auto worstBid = getWorstObligationBid(instrument, midPrice);
                    auto worstAsk = getWorstObligationAsk(instrument, midPrice);
                    auto bidPrice = round2Tick(max<PriceType>(worstBid, instrument.lowerLimit), instrumentInfo_.minimumTick, RoundPolicy::RoundUp);
                    auto askPrice = round2Tick(min<PriceType>(worstAsk, instrument.upperLimit), instrumentInfo_.minimumTick, RoundPolicy::RoundDown);
                    return make_pair(bidPrice, askPrice);
                }
                break;
            case InstrumentTradingStatus::Trading:
                return make_pair(suggestedBid, suggestedAsk);
            default:
                break;
        }
    }
    else
    {
        auto suggestedBid = suggestBidPrice(instrumentInfo_);
        auto suggestedAsk = suggestAskPrice(instrumentInfo_);
        if(isnan(instrument.suggestedMidPrice))
        {
            return make_pair(NAN,NAN);
        }
        auto worstBid = getWorstObligationBid(instrument, instrument.suggestedMidPrice);
        auto worstAsk = getWorstObligationAsk(instrument, instrument.suggestedMidPrice);
        auto currentSpread = (worstAsk - worstBid) * _maxSpreadAlpha;
        worstBid = instrument.suggestedMidPrice - 0.5 * currentSpread;
        worstAsk = instrument.suggestedMidPrice + 0.5 * currentSpread;

        PriceType tick = instrumentInfo_.minimumTick;
        auto bidPrice = round2Tick(min<PriceType>(instrument.upperLimit - tick, max<PriceType>(worstBid, instrument.lowerLimit )), tick, RoundPolicy::RoundUp);
        auto askPrice = round2Tick(max<PriceType>(instrument.lowerLimit + tick, min<PriceType>(worstAsk, instrument.upperLimit)), tick, RoundPolicy::RoundDown);
        if( currentSpread > (suggestedAsk - suggestedBid))
        {
            //LOG4CPLUS_INFO(_logger, "worstBid:" << getWorstObligationBid(instrument, instrument.suggestedMidPrice) << " worstAsk:" << getWorstObligationAsk(instrument, instrument.suggestedMidPrice) << " currentworstBid:" << worstBid << " currentworstAsk:" << worstAsk << " suggestedBid:" << suggestedBid << " suggestedAsk:" << suggestedAsk);
            return make_pair(bidPrice, askPrice);
        }
        else
        {   
            //LOG4CPLUS_INFO(_logger, "worstBid:" << getWorstObligationBid(instrument, instrument.suggestedMidPrice) << " worstAsk:" << getWorstObligationAsk(instrument, instrument.suggestedMidPrice) << " currentworstBid:" << worstBid << " currentworstAsk:" << worstAsk << " suggestedBid:" << suggestedBid << " suggestedAsk:" << suggestedAsk);
            return make_pair(suggestedBid, suggestedAsk);
        }
    }
    return make_pair(NAN, NAN);
}

PriceType BayesianMMStrategy::getWorstObligationBid(Instrument const& instrument_, PriceType midPrice_)
{
    switch(instrument_.exchangeId)
    {
        case SSE:
        case SZSE:
            if (instrument_.daysToExpiry < 63)
            {
                if (midPrice_ > 0.0262) return midPrice_ / 1.05;
                else return midPrice_ - 0.0012;
            }
            else
            {
                if (midPrice_ > 0.0138) return midPrice_ / 1.1;
                else return midPrice_ - 0.0012;
            }
            break;
        case CFFEX:
            if (instrument_.daysToExpiry < 28)
            {
                if (midPrice_ < 10.6)
                    return midPrice_ - 0.2;
                else if (midPrice_ < 21.4)
                    return midPrice_ - 0.4;
                else if (midPrice_ < 52.6)
                    return midPrice_ - 1.2;
                else if (midPrice_ < 104)
                    return midPrice_ - 2.4;
                else if (midPrice_ < 257.6)
                    return midPrice_ - 4;
                else if (midPrice_ < 515)
                    return midPrice_ - 7.4;
                else if (midPrice_ < 1030)
                    return midPrice_ - 15;
                else if (midPrice_ < 2060)
                    return midPrice_ - 30;
                else
                    return midPrice_ - 60;
            }
            else
            {
                if (midPrice_ < 11)
                    return midPrice_ - 0.4;
                else if (midPrice_ < 22)
                    return midPrice_ - 1;
                else if (midPrice_ < 54)
                    return midPrice_ - 2;
                else if (midPrice_ < 107.6)
                    return midPrice_ - 4;
                else if (midPrice_ < 262.6)
                    return midPrice_ - 7.4;
                else if (midPrice_ < 525)
                    return midPrice_ - 12.4;
                else if (midPrice_ < 1050)
                    return midPrice_ - 25;
                else if (midPrice_ < 2100)
                    return midPrice_ - 50;
                else
                    return midPrice_ - 100;
            }
            break;
        case CZCE:
        case DCE:
        case SHFE:
        case INE:
            {
                auto [bidSuggestedPrice, askSuggestedPrice] = OptionPricingUtility::getMaxSpreadPrice(instrument_, _level);
                return *bidSuggestedPrice;
            }
            break;
        default:
            return NAN;
    }
}
PriceType BayesianMMStrategy::getWorstObligationAsk(Instrument const& instrument_, PriceType midPrice_)
{
    switch(instrument_.exchangeId)
    {
        case SSE:
        case SZSE:
            if (instrument_.daysToExpiry < 63)
            {
                if (midPrice_ > 0.0262) return midPrice_ * 1.047;
                else return midPrice_ + 0.0012;
            }
            else
            {
                if (midPrice_ > 0.0138) return midPrice_ * 1.09;
                else return midPrice_ + 0.0012;
            }
            break;
        case CFFEX:
            if (instrument_.daysToExpiry < 28)
            {
                if (midPrice_ < 10.6)
                    return midPrice_ + 0.2;
                else if (midPrice_ < 21.4)
                    return midPrice_ + 0.4;
                else if (midPrice_ < 52.6)
                    return midPrice_ + 1.2;
                else if (midPrice_ < 104)
                    return midPrice_ + 2.4;
                else if (midPrice_ < 257.6)
                    return midPrice_ + 4;
                else if (midPrice_ < 515)
                    return midPrice_ + 7.4;
                else if (midPrice_ < 1030)
                    return midPrice_ + 15;
                else if (midPrice_ < 2060)
                    return midPrice_ + 30;
                else
                    return midPrice_ + 60;
            }
            else
            {
                if (midPrice_ < 11)
                    return midPrice_ + 0.4;
                else if (midPrice_ < 22)
                    return midPrice_ + 1;
                else if (midPrice_ < 54)
                    return midPrice_ + 2;
                else if (midPrice_ < 107.6)
                    return midPrice_ + 4;
                else if (midPrice_ < 262.6)
                    return midPrice_ + 7.4;
                else if (midPrice_ < 525)
                    return midPrice_ + 12.4;
                else if (midPrice_ < 1050)
                    return midPrice_ + 25;
                else if (midPrice_ < 2100)
                    return midPrice_ + 50;
                else
                    return midPrice_ + 100;
            }
            break;
        case DCE:
        case CZCE:
        case SHFE:
        case INE:
            {
                auto [bidSuggestedPrice, askSuggestedPrice] = OptionPricingUtility::getMaxSpreadPrice(instrument_, _level);
                return *askSuggestedPrice;
            }
            break;
        default:
            return NAN;
    }
}
OpenClose BayesianMMStrategy::getOpenClose(Instrument const& instrument_, Order const& order_)
{
	if (instrument_.exchangeId != ExchangeId::SHFE && instrument_.exchangeId != ExchangeId::INE)
    {
		switch (order_.longShort)
		{
		case LONG_:
			return (order_.volumeOriginalTotal > instrument_.totalClosableShortPosition) ? OPEN : CLOSE;
		case SHORT_:
            return (order_.volumeOriginalTotal > instrument_.totalClosableLongPosition) ? OPEN : CLOSE;
		default:
			return OPEN;
        }
    }
    else
    {
        switch(order_.longShort)
        {
            case LONG_:
                if (order_.volumeOriginalTotal > instrument_.totalShortPosition)
                {
                    return OPEN;
                }
                else if (order_.volumeOriginalTotal <= instrument_.todayClosableShortPosition)
                {
                    return CLOSE_TODAY;
                }
                else if (order_.volumeOriginalTotal <= instrument_.totalClosableShortPosition - instrument_.todayClosableShortPosition)
                {
                    return CLOSE;
                }
                else
                {
                    return OPEN;
                }
                break;
            case SHORT_:
                if (order_.volumeOriginalTotal > instrument_.totalLongPosition - MAX_OUTSTANDING_POSITION_THRESHOLD)
                {
                    return OPEN;
                }
                else if (order_.volumeOriginalTotal <= instrument_.todayClosableLongPosition)
                {
                    return CLOSE_TODAY;
                }
                else if (order_.volumeOriginalTotal <= instrument_.totalClosableLongPosition - instrument_.todayClosableLongPosition)
                {
                    return CLOSE;
                }
                else
                {
                    return OPEN;
                }
                break;
            default:
                return OPEN;
        }
    }
}
std::pair<OpenClose, OpenClose> BayesianMMStrategy::getOpenClose(Instrument const& instrument_, Quote const& quote_)
{
    std::pair<OpenClose, OpenClose> rtn;
	if (instrument_.exchangeId != ExchangeId::SHFE && instrument_.exchangeId != ExchangeId::INE)
    {
        if (quote_.bidSide.volumeOriginalTotal > instrument_.totalClosableShortPosition)
        {
            rtn.first = OPEN;
        }
        else
        {
            rtn.first = CLOSE;
        }
        if (quote_.askSide.volumeOriginalTotal > instrument_.totalClosableLongPosition)
        {
            rtn.second = OPEN;
        }
        else
        {
            rtn.second = CLOSE;
        }
    }
    else
    {
        if (quote_.bidSide.volumeOriginalTotal > instrument_.totalShortPosition)
        {
            rtn.first = OPEN;
        }
        else if (quote_.bidSide.volumeOriginalTotal <= instrument_.todayClosableShortPosition)
        {
            rtn.first = CLOSE_TODAY;
        }
        else if (quote_.bidSide.volumeOriginalTotal <= instrument_.totalClosableShortPosition - instrument_.todayClosableShortPosition)
        {
            rtn.first = CLOSE;
        }
        else
        {
            rtn.first = OPEN;
        }
        if (quote_.askSide.volumeOriginalTotal > instrument_.totalLongPosition - MAX_OUTSTANDING_POSITION_THRESHOLD)
        {
            rtn.second = OPEN;
        }
        else if (quote_.askSide.volumeOriginalTotal <= instrument_.todayClosableLongPosition)
        {
            rtn.second = CLOSE_TODAY;
        }
        else if (quote_.askSide.volumeOriginalTotal <= instrument_.totalClosableLongPosition - instrument_.todayClosableLongPosition)
        {
            rtn.second = CLOSE;
        }
        else
        {
            rtn.second = OPEN;
        }
    }
    return rtn;
}
void BayesianMMStrategy::updateTheoreticalPrice()
{
    //float r = 0.025;
    float r = OptionPricingUtility::getR(*_pAppliedOptionInstrument);
    float S = _baseMid - _deltaFadeTicks * _pBaseInstrument->pBaseInstrument->minimumTick;
    _pBaseInstrument->suggestedMidPrice = S;
    _pBaseInstrument->deltaFade = _deltaFadeTicks * _pBaseInstrument->pBaseInstrument->minimumTick;
    if (_pHedgeFutures) _pHedgeFutures->suggestedMidPrice = S;
#if INSTRSET >= 9
    const int vectorSize = 16;
    using Vec = Vec16f;
#elif INSTRSET >= 8
    const int vectorSize = 8;
    using Vec = Vec8f;
#elif INSTRSET >= 2
    const int vectorSize = 4;
    using Vec = Vec4f;
#endif
    Vec X, vol;
    for (int i = 0; i < _nStrikes; i += vectorSize)
    {
        X.load(_strikeArray + i);
        vol.load(_volArray + i);
        Vec erT = exp(-r*_T);
        Vec logSX = log(S / X);
        Vec sqrtTSigma = sqrt(_T) * (vol - _vegaFadeTicks * volatilityTickSize);
        Vec d1 = (logSX + sqrtTSigma * sqrtTSigma * 0.5) / sqrtTSigma;
        Vec d2 = d1 - sqrtTSigma;
        Vec nd1 = erf(d1 * M_SQRT1_2) * 0.5 + 0.5;
        Vec nd2 = erf(d2 * M_SQRT1_2) * 0.5 + 0.5;
        Vec calls = erT * (S * nd1 - X * nd2);
        Vec puts = calls + erT * (X - S);
        calls.store(_callPrices + i);
        puts.store(_putPrices + i);
    }
    //LOG4CPLUS_DEBUG(_logger, "Suggest Theo:");
    for (int i = 0; i < _nStrikes; ++i)
    {
        InstrumentInfo* pCall = _callPtrArray[i];
        InstrumentInfo* pPut = _putPtrArray[i];
        if (pCall)
        {
            float suggestedMid = _callPrices[i] + pCall->positionFader;
            if(pCall->suggestedMid != suggestedMid)
                LOG4CPLUS_DEBUG(_logger, pCall->pInstrument->instrumentId << ":" << suggestedMid << " S:" << S << " Vol:" << _volArray[i] << " T:" << _T);
            pCall->suggestedMid = suggestedMid;
            pCall->pInstrument->suggestedMidPrice = suggestedMid;
            pCall->pInstrument->quoteVolatility = _volArray[i] - _vegaFadeTicks * volatilityTickSize;
            LOG4CPLUS_INFO(_logger, pCall->pInstrument->instrumentId << " theoreticalPrice:" << pCall->pInstrument->theoreticalPrice <<  " suggestedMid:" << suggestedMid << " S:" << S << " Vol:" << _volArray[i] << " T:" << _T);
        }
        if (pPut)
        {
            float suggestedMid = _putPrices[i] + pPut->positionFader;
            pPut->suggestedMid = suggestedMid;
            pPut->pInstrument->suggestedMidPrice = suggestedMid;
            pPut->pInstrument->quoteVolatility = _volArray[i] - _vegaFadeTicks * volatilityTickSize;
            LOG4CPLUS_INFO(_logger, pPut->pInstrument->instrumentId << " theoreticalPrice:" << pPut->pInstrument->theoreticalPrice <<  " suggestedMid:" << suggestedMid << " S:" << S << " Vol:" << _volArray[i] << " T:" << _T);
            //LOG4CPLUS_DEBUG(_logger, pPut->pInstrument->instrumentId << ":" << suggestedMid);
        }
    }

}

inline
void BayesianMMStrategy::updateMinSpread()
{
    for (auto& p : _positions)
    {
        auto& insInfo = p.second;
        auto callPutType = insInfo.pInstrument->isCall;
        double tickSize = insInfo.minimumTick;
        if (insInfo.pInstrument)
        {
            Instrument const& instrument = *insInfo.pInstrument;
            float delta = insInfo.delta;
            float vega = insInfo.vega * 100;
            switch(callPutType)
            {
                case CALL:
                    // TODO
                    insInfo.minBidSpread = max<PriceType>(-(delta * (-_deltaSpreadTicksBid * tickSize - _panicBaseSpread + instrument.baseBidSpread[_level])
                            + vega * (-_vegaSpreadTicksBid * volatilityTickSize + instrument.volatilityBidSpread[_level])
                            + instrument.bidOffset), 0.5 * tickSize);
                    insInfo.minAskSpread = max<PriceType>((delta * (_deltaSpreadTicksAsk * tickSize + _panicBaseSpread + instrument.baseAskSpread[_level])
                            + vega * (_vegaSpreadTicksAsk * volatilityTickSize + instrument.volatilityAskSpread[_level])
                            + instrument.askOffset), 0.5 * tickSize);
                    LOG4CPLUS_INFO(_logger, "CALL:" << instrument.instrumentId << " minBidSpread:" << insInfo.minBidSpread << ", minAskSpread=" << insInfo.minAskSpread << ", delta=" << delta << ", vega=" << vega << ", volBidSpread:" << (-_vegaSpreadTicksBid * volatilityTickSize + instrument.volatilityBidSpread[_level]) << ", volAskSpread:" << (_vegaSpreadTicksAsk * volatilityTickSize + instrument.volatilityAskSpread[_level]) << ", deltaBidSpread:" << (-_deltaSpreadTicksBid *  tickSize - _panicBaseSpread + instrument.baseBidSpread[_level]) << ", deltaAskSpread:" << (_deltaSpreadTicksAsk * tickSize + _panicBaseSpread + instrument.baseAskSpread[_level]));
                    break;
                case PUT:
                    // TODO baseBidSpread multiplier和客户端保持一致
                    insInfo.minBidSpread = max<PriceType>(-(delta * (_deltaSpreadTicksAsk * tickSize + _panicBaseSpread - instrument.baseBidSpread[_level])
                            + vega * (-_vegaSpreadTicksBid * volatilityTickSize + instrument.volatilityBidSpread[_level])
                            + instrument.bidOffset), 0.5 * tickSize);
                    insInfo.minAskSpread = max<PriceType>((delta * (-_deltaSpreadTicksBid * tickSize - _panicBaseSpread - instrument.baseAskSpread[_level])
                            + vega * (_vegaSpreadTicksAsk * volatilityTickSize + instrument.volatilityAskSpread[_level])
                            + instrument.askOffset), 0.5 * tickSize);
                    LOG4CPLUS_INFO(_logger, "PUT:" << instrument.instrumentId << " minBidSpread:" << insInfo.minBidSpread << ", minAskSpread=" << insInfo.minAskSpread << ", delta=" << delta << ", vega=" << vega << ", volBidSpread:" << (-_vegaSpreadTicksBid * volatilityTickSize + instrument.volatilityBidSpread[_level]) << ", volAskSpread:" << (_vegaSpreadTicksAsk * volatilityTickSize + instrument.volatilityAskSpread[_level]) << ", deltaBidSpread:" << (_deltaSpreadTicksAsk *  tickSize + _panicBaseSpread - instrument.baseBidSpread[_level]) << ", deltaAskSpread:" << (-_deltaSpreadTicksBid * tickSize - _panicBaseSpread - instrument.baseAskSpread[_level]));
                    break;
                default:
                    break;
            }
        }
    }
}

inline
float BayesianMMStrategy::suggestBidPrice(InstrumentInfo const& instrumentInfo_)
{
    auto suggestedMid = instrumentInfo_.suggestedMid;
    if (isnan(suggestedMid))
    {
        return NAN;
    }
    auto& instrument = *instrumentInfo_.pInstrument;
    PriceType halfTickSize = instrument.minimumTick / 2;

    PriceType minBidSpread = instrumentInfo_.minBidSpread;
    PriceType maxBidSpread = minBidSpread * (1 + _spreadMultiplier);
    PriceType minSpreadBid = suggestedMid - minBidSpread;
    PriceType maxSpreadBid = suggestedMid - maxBidSpread;
    PriceType suggestedPrice = NAN;
    PriceSuggestor p = _priceSuggestor;
    if (instrument.isJoinFlag) {
        p = THEORETICAL_PRICE_WITH_JOIN;
    }
    switch(p)
    {
        case BLACK_SCHOLES:
            suggestedPrice = (minSpreadBid + maxSpreadBid) * 0.5;
            break;
        case THEORETICAL_PRICE_WITH_JOIN:
            if (!instrument.pMarketData) return NAN;
            if (instrument.pMarketData->bids[0]->volume == 0)
            {
                return instrument.lowerLimit;
            }
            else
            {
                if (instrument.pMarketData->bids[0]->price < minSpreadBid + halfTickSize)
                {
                    return instrument.pMarketData->bids[0]->price;
                }
                else
                {
                    suggestedPrice = (minSpreadBid + maxSpreadBid) * 0.5;
                }
            }
            break;
        case DEPTH_FIVE_DIMMING_JOIN:
            if (instrument.pMarketData == nullptr) return NAN;
            if (instrument.pMarketData->bids[4]->volume == 0)
            {
                if (instrument.lowerLimit < minSpreadBid + halfTickSize)
                {
                    suggestedPrice = (instrument.lowerLimit + minSpreadBid) * 0.5;
                }
                else
                {
                    suggestedPrice = (minSpreadBid + maxSpreadBid) * 0.5;
                }
            }
            else
            {
                if (instrument.pMarketData->bids[4]->price < minSpreadBid + halfTickSize)
                {
                    suggestedPrice = (instrument.pMarketData->bids[4]->price + minSpreadBid) * 0.5;
                }
                else
                {
                    suggestedPrice = (minSpreadBid + maxSpreadBid) * 0.5;
                }
            }
            break;
        default:
            break;
    }
    return round2Tick(suggestedPrice, instrument.minimumTick);
}

inline
float BayesianMMStrategy::suggestAskPrice(InstrumentInfo const& instrumentInfo_)
{
    auto suggestedMid = instrumentInfo_.suggestedMid;
    if (isnan(suggestedMid))
    {
        return NAN;
    }
    auto& instrument = *instrumentInfo_.pInstrument;
    PriceType halfTickSize = instrument.minimumTick / 2;

    PriceType minAskSpread = instrumentInfo_.minAskSpread;
    PriceType maxAskSpread = minAskSpread * (1 + _spreadMultiplier);
    PriceType minSpreadAsk = suggestedMid + minAskSpread;
    PriceType maxSpreadAsk = suggestedMid + maxAskSpread;
    PriceType suggestedPrice = NAN;
    PriceSuggestor p = _priceSuggestor;
    if (instrument.isJoinFlag) {
        p = THEORETICAL_PRICE_WITH_JOIN;
    }
    switch(p)
    {
        case BLACK_SCHOLES:
            suggestedPrice = (minSpreadAsk + maxSpreadAsk) * 0.5;
            break;
        case THEORETICAL_PRICE_WITH_JOIN:
            if (!instrument.pMarketData) return NAN;
            if (instrument.pMarketData->asks[0]->volume == 0)
            {
                return instrument.upperLimit;
            }
            else
            {
                if (instrument.pMarketData->asks[0]->price > minSpreadAsk - halfTickSize)
                {
                    return instrument.pMarketData->asks[0]->price;
                }
                else
                {
                    suggestedPrice = (minSpreadAsk + maxSpreadAsk) * 0.5;
                }
            }
            break;
        case DEPTH_FIVE_DIMMING_JOIN:
            if (!instrument.pMarketData) return NAN;
            if (instrument.pMarketData->asks[4]->volume == 0)
            {
                if (instrument.upperLimit > minSpreadAsk - halfTickSize)
                {
                    suggestedPrice = (instrument.upperLimit + minSpreadAsk) * 0.5;
                }
                else
                {
                    suggestedPrice = (minSpreadAsk + maxSpreadAsk) * 0.5;
                }
            }
            else
            {
                if (instrument.pMarketData->asks[4]->price > minSpreadAsk - halfTickSize)
                {
                    suggestedPrice = (instrument.pMarketData->asks[4]->price + minSpreadAsk) * 0.5;
                }
                else
                {
                    suggestedPrice = (minSpreadAsk + maxSpreadAsk) * 0.5;
                }
            }
            break;
        default:
            break;
    }
    return round2Tick(suggestedPrice, instrument.minimumTick);
}
void BayesianMMStrategy::recalcRisk()
{
    _cashDelta = 0;
    _vega = 0;
    for (auto &pair : _positions)
    {
        auto& instrumentInfo = pair.second;
        auto& instrument = *instrumentInfo.pInstrument;
        PriceType baseprice = _pBaseInstrument->theoreticalPrice;
        instrumentInfo.delta = instrument.shadowDelta;
        instrumentInfo.vega = instrument.vega;
        auto cashDelta = instrumentInfo.delta * baseprice * instrument.instrumentMultiplier * (instrumentInfo.position + instrumentInfo.longHedgingPosition - instrumentInfo.shortHedgingPosition);
        auto vega = instrumentInfo.vega * instrument.instrumentMultiplier * (instrumentInfo.position + instrumentInfo.longHedgingPosition - instrumentInfo.shortHedgingPosition);
        _cashDelta += cashDelta;
        _vega += vega;
        //LOG4CPLUS_INFO(_logger, "ins:" << instrument.instrumentId << ", delta=" << cashDelta << ", vega=" << vega << ", position:" << instrumentInfo.position << ", longHedgingPosition:" << instrumentInfo.longHedgingPosition << ", shortHedgingPosition:" << instrumentInfo.shortHedgingPosition) ;
    }
    _cashDelta -= _targetDelta;
    _vega -= _targetVega;
    LOG4CPLUS_INFO(_logger, "cash_delta=" << _cashDelta << ", vega=" << _vega);
    int rawDeltaFadeTicks = _cashDelta / _deltaFadePerTick;
    if (rawDeltaFadeTicks > _maxDeltaFadeTicks)
    {
        _deltaFadeTicks = _maxDeltaFadeTicks;
        _deltaSpreadTicksBid = rawDeltaFadeTicks - _maxDeltaFadeTicks + _deltaSpreadTicksBase;
        _deltaSpreadTicksAsk = _deltaSpreadTicksBase;
    }
    else if (rawDeltaFadeTicks < -_maxDeltaFadeTicks)
    {
        _deltaFadeTicks = -_maxDeltaFadeTicks;
        _deltaSpreadTicksBid = _deltaSpreadTicksBase;
        _deltaSpreadTicksAsk = -rawDeltaFadeTicks - _maxDeltaFadeTicks + _deltaSpreadTicksBase;
    }
    else
    {
        _deltaFadeTicks = rawDeltaFadeTicks;
        _deltaSpreadTicksAsk = _deltaSpreadTicksBase;
        _deltaSpreadTicksBid = _deltaSpreadTicksBase;
    }
    //LOG4CPLUS_INFO(_logger, "deltaFadeTicks=" << _deltaFadeTicks << ", deltaSpreadTicksBid=" << _deltaSpreadTicksBid << ", deltaSpreadTicksAsk=" << _deltaSpreadTicksAsk);
    int rawVegaFadeTicks = _vega / _vegaFadePerTick;
    if (rawVegaFadeTicks > _maxVegaFadeTicks)
    {
        _vegaFadeTicks = _maxVegaFadeTicks;
        _vegaSpreadTicksBid = rawVegaFadeTicks - _maxVegaFadeTicks + _vegaSpreadTicksBase;
        _vegaSpreadTicksAsk = _vegaSpreadTicksBase;
    }
    else if (rawVegaFadeTicks < -_maxVegaFadeTicks)
    {
        _vegaFadeTicks = -_maxVegaFadeTicks;
        _vegaSpreadTicksBid = _vegaSpreadTicksBase;
        _vegaSpreadTicksAsk = -rawVegaFadeTicks - _maxVegaFadeTicks + _vegaSpreadTicksBase;
    }
    else
    {
        _vegaFadeTicks = rawVegaFadeTicks;
        _vegaSpreadTicksAsk = _vegaSpreadTicksBase;
        _vegaSpreadTicksBid = _vegaSpreadTicksBase;
    }
    //LOG4CPLUS_INFO(_logger, "vegaFadeTicks=" << _vegaFadeTicks << ", vegaSpreadTicksBid=" << _vegaSpreadTicksBid << ", vegaSpreadTicksAsk=" << _vegaSpreadTicksAsk);
    updateMinSpread();
}

void BayesianMMStrategy::clearAllQueues()
{
    VolatilityMessage volatility;
    while (_volatilityQueue.try_dequeue(volatility));

    Feed feed;
    while (_feedQueue.try_dequeue(feed));

    OrderBook orderBook;
    while (_orderBookQueue.try_dequeue(orderBook));
    QuoteRequest rfq;
    while (_quoteRequestQueue.try_dequeue(rfq));
}
void BayesianMMStrategy::fadeDelta()
{
}

void BayesianMMStrategy::fadeVega()
{
}

void BayesianMMStrategy::fadeAllPosition()
{
    for (auto& p : _positions)
    {
        auto& insInfo = p.second;
        fadePosition(insInfo);
    }
}

void BayesianMMStrategy::fadePosition(InstrumentInfo& insInfo_)
{
    float faderBaseVolume = _faderBaseVolume;
    auto pInstrument = insInfo_.pInstrument;
    if (pInstrument && faderBaseVolume != 0)
    {
        VolumeType netVolumeTraded = pInstrument->netVolumeTraded;
        int multiplier = round(netVolumeTraded / faderBaseVolume);
        double positionFader = -insInfo_.minimumTick * multiplier;
        // auto positionFader = -multiplier;
        pInstrument->bidFader = positionFader;
        pInstrument->askFader = positionFader;
        insInfo_.positionFader = positionFader;
    }
}

inline
void BayesianMMStrategy::updateDynamicBase(MarketData const& baseMd_)
{
    double alpha = _dynamicBaseAlpha;
    double beta = _dynamicBaseBeta;
    double weight = _dynamicBaseWeight;
    VolumeType minVol = _dynamicBaseMinvol;
    double step = _dynamicBaseStep;

    PriceType bidPrice1 = baseMd_.bids[0]->price;
    VolumeType bidVolume1 = baseMd_.bids[0]->volume;
    PriceType bidPrice2 = baseMd_.bids[1]->price;
    VolumeType bidVolume2 = baseMd_.bids[1]->volume;
    PriceType askPrice1 = baseMd_.asks[0]->price;
    VolumeType askVolume1 = baseMd_.asks[0]->volume;
    PriceType askPrice2 = baseMd_.asks[1]->price;
    VolumeType askVolume2 = baseMd_.asks[1]->volume;


    // 步骤1
    int direct = 0;
    double refVol = 0;
    if (_dynamicBaseTotalVolumes.initialized())
    {
        auto lastTotalVolume = _dynamicBaseTotalVolumes.get(_dynamicBaseRefvolWindow);
        refVol = (baseMd_.tradedVolume - lastTotalVolume) / static_cast<double>(_dynamicBaseRefvolWindow);
        if (baseMd_.lastPrice > 0 && isfinite(refVol) && refVol > 0)
        {
            if (baseMd_.lastPrice >= _lastAskPrice1)
            {
                direct = 1;
            }
            else if (baseMd_.lastPrice <= _lastBidPrice1)
            {
                direct = -1;
            }
        }
    }

    // 步骤2
    double bidThreshold = 0.;
    double askThreshold = 0.;
    if (direct == 1)
    {
        askThreshold = refVol * alpha;
        bidThreshold = refVol * beta;
    }
    else if (direct == -1)
    {
        askThreshold = refVol * beta;
        bidThreshold = refVol * alpha;
    }

    // 步骤3
    bool bidFlag = false;
    bool askFlag = false;
    if (direct != 0 && bidThreshold > 0 && bidVolume1 <= max<double>(bidThreshold, minVol))
    {
        bidFlag = true;
    }
    if (direct != 0 && askThreshold > 0 && askVolume1 <= max<double>(askThreshold, minVol))
    {
        askFlag = true;
    }

    // 步骤4
    auto bestBidPrice = bidPrice1;
    auto bestAskPrice = askPrice1;
    auto bestBidVolume = bidVolume1;
    auto bestAskVolume = askVolume1;
    if (bidFlag)
    {
        bestBidPrice = (bidPrice1 * bidVolume1 + bidPrice2 * bidVolume2 * weight) / (bidVolume1 + bidVolume2 * weight);
        bestBidVolume = bidVolume1 + bidVolume2;
    }
    if (askFlag)
    {
        bestAskPrice = (askPrice1 * askVolume1 + askPrice2 * askVolume2 * weight) / (askVolume1 + askVolume2 * weight);
        bestAskVolume = askVolume1 + askVolume2;
    }
    auto base = getImbaMid(bestBidPrice, bestBidVolume, bestAskPrice, bestAskVolume, step);

    // 步骤5
    _dynamicBaseMid = base;
    _dynamicBaseTotalVolumes.push(baseMd_.tradedVolume);
    if (!isnan(askPrice1)) _lastAskPrice1 = askPrice1;
    if (!isnan(bidPrice1)) _lastBidPrice1 = bidPrice1;
}

inline
PriceType BayesianMMStrategy::getImbaMid(PriceType bidPrice1_, VolumeType bidVolume1_, PriceType askPrice1_, VolumeType askVolume1_, double step_)
{
    auto imba = (bidPrice1_ * askVolume1_ + askPrice1_ * bidVolume1_) / (askVolume1_ + bidVolume1_);
    auto mid = (bidPrice1_ + askPrice1_) / 2;
    auto spreadTick = (askPrice1_ - bidPrice1_) / _pBaseInstrument->pBaseInstrument->minimumTick;
    auto imbaWeight = max(step_, 1 - (spreadTick - 1) * step_);
    auto imbaMid = imba * imbaWeight + mid * (1 - imbaWeight);
    //LOG4CPLUS_INFO(_logger, "getImbaMid imba:" << imba  << " mid:" << mid << " spreadTick:" << spreadTick << " imbaWeight:" << imbaWeight << " imbaMid:" << imbaMid );
    return imbaMid;
}

void BayesianMMStrategy::onOptionsMarketData(MarketData const& marketData_)
{
    if (auto it = _positions.find(marketData_.pInstrument->instrumentId); it != _positions.end())
    {
        LOG4CPLUS_INFO(_logger, "OptionsMd[" << ::toString(marketData_) << "], market total position=" << marketData_.pInstrument->marketTotalPos);
        auto& insInfo = it->second;
        insInfo.pCurrentMarketData = &marketData_;
        // return true;
    }
    // return false;
}
void BayesianMMStrategy::sendHitOrders()
{
    for(auto it = _positions.begin(); it != _positions.end(); ++it)
    {
        auto& insInfo = it->second;
        Instrument* pInstrument = insInfo.pInstrument;
        if (pInstrument == nullptr) continue;
        PriceType halfTick = insInfo.minimumTick * 0.5;
        if (insInfo.pCurrentMarketData == nullptr) {
            continue;
        }
        auto const& marketData = *insInfo.pCurrentMarketData;
        switch(insInfo.pInstrument->instrumentType)
        {
            case InstrumentType::Options:
                switch(marketData.pInstrument->tradingStatus)
                {
                    case InstrumentTradingStatus::Trading:
                    {
                        std::string id = marketData.pInstrument->instrumentId;
                        if (insInfo.pInstrument->hitFlag
                                && !id.empty()
                                && pInstrument->retries[_level] < pInstrument->maxRetries[_level])
                        {
                            PriceType suggestedBid = round2Tick(insInfo.suggestedMid - insInfo.minBidSpread * _hitSpreadRatio, insInfo.minimumTick, RoundPolicy::RoundDown);
                            PriceType suggestedAsk = round2Tick(insInfo.suggestedMid + insInfo.minAskSpread * _hitSpreadRatio, insInfo.minimumTick, RoundPolicy::RoundUp);
                            if (marketData.asks[0]->volume != 0
                                    && suggestedBid > marketData.asks[0]->price - halfTick
                                    && pInstrument->bidSideTraded[_level] < pInstrument->bidSideTradesAllowed[_level])
                            {
                                if (insInfo.bidHitOrder.terminated())
                                {
                                    Order order;
                                    auto strategyLocalId = orderIdGenerator++;
                                    strcpy(order.instrumentId, pInstrument->instrumentId);
                                    order.price = suggestedBid;
                                    order.volumeOriginalTotal = pInstrument->hitLots;
                                    order.longShort = LONG_;
                                    order.orderType = LIMIT;
                                    order.matchCondition = FAK;
                                    order.openClose = AUTO_OPEN_CLOSE;
                                    order.exchangeId = pInstrument->exchangeId;
                                    order.strategyLocalId = strategyLocalId;
                                    order.basis = _pBaseInstrument->baseOffset + _pBaseInstrument->offsetAdjustment;
                                    order.basePrice = _pBaseInstrument->suggestedMidPrice - order.basis;
                                    order.volatility = pInstrument->quoteVolatility;
                                    order.theoreticalPrice = insInfo.suggestedMid;
                                    order.delta = insInfo.delta;
                                    order.vega = insInfo.vega;
                                    order.orderMotive = OrderMotive::Hit;
                                    strcpy(order.comments, "Hit");
                                    sendOrder(order);
                                    LOG4CPLUS_INFO(_logger, "HitOrder[" << order << "]");
                                    insInfo.bidHitOrder = order;
                                }
                            }
                            else if (marketData.bids[0]->volume != 0
                                    && suggestedAsk < insInfo.pCurrentMarketData->bids[0]->price + halfTick
                                    && pInstrument->askSideTraded[_level] < pInstrument->askSideTradesAllowed[_level])
                            {
                                if (insInfo.askHitOrder.terminated())
                                {
                                    Order order;
                                    auto strategyLocalId = orderIdGenerator++;
                                    strcpy(order.instrumentId, pInstrument->instrumentId);
                                    order.price = suggestedAsk;
                                    order.volumeOriginalTotal = pInstrument->hitLots;
                                    order.longShort = SHORT_;
                                    order.orderType = LIMIT;
                                    order.matchCondition = FAK;
                                    order.openClose = AUTO_OPEN_CLOSE;
                                    order.exchangeId = pInstrument->exchangeId;
                                    order.strategyLocalId = strategyLocalId;
                                    order.basis = _pBaseInstrument->baseOffset + _pBaseInstrument->offsetAdjustment;
                                    order.basePrice = _pBaseInstrument->suggestedMidPrice - order.basis;
                                    order.volatility = pInstrument->quoteVolatility;
                                    order.theoreticalPrice = insInfo.suggestedMid;
                                    order.delta = insInfo.delta;
                                    order.vega = insInfo.vega;
                                    order.orderMotive = OrderMotive::Hit;
                                    strcpy(order.comments, "Hit");
                                    sendOrder(order);
                                    LOG4CPLUS_INFO(_logger, "HitOrder[" << order << "]");
                                    insInfo.askHitOrder = order;
                                }
                            }
                        }
                        break;
                    }
                    default:
                        break;
                }
            default:
                break;
        }
    }
}
