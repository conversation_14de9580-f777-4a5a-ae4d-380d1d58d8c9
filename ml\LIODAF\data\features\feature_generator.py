"""
特征生成器模块
@author: lining
"""
import pandas as pd
import numpy as np
import os
from tqdm import tqdm
import time

from core import config
from utils.utils import log_print
from .factor_manager import factor_manager, FactorCategory

class OrderBookFeatureGenerator:
    """订单簿特征生成器"""
    def __init__(self, data,selected_features=None):
        self.data = data
        self.features = []
        self.selected_features = selected_features
        self.all_features = {}
        self.output_dir = config.OUTDIR + "/features"
        self.scalers = {}  # 存储标准化参数
        
        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_all_features(self):
        """生成所有特征"""
        log_print("开始生成所有特征...", level='info')

        # 检查必要的列是否存在
        required_cols = ['AskPrice1', 'BidPrice1','AskPrice2', 'BidPrice2','AskPrice3', 'BidPrice3','AskPrice4', 'BidPrice4','AskPrice5', 'BidPrice5','Volume','TotalValueTraded']
        missing_cols = [col for col in required_cols if col not in self.data.columns]
        if missing_cols:
            log_print(f"警告：数据中缺少必要的列: {missing_cols}", level='warning')
            return pd.DataFrame(), []
            

        # 使用因子管理器生成各类特征
        total_num = 0
        skip_num = 0
        for category in FactorCategory:
            factors = factor_manager.get_factors_by_category(category)
            for factor in tqdm(factors, desc=f"生成{category}特征"):
                total_num+=1
                calc_time = -1
                is_used = 'no'
                try:
                    # 验证因子
                    if factor.name not in self.selected_features:
                        skip_num+=1
                        is_used = 'skip'
                        log_print(f"因子 {factor.name} 未在use_features列表中，跳过", level='debug')
                        continue
                    
                    if factor_manager.validate_factor(factor.name, self.data):
                        # 计算因子,在报告中添加计算时间
                        start_time = time.time()
                        dependent_cols = factor.dependencies
                        if dependent_cols:
                            for col in dependent_cols:
                                if col not in self.data.columns:
                                    try:
                                        self.data[col] = factor_manager.calculate_factor(col, self.data)
                                    except Exception as e:
                                        log_print(f"警告：因子 {factor.name} 依赖的列 {col} 计算失败: {str(e)}", level='warning')
                        self.data[factor.name] = factor_manager.calculate_factor(factor.name, self.data)
                        end_time = time.time()
                        self.features.append(factor.name)
                        is_used = 'yes'
                        calc_time = end_time - start_time
                    else:
                        is_used = 'failed'
                        print(f"警告：因子 {factor.name} 验证失败")
                except Exception as e:
                    is_used = 'failed'
                    print(f"警告：因子 {factor.name} 计算失败: {str(e)}")
                finally:
                    self.all_features[factor.name] = [is_used, calc_time]
        
        # use_features中未生成的因子
        unused_factors = [factor for factor in self.selected_features if factor not in self.all_features]
        if unused_factors:
            log_print(f"警告：以下因子未生成: {unused_factors}", level='warning')
        
        # 检查是否有NaN值
        nan_cols = self.data.columns[self.data.isna().any()].tolist()
        if nan_cols:
            log_print(f"警告：以下特征列包含NaN值: {nan_cols}", level='warning')
            log_print(f"NaN值数量: {self.data[nan_cols].isna().sum()}", level='warning')
            
        # 检查是否有无穷值
        inf_cols = self.data.columns[self.data.isin([np.inf, -np.inf]).any()].tolist()
        if inf_cols:
            log_print(f"警告：以下特征列包含无穷值: {inf_cols}", level='warning')
            log_print(f"无穷值数量: {self.data[inf_cols].isin([np.inf, -np.inf]).sum()}", level='warning')
            
        return self.data, self.features

    
    def add_interaction_features(self):
        """
        添加特征交互项
        
        参数:
            data (pandas.DataFrame): 数据
        """
        interaction_features = []
        # 获取实际存在的特征列表
        existing_features = [col for col in self.features if col in self.data.columns]
        
        # 处理无穷值和异常值
        for col in existing_features:
            if col in self.data.columns:
                # 将无穷值替换为NaN
                self.data[col] = self.data[col].replace([np.inf, -np.inf], np.nan)
                # 使用中位数填充NaN
                self.data[col] = self.data[col].fillna(self.data[col].median())
        
        for i, col1 in enumerate(existing_features):
            for col2 in existing_features[i+1:]:
                if col1 in self.data.columns and col2 in self.data.columns:
                    # 计算交互特征
                    interaction = self.data[col1] * self.data[col2]
                    # 处理异常值
                    interaction = interaction.clip(
                        lower=interaction.quantile(0.01),
                        upper=interaction.quantile(0.99)
                    )
                    self.data[f'{col1}_{col2}_interaction'] = interaction
                    interaction_features.append(f'{col1}_{col2}_interaction')
        
        # 添加非线性变换
        for col in existing_features:
            if col in self.data.columns:
                # 平方变换
                squared = self.data[col]**2
                squared = squared.clip(
                    lower=squared.quantile(0.01),
                    upper=squared.quantile(0.99)
                )
                self.data[f'{col}_squared'] = squared
                
                # 对数变换
                # 添加小量值避免log(0)
                log_input = self.data[col].abs() + 1e-10
                log_value = np.log1p(log_input)
                log_value = log_value.clip(
                    lower=log_value.quantile(0.01),
                    upper=log_value.quantile(0.99)
                )
                self.data[f'{col}_log'] = log_value
                
                interaction_features.append(f'{col}_squared')
                interaction_features.append(f'{col}_log')

        self.features += interaction_features
