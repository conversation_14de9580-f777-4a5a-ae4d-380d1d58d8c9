"""
AutoGluon模型实现模块
@author: AI Assistant
"""

import os
import pandas as pd
import numpy as np
# from autogluon.tabular import TabularPredictor
# from autogluon.timeseries import TimeSeriesPredictor, TimeSeriesDataFrame
from sklearn.base import BaseEstimator, RegressorMixin
from typing import Dict, Optional
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.utils import log_print
from core import config


class AutoGluonRegressor(BaseEstimator, RegressorMixin):
    """
    AutoGluon回归器，用于与现有框架集成
    采用与项目中其他模型相同的接口，便于无缝替换
    """
    
    def __init__(
        self,
        time_limit: int = 600,
        presets: str = 'medium_quality',
        problem_type: str = 'regression',
        eval_metric: str = 'rmse',
        label: str = config.TARGET_COLS[0],
        model_selection_kwargs: Optional[Dict] = None,
        hyperparameters: Optional[Dict] = None,
        path: Optional[str] = None,
        verbosity: int = 2,
        time_series_predictor: bool = False,
        num_bag_folds: int = 0,
        num_stack_levels: int = 0,
        holdout_frac: Optional[float] = None,
        auto_stack: bool = True,
        refit_full: bool = False
    ):
        """
        初始化 AutoGluon 回归器
        
        参数:
            time_limit: 训练时间限制（秒）
            presets: 预设配置模式：'best_quality', 'high_quality', 'medium_quality', 'optimize_for_deployment', 'ignore_text'
            problem_type: 问题类型，默认为回归
            eval_metric: 评估指标，默认为rmse
            label: 目标列名称
            model_selection_kwargs: 模型选择参数
            hyperparameters: 模型超参数配置，可以用于排除特定模型
            path: 保存模型的路径
            verbosity: 日志详细程度
            time_series_predictor: 是否使用时间序列预测器
            num_bag_folds: 交叉验证折数，用于防止过拟合
            num_stack_levels: 堆叠模型的层数
            holdout_frac: 验证集比例
            auto_stack: 是否自动堆叠模型
            refit_full: 是否使用全部数据重新训练最佳模型
        """
        self.time_limit = time_limit
        self.presets = presets
        self.problem_type = problem_type
        self.eval_metric = eval_metric
        self.label = label
        self.model_selection_kwargs = model_selection_kwargs or {}
        
        # 设置默认不抛出无模型训练成功的错误
        if 'raise_on_no_models_fitted' not in self.model_selection_kwargs:
            self.model_selection_kwargs['raise_on_no_models_fitted'] = False
        
        self.hyperparameters = hyperparameters
        
        self.path = path
        self.verbosity = verbosity
        self.time_series_predictor = time_series_predictor
        
        # 初始化防止过拟合的参数
        self.num_bag_folds = num_bag_folds
        self.num_stack_levels = num_stack_levels
        self.holdout_frac = holdout_frac
        self.auto_stack = auto_stack
        self.refit_full = refit_full
        
        self.predictor = None
        self.feature_importance = None

    def set_sample_weight(self, train_data):
        """
        设置样本权重
        
        参数:
            train_data: 训练数据
        """
        # 新增：根据波动率设置样本权重
        if 'sample_weight' not in train_data.columns:
            # 以目标变量绝对值为权重示例（可自定义）
            # threshold = 0.00002/5000  # 你可以根据实际情况调整
            threshold = train_data[self.label].quantile(config.QUANTILE_LIST[-3])
            train_data['sample_weight'] = np.where(
                np.abs(train_data[self.label]) > threshold, 2.0, 1.0
            )
        return train_data

        
    def fit(self, X, y):
        """
        使用 AutoGluon 训练模型
        
        参数:
            X: 特征数据
            y: 目标变量
        
        返回:
            self: 训练好的模型实例
        """
        # 准备训练数据
        if isinstance(X, pd.DataFrame):
            train_data = X.copy()
        else:
            train_data = pd.DataFrame(X)
        
        # 添加目标变量
        train_data[self.label] = y

        # 设置样本权重
        # train_data = self.set_sample_weight(train_data)


        # 检查数据是否为空或有NaN值
        if train_data.empty:
            raise ValueError("训练数据为空，无法训练模型")
        
        # 记录数据信息
        log_print(f"训练数据形状: {train_data.shape}, 特征列: {list(train_data.columns)}")
        log_print(f"数据类型: {train_data.dtypes.to_dict()}")
        if train_data.isna().any().any():
            log_print(f"警告: 数据中存在NaN值，数量: {train_data.isna().sum().sum()}")
        
        # 设置模型保存路径
        if self.path is None:
            self.path = os.path.join(config.OUTDIR, 'models', 'autogluon')
            os.makedirs(self.path, exist_ok=True)
        
        log_print(f"开始训练 AutoGluon 模型，时间限制：{self.time_limit}秒，预设：{self.presets}")
        
        # 训练模型
        if self.time_series_predictor is False:
            self.predictor = TabularPredictor(
                label=self.label,
                problem_type=self.problem_type,
                eval_metric=self.eval_metric,
                path=self.path
            )
        else:
            self.predictor = TimeSeriesPredictor(
                label=self.label,
                prediction_length=10,
                eval_metric=self.eval_metric,
                path=self.path
            )
            train_data['Symbol'] = 'io'
            train_data = TimeSeriesDataFrame.from_data_frame(
                    train_data.reset_index(),
                    id_column="Symbol",
                    timestamp_column="timestamp_str",
                )
        
        # 开始训练
        try:
            self.predictor.fit(
                train_data=train_data,
                time_limit=self.time_limit,
                presets=self.presets,
                hyperparameters=self.hyperparameters,
                verbosity=self.verbosity,
                num_bag_folds=self.num_bag_folds,
                num_stack_levels=self.num_stack_levels,
                holdout_frac=self.holdout_frac,
                auto_stack=self.auto_stack,
                refit_full=self.refit_full,
                sample_weight='sample_weight',  # 新增,增大波动率样本权重
                **self.model_selection_kwargs
            )
            log_print("AutoGluon模型训练完成")
        except Exception as e:
            log_print(f"AutoGluon训练过程中出现错误: {e}", "error")
            log_print("尝试调整训练参数...")
            # 增加训练时间限制
            extended_time_limit = max(1200, self.time_limit * 2)
            log_print(f"增加时间限制到 {extended_time_limit}秒")
            
            # 使用更简单的预设以加快训练
            simple_preset = 'medium_quality' if self.presets != 'medium_quality' else 'optimize_for_deployment'
            log_print(f"使用更简单的预设: {simple_preset}")
            
            # 确保不会因为无模型训练成功而抛出异常
            self.model_selection_kwargs['raise_on_no_models_fitted'] = False
            
            self.predictor.fit(
                train_data=train_data,
                time_limit=extended_time_limit,
                presets=simple_preset,
                hyperparameters=self.hyperparameters,
                verbosity=max(self.verbosity, 3),  # 增加详细度
                num_bag_folds=self.num_bag_folds,
                num_stack_levels=self.num_stack_levels,
                holdout_frac=self.holdout_frac,
                auto_stack=self.auto_stack,
                refit_full=self.refit_full,
                **self.model_selection_kwargs
            )
            log_print("使用调整后的参数完成训练")
        
        # 获取特征重要性
        try:
            self.feature_importance = self.predictor.feature_importance(train_data)
            log_print("成功获取特征重要性")
        except Exception as e:
            log_print(f"无法获取特征重要性: {e}", "warning")
            self.feature_importance = None
            
        return self
    
    def predict(self, X):
        """
        使用训练好的模型进行预测
        
        参数:
            X: 特征数据
        
        返回:
            ndarray: 预测结果
        """
        if self.predictor is None:
            raise ValueError("模型尚未训练，请先调用 fit 方法")
        
        # 准备测试数据
        if isinstance(X, pd.DataFrame):
            test_data = X.copy()
        else:
            test_data = pd.DataFrame(X)
        
        # 检查是否有数据
        if test_data.empty:
            log_print("警告: 预测数据为空", "warning")
            return np.array([])
            
        # 设置样本权重
        # test_data = self.set_sample_weight(test_data)
        # 进行预测
        try:
            return self.predictor.predict(test_data, sample_weight='sample_weight').values
        except Exception as e:
            log_print(f"预测过程中出现错误: {e}", "error")
            # 返回空预测结果
            return np.zeros(len(test_data))
    
    def get_feature_importance(self):
        """
        获取特征重要性
        
        返回:
            DataFrame: 特征重要性
        """
        return self.feature_importance
    
    def leaderboard(self, data=None):
        """
        获取模型排行榜
        
        参数:
            data: 可选的数据集用于评估
            
        返回:
            DataFrame: 模型性能排行榜
        """
        if self.predictor is None:
            raise ValueError("模型尚未训练，请先调用 fit 方法")
        
        return self.predictor.leaderboard(data)
    
    def save(self, path=None):
        """
        保存模型
        
        参数:
            path: 保存路径
        """
        if self.predictor is None:
            raise ValueError("模型尚未训练，请先调用 fit 方法")
        
        save_path = path or self.path
        self.predictor.save()
        log_print(f"AutoGluon 模型已保存到 {save_path}")
    
    def load(self, path):
        """
        加载模型
        
        参数:
            path: 模型路径
            
        返回:
            self: 加载好的模型实例
        """
        self.predictor = TabularPredictor.load(path)
        self.path = path
        log_print(f"成功从 {path} 加载 AutoGluon 模型")
        return self
        
    def score(self, X, y):
        """
        评估模型性能
        
        参数:
            X: 特征数据
            y: 目标变量
            
        返回:
            float: R^2 分数
        """
        predictions = self.predict(X)
        
        # 计算 R^2 分数
        if len(y) > 0:
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            ss_res = np.sum((y - predictions) ** 2)
            if ss_tot == 0:
                return 0  # 避免除以零
            return 1 - (ss_res / ss_tot)
        else:
            return 0
    
    

def autogluon_example():
    """
    AutoGluon模型使用示例
    """
    # 初始化M1模型和数据
    log_print("初始化数据和模型...")
    from core.m1 import M1
    m1 = M1()
    
    # 设置模型类型为autogluon
    m1.model_type = 'autogluon'
    
    # 处理数据
    log_print(f"处理数据: {config.CODE_LIST}")
    m1.mix_df = m1.process_data(config.CODE_LIST)
    
    # 生成特征和标签
    log_print("生成特征和标签...")
    m1.generate_features_and_labels()
    
    # 获取目标和特征列
    target_col = config.TARGET_COLS[0]
    
    # 获取训练数据 - 修复：应当使用所有特征列而不是只用target_col
    X_train = m1.train_data[target_col].drop(columns=[config.TARGET_LABEL]) if config.TARGET_LABEL in m1.train_data[target_col].columns else m1.train_data[target_col]
    y_train = m1.y_train_dict[target_col]
    
    # 获取测试数据 - 同样修复
    X_test = m1.test_data[target_col].drop(columns=[config.TARGET_LABEL]) if config.TARGET_LABEL in m1.test_data[target_col].columns else m1.test_data[target_col]
    y_test = m1.y_test_dict[target_col]
    
    log_print(f"训练数据形状: {X_train.shape}, 标签形状: {y_train.shape}")
    
    # 设置AutoGluon参数
    ag_params = config.MODEL_SETTING['autogluon']['params']
    
    # 创建并训练AutoGluon模型，添加错误处理选项
    log_print("创建并训练AutoGluon模型...")
    model = AutoGluonRegressor(**ag_params)
    try:
        model.fit(X_train, y_train)
    except RuntimeError as e:
        log_print(f"训练失败: {e}", "error")
        # 尝试增加verbosity和设置raise_on_no_models_fitted=False
        log_print("尝试使用更宽松的参数重新训练...")
        model.verbosity = 3  # 增加日志详细程度
        model.model_selection_kwargs['raise_on_no_models_fitted'] = False
        model.fit(X_train, y_train)
    
    # 获取模型排行榜
    log_print("模型排行榜:")
    try:
        leaderboard = model.leaderboard()
        print(leaderboard)
    except Exception as e:
        log_print(f"无法获取模型排行榜: {e}", "warning")
    
    # 预测测试集
    log_print("预测测试集...")
    y_pred = model.predict(X_test)
    
    # 评估模型性能
    log_print("评估模型性能:")
    # 将numpy数组转换为pandas Series
    y_pred_series = pd.Series(y_pred, index=y_test.index)
    from models.model_evaluate import model_evaluate
    metrics = model_evaluate(y_test, y_pred_series, 'autogluon')
    for horizon in config.EVALUATE_HORIZONS:
        model_evaluate(m1.mix_df[f"{config.TARGET_LABEL}_{horizon}"][m1.test_data[target_col].index],
                    y_pred_series, 'test')
    
    return model, metrics


if __name__ == "__main__":
    log_print("===== AutoGluon模型 =====")
    
    # 运行AutoGluon示例
    model, metrics = autogluon_example()
    
    
    log_print("===== 完成 =====")