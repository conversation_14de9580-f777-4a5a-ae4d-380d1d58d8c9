"""
技术指标因子
@author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory
from core import config
from .basic_factors import calculate_avg_prc

factor_names = [
    "corr_rank",
    "trdshock",
    "trade_flow",
    "trade_flow_ewm",
    "trd_offset",
    "last_return_10",
    "last_out_ma_10",
    "last_out_ma_30",
    "last_out_ma_60",
    "last_out_ma_240",
    "dir_ratio",
    "turnover_mid_corr",
    "trend_strength",
    "accel_reversal",
    "volatility_adjusted_return",
    "trend_flip",
    "mean_gap_relative"
]


def corr_rank(df, window=6):  #IC -0.05
    a = df['AskPrice1']
    b = df['BidPrice1']
    c = df['LastPrice'].diff()
    # 公式: factor =  rank(price_diff) * correlation(ask_price, bid_price)
    # 其中:
    # - price_diff: 价格变化
    # - correlation: 在window窗口内计算ask_price和bid_price的相关系数
    # 计算因子：
    # 1. c.rank()：计算价格变化的排名
    # 2. pd.concat([a, b], axis=1)：将买一价和卖一价合并为一个DataFrame
    # 3. rolling(window=window, min_periods=1).corr()：计算窗口内的相关系数矩阵
    # 4. iloc[::2, 1]：从相关系数矩阵中提取买一价和卖一价的相关系数。[start:end:step]
    # 5. reset_index(drop=True)：重置索引
    # 6. 最后用价格变化排名乘以相关系数
    # 创建DataFrame用于计算相关系数
    df_corr = pd.concat([a, b], axis=1)
    # 计算滚动相关系数
    rolling_corr = df_corr.rolling(window=window, min_periods=1).corr()
    # 提取相关系数并确保长度匹配
    # 相关系数矩阵中每个窗口会产生2x2的矩阵，需要正确提取a和b之间的相关系数
    corr_values = rolling_corr.iloc[1::2, 0].values  # 提取a和b的相关系数
    # 确保长度与原始数据一致
    corr_series = pd.Series(corr_values, index=c.index)
    # 计算最终因子
    factor = c.rank() * corr_series
    # 使用np.nan_to_num函数处理因子中的异常值：
    return np.nan_to_num(factor, posinf=0, neginf=0)


factor_manager.register_factor(Factor(
    name="corr_rank",
    category=FactorCategory.TECHNICAL,
    description="计算买卖盘价格相关性的排名组合因子",
    calculation=corr_rank,
    source="lining-sxod",
    dependencies=['AskPrice1', 'BidPrice1', 'LastPrice'],
    parameters={'window': 6}
))


def trdshock(df, window=10):
    """
    计算交易冲击因子
    公式: ema_vol / trade_price
    其中:
    trade_price = BidPrice1 (当net_tradeprice > mid) 或 -AskPrice1 (当net_tradeprice <= mid)
    ema_vol = calculate_ema(trade_vol, window)
    """

    trade_vol = df['Volume'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    avg_price = calculate_avg_prc(df)

    trade_price = np.where(avg_price > mid, df['BidPrice1'], -df['AskPrice1'])
    trade_price = np.where((avg_price == 0), mid, trade_price)

    ema_vol = trade_vol.ewm(span=window, adjust=False).mean()
    ema_vol = np.where((trade_vol == 0), 0, ema_vol)
    return ema_vol / trade_price


factor_manager.register_factor(Factor(
    name="trdshock",
    category=FactorCategory.TECHNICAL,
    description="计算交易冲击因子，衡量交易量对价格的影响程度",
    calculation=trdshock,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'window': 10}
))


def trdvol_with_dir(df):
    """
    计算交易流量因子
    公式: ema(trade_vol_with_dir, window)
    其中:
    trade_vol_with_dir = trade_vol (当net_tradeprice > mid) 或 -trade_vol (当net_tradeprice <= mid)
    """
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    avg_price = trade_value / (trade_vol * config.MULT)
    avg_price = np.where((trade_vol == 0), mid, avg_price)
    trade_vol_with_dir = np.where(avg_price > mid, trade_vol, -trade_vol)
    trade_vol_with_dir = np.where((trade_vol == 0), 0, trade_vol_with_dir)

    return trade_vol_with_dir


factor_manager.register_factor(Factor(
    name="trdvol_with_dir",
    category=FactorCategory.TECHNICAL,
    description="计算交易流量因子，衡量买卖方向的交易量",
    calculation=trdvol_with_dir,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'window': 10, 'contract_mul': 200}
))

factor_manager.register_factor(Factor(
    name="trdvol_with_dir_ewm",
    category=FactorCategory.TECHNICAL,
    description="计算交易流量因子，衡量买卖方向的交易量",
    calculation=lambda df: trdvol_with_dir(df).ewm(span=10, adjust=False).mean(),
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'window': 10, 'contract_mul': 200}
))


def trd_offset(df, contract_mul=200):  #IC 0.11
    """
    计算净交易价格与中间价的差异
    公式: net_tradeprice - mid
    其中:
    net_tradeprice = trade_value / (trade_vol * contract_mul)
    avg_prc = trade_value / (trade_vol * contract_mul)
    avg_prc = np.where((avg_prc == 0) , mid, avg_prc)
    mid = (AskPrice1 + BidPrice1) / 2
    """
    trade_vol = df['Volume'].diff()
    if 'avg_prc' not in df.columns:
        df['avg_prc'] = calculate_avg_prc(df)
    df['avg_prc'] = np.where((trade_vol == 0), df['mid'], df['avg_prc'])
    return df['avg_prc'] - df['mid']


factor_manager.register_factor(Factor(
    name="trd_offset",
    category=FactorCategory.TECHNICAL,
    description="计算净交易价格与中间价的差异",
    calculation=trd_offset,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
))


def last_return(df, window=10):  #IC 0.07
    """
    计算价格动量因子
    公式: log(LastPrice / LastPrice(t-window))
    其中:
    LastPrice: 当前价格
    LastPrice(t-window): 窗口前的价格
    """
    shift_price = df['LastPrice'].shift(window)
    return np.where(pd.isna(shift_price), 0, np.log(df['LastPrice'] / shift_price))


factor_manager.register_factor(Factor(
    name="last_return_10",
    category=FactorCategory.TECHNICAL,
    description="计算动量因子",
    calculation=last_return,
    source="lining-sxod",
    dependencies=['LastPrice'],
    parameters={'window': 1}
))


def last_out_ma(df, window: int):
    """
    计算最新价格的反转因子
    公式: (LastPrice - MA) / STD
    其中:
    MA = LastPrice的window周期移动平均
    STD = LastPrice的window周期标准差
    """
    ma = df['LastPrice'].rolling(window=window).mean()
    std = df['LastPrice'].rolling(window=window).std()

    return np.nan_to_num((df['LastPrice'] - ma) / std, posinf=0, neginf=0)


for window in [10, 30, 60, 240]:
    factor_manager.register_factor(Factor(
        name=f"last_out_ma_{window}",
        category=FactorCategory.TECHNICAL,
        description="计算最新价格的反转因子，衡量价格偏离均值的程度",
        calculation=lambda df: last_out_ma(df, window),
        source="lining-sxod",
        dependencies=['LastPrice'],
        parameters={'window': window}
    ))


def dir_ratio(df, window=None):
    """
    计算上行波动占比
    公式: sum(mid_up_pct^2, window) / sum(mid_pct^2, window)
    其中:
    mid = (AskPrice1 + BidPrice1) / 2
    mid_pct = mid的百分比变化
    mid_up_pct = mid_pct (当mid_pct > 0) 或 0 (当mid_pct <= 0)
    """
    if window is None:
        window = 240
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    mid_pct = mid.pct_change()
    is_up = np.where(mid_pct > 0, 1, 0)
    mid_up_pct = is_up * mid_pct
    return (mid_up_pct ** 2).rolling(window=window, min_periods=1).sum() / (mid_pct ** 2).rolling(window=window,
                                                                                                  min_periods=1).sum()


factor_manager.register_factor(Factor(
    name="dir_ratio",
    category=FactorCategory.TECHNICAL,
    description="计算上行波动占比，衡量上涨波动在总波动中的比例",
    calculation=dir_ratio,
    source="lining-sxod",
    dependencies=['AskPrice1', 'BidPrice1'],
    parameters={'window': 240}
))

def turnover_mid_corr(df, window=60):
    trade_vol = df['Volume'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2

    return mid.rolling(window=window, min_periods=1).corr(trade_vol)


factor_manager.register_factor(Factor(
    name="turnover_mid_corr",
    category=FactorCategory.TECHNICAL,
    description="计算成交量相关性因子",
    calculation=turnover_mid_corr,
    source="lining-sxod",
    dependencies=['Volume', 'TotalValueTraded', 'AskPrice1', 'BidPrice1'],
    parameters={'window': 60}
))


def mid_persistent(df, window=240):
    """
    计算趋势强度
    公式: (mid(t) - mid(t-window)) / sum(|mid.diff()|, window)
    其中:
    mid = (AskPrice1 + BidPrice1) / 2
    """
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    mid_abs_diff = np.abs(mid.diff())
    trade_strength = (mid - mid.shift(window)) / mid_abs_diff.rolling(window=window, min_periods=1).sum()
    return np.nan_to_num(trade_strength, posinf=0, neginf=0)


factor_manager.register_factor(Factor(
    name="mid_persistent",
    category=FactorCategory.TECHNICAL,
    description="计算趋势强度，衡量价格变化的方向性",
    calculation=mid_persistent,
    source="lining-sxod",
    dependencies=['AskPrice1', 'BidPrice1'],
    parameters={'window': 240}
))


def accel_reversal(df, window_long=20, window_short=10, threshold_scale=0.5):
    """
    计算增强版alpha_49因子
    公式: 
    1 (当part1 < threshold) 或 -1 * price.pct_change(1) (当part1 >= threshold)
    其中:
    part1 = (ret_long - ret_short)
    ret_long = (price(t-window_short) - price(t-window_long)) / price(t-window_long)
    ret_short = (price(t) - price(t-window_short)) / price(t-window_short)
    threshold = -threshold_scale * part1的60周期滚动标准差
    """
    # 计算价格变化加速度（标准化为收益率）
    price = df['LastPrice']
    ret_long = (price.shift(window_short) - price.shift(window_long)) / price.shift(window_long)
    ret_short = (price - price.shift(window_short)) / price.shift(window_short)
    part1 = (ret_long - ret_short)

    # 动态阈值（基于滚动波动率）
    threshold = -threshold_scale * part1.rolling(60).std()

    # 因子赋值
    condition = (part1 < threshold)
    factor = np.where(condition, 1, -1 * price.pct_change(1))
    return factor


factor_manager.register_factor(Factor(
    name="accel_reversal",
    category=FactorCategory.TECHNICAL,
    description="计算增强版alpha_49因子，基于价格变化加速度的反转策略",
    calculation=accel_reversal,
    source="lining-sxod",
    dependencies=['LastPrice'],
    parameters={'window_long': 20, 'window_short': 10, 'threshold_scale': 0.5}
))


def volatility_adjusted_return(df, window=20):
    """
    计算增强版alpha_101因子
    公式: price_diff / range_smoothed
    其中:
    price_diff = LastPrice的1周期差分
    range_smoothed = (High - Low)的window周期移动平均 + 1e-6
    """
    # 计算波动率调整后的价格变化
    price_diff = df['LastPrice'].diff(1)
    daily_range = df['High'] - df['Low']
    # 波动率平滑（滚动均值）
    range_smoothed = daily_range.rolling(window, min_periods=1).mean() + 1e-6
    # 标准化因子
    factor = price_diff / range_smoothed
    return factor


factor_manager.register_factor(Factor(
    name="volatility_adjusted_return",
    category=FactorCategory.TECHNICAL,
    description="计算增强版alpha_101因子，波动率调整后的价格变化",
    calculation=volatility_adjusted_return,
    source="lining-sxod",
    dependencies=['LastPrice', 'High', 'Low'],
    parameters={'window': 20}
))


def trend_flip(df, window1=1, window2=5):  #IC -0.06  精品
    """
    此因子逻辑为若window2长度窗口上lp_rate值均大于0或均小于0，则lp_rate为原值；
    若window2长度窗口上lp_rate最小值小于0，最大值大于0，则lp_rate为其相反数
    
    为什么这样设计：
    1. 当价格在window2窗口内持续上涨(s1=True)或持续下跌(s2=True)时，保持原始趋势信号
    2. 当价格在window2窗口内出现震荡(既有上涨又有下跌)时，反转信号
    3. 这种设计能够捕捉趋势反转点，在震荡市场中提供逆势交易信号
    4. IC值为-0.06，说明该因子具有较好的预测能力
    
    可以加上rank,但提升不大
    """

    # 计算价格变化率
    lp_rate = df['LastPrice'].diff(window1)
    
    # 判断趋势类型
    # 持续上涨：窗口内所有价格变化都为正
    is_continuous_up = lp_rate.rolling(window=window2, min_periods=1).min() > 0
    # 持续下跌：窗口内所有价格变化都为负
    is_continuous_down = lp_rate.rolling(window=window2, min_periods=1).max() < 0
    # 震荡市场：既有上涨又有下跌
    is_oscillating = ~(is_continuous_up | is_continuous_down)
    
    # 因子计算逻辑：
    # 1. 持续上涨或持续下跌时保持原始信号
    # 2. 震荡市场时反转信号
    # 根据市场状态调整因子值：
    # - 趋势市场(持续上涨或下跌)：使用原始价格变化率
    # - 震荡市场(既有上涨又有下跌)：使用价格变化率的反向值
    # 这相当于条件表达式：factor = lp_rate if (趋势市场) else -lp_rate
    factor = np.where(is_continuous_up | is_continuous_down, lp_rate, -lp_rate)
    return np.nan_to_num(factor, posinf=0, neginf=0)


factor_manager.register_factor(Factor(
    name="trend_flip",
    category=FactorCategory.TECHNICAL,
    description="基于价格变化趋势的因子，在震荡市场中提供逆势交易信号，IC值为-0.06",
    calculation=trend_flip,
    dependencies=['LastPrice'],
    parameters={'window1': 1, 'window2': 5},
    source="lining-sxod"
))


def mean_gap(df, long_window=50, short_window=10):  #IC -0.02
    long_mean = df['LastPrice'].rolling(window=long_window).mean()
    short_mean = df['LastPrice'].rolling(window=short_window).mean()
    factor = np.nan_to_num((short_mean - long_mean) / df['LastPrice'], posinf=0, neginf=0)
    return np.nan_to_num(factor, posinf=0, neginf=0)


factor_manager.register_factor(Factor(
    name="mean_gap_relative",
    category=FactorCategory.TECHNICAL,
    description="计算长短期价格均值的相对差异，IC值为-0.02",
    calculation=mean_gap,
    dependencies=['LastPrice'],
    parameters={'long_window': 50, 'short_window': 10},
    source="lining-YA-sxod"
))
