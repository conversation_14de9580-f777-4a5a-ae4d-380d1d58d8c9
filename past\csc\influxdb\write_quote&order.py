# -*- coding: utf-8 -*-
"""
Created on Wed Sep  8 09:50:46 2021

@author: yihw
"""

from influxdb import InfluxDBClient
import datetime
import time
import numpy as np
import pandas as pd
from OmmDatabase import OmmDatabase


#%%
client = InfluxDBClient('10.17.88.168',9001,'reader','reader','omm') # 东坝机房




#%%
date_list = ['2021-08-30', '2021-08-31', '2021-09-01', '2021-09-02', '2021-09-03', '2021-09-06', '2021-09-07', '2021-09-08', '2021-09-09', '2021-09-10']
# date = '2021-08-25'



exchange = 'zce'

underlying_list = ['CF', 'RM', 'SR']
for date in date_list:
    print(date)
    if exchange == 'dce':
        db_path = 'C:/Users/<USER>/Desktop/data/dce_future2_' + date + '/dce_future/'
    elif exchange == 'zce':
        db_path = 'C:/Users/<USER>/Desktop/data/zce_future_' + date + '/zce_future/'
    elif exchange == 'shfe':
        db_path = 'C:/Users/<USER>/Desktop/data/shfe_future_' + date + '/shfe_future/'
    else:
        print('你这交易所有问题啊')
        break
    
    t = time.time()
    
    df_order_summary1 = []
    df_order_summary2 = []
    df_quote_summary = []    
    
    for underlying in underlying_list:
        testDB = OmmDatabase(db_path)
    
        dfQA = testDB.read_file(db_path + 'QuoteService/all_trade/{}/'.format(underlying), date) 
        dfQC = testDB.read_file(db_path + 'QuoteService/cancel/{}/'.format(underlying), date) 
        dfQO = testDB.read_file(db_path + 'QuoteService/order/{}/'.format(underlying), date) 
        dfQPC = testDB.read_file(db_path + 'QuoteService/prepare_cancel/{}/'.format(underlying), date) 
        dfQPO = testDB.read_file(db_path + 'QuoteService/prepare_order/{}/'.format(underlying), date) 
        dfQT = testDB.read_file(db_path + 'QuoteService/trade/{}/'.format(underlying), date) 
        
        dfOC = testDB.read_file(db_path + 'OrderService/cancel/{}/'.format(underlying), date) 
        dfOO = testDB.read_file(db_path + 'OrderService/order/{}/'.format(underlying), date) 
        dfOT = testDB.read_file(db_path + 'OrderService/trade/{}/'.format(underlying), date) 
        dfOPO = testDB.read_file(db_path + 'OrderService/prepare_order/{}/'.format(underlying), date) 
        dfOPC = testDB.read_file(db_path + 'OrderService/prepare_cancel/{}/'.format(underlying), date)  
        
        # 进 df_order_summary1
        try:
            dfOC_dict = dfOC.to_dict('records')
            df_order_summary1 += dfOC_dict
        except:
            dfOC_dict = []
            
        try:
            dfOO_dict = dfOO.to_dict('records')
            df_order_summary1 += dfOO_dict
        except:
            dfOO_dict = []    
            
        try:
            dfOT_dict = dfOT.to_dict('records')
            df_order_summary1 += dfOT_dict
        except:
            dfOT_dict = [] 
            
        try:
            dfOPO_dict = dfOPO.to_dict('records')
            df_order_summary1 += dfOPO_dict
        except:
            dfOPO_dict = []   
            
        try:
            dfOPC_dict = dfOPC.to_dict('records')
            df_order_summary1 += dfOPC_dict
        except:
            dfOPC_dict = []    
        
        try:
            dfQC_dict = dfQC.to_dict('records')
            df_order_summary1 += dfQC_dict
        except:
            dfQC_dict = []        
        
        # 进 df_order_summary2
        try:
            dfQO_dict = dfQO.to_dict('records')    
            df_order_summary2 += dfQO_dict
        except:
            dfQO_dict = []
            
        # 进 df_quote_summary
        try:
            dfQPO_dict = dfQPO.to_dict('records')    
            df_quote_summary += dfQPO_dict
        except:
            dfQPO_dict = []        
    
        try:
            dfQPC_dict = dfQPC.to_dict('records')    
            df_quote_summary += dfQPC_dict
        except:
            dfQPC_dict = []        
    
    print('数据读取时间:', time.time()-t)
    
    t = time.time()
    json_body1 = []
    json_body2 = []
    json_body3 = []
    
    
    for row in df_order_summary1:
        
        current_time = int(datetime.datetime.strptime(str(row['SystemStamp']),'%Y%m%d%H%M%S%f').timestamp()*10**9)
        
        measurement = '{}_future_order'.format(exchange)
        
        base_price = float(row['BasePrice'])
        try:
            comments = str(row['Comments'])
        except:
            comments = ''
        error_id = 0
        error_message = str(row['ErrorMessage'])
        insid = str(row['InstrumentId'])
        internal_order_id = int(row['InternalOrderId'])
        last_traded_time = -1
        last_update_time = -1
        level = int(row['Level'])
        local_time = current_time
        long_short = int(row['LongShort'])
        match_condition = int(row['MatchCondition'])
        note = 'OS准备撤单' if row['OrderStatus'] == 1 else 'OS准备下单' if row['OrderStatus'] == 0 else 'OS收到Order回报'
        open_close = int(row['OpenClose'])
        order_id = str(row['OrderID'])
        
        order_price = float(row['OrderPrice'])
        order_status = int(row['OrderStatus'])
        portfolio_id = int(row['PortfolioID'])
        stratege_id = int(row['StrategyID'])
        traded_price = float(row['TradedPrice'])
        volume_origin_total = int(row['Quantity'])
        volume_total = int(row['VolumeTotal'])
        volume_traded = int(row['VolumeTraded'])
           
        body = {
                "measurement": measurement, 
                "time": current_time, 
                "tags": {
                    "insid": insid
                }, 
                "fields": {
                    "base_price": base_price, 
                    "comments": comments,                 
                    "error_id": error_id,                 
                    "error_message": error_message,                 
                    "internal_order_id": internal_order_id,                 
                    "last_traded_time": last_traded_time,                 
                    "last_update_time": last_update_time,                    
                    "local_time": local_time,                    
                    "long_short": long_short,                    
                    "match_condition": match_condition,                    
                    "note": note, 
                    "open_close": open_close,                 
                    "order_id": order_id,                 
                    "order_price": order_price,                 
                    "order_status": order_status,                 
                    "portfolio_id": portfolio_id,                 
                    "stratege_id": stratege_id,                    
                    "traded_price": traded_price,                    
                    "volume_origin_total": volume_origin_total,                    
                    "volume_total": volume_total, 
                    "volume_traded": volume_traded               
                }, 
            }
        
        
        json_body1.append(body)
        
    
    for row in df_order_summary2:
        
        for i in range(2):
        
            current_time = int(datetime.datetime.strptime(str(row['SystemStamp']),'%Y%m%d%H%M%S%f').timestamp()*10**9)+i
            
            measurement = '{}_future_order'.format(exchange)
            
            if i == 0: # ask单
                base_price = float(row['baseAskPrice'])
                comments = ''
                error_id = 0
                error_message = str(row['errorMessage'])
                insid = str(row['instrumentId'])
                internal_order_id = int(row['askInternalOrderId'])
                last_traded_time = -1
                last_update_time = -1
                level = int(row['level'])
                local_time = current_time
                long_short = 1
                match_condition = -1
                note = ''
                open_close = int(row['askOpenClose'])
                order_id = str(row['askOrderID'])
                
                order_price = float(row['askOrderPrice'])
                
                portfolio_id = int(row['portfolioID'])
                stratege_id = int(row['strategyID'])
                traded_price = float(row['askOrderPrice'])
                volume_origin_total = int(row['askVolumeOriginalTotal'])
                volume_total = int(row['askVolumeTotal'])
                volume_traded = int(row['askVolumeTraded'])
                order_status = 2 if volume_traded == 0 else 3 if volume_traded > 0 and volume_total > 0 else 4
                   
                body = {
                        "measurement": measurement, 
                        "time": current_time, 
                        "tags": {
                            "insid": insid
                        }, 
                        "fields": {
                            "base_price": base_price, 
                            "comments": comments,                 
                            "error_id": error_id,                 
                            "error_message": error_message,                 
                            "internal_order_id": internal_order_id,                 
                            "last_traded_time": last_traded_time,                 
                            "last_update_time": last_update_time,                    
                            "local_time": local_time,                    
                            "long_short": long_short,                    
                            "match_condition": match_condition,                    
                            "note": note, 
                            "open_close": open_close,                 
                            "order_id": order_id,                 
                            "order_price": order_price,                 
                            "order_status": order_status,                 
                            "portfolio_id": portfolio_id,                 
                            "stratege_id": stratege_id,                    
                            "traded_price": traded_price,                    
                            "volume_origin_total": volume_origin_total,                    
                            "volume_total": volume_total, 
                            "volume_traded": volume_traded               
                        }, 
                    }
                                
                json_body2.append(body)    
                
            if i == 1: # bid 单
                base_price = float(row['baseBidPrice'])
                comments = ''
                error_id = 0
                error_message = str(row['errorMessage'])
                insid = str(row['instrumentId'])
                internal_order_id = int(row['bidInternalOrderId'])
                last_traded_time = -1
                last_update_time = -1
                level = int(row['level'])
                local_time = current_time
                long_short = 0
                match_condition = -1
                note = ''
                open_close = int(row['bidOpenClose'])
                order_id = str(row['bidOrderID'])
                
                order_price = float(row['bidOrderPrice'])
                
                portfolio_id = int(row['portfolioID'])
                stratege_id = int(row['strategyID'])
                traded_price = float(row['bidOrderPrice'])
                volume_origin_total = int(row['bidVolumeOriginalTotal'])
                volume_total = int(row['bidVolumeTotal'])
                volume_traded = int(row['bidVolumeTraded'])
                order_status = 2 if volume_traded == 0 else 3 if volume_traded > 0 and volume_total > 0 else 4
                   
                body = {
                        "measurement": measurement, 
                        "time": current_time, 
                        "tags": {
                            "insid": insid
                        }, 
                        "fields": {
                            "base_price": base_price, 
                            "comments": comments,                 
                            "error_id": error_id,                 
                            "error_message": error_message,                 
                            "internal_order_id": internal_order_id,                 
                            "last_traded_time": last_traded_time,                 
                            "last_update_time": last_update_time,                    
                            "local_time": local_time,                    
                            "long_short": long_short,                    
                            "match_condition": match_condition,                    
                            "note": note, 
                            "open_close": open_close,                 
                            "order_id": order_id,                 
                            "order_price": order_price,                 
                            "order_status": order_status,                 
                            "portfolio_id": portfolio_id,                 
                            "stratege_id": stratege_id,                    
                            "traded_price": traded_price,                    
                            "volume_origin_total": volume_origin_total,                    
                            "volume_total": volume_total, 
                            "volume_traded": volume_traded               
                        }, 
                    }
                                
                json_body2.append(body)    
    
    for row in df_quote_summary:
        current_time = int(datetime.datetime.strptime(str(row['SystemStamp']),'%Y%m%d%H%M%S%f').timestamp()*10**9)
        
        measurement = '{}_future_quote'.format(exchange)
        
        ask_error_id = int(-1)
        ask_open_close = int(row['askOpenClose'])
        ask_order_price = float(row['askOrderPrice'])
        
        ask_volume_original_total = int(row['askVolumeOriginalTotal'])
        ask_volume_total = int(row['askVolumeTotal'])
        ask_volume_traded = int(row['askVolumeTraded'])
        base_ask_price = float(row['baseAskPrice'])
        base_bid_price = float(row['baseBidPrice'])
        bid_error_id = int(-1)
        bid_open_close = int(row['bidOpenClose'])
        bid_order_price = float(row['bidOrderPrice'])
        
        bid_volume_original_total = int(row['bidVolumeOriginalTotal'])
        bid_volume_total = int(row['bidVolumeTotal'])
        bid_volume_traded = int(row['bidVolumeTraded'])    
        cancel_time = int(-1)
        error_id = int(-1)
        error_message = str(row['errorMessage'])
        insert_time = int(-1)
        insid = str(row['instrumentId'])
        internal_quote_id = int(row['internalQuoteId'])
        last_traded_time = int(-1)
        level = int(row['level'])
        local_time = int(current_time)
        note = str('')
        portfolio_id = int(row['portfolioID'])
        prevInternal_quote_id = int(row['internalQuoteId'])
        quote_id = str(row['quoteID'])
        quote_request_id = str(row['quoteRequestId'])
        quote_status = int(row['quoteStatus'])
        strategy_id = int(row['strategyID'])
        
        ask_order_status = int(row['askOrderStatus']) if ask_volume_traded == 0 else 3 if ask_volume_traded != ask_volume_original_total else 4
        
        bid_order_status = int(row['bidOrderStatus']) if bid_volume_traded == 0 else 3 if bid_volume_traded != bid_volume_original_total else 4
           
        body = {
                "measurement": measurement, 
                "time": current_time, 
                "tags": {
                    "insid": insid
                }, 
                "fields": {
                    "ask_error_id": ask_error_id, 
                    "ask_open_close": ask_open_close,                 
                    "ask_order_price": ask_order_price,                 
                    "ask_order_status": ask_order_status,                 
                    "ask_volume_original_total": ask_volume_original_total,                 
                    "ask_volume_total": ask_volume_total,                 
                    "ask_volume_traded": ask_volume_traded,                    
                    "base_ask_price": base_ask_price,                    
                    "base_bid_price": base_bid_price,                    
                    "bid_error_id": bid_error_id,                    
                    "bid_open_close": bid_open_close, 
                    "bid_order_price": bid_order_price,                 
                    "bid_order_status": bid_order_status,                               
                    "bid_volume_original_total": bid_volume_original_total,                 
                    "bid_volume_total": bid_volume_total,                    
                    "bid_volume_traded": bid_volume_traded,                    
                    "cancel_time": cancel_time,                    
                    "error_id": error_id, 
                    "error_message": error_message,
                    "insert_time": insert_time,
                    "internal_quote_id": internal_quote_id,
                    "last_traded_time": last_traded_time,
                    "level": level,
                    "local_time": local_time,
                    "note": note,
                    "portfolio_id": portfolio_id,
                    "prevInternal_quote_id": prevInternal_quote_id,
                    "quote_id": quote_id,
                    "quote_request_id": quote_request_id,
                    "quote_status": quote_status,
                    "strategy_id": strategy_id               
                }, 
            }
        
        
        json_body3.append(body)        
    
    print('转格式时间:', time.time()-t)
    
    t = time.time()    
    res = client.write_points(json_body1[:], batch_size = 10000)
    print('order1写入时间:', time.time()-t, '数据量:', len(df_order_summary1))
    
    t = time.time()    
    res = client.write_points(json_body2[:], batch_size = 10000)
    print('order2写入时间:', time.time()-t, '数据量:', len(df_order_summary2))
    
    t = time.time()    
    res = client.write_points(json_body3[:], batch_size = 10000)
    print('quote写入时间:', time.time()-t, '数据量:', len(df_quote_summary))
 
    
  
    
