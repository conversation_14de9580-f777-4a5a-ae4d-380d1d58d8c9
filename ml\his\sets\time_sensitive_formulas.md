# 订单簿时间敏感特征计算公式

本文档列出了从订单簿和订单流数据中提取的时间敏感特征计算公式。这些特征主要关注订单到达率、价格变动等随时间变化的指标。

## 基本概念

时间敏感特征是指那些依赖于订单时间序列的特征，通常在一个固定的时间窗口内计算，例如过去1秒、10秒或1分钟内的数据。这些特征可以捕捉市场微观结构和短期价格动态。

### 订单消息 (OpenBookMsg)

订单消息是从交易所接收到的单个订单信息，包含以下属性：
- 交易代码 (symbol)
- 价格 (price)
- 数量 (size)
- 买卖方向 (side) - 买入(Buy)或卖出(Sell)
- 时间戳 (sourceTime) - 订单发出的时间（毫秒）

### 时间窗口 (Time Window)

时间窗口定义了计算特征的时间范围，通常使用滑动窗口机制，只考虑最近N毫秒（或秒）内的订单。

## 时间敏感特征公式

### 1. 买单到达率 (Bid Arrival Rate)

买单到达率表示单位时间内买单的到达频率：

$$\text{BidArrivalRate} = \frac{\text{Count of Buy Orders}}{\text{Time Window Duration}}$$

其中：
- Count of Buy Orders 是时间窗口内买单的数量
- Time Window Duration 是时间窗口的长度（毫秒）

### 2. 卖单到达率 (Ask Arrival Rate)

卖单到达率表示单位时间内卖单的到达频率：

$$\text{AskArrivalRate} = \frac{\text{Count of Sell Orders}}{\text{Time Window Duration}}$$

### 3. 订单强度 (Order Intensity)

订单强度表示单位时间内所有订单（买单和卖单）的到达频率：

$$\text{OrderIntensity} = \frac{\text{Total Orders Count}}{\text{Time Window Duration}}$$

### 4. 买卖比率 (Buy-Sell Ratio)

买卖比率表示时间窗口内买单与卖单数量的比值：

$$\text{BuySellRatio} = \frac{\text{Count of Buy Orders}}{\text{Count of Sell Orders}}$$

### 5. 价格影响 (Price Impact)

价格影响衡量时间窗口内价格的相对变化：

$$\text{PriceImpact} = \frac{\text{Last Price} - \text{First Price}}{\text{First Price}}$$

其中：
- First Price 是时间窗口内第一个订单的价格
- Last Price 是时间窗口内最后一个订单的价格

### 6. 时间加权平均价格 (Time-Weighted Average Price, TWAP)

时间加权平均价格根据每个价格水平持续的时间长度给予权重：

$$\text{TWAP} = \frac{\sum_{i=1}^{n-1} \text{Price}_i \times (\text{Time}_{i+1} - \text{Time}_i)}{\sum_{i=1}^{n-1} (\text{Time}_{i+1} - \text{Time}_i)}$$

其中：
- Price_i 是第i个订单的价格
- Time_i 是第i个订单的时间戳
- n 是时间窗口内订单的总数

### 7. 订单不平衡比率 (Order Imbalance Ratio)

订单不平衡比率衡量买单和卖单数量的不平衡程度：

$$\text{OrderImbalanceRatio} = \frac{\text{Count of Buy Orders} - \text{Count of Sell Orders}}{\text{Total Orders Count}}$$

取值范围为[-1, 1]，正值表示买方压力大于卖方，负值表示卖方压力大于买方。

### 8. 价格波动率 (Price Volatility)

价格波动率衡量时间窗口内价格的标准差：

$$\text{PriceVolatility} = \sqrt{\frac{1}{n} \sum_{i=1}^{n} (\text{Price}_i - \overline{\text{Price}})^2}$$

其中：
- Price_i 是第i个订单的价格
- $\overline{\text{Price}}$ 是时间窗口内所有订单价格的平均值
- n 是时间窗口内订单的总数

## 公式应用说明

1. 时间窗口通常是滑动的，新订单到达时，超出窗口时间范围的旧订单会被移除
2. 对于所有特征计算，如果时间窗口中没有订单或只有一个订单，结果通常为NA（不可用）
3. 对于分母可能为零的计算（如买卖比率），需要特殊处理以避免除零错误
4. 这些时间敏感特征往往结合订单簿的静态特征（价格差异、深度等）一起使用，以提供更全面的市场微观结构视图

## 与机器学习模型结合

这些时间敏感特征可以作为机器学习模型的输入，用于：
1. 预测短期价格走势
2. 估计市场冲击成本
3. 识别异常交易行为
4. 优化交易策略执行

各种时间敏感特征可以在不同时间尺度上计算（如1秒、10秒、1分钟等），以捕捉不同的市场动态。 