def generate_multi_level_features(self, max_level=10):
    """生成多层次订单簿特征

    根据用户请求实现以下特征集：
    v1: 基础价格和交易量 (每层价格和交易量)
    v2: 价差和中间价 (每层价差和中间价)
    v3: 价格差异 (相邻层次间的价格差异)
    v4: 平均价格和交易量 (所有层次的平均值)
    v5: 累积差异 (所有层次的累积差异)
    v6: 价格和交易量导数 (时间导数)
    v7: 平均强度 (每种类型的平均强度)
    v8: 相对强度指标 (相对强度指标)
    v9: 加速度 (市场/限价单加速度)
    """
    df = self.data.copy()
    print("开始生成多层次订单簿特征...")

    # 确定实际可用的最大层次
    available_levels = 1
    for i in range(2, max_level + 1):
        if f'AskPrice{i}' in df.columns and f'BidPrice{i}' in df.columns:
            available_levels = i
        else:
            break

    print(f"检测到可用订单簿层次: {available_levels}")

    # v1: 基础价格和交易量 - 已经在原始数据中

    # v2: 价差和中间价
    for i in range(1, available_levels + 1):
        # 每层的价差
        df[f'spread_{i}'] = df[f'AskPrice{i}'] - df[f'BidPrice{i}']
        # 每层的中间价
        df[f'mid_{i}'] = (df[f'AskPrice{i}'] + df[f'BidPrice{i}']) / 2

    # v3: 价格差异
    # 最高卖价与最低卖价的差异
    if available_levels > 1:
        df['ask_price_range'] = df[f'AskPrice{available_levels}'] - df['AskPrice1']
        # 最高买价与最低买价的差异
        df['bid_price_range'] = df['BidPrice1'] - df[f'BidPrice{available_levels}']

        # 相邻层次间的价格差异
        for i in range(1, available_levels):
            df[f'ask_price_diff_{i}'] = df[f'AskPrice{i + 1}'] - df[f'AskPrice{i}']
            df[f'bid_price_diff_{i}'] = df[f'BidPrice{i}'] - df[f'BidPrice{i + 1}']

    # v4: 平均价格和交易量
    ask_price_cols = [f'AskPrice{i}' for i in range(1, available_levels + 1) if f'AskPrice{i}' in df.columns]
    bid_price_cols = [f'BidPrice{i}' for i in range(1, available_levels + 1) if f'BidPrice{i}' in df.columns]
    ask_vol_cols = [f'AskVol{i}' for i in range(1, available_levels + 1) if f'AskVol{i}' in df.columns]
    bid_vol_cols = [f'BidVol{i}' for i in range(1, available_levels + 1) if f'BidVol{i}' in df.columns]

    if ask_price_cols:
        df['mean_ask_price'] = df[ask_price_cols].mean(axis=1)
    if bid_price_cols:
        df['mean_bid_price'] = df[bid_price_cols].mean(axis=1)
    if ask_vol_cols:
        df['mean_ask_vol'] = df[ask_vol_cols].mean(axis=1)
    if bid_vol_cols:
        df['mean_bid_vol'] = df[bid_vol_cols].mean(axis=1)

    # v5: 累积差异
    if ask_price_cols and bid_price_cols:
        # 所有层次的价格累积差异
        df['cum_price_diff'] = df[ask_price_cols].sum(axis=1) - df[bid_price_cols].sum(axis=1)

    if ask_vol_cols and bid_vol_cols:
        # 所有层次的交易量累积差异
        df['cum_vol_diff'] = df[bid_vol_cols].sum(axis=1) - df[ask_vol_cols].sum(axis=1)
        # 累积交易量不平衡
        total_vol = df[bid_vol_cols].sum(axis=1) + df[ask_vol_cols].sum(axis=1)
        df['cum_vol_imbalance'] = df['cum_vol_diff'] / total_vol

    # v6: 价格和交易量导数 (时间导数)
    # 使用差分近似导数
    for i in range(1, available_levels + 1):
        if f'AskPrice{i}' in df.columns:
            df[f'dAskPrice{i}_dt'] = df[f'AskPrice{i}'].diff()
        if f'BidPrice{i}' in df.columns:
            df[f'dBidPrice{i}_dt'] = df[f'BidPrice{i}'].diff()
        if f'AskVol{i}' in df.columns:
            df[f'dAskVol{i}_dt'] = df[f'AskVol{i}'].diff()
        if f'BidVol{i}' in df.columns:
            df[f'dBidVol{i}_dt'] = df[f'BidVol{i}'].diff()

    # v7 & v8 & v9: 交易强度指标
    # 这些特征需要更复杂的计算，我们使用滚动窗口来近似
    windows = [5, 10, 20]

    # 计算交易强度 (v7)
    for window in windows:
        # 买入限价单强度
        if 'BidVol1' in df.columns:
            df[f'bid_intensity_{window}'] = df['BidVol1'].rolling(window=window).mean()

        # 卖出限价单强度
        if 'AskVol1' in df.columns:
            df[f'ask_intensity_{window}'] = df['AskVol1'].rolling(window=window).mean()

        # 市场买单强度 (使用成交量作为代理)
        if 'tradedVol' in df.columns:
            df[f'market_intensity_{window}'] = df['tradedVol'].rolling(window=window).mean()

    # 相对强度指标 (v8)
    for window in windows:
        # 相对于历史平均的买入强度
        if f'bid_intensity_{window}' in df.columns:
            long_window = window * 3
            df[f'rel_bid_intensity_{window}'] = df[f'bid_intensity_{window}'] / df['BidVol1'].rolling(
                window=long_window).mean()

        # 相对于历史平均的卖出强度
        if f'ask_intensity_{window}' in df.columns:
            long_window = window * 3
            df[f'rel_ask_intensity_{window}'] = df[f'ask_intensity_{window}'] / df['AskVol1'].rolling(
                window=long_window).mean()

        # 相对于历史平均的市场强度
        if f'market_intensity_{window}' in df.columns and 'tradedVol' in df.columns:
            long_window = window * 3
            df[f'rel_market_intensity_{window}'] = df[f'market_intensity_{window}'] / df['tradedVol'].rolling(
                window=long_window).mean()

    # 加速度 (v9) - 使用二阶差分近似
    for window in windows:
        # 买入限价单加速度
        if f'bid_intensity_{window}' in df.columns:
            df[f'bid_acceleration_{window}'] = df[f'bid_intensity_{window}'].diff()

        # 卖出限价单加速度
        if f'ask_intensity_{window}' in df.columns:
            df[f'ask_acceleration_{window}'] = df[f'ask_intensity_{window}'].diff()

        # 市场单加速度
        if f'market_intensity_{window}' in df.columns:
            df[f'market_acceleration_{window}'] = df[f'market_intensity_{window}'].diff()

    print(f"多层次订单簿特征生成完成，新增特征数: {len(df.columns) - len(self.data.columns)}")
    return df
