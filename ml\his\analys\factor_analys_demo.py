"""
因子分析系统demo
@author: lining
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any

class FactorAnalysisSystem:
    """因子分析系统"""

    def __init__(self):
        self.analysis_results = {}
        self.test_results = {}
        self.shap_values = {}
        self.output_dir = 'output'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def analyze_factor(self, data: pd.Series) -> Dict:
        """分析单个因子的统计特性"""
        # 确保数据为数值类型
        if not pd.api.types.is_numeric_dtype(data):
            print(data.name, data.dtype,data.shape,data.head(5))
            raise ValueError("数据类型错误，必顨为数值类型")
        analysis = {
            'basic_stats': {
                'mean': data.mean(),
                'std': data.std(),
                'min': data.min(),
                'max': data.max(),
                'skew': data.skew(),
                'kurtosis': data.kurtosis()
            },
            'missing_stats': {
                'total': data.isna().sum(),
                'percentage': data.isna().mean() * 100
            },
            'unique_values': data.nunique()
        }
        
        print(f"因子 {data.name} 分析完成")
        for key, value in analysis.items():
            if isinstance(value, float):
                print(f"{key}: {round(value,4)}")
            elif isinstance(value, dict):
                for k, v in value.items():
                    print(f"{key}.{k}: {round(v,4)}")
            else:
                print(f"{key}: {value}")
        
        return analysis
    
    def analyze_factor_set(self, data: pd.DataFrame, factors: List[str]) -> Dict:
        """分析因子集合"""
        results = {}
        for factor in factors:
            if factor in data.columns:
                try:
                    results[factor] = self.analyze_factor(data[factor])
                except Exception as e:
                    print(f"因子 {factor} 分析失败: {e}")
        self.analysis_results = results
        return results

    def calculate_ic(self, factor: pd.Series, returns: pd.Series) -> float:
        """计算信息系数(IC)"""
        return factor.corr(returns)
    
    def calculate_rank_ic(self, factor: pd.Series, returns: pd.Series) -> float:
        """计算秩相关系数(Rank IC)"""
        return factor.rank().corr(returns.rank())

    def calculate_ic_series(self, factor: pd.Series, returns: pd.Series, window: int = 20) -> pd.Series:
        """计算滚动信息系数"""
        return factor.rolling(window=window).corr(returns)

    def calculate_ir(self, factor: pd.Series, returns: pd.Series, window: int = 200) -> float:
        """计算信息比率(IR)"""
        ic_series = []
        for i in range(window, len(factor), window):
            window_factor = factor.iloc[i - window:i]
            window_returns = returns.iloc[i - window:i]
            if len(window_factor) > 1 and window_factor.std() != 0 and window_returns.std() != 0:
                window_ic = window_factor.corr(window_returns)
                ic_series.append(window_ic)

        ic_series = pd.Series(ic_series).dropna()
        if len(ic_series) > 0 and ic_series.std() != 0:
            return ic_series.mean() / ic_series.std()
        return 0


    def test_factors(self, data: pd.DataFrame, 
                     feature_cols: List[str], target_cols: List[str], 
                     ) -> pd.DataFrame:
        """
        进行因子测试
        
        参数:
            data: 数据集
            feature_cols: 特征列列表
            target_cols: 目标列列表
            model: 训练好的模型，用于SHAP分析
            enable_shap: 是否进行SHAP分析
        """
        print("开始因子测试...")

        # 创建输出目录
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 初始化结果存储
        ic_values = {}
        ir_values = {}
        rank_ic_values = {}


        # 计算每个因子的指标
        target = target_cols[0]
        
        for feature in feature_cols:
            if feature in data.columns:
                feature_series = data[feature]
                target_series = data[target]
                
                # 检查数据有效性
                if feature_series.std() != 0 and target_series.std() != 0:
                    # 计算基本指标
                    ic_values[feature] = self.calculate_ic(feature_series, target_series)
                    rank_ic_values[feature] = self.calculate_rank_ic(feature_series, target_series)
                    ir_values[feature] = self.calculate_ir(feature_series, target_series)
                else:
                    # 数据无效时设置为0
                    ic_values[feature] = 0
                    rank_ic_values[feature] = 0
                    ir_values[feature] = 0
                
                print(f"因子 {feature} 的IC值: {round(ic_values[feature],4)}")
                print(f"因子 {feature} 的Rank IC值: {round(rank_ic_values[feature],4)}")
                print(f"因子 {feature} 的IR值: {round(ir_values[feature],4)}")

        # 创建结果DataFrame
        factor_test_results = pd.DataFrame({
            'feature': feature_cols,
            'ic': [ic_values.get(feature, 0) for feature in feature_cols],
            'abs_ic': [abs(ic_values.get(feature, 0)) for feature in feature_cols],
            'rank_ic': [rank_ic_values.get(feature, 0) for feature in feature_cols],
            'ir': [ir_values.get(feature, 0) for feature in feature_cols],
        })
        self.test_results = factor_test_results.sort_values('abs_ic', ascending=False)

        # 输出统计信息
        self._print_statistics(self.test_results)

        return self.test_results

    def _print_statistics(self, results: pd.DataFrame):
        """输出统计信息"""
        print("\n=== 因子检验结果 ===")
        print(f"因子检验完成，共检验 {len(results)} 个因子")
        print(f"平均IC值: {results['ic'].mean():.4f}")
        print(f"平均Rank IC值: {results['rank_ic'].mean():.4f}")
        print(f"平均IR值: {results['ir'].mean():.4f}")

        good_factors = len(results[results['abs_ic'] > 0.02])
        excellent_factors = len(results[results['abs_ic'] > 0.05])
        outstanding_factors = len(results[results['abs_ic'] > 0.10])

        print("\n=== 因子质量分布 ===")
        print(f"良好因子数量 (|IC| > 0.02): {good_factors}")
        print(f"优秀因子数量 (|IC| > 0.05): {excellent_factors}")
        print(f"极好因子数量 (|IC| > 0.10): {outstanding_factors}")

        print("\n=== Top 5因子 ===")
        print(f"因子数量: {len(results)}")
    
# 创建全局因子分析系统实例
factor_analysis_system = FactorAnalysisSystem()

def main():
    # 读取数据
    data = pd.read_parquet('analys\md_20250102_cffex_multicast.parquet')
    # 预处理数据
    feature_cols = ['factor1','factor2']
    target_cols = ['target1',]
    data = preprocess_data(data,feature_cols,target_cols)
    # 删除缺失值
    data = data.dropna()
    # 因子分析
    factor_analysis_system.analyze_factor_set(data, feature_cols)
    # 因子测试
    factor_analysis_system.test_factors(data, feature_cols, target_cols)

if __name__ == "__main__":

    def preprocess_data(data: pd.DataFrame,feature_cols: List[str],target_cols: List[str]) -> pd.DataFrame:
        """预处理数据"""
        # 删除全部都是缺失值的列
        data = data.dropna(axis=1, how='all')

        data['mid_price'] = (data['BidPrice1'] + data['AskPrice1']) / 2

        # 计算因子指标
        data['factor1'] = calculate_factor1(data)
        data['factor2'] = calculate_factor2(data)

        # 计算target
        data['target1'] = calculate_target(data)

        return data

    def calculate_target(data: pd.DataFrame) -> pd.Series:
        """计算target"""
        data['target1'] = data['mid_price'].shift(-10) / data['mid_price'] - 1
        return data['target1']

    def calculate_factor1(data: pd.DataFrame) -> pd.Series:
        """计算因子"""
        return data['mid_price'].diff()

    def calculate_factor2(data: pd.DataFrame) -> pd.Series:
        """计算因子"""
        return data['mid_price'].diff().diff()
    
    main()
