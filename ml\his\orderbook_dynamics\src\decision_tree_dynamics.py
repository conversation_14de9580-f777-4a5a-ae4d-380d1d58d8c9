"""
Decision tree dynamics implementation for orderbook dynamics.
This module provides functionality for training decision tree models on order book data.
"""
import os
import argparse
from typing import Dict, List, Optional, Tuple
from collections import Counter
import numpy as np
from sklearn.tree import DecisionTreeClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import logging
import sys

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..')))

from orderbook_dynamics.src.models import OpenBookMsg
from orderbook_dynamics.src.open_book import OpenBook, OpenBookFile
from orderbook_dynamics.src.features_extractor import FeaturesExtractor
from orderbook_dynamics.src.attribute.label import MeanPriceMove, MeanPriceMoveEncode
from orderbook_dynamics.src.attribute.labeled_points_extractor import LabeledPoint, LabeledOrderLog


logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DecisionTreeDynamics:
    """
    Decision tree based dynamics for order book prediction.
    """
    def __init__(self, max_depth: int = 5, max_level: int = 5, time_window_ms: int = 1000):
        """
        Initialize a decision tree dynamics model.
        
        Args:
            max_depth: Maximum depth of the decision tree
            max_level: Maximum order book level to consider
            time_window_ms: Time window in milliseconds
        """
        self.max_depth = max_depth
        self.max_level = max_level
        self.time_window_ms = time_window_ms
        self.features_extractor = FeaturesExtractor(max_level, time_window_ms)
        self.label_encode = MeanPriceMoveEncode()
        
    def _extract_labeled_data(self, symbol: str, orders: List[OpenBookMsg]) -> List[LabeledPoint]:
        """
        Extract labeled data from order messages.
        
        Args:
            symbol: Symbol of the orders
            orders: List of order messages
            
        Returns:
            List of labeled points
        """
        extractor = self.features_extractor.create_features_extractor(symbol)
        return extractor.labeled_points(orders)
        
    def train(self, training_files: List[OpenBookFile], validation_files: List[OpenBookFile], 
              filter_prefix: Optional[str] = None, filter_symbol: Optional[str] = None) -> Dict[str, DecisionTreeClassifier]:
        """
        Train decision tree models on order book data.
        
        Args:
            training_files: List of training files
            validation_files: List of validation files
            filter_prefix: Filter files by prefix (e.g., 'AA')
            filter_symbol: Filter by symbol
            
        Returns:
            Dictionary mapping symbols to trained models
        """
        # Filter files if requested
        if filter_prefix:
            training_files = [f for f in training_files if f.source == filter_prefix]
            validation_files = [f for f in validation_files if f.source == filter_prefix]
            
        logger.info(f"Training on {len(training_files)} files, validating on {len(validation_files)} files")
        
        # Load training data
        training_orders = OpenBook.order_log(training_files)
        logger.info(f"Training order log size: {len(training_orders)}")
        
        # Group by symbol
        symbols = set(order.symbol for order in training_orders)
        if filter_symbol:
            symbols = {filter_symbol}
            
        # Train models for each symbol
        models = {}
        
        for symbol in symbols:
            logger.info(f"Training model for symbol: {symbol}")
            
            # Filter orders for this symbol
            symbol_orders = [order for order in training_orders if order.symbol == symbol]
            
            # Extract labeled data
            labeled_points = self._extract_labeled_data(symbol, symbol_orders)
            if not labeled_points:
                logger.warning(f"No labeled points extracted for symbol: {symbol}")
                continue
                
            # Create arrays for scikit-learn
            X = np.array([lp.features for lp in labeled_points])
            y = np.array([lp.label for lp in labeled_points])
            
            # Count labels
            label_counts = Counter(y)
            logger.info(f"Label counts for {symbol}: {label_counts}")
            
            # Train decision tree
            model = DecisionTreeClassifier(
                max_depth=self.max_depth,
                criterion='gini',
                random_state=42
            )
            
            model.fit(X, y)
            models[symbol] = model
            
        # Validate models
        validation_orders = OpenBook.order_log(validation_files)
        logger.info(f"Validation order log size: {len(validation_orders)}")
        
        for symbol, model in models.items():
            logger.info(f"Validating model for symbol: {symbol}")
            
            # Filter orders for this symbol
            symbol_orders = [order for order in validation_orders if order.symbol == symbol]
            
            # Extract labeled data
            labeled_points = self._extract_labeled_data(symbol, symbol_orders)
            if not labeled_points:
                logger.warning(f"No labeled points extracted for symbol: {symbol}")
                continue
                
            # Create arrays for scikit-learn
            X_val = np.array([lp.features for lp in labeled_points])
            y_val = np.array([lp.label for lp in labeled_points])
            
            # Evaluate model
            y_pred = model.predict(X_val)
            accuracy = accuracy_score(y_val, y_pred)
            logger.info(f"Accuracy for {symbol}: {accuracy:.4f}")
            
            # Print classification report
            report = classification_report(y_val, y_pred, target_names=[m.name for m in MeanPriceMove])
            logger.info(f"Classification report for {symbol}:\n{report}")
            
        return models


def main():
    """Main entry point for the decision tree dynamics command-line tool."""
    parser = argparse.ArgumentParser(description="Order Book Dynamics with Decision Trees")
    parser.add_argument('-t', '--training', required=True, help="Path to training data directory")
    parser.add_argument('-v', '--validation', required=True, help="Path to validation data directory")
    parser.add_argument('-f', '--filter', help="Filter by prefix (e.g., 'AA')")
    parser.add_argument('-s', '--symbol', help="Filter by symbol")
    parser.add_argument('-d', '--max-depth', type=int, default=5, help="Maximum decision tree depth")
    parser.add_argument('-l', '--max-level', type=int, default=5, help="Maximum order book level")
    parser.add_argument('-w', '--time-window', type=int, default=1000, help="Time window in milliseconds")
    
    args = parser.parse_args()

    args.training = os.path.abspath(args.training)
    args.validation = os.path.abspath(args.validation)
    
    # Check that directories exist
    for path in [args.training, args.validation]:
        if not os.path.isdir(path):
            parser.error(f"Directory not found: {path}")
            
    # Load files
    training_files = OpenBook.open_book_files(args.training)
    validation_files = OpenBook.open_book_files(args.validation)
    
    # Train models
    dynamics = DecisionTreeDynamics(args.max_depth, args.max_level, args.time_window)
    models = dynamics.train(training_files, validation_files, args.filter, args.symbol)
    
    logger.info(f"Trained {len(models)} models")


if __name__ == "__main__":
    main()