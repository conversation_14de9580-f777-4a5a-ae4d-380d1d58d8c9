#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()
ip.set_ip('DCE.dev')
""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所生产机    
"""
#%%
"""排队撮合"""
import vnpy.app.my_tick_strategy.backtesting,strategies.tick_resilience
from vnpy.app.my_tick_strategy.base import BacktestingMode
from vnpy.app.my_tick_strategy.backtesting import BacktestingEngine, OptimizationSetting
from strategies.tick_resilience import TickStrategy
from datetime import datetime
from vnpy.trader.constant import Interval

#%%
engine = BacktestingEngine()
engine.set_parameters(
    vt_symbol="m2203.DCE", # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔
    start=datetime(2021,8, 5, 9, 0), # 开始时间
    end=datetime(2021, 8, 5, 15, 0), # 结束时间
    rate=0, # 手续费率
    slippage=0, # 滑点
    size=10, # 合约规模
    pricetick=1, # 一个tick大小
    capital=1_000_000, # 初始资金
    mode=BacktestingMode.TICK # 回测模式
)
# 添加回测策略，并修改内部参数
engine.add_strategy(TickStrategy, {'fixed_size':10})

#%%
engine.load_data() # 加载历史数据
print('start: ', engine.history_data[0].datetime)
print('end: ', engine.history_data[-1].datetime)
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
# engine.show_chart() # 显示图表
engine.duty_statistics() #显示做市义务

# %%
import pandas as pd
orders = pd.DataFrame(engine.limit_orders.values()) #输出order
trades = pd.DataFrame(engine.trades.values()) #输出trades