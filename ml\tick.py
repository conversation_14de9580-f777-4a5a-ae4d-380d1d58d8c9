'''backtest
start: 2020-04-22 13:30:00
end: 2020-04-22 15:00:00
period: 1h
basePeriod: 1h
exchanges: [{"eid":"Futures_CTP","currency":"FUTURES"}]
mode: 1
'''

class FuturesTradeFilter():
    type_enum = {
        "OPENLONG":"多开|OpenLong",
        "OPENSHORT":"空开|OpenShort",
        "OPENDOUBLE":"双开|OpenDouble",
        "CLOSELONG":"多平|CloseLong",
        "CLOSESHORT":"空平|CloseShort",
        "CLOSEDOUBLE":"双平|CloseDouble",
        "EXCHANGELONG":"多换|ExchangeLong",
        "EXCHANGESHORT":"空换|ExchangeShort",
        "OPENUNKOWN":"开仓|OpenUnkown",
        "CLOSEUNKOWN":"平仓|CloseUnkown",
        "EXCHANGEUNKOWN":"换仓|ExchangeUnkown",
        "UNKOWN":"未知|Unkown",
        "NOCHANGE":"空闲|NoChange"
    }

    color_enum = {
        "RED":"#0000ff", "GREEN":"#ff0000", "WHITE":"#666666"
    }

    tick_dict = {
        "delta_enum_NONE": {
            "forward_enum_UP": [type_enum["NOCHANGE"], color_enum["WHITE"]],
            "forward_enum_DOWN": [type_enum["NOCHANGE"], color_enum["WHITE"]],
            "forward_enum_MIDDLE": [type_enum["NOCHANGE"], color_enum["WHITE"]]
        },
        "delta_enum_EXCHANGE": {
            "forward_enum_UP": [type_enum["EXCHANGELONG"], color_enum["RED"]],
            "forward_enum_DOWN": [type_enum["EXCHANGESHORT"], color_enum["GREEN"]],
            "forward_enum_MIDDLE": [type_enum["EXCHANGEUNKOWN"], color_enum["WHITE"]]
        },
        "delta_enum_OPENFWDOUBLE": {
            "forward_enum_UP": [type_enum["OPENDOUBLE"], color_enum["RED"]],
            "forward_enum_DOWN": [type_enum["OPENDOUBLE"], color_enum["GREEN"]],
            "forward_enum_MIDDLE": [type_enum["OPENDOUBLE"], color_enum["WHITE"]]
        },
        "delta_enum_OPEN": {
            "forward_enum_UP": [type_enum["OPENLONG"], color_enum["RED"]],
            "forward_enum_DOWN": [type_enum["OPENSHORT"], color_enum["GREEN"]],
            "forward_enum_MIDDLE": [type_enum["OPENUNKOWN"], color_enum["WHITE"]]
        },
        "delta_enum_CLOSEFWDOUBLE": {
            "forward_enum_UP": [type_enum["CLOSEDOUBLE"], color_enum["RED"]],
            "forward_enum_DOWN": [type_enum["CLOSEDOUBLE"], color_enum["GREEN"]],
            "forward_enum_MIDDLE": [type_enum["CLOSEDOUBLE"], color_enum["WHITE"]]
        },
        "delta_enum_CLOSE": {
            "forward_enum_UP": [type_enum["CLOSESHORT"], color_enum["RED"]],
            "forward_enum_DOWN": [type_enum["CLOSELONG"], color_enum["GREEN"]],
            "forward_enum_MIDDLE": [type_enum["CLOSEUNKOWN"], color_enum["WHITE"]]
        },
    }

    def __init__(self):
        self.preInfo = None 

    def feed(self, info):
        # 如果没有前一条tick信息，保存当前tick并返回None
        if not self.preInfo:
            self.preInfo = info
            return None 

        # 计算成交量和持仓量的变化
        volume_delta = info["Volume"] - self.preInfo["Volume"]
        open_interest_delta = info["OpenInterest"] - self.preInfo["OpenInterest"]

        # 默认方向为未知
        delta_forward = "delta_enum_UNKOWN"

        # 根据成交量和持仓量的变化判断成交类型
        if open_interest_delta == 0 and volume_delta == 0:
            # 无成交和持仓变化，空闲
            delta_forward = "delta_enum_NONE"
        elif open_interest_delta == 0 and volume_delta > 0:
            # 仅有成交量增加，换手
            delta_forward = "delta_enum_EXCHANGE"
        elif open_interest_delta > 0:
            # 持仓量增加，开仓
            if open_interest_delta - volume_delta == 0:
                # 开双，开仓量等于成交量
                delta_forward = "delta_enum_OPENFWDOUBLE"
            else:
                # 普通开仓
                delta_forward = "delta_enum_OPEN"
        elif open_interest_delta < 0:
            # 持仓量减少，平仓
            if open_interest_delta + volume_delta == 0:
                # 双平，平仓量等于成交量
                delta_forward = "delta_enum_CLOSEFWDOUBLE"
            else:
                # 普通平仓
                delta_forward = "delta_enum_CLOSE"

        # 获取当前成交类型对应的字典
        obj = FuturesTradeFilter.tick_dict[delta_forward]
        ret = None

        # 如果成交类型在tick_dict中，进一步判断方向
        if delta_forward in FuturesTradeFilter.tick_dict:
            order_forward = ""
            # 先用上一笔的盘口判断方向
            if info["Last"] >= self.preInfo["Sell"]:
                order_forward = "forward_enum_UP"
            elif info["Last"] <= self.preInfo["Buy"]:
                order_forward = "forward_enum_DOWN"
            else:
                # 若不满足，再用当前盘口判断
                if info["Last"] >= info["Sell"]:
                    order_forward = "forward_enum_UP"
                elif info["Last"] <= info["Buy"]:
                    order_forward = "forward_enum_DOWN"
                else:
                    order_forward = "forward_enum_MIDDLE"
            # 如果方向已确定，获取对应的类型和颜色
            if order_forward != "":
                d = obj[order_forward]
                if order_forward in obj:
                    # 返回格式：[最新价, 成交量变化, 类型, 颜色]
                    ret = [info["Last"], volume_delta, d[0], d[1]]
        # 更新前一条tick信息
        self.preInfo = info
        return ret

    def reset(self):
        self.preInfo = None 


def main():
    _C(exchange.SetContractType, "MA888")
    filt = FuturesTradeFilter()
    while True:
        ret = filt.feed(_C(exchange.GetTicker))
        if ret:
            Log("Price:", ret[0], "Amount:", ret[1], ret[2], ret[3])
            