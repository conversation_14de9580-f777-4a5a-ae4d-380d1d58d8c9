# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-29

import pandas as pd
import numpy
import pymssql
from datetime import datetime

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = datetime.now()
# beginDate = "2017-06-24"

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')

sql = "SELECT  datetime,stkid,openprice,high,low,closeprice,volume,amount FROM ZZStks WHERE datetime BETWEEN " \
      "'2015-1-29' AND '2015-6-29' "

pf = pd.read_sql(sql, conn, index_col=[ "stkid"], coerce_float=True, params=None, parse_dates=None, columns=None, chunksize=None)

print(pf)

pf2 = pf.ix['300113.SZ', :]
print(pf2)

conn.commit()

conn.close()
