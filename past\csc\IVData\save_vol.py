# -*- coding: utf-8 -*-
"""
Store the Volatility at the end of the day
@author: zhanghc
"""
import numpy as np
import pandas as pd
import WindPy
from WindPy import *
import os
import scipy
from scipy import interpolate
from datetime import datetime, date
import time
import py_vollib
import py_vollib_vectorized


def add_voldata_wind(df_vol, product, contract, delta_range):
    str_today = str(date.today())
    field = "field=us_code,option_code,strike_price,call_put,last_tradedate"
    option_contract = w.wset("optionchain",
                             "us_code=" + contract + ";" + field,
                             usedf=True)[1]
    if (not option_contract.empty):
        exp_T = w.tdayscount(str_today, str(option_contract['last_tradedate'][0])[:10], "")
        T = (exp_T.Data[0][0] - 1) / 252
        S = w.wsq(contract, "rt_last").Data[0][0]
        #Get OTM Option Strip
        call_strip = option_contract[(option_contract['call_put'] == "认购") & (option_contract['strike_price'] > S)]
        put_strip = option_contract[(option_contract['call_put'] == "认沽") & (option_contract['strike_price'] <= S)]
        option_strip = pd.concat([call_strip, put_strip])
        option_strip = option_strip.sort_values(by=['strike_price'], ascending=True)
        option_strip['call_put'] = option_strip['call_put'].map({"认购": "c", "认沽": 'p'})

        option_strip = option_strip.drop(columns=['last_tradedate', 'us_code'])
        contract_string = option_strip['option_code'].tolist()

        # field_list = "rt_imp_volatility,rt_bid1_ivl,rt_ask1_ivl,rt_delta"
        field_list = "rt_bid1,rt_ask1"
        market_data_df = w.wsq(contract_string, field_list, usedf=True)[1]
        option_strip = option_strip.set_index('option_code')
        df = option_strip.join(market_data_df)
        #remove no bid/offer
        df = df[df['RT_ASK1'] != 0]
        df = df[df['RT_BID1'] != 0]
        # 计算bid/ask IV
        K = df['strike_price']
        r = 0.0
        flag = df['call_put']
        bid_prices = df['RT_BID1']
        ask_prices = df['RT_ASK1']
        df['RT_BID1_IVL'] = py_vollib.black_scholes_merton.implied_volatility.implied_volatility(
            bid_prices, S, K, T, r, flag, q=r, return_as='numpy')
        df['RT_ASK1_IVL'] = py_vollib.black_scholes_merton.implied_volatility.implied_volatility(
            ask_prices, S, K, T, r, flag, q=r, return_as='numpy')
        df = df[abs(df['RT_ASK1_IVL'] - df['RT_BID1_IVL']) <= 0.02]
        if len(df) == 0:
            return None
        df['RT_IMP_VOLATILITY'] = (df['RT_ASK1_IVL'] + df['RT_BID1_IVL']) / 2
        df['RT_DELTA'] = py_vollib.black_scholes.greeks.numerical.delta(df['call_put'], S, df['strike_price'], T, r,
                                                                        df['RT_IMP_VOLATILITY'], return_as='numpy')
        df['RT_IMP_VOLATILITY'] *= 100
        df['RT_DELTA'] *= 100
        df = df[~((df['RT_DELTA'] > -5) & (df['RT_DELTA'] < 5))]
        df = df.filter(['RT_DELTA', 'RT_IMP_VOLATILITY'])

        df.reset_index(drop=True, inplace=True)
        delta_arr = np.array(df['RT_DELTA'].tolist())
        call_d_arr = 100 * (delta_arr < 0) + delta_arr
        vol_arr = np.array(df['RT_IMP_VOLATILITY'].tolist())

        if call_d_arr.size > 1 and vol_arr.size > 1:
            # f = interpolate.interp1d(call_d_arr,vol_arr,kind='linear',fill_value='extrapolate')
            f = interpolate.interp1d(call_d_arr, vol_arr, kind='linear', bounds_error=False)
            target_delta_arr = np.array(delta_range)
            target_c_delta_arr = 100 * (target_delta_arr < 0) + target_delta_arr
            vol_fitted_arr = f(target_c_delta_arr)
            N = np.where(vol_fitted_arr > 0)[0]
            vol_fitted_arr[:N[0]] = vol_fitted_arr[N[0]]
            vol_fitted_arr[N[-1]:] = vol_fitted_arr[N[-1]]
            vol_fitted_list = [str_today, product, contract, S, T] + vol_fitted_arr.tolist()
            df_vol.loc[len(df_vol)] = vol_fitted_list


if __name__ == '__main__':

    str_today = str(date.today())
    w.start()
    #load config file
    mydir = "C:/Users/<USER>/Desktop/IVData"
    os.chdir(mydir)
    config = pd.read_csv("config.csv")
    delta_range = [-5, -10, -15, -20, -25, -30, -35, 50, 35, 30, 25, 20, 15, 10, 5]
    #Initiate Vol Data columns
    df_vol = pd.DataFrame(columns=['Date', 'product', 'contract', 'S', 'T'] + delta_range)
    for i in range(config.shape[0]):
        #主力合约波动率

        contract1 = w.wss(config['Contract1'][i], "trade_hiscode").Data[0][0]
        print(contract1)
        try:
            add_voldata_wind(df_vol, config['Product'][i], contract1, delta_range)
        except:
            print("!")
        #次主力合约波动率

        contract2 = w.wss(config['Contract2'][i], "trade_hiscode").Data[0][0]
        print(contract2)
        try:
            add_voldata_wind(df_vol, config['Product'][i], contract2, delta_range)
        except:
            print("!")
            #save vol
    df_vol.set_index('Date', inplace=True)
    #save in the vol data folder
    df_vol.to_csv(mydir + "/" + "VolData/" + "vol_" + str_today + ".csv")
    print("Vol Saved Successful")

    # x.to_csv('vol.csv')
    # x.head()
    # contract_string = get_option_contract_list(','.join(future_list),'.DCE')
    # field_list = ["rt_bid1,rt_ask1,rt_bsize1,rt_asize1,rt_latest,rt_time,rt_opt_margin"]

    # market_data_df = w.wsq(contract_string,field_list,usedf=True)[1]   

    # print (T)
    # print (option_contract.head())
