from  ..LIODAF.data.features.factor_manager  import factor_manager


import xgboost as xgb
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import matplotlib.pyplot as plt
import datetime as dt
from math import sqrt, log, exp
from scipy import stats
from datetime import date, time, datetime, timedelta
import xlwings as xw
import warnings
from itertools import combinations
import time
from xgboost import plot_importance
import os
import lightgbm as lgb
import nolds
import mlflow
import mlflow.sklearn
from sklearn.metrics import mean_squared_error

start_time = time.time()

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)



def datanew(df, inst, symbol="", n=1, drop=[]):
    # 设置合约乘数和最小变动价位

    # 初始化列
    df['aggbidV'] = 0
    df['aggaskV'] = 0
    df['aggbidnum'] = 0
    df['aggasknum'] = 0
    df['dsA'] = 0
    df['dsB'] = 0
    
    # 时间特征
    df['second'] = df['timestamp_str'].dt.second
    df['minute'] = df['timestamp_str'].dt.minute
    df['second_sin'] = np.sin(2 * np.pi * df['second']/60)
    df['second_cos'] = np.cos(2 * np.pi * df['second']/60)
    df['minute_sin'] = np.sin(2 * np.pi * df['minute']/60)
    df['minute_cos'] = np.cos(2 * np.pi * df['minute']/60)

    # 价格变化
    df['poschg'] = df['AuctionPrice'].diff().fillna(0)

    # 计算中间价格
    for i in range(1, 6):
        df[f'midpx{i}'] = 0.5 * (df[f'AskPrice{i}'] + df[f'BidPrice{i}'])

    # 主循环处理
    for i in range(1, len(df)):
        # 计算深度加权价差
        # 计算卖方深度加权价差 dsA = ∑[(AskPrice_{j+1} - AskPrice_j) / AskVol_j] / ∑[1/AskVol_j], j=1,2,3,4
        df['dsA'].iloc[i] = (sum([(df[f'AskPrice{j+1}'].iloc[i] - df[f'AskPrice{j}'].iloc[i]) / df[f'AskVol{j}'].iloc[i]
                                for j in range(1, 5)])/sum([1/df[f'AskVol{j}'].iloc[i] for j in range(1, 5)]))
        
        # 计算买方深度加权价差 dsB = -∑[(BidPrice_j - BidPrice_{j+1}) / BidVol_j] / ∑[1/BidVol_j], j=1,2,3,4
        df['dsB'].iloc[i] = -(sum([(df[f'BidPrice{j}'].iloc[i] - df[f'BidPrice{j+1}'].iloc[i]) / df[f'BidVol{j}'].iloc[i]
                                 for j in range(1, 5)])/sum([1/df[f'BidVol{j}'].iloc[i] for j in range(1, 5)]))

        # 处理挂单量变化
        ask_prices = [df[f'AskPrice{j}'].iloc[i] for j in range(1, 6)]
        bid_prices = [df[f'BidPrice{j}'].iloc[i] for j in range(1, 6)]
        ask_vols = [df[f'AskVol{j}'].iloc[i] for j in range(1, 6)]
        bid_vols = [df[f'BidVol{j}'].iloc[i] for j in range(1, 6)]
        
        # 计算主动性买卖量
        # 检查是否有卖盘价格低于上一时刻的最优卖价（AskPrice1），这表示有主动性卖单
        if any(ask_price < df['AskPrice1'].iloc[i - 1] for ask_price in ask_prices):
            # 计算主动性卖单的总量：所有价格低于上一时刻最优卖价的卖单量之和
            df['aggaskV'].iloc[i] = sum([ask_vols[j] for j, ask_price in enumerate(ask_prices) if
                                         ask_price < df['AskPrice1'].iloc[i - 1]])
            
            # 计算主动性卖单的深度指标：根据价格突破的档位设置不同的负向权重
            for j, ask_price in enumerate(ask_prices):
                if j == 4:  # 如果遍历到第5档仍未找到高于上一时刻最优卖价的价格
                    df['aggasknum'].iloc[i] = -0.2*(j+1)  # 设置为最大负向权重 -1.0
                    break
                if ask_price > df['AskPrice1'].iloc[i - 1]:  # 找到第一个高于上一时刻最优卖价的价格
                    df['aggasknum'].iloc[i] = -0.2*j  # 根据档位j设置负向权重
                    break

        if any(bid_price > df['BidPrice1'].iloc[i - 1] for bid_price in bid_prices):
            df['aggbidV'].iloc[i] = sum([bid_vols[j] for j, bid_price in enumerate(bid_prices) if
                                         bid_price > df['BidPrice1'].iloc[i - 1]])
            for j, bid_price in enumerate(bid_prices):
                if j == 4:
                    df['aggbidnum'].iloc[i] = 0.2*(j+1)
                if bid_price < df['BidPrice1'].iloc[i - 1]:
                    df['aggbidnum'].iloc[i] = 0.2*j
                    break

        # 处理相同价格但量变化的情况
        for j, (ask_price, ask_vol) in enumerate(zip(ask_prices, ask_vols), start=1):
            if ask_price == df['AskPrice1'].iloc[i - 1] and j <= 3:
                df['aggaskV'].iloc[i] += max(ask_vol - df['AskVol1'].iloc[i - 1], 0)
        for j, (bid_price, bid_vol) in enumerate(zip(bid_prices, bid_vols), start=1):
            if bid_price == df['BidPrice1'].iloc[i - 1] and j <= 3:
                df['aggbidV'].iloc[i] += max(bid_vol - df['BidVol1'].iloc[i - 1], 0)

    # 计算价格差异特征
    for i in range(1, 6):
        df[f'bidpxdiff{i}'] = df[f'BidPrice{i}'].diff().fillna(0)
        df[f'askpxdiff{i}'] = df[f'AskPrice{i}'].diff().fillna(0)
    
    # 计算盘口价差
    for i in range(1, 5):
        df[f'bidpxcx{i}'] = df[f'BidPrice{i}'] - df[f'BidPrice{i+1}']
        df[f'askpxcx{i}'] = -(df[f'AskPrice{i}'] - df[f'AskPrice{i+1}'])
    
    df[f'bidpxcx_avg'] = df[[f'bidpxcx{j}' for j in range(1, 3)]].mean(axis=1)
    df[f'askpxcx_avg'] = df[[f'askpxcx{j}' for j in range(1, 3)]].mean(axis=1)

    # 处理价格和量特征
    for i in range(1, 6):
        drop.append(f'BidPrice{i}')
        drop.append(f'AskPrice{i}')
        drop.append(f'BidVol{i}')
        drop.append(f'AskVol{i}')
        df[f'avgbid{i}'] = df[[f'bidpxdiff{j}' for j in range(1, i + 1)]].mean(axis=1)
        df[f'avgask{i}'] = df[[f'askpxdiff{j}' for j in range(1, i + 1)]].mean(axis=1)
        df[f'avgbidSUM{i}'] = df[[f'bidpxdiff{j}' for j in range(1, i + 1)]].sum(axis=1)
        df[f'avgaskSUM{i}'] = df[[f'askpxdiff{j}' for j in range(1, i + 1)]].sum(axis=1)
        df[f'sumbidv{i}'] = df[[f'BidVol{j}' for j in range(1, i + 1)]].sum(axis=1)
        df[f'sumaskv{i}'] = df[[f'AskVol{j}' for j in range(1, i + 1)]].sum(axis=1)
    
    # 计算主动性买卖量特征
    for i in [3, 4, 5]:
        df[f'aggbidV_RES_{i}'] = np.where(df['aggbidV'] > 0, df['aggbidV'] - df[f'sumaskv{i}'], 0)
        df[f'aggaskV_RES_{i}'] = np.where(df['aggaskV'] > 0, df['aggaskV'] - df[f'sumbidv{i}'], 0)
        df[f'aggbidV_RATIO_{i}'] = (df['aggbidV'] / df[f'sumaskv{i}'])
        df[f'aggaskV_RATIO_{i}'] = (df['aggaskV'] / df[f'sumbidv{i}'])
        
        # 滚动窗口特征
        for window in range(2, 20, 2):
            df[f'aggbidV_RES_{i}_{window}_sum'] = df[f'aggbidV_RES_{i}'].rolling(window).sum()
            df[f'aggbidV_RES_{i}_{window}_avg'] = df[f'aggbidV_RES_{i}'].rolling(window).mean()
            df[f'aggbidV_RES_{i}_{window}_diff'] = (df[f'aggbidV_RES_{i}'] - df[f'aggbidV_RES_{i}'].rolling(window).median().shift())

            df[f'aggaskV_RES_{i}_{window}_sum'] = df[f'aggaskV_RES_{i}'].rolling(window).sum()
            df[f'aggaskV_RES_{i}_{window}_avg'] = df[f'aggaskV_RES_{i}'].rolling(window).mean()
            df[f'aggaskV_RES_{i}_{window}_diff'] = (df[f'aggaskV_RES_{i}'] - df[f'aggaskV_RES_{i}'].rolling(window).median().shift())

            df[f'aggbidV_RATIO_{i}_{window}_avg'] = df[f'aggbidV_RES_{i}'].rolling(window).mean()
            df[f'aggaskV_RATIO_{i}_{window}_avg'] = df[f'aggaskV_RES_{i}'].rolling(window).mean()
    
    # 分组特征
    for i in [3, 4, 5]:
        df[f'aggbidV_RES_{i}_groupbysecond_diff'] = (df[f'aggbidV_RES_{i}'] - df.groupby('minute')[f'aggbidV_RES_{i}'].transform(lambda x: x.expanding().median().shift().fillna(0)))
        df[f'aggaskV_RES_{i}_groupbysecond_diff'] = (df[f'aggaskV_RES_{i}'] - df.groupby('minute')[f'aggaskV_RES_{i}'].transform(lambda x: x.expanding().median().shift().fillna(0)))

    # 计算买卖量差
    df['vol5'] = df['BidVol1'] + df['BidVol2'] + df['BidVol3'] + df['BidVol4'] + df['BidVol5'] - (
            df['AskVol1'] + df['AskVol2'] + df['AskVol3'] + df['AskVol4'] + df['AskVol5'])

    # 成交量和成交额特征
    df['voldiff'] = df['Volume'].diff().fillna(0)
    df['amtdiff'] = df['TotalValueTraded'].diff().fillna(0)
    df['avgpx'] = (df['amtdiff'] / df['voldiff']) / multi
    df['avgpx'].fillna(method='ffill', inplace=True)
    df['avgdiff'] = df['avgpx'].diff()
    df['avgdiff'].iloc[0:2] = 0
    df['trddir'] = 0
    df['bidaskspread'] = (df['AskPrice1'] - df['BidPrice1'])

    # 计算中间价格组合特征
    prices = [f'midpx{i}' for i in range(1, 6)]
    for c in combinations(prices, 2):
        df[f'{c[0]}_{c[1]}_imb'] = df.eval(f'({c[0]} - {c[1]})')

    # 计算价格差异和持续性特征
    df['middiff'] = df['midpx1'].diff().fillna(0)
    df['middiff_hurst'] = nolds.hurst_rs(df['middiff'])
    df['Lowdiff'] = df['Low'].diff().fillna(0)
    df['Highdiff'] = df['High'].diff().fillna(0)
    df['LowDura'] = df.groupby('Low').cumcount()
    df['HighDura'] = df.groupby('High').cumcount()
    df['LowdiffA'] = df['LowDura'] * df['Lowdiff']
    df['HighdiffA'] = df['HighDura'] * df['Highdiff']

    # 计算趋势特征
    decay = [0.9, 0.95]
    for _ in decay:
        df[f'midtrend-{_}'] = 0
        
    for i in range(1, len(df)):
        for _ in decay:
            df[f'midtrend-{_}'].iloc[i] = df[f'midtrend-{_}'].iloc[i - 1] * _ + df['middiff'].iloc[i]
            
    for _ in decay:
        df[f'trenddiff-{_}'] = df[f'midtrend-{_}'].diff().fillna(0)
        df[f'trendoftrend-{_}'] = 0

    for i in range(1, len(df)):
        for _ in decay:
            df[f'trendoftrend-{_}'].iloc[i] = df[f'trendoftrend-{_}'].iloc[i - 1] * _ + df[f'trenddiff-{_}'].iloc[i]

    # 计算不同窗口的价格差异
    midgap = range(2, 20, 2)
    for gap in midgap:
        df[f'midgap{gap}'] = df['midpx1'].diff(gap).fillna(0)
        df[f'Lowgap{gap}'] = df['Low'].diff(gap).fillna(0)
        df[f'Highgap{gap}'] = df['High'].diff(gap).fillna(0)
        df[f'midgap{gap}_hurst'] = nolds.hurst_rs(df[f'midgap{gap}'])

    # 确定交易方向
    # 1. 买入方向(trddir=1)判断条件:
    #    - 当前成交均价高于上一时刻卖一价格减去价差的25%(接近卖一价)且成交量为正
    #    - 或者当前成交均价高于上一时刻成交均价且成交量大于10
    # 2. 卖出方向(trddir=-1)判断条件:
    #    - 当前成交均价低于上一时刻买一价格加上价差的25%(接近买一价)且成交量为正且均价大于0
    #    - 或者当前成交均价低于上一时刻成交均价且成交量大于10
    for i in range(1, len(df)):
        if (df['avgpx'].iloc[i] >= (df['AskPrice1'].iloc[i - 1] - 0.25 * (df['AskPrice1'].iloc[i - 1] - df['BidPrice1'].iloc[i - 1]))
            and df['voldiff'].iloc[i] > 0) or (df['avgpx'].iloc[i] > df['avgpx'].iloc[i - 1] and df['voldiff'].iloc[i] > 10):
            df['trddir'].iloc[i] = 1
        elif (df['avgpx'].iloc[i] <= (df['BidPrice1'].iloc[i-1] + 0.25*(df['AskPrice1'].iloc[i-1]-df['BidPrice1'].iloc[i-1]))
              and df['voldiff'].iloc[i] > 0 and df['avgpx'].iloc[i] > 0) or (df['avgpx'].iloc[i] < df['avgpx'].iloc[i-1] and
                         df['voldiff'].iloc[i] > 10):
            df['trddir'].iloc[i] = -1

    # 计算交易方向滚动特征
    for window in range(2, 20, 2):
        df[f"trddir_{window}_sum"] = df['trddir'].rolling(window).sum()
        
    # 计算交易量特征
    df['trdaskV'] = df['voldiff'] * (df['trddir'] == -1)
    df['trdbidV'] = df['voldiff'] * (df['trddir'] == 1)
    df['trdimb'] = df['trdbidV'] - df['trdaskV']
    df['ordimb'] = df['aggbidV'] - df['aggaskV']
    df['BreakBid'] = df['aggbidV'] + df['trdbidV']
    df['BreakAsk'] = df['trdaskV'] + df['aggaskV']
    df['BreakBid1'] = df['aggbidV'] - df['trdbidV']
    df['BreakAsk1'] = df['aggaskV'] - df['trdaskV']

    # 计算盘口价差特征
    df['gapAsk'] = df['AskPrice2'] - df['AskPrice1']
    df['gapBid'] = df['BidPrice1'] - df['BidPrice2']
    df['vol5B'] = df[[f'BidVol{j}' for j in range(1, 6)]].sum(axis=1)
    df['vol5S'] = df[[f'AskVol{j}' for j in range(1, 6)]].sum(axis=1)

    # 计算信号特征
    powexp = 0.8
    for i in range(1, 6):
        df[f'signal{i}'] = 0
        drop.append(f'signal{i}')
        
    # 计算各种信号
    for i in range(6, len(df)):
        # 信号1: 连续同向交易且成交量大于挂单量
        if df['trddir'].iloc[i] == 1 and df['trddir'].iloc[i-1] == 1 and df['voldiff'].iloc[i] > df['vol5S'].iloc[i] and df['bidaskspread'].iloc[i] < 2:
            df['signal1'].iloc[i] = 0.4 * pow(df['voldiff'].iloc[i]/df['vol5S'].iloc[i], powexp)
        elif df['trddir'].iloc[i] == -1 and df['trddir'].iloc[i-1] == -1 and df['voldiff'].iloc[i] > df['vol5B'].iloc[i] and df['bidaskspread'].iloc[i] < 2:
            df['signal1'].iloc[i] = -0.4 * pow(df['voldiff'].iloc[i]/df['vol5B'].iloc[i], powexp)
            
        # 信号2: 交易方向与主动性挂单方向一致
        if df['trddir'].iloc[i] == 1 and df['voldiff'].iloc[i] < df['aggbidV'].iloc[i] and df['aggbidV'].iloc[i] > 10 and df['bidaskspread'].iloc[i] < 2:
            df['signal2'].iloc[i] = 0.4 * pow(df['aggbidV'].iloc[i]/10, powexp)
        elif df['trddir'].iloc[i] == -1 and df['voldiff'].iloc[i] < df['aggaskV'].iloc[i] and df['aggaskV'].iloc[i] > 10 and df['bidaskspread'].iloc[i] < 2:
            df['signal2'].iloc[i] = -0.4 * pow(df['aggaskV'].iloc[i]/10, powexp)
            
        # 信号3: 交易方向与深度加权价差一致
        if df['trddir'].iloc[i] == 1 and df['dsA'].iloc[i] > 0.6 and df['bidaskspread'].iloc[i] < 2:
            df['signal3'].iloc[i] = 0.4 * pow(df['dsA'].iloc[i]/0.6, powexp)
        elif df['trddir'].iloc[i] == -1 and df['dsB'].iloc[i] < -0.6 and df['bidaskspread'].iloc[i] < 2:
            df['signal3'].iloc[i] = -0.4 * pow(-df['dsB'].iloc[i]/0.6, powexp)
            
        # 信号4: 交易方向与价格变动一致
        if df['trddir'].iloc[i] == 1 and df['avgbid3'].iloc[i] > 0.6 and df[f'bidpxcx_avg'].iloc[i] < 0.5 and df['bidaskspread'].iloc[i] < 2:
            df['signal4'].iloc[i] = 0.4 * pow(df['avgbid3'].iloc[i]/0.6, powexp)
        elif df['trddir'].iloc[i] == -1 and df['avgask3'].iloc[i] < -0.6 and df[f'askpxcx_avg'].iloc[i] < 0.5 and df['bidaskspread'].iloc[i] < 2:
            df['signal4'].iloc[i] = -0.4 * pow(-df['avgask3'].iloc[i]/0.6, powexp)
            
        # 信号5: 交易方向与主动性挂单数量一致
        if df['trddir'].iloc[i] == 1 and df['aggbidnum'].iloc[i] > 0.4 and df['bidaskspread'].iloc[i] < 2:
            df['signal5'].iloc[i] = 0.4 * pow(df['aggbidnum'].iloc[i]/0.6, powexp)
        elif df['trddir'].iloc[i] == -1 and df['aggasknum'].iloc[i] < -0.4 and df['bidaskspread'].iloc[i] < 2:
            df['signal5'].iloc[i] = -0.4 * pow(-df['aggasknum'].iloc[i]/0.6, powexp)

    # 计算目标变量
    delta = 0.4
    df['rawprice'] = (df['avgpx'].rolling(n).mean().shift(-n) - df['midpx1']).fillna(method='ffill')
    df['price'] = df['rawprice'].apply(lambda x: 1 if x > delta else (2 if x < -delta else 0))
    drop.append(f'price')

listtrain = []
listtest = []

rawfile = 'md_20250325_shm_receiver.csv'
rawdata = pd.read_csv(rawfile, encoding='gbk')
rawdata['date'] = rawdata['ForQuoteSysID'].apply(lambda x: x[:8])
datetrain = ['20240909', '20240910', '20240911', '20240912', '20240913']
datetest = ['20240918', '20240919']

rawdata.index = pd.DatetimeIndex(rawdata.apply(
    lambda row: covert_time(row['timestamp_str'], row['date'], '%Y%m%d%H:%M:%S.%f'), axis=1))
rawdata['timestamp_str'] = rawdata.apply(
    lambda row: covert_time(row['timestamp_str'], row['date'], '%Y%m%d%H:%M:%S.%f'), axis=1)

