# -*- coding:utf-8 -*-

from datetime import datetime
import os
import shutil
import sys

# codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
sys.path.append(codepath)
sys.path.append('D:\onedrive\中泰衍生\mine_python\quantpy')
from MMaker import sendmail

if datetime.now().weekday() >= 5:
    print("今天是周末")
    os._exit(0)

# 邮件接收人，用列表保存，可以添加多个
receivers1 = ["<EMAIL>", ]  # 接收邮箱
receivers2 = ["<EMAIL>", "<EMAIL>"]  # 提醒邮箱

dt = datetime.now()
dt = datetime.strftime(dt, "%Y%m%d")
# dt = "20180327"
copyPath = u'D:\\works\\中泰衍生\\做市组日常工作\\盘后损益\\图片\\%s盘后损益.jpg' % dt
pic = '%s.jpg' % dt
try:
    shutil.copy(copyPath, pic)
except:
    print(u'损益未更新')
    sendmail.sendmail(receivers2, u'#IFTTT 损益未更新', u'检查内网文件错误', '')
    os._exit(0)

subject = u'#IFTTT 做市组%s盘后损益' % dt  # input(u"{'请输入邮件主题：'}")
content = u'大家好,\r\n\r\n\r\n    这是今天的做市损益 \r\n\r\n\r\n李宁\r\n\r\n'  # input(u"{'请输入邮件主内容:'}")
try:
    sendmail.sendmail(receivers2, subject, content, pic)
except:
    sendmail.sendmail(receivers2, u'#IFTTT 损益邮件发生错误', u'检查图片', '')

# from MMaker.pldaily import wechat

# wechat1 = wechat.WeChat()
# wechat1.add_filehelper()
# wechat1.add_friends(u'李宁')
# wechat1.add_friends(u'哟')
# wechat1.add_rooms(u'MMF7')
# wechat1.add_mps(u'八卦')
# wechat1.send_msg(u'你')
# wechat1.send_image(pic)
# print(datetime.strftime(datetime.now(), "%H%M%S"), ':wechat succeed')

os.remove(pic)
