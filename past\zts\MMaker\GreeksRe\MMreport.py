# -*- coding:utf-8 -*-
import json
import pyodbc
from datetime import datetime
import os
import shutil
import xlwings as xw
import win32com.client
from WindPy import w
import calendar
import pandas as pd


def makexls(app):
    monthRange = calendar.monthrange(int(dt00[:4]), int(dt00[-2:]))
    data1 = w.wsd("IH.CFE", "trade_hiscode", dt00 + "01",
                  dt00 + str(monthRange[1]), "Fill=Previous")

    os.makedirs(fengkongPath)
    lt = os.listdir(pypath)
    keyWord = ["xls", ]
    for f in lt:
        if not os.path.isdir(f):
            for keyword in keyWord:
                if keyword in f:
                    shutil.copy(pypath + f, fengkongPath + dt00 + " " + f)

    lt = os.listdir(fengkongPath)
    for f in lt:
        if not os.path.isdir(f):
            for keyword in keyWord:
                if keyword in f:
                    filename = fengkongPath + f

                    # 打开 data.xlsx 文件到 wookbook 中
                    wb = app.books.open(filename)

                    for i in range(0, len(data1.Data[0])):
                        date = data1.Times[i]
                        sht1 = wb.sheets["Sheet" + str(i + 1)]
                        sht1.name = datetime.strftime(date, "%m%d")
                        # sht1 = xw.sheets.add(name=datetime.strftime(date, "%m%d"), before=None, after=sht)
                        if u"交易记录 期货" not in f:
                            sht1.range('B4').value = str(date)[5:]
                        if u"持仓 期货" in f:
                            sht1.range('B6').value = data1.Data[0][i]
                        # sht1.autofit()
                    for i in range(len(data1.Data[0]), 25):
                        shti = wb.sheets["Sheet" + str(i + 1)]
                        shti.delete()
                    wb.sheets[0].delete()
                    wb.save()

                    # # wb = xw.Book(filename)
                    # sht = wb.sheets[0]
                    # ori = sht.range('A1:S11').value
                    # for i in range(0, len(data1.Data[0])):
                    #     date = data1.Times[i]
                    #     sht1 = xw.sheets.add(name=datetime.strftime(date, "%m%d"), before=None, after=sht)
                    #     sht1.range('A1').value = ori
                    #     if u"交易记录 期货" not in f:
                    #         sht1.range('B4').value = str(date)[5:]
                    #     if u"持仓 期货" in f:
                    #         sht1.range('B6').value = data1.Data[0][i]
                    #     # sht1.autofit()
                    #     sht = sht1
                    # wb.sheets[0].delete()
                    # wb.save()


def MMdatasend(app):
    wb = app.books.open(copyPath)
    app.calculate()

    sht0 = wb.sheets[0]
    sht1 = wb.sheets[1]

    rng0 = sht0.range('A2').expand().value
    rng00 = sht0.range('M1').expand().value
    rng000 = sht0.range('P1').expand().value

    rng1 = sht1.range('A2').expand().value
    rng11 = sht1.range('M1').expand().value
    rng111 = sht1.range('P1').expand().value

    xian0 = sht0.range('N2').value
    xian1 = sht1.range('N2').value

    savePath2 = []
    if rng0 == rng1 or rng000 == rng111:
        print(u"与前日数据一样")
        app.quit()
        app.kill()
        price = []
        sendmail.sendmail(receivers2, u"#IFTTT 做市数据未报送 错误0", u"与前日数据一样", '')
        os._exit(0)
    else:
        print(u"数据正在更新")
        sht1.range('A2').expand().clear_contents()
        sht1.range('A2').value = rng0
        sht1.range('M1').value = rng00
        sht1.range('P1').value = rng000
        sht1.range('J2:K2').value = sht0.range('J2:K2').value
        date = datetime.strftime(dt, "%Y%m%d")
        sht1.range('J4').value = date

        jsr=getpsw("workdatabase","option-SH")
        server =jsr["server"]
        user = jsr["user"]
        password = jsr["password"]
        # beginDate = "2017-06-24"

        # Specifying the ODBC driver, server name, database, etc. directly
        conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='option-SH', PWD=password, UID=user)

        sql = "SELECT [Symbol],[PreSettlPrice],[Price] FROM [option-SH].[dbo].[FutRealQuote] where left(Symbol," \
              "2)='IH' order by Symbol "

        price1 = pd.read_sql(sql, conn, index_col=None, coerce_float=True, params=None, parse_dates=True,
                         columns=None,
                         chunksize=None)

        sql = "SELECT Price FROM [option-SH].[dbo].[StkRealQuote]"

        price2 = pd.read_sql(sql, conn, index_col=None, coerce_float=True, params=None, parse_dates=True,
                         columns=None,
                         chunksize=None)
        conn.commit()
        conn.close()
        price = []
        # a = w.wsi("IH.CFE", "close", "%s 15:00:00" % date, "%s 15:01:00" % date, "Fill=Previous")
        # price.append(w.wsi("IH.CFE", "close", "%s 14:59:00" % date, "%s 15:00:00" % date, "Fill=Previous").Data[0][0])
        # price.append(
            # w.wsi("510050.SH", "close", "%s 15:00:00" % date, "%s 15:01:00" % date, "Fill=Previous").Data[0][0])
        price.append(price1['Price'][0])
        price.append(price2.iloc[0,0])
        wb2 = app.books.open(savePath)
        sht3 = wb2.sheets[u'持仓 & Greeks']
        sht2 = wb2.sheets[u'记录']

        sht3.range('K9').value = price[0]
        sht3.range('N9').value = price[1]
        sht3.range('A2').expand().clear_contents()
        sht3.range('A2').value = rng0
        sht3.range('M1').value = rng00
        sht3.range('P1').value = rng000
        sht3.range('J2:K2').value = sht0.range('J2:K2').value
        sht3.range('J18').value = dt
        rng = sht2.range('A1').expand()
        nrows = rng.rows.count
        # ncols = rng.columns.count
        sht2.range(nrows + 1, 1).value = datetime.strftime(dt, "%Y-%m-%d")
        app.calculate()
        sht2.range(nrows + 1, 2).value = sht3.range('J20:U20').value

        wb3 = app.books.open(sendPath1)
        shtnew = wb3.sheets[0]
        shtnew.range(2, 1).value = datetime.strftime(dt, "%Y-%m-%d")
        shtnew.range(2, 2).value = sht3.range('J20:U20').value
        # shtnew.autofit()

        path0 = os.path.dirname(sendPath1)
        lt = os.listdir(path0)
        keyWord = [u"中泰证券做市业务数据报送--20", ]
        for f in lt:
            if not os.path.isdir(f):
                for keyword in keyWord:
                    if keyword in f:
                        os.remove(path0 + "\\" + f)

        savePath2 = u"%s--%s.xlsx" % (sendPath1[:-5],
                                      datetime.strftime(dt, "%Y%m%d"))
        wb3.save(savePath2)

        wb2.save()

    wb.save()

    return savePath2, xian0, xian1, price


def upRisk(app):
    dt001 = datetime.strftime(dt, "%m%d")

    wb0 = app.books.open(copyPath)
    sht0 = wb0.sheets[0]
    sht00 = wb0.sheets[1]

    print("更新持仓期货")
    wb1 = app.books.open(fengkongPath + dt00 + " 持仓 期货.xlsm")
    sht1 = wb1.sheets[dt001]
    sht1.range('C6').value = sht0.range('N3').value + sht0.range('N4').value
    sht1.autofit()
    sht1.activate()
    wb1.save()

    if xian0 != xian1:
        print("更新持仓现货", xian0, xian1)
        wb2 = app.books.open(fengkongPath + dt00 + " 持仓 现货.xlsx")
        sht2 = wb2.sheets[dt001]
        sht2.range('C6').value = xian0
        sht2.range('D6').value = price[1]
        sht2.range('E6').value = sht2.range(
            'C6').value * sht2.range('D6').value
        sht2.autofit()
        sht2.activate()
        wb2.save()
    else:
        print("不需要更新持仓现货", xian0, xian1)
        wb2 = app.books.open(fengkongPath + dt00 + " 持仓 现货.xlsx")
        sht2 = wb2.sheets[dt001]
        sht2.range('C6').value = xian0
        sht2.range('D6').value = price[1]
        sht2.range('E6').value = sht2.range(
            'C6').value * sht2.range('D6').value
        # wb2.sheets[dt001].delete()
        # sht2.autofit()
        sht2.activate()
        wb2.save()

    print("更新交易记录期权")
    wb3 = app.books.open(fengkongPath + dt00 + " 交易记录 期权.xlsm")
    sht3 = wb3.sheets[dt001]
    sht3.range('A2').value = sht0.range('Q2').value
    sht3.range('B2').value = sht0.range('Q4:Q6').value

    # sht3.autofit()
    sht3.activate()
    wb3.save()

    wb0.save()


def getpsw(classname,name):
    import json
    data = open(u"D:/onedrive/文档/package.json", encoding='utf-8')
    strJson = json.load(data)
    users = strJson[classname]
    for user in users:
        if name == user["name"]:
            return user


if __name__ == "__main__":
    if datetime.now().weekday() >= 5:
        print(datetime.now().weekday(), "今天是周末，不需要更新")
        os._exit(0)

    import sys

    codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
    sys.path.append(codepath)
    sys.path.append('D:\onedrive\中泰衍生\mine_python\quantpy')
    from MMaker import sendmail

    w.start()

    dt = datetime.now()
    # dt = datetime.strptime("20190513", "%Y%m%d")
    print(dt)
    dt00 = datetime.strftime(dt, "%Y%m")

    # copyPath = u'\\\\10.25.18.38\\home\\做市业务数据报送\\做市业务数据报送--拷贝.xlsm'
    copyPath = u'D:\\works\\中泰衍生\\做市组日常工作\\业务数据报送\\做市业务数据报送--拷贝.xlsm'
    savePath = os.path.dirname(copyPath) + u'\\中泰证券做市业务数据报送--Backup.xlsm'
    sendPath1 = os.path.dirname(savePath) + u'\\HIS\\中泰证券做市业务数据报送.xlsx'

    pypath = codepath + u"\\MMaker\\files\\"  # 风控模板文件

    fengkongPath = u"\\\\%s\\共享\\05 风控合规\\成交持仓明细\\正修\\" % getpsw("computer","officepublic")["ip"]
    fengkongPath = fengkongPath + dt00 + "\\"

    # 发送邮件
    # 邮件接收人，用列表保存，可以添加多个
    # receivers = ['<EMAIL>', ]
    # send list
    # receivers = ["<EMAIL>", ]
    receivers1 = ["<EMAIL>", ]  # "<EMAIL>"]
    receivers2 = ["<EMAIL>", ]  # 错误邮箱

    # 更新当日内网监控数据
    app1 = xw.App(visible=False, add_book=False)
    app1.display_alerts = False
    try:
        sendPath2, xian0, xian1, price = MMdatasend(app1)
        print(u'上交所数据报送成功')
    except Exception as e:
        print(e)
        print('none file 1')
        sendmail.sendmail(receivers2, u'#IFTTT 数据未报送', '更新当日内网监控数据错误', '')
        print(u'上交所数据报送成功')
        app1.quit()
        app1.kill()
        os._exit(0)
    else:
        app1.quit()
        app1.kill()

    subject = u'中泰证券50ETF期权做市业务数据报送'  # input(u"{'请输入邮件主题：'}")
    # input(u"{'请输入邮件主内容:'}")
    content = u'Hello,\r\n\r\n\r\n    附件为中泰证券50ETF期权做市业务数据报送 ' \
              u'\r\n\r\n\r\n李宁\r\n电话：18018599235\r\n办公邮箱：<EMAIL>\r\n\r\n\r\n '

    filepath = os.path.dirname(savePath) + u'\\HIS\\中泰证券做市业务数据报送--%s.xlsx' \
               % datetime.strftime(datetime.now(), "%Y%m%d")

    try:
        sendmail.sendmailwork(receivers1, subject, content, filepath)
    except Exception as e:
        print('none file 2', e)
        sendmail.sendmailwork(receivers1, subject, content, sendPath1)
        sendmail.sendmail(receivers2, u'#IFTTT 数据已报送', '更新后的文件不存在', '')
    except:
        print('none file 2.2')
        sendmail.sendmail(receivers2, u'#IFTTT 数据未报送', '发送邮件未知错误', '')
        os._exit(0)

    # 更新公共盘风控数据
    # 如果是新月份，建立文件夹
    try:
        if not os.path.exists(fengkongPath):
            print(u"建立公共盘风控文件")
            app2 = xw.App(visible=False, add_book=False)
            app2.display_alerts = False
            makexls(app2)
            app2.quit()
            app2.kill()

        app2 = xw.App(visible=False, add_book=False)
        app2.display_alerts = False
        upRisk(app2)
        app2.quit()
        app2.kill()

        print(u"更新完毕")
    except Exception as e:
        app2.quit()
        app2.kill()
        print(e)
        sendmail.sendmail(receivers2, u'#IFTTT 风控盘更新错误', '报送成功', '')
        os._exit(0)
