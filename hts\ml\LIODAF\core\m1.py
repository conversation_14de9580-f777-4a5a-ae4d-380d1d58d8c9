
"""
机器学习分析框架
@author: lining
"""
import os
import sys
import pandas as pd
from tqdm import tqdm
import re

sys.path.insert(0, sys.path[0]+"/../")

from data.data_loader import OrderBookDataLoader
from data.features.main import FactorSystem
from models.model_trainer import OrderBookModelTrainer
from utils.visualizer import OrderBookVisualizer
from core import config
from data.labels.cal_labels import LabelGenerator
from utils.utils import log_print, search_days, clear_log
from data.data_loader import split_normalize_data
from models.model_evaluate import model_evaluate
from core.backtester import OrderBookBacktester




class M1:
    daysnum = 6 
    
    max_data = config.ROLLING_WINDOW_SIZE + 20000 if config.BACKMODE == 'rolling' else 1000000000

    model_selection = False
    scaler = config.SCALER
    target_cols = config.TARGET_COLS
    backmode = config.BACKMODE
    output_dir = config.OUTDIR
    data_path = config.DATA_PATH

    def __init__(self):
        
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        self.mix_df = None
        self.feature_cols = None
        self.results = None
        self.trade_report = None
        self.days = None
        self.train_data = {}
        self.y_train_dict = {}
        self.test_data = {}
        self.y_test_dict = {}
        self.train_pred = None
        self.test_pred = None
        self.all_predictions = None
        self.factor_test_results = None
        self.factor_doc_df = None


    def process_data(self):
        if config.BACKMODE == 'days':
            self.days = search_days(config.DATE_STR, self.daysnum, config.DATA_PATH)
        else:
            self.days = [config.DATE_STR]
        df = pd.DataFrame()
        for day in self.days:
                    
            data_loader = OrderBookDataLoader(data_path=self.data_path,  
                                            code_list=[],
                                            date_str=day,
                                            time_range=config.TIME_RANGE)

            data_loader.load_data()
            
            exp = data_loader.data_md['Symbol'].str.extract(r'(\d+)')

            if not re.search(r'\d+$', config.CODE_LIST[0]):
                
                codeslist = [config.CODE_LIST[0] + str(int(exp.min().iloc[0])), ]
            else:
                codeslist = config.CODE_LIST
            data = data_loader.filtor(codeslist, day, config.TIME_RANGE)[:self.max_data]
            
            data['delete'] = False
            data.loc[data.index[-100:], 'delete'] = True
            log_print(f"数据加载完成{day}_{codeslist[0]}，共 {len(data)} 条记录")
            df = pd.concat([df, data])
        self.mix_df = df

    def generate_features_and_labels(self):
        
        self.factor_system = FactorSystem()
        self.factor_system.initialize(self.mix_df, self.feature_cols, self.target_cols)
        features_df, self.feature_cols = self.factor_system.generate_features()

        
        label_generator = LabelGenerator(self.mix_df, horizons=config.EVALUATE_HORIZONS)
        label_df = label_generator.generate_all_labels()

        
        df = pd.concat([features_df, label_df], axis=1)
        df = df.loc[:, ~df.columns.duplicated()]  
                
        self.mix_df = self.mix_df[~self.mix_df['delete']]

        
        if self.backmode != 'rolling':
            self.train_data, self.test_data, self.y_train_dict, self.y_test_dict, self.scalers = split_normalize_data(self.mix_df,
                                                                                                        self.feature_cols,
                                                                                                        self.scaler)

    def model_selection(self, x_train, y_train):
        model_trainer = OrderBookModelTrainer(self.feature_cols, self.target_cols)
        model_performances, self.model_type = model_trainer.model_selection(x_train, y_train)

    def train_model(self):
        if config.BACKMODE == 'rolling':
            
            all_predictions = pd.DataFrame(index=self.mix_df.index)
            
            min_train_size = config.ROLLING_WINDOW_SIZE
            
            log_print(f"开始滚动窗口训练，窗口大小: {config.ROLLING_WINDOW_SIZE}")
            for i in tqdm(range(min_train_size, len(self.mix_df)), desc="滚动窗口训练进度"):
                
                self.train_data, self.test_data, self.y_train_dict, self.y_test_dict = split_normalize_data(
                    self.mix_df[i - config.ROLLING_WINDOW_SIZE:i - config.PREDICTION_HORIZONS + 1], self.feature_cols,
                    self.target_cols, self.backmode, self.scaler)

                
                model_trainer = OrderBookModelTrainer(self.feature_cols, self.target_cols)
                
                model_trainer.train_models(self.train_data, self.y_train_dict, model_type=self.model_type,
                                           params=config.MODEL_SETTING[self.model_type]['params'])
                
                pred = model_trainer.predict(self.test_data)
                
                all_predictions.loc[self.test_data.index, self.target_cols[0] + '_pred'] = pred.iloc[0, 0]

            if len(all_predictions.dropna()) - config.ROLLING_WINDOW_SIZE > 0:
                log_print(
                    f"滚动窗口预测完成，共生成 {len(all_predictions.dropna()) - config.ROLLING_WINDOW_SIZE} 条预测")

        else:

            model_trainer = OrderBookModelTrainer(self.feature_cols, self.target_cols, model_type=config.MODEL_TYPE)
            
            model_trainer.train_models(self.train_data, self.y_train_dict, 
                                       params=config.MODEL_SETTING[config.MODEL_TYPE]['params'], model_parameter_search=config.MODEL_PARAMETER_SEARCH)

            
            self.train_pred = model_trainer.predict(self.train_data[self.target_cols[0]])
            self.test_pred = model_trainer.predict(self.test_data[self.target_cols[0]])
            self.all_predictions = pd.concat([self.train_pred, self.test_pred], axis=0)

        self.model_trainer = model_trainer

    def plot_results(self):
        
        visualizer = OrderBookVisualizer(use_chinese=True)
        
        visualizer.plot_backtest_results(self.results)
        
        visualizer.plot_feature_importance(self.model_trainer.models[config.TARGET_COLS[0]], self.feature_cols)
        
        visualizer.plot_feature_correlation(self.train_data, self.y_train_dict, features=self.feature_cols,
                                            target=config.TARGET_COLS[0])
        
        visualizer.plot_IC_IR_win_rate(self.factor_test_results)
        
        
        visualizer.plot_trade_analysis(self.trade_report, self.results['return'], standardize_qq=True, title=None)

        
        visualizer.plot_shap_summary(self.shap_values['importance'])

        
        visualizer.plot_shap_values(self.shap_values)


    def save_results(self):
        
        log_print("保存结果...")
        self.mix_df.to_csv(os.path.join(self.output_dir, 'features.csv'), index=True)
        self.results.to_csv(os.path.join(self.output_dir, 'backtest_results.csv'), index=True)
        if len(self.trade_report) > 0:
            self.trade_report.to_csv(os.path.join(self.output_dir, 'trade_report.csv'), index=True)


def main():
    clear_log()
    m1 = M1()
    m1.process_data()
    m1.generate_features_and_labels()
    m1.factor_system.generate_reports()
    
    m1.train_model()
    for targ in m1.target_cols:
        model_evaluate(m1.y_train_dict[targ], m1.train_pred[targ + '_pred'], 'train')
    for horizon in config.EVALUATE_HORIZONS:
        model_evaluate(m1.mix_df[f"{config.TARGET_LABEL}_{horizon}"][m1.test_data[targ].index],
                    m1.test_pred[targ + '_pred'],'test')
    
    backtester = OrderBookBacktester(m1.mix_df.loc[m1.test_data[targ].index], m1.test_pred)
    m1.results, m1.summary, m1.trade_report = backtester.run_backtest(target_col=targ)
    if config.FACTOR_TEST:
        m1.factor_test_results,m1.shap_values = m1.factor_system.factor_test(m1.train_data[targ], m1.y_train_dict[targ],model=m1.model_trainer.models[targ])
    m1.plot_results()
    m1.save_results()
    log_print(f"分析完成，结果已保存到 {m1.output_dir}")


if __name__ == "__main__":
    main()