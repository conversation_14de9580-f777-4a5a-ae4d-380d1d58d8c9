# -*- coding: utf-8 -*-
 
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta
 
from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData
 
from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random
import math
 
class BasisStrategy(StrategyTemplate):
    """"""
 
    author = "vnpy"
 
    buy_price = 0
    short_price = 0
 
    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'edge',
        'alpha',
        'gamma',
        'typ',
        'maxDelta',
        'minDelta',
        'redifEWMAFlag',
        'useMakerTrend',
        'floatEdgeFlag',
        'minEdge',
        'validVolume',
        'SVMJoinVolume',
        'predict_refer_trend',
        'fair_refer_calculator'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]
 
    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)
 
        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        self.buy_refer_orderids = None
        self.short_refer_orderids = None
        self.buy_max_orderids = None
        self.short_max_orderids = None
        
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair
 
    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.avg_refer = 0
        self.avg_maker = 0
        self.avg_Basis = 0
        self.realPnl_refer = 0
        self.realPnl_maker = 0
        self.realPnl_basis = 0
        self.cumPnl_refer = 0
        self.cumPnl_maker = 0
        self.cumPnl_basis = 0        
        self.net=0
        self.maker_delta = 0
        self.refer_delta = 0
        self.basis_delta = 0
        self.fair_maker = 0
        self.fair_refer = 0
        self.rCount=0
        self.mCount=0        
        self.pauseCount=0
        self.lastRefer={"net":0, "tick":None, "timepoint":0,"tick2":None}        
        self.lastMaker={"pos":0, "tick":None, "timepoint":0,"tick2":None,"tick3":None}
        self.lastTicks={self.maker:0,self.refer:0}
        self.comments  = None
        self.last_bidP = 0
        self.last_askP = 0
        self.ewma = 0
        self.Basis = 0
        self.ewmaCount=0
        self.isQuoting = True
        self.offer_list = []
        self.pos_adjust = 0
        self.offer_list_head = []
        self.onHedgingVolume = 0
        self.hedgeInterval=1
        self.lastDimmerTime=0
        self.isValid = False
 
        self.credit=0.5
        
        self.primer=0
        self.second=0
        self.lastBasis=[]
        
        self.fair_refer_on_trade=False
        self.isValid = False
        self.makerTrend = 0
        self.makerTrendDt = 0
 
        self.pos_adjust_bid = 0
        self.pos_adjust_ask = 0
        
        self.predict_direction = 0
 
        length=99999
        self.maker_list=np.zeros(length) # maker fair price
        self.refer_list=np.zeros(length) # refer fair price
        self.Basis_list=np.zeros(length) # Basis
        self.last_trade_time = 0
 
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")
 
    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")
 
    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
    
            if 'SP' in key:
                continue
            if ticks[key]!=self.lastTicks[key]:
                tick = ticks[key]
                if key==self.maker:
                    # if tick.datetime.time()  > dtime(9,10,9):
                    #     print(1)
                    self.on_tick_maker(ticks[key])
                if key==self.refer:
                    # if tick.datetime.time()  > dtime(9,10,9):
                    #     print(1)
                    self.on_tick_refer(ticks[key])
                    
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick
        
    def getAsk5(self,tick,ask_volume,away_price):
        volume=0
        for i in range(1,6):
            try:
                volume += tick.__getattribute__('ask_volume_%d'%i)
            except:
                volume +=0
            if volume>=ask_volume:
                short_price = tick.__getattribute__('ask_price_%d'%i)
                break
        if volume<ask_volume:
            while i>0:
                try:
                    short_price = tick.__getattribute__('ask_price_%d'%i)+away_price
                    break
                except:
                    i-=1
        return short_price
        
    def getBid5(self,tick,buy_volume,away_price):
        volume=0
        for i in range(1,6):
            try:
                volume += tick.__getattribute__('bid_volume_%d'%i)
            except:
                volume +=0
            if volume>=buy_volume:
                buy_price = tick.__getattribute__('bid_price_%d'%i)
                break
        if volume<buy_volume:
            while i>0:
                try:
                    buy_price = tick.__getattribute__('bid_price_%d'%i) - away_price
                    break
                except:
                    i-=1
        return buy_price
        
    def Validtick(self,tick): #无效的tick不计算ewma
        if self.getAsk5(tick,2*self.lots,10*self.priceticks[self.maker]) - self.getBid5(tick,2*self.lots,10*self.priceticks[self.maker]) > 8*self.priceticks[self.maker]:
            return False
        return True
    
    def redifEWMA(self,tickRefer,tickMaker,lastTickRefer,lastTickMaker,lastTickRefer2,lastTickMaker2):
        mid = np.mean([tickMaker.bid_price_1,tickMaker.ask_price_1])
        MID = (tickRefer.bid_price_1+tickRefer.ask_price_1)/2
        midPre = np.mean([lastTickMaker.bid_price_1,lastTickMaker.ask_price_1])
        MIDPre = np.mean([lastTickRefer.bid_price_1,lastTickRefer.ask_price_1])
        midPre2 = np.mean([lastTickMaker2.bid_price_1,lastTickMaker2.ask_price_1])
        MIDPre2 = np.mean([lastTickRefer2.bid_price_1,lastTickRefer2.ask_price_1])
        basis = mid - MID
        direction = 0 if mid == midPre else 1 if mid > midPre else -1
        DIRECTION = 0 if MID == MIDPre else 1 if MID > MIDPre else -1
        lastDirection = 0 if MIDPre == MIDPre2 else 1 if MIDPre > MIDPre2 else -1
        lastDIRECTION = 0 if MIDPre == MIDPre2 else 1 if MIDPre > MIDPre2 else -1
        dv = tickMaker.volume - tickMaker.volume
        
        signal = ((direction==1)&(DIRECTION==1))|((direction==-1)&(DIRECTION==-1))|((direction==1)&(lastDIRECTION==1))|((direction==-1)&(lastDIRECTION==-1))|((lastDirection==1)&(DIRECTION==1))|((lastDirection==-1)&(DIRECTION==-1))|((dv>90)&(direction!=0))
 
        signal = True
        if not math.isnan(basis) and basis%self.priceticks[self.maker]==0:
            self.lastBasis.append(basis)
        if len(self.lastBasis)<30:
            return math.nan
        ll=self.lastBasis[-10:]
        ll.sort()
        
        if signal and basis%self.priceticks[self.maker]==0:
            
            if self.primer==0: #init
                self.primer==basis
            elif self.second==0: #init
                self.second==basis
            elif basis==self.primer: #和之前的一样
                pass
            elif basis==self.second: #和之前的一样
                pass
            elif abs(basis-self.primer)==self.priceticks[self.maker]: #切换second
                self.second=basis
            elif abs(basis-self.second)==self.priceticks[self.maker]: #切换primer
                self.primer=basis
            elif abs(basis-(self.primer+self.second)/2)==2.5*self.priceticks[self.maker]:
                if basis>self.primer:
                    self.primer=basis
                    second=basis-self.priceticks[self.maker]
                else:
                    self.primer=basis
                    self.second=basis+self.priceticks[self.maker]
            elif abs(basis-(self.primer+self.second)/2)>2.5*self.priceticks[self.maker]:
                pass
            else:
                print('bad 1st')
                exit
                
        elif signal and basis%self.priceticks[self.maker]!=0: #处理.5
            pass
        
            if basis<=max(self.primer,self.second) and basis>=min(self.primer,self.second): #在两者之间
                pass
            elif abs(basis-(self.primer+self.second)/2)==self.priceticks[self.maker] and abs(basis-self.primer)<abs(basis-self.second): #非稳态
                pass
            elif abs(basis-(self.primer+self.second)/2)==self.priceticks[self.maker] and abs(basis-self.primer)>abs(basis-self.second): #非稳态
                pass
            elif abs(basis-self.primer)<abs(basis-self.second): #非稳态skew
                pass
            else:
                pass
            
            
        if ll[0]==ll[-1] and ll[0]!=self.primer:
            if ll[0]==self.second: #switch
                self.second=self.primer
                self.primer=ll[0]
            elif self.primer==0: #init
                self.primer=ll[0]
            elif self.second==0 and ll[0]!=self.primer: #init
                self.second=self.primer
                self.primer=ll[0]
            elif abs(ll[0]-self.primer)==self.priceticks[self.maker]:
                self.second=self.primer
                self.primer=ll[0]
            elif abs(ll[0]-self.primer)==2*self.priceticks[self.maker] and abs(ll[0]-self.second)==self.priceticks[self.maker]:
                #second=primer
                self.primer=ll[0]
            elif abs(basis-(self.primer+self.second)/2)>=1.5*self.priceticks[self.maker]:
                pass
            elif self.second!=0:
                print('bad 2rd')
                exit
                
        return np.mean([self.primer,self.second])
        
    def on_tick_refer(self,tick):
        self.rCount +=1 
        if self.MMTrading and not self.strategy_engine.refer_test:
            self.on_position() #先平delta 
 
        ts = self.priceticks[self.refer]
        net = self.net  
        multiplier = self.sizes[self.maker]
 
        if not self.fair_refer_on_trade and self.lastRefer['tick']:
            microPrice = (tick.bid_price_1 * tick.ask_volume_1 + tick.ask_price_1 * tick.bid_volume_1) / (tick.bid_volume_1 + tick.ask_volume_1) 
            midPrice = (tick.bid_price_1 + tick.ask_price_1) / 2
            refer_trend_volume = self.SVMJoinVolume
            dv = tick.volume - self.lastRefer['tick'].volume
            dt = tick.turnover - self.lastRefer['tick'].turnover
            average_p = dt/dv/multiplier if dv > 0 else 0
            buy_sell_rate = 0.5
            if self.lastRefer['tick'].ask_price_1 == self.lastRefer['tick'].bid_price_1:
                buy_sell_rate = 0.5
            else:
                buy_sell_rate = max( min( (average_p - self.lastRefer['tick'].bid_price_1) / (self.lastRefer['tick'].ask_price_1 - self.lastRefer['tick'].bid_price_1) , 1.0) , 0.0)
            buy_volume = dv * buy_sell_rate
            sell_volume = dv * (1 - buy_sell_rate)
        
            if self.predict_refer_trend and (self.rCount>=2) and \
                    (buy_volume > refer_trend_volume and tick.ask_volume_1 < refer_trend_volume) and \
                    (sell_volume <= refer_trend_volume or tick.bid_volume_1 >= refer_trend_volume):
                self.fair_refer = tick.ask_price_1 + ts
                self.comments = "predict"
                self.predict_direction = 1 
            elif self.predict_refer_trend and (self.rCount>=2) and \
                    (sell_volume > refer_trend_volume and tick.bid_volume_1 < refer_trend_volume) and \
                    (buy_volume <= refer_trend_volume or tick.ask_volume_1 >= refer_trend_volume) :
                self.fair_refer = tick.bid_price_1 - ts
                self.comments = "predict"
                self.predict_direction = -1 
            elif self.fair_refer_calculator == 0:
                self.fair_refer = microPrice
                self.comments = "MM"
                self.predict_direction = 0
            elif self.fair_refer_calculator == 1:        
                self.fair_refer = midPrice
                self.comments = "MM"
                self.predict_direction = 0
            else:
                if tick.ask_price_1 - tick.bid_price_1 > ts:
                    self.fair_refer = midPrice
                    self.comments = "MM"
                    self.predict_direction = 0
                else:
                    self.fair_refer = microPrice
                    self.comments = "MM"
                    self.predict_direction = 0  
                    
        if self.fair_refer_on_trade:
            self.fair_refer_on_trade = False
 
        if self.predict_direction!=0 and self.isQuoting:
            self.isQuoting = False
            self.pauseCount = self.rCount + 1
 
        shortResendFlag=False
        buyResendFlag=False
        
        # if self.buy_hedge_orderids or self.short_hedge_orderids:
        #     self.cancel_all()
        #     self.buy_hedge_orderids = []
        #     self.short_hedge_orderids = [] 
        
        # if self.net > 0:
        #     self.short_hedge_orderids = self.short(self.refer,tick.bid_price_1, abs(self.net),'referHedge')
        # if self.net < 0:
        #     self.buy_hedge_orderids = self.buy(self.refer,tick.ask_price_1, abs(self.net),'referHedge')        
        
        #1. Filter ：非可控情景暂停 
        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
            # 持仓超限暂停 、在途订单超限暂停、
            # if abs(net)>1.5*self.maxPos:
            #     self.isQuoting=False 
            #     self.pauseCount=self.rCount+10      
            #     print("Net Position limit pause",tick.datetime+timedelta(hours=8))
            # if len(self.strategy_engine.active_limit_orders)>10:     #在途订单超限暂停 or mdDelay>mdDelayLimit or tdDelay>tdDelayLimit 
            #     self.isQuoting=False 
            #     self.pauseCount=self.rCount+10    
            #     print("Delay limit pause",tick.datetime+timedelta(hours=8))
            # market cross pause
            if self.rCount>5 and (tick.ask_price_1 < self.lastRefer["tick"].bid_price_1 -ts or tick.bid_price_1 > self.lastRefer["tick"].ask_price_1 +ts):
                self.isQuoting=False 
                self.pauseCount=self.rCount+1   
                # print("refer gap limit pause",tick.datetime) #   
            # near to market limit price  
            # if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (
            #     tick.bid_price_1 > float(tick.limit_up) - 10*ts or  # 涨停附近                   
            #     tick.ask_price_1 < float(tick.limit_down) + 10*ts):   # 跌停附近                  
            #         self.isQuoting=False
            #         self.pauseCount=self.rCount + 100
            #         print("price limit pause", tick.datetime+timedelta(hours=8))
        # else:
        #     if self.pauseCount<self.rCount and abs(net)<1.5*self.maxPos:
        #         self.isQuoting = True
        #     else:
        #         if self.buy_vt_orderids:
        #             self.cancel_order(self.buy_vt_orderids[0])
        #         if self.short_vt_orderids:
        #             self.cancel_order(self.short_vt_orderids[0])
                
        # 2. Quote：报价
 
        if self.rCount > 10 and self.isValid: #启动后10个tick开始定价,对有效的tick进行定价
            self.Basis = self.fair_maker - self.fair_refer
            self.Basis_list[self.rCount] = self.Basis
            if self.ewma == 0 :
                self.ewma = self.Basis
            elif not self.makerTrend:
                self.ewma = (1-self.alpha) * self.ewma + self.alpha * self.Basis
            else:
                self.ewma = self.ewma #makerTrend 锁定ewma
            if self.ewmaCount < 30+1/self.alpha:
                self.ewmaCount+=1
            if self.redifEWMAFlag:
                self.ewma = self.redifEWMA(tick,self.lastMaker['tick'],self.lastRefer['tick'],self.lastMaker['tick2'],self.lastRefer['tick2'],self.lastMaker['tick3'])
        elif self.ewmaCount>0:
            self.ewmaCount-=1
 
        if self.floatEdgeFlag and self.maker in self.strategy_engine.ticks:
            tick_maker = self.lastMaker['tick']
            if tick_maker:
                marketSpread = (self.getAsk5(tick_maker, self.validVolume, 0) - self.getBid5(tick_maker, self.validVolume, 0)) / ts
                self.edge = max(int(self.minEdge),int(marketSpread))
            else:
                self.edge = 10 # 补丁
 
        if self.ewmaCount > 10+1/self.alpha and not math.isnan(self.ewma):  #开始报价 （若alpha = 0.02,则用50个tick计算初始Basis）
        
            self.theto = self.fair_refer + self.ewma
            theto = self.theto
 
            # pos_adjust = -self.gamma*(self.get_pos(self.maker)/self.lots)*ts  
            if self.alpha<=0 or self.alpha>=1:
                self.adjustRatio = 0.99
            else:
                self.adjustRatio = 1 - self.alpha
 
            self.pos_adjust_bid = self.pos_adjust_bid * self.adjustRatio if self.pos_adjust_bid < -0.1 else 0
            self.pos_adjust_ask = self.pos_adjust_ask * self.adjustRatio if self.pos_adjust_ask > 0.1 else 0
            # self.pos_adjust_bid = self.pos_adjust_bid * self.adjustRatio
            # self.pos_adjust_ask = self.pos_adjust_ask * self.adjustRatio       
 
            # if self.makerTrend:
            #      self.bidP = max(theto - 0.5*self.edge*ts,  self.avg + self.ewma - ts)
            # else:
            #     self.bidP = theto - 0.5*self.edge*ts + pos_adjust
            # if self.makerTrend and self.net>0:
            #     self.askP = min(theto + 0.5*self.edge*ts,  self.avg + self.ewma + ts)
            # else:
            #     self.askP = theto + 0.5*self.edge*ts + pos_adjust
 
            if self.predict_direction!=0:
                self.comments = 'predict'
                if self.predict_direction>0:
                    self.bidP = theto - 0.5 * self.edge * ts + self.pos_adjust_bid
                    self.askP = tick.ask_price_1 + 100*ts
                else:
                    self.askP = theto + 0.5 * self.edge * ts + self.pos_adjust_ask
                    self.bidP = tick.bid_price_1 - 100*ts            
            else:
                self.bidP = theto - 0.5 * self.edge * ts + self.pos_adjust_bid
                self.askP = theto + 0.5 * self.edge * ts + self.pos_adjust_ask
 
            self.bidP = ts*round(self.bidP/ts+0.000000001) 
            self.askP = ts*round(self.askP/ts+0.000000001)  
            
            tick_maker = self.lastMaker['tick']
            
            # if self.bidP > tick_maker.bid_price_1 and self.net >0:
            #     self.bidP = tick_maker.bid_price_1
 
            # if self.askP < tick_maker.ask_price_1 and self.net <0:
            #     self.askP = tick_maker.ask_price_1
                
 
            if self.last_askP != self.askP:
                shortResendFlag=True
            if self.last_bidP != self.bidP:
                buyResendFlag=True
 
            self.last_bidP = self.bidP
            self.last_askP = self.askP
 
        self.refer_list[self.rCount] = self.fair_refer
        
        dt = tick.datetime
 
        #1. taker 模式，套利
        if self.typ=='taker' and self.MMTrading and self.ewmaCount > 10+1/self.alpha : #若流动性差 或者刚开盘和收盘 不报价
            self.cancel_all()
 
            if self.bidP >= self.strategy_engine.ticks[self.maker].ask_price_1:
                if self.get_pos(self.maker) < self.maxPos:
                    if not self.buy_vt_orderids:
                            self.buy_vt_orderids = self.buy(self.maker,self.strategy_engine.ticks[self.maker].ask_price_1, self.lots,self.comments)
                    elif buyResendFlag and self.buy_vt_orderids[0] :
                            if self.cancel_order(self.buy_vt_orderids[0]):  
                                self.buy_vt_orderids = self.buy(self.maker,self.strategy_engine.ticks[self.maker].ask_price_1, self.lots,self.comments)
 
            if self.askP <= self.strategy_engine.ticks[self.maker].bid_price_1:
                if self.get_pos(self.maker) > -self.maxPos:      
                    if not self.short_vt_orderids:
                            self.short_vt_orderids = self.short(self.maker,self.strategy_engine.ticks[self.maker].bid_price_1, self.lots,self.comments)
                    elif shortResendFlag and self.short_vt_orderids[0] :
                            if self.cancel_order(self.short_vt_orderids[0]): 
                                self.short_vt_orderids = self.short(self.maker,self.strategy_engine.ticks[self.maker].bid_price_1, self.lots,self.comments)
        
        #2. maker 模式，正常MM
        elif self.typ=='maker' and self.MMTrading and self.ewmaCount > 10+1/self.alpha and self.isQuoting: #若流动性差 或者刚开盘和收盘 不报价
 
            if self.bidP >= self.lastMaker['tick'].ask_price_1:
                if self.get_pos(self.maker) < 1.5*self.maxPos: #持仓正常才报价
                    if not self.buy_vt_orderids:
                            self.buy_vt_orderids = self.buy(self.maker,self.strategy_engine.ticks[self.maker].ask_price_1, self.lots,self.comments)
                    elif buyResendFlag and self.buy_vt_orderids[0] :
                            if self.cancel_order(self.buy_vt_orderids[0]):  
                                self.buy_vt_orderids = self.buy(self.maker,self.strategy_engine.ticks[self.maker].ask_price_1, self.lots,self.comments)
            else:
                if self.get_pos(self.maker) < 1.5*self.maxPos: #持仓正常才报价
                    if not self.buy_vt_orderids:
                            self.buy_vt_orderids = self.buy(self.maker,self.bidP, self.lots,self.comments)
                    elif buyResendFlag and self.buy_vt_orderids[0] :
                            if self.cancel_order(self.buy_vt_orderids[0]):  
                                self.buy_vt_orderids = self.buy(self.maker,self.bidP, self.lots,self.comments)
                
            if self.askP <= self.lastMaker['tick'].bid_price_1:
                if self.get_pos(self.maker) > -1.5*self.maxPos:      
                    if not self.short_vt_orderids:
                            self.short_vt_orderids = self.short(self.maker,self.strategy_engine.ticks[self.maker].bid_price_1, self.lots,self.comments)
                    elif shortResendFlag and self.short_vt_orderids[0] :
                            if self.cancel_order(self.short_vt_orderids[0]): 
                                self.short_vt_orderids = self.short(self.maker,self.strategy_engine.ticks[self.maker].bid_price_1, self.lots,self.comments)   
            else:
                if self.get_pos(self.maker) > -1.5*self.maxPos:      
                    if not self.short_vt_orderids:
                            self.short_vt_orderids = self.short(self.maker,self.askP, self.lots,self.comments)
                    elif shortResendFlag and self.short_vt_orderids[0] :
                            if self.cancel_order(self.short_vt_orderids[0]): 
                                self.short_vt_orderids = self.short(self.maker,self.askP, self.lots,self.comments)   
 
        #3. 保守 maker 模式, shfe外层MM
        elif self.typ=='maker_conservative' and self.MMTrading and self.ewmaCount > 10+1/self.alpha: #若流动性差 或者刚开盘和收盘 不报价
 
            if self.bidP >= self.strategy_engine.ticks[self.maker].bid_price_1-ts:
                if self.get_pos(self.maker) < self.maxPos:
                    if not self.buy_vt_orderids:
                            self.buy_vt_orderids = self.buy(self.maker, self.strategy_engine.ticks[self.maker].bid_price_1-ts, self.lots,self.comments)
                    elif buyResendFlag and self.buy_vt_orderids[0] :
                            if self.cancel_order(self.buy_vt_orderids[0]):  #cancel完会跳转至onOrder，然后自动发一个单                                                                
                                self.buy_vt_orderids = self.buy(self.maker, self.strategy_engine.ticks[self.maker].bid_price_1-ts, self.lots,self.comments)
            else:
                if self.get_pos(self.maker) < self.maxPos:
                    if not self.buy_vt_orderids:
                            p = np.min([np.max([self.bidP, self.strategy_engine.ticks[self.maker].ask_price_1+ts-self.edge*ts]), self.strategy_engine.ticks[self.maker].bid_price_1-ts])
                            self.buy_vt_orderids = self.buy(self.maker, p, self.lots,self.comments)
                    elif buyResendFlag and self.buy_vt_orderids[0] :
                            if self.cancel_order(self.buy_vt_orderids[0]):  #cancel完会跳转至onOrder，然后自动发一个单
                                p = np.min([np.max([self.bidP, self.strategy_engine.ticks[self.maker].ask_price_1+ts-self.edge*ts]), self.strategy_engine.ticks[self.maker].bid_price_1-ts])
                                self.buy_vt_orderids = self.buy(self.maker, p, self.lots,self.comments)
                
 
            if self.askP <= self.strategy_engine.ticks[self.maker].ask_price_1:
                if self.get_pos(self.maker) > -self.maxPos:      
                    if not self.short_vt_orderids:
                            self.short_vt_orderids = self.short(self.maker,self.strategy_engine.ticks[self.maker].ask_price_1+ts, self.lots,self.comments)
                    elif shortResendFlag and self.short_vt_orderids[0] :
                            if self.cancel_order(self.short_vt_orderids[0]): #cancel完会跳转至onOrder，然后自动发一个单
                                self.short_vt_orderids = self.short(self.maker,self.strategy_engine.ticks[self.maker].ask_price_1+ts, self.lots,self.comments)   
            else:
                if self.get_pos(self.maker) > -self.maxPos:      
                    if not self.short_vt_orderids:
                            p = np.max([np.min([self.askP, self.strategy_engine.ticks[self.maker].bid_price_1+self.edge*ts]), self.strategy_engine.ticks[self.maker].ask_price_1+ts])
                            self.short_vt_orderids = self.short(self.maker, p, self.lots,self.comments)
                    elif shortResendFlag and self.short_vt_orderids[0] :
                            if self.cancel_order(self.short_vt_orderids[0]): #cancel完会跳转至onOrder，然后自动发一个单
                                p = np.max([np.min([self.askP, self.strategy_engine.ticks[self.maker].bid_price_1+self.edge*ts]), self.strategy_engine.ticks[self.maker].ask_price_1+ts])
                                self.short_vt_orderids = self.short(self.maker, p, self.lots,self.comments) 
                                
        # 4. 较保守模式, 不超过盘口
        elif self.typ=='' and self.MMTrading and self.ewmaCount > 10+1/self.alpha : #若流动性差 或者刚开盘和收盘 不报价
            if self.askP-self.bidP < self.strategy_engine.ticks[self.maker].ask_price_1-self.strategy_engine.ticks[self.maker].bid_price_1:
                if self.short_vt_orderids:
                    self.cancel_order(self.short_vt_orderids[0])
                if self.buy_vt_orderids:
                    self.cancel_order(self.buy_vt_orderids[0])
            else:
                adj_p = 0
 
                if self.askP < self.strategy_engine.ticks[self.maker].ask_price_1:
                    adj_p = self.strategy_engine.ticks[self.maker].ask_price_1-self.askP
                if self.bidP > self.strategy_engine.ticks[self.maker].bid_price_1:
                    adj_p = self.strategy_engine.ticks[self.maker].bid_price_1-self.bidP
                    
 
 
                if self.get_pos(self.maker) < self.maxPos: #持仓正常才报价
                    if not self.buy_vt_orderids:
                            self.buy_vt_orderids = self.buy(self.maker,self.bidP+adj_p, self.lots,self.comments)
                    elif buyResendFlag and self.buy_vt_orderids[0] :
                            if self.cancel_order(self.buy_vt_orderids[0]):  
                                self.buy_vt_orderids = self.buy(self.maker,self.bidP+adj_p, self.lots,self.comments)
                  
 
                if self.get_pos(self.maker) > -self.maxPos:      
                    if not self.short_vt_orderids:
                            self.short_vt_orderids = self.short(self.maker,self.askP+adj_p, self.lots,self.comments)
                    elif shortResendFlag and self.short_vt_orderids[0] :
                            if self.cancel_order(self.short_vt_orderids[0]): 
                                self.short_vt_orderids = self.short(self.maker,self.askP+adj_p, self.lots,self.comments)  
    
        # Save：数据存储                    
        self.lastRefer["timepoint"]=tick.datetime
        self.lastRefer["net"]=self.net
        self.lastRefer["tick"]=tick
        self.lastRefer["tick2"]=self.lastRefer["tick"]
 
        self.cumPnl_basis = round(self.basis_delta*self.Basis + self.realPnl_basis)*multiplier
        self.cumPnl_maker = round(self.maker_delta*self.fair_maker + self.realPnl_maker)*multiplier
        self.cumPnl_refer = round(self.refer_delta*self.fair_refer + self.realPnl_refer)*multiplier
        
        self.offer_list.append([tick.datetime,self.cumPnl_basis,self.cumPnl_maker,self.cumPnl_refer])
        self.offer_list_head = ['datetime','cumPnl_basis','cumPnl_maker','cumPnl_refer']
            
    def on_tick_maker(self, tick):
 
        self.mCount += 1
        ts = self.priceticks[self.maker]
        multiplier =  self.sizes[self.maker]
        net = self.net
        lots = self.lots
        maxV=50
        
        if self.isQuoting:  # 最好时间驱动， 否则主力和做市合约都要运行 
            # 持仓超限暂停 、在途订单超限暂停、
            if abs(net)>1.5*self.maxPos:
                self.isQuoting=False 
                self.pauseCount=self.rCount+10      
                # print("Net Position limit pause",tick.datetime)
            # if len(self.strategy_engine.active_limit_orders)>10:     #在途订单超限暂停 or mdDelay>mdDelayLimit or tdDelay>tdDelayLimit 
            #     self.isQuoting=False 
            #     self.pauseCount=self.rCount+10    
            #     print("Delay limit pause",tick.datetime+timedelta(hours=8))
            # market cross pause
            if self.rCount > 5 and self.lastMaker['tick'] and (tick.ask_price_1 < self.lastMaker["tick"].bid_price_1 -ts or tick.bid_price_1 > self.lastMaker["tick"].ask_price_1 +ts):
                self.isQuoting=False 
                self.pauseCount=self.rCount+1   
                # print("maker gap limit pause",tick.datetime) #   
            # near to market limit price  
            # if (tick.limit_up and tick.limit_down) and (tick.ask_volume_1>0 and tick.bid_volume_1>0 ) and (
            #     tick.bid_price_1 > float(tick.limit_up) - 10*ts or  # 涨停附近                   
            #     tick.ask_price_1 < float(tick.limit_down) + 10*ts):   # 跌停附近                  
            #         self.isQuoting=False
            #         self.pauseCount=self.rCount + 100
            #         print("price limit pause", tick.datetime+timedelta(hours=8))
        else:
            if self.pauseCount<self.rCount and abs(net)<1.5*self.maxPos:
                self.isQuoting = True
            else:
                if self.buy_vt_orderids:
                    self.cancel_order(self.buy_vt_orderids[0])
                if self.short_vt_orderids:
                    self.cancel_order(self.short_vt_orderids[0])
        
        if self.buy_hedge_orderids or self.short_hedge_orderids:
            self.cancel_all()
            self.buy_hedge_orderids = []
            self.short_hedge_orderids = [] 
        
        self.isValid = self.Validtick(tick)
        
        if ((tick.datetime.time() > dtime(14, 55) and tick.datetime.time() < dtime(14, 56))
            or (tick.datetime.time() > dtime(22, 55) and tick.datetime.time() < dtime(22, 56))): 
            self.cancel_all()
            if self.get_pos(self.maker)!= 0:  
                if self.get_pos(self.maker) >0:
                    self.short_hedge_orderids = self.short(self.maker,tick.bid_price_1,abs(self.get_pos(self.maker)),'hedge')
                else:
                    self.buy_hedge_orderids = self.buy(self.maker,tick.ask_price_1,abs(self.get_pos(self.maker)),'hedge')
                
        
        spread = tick.ask_price_1 - tick.bid_price_1
        away_price = ts  # away_price暂时设定为1个tick
        bidP = self.getBid5(tick,self.validVolume, away_price)
        askP = self.getAsk5(tick,self.validVolume, away_price)         #   L2行情更稳定、L1行情偏积极 
        self.fair_maker = self.getFairPrice(self.fair_maker,askP,bidP, self.edge*ts) 
        self.fair_maker = self.getFairPrice(self.fair_maker,tick.ask_price_1,tick.bid_price_1, 3*ts)   
        self.maker_list[self.mCount] = self.fair_maker
                    
        self.lastMaker["timepoint"]=tick.datetime
        self.lastMaker["pos"]=self.get_pos(self.maker)
        self.lastMaker["tick3"]=self.lastRefer["tick2"]
        self.lastMaker["tick2"]=self.lastRefer["tick"]
        self.lastMaker["tick"]=tick
        
        
        
    def  on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price 
        lots=self.lots   
        if volume > 0:
            if trade.direction.value=='多':
                if self.net>=0 :            
                     self.avg = (self.net*self.avg +volume*self.fair_refer)/(self.net+volume)              
                elif volume+self.net>0: # net<0 # 平仓
                     self.avg = self.fair_refer         
                self.net += volume 
            elif trade.direction.value=='空':    
                if self.net<=0:
                    self.avg =(-self.net*self.avg + volume*self.fair_refer)/(-self.net+volume)
                elif volume-self.net>0: # net >0 # 平仓
                    self.avg=self.fair_refer
                self.net -= volume 
        if trade.comments=='referHedge':
            self.onHedgingVolume-= volume if trade.direction.value=='多' else -volume
        if not self.strategy_engine.refer_test:
            self.on_position()
            self.on_hedge()
        if trade.symbol+'.'+trade.exchange.value==self.maker:
            self.onMMTrade(trade)
        if trade.symbol+'.'+trade.exchange.value==self.refer:
            self.onReferTrade(trade)
 
        trade.midPrice = self.fair_maker
        trade.basePrice = self.fair_refer    
        
        if trade.symbol+'.'+trade.exchange.value==self.refer:
            trade.midPrice = self.fair_refer
 
    def onMMTrade(self, trade: TradeData):
        volume=trade.volume
        price=trade.price 
        lots=self.lots
        
        if volume > 0:
            if trade.direction.value=='多':
                self.pos_adjust_bid = self.pos_adjust_bid - self.gamma*volume/lots  
                self.pos_adjust_ask = self.pos_adjust_ask - self.gamma*volume/lots
            if trade.direction.value=='空':
                self.pos_adjust_bid = self.pos_adjust_bid + self.gamma*volume/lots  
                self.pos_adjust_ask = self.pos_adjust_ask + self.gamma*volume/lots 
        
        if volume > 0: #计算成本
            if trade.direction.value=='多':
                if self.refer_delta < 0: #组合Basis
                    price_Basis = price - self.avg_refer    
                    volume_Basis = min(abs(self.refer_delta),volume)      
                    if self.basis_delta>=0 : 
                        self.avg_Basis = (self.basis_delta*self.avg_Basis +volume_Basis*price_Basis)/(self.basis_delta+volume_Basis)              
                    elif volume_Basis+self.basis_delta>0: # net<0 # 平仓
                        self.avg_Basis = price_Basis     
                    self.basis_delta += volume_Basis
                    self.realPnl_basis -= volume_Basis * price_Basis
                    self.refer_delta += volume_Basis    
                    self.realPnl_refer -= volume_Basis * self.avg_refer
                    if volume - volume_Basis > 0:
                        if self.maker_delta>=0 :            
                            self.avg_maker = (self.maker_delta*self.avg_maker +(volume - volume_Basis)*price)/(self.maker_delta+volume - volume_Basis)              
                        elif volume - volume_Basis+self.maker_delta>0: # net<0 # 平仓
                            self.avg_maker = price 
                        self.maker_delta += volume - volume_Basis
                        self.realPnl_maker -= (volume - volume_Basis) * price
                                           
                else:
                    if self.maker_delta>=0 :            
                        self.avg_maker = (self.maker_delta*self.avg_maker +volume*price)/(self.maker_delta+volume)              
                    elif volume+self.maker_delta>0: # net<0 # 平仓
                        self.avg_maker = price       
                    self.maker_delta += volume 
                    self.realPnl_maker -= volume  * price   
            elif trade.direction.value=='空':  
                if self.refer_delta > 0:
                    price_Basis = price - self.avg_refer    
                    volume_Basis = min(abs(self.refer_delta),volume)      
                    if self.basis_delta<=0:
                        self.avg_Basis =(-self.basis_delta*self.avg_Basis + volume_Basis*price_Basis)/(-self.basis_delta+volume_Basis)
                    elif volume_Basis-self.basis_delta>0: # net >0 # 平仓
                        self.avg_Basis=price_Basis
                    self.basis_delta -= volume_Basis 
                    self.realPnl_basis += volume_Basis * price_Basis
                    self.refer_delta -= volume_Basis 
                    self.realPnl_refer += volume_Basis * self.avg_refer
                    if volume - volume_Basis > 0:
                        if self.maker_delta<=0:
                            self.avg_maker =(-self.maker_delta*self.avg_maker +(volume - volume_Basis)*price)/(-self.maker_delta+(volume - volume_Basis))
                        elif (volume - volume_Basis)-self.maker_delta>0: # net >0 # 平仓
                            self.avg_maker=price
                        self.maker_delta -= volume - volume_Basis
                        self.realPnl_maker += (volume - volume_Basis) * price
                else:
                    if self.maker_delta<=0:
                        self.avg_maker =(-self.maker_delta*self.avg_maker + volume*price)/(-self.maker_delta+volume)
                    elif volume-self.maker_delta>0: # net >0 # 平仓
                        self.avg_maker=price
                    self.maker_delta -= volume
                    self.realPnl_maker += (volume) * price
 
    def onReferTrade(self, trade: TradeData):
        volume=trade.volume
        price=trade.price 
        lots=self.lots
        tick = self.lastRefer["tick"]
        refer_trend_volume = self.SVMJoinVolume
        ts = self.priceticks[self.maker]
 
        if volume > 0: #计算成本
            if trade.direction.value=='空':
                if self.maker_delta > 0: #refer空头，认为基差拿的正持仓
                    price_Basis = self.avg_maker - price    
                    volume_Basis = min(abs(self.maker_delta),volume)      
                    if self.basis_delta>=0 : 
                        self.avg_Basis = (self.basis_delta*self.avg_Basis +volume_Basis*price_Basis)/(self.basis_delta+volume_Basis)              
                    elif volume_Basis+self.basis_delta>0: # net<0 # 平仓
                        self.avg_Basis = price_Basis     
                    self.basis_delta += volume_Basis
                    self.realPnl_basis -= volume_Basis * price_Basis 
                    self.maker_delta -= volume_Basis
                    self.realPnl_maker += volume_Basis * self.avg_maker
                    if volume-volume_Basis > 0:
                        if self.refer_delta<=0:
                            self.avg_refer =(-self.refer_delta*self.avg_refer + (volume-volume_Basis)*price)/(-self.refer_delta+(volume-volume_Basis))
                        elif (volume-volume_Basis)-self.refer_delta>0: # net >0 # 平仓
                            self.avg_refer=price    
                        self.refer_delta -= volume - volume_Basis
                        self.realPnl_refer += (volume - volume_Basis) * price
                else:
                    
                    if self.refer_delta<=0:
                        self.avg_refer =(-self.refer_delta*self.avg_refer + (volume)*price)/(-self.refer_delta+(volume))
                    elif (volume)-self.refer_delta>0: # net >0 # 平仓
                        self.avg_refer=price 
                    self.refer_delta -= volume
                    self.realPnl_refer += (volume) * price
                     
            elif trade.direction.value=='多' :
                if self.maker_delta < 0: #refer多头，认为基差拿的负持仓
                    price_Basis = self.avg_maker - price   
                    volume_Basis = min(abs(self.maker_delta),volume)                 
                    if self.basis_delta<=0:
                        self.avg_Basis =(-self.basis_delta*self.avg_Basis + volume_Basis*price_Basis)/(-self.basis_delta+volume_Basis)
                    elif volume_Basis-self.basis_delta>0: # net >0 # 平仓
                        self.avg_Basis=price_Basis
                    self.basis_delta -= volume_Basis 
                    self.realPnl_basis += volume_Basis * price_Basis 
                    self.maker_delta += volume_Basis
                    self.realPnl_maker -= volume_Basis * self.avg_maker
                    if volume-volume_Basis > 0:
                        if self.refer_delta>=0 :            
                            self.avg_refer = (self.refer_delta*self.avg_refer +(volume-volume_Basis)*price)/(self.refer_delta+(volume-volume_Basis))              
                        elif (volume-volume_Basis)+self.refer_delta>0: # net<0 # 平仓
                            self.avg_refer = price  
                        self.refer_delta += volume - volume_Basis    
                        self.realPnl_refer -= (volume - volume_Basis) * price              
 
                else:
                    if self.refer_delta>=0 :            
                        self.avg_refer = (self.refer_delta*self.avg_refer +(volume)*price)/(self.refer_delta+(volume))              
                    elif (volume)+self.refer_delta>0: # net<0 # 平仓
                        self.avg_refer = price 
                    self.refer_delta += volume
                    self.realPnl_refer -= (volume) * price
 
        if trade.comments in ['referDetection','secondConfirmation','secondConfirmationReverse']:
            self.fair_refer_on_trade = True
            if self.predict_refer_trend:
                total_volume = 0
                total_volume_comfirmed = 0
                if volume > 0:
                    if trade.direction.value=='空':
                        for i in range(1,5):
                            total_volume = total_volume + tick.__getattribute__('ask_volume_%d'%i)
                            if total_volume > refer_trend_volume and self.isQuoting: 
                                self.isQuoting = False
                                self.pauseCount = self.rCount + 1
                            if price == tick.__getattribute__('ask_price_%d'%i) and  trade.comments == 'secondConfirmation':
                                total_volume_comfirmed = total_volume_comfirmed + tick.__getattribute__('ask_volume_%d'%i)
                            if (price == tick.__getattribute__('ask_price_%d'%i)) and\
                                (total_volume_comfirmed > refer_trend_volume) and\
                                    (tick.__getattribute__('ask_volume_%d'%(i+1)) < refer_trend_volume):
                                self.fair_refer = price + ts
                                self.comments = "predict"
                                self.predict_direction = 1 
                                break
                            elif price == tick.__getattribute__('ask_price_%d'%i):
                                if trade.datetime == self.last_trade_time:
                                    self.fair_refer = max(price,self.fair_refer)
                                else:  
                                    self.fair_refer = price
                                self.comments = "fairTradedMM"
                                self.predict_direction = 0
                                break
                            total_volume_comfirmed = total_volume_comfirmed + tick.__getattribute__('ask_volume_%d'%i)
                    if trade.direction.value=='多':
                        for i in range(1,5):
                            total_volume = total_volume + tick.__getattribute__('bid_volume_%d'%i)
                            if total_volume > refer_trend_volume and self.isQuoting: 
                                self.isQuoting = False
                                self.pauseCount = self.rCount + 1
                            if price == tick.__getattribute__('bid_price_%d'%i) and  trade.comments == 'secondConfirmation':
                                total_volume_comfirmed = total_volume_comfirmed + tick.__getattribute__('bid_volume_%d'%i)
                            if (price == tick.__getattribute__('bid_price_%d'%i)) and\
                                (total_volume_comfirmed > refer_trend_volume) and\
                                    (tick.__getattribute__('bid_volume_%d'%(i+1)) < refer_trend_volume):
                                self.fair_refer = price - ts
                                self.comments = "predict"
                                self.predict_direction = -1 
                                break
                            elif price == tick.__getattribute__('bid_price_%d'%i):
                                if trade.datetime == self.last_trade_time:
                                    self.fair_refer = min(price,self.fair_refer)
                                else:  
                                    self.fair_refer = price
                                self.comments = "fairTradedMM"
                                self.predict_direction = 0
                                break
                            total_volume_comfirmed = total_volume_comfirmed + tick.__getattribute__('bid_volume_%d'%i)       
            else:                       
                    self.fair_refer = price
                    self.comments = "fairTradedMM"
                    self.predict_direction = 0
            self.last_trade_time = trade.datetime        
            
            self.on_tick_refer(self.lastRefer["tick"])
    
    def on_position(self):
        #激进对冲
        totalDelta = self.net
        if totalDelta + self.onHedgingVolume>=self.maxDelta or (totalDelta>=self.maxDelta and self.rCount-self.hedgeInterval> self.lastDimmerTime):
            if self.short_refer_orderids and self.short_refer_orderids[0]:
                self.cancel_order(self.short_refer_orderids[0])
            comments='referHedge'
            volume = round(abs(totalDelta)-self.minDelta/2)
            price = self.lastRefer["tick"].ask_price_1-self.priceticks[self.refer]
            self.short_refer_orderids = self.short(self.refer, price, volume,comments)
            self.lastReferSellPrice = price
            self.onHedgingVolume = -volume
            self.lastDimmerTime = self.rCount
            # print('dimmer sell: %s %d %d'%(self.refer,price,volume))
 
        elif totalDelta+self.onHedgingVolume<=-self.maxDelta or (totalDelta<=-self.maxDelta and self.rCount-self.hedgeInterval> self.lastDimmerTime):
            if self.buy_refer_orderids and self.buy_refer_orderids[0]:
                self.cancel_order(self.buy_refer_orderids[0])
            comments='referHedge'
            volume = round(abs(totalDelta)-self.minDelta/2)
            price = self.lastRefer["tick"].bid_price_1+self.priceticks[self.refer]
            self.buy_refer_orderids = self.buy(self.refer, price, volume,comments)
            self.lastReferBuyPrice=price
            self.onHedgingVolume = volume
            self.lastDimmerTime = self.rCount
            # print('dimmer buy: %s %d %d'%(self.refer,price,volume))
            
    def on_hedge(self ):
        totalDelta = self.net
        #柔和对冲
        if totalDelta + self.onHedgingVolume>=self.minDelta and totalDelta<self.maxDelta:
            if self.short_refer_orderids and self.short_refer_orderids[0]:
                self.cancel_order(self.short_refer_orderids[0])
            comments='referHedge'
            volume = round(abs(totalDelta)-self.minDelta/2+0.0000000001)
            self.onHedgingVolume = -volume
            price = round((self.avg + self.credit*self.priceticks[self.refer])/self.priceticks[self.refer]+0.000000001)*self.priceticks[self.refer]
            self.short_refer_orderids = self.short(self.refer, price, volume,comments)
            self.lastReferSellPrice = price
            # print('hedge sell: %s %d %d'%(self.refer,price,volume))
            
        elif totalDelta + self.onHedgingVolume <=-self.minDelta and totalDelta>-self.maxDelta:
            if self.buy_refer_orderids and self.buy_refer_orderids[0]:
                self.cancel_order(self.buy_refer_orderids[0])
            comments='referHedge'
            volume = round(abs(totalDelta)-self.minDelta/2+0.0000000001)
            self.onHedgingVolume = volume
            price = round((self.avg - self.credit*self.priceticks[self.refer])/self.priceticks[self.refer]+0.000000001)*self.priceticks[self.refer]
            self.buy_refer_orderids = self.buy(self.refer, price, volume,comments)
            self.lastReferBuyPrice=price
            # print('hedge buy: %s %d %d'%(self.refer,price,volume))
                 
    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return
 
        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids,
            self.buy_refer_orderids,
            self.short_refer_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
 
    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """ # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if not(
            (dt.time() > dtime(21, 1) and dt.time() < dtime(22, 56))
            or (dt.time() > dtime(9, 1) and dt.time() < dtime(10, 14))
            or (dt.time() > dtime(10, 31) and dt.time() < dtime(11, 29))
            or (dt.time() > dtime(13, 31) and dt.time() < dtime(14, 56))
        ):
            if self.buy_vt_orderids:
                for i in self.buy_vt_orderids:
                    self.cancel_order(i)
            if self.short_vt_orderids:
                for i in self.short_vt_orderids:
                    self.cancel_order(i)            
            if self.buy_hedge_orderids:
                for i in self.buy_hedge_orderids:
                    self.cancel_order(i)
            if self.short_hedge_orderids:
                for i in self.short_hedge_orderids:
                    self.cancel_order(i)
            if self.buy_refer_orderids:
                for i in self.buy_refer_orderids:
                    self.cancel_order(i)
            if self.short_refer_orderids:
                for i in self.short_refer_orderids:
                    self.cancel_order(i)
            # self.cancel_all()
            self.MMTrading = False
        else:
            self.MMTrading = True