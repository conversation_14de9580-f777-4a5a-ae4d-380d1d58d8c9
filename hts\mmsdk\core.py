import requests
import os
import datetime
from .config import SERVER, TRANS_URL, DOWNLOAD_URL, ENV_CONFIG, SAVE_BASE_DIR, CHUNK_SIZE


def download(exchange, file_path):
    _config = ENV_CONFIG[exchange]
    _headers = {
        'Content-Type': "application/json"
    }
    _data = {
        'serverId': _config['server_id'],
        'remotePath': file_path
    }
    _url = SERVER + TRANS_URL
    _resp = requests.post(url=_url, json=_data, headers=_headers)
    _data = _resp.json()
    if _data['code'] == '0':
        _file_path = _data['data']['filePath']
        _file_name = os.path.basename(_file_path)
        # 这里可能会报错
        _real_file_name = _file_name.split('_', 1)[1]
        _today_dir = datetime.datetime.now().strftime('%Y%m%d')
        _save_dir = os.path.abspath(SAVE_BASE_DIR + _today_dir)
        if not os.path.exists(_save_dir):
            os.mkdir(_save_dir)
        _save_file_path = os.path.join(_save_dir, _real_file_name)
        print('save path: ' + _save_file_path)
        _download_url = SERVER + DOWNLOAD_URL + _file_name
        r = requests.get(url=_download_url, stream=True)
        f = open(_save_file_path, 'wb')
        for chunk in r.iter_content(chunk_size=CHUNK_SIZE):
            if chunk:
                f.write(chunk)
        return 'OK'
    else:
        return _data['msg']


def handle_files(exchange):
    _config = ENV_CONFIG[exchange]
    if _config is None:
        return None
    _remote_paths = _config['remote_paths']
    _today = datetime.datetime.now().strftime('%Y%m%d')
    for _item in _remote_paths:
        _filename = str.replace(_item, '*', _today)
        # 顺序执行即可，异步执行意义不大
        download(exchange, _filename)

