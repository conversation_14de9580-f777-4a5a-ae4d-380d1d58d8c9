# -*- coding: utf-8 -*-
"""
Created on Mon Jul 12 17:20:53 2021
 
@author: humy2
"""
import datetime
beginStr = '2022-06-22T21:00:00.0Z'
endStr = '2022-06-22T23:00:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
from datetime import timedelta
from influxdb import InfluxDBClient
client = InfluxDBClient('192.168.203.11',8989,'reader','iamreader','testbase') #上期所prod
client = InfluxDBClient('10.17.30.134',9001,'reader','iamreader','testbase') #东坝
client = InfluxDBClient('10.101.237.137',9090,'','','testbase') #东坝测试，带实时行情
client = InfluxDBClient('202.0.3.209',8989,'reader','iamreader','testbase') #大商所
client = InfluxDBClient('202.0.3.200',8989,'reader','iamreader','testbase') #鄭商所

result = client.query("select * from testdalian where insid_md=~/SP/ and time >= %d and time <= %d;"%(begin,end)) 
result = client.query("select * from testdalian where insid_md='m2208' and time >= %d and time <= %d;"%(begin,end)) 
result = client.query("select * from zce_md where insid_md='CF211' and time >= %d and time <= %d;"%(begin,end)) 
        
points = result.get_points()
l=[]
for d in points:
    l.append(d)
print(len(l))
    
import pandas as pd
import datetime
df = pd.DataFrame(l)
df['datetime']=df.time.apply(lambda x:datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ'))+timedelta(hours=8)
df.index = df['datetime']
 
