# encoding: UTF-8


from datetime import datetime, time
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    TickArrayManager
)


########################################################################
class IFStrategy(CtaTemplate):
    """基于Tick的交易策略"""
    className = 'IFStrategy'

    # 策略参数
    fixedSize = 1  # 下单数量
    size = 2  # 缓存大小
    MA1 = 1
    MA2 = 1

    trailing_long = 0.5
    trailing_short = 0.5
    win_ratio = 0.2
    loss_ratio = 0.2
    multiplier = 3

    refdealnum = 10
    wide = 0.6
    minnum = 1.5
    ratio = 0.2
    match = 40
    maxpos = 8
    # DAY_START = time(8, 45)  # 日盘启动和停止时间
    # DAY_END = time(13, 45)
    # NIGHT_START = time(15, 00)  # 夜盘启动和停止时间
    # NIGHT_END = time(5, 00)

    # 策略变数
    posPrice = 0  # 持仓价格
    pos = 0  # 持仓数量

    intra_trade_high = 0
    intra_trade_low = 0
    avgprc = 0

    # 参数列表，保存了参数的名称
    paramList = ['name',
                 'className',
                 'author',
                 'vtSymbol',
                 ]

    # 变数清单，保存了变数的名称
    varList = ['inited',
               'trading',
               'pos',
               'posPrice'
               ]

    # 同步清单，保存了需要保存到资料库的变数名称
    syncList = ['pos',
                'posPrice',
                'intraTradeHigh',
                'intraTradeLow']

    # ----------------------------------------------------------------------
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """Constructor"""
        super(IFStrategy, self).__init__(
            cta_engine, strategy_name, vt_symbol, setting
        )

        # 创建Array伫列
        self.mult2 = 1
        self.mult1 = 0.5
        self.sigmode = 'or'
        self.sig2 = None
        self.sig1 = None
        self.tickArray = TickArrayManager(self.size, self.MA1, self.MA2, self.cta_engine.pids)
        self.totalscore = 0
        self.futscore = 0
        self.stkscore = 0

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        # self.load_bar(10)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    # ----------------------------------------------------------------------
    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        pidnum = 0

        self.tickArray.updateTradeBook(tick, pidnum, 'tradebook')
        # self.tickArray.updateRealBook(pidnum)

        futsignal = self.futsignal2(self.wide, self.minnum, self.ratio, self.refdealnum, pidnum)

        if futsignal == 0:
            self.futscore = 0
        else:
            self.futscore = futsignal
            print("---------------------------------------------")
            print("futsignal:" + str(futsignal))
            print("self.pos:" + str(self.pos))
            print("self.price:" + str(tick.last_price))

        self.totalscore = self.futscore

        if self.pos == 0:
            self.intra_trade_high = tick.last_price
            self.intra_trade_low = tick.last_price
            self.avgprc = tick.last_price

        elif self.pos > 0:
            self.intra_trade_high = max(self.intra_trade_high, tick.last_price)
            winget = max(self.trailing_long - (self.intra_trade_high / self.avgprc - 1), self.win_ratio)
            long_stop = self.trailing_long - (self.intra_trade_high - self.avgprc) * winget
            long_wing = self.avgprc * (1 + self.win_ratio / 100)
            long_loss = self.avgprc * (1 - self.loss_ratio / 100)
            if tick.last_price < long_loss:
                self.totalscore -= 1
                print(winget)
            elif long_stop > tick.last_price > self.avgprc:
                self.totalscore -= 1

        elif self.pos < 0:
            self.intra_trade_low = min(self.intra_trade_low, tick.last_price)
            winget = max(self.trailing_short - (self.intra_trade_low / self.avgprc - 1), self.win_ratio)
            short_stop = (self.avgprc - self.intra_trade_low) * winget + self.trailing_short + 2
            short_wing = self.avgprc * (1 - self.win_ratio / 100)
            short_loss = self.avgprc * (1 + self.win_ratio / 100)
            if tick.last_price > short_loss:
                self.totalscore += 1
                print(winget)
            elif short_stop < tick.last_price < self.avgprc:
                self.totalscore += 1

        if self.pos > self.maxpos:
            self.orderfilter(0, 3, pidnum)
        elif self.pos < -self.maxpos:
            self.orderfilter(3, 0, pidnum)
        else:
            self.orderfilter(1, 1, pidnum)

        if tick.datetime.time() > time(hour=14, minute=55):
            if self.pos > 0:
                self.sell(self.tickArray.TickbidPrice2Array[pidnum, -1], abs(self.pos))
            elif self.pos < 0:
                self.cover(self.tickArray.TickaskPrice2Array[pidnum, -1], abs(self.pos))

    def orderfilter(self, num1, num2, pidnum):
        if self.totalscore == 1:
            # 当前持有空头仓位，则先平空，再开多
            if self.pos < 0:
                self.cover(self.tickArray.TickaskPrice1Array[pidnum, -1], num1)
            else:
                self.buy(self.tickArray.TickaskPrice1Array[pidnum, -1], num1)
            self.totalscore = 0
            self.stkscore = 0
            self.futscore = 0
        elif self.totalscore == -1:
            if self.pos > 0:
                self.sell(self.tickArray.TickbidPrice1Array[pidnum, -1], num2)
            else:
                self.short(self.tickArray.TickbidPrice1Array[pidnum, -1], num2)
            self.totalscore = 0
            self.stkscore = 0
            self.futscore = 0

    # ----------------------------------------------------------------------
    def futsignal(self, wide, minnum, ratio, refdealnum, pidnum):
        if self.tickArray.TickaskPrice1Array[pidnum, -1] - self.tickArray.TickbidPrice1Array[pidnum, -1] <= wide:
            if self.tickArray.TickaskVolume1Array[pidnum, -1] < minnum \
                    and self.tickArray.TickbidVolume1Array[pidnum, -1] / self.tickArray.TickaskVolume1Array[
                pidnum, -1] > ratio \
                    and self.tickArray.TicklastVolumeArray[pidnum, -1] > refdealnum:
                return 1
            elif self.tickArray.TickbidVolume1Array[pidnum, -1] < minnum \
                    and self.tickArray.TickaskVolume1Array[pidnum, -1] / self.tickArray.TickbidVolume1Array[
                pidnum, -1] > ratio \
                    and self.tickArray.TicklastVolumeArray[pidnum, -1] > refdealnum:
                return -1
            else:
                return 0
        else:
            return 0

    def futsignal2(self, wide, minnum, ratio, refdealnum, pidnum):

        self.sig1 = self.signal(pidnum, -1)
        self.sig2 = self.signal(pidnum, -2)

        pxchg = self.sig1['im5'] - self.sig2['im5']
        pxchg2 = self.sig1['im5vol'] - self.sig2['im5vol']
        if self.sigmode == 'chg':
            sig = pxchg
        elif self.sigmode == 'one':
            sig = self.sig1['im5']
        elif self.sigmode == 'mix':
            sig = pxchg * self.mult1 + pxchg2 * self.mult2
        elif self.sigmode == 'or':
            sig = pxchg * self.mult1
            sig2 = pxchg2 * self.mult2
            if abs(sig) > self.ratio:
                sig = sig
            elif abs(sig2) > self.ratio:
                sig = sig2

        if self.tickArray.TickaskPrice1Array[pidnum, -1] - self.tickArray.TickbidPrice1Array[pidnum, -1] <= wide:
            if sig > ratio:
                return 1
            elif sig < -ratio:
                return -1
            else:
                return 0
        else:
            return 0

    def signal(self, pidnum, posi):
        data_md = {}
        i1 = 1
        i2 = 1
        data_md['imsum'] = 0
        data_md['imvolsum'] = 0
        data_md['im5'] = 0
        data_md['im5vol'] = 0
        data_md['mid'] = (self.tickArray.TickaskPrice1Array[pidnum, posi] / 2
                          + self.tickArray.TickbidPrice1Array[pidnum, posi] / 2)
        for j in ['bid', 'ask']:
            for i in range(1, 6):
                data_md[f'{j}Vol{str(i)}'] = getattr(self.tickArray, f'Tick{j}Volume{i}Array')[pidnum, posi]
                data_md[f'{j}Price{str(i)}'] = getattr(self.tickArray, f'Tick{j}Price{i}Array')[pidnum, posi]

                data_md[f'imvol{j}{str(i)}'] = 1 / data_md[f'{j}Vol{str(i)}'] ** i1
                data_md[f'imprc{j}{str(i)}'] = abs(data_md[f'{j}Price{str(i)}'] / data_md['mid'] - 1) ** i2
                data_md['imsum'] += data_md[f'imvol{j}{str(i)}'] * data_md[f'imprc{j}{str(i)}']
                data_md['im5'] += data_md[f'{j}Price{str(i)}'] * data_md[f'imvol{j}{str(i)}'] * data_md[
                    f'imprc{j}{str(i)}']
                if j == 'bid':
                    data_md['im5vol'] += data_md[f'{j}Vol{str(i)}'] / data_md[f'imprc{j}{str(i)}']
                else:
                    data_md['im5vol'] -= data_md[f'{j}Vol{str(i)}'] / data_md[f'imprc{j}{str(i)}']
                data_md['imvolsum'] += data_md[f'{j}Vol{str(i)}'] / data_md[f'imprc{j}{str(i)}']

        data_md['im5'] = (data_md['im5'] / data_md['imsum'] - data_md['mid']).round(2)
        data_md['im5vol'] = (data_md['im5vol'] / data_md['imvolsum']).round(2)

        return data_md

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.
        """
        self.bg.update_bar(bar)

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        pass

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """
        Callback of stop order update.
        """
        pass
