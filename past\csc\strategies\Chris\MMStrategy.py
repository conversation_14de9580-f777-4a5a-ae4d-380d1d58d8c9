# -*- coding: utf-8 -*-
"""
Created on Tue Jul 20 14:52:36 2021

@author: <PERSON>
"""

""""""
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle

class MMStrategy(StrategyTemplate):
    buy_price = 0
    short_price = 0
    
    parameters = [
        'maker',
        'refer',
        'lots',
        'edgetype',   #1:Fixed Edge 2.MktSpread+Adj.
        'edge',
        'fader',
        'fader_start_pos',
        'priceticks', #ts
        'sizes',
        'price_protect_tick', #报价保护
        'marketspread_indicator', #仓位小于fader_start, 市场宽度报价
        'reverse_indicator', #默认选择性反手
        'reverse_tick',
        'autohedgeclose'
    ]
    
    
    variables = [
        'buy_price',
        'short_price'
    ]
    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.isQuoting=False
        self.load_ticks(0)
        self.net=0
        self.fair = 0
        self.pauseCount=0
        # self.isQuoting=False
        self.hedgeclosemode=False
        self.lastMaker={"net":0, "tick":None, "timepoint":0}
        self.lastTicks={self.maker:0,self.refer:0}

    def on_tick_maker(self, tick):
        

        ts = self.priceticks[self.maker] #ticksize
        edge=self.edge
        edgetype = self.edgetype
        marketspread_indicator = self.marketspread_indicator
        net = self.net
        fader = self.fader
        fader_start_pos = self.fader_start_pos
        lots = self.lots
        price_protect_tick = self.price_protect_tick
        
        shortResendFlag=False
        buyResendFlag=False
        
        _spread = (tick.ask_price_1 - tick.bid_price_1)/ts #计算最优买卖价差
        #计算micro-price
        _fair = (tick.bid_price_1 * tick.ask_volume_1 + tick.ask_price_1 * tick.bid_volume_1) / (tick.bid_volume_1 + tick.ask_volume_1)
        #根据持仓调整理论价
        if net>fader_start_pos:
            price_fade = -fader*(float(net-fader_start_pos)/float(lots))*ts
        elif net<-fader_start_pos:
            price_fade = fader*(float(abs(net)-fader_start_pos)/float(lots))*ts
        else:
            price_fade = 0
        _fair+=price_fade
        # _fair-= fader * (float(net)/float(lots))
        if edgetype == 1:
            _edge = edge
        if edgetype ==2:
            _edge = _spread + edge
        if marketspread_indicator and abs(net) < fader_start_pos:
            _edge = _spread
        bidP = _fair - 0.5*_edge*ts
        askP = _fair + 0.5*_edge*ts
        bidP = ts * round(bidP / ts)
        askP = ts * round(askP / ts)
        #报价保护 - 不优于盘口X tick
        bidP = min(bidP,tick.bid_price_1+price_protect_tick*ts)
        askP = max(askP,tick.ask_price_1-price_protect_tick*ts)
        
        #收盘前对冲模式
        if (self.hedgeclosemode and self.autohedgeclose):
            if net > 0:
                askP = tick.ask_price_1
                bidP = tick.bid_price_1 - 2*ts
            elif net==0:
                askP = tick.ask_price_1 + 2*ts
                bidP = tick.bid_price_1 - 2*ts
            else:
                askP = tick.ask_price_1 + 2*ts
                bidP = tick.bid_price_1 
        
        
        #撤单重挂机制
        if askP!=self.short_price:
            shortResendFlag=True
        if bidP!=self.buy_price:
            buyResendFlag=True
        
        self.short_price = askP
        self.buy_price = bidP
        
        # if tick.datetime.time() > dtime(21,0,35):
        #     print('1')
        
        if not self.buy_vt_orderids: #第一次挂单
            self.buy_vt_orderids = self.buy(self.maker,self.buy_price, self.lots,'MM')
        elif buyResendFlag and self.buy_vt_orderids[0]: #撤单重挂
            if self.cancel_order(self.buy_vt_orderids[0]):  #cancel完会跳转至onOrder，然后自动发一个单
                self.buy_vt_orderids = self.buy(self.maker,self.buy_price, self.lots,'MM')
        
        if not self.short_vt_orderids:
            self.short_vt_orderids = self.short(self.maker,self.short_price, self.lots,'MM')
        elif shortResendFlag and self.short_vt_orderids[0]:
            if self.cancel_order(self.short_vt_orderids[0]): #cancel完会跳转至onOrder，然后自动发一个单
                self.short_vt_orderids = self.short(self.maker,self.short_price, self.lots,'MM')
        

            
        
        
        
    #-------------------------------------------------------不变-----------------------------------------------------------
    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        #self.sbg = SecondBarGenerator(self.on_bar)
        #self.sbg5 = SecondBarGenerator(self.on_bar, 5, self.on_5s_bar)
        #self.am = ArrayManager(size=300)

        self.buy_vt_orderids = []
        self.short_vt_orderids = []
        self.buy_reverse_orderids = []
        self.short_reverse_orderids = []
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")
    #-------------------------------------------------------不变-----------------------------------------------------------

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.maker:
                    self.on_tick_maker(ticks[key])
                if key==self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick

        #储存之前的tick
        
    def on_tick_refer(self,tick):
        return

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        reverse_indicator = self.reverse_indicator
        reverse_tick = self.reverse_tick
        ts = self.priceticks[self.maker] #ticksize
        volume=trade.volume 
        price=trade.price 
        if trade.direction.value=='多':
            if reverse_indicator and self.net > 0:#成交增加持仓,触发反手
                reverse_lot = min(self.lots,abs(self.net))
                self.short_price = price - self.reverse_tick * ts
                self.short_reverse_orderids = self.short(self.maker,self.short_price, reverse_lot,'reverse')
            self.net += volume 
            
        if trade.direction.value=='空':    
            if reverse_indicator and self.net < 0:#成交增加持仓，触发反手
                reverse_lot = min(self.lots,abs(self.net))
                self.buy_price = price + self.reverse_tick * ts
                self.buy_reverse_orderids = self.buy(self.maker,self.buy_price, reverse_lot,'reverse')
            self.net -= volume 
            
    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """ # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        #收盘前清理持仓
        if dt.time() > dtime(14, 55) and dt.time() < dtime(14, 59):
            self.hedgeclosemode = True
        else:
            self.hedgeclosemode = False

        
        
       