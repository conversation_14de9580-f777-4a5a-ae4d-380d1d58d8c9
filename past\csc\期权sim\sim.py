# -*- coding: utf-8 -*-
"""
Created on Fri Nov  5 18:26:08 2021
#Run the simulation of current option position
@author: zhanghc
"""
import os
import numpy as np
import pandas as pd
# from WindPy import *
import re
import py_vollib
import py_vollib_vectorized
import py_vollib.black_scholes.greeks.numerical as greeks
import py_vollib.black as blk

def getundlcode(s):
    # print (s)
    product_code = s[:re.search(r"\d",s).start()]
    s2 = s[re.search(r"\d",s).start():]
    m = s2[:re.search(r'\D',s2).start()]
    return product_code + m

if __name__ == '__main__':
    r = 0
    date_str = "20220711"
    path_read = "./"
    spot_shift_range=[-20,-15,-10,-5,-3,-2,-1,0,1,2,3,5,10,15,20]
    greeks_list = ['premium','delta','gamma','vega','theta']
    
    #提取总持仓数据
    name = 'risk_'+ date_str
    file_name = [i for i in os.listdir() if name in i][0]
    df = pd.read_csv(path_read + file_name,encoding='gb18030')
    columns_list = ['InstrumentId','Theoretical Volatility%','Multiplier','Net Position','T','Delta','Gamma','Vega','Theta','Underlying Price']#'Call/Put','理论价']
    df = df[columns_list]
    
    df.columns=['Ticker','Vol','Multiplier','Pos','T','delta','gamma','vega','theta','Underlying Price'] #c/p,Theo
    df['T'] /= 244
    df['T'] = df['T'].map(float)
    df['ProductCode'] = df['Ticker'].map(lambda x: x[:re.search(r"\d",x).start()].upper())
    df['type'] = df.Ticker.apply(lambda x: 'Futures' if not re.match(r'\D+\d+\D+\d+',x) else 'Options')
    
    df_fut = df[df.type=='Futures']
    df_option = df[df.type=='Options']
    
    df_fut['Theo'] = df_fut['Underlying Price']
    
    df_option['Undl'] = df_option['Ticker'].map(getundlcode)
    df_option['K'] = df_option['Ticker'].map(lambda x: int(re.search(r'(\D)\d*$', x)[0][1:]))
    df_option['c/p'] = df_option['Ticker'].map(lambda x: re.findall(r"[A-Z]",x)[-1].lower())
    df_option['Vol'] = df_option['Vol'].map(float)
    df_option = df_option[df_option['Pos']!=0]
    df_option.reset_index(inplace=True,drop=True)
    df_option['Theo'] = blk.black(df_option['c/p'],df_option['Underlying Price'],df_option['K'],df_option['T'],r,df_option['Vol']/100.0,return_as='numpy')
    #Get UNDL Spot Price------------------------------------------------------------------------
    df_spot = df_fut[['Ticker','Theo']].set_index('Ticker')
    df_spot.columns=['Undl_spot_price']
    df_option = df_option.merge(df_spot,how='left',left_on = 'Undl',right_index=True)
    df_option.dropna(inplace=True)
    #-------------------------------------------------------------------------------------------
    
    df_fut = df_fut[df_fut['Pos']!=0]

    #calculate premium
    for spot_shift in spot_shift_range:
        df_option['premium'+str(spot_shift)]=blk.black(df_option['c/p'],df_option['Undl_spot_price']*(1+spot_shift/100.0),df_option['K'],df_option['T'],r,df_option['Vol']/100.0,return_as='numpy')*df_option['Multiplier'] * df_option['Pos']
    #calculate delta
    for spot_shift in spot_shift_range:
        df_option['delta'+str(spot_shift)]=greeks.delta(df_option['c/p'],df_option['Undl_spot_price']*(1+spot_shift/100.0),df_option['K'],df_option['T'],r,df_option['Vol']/100.0,return_as='numpy')*df_option['Multiplier'] * df_option['Pos'] * df_option['Undl_spot_price']
    #calculate $Gamma
    for spot_shift in spot_shift_range:
        #print ('premium'+str(spot_shift))
        df_option['gamma'+str(spot_shift)]=greeks.gamma(df_option['c/p'],df_option['Undl_spot_price']*(1+spot_shift/100.0),df_option['K'],df_option['T'],r,df_option['Vol']/100.0,return_as='numpy')*df_option['Multiplier'] * df_option['Pos'] * df_option['Undl_spot_price'] * df_option['Undl_spot_price'] /100.0
    #calculate $vega
    for spot_shift in spot_shift_range:
        #print ('premium'+str(spot_shift))
        df_option['vega'+str(spot_shift)]=greeks.vega(df_option['c/p'],df_option['Undl_spot_price']*(1+spot_shift/100.0),df_option['K'],df_option['T'],r,df_option['Vol']/100.0,return_as='numpy')*df_option['Multiplier'] * df_option['Pos']   
    #calculate $theta
    for spot_shift in spot_shift_range:
        #print ('premium'+str(spot_shift))
        df_option['theta'+str(spot_shift)]=greeks.theta(df_option['c/p'],df_option['Undl_spot_price']*(1+spot_shift/100.0),df_option['K'],df_option['T'],r,df_option['Vol']/100.0,return_as='numpy')*df_option['Multiplier'] * df_option['Pos'] 

    col_list = []
    for greek in greeks_list:
        for spot_shift in spot_shift_range:
            col_list.append(greek + str(spot_shift))
    # ticker_str = ','.join(ticker_arr)
    df_option_byproduct = df_option.groupby(['ProductCode'])[col_list].sum()
    #Calculate PnL
    df_premium0 = df_option_byproduct['premium0'].copy()
    for spot_shift in spot_shift_range:
        df_option_byproduct['premium'+str(spot_shift)]-= df_premium0
    
    df_simulation = pd.DataFrame(columns=spot_shift_range)
    for i in range(df_option_byproduct.shape[0]):
        df_simulation = df_simulation.append(pd.DataFrame(df_option_byproduct[i:i+1].values.reshape(5,15),columns=spot_shift_range))
        
    rows_list = []
    items_list = ['PnL','Delta','Gamma','Vega','Theta']
    for product in df_option_byproduct.index:
        for item in items_list:
            rows_list.append(product + "_" + item)
    df_simulation.index = rows_list

    #Calculate Future Spot Move & PnL
    #calculate PnL
    for spot_shift in spot_shift_range:
        df_fut['pnl'+str(spot_shift)]= df_fut['Theo'] * spot_shift / 100.0 * df_fut['Pos'] * df_fut['Multiplier']
    for spot_shift in spot_shift_range:
        df_fut['delta'+str(spot_shift)]= df_fut['Theo'] * (1+spot_shift / 100.0) * df_fut['Pos'] * df_fut['Multiplier']
    fut_col_list = []
    
    for type in ['pnl','delta']:
        for spot_shift in spot_shift_range:
            fut_col_list.append(type + str(spot_shift))
    df_fut_byproduct = df_fut.groupby(['ProductCode'])[fut_col_list].sum()
    
    df_fut_simulation = pd.DataFrame(columns=spot_shift_range)
    for i in range(df_fut_byproduct.shape[0]):
        df_fut_simulation = df_fut_simulation.append(pd.DataFrame(df_fut_byproduct[i:i+1].values.reshape(2,15),columns=spot_shift_range))
    
    rows_fut_list = []
    fut_items_list = ['PnL','Delta']
    for product in df_fut_byproduct.index:
        for item in fut_items_list:
            rows_fut_list.append(product + "_" + item)
    df_fut_simulation.index = rows_fut_list
       
    sim_final = pd.concat([df_simulation,df_fut_simulation]).sum(level=0)
    sim_final.to_csv('sim'+date_str+'.csv')
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    # for ticker in df_option['Ticker']:
    #     print (getundlcode(ticker))
    # # df['K'] = df['商品代码'].map(lambda x: int(re.search(r'(\D)\d*$', x[:-4])[0][1:]))