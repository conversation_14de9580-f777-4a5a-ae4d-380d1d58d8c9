import pyodbc
import pandas as pd
from pandas import DataFrame
import numpy as np
from WindPy import w
import datetime

# import sys

# reload(sys)
# sys.setdefaultencoding('utf-8')

server = '10.25.18.36'
user = 'Alex'
password = '789456'

# Specifying the ODBC driver, server name, database, etc. directly
cnxn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user, charset="utf8")

# Create a cursor from the connection
# cursor = cnxn.cursor()

w.start()

futuredata = w.wsd("M.DCE", "trade_hiscode", "2017-01-01", "2017-11-23", "PriceAdj=F")
df = DataFrame({"times": futuredata.Times, "codes": futuredata.Data[0]})

# sql = "SELECT  codes,exe_price,exe_date  FROM [Alex].[dbo].[SR2] "

# pf = pd.read_sql(sql, cnxn, index_col=["codes"], coerce_float=True, params=None, parse_dates=None,
#                 columns=None, chunksize=None)

print(futuredata)
print(df)

# for index, row in df.iterrows():
#    if df.loc[index].values[0:-1] !=

sql2 = "INSERT INTO [Alex].[dbo].[M_Future_min] VALUES (?,?,?,?,?,?,?,?,?)"

cursor = cnxn.cursor()

cursor.execute("""
 IF OBJECT_ID('M_Future_min', 'U') IS NOT NULL
    DROP TABLE M_Future_min
 CREATE TABLE M_Future_min (
    DateTime VARCHAR(20) NOT NULL,
    Code VARCHAR(20) NOT NULL,
    OpenPrice VARCHAR(20),
    High VARCHAR(20),
    Low VARCHAR(20),
    ClosePrice VARCHAR(20),
    Volume VARCHAR(20),
    Amount VARCHAR(20),
    oi VARCHAR(20)
    )
 """)
cnxn.commit()


# codes=pf.index.values.tolist()
for i in range(0, len(futuredata.Data[0])):
    date1 = futuredata.Times[i]
    fcode = futuredata.Data[0][i]
    # wdata = w.wss(fcode, "open,high,low,close,volume,amt,dealnum,oi,lasttrade_date,dlmonth,lastdelivery_date","tradeDate=%s;priceAdj=U;cycle=D" % date1)
    wdata=w.wsi(fcode, "open,high,low,close,volume,amt,oi", "%s 00:00:00" % date1, "%s 23:59:59.999" % date1, "PriceAdj=F")
    sqllist2 = []
    for j in range(0, len(wdata.Times)):
        sqllist = []
        sqllist.append(wdata.Times[j].strftime('%Y-%m-%d %H:%M:%S'))

        sqllist.append(str(fcode))

        for k in range(0, len(wdata.Fields)):
            sqllist.append(wdata.Data[k][j])

        print(sqllist)

        sqltuple = tuple(sqllist)

        sqllist2.append(sqltuple)
        try:
            cursor.execute(sql2, sqltuple)
        except Exception as e:
            print(str(e))
        cnxn.commit()


cnxn.close()
