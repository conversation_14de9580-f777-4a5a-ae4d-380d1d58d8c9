
"""
LSTM回归模型实现
@author: lining
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.base import BaseEstimator, RegressorMixin
from torch.utils.data import DataLoader, TensorDataset
import time
from tqdm import tqdm
import pandas as pd

class LSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers=1, dropout=0):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout)
        self.fc = nn.Linear(hidden_size, 1)
    
    def forward(self, x):
        
        
        batch_size = x.size(0)
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
        
        out, _ = self.lstm(x, (h0, c0))
        
        out = self.fc(out[:, -1, :])
        return out

class LSTMRegressor(BaseEstimator, RegressorMixin):
    
    def __init__(self, input_size=None, hidden_size=64, num_layers=1, dropout=0,
                 learning_rate=0.001, epochs=1, batch_size=32, sequence_length=10,
                 early_stopping=5, use_cuda=True, verbose=1):
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.batch_size = batch_size
        self.sequence_length = sequence_length
        self.early_stopping = early_stopping
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.verbose = verbose  
        self.model = None
        self.device = torch.device("cuda" if self.use_cuda else "cpu")
        
    def _create_sequences(self, X):
        
        
        if self.sequence_length <= 1:
            return X
            
        
        if isinstance(X, np.ndarray):
            n_samples = len(X) - self.sequence_length + 1
            if len(X.shape) > 1:
                seq_data = np.zeros((n_samples, self.sequence_length, X.shape[1]))
                for i in range(n_samples):
                    seq_data[i] = X[i:i+self.sequence_length]
            else:
                seq_data = np.zeros((n_samples, self.sequence_length))
                for i in range(n_samples):
                    seq_data[i] = X[i:i+self.sequence_length]
            return seq_data
        else:
            
            n_samples = len(X) - self.sequence_length + 1
            seq_data = []
            for i in range(n_samples):
                seq_data.append(X[i:i+self.sequence_length])
            return np.array(seq_data)
    
    def fit(self, X, y):
        
        start_time = time.time()
        
        if self.input_size is None:
            self.input_size = X.shape[1] if len(X.shape) > 1 else 1
        
        
        if self.verbose > 0:
            print("准备数据序列...")
        X_seq = self._create_sequences(X)
        y_seq = y[self.sequence_length-1:] if self.sequence_length > 1 else y
        
        
        y_numpy = y_seq.values if hasattr(y_seq, 'values') else np.array(y_seq)
        y_numpy = y_numpy.reshape(-1, 1)
        
        
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        y_tensor = torch.FloatTensor(y_numpy).to(self.device)
        
        
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)
        
        
        if self.verbose > 0:
            print(f"创建LSTM模型: 输入维度={self.input_size}, 隐藏层大小={self.hidden_size}, 层数={self.num_layers}")
            print(f"运行设备: {self.device}")
            
        self.model = LSTMModel(self.input_size, self.hidden_size, self.num_layers, self.dropout).to(self.device)
        
        
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        
        
        best_loss = float('inf')
        patience_counter = 0
        
        
        if self.verbose > 0:
            print(f"开始训练: {self.epochs}轮, 批大小={self.batch_size}, 学习率={self.learning_rate}")
            print(f"数据集大小: {len(X_seq)} 样本, {len(dataloader)} 批次")
            print("-" * 50)
            
        
        epoch_iter = tqdm(range(self.epochs), desc="训练进度", disable=self.verbose == 0)
        history = {'loss': [], 'val_loss': []}
        
        for epoch in epoch_iter:
            self.model.train()
            epoch_loss = 0.0
            batch_count = 0
            
            
            batch_iter = tqdm(
                dataloader, 
                desc=f"Epoch {epoch+1}/{self.epochs}", 
                leave=False, 
                disable=self.verbose < 2
            )
            
            
            for batch_X, batch_y in batch_iter:
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
                
                
                if self.verbose >= 2:
                    batch_iter.set_postfix({"loss": f"{loss.item():.6f}"})
            
            
            avg_loss = epoch_loss / batch_count
            history['loss'].append(avg_loss)
            
            
            if self.verbose >= 1:
                epoch_iter.set_postfix({
                    "loss": f"{avg_loss:.6f}", 
                    "best": f"{best_loss:.6f}",
                    "patience": f"{patience_counter}/{self.early_stopping}"
                })
            
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
                
                best_model_state = self.model.state_dict().copy()
                if self.verbose >= 2:
                    print(f"Epoch {epoch+1}: 新的最佳模型 (loss={best_loss:.6f})")
            else:
                patience_counter += 1
                if patience_counter >= self.early_stopping and self.early_stopping > 0:
                    
                    self.model.load_state_dict(best_model_state)
                    if self.verbose >= 1:
                        print(f"早停在第 {epoch+1} 轮，共 {self.epochs} 轮 (best_loss={best_loss:.6f})")
                    break
        
        
        training_time = time.time() - start_time
        if self.verbose >= 1:
            print("-" * 50)
            print(f"训练完成，耗时: {training_time:.2f} 秒")
            print(f"最佳损失: {best_loss:.6f}")
            
        
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
            
        return self
    
    def predict(self, X):
        
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        
        original_index = X.index if hasattr(X, 'index') else None
        
        
        X_seq = self._create_sequences(X)
        
        
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        
        
        batch_size = min(1024, len(X_tensor))
        dataset = TensorDataset(X_tensor)
        dataloader = DataLoader(dataset, batch_size=batch_size)
        
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for (batch_X,) in dataloader:
                batch_preds = self.model(batch_X).cpu().numpy()
                predictions.append(batch_preds)
        
        
        pred_values = np.vstack(predictions).flatten()
        
        
        
        if original_index is not None and self.sequence_length > 1:
            
            adjusted_index = original_index[self.sequence_length-1:]
            
            if len(pred_values) != len(adjusted_index):
                
                min_len = min(len(pred_values), len(adjusted_index))
                pred_values = pred_values[:min_len]
                adjusted_index = adjusted_index[:min_len]
            
            
            return pd.Series(pred_values, index=adjusted_index)
        
        
        if original_index is None:
            return pred_values
        
        
        if len(pred_values) != len(original_index):
            min_len = min(len(pred_values), len(original_index))
            pred_values = pred_values[:min_len]
            original_index = original_index[:min_len]
        
        
        return pd.Series(pred_values, index=original_index) 