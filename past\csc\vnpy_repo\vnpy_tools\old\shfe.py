# -*- coding: utf-8 -*-
"""
Created on Mon Jul 12 17:20:53 2021
 
@author: humy2
"""
 
from datetime import timedelta
from influxdb import InfluxDBClient
# client = InfluxDBClient('192.168.203.11',8989,'reader','iamreader','testbase') #上期所prod
client = InfluxDBClient('10.17.30.134',9001,'reader','iamreader','testbase') #东坝
 
import datetime
beginStr = '2022-02-22T09:00:00.0Z'
endStr = '2022-02-22T15:00:00.0Z'
begin = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
end = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
 
#%%
def influx_to_df(result):      
    points = result.get_points()
    l=[]
    for d in points:
        l.append(d)
    print(len(l))
    import pandas as pd
    import datetime
    df = pd.DataFrame(l)
    df['datetime']=df.time.apply(lambda x:datetime.datetime.strptime(x,'%Y-%m-%dT%H:%M:%S.%fZ'))+timedelta(hours=8)
    df.index = df['datetime']
    return df

#%%
id = 33
result = client.query("select * from shfe_future_trade where insid = 'hc2209' and time >= %d and time <= %d;"%(begin,end))   
trades = influx_to_df(result)
trades = trades[trades.strategy_id == id]
result = client.query("select * from shfe_future_order where insid = 'hc2209' and time >= %d and time <= %d;"%(begin,end))   
orders = influx_to_df(result)
orders = orders[orders.strategy_id == id]
result = client.query("select * from test10 where insid_md = 'hc2209' and time >= %d and time <= %d;"%(begin,end))   
mkts = influx_to_df(result)

#%%
l = 0
time_list = []
for i in trades.index:
    trade = trades.loc[i]
    order_id = trade.internal_order_id
    time = trade.datetime
    order = orders[orders.internal_order_id == order_id].iloc[-1]
    order_price = order.order_price
    mkt = mkts[mkts.datetime <= time].iloc[-1]
    bid1 = mkt.b_p1
    ask1 = mkt.a_p1
    if order_price == bid1 or order_price == ask1:
        l +=1
        time_list.append(time)
        


