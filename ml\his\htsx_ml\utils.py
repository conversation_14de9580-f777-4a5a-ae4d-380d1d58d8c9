import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler  

def calculate_ema(data, span):
    """
    计算某一列的 EMA，自动跳过 NaN 值。

    Args:
        data (pd.Series): 需要计算 EMA 的列。
        span (int): 时间窗口大小。

    Returns:
        pd.Series: EMA 结果。
    """
    # 计算平滑系数 alpha
    alpha = 2 / (span + 1)
    
    # 初始化 EMA 数组
    ema = []  # 用于存储 EMA 结果
    last_ema = None  # 用于记录上一个有效的 EMA 值
    
    # 计算 EMA
    for value in data:
        if np.isnan(value):  # 如果当前值是 NaN，跳过
            ema.append(np.nan)  # 在结果中保留 NaN
        else:
            if last_ema is None:  # 第一个非 NaN 值
                last_ema = value
            else:
                last_ema = alpha * value + (1 - alpha) * last_ema
            ema.append(last_ema)
    
    return ema


import pandas as pd
from sklearn.preprocessing import StandardScaler

def normalize_columns(X_train, X_test, exclude_columns=None):
    """
    对 X_train 和 X_test 进行归一化处理，排除指定的列。

    Args:
        X_train (pd.DataFrame): 训练集数据。
        X_test (pd.DataFrame): 测试集数据。
        exclude_columns (list): 不需要归一化的列名或列下标。

    Returns:
        X_train_normalized (pd.DataFrame): 归一化后的训练集数据。
        X_test_normalized (pd.DataFrame): 归一化后的测试集数据。
    """
    # 如果 exclude_columns 为 None，初始化为空列表
    if exclude_columns is None:
        exclude_columns = []

    # 如果 exclude_columns 是列下标，转换为列名
    if exclude_columns is not None and all(isinstance(col, int) for col in exclude_columns):
        exclude_columns = [X_train.columns[i] for i in exclude_columns]

    # 获取需要归一化的列
    columns_to_normalize = [col for col in X_train.columns if col not in exclude_columns]

    # 初始化 StandardScaler
    scaler = StandardScaler()

    # 对训练集进行归一化
    X_train_normalized = X_train.copy()
    X_train_normalized[columns_to_normalize] = scaler.fit_transform(X_train[columns_to_normalize])

    # 对测试集进行归一化，使用训练集的参数
    X_test_normalized = X_test.copy()
    X_test_normalized[columns_to_normalize] = scaler.transform(X_test[columns_to_normalize])

    return X_train_normalized, X_test_normalized
