
"""
因子管理模块
用于集中管理和维护所有因子
@author: lining
"""
from typing import Dict, List, Optional, Callable
import pandas as pd
import numpy as np
from dataclasses import dataclass
from enum import Enum


class FactorCategory(Enum):
    
    BASIC = "basic"  
    ADVANCED = "advanced"  
    DIY = "diy"  
    TECHNICAL = "technical"  

    TIME_SERIES = "time_series"  
    INTERACTION = "interaction"  


@dataclass
class Factor:
    
    name: str  
    category: FactorCategory  
    description: str  
    calculation: Callable  
    dependencies: List[str]  
    parameters: Dict = None  
    validation_rules: List[Callable] = None  
    source: str = 'lining'  


class FactorManager:
    

    def __init__(self):
        self._factors: Dict[str, Factor] = {}

    def register_factor(self, factor: Factor):
        
        self._factors[factor.name] = factor

    def get_factor(self, name: str) -> Optional[Factor]:
        
        return self._factors.get(name)

    def get_factors_by_category(self, category: FactorCategory) -> List[Factor]:
        
        return [f for f in self._factors.values() if f.category == category]

    def calculate_factor(self, name: str, data: pd.DataFrame) -> pd.Series:
        
        factor = self.get_factor(name)
        if not factor:
            raise ValueError(f"因子 {name} 未注册")
        return factor.calculation(data)

    def validate_factor(self, name: str, data: pd.DataFrame) -> bool:
        
        factor = self.get_factor(name)
        if not factor or not factor.validation_rules:
            return True

        for rule in factor.validation_rules:
            if not rule(data):
                return False
        return True

    def get_factor_documentation(self, all_features: Dict[str, List[str]]) -> str:
        
        
        
        data = []
        
        
        x = 0
        for category in FactorCategory:
            factors = self.get_factors_by_category(category)
            for factor in factors:
                x += 1
                params_str = str(factor.parameters) if factor.parameters else ""
                dependencies_str = ', '.join(factor.dependencies)
                data.append({
                    '序号': f"{x:03d}",
                    '因子名称': factor.name,
                    '类别': category.value,
                    '使用情况': all_features[factor.name][0],
                    '计算时间': round(all_features[factor.name][1], 4),
                    '描述': factor.description,
                    '依赖': dependencies_str,
                    '参数': params_str
                })

        
        return pd.DataFrame(data)



factor_manager = FactorManager()




def calculate_mid_price(data: pd.DataFrame, level: int = 1) -> pd.Series:
    
    return (data[f'AskPrice{level}'] + data[f'BidPrice{level}']) / 2



for i in range(1, 6):
    is_base_level = i == 1  
    factor_manager.register_factor(Factor(
        name=f"mid_price_level_{i}" if not is_base_level else "mid_price",
        category=FactorCategory.BASIC,
        description=f"{i}档位的中间价格",
        calculation=lambda data, i=i: calculate_mid_price(data, i),  
        dependencies=[f"AskPrice{i}", f"BidPrice{i}"],
        parameters={"level": i} if not is_base_level else None,
        source="orderbook-dynamic-features v2"
    ))



def calculate_mid_return(data: pd.DataFrame, window: int = 1) -> pd.Series:
    
    if 'mid_price' not in data.columns:
        mid = calculate_mid_price(data)
    else:
        mid = data['mid_price']

    return mid.pct_change(window)


factor_manager.register_factor(Factor(
    name="mid_return",
    category=FactorCategory.BASIC,
    description="中间价一期收益率",
    calculation=lambda data: calculate_mid_return(data, 1),
    dependencies=["mid_price", "AskPrice1", "BidPrice1"],
    parameters={"window": 1}
))



def calculate_volume(data: pd.DataFrame) -> pd.Series:
    
    return data['tradedVol']


factor_manager.register_factor(Factor(
    name="tradedVol",
    category=FactorCategory.BASIC,
    description="成交量",
    calculation=calculate_volume,
    dependencies=["tradedVol"]
))



for i in range(1, 6):
    factor_manager.register_factor(Factor(
        name=f"spread_{i}",
        category=FactorCategory.BASIC,
        description=f"{i}档位的价差",
        calculation=lambda data, level=i: data[f'AskPrice{level}'] - data[f'BidPrice{level}'],
        dependencies=[f"AskPrice{i}", f"BidPrice{i}"],
        parameters={"level": i},
        source="orderbook-dynamic-features v2"
    ))



def calculate_price_diff(data: pd.DataFrame, side: str, level: int) -> pd.Series:
    

    return data[f'{side}Price{level}'] - data[f'{side}Price{level + 1}']


def calculate_spread(data: pd.DataFrame, side: str, level: int) -> pd.Series:
    return data[f'{side}Price{level}'] - data[f'{side}Price{level}']



for i in range(1, 5):
    factor_manager.register_factor(Factor(
        name=f"bid_price_diff_{i}",
        category=FactorCategory.BASIC,
        description=f"买入{i}档与{i + 1}档之间的价格差异",
        calculation=lambda data, level=i: calculate_price_diff(data, 'Bid', level),
        dependencies=[f"BidPrice{i}", f"BidPrice{i + 1}"],
        parameters={"level": i},
        source="orderbook-dynamic-features v3"
    ))
factor_manager.register_factor(Factor(
    name="bid_price_diff_max",
    category=FactorCategory.BASIC,
    description="买入5档与1档之间的价格差异",
    calculation=lambda data: data['BidPrice5'] - data['BidPrice1'],
    dependencies=["BidPrice5", "BidPrice1"],
    parameters={"level": 5},
    source="orderbook-dynamic-features v3"
))


for i in range(1, 5):
    factor_manager.register_factor(Factor(
        name=f"ask_price_diff_{i}",
        category=FactorCategory.BASIC,
        description=f"卖出{i + 1}档与{i}档之间的价格差异",
        calculation=lambda data, level=i: calculate_price_diff(data, 'Ask', level),
        dependencies=[f"AskPrice{i}", f"AskPrice{i + 1}"],
        parameters={"level": i},
        source="orderbook-dynamic-features v3"
    ))

factor_manager.register_factor(Factor(
    name="ask_price_diff_max",
    category=FactorCategory.BASIC,
    description="卖出5档与1档之间的价格差异",
    calculation=lambda data: data['AskPrice5'] - data['AskPrice1'],
    dependencies=["AskPrice5", "AskPrice1"],
    parameters={"level": 5},
    source="orderbook-dynamic-features v3"
))



def calculate_mean_price_volume(data: pd.DataFrame, side: str, type: str) -> pd.Series:
    
    
    cols = [f'{side}{type}{i}' for i in range(1, 6) if f'{side}{type}{i}' in data.columns]

    
    if not cols:
        return pd.Series(0, index=data.index)
    else:
        return data[cols].mean(axis=1)



for side, side_desc in [('Ask', '卖方'), ('Bid', '买方')]:
    for type, type_desc in [('Price', '价格'), ('Vol', '成交量')]:
        factor_manager.register_factor(Factor(
            name=f"mean_{side.lower()}_{type.lower()}",
            category=FactorCategory.BASIC,
            description=f"{side_desc}五档平均{type_desc}",
            calculation=lambda data, s=side, t=type: calculate_mean_price_volume(data, s, t),
            dependencies=[f"{side}{type}{i}" for i in range(1, 6)],
            source="orderbook-dynamic-features v4"
        ))



def calculate_accumulated_price_spread(data: pd.DataFrame) -> pd.Series:
    
    return sum(data[f"AskPrice{i}"] - data[f"BidPrice{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_price_spread",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖价差的累计值",
    calculation=calculate_accumulated_price_spread,
    dependencies=[f"AskPrice{i}" for i in range(1, 6)] + [f"BidPrice{i}" for i in range(1, 6)],
    source="orderbook-dynamic-features v5"
))



def calculate_accumulated_volume_spread(data: pd.DataFrame) -> pd.Series:
    
    return sum(data[f"AskVol{i}"] - data[f"BidVol{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_volume_spread",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖量差异的累计值",
    calculation=calculate_accumulated_volume_spread,
    dependencies=[f"AskVol{i}" for i in range(1, 6)] + [f"BidVol{i}" for i in range(1, 6)],
    source="orderbook-dynamic-features v5"
))



def calculate_imbalance(data: pd.DataFrame, level: int = 1) -> pd.Series:
    
    bid_vol = data[f'BidVol{level}']
    ask_vol = data[f'AskVol{level}']
    denominator = bid_vol + ask_vol
    
    return (bid_vol - ask_vol).div(denominator.where(denominator != 0, np.nan))



for i in range(1, 6):
    factor_manager.register_factor(Factor(
        name=f"imbalance_{i}",
        category=FactorCategory.BASIC,
        description=f"{i}档位的买卖不平衡度",
        calculation=lambda data, level=i: calculate_imbalance(data, level),
        dependencies=[f"BidVol{i}", f"AskVol{i}"],
        parameters={"level": i},
        source="orderbook-dynamic-features v6"
    ))


from .libs import time_series_factors
from .libs import advanced_factors
from .libs import technical_factors
from .libs import technical_factors2
