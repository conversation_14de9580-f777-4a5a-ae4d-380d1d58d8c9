import os
import shutil
from datetime import datetime
import sys

# codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
codepath = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
sys.path.append(codepath)
from MMaker import myemail


def getpsw(classname,name):
    import json
    data = open(u"D:/onedrive/文档/package.json", encoding='utf-8')
    strJson = json.load(data)
    users = strJson[classname]
    for user in users:
        if name == user["name"]:
            return user


def sendmailwork(receivers0, subject0, content0, filepath0):
    name = u"liningzts"
    user = getpsw("email",name)
    usr = user["user"]
    psw = user["password"]
    smtpSvr = user["smtpSvr"]

    mm = myemail.MailSender(smtpSvr, 465, usr, psw, name)
    mm.login()
    if filepath0 != '':
        mm.add_attachment(filepath0)
    # res = False
    # for i in range(3):
    res = mm.send(subject0, content0, receivers0)
    if res:
        print(datetime.now(), ':success')
    mm.close()


def sendmail(receivers0, subject0, content0, filepath0):
    name = u"lining163"
    user = getpsw("email",name)
    usr = user["user"]
    psw = user["password"]
    smtpSvr = user["smtpSvr"]

    mm = myemail.MailSender(smtpSvr, 465, usr, psw, name)
    mm.login()
    if filepath0 != '':
        mm.add_attachment(filepath0)
    # res = False
    # for i in range(3):
    res = mm.send(subject0, content0, receivers0)
    if res:
        print(datetime.now(), ':success')
    mm.close()
