# -*- coding: utf-8 -*-
"""
Created on Fri Feb 23 15:43:39 2024

@author: admin
"""

import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages

# Reload the dataframes due to code execution state reset
ask_df = pd.read_csv('D:/code/htsc/result/resampled_ask.csv')
bidvol_df = pd.read_csv('D:/code/htsc/result/resampled_bidvol.csv')
vol_df = pd.read_csv('D:/code/htsc/result/resampled_vol.csv')
tradetable_df = pd.read_csv('D:/code/htsc/result/tradetable_pos_sum.csv')

# Convert 'time_seconds' to datetime and set as index
ask_df['time_seconds'] = pd.to_datetime(ask_df['time_seconds'])
ask_df.set_index('time_seconds', inplace=True)
print(ask_df.head())
bidvol_df['time_seconds'] = pd.to_datetime(bidvol_df['time_seconds'])
bidvol_df.set_index('time_seconds', inplace=True)

vol_df['time_seconds'] = pd.to_datetime(vol_df['time_seconds'])
vol_df.set_index('time_seconds', inplace=True)

tradetable_df['time_seconds'] = pd.to_datetime(tradetable_df.index, format='%Y/%m/%d %H:%M')
tradetable_df.set_index('time_seconds', inplace=True)

# Function to plot for a single time point
def plot_for_time_point(time_point, ask_series, bidvol_series, vol_series, trade_series, strikes):
    plt.figure(figsize=(10, 6))
    
    # Plot ask, bidvol, and vol
    plt.plot(strikes, ask_series, color='grey', label='Ask Volume')
    plt.plot(strikes, bidvol_series, color='yellow', label='Bid Volume')
    plt.plot(strikes, vol_series, color='blue', label='Volatility')
    
    # Check for trades and plot accordingly
    #print(strikes,"strikes",trade_series,"trade_series")
    for strike in strikes:
        strike_value = int(strike)  # Ensure strike is in the correct data type
        if strike_value in trade_series.index:
            trade_val = trade_series[strike_value]
            if trade_val > 0:
                plt.scatter(strike_value, bidvol_series.get(str(strike_value), 0), color='red', s=100, zorder=5)  # Positive trade on bidvol
            elif trade_val < 0:
                plt.scatter(strike_value, ask_series.get(str(strike_value), 0), color='green', s=100, zorder=5)  # Negative trade on askvol
    plt.ylim(0.2, 0.4)
    plt.title(f"Option Data Visualization for {time_point}")
    plt.xlabel("Strike Price")
    plt.ylabel("Value")
    plt.legend()
    plt.grid(True)

# Prepare a PDF to save all plots
pdf_path = "D:/code/htsc/result/options_data_visualization.pdf"
with PdfPages(pdf_path) as pdf:
    # Assuming similar time points across dataframes for simplicity
    for time_point in ask_df.index.unique():
        ask_series = ask_df.loc[time_point]
        bidvol_series = bidvol_df.loc[time_point]
        vol_series = vol_df.loc[time_point]
        trade_series = tradetable_df.loc[time_point].squeeze() if time_point in tradetable_df.index else pd.Series()
        strikes = ask_df.columns.astype(int)
        trade_series.index = trade_series.index.astype(int)
        plot_for_time_point(time_point, ask_series, bidvol_series, vol_series, trade_series, strikes)
        pdf.savefig()  # saves the current figure into the pdf
        plt.close()  # close the figure to avoid memory issues

#pdf_path