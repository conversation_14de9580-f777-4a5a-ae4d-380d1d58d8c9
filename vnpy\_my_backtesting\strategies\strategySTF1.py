# encoding: UTF-8


from datetime import datetime, time
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    TickArrayManager
)


########################################################################
class STF1Strategy(CtaTemplate):
    """基於Tick的交易策略"""
    className = 'STF1Strategy'

    # 策略參數
    fixedSize = 1  # 下單數量
    size = 2  # 緩存大小
    MA1 = 1
    MA2 = 1

    refdealnum = 10
    wide = 0.15
    minnum = 5
    ratio = 1.5
    match = 40
    # DAY_START = time(8, 45)  # 日盤啟動和停止時間
    # DAY_END = time(13, 45)
    # NIGHT_START = time(15, 00)  # 夜盤啟動和停止時間
    # NIGHT_END = time(5, 00)

    # 策略變數
    posPrice = 0  # 持倉價格
    pos = 0  # 持倉數量

    # 參數列表，保存了參數的名稱
    paramList = ['name',
                 'className',
                 'author',
                 'vtSymbol',
                 ]

    # 變數清單，保存了變數的名稱
    varList = ['inited',
               'trading',
               'pos',
               'posPrice'
               ]

    # 同步清單，保存了需要保存到資料庫的變數名稱
    syncList = ['pos',
                'posPrice',
                'intraTradeHigh',
                'intraTradeLow']

    # ----------------------------------------------------------------------
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """Constructor"""
        super(STF1Strategy, self).__init__(
            cta_engine, strategy_name, vt_symbol, setting
        )

        # 创建Array伫列
        self.tickArray = TickArrayManager(self.size, self.MA1, self.MA2, self.cta_engine.pids)
        self.totalscore = 0
        self.futscore = 0
        self.stkscore = 0

    # ----------------------------------------------------------------------
    def onminBarClose(self, bar):
        """"""

    # ----------------------------------------------------------------------

    def onInit(self):
        """初始化策略（必須由用戶繼承實現）"""
        self.write_log(u'%s策略初始化' % self.strategy_name)
        # tick級別交易，不需要過往歷史資料
        self.put_event()

    # ----------------------------------------------------------------------
    def onStart(self):
        """啟動策略（必須由用戶繼承實現）"""
        self.write_log(u'%s策略啟動' % self.strategy_name)
        self.put_event()

    # ----------------------------------------------------------------------
    def onStop(self):
        """停止策略（必須由用戶繼承實現）"""
        self.write_log(u'%s策略停止' % self.strategy_name)
        self.put_event()

    # ----------------------------------------------------------------------
    def onTick(self, tick):
        """收到行情TICK推送（必須由用戶繼承實現）"""

        if str(tick.symbol) == str(self.cta_engine.pids[1]):

            self.tickArray.updateTradeBook(tick, 1, 'tradebook')

            self.stkscore = self.refsignal(self.refdealnum)

            self.totalscore = self.stkscore + self.futscore
            # print("totalscore:" + str(self.totalscore))
        else:
            self.tickArray.updateTradeBook(tick, 0, 'trade')
            self.tickArray.updateRealBook(0)

            self.futscore = self.futsignal(self.wide, self.minnum, self.ratio)

        self.order_filter()

    def order_filter(self):
        if self.pos > 8:
            if self.totalscore == 2:
                self.buy(self.tickArray.TickaskPrice1Array[0, -1], 0, False)
            elif self.totalscore == -2:
                self.short( self.tickArray.TickbidPrice1Array[0, -1], 3, False)
        elif self.pos < -8:
            if self.totalscore == 2:
                self.buy(self.tickArray.TickaskPrice1Array[0, -1], 3, False)
            elif self.totalscore == -2:
                self.short(self.tickArray.TickbidPrice1Array[0, -1], 0, False)
        else:
            if self.totalscore == 2:
                self.buy( self.tickArray.TickaskPrice1Array[0, -1], 1, False)
            elif self.totalscore == -2:
                self.short(self.tickArray.TickbidPrice1Array[0, -1], 1, False)
        self.totalscore = 0
        self.stkscore = 0
        self.futscore = 0

        # print("self.pos:" + str(self.pos))
        # print("---------------------------------------------")

    # ----------------------------------------------------------------------
    def onBook(self, tick):

        if str(tick.symbol) == str(self.pids[1]):
            self.tickArray.updateTradeBook(tick, 1, 'tradebook')

        else:
            self.tickArray.updateTradeBook(tick, 0, 'book')

            self.futscore = self.futsignal(self.wide, self.minnum, self.ratio)

            self.totalscore = self.stkscore + self.futscore
            # print("totalscore:" + str(self.totalscore))

        self.order_filter()

    def futsignal(self, wide, minnum, ratio):
        if self.tickArray.TickaskPrice1Array[0, -1] - self.tickArray.TickbidPrice1Array[0, -1] <= wide:
            if self.tickArray.TickaskVolume1Array[0, -1] < minnum and self.tickArray.TickbidVolume1Array[0, -1] / \
                    self.tickArray.TickaskVolume1Array[0, -1] > ratio:

                return 1
            elif self.tickArray.TickbidVolume1Array[0, -1] < minnum and self.tickArray.TickaskVolume1Array[0, -1] / \
                    self.tickArray.TickbidVolume1Array[0, -1] > ratio:

                return -1
            else:
                return 0
        else:
            return 0

    # ----------------------------------------------------------------------
    def refsignal(self, refdealnum):
        if self.tickArray.TickaskPrice1Array[1, -1] > self.tickArray.TickaskPrice1Array[1, -2] and \
                self.tickArray.TicklastVolumeArray[1, -1] > refdealnum:

            return 1
        elif self.tickArray.TickbidPrice1Array[1, -1] < self.tickArray.TickbidPrice1Array[1, -2] and \
                self.tickArray.TicklastVolumeArray[1, -1] > refdealnum:

            return -1
        else:
            return 0

    # ----------------------------------------------------------------------
    def onBar(self, bar):
        """收到Bar推送（必須由用戶繼承實現）"""

    # ----------------------------------------------------------------------
    def onXminBar(self, bar):
        """收到X分鐘K線"""

    # ----------------------------------------------------------------------
    def onOrder(self, order):
        """收到委託變化推送（必須由用戶繼承實現）"""
        pass

    # ----------------------------------------------------------------------
    def onTrade(self, trade):

        self.posPrice = trade.price
        # 同步資料到資料庫
        self.sync_data()
        # 發出狀態更新事件
        self.put_event()

    # ----------------------------------------------------------------------
    def onStopOrder(self, so):
        """停止單推送"""
        pass
