# -*- coding: utf-8 -*-
 
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta
 
from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData
 
from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random
 
class BasisTrading(StrategyTemplate):
    """"""
 
    author = "vnpy"
 
    buy_price = 0
    short_price = 0
 
    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'path',
        'eta',
        'eta_cancel',
        'x_stop',
        'net_limit',
        'position_flag',
        'cancel_layer'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]
 
    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)
 
        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        self.buy_refer_orderids = None
        self.short_refer_orderids = None
        
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair
 
    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net=0
        self.fair_maker = 0
        self.fair_refer = 0
        self.rCount=0
        self.mCount = 0
        self.pauseCount=0
        self.isQuoting=False
        self.lastTicks={self.maker:0,self.refer:0}
        self.comments  = None
        self.ewma = 0
        self.Basis = 0
        self.isQuoting = True
        self.offer_list = []
        self.offer_list_head = []
        self.basisCount = 0
        self.basislist = np.zeros(99999999)
        self.maker_buy_price = 0
        self.maker_short_price = 0
        self.refer_buy_price = 0
        self.refer_short_price = 0
        self.maker_fair_order = 0
        self.refer_fair_order = 0
        self.last_net = 0
        self.cancel_count = 0
 
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")
 
    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")
 
    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
    
            if ticks[key]!=self.lastTicks[key]:
                if key==self.maker:
                    self.on_tick_maker(ticks[key])
                elif key==self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        #储存之前的tick
        
    def Validtick(self,tick): #无效的tick不计算ewma
        if tick.ask_price_1 - tick.bid_price_1 > 3*self.priceticks[self.maker]:
            return False
        return True
 
    def on_hedge_2(self,tick):
 
        buyResendFlag=False
        shortResendFlag=False
        ts = self.priceticks[self.refer]
        lots = self.lots
            
        if tick.ask_price_1 <= self.refer_short_price - self.x_stop * ts and self.refer_short_price != 0: 
            shortResendFlag=True
        if tick.bid_price_1 >= self.refer_buy_price + self.x_stop * ts and self.refer_buy_price != 0: 
            buyResendFlag=True
 
        if self.net >= abs(self.net_limit):
            if not self.short_hedge_orderids: #首次发单
                self.refer_short_price = max(np.ceil(self.fair_refer),tick.ask_price_1 - ts)
                self.short_hedge_orderids = self.short(self.refer,self.refer_short_price, lots,'refer_hedge')
                self.refer_fair_mid = self.fair_refer 
            elif shortResendFlag and self.short_hedge_orderids[0] :
                self.cancel_order(self.short_hedge_orderids[0])
                self.refer_short_price = tick.ask_price_1
                self.short_hedge_orderids = self.short(self.refer,self.refer_short_price, lots,'refer_hedge')
        if self.net <= -abs(self.net_limit):
            if not self.buy_hedge_orderids: #首次发单
                self.refer_buy_price = min(np.floor(self.fair_refer),tick.bid_price_1 + ts)
                self.buy_hedge_orderids = self.buy(self.refer,self.refer_buy_price, lots,'refer_hedge')
                self.refer_fair_mid = self.fair_refer  
            elif buyResendFlag and self.buy_hedge_orderids[0] :
                self.cancel_order(self.buy_hedge_orderids[0])
                self.refer_buy_price = tick.bid_price_1
                self.buy_hedge_orderids = self.buy(self.refer,self.refer_buy_price,lots,'refer_hedge')  
 
    def on_hedge_1(self,tick):
        lots = self.lots
        if abs(self.net) > abs(self.last_net):
            if self.net > self.last_net:
                self.short(self.refer,tick.ask_price_1,lots,'refer_sell')
                self.refer_fair_mid = self.fair_refer 
            else:
                self.buy(self.refer,tick.bid_price_1,lots,'refer_buy')
                self.refer_fair_mid = self.fair_refer 
 
    def on_refer_cancel(self,tick):
        orders = list(self.strategy_engine.active_limit_orders.items())
        if self.net > 0:
            for vt_orderid, order in orders:
                if order.price == tick.bid_price_1 and order.direction.value == '多' and order.vt_symbol == self.refer:
                    self.cancel_order(vt_orderid)
        if self.net < 0:
            for vt_orderid, order in orders:
                if order.price == tick.ask_price_1 and order.direction.value == '空' and order.vt_symbol == self.refer:
                    self.cancel_order(vt_orderid)
                    
    def on_refer_time(self,tick):
        if (tick.datetime.time() > dtime(14,54) and tick.datetime.time() < dtime(14, 55)):
            self.cancel_symbol(self.maker) #做市合约撤单
            if self.net > 0:
                self.short(self.refer,tick.bid_price_1,abs(self.net),'end_short')
            if self.net < 0:
                self.buy(self.refer,tick.ask_price_1,abs(self.net),'end_buy')
                
    def edgeP(self,tick): 
        edge = 0
        bollup = self.MovingAverage + self.eta * self.Std
        bolldown = self.MovingAverage - self.eta * self.Std
        if self.Basis > bollup:
            edge = -1  #强买信号
        elif self.Basis < bolldown:
            edge = 1 #强卖信号
        else:
            edge = 0
        return edge
 
    def edgeQ(self,tick): 
        edge = 0
        bollup = self.MovingAverage + self.eta_cancel * self.Std
        bolldown = self.MovingAverage - self.eta_cancel * self.Std
        if self.Basis > bollup:
            edge = -1  #强买信号
        elif self.Basis < bolldown:
            edge = 1 #强卖信号
        else:
            edge = 0
        return edge
                
    def on_maker_pricing(self,tick):
        
        if self.mCount > 10 and self.Validtick(tick): #启动后10个tick开始定价,对有效的tick进行定价
            self.basisCount += 1
            self.fair_maker = (tick.bid_price_1 + tick.ask_price_1) / 2
            self.Basis = self.fair_maker - self.fair_refer
            self.basislist[self.basisCount] = self.Basis
 
        if self.basisCount > self.path and self.MMTrading:
 
            self.MovingAverage = np.mean(self.basislist[self.basisCount - self.path:self.basisCount])
            self.Std = np.std(self.basislist[self.basisCount - self.path:self.basisCount])
            self.bollup = self.MovingAverage + self.eta * self.Std
            self.bolldown = self.MovingAverage - self.eta * self.Std
            self.offer_list.append([self.maker.split('.')[0],tick.datetime,self.Basis,self.bollup,self.bolldown,self.MovingAverage,self.Std])
 
            if self.Validtick(tick):
                orders = list(self.strategy_engine.active_limit_orders.items())
                #撤单逻辑
                for vt_orderid, order in orders:
                    #skew_pos内反edge则盘口撤单
                    if abs(self.net) <= abs(self.net_limit):
                        if self.position_flag:
                            if order.price >= tick.__getattribute__(f'bid_price_{self.cancel_layer}') and self.edgeQ(tick) != 1 and order.vt_symbol == self.maker and order.direction.value == '多' and self.net >= 0:
                                self.cancel_order(vt_orderid)
                            if order.price <= tick.__getattribute__(f'ask_price_{self.cancel_layer}') and self.edgeQ(tick) != -1 and order.vt_symbol == self.maker and order.direction.value == '空' and self.net <= 0:
                                self.cancel_order(vt_orderid)   
                        else:
                            if order.price >= tick.__getattribute__(f'bid_price_{self.cancel_layer}') and self.edgeQ(tick) != 1 and order.vt_symbol == self.maker and order.direction.value == '多':
                                self.cancel_order(vt_orderid)
                            if order.price <= tick.__getattribute__(f'ask_price_{self.cancel_layer}') and self.edgeQ(tick) != -1 and order.vt_symbol == self.maker and order.direction.value == '空':
                                self.cancel_order(vt_orderid)                               
                    #超过skewpos反向订单只能edge=1不撤
                    if self.net > self.net_limit and order.price == tick.bid_price_1 and order.vt_symbol == self.maker:
                        self.cancel_order(vt_orderid)
                    if self.net < -self.net_limit and order.price == tick.ask_price_1 and order.vt_symbol == self.maker:
                        self.cancel_order(vt_orderid)     
                        
                #挂单逻辑
                orders = list(self.strategy_engine.active_limit_orders.items())
                bid1_orders = np.sum([order.price == tick.bid_price_1 for vt_orderid, order in orders]) 
                ask1_orders = np.sum([order.price == tick.ask_price_1 for vt_orderid, order in orders]) 
                if abs(self.net) <= abs(self.net_limit):
                    if self.edgeP(tick) == 1 and bid1_orders == 0 and self.get_pos(self.maker) < self.maxPos:
                        self.buy(self.maker,tick.bid_price_1, self.lots,'down_buy')
                    if self.edgeP(tick) == -1 and ask1_orders == 0 and self.get_pos(self.maker) > -self.maxPos:
                        self.short(self.maker,tick.ask_price_1, self.lots,'up_sell') 
            
        
    def on_tick_refer(self,tick):
        self.rCount += 1 
        self.fair_refer = (tick.bid_price_1 + tick.ask_price_1) / 2
        
        if self.cancel_count >= 400:
            self.cancel_all()
            if self.net > 0:
                self.short(self.refer, tick.bid_price_1, abs(self.net))
            elif self.net < 0:
                self.buy(self.refer, tick.ask_price_1, abs(self.net))                
            return
        
        if self.maker in self.strategy_engine.ticks:
            tick_maker = self.strategy_engine.ticks[self.maker]
            self.on_maker_pricing(tick_maker)
 
        self.on_refer_cancel(tick)
            
        if abs(self.net) < self.net_limit:
            self.on_hedge_1(tick)
        else:
            self.on_hedge_2(tick)  
 
        self.on_refer_time(tick)
        self.last_net = self.net
 
    def on_tick_maker(self, tick):
        self.mCount += 1
    
        ts = self.priceticks[self.refer]
        net = self.net 
        lots = self.lots
 
        self.offer_list_head = ['insid','datetime','Basis','bollup','bolldown','MovingAverage','Std']   
        
    def  on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if volume > 0:
            if trade.direction.value=='多':
                if self.net>=0 :            
                     self.avg = (self.net*self.avg +volume*self.fair_refer)/(self.net+volume)              
                elif volume+self.net>0: # net<0 # 平仓
                     self.avg = self.fair_refer         
                self.net += volume 
            #         
            elif trade.direction.value=='空':    
                if self.net<=0:
                    self.avg =(-self.net*self.avg + volume*self.fair_refer)/(-self.net+volume)
                elif volume-self.net>0: # net >0 # 平仓
                    self.avg=self.fair_refer
                self.net -= volume 
        if trade.vt_symbol == self.maker:        
            trade.basePrice = self.fair_refer
            trade.midPrice = self.fair_maker
        self.trade_basis = trade.midPrice - trade.basePrice
                
    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return
        
        if order.status == Status.CANCELLED:
            self.cancel_count += 1
 
        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids,
            self.buy_refer_orderids,
            self.short_refer_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
 
    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """ # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if not(
            (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 55))
            or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 14))
            or (dt.time() > dtime(10, 31) and dt.time() < dtime(11, 29))
            or (dt.time() > dtime(13, 31) and dt.time() < dtime(14, 55))
        ):
            self.cancel_all()
            self.MMTrading = False
        else:
            self.MMTrading = True