# -*- coding: utf-8 -*-
"""
Created on Fri Oct 15 18:07:27 2021
#排队策略的参数
@author: zhanghc
"""
import os
path = "C:/Users/<USER>/Desktop/vnpy_repo/Chris/"
os.chdir(path)
#import python pacakges
import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
import math
from datetime import timedelta
import datetime

import GetMarketData


if __name__ == '__main__':

       
    # contracts = pd.read_excel("contracts.xlsx")
    tickers = ['eb2209.DCE','eb2210.DCE','v2209.DCE','v2301.DCE','TA209.CZC','MA209.CZC','i2209.DCE','i2301.DCE','pg2209.DCE','pg2210.DCE','pp2209.DCE','pp2301.DCE']
    # tickers = ['MA209.CZC','v2209.DCE','OI301.CZC','i2209.DCE','i2208.DCE','MA209.CZC','MA301.CZC',
    # 'TA209.CZC','TA301.CZC','i2301.DCE','pg2209.DCE','TA209.CZC','eb2209.DCE','eb2208.DCE','eg2209.DCE']
    # tickers = ['SR209.CZC','SR301.CZC','TA209.CZC','TA301.CZC','c2209.dce','cs2209.dce','m2209.dce',
    #            'sp2209.SHF','CF209.CZC','CF301.CZC','bc2207.ine','lu2208.ine','zn2207.shf']
    
    
    beginStr = '2022-8-3T21:00:00.0Z'
    endStr = '2022-8-4T15:00:00.0Z'
    # mode = 'dev'
    mode = 'prod'
    beginT = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    endT = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    for ticker in tickers:
        exchange = ticker[-3:]
        contract = ticker[:-4]
        # print (exchange)
        # print (contract)
        df = GetMarketData.getmarketdata(mode,exchange,contract,beginT,endT)
        df['v'] = df['v'].diff().fillna(0)
        avg_size = 0.5*(df['b_v1'].mean()+df['a_v1'].mean())
        print(contract,avg_size)
    
    
    