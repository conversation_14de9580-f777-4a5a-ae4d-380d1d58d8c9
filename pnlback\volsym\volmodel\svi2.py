import numpy as np
from scipy.optimize import minimize
import pandas as pd


class SVI:
    """
    SVI模型，用于拟合波动率曲线
    """

    def __init__(self, alpha, beta, rho, m, nu):
        self.alpha = alpha
        self.beta = beta
        self.rho = rho
        self.m = m
        self.nu = nu

    def check_params(self):
        if abs(self.rho) >= 1:
            print("参数约束不满足: ρ 的绝对值必须小于 1")
        if self.beta <= 0:
            print("参数约束不满足: β 必须大于 0")
        if self.nu <= 0:
            print("参数约束不满足: ν 必须大于 0")
        if self.alpha < 0:
            print("参数约束不满足: α 必须大于等于 0")

    def variance(self, k):
        """计算隐含波动率"""
        return self.alpha + self.beta * (self.rho * (k - self.m) + np.sqrt((k - self.m) ** 2 + self.nu ** 2))

    def sigma(self, k, t):
        return np.sqrt(self.variance(k) / t.iloc[0])

    def atm_volatility(self):
        """计算ATM波动率 (k=0)"""
        return self.variance(0)

    def first_derivative(self, k):
        """计算一阶导数"""
        return self.beta * (self.rho + (k - self.m) / np.sqrt((k - self.m) ** 2 + self.nu ** 2))

    def second_derivative(self, k):
        """计算二阶导数"""
        return self.beta * (self.nu ** 2 / ((k - self.m) ** 2 + self.nu ** 2) ** (3 / 2))

    def otm_slope(self):
        """OTM区域无穷远斜率 (k -> +∞)"""
        return self.beta * (1 + self.rho)

    def itm_slope(self):
        """ITM区域无穷远斜率 (k -> -∞)"""
        return self.beta * (self.rho - 1)

    def get_results(self, k):
        self.results = {
            'atm_vol': self.atm_volatility(),
            'skew': self.first_derivative(k),
            'convexity': self.second_derivative(k),
            'otm_slope': self.otm_slope(),
            'itm_slope': self.itm_slope()
        }
        return self.results

    def calculate_svi_parameters(self):
        for k in self.results:
            setattr(self, k, self.results[k])
        # 步骤 1: 反解 ρ
        rho = (self.otm_slope / self.itm_slope + 1) / (self.otm_slope / self.itm_slope - 1)

        # 步骤 2: 反解 β
        beta = self.otm_slope / (1 + rho)

        # 步骤 3: 反解 ν
        # 使用迭代计算ν
        # 初始猜测
        nu = 0.1
        for _ in range(10):  # 迭代10次
            nu = np.sqrt((self.second_derivative * (nu ** 2 + (beta / self.second_derivative) ** (2 / 3))) / beta)

        # 步骤 4: 反解 m
        # 从一阶导数计算 m
        m = (rho * np.sqrt(nu ** 2) * self.first_derivative) / (beta * (1 - rho))

        # 步骤 5: 反解 α
        alpha = self.atm_vol - beta * (-rho * m + np.sqrt(m ** 2 + nu ** 2))

        return {
            'alpha': alpha,
            'beta': beta,
            'rho': rho,
            'm': m,
            'nu': nu,
        }


def fit_svi_model(config, exp_data, exp, last_exp_data):
    """
        使用SVI模型拟合波动率曲线并计算导数
        
        参数:
            config: dict, 配置参数
            exp_data: DataFrame, 包含特定到期日的期权数据
            exp: str, 到期日
        
        返回:
            K_fit: ndarray, 拟合用的行权价序列
            sigma_fit: ndarray, 拟合的波动率值
            params: tuple, SVI模型参数(a,b,rho,m,sigma)
            derivatives: dict, 包含各种导数的字典
        """
    t = exp_data['time2expiry']
    k = np.log(exp_data['K'] / exp_data['forward'])
    v = exp_data['market_sigma'] ** 2 * t.iloc[0]

    # 计算vega权重并归一化
    weights = exp_data['vega'].clip(lower=0.000000001)
    weights /= weights.sum()

    # 获取上次的参数作为初始值
    if exp in last_exp_data:
        initial_params = last_exp_data[exp]['last_svi_params']
    else:
        initial_params = [0.04, 0.1, 0.1, 0.1, 0.1]

    def objective(params):
        a, b, rho, m, sigma = params
        names = ['a', 'b', 'rho', 'm', 'sigma']
        # 为不同参数设置不同的惩罚权重
        param_weights = config['param_weights']
        total_weight = config['svi_weights']

        # 参数约束
        if abs(rho) >= 1 or b <= 0 or sigma <= 0:
            return 1e6

        # 计算当前参数的ATM特征
        k_atm = 0  # ATM点的对数行权价为0
        svi_model = SVI(a, b, rho, m, sigma)
        atm_features = svi_model.get_results(k_atm)

        # 当前数据的拟合误差（vega加权）
        predicted = svi_model.variance(k)
        fit_error = np.sum((weights * (predicted - v)) ** 2)

        # 与上次参数的偏离程度
        if exp in last_exp_data:
            last_params = last_exp_data[exp]['last_svi_params']
            last_atm_features = last_exp_data[exp]['last_atm_features']

            # 计算加权参数变化惩罚
            param_change_penalty = sum([
                param_weights[param] * ((p - l_p) / l_p) ** 2
                for param, p, l_p in zip(names, params, last_params)
            ])

            # 计算ATM特征的变化惩罚
            atm_penalty = sum(
                config['atm_weights'][key] * (
                        (atm_features[key] - last_atm_features[key]) / (abs(last_atm_features[key]) + 1e-6)) ** 2
                for key in ['atm_vol', 'skew', 'convexity', 'otm_slope', 'itm_slope']
            )

            # 根据时间间隔调整惩罚权重
            if hasattr(exp_data, 'current_time') and hasattr(last_exp_data, 'last_update_time'):
                time_diff = (exp_data['current_time'] - last_exp_data['last_update_time']).total_seconds()
                # 使用更平缓的时间衰减
                time_weight = np.exp(-time_diff / total_weight['time_decay'])  # 20秒的衰减周期
            else:
                time_weight = 0.5

            # 总体惩罚权重随到期时间调整
            if hasattr(exp_data, 'time2expiry'):
                ttm = exp_data['time2expiry'].iloc[0]  # 到期时间，以年为单位
                # 到期时间越短，惩罚越大，保持短期曲线更稳定
                ttm_weight = np.exp(-ttm * 2)  # 随到期时间指数衰减
            else:
                ttm_weight = 1.0

            # 组合所有权重
            final_penalty_weight = total_weight['fit'] * time_weight * ttm_weight * (
                    total_weight['param'] * param_change_penalty + total_weight['atm'] * atm_penalty)
            return fit_error + final_penalty_weight

        return fit_error

    # 设置参数优化边界
    bounds = [
        (-np.inf, np.inf),  # a
        (0, np.inf),  # b
        (-1, 1),  # rho
        (-np.inf, np.inf),  # m
        (0, np.inf)  # sigma
    ]

    # 优化
    result = minimize(objective, initial_params, bounds=bounds, method='L-BFGS-B', tol=1e-12)

    if result.success:
        # 保存本次的ATM特征，供下次使用
        k_atm = 0
        svi_model = SVI(*result.x)
        atm_features = svi_model.get_results(k_atm)

        # 生成拟合曲线
        sigma_fit = svi_model.sigma(k, t)

        # 计算导数
        derivatives = svi_model.get_results(k)

        rmse_error = np.sqrt(np.mean((weights * (sigma_fit - exp_data['market_sigma'])) ** 2))

        r2 = 1 - np.sum((exp_data['market_sigma'] - sigma_fit) ** 2) / np.sum(
            (exp_data['market_sigma'] - np.mean(exp_data['market_sigma'])) ** 2)

        # 创建包含所有数据的字典
        voltime = {
            **dict(zip(['a', 'b', 'rho', 'm', 'sigma'], result.x)),  # SVI参数
            **atm_features,  # ATM特征
            'rmse_error': rmse_error,
            'r2': r2,
        }

        return exp, sigma_fit, voltime
    else:
        return None, None, None
