"""
回测模块
@author: lining
"""
import pandas as pd
import numpy as np
import datetime
from tqdm import tqdm
from core import config
from utils.utils import log_print

class OrderBookBacktester:
    """订单簿回测器"""

    def __init__(self, data=None, predictions=None):
        """
        初始化回测器
        
        参数:
            data (pandas.DataFrame): 原始数据
            predictions (pandas.DataFrame): 预测结果
        """
        self.data = data
        self.predictions = predictions
        self.results = None
        self.trades = []
        self.position = 0
        self.entry_price = 0
        self.holding_period = 0
        self.entry_time = None
        self.exit_time = None
        self.avg_price = 0
        self.spread = 0
        self.max_pnl = 0

        # 获取参数
        self.threshold_percent = config.THRESHOLD_PERCENT
        self.max_holding_period = config.HOLDING_PERIOD
        self.commission = config.COMMISSION
        self.max_position = config.MAX_POSITION
        self.order_size = config.ORDER_SIZE
        self.stop_loss = config.STOP_LOSS
        self.take_profit = config.TAKE_PROFIT
        self.use_mid_price = config.USE_MID_PRICE
        self.min_profit = config.MIN_PROFIT
        self.spread_threshold_percent = config.SPREAD_THRESHOLD/self.current_price

    def set_data(self, data, predictions=None):
        """
        设置数据
        
        参数:
            data (pandas.DataFrame): 原始数据
            predictions (pandas.DataFrame): 预测结果
        """
        self.data = data
        if predictions is not None:
            self.predictions = predictions

    def on_trade(self, df,i,type,reason):
        """
        交易回调函数
        """
        df['trade_type'].iloc[i] = type
        df['reason'].iloc[i] = reason
        if type == 'close':
            df['exit_price'].iloc[i] = self.current_price
            df['tradevol'].iloc[i] = abs(self.position)
            df['return'].iloc[i] = self.position * (self.current_price / self.avg_price - 1)
            self.holding_period = (self.current_time - self.entry_time).total_seconds()
            self.position = 0
        elif type == 'open':
            self.position = self.position+self.current_signal * self.order_size
            df['tradevol'].iloc[i] = abs(self.order_size)
            self.entry_price = self.current_price
            self.avg_price = (self.avg_price * (self.position-self.current_signal*self.order_size) + self.current_price * self.current_signal*self.order_size) / (self.position)
            self.entry_time = self.current_time
            df['entry_price'].iloc[i] = self.current_price
        
        df['spread'].iloc[i] = abs(self.spread*df['tradevol'].iloc[i])
        df['position'].iloc[i] = self.position
        df['commission'].iloc[i] = abs(self.commission*df['tradevol'].iloc[i])
        
        trade = {
            'reason': reason,
            'type': type,
            'entry_time': self.entry_time,
            'exit_time': self.current_time,
            'position': self.position,
            'tradevol': df['tradevol'].iloc[i],
            'entry_price': self.avg_price.round(2),
            'exit_price': self.current_price,
            'return': df['return'].iloc[i],
            'commission': df['commission'].iloc[i],
            'spread': df['spread'].iloc[i],
            'holding_period': (self.current_time - self.entry_time).total_seconds()   # 秒
        }
        self.trades.append(trade)

        if type == 'close':
            self.entry_price = 0
            self.entry_time = None
            self.avg_price = 0
            self.holding_period = 0
            self.max_pnl = 0
        
        log_print(f"{reason} {type}: {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S')} 方向: {self.current_signal} 持仓: {self.position} 价格: {self.current_price} avg: {self.avg_price} 盈亏: {df['return'].iloc[i]:.6f} 持仓时间: {self.holding_period:.2f} 秒",'debug')

    def run_backtest(self, target_col=None):
        """
        运行回测
        
        参数:
            target_col (str): 目标列名
            threshold_percent (float): 信号阈值分位数
            holding_period (int): 持仓周期
            commission (float): 手续费率
            max_position (float): 最大仓位
            stop_loss (float): 止损比例
            take_profit (float): 止盈比例
            use_mid_price (bool): 是否使用中间价
            
        返回:
            tuple: (回测结果, 摘要, 交易记录)
        """
        if self.data is None:
            raise ValueError("数据未设置")

        if self.predictions is None:
            raise ValueError("预测结果未设置")


        # 检查预测列是否存在
        pred_col = f"{target_col}_pred"
        if pred_col not in self.predictions.columns:
            raise ValueError(f"预测结果中不存在列 {pred_col}")

        # 合并数据
        df = self.data.copy()
        df[pred_col] = self.predictions[pred_col]

        # 检查价格列
        price_col = 'mid' if self.use_mid_price else 'close'
        if price_col not in df.columns:
            if price_col == 'mid' and 'AskPrice1' in df.columns and 'BidPrice1' in df.columns:
                df['mid'] = (df['AskPrice1'] + df['BidPrice1']) / 2
                log_print(f"已创建 {price_col} 列")
            else:
                raise ValueError(f"数据中不存在价格列 {price_col}")

        # 生成信号
        threshold = df[pred_col].abs().quantile(self.threshold_percent)
        # 信号绝对值的分布
        df['signal'] = 0
        df.loc[df[pred_col] > threshold, 'signal'] = 1
        df.loc[df[pred_col] < -threshold, 'signal'] = -1

        # 初始化结果列
        df['position'] = np.nan
        df['entry_price'] = np.nan
        df['exit_price'] = np.nan
        df['return'] = 0
        df['commission'] = np.nan
        df['spread'] = np.nan
        df['tradevol'] = np.nan
        df['cum_return'] = 1.0
        df['trade_type'] = np.nan
        df['reason'] = np.nan

        # 回测逻辑
        is_close=False


        for i in tqdm(range(len(df)), desc="回测进度"):
            self.current_time = df.index[i]
            self.current_price = df[price_col].iloc[i]
            self.current_signal = df['signal'].iloc[i]
            pnl=(self.current_price / self.avg_price - 1)*self.position
            self.spread = (df['AskPrice1'].iloc[i] - df['BidPrice1'].iloc[i])/df['mid'].iloc[i]/2

            if self.predictions.index[i] == self.predictions.index[-1]:
                continue

            is_smallspread=self.spread<=self.spread_threshold_percent

            # 检查是否在收盘前或午休时间段需要平仓
            current_time = self.current_time.time()
            close_time = (datetime.datetime.strptime(config.TIME_RANGE[-1][1], '%H:%M:%S') - datetime.timedelta(minutes=3)).time()
            noon_close_time = (datetime.datetime.strptime(config.TIME_RANGE[0][1], '%H:%M:%S') - datetime.timedelta(minutes=3)).time()
            noon_open_time = datetime.datetime.strptime(config.TIME_RANGE[-1][0], '%H:%M:%S').time()
            
            if current_time >= close_time or (current_time >= noon_close_time and current_time <= noon_open_time):
                if self.position != 0:
                    self.on_trade(df, i, 'close', '收盘平仓')
                continue
            
            # 检查是否需要平仓
            if self.position != 0:
                # 检查持仓时间
                holding_period = (self.current_time - self.entry_time).total_seconds()
                if self.entry_time is not None and holding_period >= self.max_holding_period:
                    # 平仓
                    if is_smallspread or holding_period >= self.max_holding_period*5:
                        is_close=True
                        reason='超时'

                # 检查止损
                elif self.stop_loss is not None and pnl <= -self.stop_loss:
                    # 止损平仓
                    is_close=True
                    reason='止损'

                # 检查止盈
                elif self.take_profit is not None :
                    # 止盈平仓
                    if pnl >= self.min_profit:
                        is_close=True                    
                        reason='止盈'

                if is_close:  # 当有平仓时记录交易
                    self.on_trade(df,i,'close',reason)
                    is_close=False
                    continue
            
            # 检查是否需要开仓
            if self.current_signal != 0:
                if self.position*self.current_signal < 0:
                    # 平仓
                    self.on_trade(df,i,'close','信号平仓')
                else:
                    # 开仓
                    if is_smallspread:
                        if abs(self.position) < config.MAX_POSITION:
                            self.on_trade(df,i,'open','开仓')
                        else:
                            log_print(f"仓位过大 {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S')} 仓位: {self.position:.2f} 最大仓位: {config.MAX_POSITION:.2f}",'debug')
                    else:
                        log_print(f"价差过宽 {datetime.datetime.strftime(self.current_time, '%Y-%m-%d %H:%M:%S')} 价差: {self.spread:.6f} 阈值: {self.spread_threshold_percent:.6f}",'debug')
        
        # 将交易记录转换为DataFrame并保存
        self.trades = pd.DataFrame(self.trades)

        # 添加统计信息
        self.trades['profit'] = self.trades['return'] > 0

        # 计算累计收益
        df['cum_return'] = (1 + df['return']).cumprod()
        df['net_return'] = (1 + df['return'] - df['commission'] - df['spread']).cumprod()
        df['total_trades'] = df['tradevol'].cumsum()

        # 计算回测指标
        total_return = df['cum_return'].iloc[-1] - 1
        annual_return = (1 + total_return) ** (252 / len(df)) - 1
        daily_returns = df['return'].resample('D').sum()
        sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() != 0 else 0
        max_drawdown = (df['cum_return'] / df['cum_return'].cummax() - 1).min()
        win_rate = len(df[df['return'] > 0]) / len(df[df['return'] != 0]) if len(df[df['return'] != 0]) > 0 else 0
        win_loss_ratio = abs(df[df['return'] > 0]['return'].mean() / df[df['return'] < 0]['return'].mean()) if len(df[df['return'] < 0]) > 0 else 0

        # 保存结果
        self.results = df

        # 创建回测摘要
        summary = {
            '总收益': total_return,
            '年化收益': annual_return,
            '夏普比率': sharpe_ratio,
            '最大回撤': max_drawdown,
            '胜率': win_rate,
            '盈亏比': win_loss_ratio,
            '手续费': df['commission'].sum(),
            '价差': df['spread'].sum(),
            '阈值': threshold,
            '持仓周期': self.max_holding_period,
            '交易量': df['tradevol'].sum(),
            '信号次数': len(df[df['signal']!=0]),
            '交易次数': len(df[df['tradevol'].notna()]),
            '开仓次数': len(df[df['trade_type'] == 'open']),
            '平仓次数': len(df[df['trade_type'] == 'close']),
        }
        reasons = df['reason'].unique()
        for reason in reasons:
            summary[reason] = len(df[df['reason'] == reason])

        log_print("回测完成，结果摘要:")
        for key, value in summary.items():
            log_print(f"  {key}: {value:.4f}" if isinstance(value, float) else f"  {key}: {value}")

        return df, summary, self.trades
