from data_process import FactorDataGenerator
from model import OLSModel, RandomForestModel, LassoModel

if __name__ == '__main__':
    # 生成数据
    generator = FactorDataGenerator(data_dir='C:/Users/<USER>/Desktop/data', future='IC')
    generator.load_data()
    # split_method:按照日来划分训练集和测试集，否则按照所有数据
    generator.generate_factor_data(use_similar=False, split_method='day') 
    generator.save_to_csv('C:/Users/<USER>/Desktop/data/featuresIC.csv')
    # 模型训练以及结果输出
    # model = RandomForestModel(data_path='./data/featuresIC.csv')
    model = OLSModel(data_path='C:/Users/<USER>/Desktop/data/featuresIC.csv')
    # model = LassoModel(data_path='./data/featuresIC.csv')
    model.run()
