
"""
模型训练模块
@author: lining
"""
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import ExtraTreesRegressor
from sklearn.neural_network import MLPRegressor
import xgboost as xgb
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from utils.utils import log_print
from models.lstm_regressor import LSTMRegressor

def set_model(model_type='xgboost', params=None):
    
    
    if params is None:
        log_print("未模型设置参数")

    if model_type == 'xgboost':
        
        
        
        
        model = xgb.XGBRegressor(**params)
    elif model_type == 'lightgbm':
        
        
        
        
        model = lgb.LGBMRegressor(**params)
    elif model_type == 'random_forest':
        
        
        
        
        model = RandomForestRegressor(**params)
    elif model_type == 'gbdt':
        
        
        
        
        model = GradientBoostingRegressor(**params)
    elif model_type == 'linear':
        
        
        
        
        model = LinearRegression(**params)
    elif model_type == 'svm':
        
        
        
        
        model = SVR(**params)
    elif model_type == 'mlp':
        
        
        
        
        model = MLPRegressor(**params)
    elif model_type == 'knn':
        
        
        
        
        model = KNeighborsRegressor(**params)
    elif model_type == 'decision_tree':
        
        
        
        
        model = DecisionTreeRegressor(**params)
    elif model_type == 'extra_trees':
        
        
        
        
        model = ExtraTreesRegressor(**params)
    elif model_type == 'lstm':
        
        
        
        
        model = LSTMRegressor(**params)

    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

    return model





