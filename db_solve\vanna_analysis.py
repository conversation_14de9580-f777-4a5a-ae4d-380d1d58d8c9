import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from scipy.stats import norm

def calculate_d1(S, K, r, T, sigma):
    """计算 d1"""
    return (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))

def calculate_d2(d1, sigma, T):
    """计算 d2"""
    return d1 - sigma * np.sqrt(T)

def calculate_vanna_old(S, K, r, T, sigma):
    """原始公式计算 Vanna"""
    d1 = calculate_d1(S, K, r, T, sigma)
    d2 = calculate_d2(d1, sigma, T)
    d1_pdf = norm.pdf(d1)
    return -d1_pdf * d2 / sigma

def calculate_vega(S, K, r, T, sigma):
    """计算 Vega"""
    d1 = calculate_d1(S, K, r, T, sigma)
    return S * np.sqrt(T) * norm.pdf(d1)

def calculate_delta(S, K, r, T, sigma):
    """计算 Delta"""
    d1 = calculate_d1(S, K, r, T, sigma)
    return norm.cdf(d1)

def calculate_vanna_numerical(S, K, r, T, sigma, h=0.0001):
    """使用数值方法计算 Vanna
    方法1：通过 dvega/dS 计算
    方法2：通过 ddelta/dsigma 计算
    """
    # 方法1：计算 dvega/dS
    vega_up = calculate_vega(S + h, K, r, T, sigma)
    vega_down = calculate_vega(S - h, K, r, T, sigma)
    vanna_1 = (vega_up - vega_down) / (2 * h)
    
    # 方法2：计算 ddelta/dsigma
    delta_up = calculate_delta(S, K, r, T, sigma + h)
    delta_down = calculate_delta(S, K, r, T, sigma - h)
    vanna_2 = (delta_up - delta_down) / (2 * h)
    
    return vanna_1, vanna_2

# 参数设置
S = 100  # 固定标的价格
r = 0.05
T = 0.1
sigma = 0.2

# 生成行权价格范围
K_values = np.linspace(50, 150, 100)

# 计算各种方法的结果
vanna_values = []
vanna_num1_values = []
vanna_num2_values = []

for K in K_values:
    vanna = calculate_vanna_old(S, K, r, T, sigma)
    vanna_num1, vanna_num2 = calculate_vanna_numerical(S, K, r, T, sigma)
    
    vanna_values.append(vanna)
    vanna_num1_values.append(vanna_num1)
    vanna_num2_values.append(vanna_num2)

# 创建plotly图表
fig = go.Figure()

# 添加三条线
fig.add_trace(
    go.Scatter(x=K_values, y=vanna_values, name='Analytical Formula',
               line=dict(color='blue'))
)
fig.add_trace(
    go.Scatter(x=K_values, y=vanna_num1_values, name='Numerical (dVega/dS)',
               line=dict(color='green', dash='dash'))
)
fig.add_trace(
    go.Scatter(x=K_values, y=vanna_num2_values, name='Numerical (dDelta/dSigma)',
               line=dict(color='orange', dash='dash'))
)

# 添加垂直线标记当前股价S
fig.add_vline(x=S, line_dash="dash", line_color="red", opacity=0.3)
fig.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.3)

# 更新布局
fig.update_layout(
    title='Vanna vs Strike Price (K)',
    xaxis_title='Strike Price (K)',
    yaxis_title='Vanna',
    hovermode='x unified',
    template='plotly_white',
    width=1000,
    height=600,
    showlegend=True,
    legend=dict(
        yanchor="top",
        y=0.99,
        xanchor="left",
        x=0.01
    )
)

# 添加网格线
fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='LightGray')
fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='LightGray')

# 显示图表
fig.show()

# 打印数值比较表格
test_prices = [80, 100, 120]
print("\nVanna值比较：")
print("价格\t\t原始公式\t\t修正公式\t\tdVega/dS\t\tdDelta/dSigma")
print("-" * 90)
for S in test_prices:
    old_vanna = calculate_vanna_old(S, K, r, T, sigma)
    new_vanna = calculate_vanna_new(S, K, r, T, sigma)
    num_vanna1, num_vanna2 = calculate_vanna_numerical(S, K, r, T, sigma)
    print(f"{S}\t\t{old_vanna:.6f}\t\t{new_vanna:.6f}\t\t{num_vanna1:.6f}\t\t{num_vanna2:.6f}")

# 创建plotly图表
fig = go.Figure()

# 计算Vega值
vega_values = []
for S in S_values:
    vega = calculate_vega(S, K, r, T, sigma)
    vega_values.append(vega)

# 添加Vega线
fig.add_trace(
    go.Scatter(x=S_values, y=vega_values, name='Vega',
               line=dict(color='blue'))
)

# 添加垂直线标记行权价K
fig.add_vline(x=K, line_dash="dash", line_color="red", opacity=0.3)

# 更新布局
fig.update_layout(
    title='Vega vs Stock Price',
    xaxis_title='Underlying Asset Price (S)',
    yaxis_title='Vega',
    hovermode='x unified',
    template='plotly_white',
    width=1000,
    height=600,
    showlegend=True,
    legend=dict(
        yanchor="top",
        y=0.99,
        xanchor="left",
        x=0.01
    )
)

# 添加网格线
fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='LightGray')
fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='LightGray')

# 显示图表
fig.show()

# 打印一些关键点的Vega值
test_prices = [80, 90, 100, 110, 120]
print("\nVega值比较：")
print("价格(S)\t\tVega")
print("-" * 30)
for S in test_prices:
    vega = calculate_vega(S, K, r, T, sigma)
    print(f"{S}\t\t{vega:.6f}")
