""""""
import numpy as np
from typing import List, Dict, Union
from datetime import datetime, time as dtime

from vnpy.app.my_portfolio_strategy import(
    StrategyTemplate,
    StrategyEngine,
    BacktestingEngine
)
from vnpy.trader.object import TickData, OrderData, TradeData


class PFTest(StrategyTemplate):
    """"""

    author = ""

    loss = 3000  # max loss per cycle
    lots = 10  # 做市报单量（maker）
    maxV = 20  # 单笔最大下单数量 （因为交易所规定超过maxV，手续费不返还）
    edge = 5  # 最大有效价宽 tick数（ask-bid）
    minEdge = 5  # 报价最小价宽  （有些合约只考核时间义务，报窄了没有意义, 输入数值大于1 起作用,一般就是交易所规定价宽）
    maxPos = 5*lots  # 最大单边 or 跨期持仓量 || 建议最少为lots的 3~5倍
    validVolume = 5*lots  # 有效量 （数值越大, 价宽越大; 太小有可能被慌偏，逆向选择概率大）
    safeVolume = 15*lots  # 安全量 （市场难被击穿的量, 报价可以优于对应价格）
    gamma = 5  # 持仓调整系数  1.0~100. 越大风险偏好越小、 增仓越不积极、平仓越积极
    eta = 0.8  # 价差调整系数  0.0~2.0   （ 需参考义务统计跟踪调整、平衡成交量、价差和盈亏 ）
    isBetter = False  # 是否自动更优报价， 某些条件满足选择更积极报价
    isLevel0 = True
    isReverse = True

    _maker = ""  # 做市合约
    _refer = ""  # 主力、流动性最好的合约、或者次主力、至少比做市合约流动 ........性好很多
    ts = 0
    mu = 0
    hold = 2*lots  # 最小持仓量 （ 单边 for 持仓量 义务 )

    parameters = [
        '_maker',
        '_refer',
        'loss',
        'lots',
        'maxV',
        'edge',
        'minEdge',
        'maxPos',
        'validVolume',
        'safeVolume',
        'gamma',
        'eta',
        'isBetter',
        'isLevel0',
        'isReverse',
    ]
    variables = [
        'ts',
        'mu',
        'hold'
    ]

    def __init__(
        self,
        strategy_engine: Union[StrategyEngine, BacktestingEngine],
        strategy_name: str,
        vt_symbols: List[str],
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbols, setting)
        # 拆分目标单和主力单

        self.buy_vt_orderids = None
        self.sell_vt_orderids = None

        self.ts = strategy_engine.priceticks[self._maker]
        self.mu = strategy_engine.sizes[self._maker]

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """
        if (
            (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 55))
            or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 10))
            or (dt.time() > dtime(10, 35) and dt.time() < dtime(11, 25))
            or (dt.time() > dtime(13, 35) and dt.time() < dtime(14, 55))
        ):
            self.trading = True
        else:
            self.trading = False

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        if self.target_symbol not in self.strategy_engine.update_vt_symbols:
            return

        tick = ticks[self.target_symbol]

        ts = 1
        self.bidP1 = tick.bid_price_1
        self.askP1 = tick.ask_price_1
        self.bidV1 = tick.bid_volume_1
        self.askV1 = tick.ask_volume_1
        self.lastP = tick.last_price
        spread = self.askP1 - self.bidP1
        midP = (self.bidP1+self.askP1)/2
        net = self.pos[self.target_symbol]
        bidV = 0
        askV = 0
        bidP = self.bidP1 - ts
        askP = self.askP1 + ts

        if self.midP1 and self.lastP1:
            if spread <= 2 and np.sign(midP - self.lastP) == np.sign(self.midP1-self.lastP1):
                self.signal = midP - self.lastP
                bidV = self.fixed_size
                askV = self.fixed_size
                if self.signal > 0:  # 预计未来价格上涨
                    if spread == 1:
                        bidP = self.bidP1  # 针对lastP在bidP1和askP1之外的情况
                        askP = self.askP1 + ts
                    if spread == 2:
                        bidP = self.bidP1
                        askP = self.askP1
                    if net > 0:  # 我们不希望裸露过多的多头头寸
                        bidP = bidP - ts

                elif self.signal < 0:   # 预计未来价格下跌
                    if spread == 1:
                        bidP = self.bidP1 - ts
                        askP = self.askP1
                    if spread == 2:
                        bidP = self.bidP1
                        askP = self.askP1
                    if net < 0:  # 我们不希望裸露过多的空头头寸
                        askP = askP + ts

                else:  # 预计未来价格变动不大,指spread==2的情况
                    bidP = self.bidP1
                    askP = self.askP1
                    if net > 0:  # 平仓
                        askP = self.askP1 - ts
                    if net < 0:
                        bidP = self.bidP1 + ts

            else:  # 无信号2种情况 1:无连续信号 2：流动性较差
                self.signal = None  # 无信号的情况
                if spread <= 2:  # 只对流动性好的时候报价，价差放大，停止报价
                    bidP = self.bidP1 - ts
                    askP = self.askP1 + ts
                    bidV = self.fixed_size
                    askV = self.fixed_size

                    if net > 0:
                        askP = self.bidP1 + ts
                    if net < 0:
                        bidP = self.askP1 - ts

                else:  # 流动性较差停止报价，但如果存在净持仓，则朝持仓为0的方向激进报价
                    bidV = 0
                    askV = 0

                    if net > 0:
                        askP = max(self.askP1 - ts, self.bidP1 + 2*ts)

                    if net < 0:
                        bidP = min(self.bidP1 + ts, self.askP1 - 2*ts)

        if self.bidP1 > 0 and self.askP1 > 0:
            self.mCount += 1

        if self.buy_vt_orderids and (self.last_bid != bidP or bidV == 0):
            for orderID in self.buy_vt_orderids:
                self.cancel_order(orderID)
            self.buy_vt_orderids = None
        if self.sell_vt_orderids and (self.last_ask != askP or askV == 0):
            for orderID in self.sell_vt_orderids:
                self.cancel_order(orderID)
            self.sell_vt_orderids = None

        # 交易时间段内检查之前委托都已经结束
        if self.trading:
            if not self.buy_vt_orderids:
                if bidV > 0 and askV > 0:
                    self.buy_vt_orderids = self.buy(
                        self.target_symbol, bidP, bidV)
                    self.last_bid = bidP

            if not self.sell_vt_orderids:
                if bidV > 0 and askV > 0:
                    self.sell_vt_orderids = self.short(
                        self.target_symbol, askP, askV)
                    self.last_ask = askP

        # 记录上一批的lastP和midP
        self.lastP1 = self.lastP
        self.midP1 = midP

    def on_trade(self, trade: TradeData) -> None:
        """
        Callback of new trade data update.
        """
        pass

    def on_order(self, order: OrderData) -> None:
        """
        Callback of new order data update.
        """
        pass


# functions
def getBid5(tick: TickData, bid_volume, away_price):
    """
    根据最新tick行情（5档），计算如果需要成交bid_volume量
    的最优价bidP， 以及最优价能成交的最大量bidV.
    """
    # 1b
    bidP = tick.bid_price_1 if tick.bid_volume_1 > 0 else tick.limit_down*0.8
    bidV = tick.bid_volume_1   # 无价则为 0
    bid_volume -= tick.bid_volume_1
    # 2b
    if bid_volume > 0:
        bidP = tick.bid_price_2 if tick.bid_volume_2 > 0 else bidP
        bidV += tick.bid_volume_2
        bid_volume -= tick.bid_volume_2
    # 3b
    if bid_volume > 0:
        bidP = tick.bid_price_3 if tick.bid_volume_3 > 0 else bidP
        bidV += tick.bid_volume_3
        bid_volume -= tick.bid_volume_3
    # 4b
    if bid_volume > 0:
        bidP = tick.bid_price_4 if tick.bid_volume_4 > 0 else bidP
        bidV += tick.bid_volume_4
        bid_volume -= tick.bid_volume_4
    # 5b
    if bid_volume > 0:
        bidP = tick.bid_price_5 if tick.bid_volume_5 > 0 else bidP
        bidV += tick.bid_volume_5
        bid_volume -= tick.bid_volume_5
    # 5+ b
    if bid_volume > 0:
        bidP -= away_price  # or  bidP=tick.bid_price_5*0.99
        # print("Bid volume shortage...")
    return bidP, bidV


def getAsk5(tick: TickData, ask_volume, away_price):
    """
    根据最新深度行情（5档），计算如果需要成交 ask_volume 量的最优价 askP，
    以及最优价能成交的最大量 askV. 有可能不足5挡行情，其他数值为0
    (可能需要在行情端预处理， 正常情况 量都大于0， 也有为 NAN情况 ）
    """
    # 1a
    askP = tick.ask_price_1 if tick.ask_volume_1 > 0 else tick.limit_up*1.2
    askV = tick.ask_volume_1  # 无价则为 0
    ask_volume -= tick.ask_volume_1
    # 2a
    if ask_volume > 0:
        askP = tick.ask_price_2 if tick.ask_volume_2 > 0 else askP  # if
        askV += tick.ask_volume_2
        ask_volume -= tick.ask_volume_2
    # 3a
    if ask_volume > 0:
        askP = tick.ask_price_3 if tick.ask_volume_3 > 0 else askP
        askV += tick.ask_volume_3
        ask_volume -= tick.ask_volume_3
    # 4a
    if ask_volume > 0:
        askP = tick.ask_price_4 if tick.ask_volume_4 > 0 else askP
        askV += tick.ask_volume_4
        ask_volume -= tick.ask_volume_4
    # 5a
    if ask_volume > 0:
        askP = tick.ask_price_5 if tick.ask_volume_5 > 0 else askP
        askV += tick.ask_volume_5
        ask_volume -= tick.ask_volume_5
    # 5+ a
    if ask_volume > 0:
        askP += away_price
        # print("Ask volume shortage...")
    return askP, askV


# stable price
def getStableAsk5(tick: TickData, minVolume, maxVolume, lastP, away_price):
    """
    依据订单深度和最新价获得稳定的ask
    订单深度够的话使用最新价，否则使用最深的价格
    """
    askP_min, askV_min = getAsk5(tick, minVolume, away_price)
    askP_max, askV_max = getAsk5(tick, maxVolume, away_price)
    if askP_min <= lastP:
        return lastP, askV_min
    else:
        return askP_max, askV_max


def getStableBid5(quote, minVolume, maxVolume, lastP, away_price):
    """
    依据订单深度和最新价获得稳定的bid
    订单深度够的话使用最新价，否则使用最深的价格
    """
    bidP_min, bidV_min = getBid5(quote, minVolume, away_price)
    bidP_max, bidV_max = getBid5(quote, maxVolume, away_price)
    if bidP_min >= lastP:
        return lastP, bidV_min
    else:
        return bidP_max, bidV_max


def getFadeVolume(net, gamma, maxPos):
    """
    根据现在的持仓，返回对bid和ask订单厚度的偏移量
    gamma: 持仓调整系数1.0~100. 越大风险偏好越小、增仓越不积极、平仓越积极
    """
    bidFade = 0
    askFade = 0
    if net > 0:
        if net >= 0.6*maxPos:
            # askFade= -round(gamma*net)      # <0
            bidFade = round(3*gamma*net)     # >0
        else:
            # askFade= -round(0.5*gamma*net)  # <0
            bidFade = round(gamma*net)       # >0
    if net < 0:
        if net <= -0.6*maxPos:
            askFade = -round(3*gamma*net)    # >0
            # bidFade= round(gamma*net)       # <0
        else:
            askFade = -round(gamma*net)      # >0
            # bidFade= round(0.5*gamma*net)   # <0
    return bidFade, askFade


def getFairPrice(lastP, askP, bidP, edgeP):
    """平价"""
    fair = lastP
    if askP > 0 and bidP > 0:
        if askP-bidP <= edgeP:  # 有流动性，中间价
            fair = 0.5*(askP+bidP)
        else:   # 流动性不足, 不变
            if lastP > askP:
                fair = askP
            if lastP < bidP:
                fair = bidP
    return fair

def on_trade(trade, net, avg, realPnl):
    volume = trade.volume
    price = trade.price
    if trade.direction == "BUY":
        if net >= 0:
            avg = (net*avg + volume*price)/(net+volume)
        elif volume+net > 0:  # net<0 # 平仓
            avg = price
        net += volume
        realPnl -= volume*price
    #
    if trade.direction == "SELL":
        if net <= 0:
            avg = (-net*avg + volume*price)/(-net+volume)
        elif volume-net > 0:  # net >0 # 平仓
            avg = price
        net -= volume
        realPnl += volume*price
    logging.info("trade:,"+str(trade.price)+","+str(trade.volume)+","+trade.direction +
                 ","+str(datetime.now())+",Net:"+str(net)+",Avg:"+str(avg))
    return net, avg, realPnl


def cancelALL(bidOrder, askOrder, api):
    if bidOrder:
        api.cancel_order(bidOrder)
    if askOrder:
        api.cancel_order(askOrder)
    return None, None
#


def checkOrderList(orderList, api, net, avg, realPnl):
    trade = None
    if orderList:
        for d in list(orderList.keys()):
            order = api.get_order(order_id=d)
            # 仅以后一定不会再产生成交的状态
            if order.is_dead or order.is_error:
                rc = order.trade_records
                for td in rc:  # 该笔订单对应的成交记录
                    net, avg, realPnl = on_trade(rc[td], net, avg, realPnl)
                    trade = rc[td]
                del orderList[d]
    return orderList, net, avg, realPnl, trade
#
