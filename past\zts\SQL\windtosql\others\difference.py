# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-23
from WindPy import w
import pymssql
from datetime import datetime

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = "2017-06-23"  # datetime.now()
#beginDate = "2017-06-24"
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')
cursor = conn.cursor()
cursor.execute("""
 IF OBJECT_ID('stockprice', 'U') IS NOT NULL
    DROP TABLE stockprice
 CREATE TABLE stockprice (
    DateTime DATE NOT NULL,
    Time TIME(7) NOT NULL,
    STKID VARCHAR(20) NOT NULL,
    OPENPrice numeric(15, 2),
   HIGH numeric(15, 2),
   LOW numeric(15, 2),
    CLOSEPrice numeric(15, 2),
    Volume BIGINT,
    Amount numeric(15, 2),
    Ticks BIGINT,
    )
 """)
sql = "INSERT INTO stockprice VALUES (%s,%d, %s, %d, %d, %d, %d, %d, %d,%d)"

# 通过wset来取数据集数据
print('\n\n' + '-----通过wset来取数据集数据,获取0823中证500代码列表-----' + '\n')
wsetdata1 = w.wset("sectorconstituent", "date=2017-08-23;windcode=000905.SH")
print(wsetdata1)

# 通过wset来取数据集数据
print('\n\n' + '-----通过wset来取数据集数据,获取0322中证500代码列表-----' + '\n')
wsetdata2 = w.wset("sectorconstituent", "date=2017-03-22;windcode=000905.SH")
print(wsetdata2)

stocks = list(set(wsetdata1.Data[1])-(set(wsetdata2.Data[1])))

for j in range(0, len(stocks)):
    # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
    print("\n\n-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----\n" % (j, str(stocks[j])))
    wssdata = w.wss(str(stocks[j]), 'ipo_date')
    wsddata1 = w.wsd(str(stocks[j]), "open,high,low,close,volume,amt", wssdata.Data[0][0], dt, "Fill=Previous")

    #wsddata1 = w.wsd(str(stocks[j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
    if wsddata1.ErrorCode != 0:
        continue
    print(wsddata1)
    for i in range(0, len(wsddata1.Data[1])):
        sqllist = []
        sqltuple = ()
        if len(wsddata1.Times) > 1:
            sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
            sqllist.append("00:00:00.0000000")

        sqllist.append(str(stocks[j]))

        for k in range(0, len(wsddata1.Fields)):
            sqllist.append(wsddata1.Data[k][i])

        sqllist.append(0)

        sqltuple = tuple(sqllist)
        cursor.execute(sql, sqltuple)
    conn.commit()
conn.close()

