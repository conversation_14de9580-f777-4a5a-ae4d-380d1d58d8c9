import datetime
import os
import re
import shutil
import sys
import requests
import time

adpath = os.path.abspath(__file__) if '__file__' in globals() else os.getcwd()
sys.path.extend([os.path.abspath(os.path.join(adpath, *(['..'] * i))) for i in range(3)])

from utility import Logger
from db_solve.configs import paths

sys.stdout = Logger('G:\\logs\\csvdown.log')


def download(exchange, save_dir, file_path, ):
    _config = ENV_CONFIG[exchange]
    _headers = {
        'Content-Type': "application/json"
    }
    _data = {
        'serverId': _config['server_id'],
        'remotePath': file_path
    }
    _url = SERVER + TRANS_URL
    _resp = requests.post(url=_url, json=_data, headers=_headers)
    _data = _resp.json()
    if _data['code'] == '0':
        _file_path = _data['data']['filePath']
        _file_name = os.path.basename(_file_path)
        # 这里可能会报错
        _real_file_name = _file_name.split('_', 1)[1]
        _save_file_path = os.path.join(save_dir, _real_file_name)
        print('保存路径: ', _save_file_path)
        _download_url = SERVER + DOWNLOAD_URL + _file_name
        r = requests.get(url=_download_url, stream=True)
        with open(_save_file_path, 'wb') as f:
            for chunk in r.iter_content(chunk_size=CHUNK_SIZE):
                if chunk:
                    f.write(chunk)
        return 'OK'
    else:
        return _data['msg']


def download_csv(exchange, save_dir, _today, trans=False):
    _config = ENV_CONFIG[exchange]
    if _config is None:
        return None
    _remote_paths = _config['remote_paths']
    for _item in _remote_paths:
        _filename = str.replace(_item, '*', _today)
        print('处理文件:', _filename)
        # 顺序执行即可，异步执行意义不大
        try:
            download(exchange, save_dir, _filename)
            if trans:
                trans_parquet(paths.Paths(exchange).dirs, _filename.split('/')[-1])
        except Exception as e:
            print(e)
            print('无法下载文件' + _filename)


def download_zip(exchange, save_dir, _today, trans=False):
    _config = ENV_CONFIG[exchange]
    if _config is None:
        return None
    _remote_paths = _config['remote_paths']
    for _item in _remote_paths:
        _filename = str.replace(_item, '*', _today)
        print('处理文件:', _filename)
        download(exchange, save_dir, _filename)

        if trans:
            import zipfile
            zip_file = os.path.join(save_dir, _filename.split('/')[-1])
            try:
                if zipfile.is_zipfile(zip_file):
                    with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                        zip_ref.extractall(save_dir)
                        extract_dir = os.path.join(save_dir, zip_ref.namelist()[0].split('/')[0])
                        print(f"已解压 {zip_file} 到 {extract_dir}")
                        trans_parquet(extract_dir, extract_dir.split('/')[-1])
                    time.sleep(0.5)
                    os.remove(zip_file)
            except Exception as e:
                print(f"处理zip文件出错 {zip_file}: {e}")


def copy_trade(exchange, datetoday, trans=False):
    trade_dirs = f"/Sepro_{exchange}/Data/TradeRecords/"
    pattern = rf"{datetoday}"
    outfile = paths.Paths(exchange).str_trade % datetoday
    dir_path = os.path.splitext(outfile)[0] + '.csv'
    dirs = paths.gui_dirs[exchange] + trade_dirs
    for file in os.listdir(dirs):
        if re.search(pattern, file):
            shutil.copy(os.path.join(dirs, file), dir_path)
            print("已复制文件: " + dir_path)
            if trans:
                trans_parquet(os.path.dirname(dir_path), os.path.basename(dir_path))


if __name__ == '__main__':
    from db_solve.configs import paths
    from db_solve.configs.csv_server import SERVER, TRANS_URL, DOWNLOAD_URL, ENV_CONFIG, CHUNK_SIZE
    from db_solve.db_convert import trans_parquet, compress_csv, auto_git, copy_csv

    today = datetime.datetime.now().strftime('%Y%m%d')
    print("start download",datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'))
    today = '20250807'
    # clean_oldest_files()
    # for today in [f'{20250807+i}' for i in range(1, 30)]:
    try:
        for ex in ['SH500', 'SH300']:
            print(today)
            _save_dir = os.path.abspath(paths.Paths(ex).dirs)
            if not os.path.exists(_save_dir):
                os.mkdir(_save_dir)
            print(f"开始下载交易所数据: {ex}")
            download_csv(ex, _save_dir, today, trans=True)
            try:
                download_zip('VOLS_' + ex, _save_dir, today, trans=True)
            except Exception as e:
                print(e)
                print('无法下载VOLS数据' + ex)
            copy_trade(ex, today, trans=True)
            # split_zip(ex, today)
            # compress_csv(ex, today)
            copy_csv(ex, today)
            # decompress_csv(exchange,today)
    except Exception as e:
        print(e)
        print('下载失败')

    # trans_parquet(r'G:\\DATA\\SH500', 'TradeRecords20240826.csv')
    auto_git()
    print("all done", datetime.datetime.now().strftime('%Y%m%d %H:%M:%S'))
    sys.exit()
