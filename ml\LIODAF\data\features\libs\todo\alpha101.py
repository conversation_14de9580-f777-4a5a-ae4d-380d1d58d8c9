'''
Alpha101 因子
@author: lining
'''
import pandas as pd
import numpy as np
from factor_manager import factor_manager, Factor, FactorCategory


# 计算window窗口内的差: delta(x, d) — 计算当前值与d天前的差
def delta(df, column, window):
    return df[column].diff(periods=window)

# 计算window窗口内的差（适用于整个DataFrame）: delta2(df, d) — 计算当前值与d天前的差
def delta2(df, window):
    return df.diff(periods=window)

# 排名函数: rank(x) — 对x进行横截面排名，返回[0,1]之间的值
def rank(series):
    """对时间序列数据进行滚动排名，避免未来数据泄露"""
    #这样算太慢了，感觉rank用到的未来的信息很弱？
    #return series.expanding().apply(lambda x: x.rank(pct=True).iloc[-1])

    return series.rank(pct=True)

# 线性衰减移动平均: decay_linear(x, d) — 对x进行d天的线性衰减加权平均
def decay_linear(series, d):
    weights = np.linspace(d, 1, d)  # 权重是线性衰减
    weights /= np.sum(weights)  # 归一化，使得总和为1
    return series.rolling(window=d).apply(lambda x: np.dot(x, weights), raw=True)


# 符号函数: sign(x) — 返回x的符号，1表示正，-1表示负，0表示零
def sign(series):
    return np.sign(series)

# 计算时间序列相关性: correlation(x, y, d) — 计算x和y在d天窗口内的相关系数
def correlation(df, col1, col2, d):
    return df[col1].rolling(window=d).corr(df[col2])

# 计算时间序列相关性: correlation2(x, y, d) — 计算x和y在d天窗口内的相关系数
def correlation2(x, y, d):
    """计算x和y的d天时间序列相关性"""
    return x.rolling(window=d).corr(y)


# 计算求和: sum(x, d) — 计算过去d天的和
def mysum(series, d):
    return series.rolling(window=d).sum()

# 标准化: scale(x, a) — 将数据标准化为 abs(x)之和为a
def scale(series, a=1):
    return series / series.abs().sum() * a

# 计算时间序列的最小值: ts_min(x, d) — 计算过去d天的最小值
def ts_min(series, d):
    return series.rolling(window=d).min()

# 计算时间序列的最大值: ts_max(x, d) — 计算过去d天的最大值
def ts_max(x, d):
    """计算x的d天时间序列最大值"""
    return x.rolling(window=d).max()

# 计算时间序列的排名: ts_rank(x, d) — 计算x在过去d天中的排名
def ts_rank(x, d):
    """计算x的d天时间序列排名"""
    return x.rolling(window=d).apply(lambda x: (x.rank(pct=True).iloc[-1]), raw=False)

# 计算时间序列最大值的位置: ts_argmax(x, d) — 计算过去d天内x的最大值所在的位置
def ts_argmax(x, d):
    """计算x的d天时间序列最大值的位置"""
    return x.rolling(window=d).apply(lambda x: x.argmax(), raw=True)

# 计算时间序列最小值的位置: ts_argmin(x, d) — 计算过去d天内x的最小值所在的位置
def ts_argmin(x, d):
    """计算过去d天内x的最小值所在的位置（天数索引）"""
    return x.rolling(window=d).apply(lambda x: x.argmin(), raw=True)


# 计算延迟: delay(x, d) — 获取d天前的值
def delay(series, d):
    return series.shift(d)

# 计算带符号的幂: signedpower(x, a) — 计算x的a次方，保留符号
def signedpower(x, a):
    """计算x的a次方"""
    return x ** a

# 计算标准差: stddev(x, d) — 计算x的d天滚动标准差
def stddev(x, d):
    """计算x的d天滚动标准差"""
    return x.rolling(window=d).std()


# 计算成交量加权平均价格: vwap(df) — 计算成交量加权平均价格
def vwap(df):
    """计算VWAP"""
    volume_diff = df['Volume'].diff()  # 成交量变化
    vwap = (df['LastPrice'] * volume_diff).cumsum() / volume_diff.cumsum()
    return vwap

# Alpha101 因子注册

# Alpha#18 变种
def feature_rank_rolling_std_1(df, window1=5, window2=10):
    a = df['LastPrice']
    b = df['BidPrice1']
    p = a - b
    factor = (-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha18_variant_1",
    category=FactorCategory.ALPHA101,
    description="Alpha#18变种1：基于LastPrice和BidPrice1的排名滚动标准差",
    calculation=feature_rank_rolling_std_1,
    dependencies=["LastPrice", "BidPrice1"],
    parameters={"window1": 5, "window2": 10},
    source="alpha101"
))

def feature_rank_rolling_std_2(df, window1=5, window2=10):
    a = df['LastPrice']
    b = df['BidVol1']
    p = a - b
    factor = (-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha18_variant_2",
    category=FactorCategory.ALPHA101,
    description="Alpha#18变种2：基于LastPrice和BidVol1的排名滚动标准差",
    calculation=feature_rank_rolling_std_2,
    dependencies=["LastPrice", "BidVol1"],
    parameters={"window1": 5, "window2": 10},
    source="alpha101"
))

def feature_rank_rolling_std_3(df, window1=5, window2=10):
    a = df['Volume'].diff()
    b = df['AskVol1']
    p = a - b
    factor = (-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha18_variant_3",
    category=FactorCategory.ALPHA101,
    description="Alpha#18变种3：基于Volume差分和AskVol1的排名滚动标准差",
    calculation=feature_rank_rolling_std_3,
    dependencies=["Volume", "AskVol1"],
    parameters={"window1": 5, "window2": 10},
    source="alpha101"
))

def feature_rank_rolling_std_4(df, window1=5, window2=10):
    a = df['BidVol1']
    b = df['AskVol1']
    p = a - b
    factor = (-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha18_variant_4",
    category=FactorCategory.ALPHA101,
    description="Alpha#18变种4：基于BidVol1和AskVol1的排名滚动标准差",
    calculation=feature_rank_rolling_std_4,
    dependencies=["BidVol1", "AskVol1"],
    parameters={"window1": 5, "window2": 10},
    source="alpha101"
))

# Alpha#31
def alpha_31(df):
    delta_close_10 = delta(df, 'BidPrice1', 10)
    delta_close_3 = delta(df, 'BidPrice1', 3)
    scaled_corr = scale(correlation(df, 'LastPrice', 'BidPrice1', 12))
    factor = (rank(rank(rank(-1 * rank(delta_close_10)))) + rank(-delta_close_3)) + sign(scaled_corr)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha31",
    category=FactorCategory.ALPHA101,
    description="Alpha#31：基于BidPrice1的delta和LastPrice与BidPrice1的相关性",
    calculation=alpha_31,
    dependencies=["LastPrice", "BidPrice1"],
    source="alpha101"
))

# Alpha#32 变种
def alpha_32_1(df, window1=7, window2=230):
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale(df['BidPrice1'].rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha32_variant_1",
    category=FactorCategory.ALPHA101,
    description="Alpha#32变种1：基于LastPrice和BidPrice1的相关性",
    calculation=alpha_32_1,
    dependencies=["LastPrice", "BidPrice1"],
    parameters={"window1": 7, "window2": 230},
    source="alpha101"
))

def alpha_32_2(df, window1=7, window2=230):
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale((df['Volume'].diff()).rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha32_variant_2",
    category=FactorCategory.ALPHA101,
    description="Alpha#32变种2：基于LastPrice和Volume差分的相关性",
    calculation=alpha_32_2,
    dependencies=["LastPrice", "Volume"],
    parameters={"window1": 7, "window2": 230},
    source="alpha101"
))

def alpha_32_3(df, window1=7, window2=230):
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale(df['BidVol1'].rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha32_variant_3",
    category=FactorCategory.ALPHA101,
    description="Alpha#32变种3：基于LastPrice和BidVol1的相关性",
    calculation=alpha_32_3,
    dependencies=["LastPrice", "BidVol1"],
    parameters={"window1": 7, "window2": 230},
    source="alpha101"
))

def alpha_32_4(df, window1=7, window2=230):
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale(df['AskVol1'].rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha32_variant_4",
    category=FactorCategory.ALPHA101,
    description="Alpha#32变种4：基于LastPrice和AskVol1的相关性",
    calculation=alpha_32_4,
    dependencies=["LastPrice", "AskVol1"],
    parameters={"window1": 7, "window2": 230},
    source="alpha101"
))

# Alpha#34 变种
def alpha_34_1(df, window1=2, window2=5):
    returns = df['LastPrice'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['LastPrice'], window=1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha34_variant_1",
    category=FactorCategory.ALPHA101,
    description="Alpha#34变种1：基于LastPrice收益率的标准差比率",
    calculation=alpha_34_1,
    dependencies=["LastPrice"],
    parameters={"window1": 2, "window2": 5},
    source="alpha101"
))

def alpha_34_2(df, window1=2, window2=5):
    returns = ((df['BidVol1'] - df['AskVol1']) / (df['BidVol1'] + df['AskVol1'])).pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(((df['BidVol1'] - df['AskVol1']) / (df['BidVol1'] + df['AskVol1'])), 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha34_variant_2",
    category=FactorCategory.ALPHA101,
    description="Alpha#34变种2：基于买卖不平衡度的标准差比率",
    calculation=alpha_34_2,
    dependencies=["BidVol1", "AskVol1"],
    parameters={"window1": 2, "window2": 5},
    source="alpha101"
))

def alpha_34_3(df, window1=2, window2=5):
    returns = df['BidPrice1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['BidPrice1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha34_variant_3",
    category=FactorCategory.ALPHA101,
    description="Alpha#34变种3：基于BidPrice1收益率的标准差比率",
    calculation=alpha_34_3,
    dependencies=["BidPrice1"],
    parameters={"window1": 2, "window2": 5},
    source="alpha101"
))

def alpha_34_4(df, window1=2, window2=5):
    returns = df['AskPrice1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['AskPrice1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha34_variant_4",
    category=FactorCategory.ALPHA101,
    description="Alpha#34变种4：基于AskPrice1收益率的标准差比率",
    calculation=alpha_34_4,
    dependencies=["AskPrice1"],
    parameters={"window1": 2, "window2": 5},
    source="alpha101"
))

def alpha_34_5(df, window1=2, window2=5):
    returns = df['BidVol1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['BidVol1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha34_variant_5",
    category=FactorCategory.ALPHA101,
    description="Alpha#34变种5：基于BidVol1收益率的标准差比率",
    calculation=alpha_34_5,
    dependencies=["BidVol1"],
    parameters={"window1": 2, "window2": 5},
    source="alpha101"
))

def alpha_34_6(df, window1=2, window2=5):
    returns = df['AskVol1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['AskVol1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha34_variant_6",
    category=FactorCategory.ALPHA101,
    description="Alpha#34变种6：基于AskVol1收益率的标准差比率",
    calculation=alpha_34_6,
    dependencies=["AskVol1"],
    parameters={"window1": 2, "window2": 5},
    source="alpha101"
))

# Alpha#35
def alpha_35(df, window1=32, window2=16):
    volume_ts_rank = ts_rank(df['Volume'].diff(), window1)
    close_high_low = (df['LastPrice'] + df['High']) - df['Low']
    close_high_low_ts_rank = 1 - ts_rank(close_high_low, window2)
    returns = df['LastPrice'].pct_change()
    returns_ts_rank = 1 - ts_rank(returns, window2)
    factor = volume_ts_rank * close_high_low_ts_rank * returns_ts_rank
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha35",
    category=FactorCategory.ALPHA101,
    description="Alpha#35：基于成交量、价格和收益率的时间序列排名",
    calculation=alpha_35,
    dependencies=["Volume", "LastPrice", "High", "Low"],
    parameters={"window1": 32, "window2": 16},
    source="alpha101"
))

# Alpha#37
def alpha_37(df, window1=1, window2=200):
    delay_open_close_1 = delay(df['LastPrice'].shift(1) - df['LastPrice'], window1)
    corr_delay_open_close_close = correlation2(delay_open_close_1, df['LastPrice'], window2)
    part1 = rank(corr_delay_open_close_close)
    open_close = df['LastPrice'].shift(1) - df['LastPrice']
    part2 = rank(open_close)
    factor = part1 + part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha37",
    category=FactorCategory.ALPHA101,
    description="Alpha#37：基于价格变化和相关性的排名",
    calculation=alpha_37,
    dependencies=["LastPrice"],
    parameters={"window1": 1, "window2": 200},
    source="alpha101"
))

# Alpha#38
def alpha_38(df, window=10):
    ts_rank_close_10 = ts_rank(df['LastPrice'], window)
    part1 = -1 * rank(ts_rank_close_10)
    close_open_ratio = (df['LastPrice'].shift(1))/df['LastPrice']
    part2 = rank(close_open_ratio)
    factor = part1 * part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha38",
    category=FactorCategory.ALPHA101,
    description="Alpha#38：基于价格时间序列排名和价格比率",
    calculation=alpha_38,
    dependencies=["LastPrice"],
    parameters={"window": 10},
    source="alpha101"
))

# Alpha#39
def alpha_39(df):
    delta_close_7 = delta2(df['LastPrice'], 7)
    volume_adv20_ratio = (df['Volume'].diff()) / (df['Volume'].diff()).rolling(window=20).mean()
    decay_volume_adv20 = decay_linear(volume_adv20_ratio, 9)
    part1 = -1 * rank(delta_close_7 * (1 - rank(decay_volume_adv20)))
    sum_returns_250 = mysum(df['LastPrice'].pct_change(), 250)
    part2 = 1 + rank(sum_returns_250)
    factor = part1 * part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha39",
    category=FactorCategory.ALPHA101,
    description="Alpha#39：基于价格变化、成交量和长期收益率",
    calculation=alpha_39,
    dependencies=["LastPrice", "Volume"],
    source="alpha101"
))

# Alpha#41
def alpha_41(df):
    high_low_sqrt = signedpower(df['High'] * df['Low'], 0.5)
    factor = high_low_sqrt - vwap(df)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha41",
    category=FactorCategory.ALPHA101,
    description="Alpha#41：基于最高价、最低价和VWAP的差异",
    calculation=alpha_41,
    dependencies=["High", "Low", "LastPrice", "Volume"],
    source="alpha101"
))

# Alpha#42
def alpha_42(df):
    vwap_close_diff = vwap(df) - df['LastPrice']
    vwap_close_sum = vwap(df) + df['LastPrice']
    factor = rank(vwap_close_diff) / rank(vwap_close_sum)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha42",
    category=FactorCategory.ALPHA101,
    description="Alpha#42：基于VWAP和收盘价的比率",
    calculation=alpha_42,
    dependencies=["LastPrice", "Volume"],
    source="alpha101"
))

# Alpha#43
def alpha_43(df):
    volume_adv20_ratio = (df['Volume'].diff())/ (df['Volume'].diff()).rolling(window=20).mean()
    ts_rank_volume_adv20 = ts_rank(volume_adv20_ratio, 20)
    delta_close_7 = delta2(df['LastPrice'], 7)
    ts_rank_delta_close_7 = ts_rank(-1 * delta_close_7, 8)
    factor = ts_rank_volume_adv20 * ts_rank_delta_close_7
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha43",
    category=FactorCategory.ALPHA101,
    description="Alpha#43：基于成交量比率和价格变化的时间序列排名",
    calculation=alpha_43,
    dependencies=["LastPrice", "Volume"],
    source="alpha101"
))

# Alpha#44
def alpha_44(df):
    rank_volume = rank(df['Volume'].diff())
    corr_high_rank_volume = correlation2(df['High'], rank_volume, 5)
    factor = -1 * corr_high_rank_volume
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha44",
    category=FactorCategory.ALPHA101,
    description="Alpha#44：基于最高价和成交量排名的负相关性",
    calculation=alpha_44,
    dependencies=["High", "Volume"],
    source="alpha101"
))

# Alpha#45
def alpha_45(df):
    delay_close_5 = delay(df['LastPrice'], 5)
    sum_delay_close_20 = mysum(delay_close_5, 20)
    rank_sum_delay_close = rank(sum_delay_close_20 / 20)
    corr_close_volume = correlation2(df['LastPrice'], df['Volume'].diff(), 2)
    sum_close_5 = mysum(df['LastPrice'], 5)
    sum_close_20 = mysum(df['LastPrice'], 20)
    corr_sum_close_5_sum_close_20 = correlation2(sum_close_5, sum_close_20, 2)
    rank_corr_sum_close = rank(corr_sum_close_5_sum_close_20)
    factor = -1 * (rank_sum_delay_close * corr_close_volume * rank_corr_sum_close)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha45",
    category=FactorCategory.ALPHA101,
    description="Alpha#45：基于价格延迟、价格与成交量相关性和价格和的相关性",
    calculation=alpha_45,
    dependencies=["LastPrice", "Volume"],
    source="alpha101"
))

# Alpha#46
def alpha_46(df):
    delay_close_20 = delay(df['LastPrice'], 20)
    delay_close_10 = delay(df['LastPrice'], 10)
    part1 = ((delay_close_20 - delay_close_10) / 10) - ((delay_close_10 - df['LastPrice']) / 10)
    condition1 = (part1 > 0.25)
    condition2 = (part1 < 0)
    factor = np.where(condition1, -1, np.where(condition2, 1, (-1) * (df['LastPrice'] - delay(df['LastPrice'], 1))))
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha46",
    category=FactorCategory.ALPHA101,
    description="Alpha#46：基于价格变化率的条件因子",
    calculation=alpha_46,
    dependencies=["LastPrice"],
    source="alpha101"
))

# Alpha#47
def alpha_47(df):
    rank_1_close = rank(1 / df['LastPrice'])
    part1 = (rank_1_close * df['Volume'].diff()) /(df['Volume'].diff()).rolling(window=20).mean()
    high_rank = rank(df['High'] - df['LastPrice'])
    part2 = (df['High'] * high_rank) / (mysum(df['High'], 5) / 5)
    part3 = rank(vwap(df) - delay(vwap(df), 5))
    factor = (part1 * part2) - part3
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha47",
    category=FactorCategory.ALPHA101,
    description="Alpha#47：基于价格倒数、成交量、最高价和VWAP的复合因子",
    calculation=alpha_47,
    dependencies=["LastPrice", "High", "Volume"],
    source="alpha101"
))

# Alpha#49
def alpha_49(df):
    delay_close_20 = delay(df['LastPrice'], 20)
    delay_close_10 = delay(df['LastPrice'], 10)
    part1 = ((delay_close_20 - delay_close_10) / 10) - ((delay_close_10 - df['LastPrice']) / 10)
    condition = (part1 < -0.1)
    factor = np.where(condition, 1, (-1) * (df['LastPrice'] - delay(df['LastPrice'], 1)))
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha49",
    category=FactorCategory.ALPHA101,
    description="Alpha#49：基于价格变化率的条件因子",
    calculation=alpha_49,
    dependencies=["LastPrice"],
    source="alpha101"
))

# Alpha#51
def alpha_51(df):
    delay_close_20 = delay(df['LastPrice'], 20)
    delay_close_10 = delay(df['LastPrice'], 10)
    part1 = ((delay_close_20 - delay_close_10) / 10) - ((delay_close_10 - df['LastPrice']) / 10)
    condition = (part1 < -0.05)
    factor = np.where(condition, 1, (-1) * (df['LastPrice'] - delay(df['LastPrice'], 1)))
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha51",
    category=FactorCategory.ALPHA101,
    description="Alpha#51：基于价格变化率的条件因子",
    calculation=alpha_51,
    dependencies=["LastPrice"],
    source="alpha101"
))

# Alpha#52
def alpha_52(df):
    ts_min_low_5 = ts_min(df['Low'], 5)
    part1 = (-1 * ts_min_low_5) + delay(ts_min_low_5, 5)
    returns = df['LastPrice'].pct_change()
    part2 = rank((mysum(returns, 240) - mysum(returns, 20)) / 220)
    part3 = ts_rank(df['Volume'].diff(), 5)
    factor = (part1 * part2) * part3
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha52",
    category=FactorCategory.ALPHA101,
    description="Alpha#52：基于最低价、收益率和成交量的复合因子",
    calculation=alpha_52,
    dependencies=["LastPrice", "Low", "Volume"],
    source="alpha101"
))

# Alpha#53
def alpha_53(df):
    part1 = ((df['LastPrice'] - df['Low']) - (df['High'] - df['LastPrice'])) / (df['LastPrice'] - df['Low'])
    factor = -1 * delta2(part1, 9)
    return np.nan_to_num(factor, posinf=0, neginf=0)

factor_manager.register_factor(Factor(
    name="alpha53",
    category=FactorCategory.ALPHA101,
    description="Alpha#53：基于价格位置的变化",
    calculation=alpha_53,
    dependencies=["LastPrice", "High", "Low"],
    source="alpha101"
))

# Alpha#55
def alpha_55(df):
    ts_min_low_12 = ts_min(df['Low'], 12)
    ts_max_high_12 = ts_max(df['High'], 12)
    part1 = (df['LastPrice'] - ts_min_low_12) / (ts_max_high_12 - ts_min_low_12)
    part2 = rank(part1)
    part3 = rank(df['Volume'].diff())
    factor = -1 * correlation2(part2, part3, 6)
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#56
def alpha_56(df, cap=None): #IC -0.05
    if cap is None:
        cap=1
    returns = df['LastPrice'].pct_change()
    part1 = rank(mysum(returns, 10) / mysum(mysum(returns, 2), 3))
    part2 = rank(returns * cap)
    factor = 0 - (1 * (part1 * part2))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#57
def alpha_57(df): #IC 0.02
    part1 = (df['LastPrice'] - vwap(df))
    part2 = decay_linear(rank(ts_argmax(df['LastPrice'], 30)), 2)
    factor = 0 - (1 * (part1 / part2))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#60
def alpha_60(df): #IC 0.04
    part1 = ((df['LastPrice'] - df['Low']) - (df['High'] - df['LastPrice'])) / (df['High'] - df['Low']) *(df['Volume'].diff())
    part1_scaled = scale(rank(part1))
    part2 = scale(rank(ts_argmax(df['LastPrice'], 10)))
    factor = 0 - (1 * ((2 * part1_scaled) - part2))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#61 不行
# def alpha_61(df):
#     """Alpha#61: 基于VWAP和ADV180的因子"""
#     vwap_val = vwap(df)
#     adv180_val = (df['Volume'].diff()).rolling(window=180).mean()
#     part1 = rank(vwap_val - ts_min(vwap_val, 16))
#     part2 = rank(correlation2(vwap_val, adv180_val, 18))
#     return part1 < part2

# Alpha#62
def alpha_62(df): # IC -0.01
    """Alpha#62: 基于VWAP、ADV20、开盘价、最高价和最低价的因子"""
    vwap_val = vwap(df)
    adv20_val = (df['Volume'].diff()).rolling(window=20).mean()
    part1 = rank(correlation2(vwap_val, mysum(adv20_val, 22), 10))
    part2 = (rank(df['LastPrice'].shift(1)) + rank(df['LastPrice'].shift(1))) < (rank((df['High'] + df['Low']) / 2) + rank(df['High']))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#63不行

# Alpha#64  不行
# def alpha_64(df):
#     """Alpha#64: 基于开盘价、最低价、ADV120、最高价和VWAP的因子"""
#     vwap_val = vwap(df)
#     adv120_val = (df['Volume'].diff()).rolling(window=120).mean()
#     part1 = rank(correlation2(mysum(df['LastPrice'].shift(1) * 0.178404 + df['Low'] * (1 - 0.178404), 13), mysum(adv120_val, 13), 17))
#     part2 = rank(delta2((df['High'] + df['Low']) / 2 * 0.178404 + vwap_val * (1 - 0.178404), 4))
#     return (part1 < part2) * -1

# Alpha#65
def alpha_65(df): #IC -0.03
    """Alpha#65: 基于开盘价、VWAP和ADV60的因子"""
    vwap_val = vwap(df)
    adv60_val =(df['Volume'].diff()).rolling(window=60).mean()
    part1 = rank(correlation2(df['LastPrice'].shift(1) * 0.00817205 + vwap_val * (1 - 0.00817205), mysum(adv60_val, 9), 6))
    part2 = rank(df['LastPrice'].shift(1) - ts_min(df['LastPrice'].shift(1), 14))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#66
def alpha_66(df):  #IC 0.01
    """Alpha#66: 基于VWAP、最低价、开盘价和最高价的因子"""
    vwap_val = vwap(df)
    part1 = rank(decay_linear(delta2(vwap_val, 4), 7))
    part2 = ts_rank(decay_linear((df['Low'] * 0.96633 + df['Low'] * (1 - 0.96633) - vwap_val) / \
                                 (df['LastPrice'].shift(1) - (df['High'] + df['Low']) / 2), 11), 7)
    factor= (part1 + part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#67 不行


# Alpha#68
def alpha_68(df): #IC 0.02
    """Alpha#68: 基于最高价、ADV15、收盘价和最低价的因子"""
    adv15_val = (df['Volume'].diff()).rolling(window=15).mean()
    part1 = ts_rank(correlation2(rank(df['High']), rank(adv15_val), 9), 14)
    part2 = rank(delta2(df['LastPrice'] * 0.518371 + df['Low'] * (1 - 0.518371), 1))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#69 不行

# Alpha#70 不行

# Alpha#71 不行
# def alpha_71(df): #IC -0.00
#     """Alpha#71: 基于收盘价、ADV180、最低价、开盘价和VWAP的因子"""
#     ts_rank_close = ts_rank(df['LastPrice'], 3)
#     ts_rank_adv180 = ts_rank((df['Volume'].diff()).rolling(window=180).mean(), 12)
#     part1 = ts_rank(decay_linear(correlation2(ts_rank_close, ts_rank_adv180, 18), 4), 16)
#
#     part2 = ts_rank(decay_linear((rank((df['Low'] + df['LastPrice'].shift(1)) - (vwap(df) + vwap(df))) ** 2), 16), 4)
#
#     factor= np.maximum(part1, part2)
#     return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#72
def alpha_72(df): #IC 0.02
    """Alpha#72: 基于最高价、最低价、ADV40、VWAP和成交量的因子"""
    part1 = rank(decay_linear(
        correlation2((df['High'] + df['Low']) / 2,(df['Volume'].diff()).rolling(window=40).mean(), 9), 10))
    part2 = rank(decay_linear(correlation2(ts_rank(vwap(df), 4), ts_rank(df['Volume'], 19), 7), 3))
    factor= part1 / part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#73
def alpha_73(df): #IC 0.02
    """Alpha#73: 基于VWAP、开盘价和最低价的因子"""
    part1 = rank(decay_linear(delta2(vwap(df), 5), 3))

    weighted_price = (df['Volume'].shift(1))* 0.147155 + df['Low'] * (1 - 0.147155)
    part2 = ts_rank(decay_linear((delta2(weighted_price, 2) / weighted_price) * -1, 3), 17)

    factor= np.maximum(part1, part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#74 不行
# def alpha_74(df):
#     """Alpha#74: 基于收盘价、ADV30、最高价、VWAP和成交量的因子"""
#     part1 = rank(correlation2(df['LastPrice'], mysum((df['Volume'].diff()).rolling(window=30).mean(), 37), 15))
#     part2 = rank(correlation2(rank(df['High'] * 0.0261661 + vwap(df) * (1 - 0.0261661)), rank(df['Volume']), 11))
#
#     return (part1 < part2) * -1

# Alpha#75
def alpha_75(df): #IC 0.01
    """Alpha#75: 基于VWAP、成交量、最低价和ADV50的因子"""
    part1 = rank(correlation2(vwap(df), df['Volume'], 4))
    part2 = rank(correlation2(rank(df['Low']), rank((df['Volume'].diff()).rolling(window=50).mean()), 12))

    factor= part1 < part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

# 76不行

# Alpha#77  不行
def alpha_77(df): #IC 0.01
    """Alpha#77: 基于最高价、最低价、VWAP和ADV40的因子"""
    part1 = rank(decay_linear((((df['High'] + df['Low']) / 2) + df['High']) - (vwap(df) + df['High']), 20))
    part2 = rank(decay_linear(
        correlation2((df['High'] + df['Low']) / 2,(df['Volume'].diff()).rolling(window=40).mean(), 3), 6))

    factor= np.minimum(part1, part2)
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#78
def alpha_78(df): #IC -0.01
    """Alpha#78: 基于最低价、VWAP、ADV40和成交量的因子"""
    weighted_price = df['Low'] * 0.352233 + vwap(df) * (1 - 0.352233)
    part1 = rank(
        correlation2(mysum(weighted_price, 20), mysum((df['Volume'].diff()).rolling(window=40).mean(), 20), 7))
    part2 = rank(correlation2(rank(vwap(df)), rank(df['Volume']), 6))

    factor= part1 ** part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#79不行

# Alpha#80不行

#Alpha#81 不行

#Alpha#82 不行

#Alpha#83 不行
# def alpha_83(df):
#     """Alpha#83: 基于最高价、最低价、收盘价、成交量和VWAP的因子"""
#     part1 = rank(delay((df['High'] - df['Low']) / (mysum(df['LastPrice'], 5) / 5), 2))
#     part2 = rank(rank(df['Volume']))
#     part3 = ((df['High'] - df['Low']) / (mysum(df['LastPrice'], 5) / 5)) / (vwap(df) - df['LastPrice'])
#     return (part1 * part2) / part3

#Alpha#84 不行
# def alpha_84(df):
#     """Alpha#84: 基于VWAP和收盘价的因子"""
#     part1 = ts_rank(vwap(df) - ts_max(vwap(df), 15), 21)
#     part2 = delta2(df['LastPrice'], 5)
#     return signedpower(part1, part2)

#Alpha#85 不行
# def alpha_85(df): #IC -0.00
#     """Alpha#85: 基于最高价、收盘价、ADV30和成交量的因子"""
#     part1 = rank(correlation2(df['High'] * 0.876703 + df['LastPrice']* (1 - 0.876703),(df['Volume'].diff()).rolling(window=30).mean(), 10))
#     part2 = rank(correlation2(ts_rank((df['High'] + df['Low']) / 2, 4), ts_rank(df['Volume'], 10), 7))
#     factor= part1 ** part2
#     return np.nan_to_num(factor, posinf=0, neginf=0)


#Alpha#86
def alpha_86(df): #IC 0.02
    """Alpha#86: 基于收盘价、ADV20、开盘价和VWAP的因子"""
    part1 = ts_rank(correlation2(df['LastPrice'], mysum((df['Volume'].diff()).rolling(window=20).mean(), 15), 6), 20)
    part2 = rank((df['LastPrice'].shift(1) + df['LastPrice']) - (vwap(df) + df['LastPrice'].shift(1)))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#87 不行

#Alpha#88
def alpha_88(df): #IC -0.02
    """Alpha#88: 基于开盘价、最低价、最高价、收盘价和ADV60的因子"""
    part1 = rank(decay_linear((df['LastPrice'].shift(1) + rank(df['Low'])) - (rank(df['High']) + rank(df['LastPrice'])), 8))
    part2 = ts_rank(decay_linear(correlation2(ts_rank(df['LastPrice'], 8), ts_rank((df['Volume'].diff()).rolling(window=60).mean(), 21), 8), 7), 3)
    factor= np.minimum(part1, part2)
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#89 不行

#Alpha#90 不行

#Alpha#91 不行

#Alpha#92
def alpha_92(df): #IC 0.03
    """Alpha#92: 基于最高价、最低价、收盘价、开盘价和ADV30的因子"""
    part1 = ts_rank(decay_linear((((df['High'] + df['Low']) / 2) + df['LastPrice']) < (df['Low'] + df['LastPrice'].shift(1)), 15), 19)
    part2 = ts_rank(decay_linear(correlation2(rank(df['Low']), rank((df['Volume'].diff()).rolling(window=30).mean()), 8), 7), 7)
    factor= np.minimum(part1, part2)
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#93 不行

#Alpha#94 不行
# def alpha_94(df): #IC 0.00
#     """Alpha#94: 基于VWAP、ADV60的因子"""
#     part1 = rank(vwap(df) - ts_min(vwap(df), 12))
#     part2 = ts_rank(correlation2(ts_rank(vwap(df), 20), ts_rank((df['Volume'].diff()).rolling(window=60).mean(), 4), 18), 3)
#     factor= (part1 ** part2) * -1
#     return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#95 不行
# def alpha_95(df): #IC 0.00
#     """Alpha#95: 基于开盘价、最高价、最低价、ADV40的因子"""
#     part1 = rank(df['LastPrice'].shift(1) - ts_min(df['LastPrice'].shift(1), 12))
#     part2 = ts_rank((rank(correlation2(mysum((df['High'] + df['Low']) / 2, 19), mysum((df['Volume'].diff()).rolling(window=40).mean(), 19), 13)) ** 5), 12)
#     return part1 < part2

#Alpha#96
def alpha_96(df): #IC -0.02
    """Alpha#96: 基于VWAP、成交量、收盘价、ADV60的因子"""
    part1 = ts_rank(decay_linear(correlation2(rank(vwap(df)), rank(df['Volume']), 4), 4), 8)
    part2 = ts_rank(decay_linear(ts_argmax(correlation2(ts_rank(df['LastPrice'], 7), ts_rank((df['Volume'].diff()).rolling(window=60).mean(), 4), 4), 13), 14), 13)
    factor= np.maximum(part1, part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#97 不行

#Alpha#98
def alpha_98(df): #IC -0.01
    """Alpha#98: 基于VWAP、ADV5、开盘价、ADV15的因子"""
    part1 = rank(decay_linear(correlation2(vwap(df), mysum((df['Volume'].diff()).rolling(window=5).mean(), 26), 5), 7))
    part2 = rank(decay_linear(ts_rank(ts_argmin(correlation2(rank(df['LastPrice'].shift(1)), rank((df['Volume'].diff()).rolling(window=15).mean()), 21), 9), 7), 8))
    factor= part1 - part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#99
def alpha_99(df): #IC -0.01
    """Alpha#99: 基于最高价、最低价、ADV60、成交量的因子"""
    part1 = rank(correlation2(mysum((df['High'] + df['Low']) / 2, 20), mysum((df['Volume'].diff()).rolling(window=60).mean(), 20), 9))
    part2 = rank(correlation2(df['Low'], df['Volume'], 6))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#100 不行

#Alpha#101
def alpha_101(df): #IC 0.05
    """Alpha#101: 基于收盘价、开盘价、最高价、最低价的因子"""
    factor= (df['LastPrice'] - df['LastPrice'].shift(1)) / ((df['High'] - df['Low']) + 0.001)
    return np.nan_to_num(factor, posinf=0, neginf=0)


