import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import Qt.labs.platform as Platform

Item {
    id: root
    height: 40
    
    signal fileSelected(string filePath)
    signal liveModeToggled()
    signal customRChanged(real value)
    
    RowLayout {
        anchors.fill: parent
        anchors.margins: 5
        spacing: 10
        
        Button {
            text: "选择文件"
            onClicked: fileDialog.open()
        }
        
        ComboBox {
            id: monthCombo
            Layout.preferredWidth: 200
            model: ["所有月份", "当月", "次月", "季月", "次季月"]
        }
        
        Label {
            text: "r:"
        }
        
        TextField {
            Layout.preferredWidth: 50
            text: "0"
            validator: DoubleValidator {}
            onTextChanged: if (acceptableInput) root.customRChanged(Number(text))
        }
        
        CheckBox {
            text: "newIV"
            onCheckedChanged: console.log("New IV:", checked)
        }
        
        Button {
            text: root.parent.parent.parent.liveMode ? "关闭实时" : "实时模式"
            onClicked: root.liveModeToggled()
        }
        
        Item {
            Layout.fillWidth: true  // 弹性空间
        }
    }
    
    Platform.FileDialog {
        id: fileDialog
        title: "选择数据文件"
        nameFilters: ["Parquet Files (*.parquet)", "All files (*)"]
        onAccepted: root.fileSelected(file)
    }
}