# -*- coding: utf-8 -*-
 
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta
 
from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData
 
from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle
import random
import re
 
class ArbiStrategyV3(StrategyTemplate):
    """"""
    author = "vnpy"
 
    buy_price = 0
    short_price = 0
 
    parameters = [
        'lots',
        'maxPos',
        'sizes',
        'maker',
        'refer',
        'priceticks',
        'path',
        'min_std',
        'open_std',
        'close_std',
        'grid_start',
        'grid_interval',
        'grid_layer'
    ]
    variables = [
        'buy_price',
        'short_price'
    ]
 
    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)
 
        self.buy_vt_orderids = None
        self.short_vt_orderids = None
        self.buy_hedge_orderids = None
        self.short_hedge_orderids = None
        self.buy_refer_orderids = None
        self.short_refer_orderids = None
        
    def getFairPrice(self,lastP,askP,bidP,edgeP):
        fair=lastP 
        if askP>0 and bidP>0:
            if  askP-bidP<=edgeP: # 有流动性，中间价
                fair=0.5*(askP+bidP)
            else:   # 流动性不足, 不变 
                if lastP>askP:
                    fair=askP
                if lastP<bidP:
                    fair=bidP
        return fair
 
    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        self.avg = 0
        self.net=0
        self.rCount=0
        self.mCount = 0
        self.pauseCount=0
        self.isQuoting=False
        vt_symbol = self.sp_contract()
        self.lastTicks={self.maker:0,self.refer:0,vt_symbol:0}
        self.comments  = None
        self.ewma = 0
        self.Basis = 0
        self.isQuoting = True
        self.offer_list = []
        self.offer_list_head = []
        self.basisCount = 0
        self.basislist = np.zeros(99999999)
        self.maker_buy_price = 0
        self.maker_short_price = 0
        self.last_net = 0
        self.trade_price = 0
        self.last_tick = None
        self.offer_list = []
        self.offer_list_head = []
 
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")
 
    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")
 
    # def on_ticks(self, ticks: Dict[str, TickData]):
    #     """
    #     Callback of new tick data update.
    #     """
    #     #对比tick
    #     vt_symbol = self.sp_contract()
    #     if vt_symbol in ticks and ticks[vt_symbol]!=self.lastTicks[vt_symbol]:
            
            
    #         self.on_tick_maker(ticks[vt_symbol])
    #         self.lastTicks[vt_symbol] = ticks[vt_symbol]
    #     #决定on_tick
    #     #储存之前的tick
        
        
    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        vt_symbol = self.sp_contract()
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.refer:
                    self.on_tick_refer(ticks[key])
                elif key==vt_symbol:
                    self.on_tick_maker(ticks[key])
                self.lastTicks[key] = ticks[key]
        
    def sp_contract(self):
        exchange = self.maker.split('.')[-1]
        maker = self.maker.split('.')[0]
        refer = self.refer.split('.')[0]
        maker_date = int(re.findall(r'\d+',maker)[0])
        refer_date = int(re.findall(r'\d+',refer)[0])
        if maker_date < refer_date:
            self.sp_order = 1
        else:
            self.sp_order = -1
        if exchange == 'CZCE':
            if self.sp_order == 1:
                sp = f"SPD-{maker}%2F{refer}"
            else:
                sp = f"SPD-{refer}%2F{maker}"
        if exchange == 'DCE':
            if self.sp_order == 1:
                sp = f"SP+{maker}%26{refer}"
            else:
                sp = f"SP+{refer}%26{maker}"
        if exchange in ['SHFE','INE']:
            return None
        else:
            return f"{sp}.{exchange}"
 
    def on_tick_refer(self,tick):
        
        if self.mCount >0:
            self.basislist[self.rCount] = self.fair_price
            self.rCount += 1 
        return
    
    # def on_maker_time(self,tick):
    #     dt = tick.datetime
    #     if (dt.time() > dtime(22,54) and dt.time() < dtime(22, 55)) or (dt.time() > dtime(14,54) and dt.time() < dtime(14, 55)):
    #         if self.net > 0:
    #             self.short(self.maker,tick.bid_price_1,abs(self.net),'end')
    #         if self.net < 0:
    #             self.buy(self.maker,tick.ask_price_1,abs(self.net),'end')
    
    def process_tick(self,tick):
        ts = self.priceticks[self.refer]
        if not tick.bid_price_2:
            tick.bid_price_2 = tick.bid_price_1 - ts
            tick.bid_price_3 = tick.bid_price_1 - 2 * ts
            tick.bid_price_4 = tick.bid_price_1 - 3 * ts
            tick.bid_price_5 = tick.bid_price_1 - 4 * ts
            tick.ask_price_2 = tick.ask_price_1 + ts
            tick.ask_price_3 = tick.ask_price_1 + 2 * ts
            tick.ask_price_4 = tick.ask_price_1 + 3 * ts
            tick.ask_price_5 = tick.ask_price_1 + 4 * ts
        return tick
            
 
    def on_tick_maker(self, tick):
        # print (tick.bid_price_1)
        tick = self.process_tick(tick) # 只有买一卖一的数据
        last_tick = self.last_tick
        self.mCount += 1
        ts = self.priceticks[self.refer]
        net = self.net 
        lots = self.lots
        path = self.path
        self.fair_price = tick.fair_price
        # self.basislist[self.mCount] = self.fair_price
        self.offer_list_head = ['insid','datetime','Basis','MovingAverage']  
        vt_symbol = self.sp_contract()
        orders = list(self.strategy_engine.active_limit_orders.items())   
        quote_prices = []
        for vt_orderid, order in orders:
            quote_prices.append(order.price)
        bid1_orders = np.sum([order.price == tick.bid_price_1 for vt_orderid, order in orders]) 
        ask1_orders = np.sum([order.price == tick.ask_price_1 for vt_orderid, order in orders]) 
        # print (self.MMTrading)
        
        if self.rCount > path and self.MMTrading:
            MA = np.mean(self.basislist[self.rCount - self.path:self.rCount])
            std = np.std(self.basislist[self.rCount - self.path:self.rCount])
            std = max(std,self.min_std*ts)
            self.offer_list.append([self.maker.split('.')[0],tick.datetime,tick.fair_price,MA])
            orders = list(self.strategy_engine.active_limit_orders.items())
            #先判断撤单    
            for vt_orderid, order in orders:
                price = order.price
                direction = order.direction.value
                #平仓
                if price==tick.bid_price_1 and direction == '多':
                    if self.net > self.maxPos:
                        self.cancel_order(vt_orderid)
                        # print ('cancel1')
                        # continue
                    elif self.net <0 and price > MA - std * self.close_std:
                        self.cancel_order(vt_orderid)
                        # print ('cancel2')
                        # continue
                    elif self.net >=0 and price > MA - std * self.open_std:
                        self.cancel_order(vt_orderid)
                        # print ('cancel3')
                        # continue
                
                if price==tick.ask_price_1 and direction == '空':
                    if self.net < -self.maxPos:
                        self.cancel_order(vt_orderid)
                        # continue
                    elif self.net >0 and price < MA + std * self.close_std:
                        self.cancel_order(vt_orderid)
                        # continue
                    elif self.net <=0 and price < MA + std * self.open_std:
                        self.cancel_order(vt_orderid)    
                        # continue

            #开始铺单
            my_bid1 = np.floor((tick.bid_price_1 - (self.grid_start-1)*ts)/self.grid_interval/ts)*self.grid_interval*ts
            my_ask1 = np.ceil((tick.ask_price_1 + (self.grid_start-1)*ts)/self.grid_interval/ts)*self.grid_interval*ts
            # print (tick.bid_price_1)
            # print (tick.ask_price_1)    
            for i in range(self.grid_layer):
                my_bid = my_bid1 - i * self.grid_interval * ts
                my_ask = my_ask1 + i * self.grid_interval * ts
                if my_bid not in quote_prices:
                    self.buy(vt_symbol,my_bid,lots,'MM')
                    # print (my_bid)
                if my_ask not in quote_prices:
                    self.short(vt_symbol,my_ask,lots,'MM')
                    # print (my_ask)
            #盘口有edge主动挂单
            if self.net >-self.maxPos and self.net < self.maxPos:
                if bid1_orders==0 and self.net>=0 and tick.bid_price_1 <= MA - std*self.open_std:
                    self.buy(vt_symbol,tick.bid_price_1,lots,'MM')
                    
                if bid1_orders==0 and self.net<0 and tick.bid_price_1 <= MA - std*self.close_std:
                    self.buy(vt_symbol,tick.bid_price_1,lots,'MM')
                
                if ask1_orders==0 and self.net<=0 and tick.ask_price_1 >= MA + std*self.open_std:
                    self.short(vt_symbol,tick.ask_price_1,lots,'MM')
                if ask1_orders==0 and self.net>0 and tick.ask_price_1 >= MA + std*self.close_std:
                    self.short(vt_symbol,tick.ask_price_1,lots,'MM')
                
                # print (MA)
                
                
        self.last_net = self.net
        self.last_tick = tick
        # self.on_maker_time(tick)
        
    def  on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if volume > 0:
            if trade.direction.value=='多':        
                self.net += volume  
            elif trade.direction.value=='空':    
                self.net -= volume 
 
    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return
 
        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
            self.buy_hedge_orderids,
            self.short_hedge_orderids,
            self.buy_refer_orderids,
            self.short_refer_orderids
        ]:
            if buf_vt_orderids and order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
 
    def on_time(self, dt: datetime):
        """
        Callback of new tick time update.
        """ # influxdb数据迟8小时 此处是在夜盘21：05之后开始报价
        if not(
            (dt.time() > dtime(21, 5) and dt.time() < dtime(22, 55))
            or (dt.time() > dtime(9, 5) and dt.time() < dtime(10, 14))
            or (dt.time() > dtime(10, 31) and dt.time() < dtime(11, 29))
            or (dt.time() > dtime(13, 31) and dt.time() < dtime(14, 55))
        ):
            self.cancel_all()
            self.MMTrading = False
        else:
            self.MMTrading = True