# -*- coding: utf-8 -*-
"""
Created on Wed Mar 15 16:24:58 2023

@author: Lenovo
"""

import numpy as np
import pandas as pd
import cx_Oracle


##  ————————————————————————————————————参数设置————————————————————————————————————————
monthmult = np.array([1, 1.1, 1.2, 1])
monthminedge = np.array([0.4, 0.3, 0.4, 0.4])
monthmaxedge = np.array([1.4, 1.4, 1.4, 1.4])
callextra = np.array([0, 0, 0, 0])
putextra = np.array([0, 0, 0, 0])

# os.environ['path'] = r'D:\Oracle\Instant Client\bin'
cx_Oracle.init_oracle_client(lib_dir=r"E:\instantclient_23_5")
oracle_tns = cx_Oracle.makedsn('168.70.16.21', 1521, 'prdqqzs3')
con = cx_Oracle.connect('hs_asset', 'hundsun', oracle_tns)
sql_cmd = 'SELECT * FROM HS_USER.HT_EXCALIBUR'
data = pd.read_sql(sql_cmd, con)
sql_cmd = 'SELECT * FROM HS_USER.PRODUCTS'
product = pd.read_sql(sql_cmd, con)
data = pd.merge(data, product, left_on='OPTIONCODE', right_on='SECURITYID', how='inner')

month = np.sort(data.ENDDATE.unique())
monthparam = pd.DataFrame(monthmult, index=month, columns=['monthmult'])
monthparam['monthminedge'] = monthminedge
monthparam['monthmaxedge'] = monthmaxedge
monthparam['callextra'] = callextra
monthparam['putextra'] = putextra

sql_cmd = 'SELECT * FROM HS_ASSET.HT_OPTION_LEVEL_PARAM'
Options = pd.read_sql(sql_cmd, con)
Options = Options[Options.ISOPENTAKEOUT == '2']

newopt = pd.merge(Options, data, on='OPTIONCODE')
newopt = pd.merge(newopt, monthparam, left_on='ENDDATE', right_index=True)

newopt['absdelta'] = np.abs(newopt.GEN2DELTA)
newopt.loc[newopt['absdelta'] > 0.5, 'EDGEthre'] = newopt.loc[newopt['absdelta'] > 0.5, 'monthmaxedge']
newopt.loc[newopt['absdelta'] <= 0.5, 'EDGEthre'] = newopt.loc[newopt['absdelta'] <= 0.5, 'monthminedge']
newopt['newedge'] = (newopt['absdelta'] - 0.5) / 0.4 * np.abs(1 - newopt.EDGEthre) + 1
newopt['newedge'] = np.minimum(np.maximum(newopt['newedge'], newopt['monthminedge']), newopt['monthmaxedge'])
newopt.loc[newopt.OPTIONTYPE == 'C', 'newedge'] = newopt.loc[newopt.OPTIONTYPE == 'C', 'newedge'] + newopt.loc[
    newopt.OPTIONTYPE == 'C', 'callextra']
newopt.loc[newopt.OPTIONTYPE == 'P', 'newedge'] = newopt.loc[newopt.OPTIONTYPE == 'P', 'newedge'] + newopt.loc[
    newopt.OPTIONTYPE == 'P', 'putextra']

newopt['newedge'] = np.around(newopt['newedge'] * newopt['monthmult'], 2)


def formula_update(newopt, oracle_tns):
    con = cx_Oracle.connect('hs_asset', 'hundsun', oracle_tns)
    c = con.cursor()
    for i in newopt.index:
        cmd1 = ("UPDATE HS_ASSET.HT_OPTION_LEVEL_PARAM SET EDGEMULT='" + str(newopt.loc[i, 'newedge'])
                + "' WHERE OPTIONCODE='" + str(newopt.loc[i, 'OPTIONCODE']) + "'")
        x = c.execute(cmd1)
    con.commit()
    c.close()
    con.close()


formula_update(newopt, oracle_tns)
