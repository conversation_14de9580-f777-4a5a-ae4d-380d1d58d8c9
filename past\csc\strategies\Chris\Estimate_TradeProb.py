# -*- coding: utf-8 -*-
"""
Created on Fri Oct 15 18:07:27 2021
#估算五档成交概率
@author: zhanghc
"""

#import python pacakges
import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
import math
from datetime import timedelta
import datetime

import GetMarketData

def tradeindicator(bid1_next,ask1_next,last_next,volume_next,dict_tradeprob,ordertype,orderprice):
    trade_flag = 1 if ordertype[0]=='b' else -1
    if trade_flag == 1:
        if ask1_next <= orderprice or (last_next <= orderprice and volume_next!=0):
            dict_tradeprob[ordertype]+=1
    else:
        if bid1_next >= orderprice or (last_next >= orderprice and volume_next!=0):
            dict_tradeprob[ordertype]+=1

if __name__ == '__main__':

       
    # contracts = pd.read_excel("contracts.xlsx")
    contracts = ['pp2209.DCE','l2209.DCE','v2209.DCE','eg2209.DCE','eb2208.DCE','pg2208.DCE']
    
    
    beginStr = '2022-7-13T21:00:00.0Z'
    endStr = '2022-7-14T15:00:00.0Z'
    # mode = 'dev'
    mode = 'prod'
    beginT = datetime.datetime.strptime(beginStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    endT = datetime.datetime.strptime(endStr,'%Y-%m-%dT%H:%M:%S.%fZ').timestamp() *1000000000
    result = pd.DataFrame()
    for contract in contracts:
        exchange = contract[-3:]
        contract = contract[:-4]
        # print (exchange)
        # print (contract)
        df = GetMarketData.getmarketdata(mode,exchange,contract,beginT,endT)
        df['v'] = df['v'].diff().fillna(0)
        # print (df.columns)
        
        # dict_tradeprob={'bid5':0,'bid4':0,'bid3':0,'bid2':0,'bid1':0,'ask1':0,'ask2':0,'ask3':0,'ask4':0,'ask5':0}
        dict_tradeprob={'b_p5':0,'b_p4':0,'b_p3':0,'b_p2':0,'b_p1':0,'a_p1':0,'a_p2':0,'a_p3':0,'a_p4':0,'a_p5':0}
        for i in range(df.shape[0]-1):
           bid1_next = df['b_p1'][i+1]
           ask1_next = df['a_p1'][i+1]
           last_next = df['last_p'][i+1]
           volume_next = df['v'][i+1]
           for key in dict_tradeprob.keys():
               tradeindicator(bid1_next,ask1_next,last_next,volume_next,dict_tradeprob,key,df[key][i])
        df_prob = pd.DataFrame([dict_tradeprob])
    
        result = pd.concat([result,df_prob])  
    result.index = contracts
    count = result.apply(lambda x: x.sum(), axis=1)
    for col in result.columns:
        result[col] = result[col]/count
        result[col] = result[col].apply(lambda x:"{:.2%}".format(x))
    result.to_excel('_trade_prob.xlsx')

   
    