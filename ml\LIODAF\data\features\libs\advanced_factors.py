"""
高级因子模块
包含订单簿深度、斜率、流量等高级微观结构因子
@author: lining
"""
import pandas as pd
import numpy as np
from ..factor_manager import factor_manager, Factor, FactorCategory



# 计算总买卖委托量不平衡度
def calculate_vol_imbalance(data: pd.DataFrame) -> pd.Series:
    """计算总买卖委托量不平衡度
    公式: vol_imbalance = (total_bid_vol - total_ask_vol) / (total_bid_vol + total_ask_vol)
    """
    total_bid_vol = [f'BidVol{i}' for i in range(1, 6) if f'BidVol{i}' in data.columns]
    total_ask_vol = [f'AskVol{i}' for i in range(1, 6) if f'AskVol{i}' in data.columns]
    return (total_bid_vol - total_ask_vol) / (total_bid_vol + total_ask_vol)


factor_manager.register_factor(Factor(
    name="vol_imbalance",
    category=FactorCategory.BASIC,
    description="总买卖委托量不平衡度",
    calculation=calculate_vol_imbalance,
    dependencies=["total_bid_vol", "total_ask_vol"],
    source="lining"
))


def calculate_volume_intensity(data: pd.DataFrame) -> pd.Series:
    """计算交易量强度
    公式: volume_intensity = tradedVol / spread
    """
    if 'spread' not in data.columns:
        spread = data['AskPrice1'] - data['BidPrice1']
    else:
        spread = data['spread']
    return data['tradedVol'] / spread


factor_manager.register_factor(Factor(
    name="volume_intensity",
    category=FactorCategory.ADVANCED,
    description="交易量与价差的比率，反映交易强度",
    calculation=calculate_volume_intensity,
    dependencies=["tradedVol", "AskPrice1", "BidPrice1"],
    source="lining"
))


### 订单簿深度

def calc_depth(df):
    """计算订单簿深度
    
    该指标衡量市场的流动性，通过计算买卖双方挂单的总价值。
    
    公式: depth = Σ(bid_price_i * bid_size_i + ask_price_i * ask_size_i)
    """
    depth = df['BidPrice1'] * df['BidVol1'] + df['AskPrice1'] * df['AskVol1'] + df['BidPrice2'] * df['BidVol2'] + df[
        'AskPrice2'] * df['AskVol2']
    return depth


factor_manager.register_factor(Factor(
    name="depth",
    category=FactorCategory.ADVANCED,
    description="订单簿深度",
    calculation=calc_depth,
    dependencies=["BidPrice1", "BidVol1", "AskPrice1", "AskVol1", "BidPrice2", "BidVol2", "AskPrice2", "AskVol2"],
    source="lining-zh/291948456/2486930660"
))


### 订单簿斜率

def calc_slope(df):
    """计算订单簿斜率
    
    该指标衡量价格变动对订单量的敏感性，反映市场深度。
    
    公式: slope = (v1/v0-1)/abs(p1/p0-1)+(v2/v1-1)/abs(p2/p1-1)
    返回值: (平均斜率, 斜率不平衡度)
    """
    midvol = (df['BidVol1'] + df['AskVol1']) / 2
    midprice = (df['BidPrice1'] + df['AskPrice1']) / 2
    slope_bid = ((df['BidVol1'] / midvol) - 1) / abs((df['BidPrice1'] / midprice) - 1) + (
            (df['BidVol2'] / df['BidVol1']) - 1) / abs((df['BidPrice2'] / df['BidPrice1']) - 1)
    slope_ask = ((df['AskVol1'] / midvol) - 1) / abs((df['AskPrice1'] / midprice) - 1) + (
            (df['AskVol2'] / df['AskVol1']) - 1) / abs((df['AskPrice2'] / df['AskPrice1']) - 1)
    return abs(slope_bid - slope_ask)


factor_manager.register_factor(Factor(
    name="slope2",
    category=FactorCategory.ADVANCED,
    description="订单簿斜率不平衡度",
    calculation=lambda data: calc_slope(data),
    dependencies=["BidPrice1", "BidVol1", "AskPrice1", "AskVol1", "BidPrice2", "BidVol2", "AskPrice2", "AskVol2"],
    source="lining-zh/291948456/2486930660"
))


### 订单流不平衡

def calc_ofi(df):
    """计算订单流不平衡

    该指标衡量买卖订单流的不平衡程度，可预测短期价格走势。

    公式基于买卖订单量变化和价格变动方向的组合。
    """
    # 订单流不平衡(Order Flow Imbalance)计算逻辑解释：
    # bidprice.diff()等于0,会计算a-b,也就是买一量-买一量.shift().
    # 1. 买方积极性：当买一价格上涨(diff>=0)时，当前买一量(BidVol1)表示积极买入
    a = df['BidVol1']*np.where(df['BidPrice1'].diff()>=0,1,0)
    
    # 2. 买方消极性：当买一价格下跌(diff<=0)时，前一时刻买一量(BidVol1.shift())表示消失的买单
    b = df['BidVol1'].shift()*np.where(df['BidPrice1'].diff()<=0,1,0)

    # 3. 卖方积极性：当卖一价格下跌(diff<=0)时，当前卖一量(AskVol1)表示积极卖出
    c = df['AskVol1']*np.where(df['AskPrice1'].diff()<=0,1,0)
    
    # 4. 卖方消极性：当卖一价格上涨(diff>=0)时，前一时刻卖一量(AskVol1.shift())表示消失的卖单
    d = df['AskVol1'].shift()*np.where(df['AskPrice1'].diff()>=0,1,0)
    
    # 最终OFI = 买方积极性 - 买方消极性 - 卖方积极性 + 卖方消极性
    # 正值表示买方力量更强，负值表示卖方力量更强
    return a - b - c + d

factor_manager.register_factor(Factor(
    name="ofi2",
    category=FactorCategory.ADVANCED,
    description="订单流不平衡",
    calculation=lambda data: calc_ofi(data),
    dependencies=["BidVol1", "BidPrice1", "AskVol1", "AskPrice1"],
    source="lining-zh/291948456/2486930660|diy"
))

### 加权标准化价差

def calc_wss12(df):
    """计算加权标准化价差
    计算订单簿离散度

    该指标衡量订单分布的集中程度，反映市场的深度和流动性结构。

    该指标通过加权方式计算买卖价差，并进行标准化处理。

    公式: WSS = (加权ask - 加权bid) / 中间价
    """
    ask = (df['AskPrice1'] * df['AskVol1'] + df['AskPrice2'] * df['AskVol2'])/(df['AskVol1']+df['AskVol2'])
    bid = (df['BidPrice1'] * df['BidVol1'] + df['BidPrice2'] * df['BidVol2'])/(df['BidVol1']+df['BidVol2'])
    midprice = (df['AskPrice1'] + df['BidPrice1']) / 2
    return (ask - bid) / midprice

factor_manager.register_factor(Factor(
    name="wss12",
    category=FactorCategory.ADVANCED,
    description="加权标准化价差, 计算方法: (加权ask - 加权bid) / 中间价",
    calculation=lambda data: calc_wss12(data),
    dependencies=["AskPrice1", "AskVol1", "BidPrice1", "BidVol1", "AskPrice2", "AskVol2", "BidPrice2", "BidVol2"],
    source="lining-zh/291948456/2486930660|diy"
))

# 计算累计价差
def calculate_accumulated_price_spread(data: pd.DataFrame) -> pd.Series:
    """计算累计价差"""
    return sum(data[f"AskPrice{i}"] - data[f"BidPrice{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_price_spread",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖价差的累计值",
    calculation=calculate_accumulated_price_spread,
    dependencies=[f"AskPrice{i}" for i in range(1, 6)] + [f"BidPrice{i}" for i in range(1, 6)],
    source="lining-orderbook-dynamic-features v5"
))


# 计算累计成交量差异
def calculate_accumulated_volume_spread(data: pd.DataFrame) -> pd.Series:
    """计算累计成交量差异"""
    return sum(data[f"AskVol{i}"] - data[f"BidVol{i}"] for i in range(1, 6))


factor_manager.register_factor(Factor(
    name="accumulated_volume_spread",
    category=FactorCategory.ADVANCED,
    description="所有层级买卖量差异的累计值",
    calculation=calculate_accumulated_volume_spread,
    dependencies=[f"AskVol{i}" for i in range(1, 6)] + [f"BidVol{i}" for i in range(1, 6)],
    source="lining-orderbook-dynamic-features v5"
))


def calculate_volume_intensity(data: pd.DataFrame) -> pd.Series:
    """计算交易量强度
    公式: volume_intensity = tradedVol / spread
    """
    if 'spread' not in data.columns:
        spread = data['AskPrice1'] - data['BidPrice1']
    else:
        spread = data['spread']
    return data['tradedVol'] / spread


factor_manager.register_factor(Factor(
    name="volume_intensity",
    category=FactorCategory.ADVANCED,
    description="交易量与价差的比率，反映交易强度",
    calculation=calculate_volume_intensity,
    dependencies=["tradedVol", "AskPrice1", "BidPrice1"],
))


def total_turnover(data: pd.DataFrame) -> pd.Series:
    """计算
    公式: total_turnover = ∑(AskPrice_i * AskVol_i + BidPrice_i * BidVol_i) - ∑(AskPrice_i * AskVol_i + BidPrice_i * BidVol_i)

    """
    p1 = data['AskPrice1'] * data['AskVol1'] + data['BidPrice1'] * data['BidVol1']
    p2 = data['AskPrice2'] * data['AskVol2'] + data['BidPrice2'] * data['BidVol2']
    return p2 - p1


factor_manager.register_factor(Factor(
    name="total_turnover",
    category=FactorCategory.DIY,
    description="总成交量",
    calculation=total_turnover,
    dependencies=["AskPrice1", "AskVol1", "BidPrice1", "BidVol1", "AskPrice2", "AskVol2", "BidPrice2", "BidVol2"],
    source="lining-zh/291948456/answer/2486930660"
))
