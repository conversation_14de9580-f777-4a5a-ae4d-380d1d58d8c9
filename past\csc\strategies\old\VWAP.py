# -*- coding: utf-8 -*-
"""
Created on Tue Jul 20 14:52:36 2021

@author: humy2
"""

""""""
import numpy as np
from typing import List, Dict
from datetime import datetime, time as dtime
from datetime import timedelta

from vnpy.app.my_portfolio_strategy import StrategyTemplate, StrategyEngine
from vnpy.trader.object import TickData, OrderData, TradeData

from vnpy.trader.constant import Status
import numpy as np
import talib as tb
import pickle

class VWAP(StrategyTemplate):
    """"""
    author = "kehao"

    parameters = [
        'lots',
        'edge',
        'stop',
        'priceticks',
        'sizes',
        'refer',
        'maker',
        'path'
    ]

    def __init__(
        self,
        strategy_engine: StrategyEngine,
        strategy_name: str,
        vt_symbol: str,
        setting: dict,
    ):
        """"""
        super().__init__(strategy_engine, strategy_name, vt_symbol, setting)

        self.buy_vt_orderids = []
        self.short_vt_orderids = []
        self.buy_hedge_orderids = []
        self.short_hedge_orderids = []

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_ticks(0)
        length=99999 
        self.mAsk=np.zeros(length)  
        self.mBid=np.zeros(length)
        self.mFair=np.zeros(length)
        self.mVolume=np.zeros(length)
        self.mCount=0
        self.lastMaker={"net":0, "tick":None, "timepoint":0}
        self.isQuoting=False
        self.avg = 0
        self.lastTicks={self.maker:0,self.refer:0}
        self.buy_price= 0
        self.short_price = 0
        self.net = 0
        
    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_ticks(self, ticks: Dict[str, TickData]):
        """
        Callback of new tick data update.
        """
        #对比tick
        for key in ticks:
            if ticks[key]!=self.lastTicks[key]:
                if key==self.maker:
                    self.on_tick_maker(ticks[key])
                if key==self.refer:
                    self.on_tick_refer(ticks[key])
                self.lastTicks[key] = ticks[key]
        #决定on_tick
        
        #储存之前的tick
        
    def on_tick_refer(self,tick):
        pass

    def on_tick_maker(self, tick):
        
        ts = self.priceticks[self.maker]
        lots = self.lots
        shortResendFlag=False
        buyResendFlag=False
        mCount = self.mCount  
        net = self.net
        edge = self.edge
        stop = self.stop * edge * lots / (lots + abs(net))
        spread = tick.ask_price_1 - tick.bid_price_1
        self.mBid[mCount] = tick.bid_price_1
        self.mAsk[mCount] = tick.ask_price_1
    
        if mCount > 0 :    
            turnover = tick.turnover - self.lastMaker["tick"].turnover 
            volume = tick.volume - self.lastMaker["tick"].volume
            if volume != 0:
                self.mVolume[mCount] = volume
                self.mFair[mCount] = turnover/volume/self.sizes[self.maker]

        #计算VWAP止盈止损线
        path = self.path
        if mCount > path:
            VWAPline_up = (self.mFair[mCount-path:mCount+1] * self.mVolume[mCount-path:mCount+1]).sum() / self.mVolume[mCount-path:mCount+1].sum() + stop * ts
            VWAPline_low = (self.mFair[mCount-path:mCount+1] * self.mVolume[mCount-path:mCount+1]).sum() / self.mVolume[mCount-path:mCount+1].sum() - stop * ts
            Volume = self.mVolume[mCount-path:mCount+1].mean()

            VV = min(Volume,abs(self.net))
            VV = round(VV)

            if net>0 and 0<spread<=max(1, 0.8*edge)*ts : 
                if self.avg < VWAPline_low:  #止盈
                    self.cancel_all() # 避免自成交 
                    self.short_hedge_orderids = self.short(self.maker,tick.bid_price_1, abs(self.net),'stop_profit')
                    print("send hedge order",tick.bid_price_1,VV)
                if self.avg > VWAPline_up:  #止损
                    self.cancel_all() # 避免自成交
                    self.short_hedge_orderids = self.short(self.maker,tick.bid_price_1, VV,'stop_loss')
                    print("send hedge order",tick.bid_price_1,VV)

            if net<0 and 0<spread<=max(1, 0.8*edge)*ts : 
                if self.avg > VWAPline_up:  #止盈
                    self.cancel_all() # 避免自成交 
                    self.buy_hedge_orderids = self.buy(self.maker,tick.ask_price_1, abs(self.net),'stop_profit')
                    print("send hedge order",tick.ask_price_1,VV)
                if self.avg < VWAPline_low:  #止损
                    self.cancel_all() # 避免自成交
                    self.buy_hedge_orderids = self.buy(self.maker,tick.ask_price_1, VV,'stop_loss')
                    print("send hedge order",tick.ask_price_1,VV)
                
        #### Algo : JoinMarket  ~~~~~~~无效价格情况暂未处理~~~~~~~~~~~~~~~~~~ 
        bidP = tick.bid_price_1
        askP = tick.ask_price_1
        
        if askP!=self.short_price:
            shortResendFlag=True
        if bidP!=self.buy_price:
            buyResendFlag=True
            
        self.short_price = askP
        self.buy_price = bidP

        if mCount>35:
            if not self.buy_vt_orderids:
                self.buy_vt_orderids = self.buy(self.maker,self.buy_price, self.lots,'MM')
            elif buyResendFlag and self.buy_vt_orderids[0]:
                if self.cancel_order(self.buy_vt_orderids[0]):  #cancel完会跳转至onOrder，然后自动发一个单
                    self.buy_vt_orderids = self.buy(self.maker,self.buy_price, self.lots,'MM')
            
            if not self.short_vt_orderids:
                self.short_vt_orderids = self.short(self.maker,self.short_price, self.lots,'MM')
            elif shortResendFlag and self.short_vt_orderids[0]:
                if self.cancel_order(self.short_vt_orderids[0]): #cancel完会跳转至onOrder，然后自动发一个单
                    self.short_vt_orderids = self.short(self.maker,self.short_price, self.lots,'MM')

        self.lastMaker["timepoint"]=mCount
        self.lastMaker["net"] =self.net
        self.lastMaker["tick"]=tick
        
        self.mCount += 1


    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        volume=trade.volume
        price=trade.price    
        if trade.direction.value=='多':
            if self.net>=0 :            
                 self.avg = (self.net*self.avg +volume*price)/(self.net+volume)              
            elif volume+self.net>0: # net<0 # 平仓
                 self.avg = price         
            self.net += volume 
        #         
        if trade.direction.value=='空':    
            if self.net<=0:
                self.avg =(-self.net*self.avg + volume*price)/(-self.net+volume)
            elif volume-self.net>0: # net >0 # 平仓
                self.avg=price
            self.net -= volume 

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        if order.status not in (Status.ALLTRADED, Status.CANCELLED):
            return

        # 移除已经结束的委托号
        for buf_vt_orderids in [
            self.buy_vt_orderids,
            self.short_vt_orderids,
        ]:
            if order.vt_orderid in buf_vt_orderids:
                buf_vt_orderids.remove(order.vt_orderid)
        

