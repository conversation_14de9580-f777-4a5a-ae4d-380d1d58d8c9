{"cells": [{"cell_type": "code", "execution_count": 216, "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "#import scipy.stats as t\n", "plt.style.use(\"dark_background\")\n"]}, {"cell_type": "code", "execution_count": 217, "outputs": [{"data": {"text/plain": "<Figure size 576x576 with 2 Axes>", "image/png": "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\n"}, "metadata": {}, "output_type": "display_data"}], "source": ["from matplotlib.ticker import StrMethodFormatter\n", "df = pd.read_csv(\"../data_cleaned/orders.csv\")\n", "# Histogram\n", "nbins = int(np.sqrt(df.shape[0])) * 2\n", "f, (ax1, ax2) = plt.subplots(2, 1, figsize=(8,8))\n", "counts, bins, patches = ax1.hist(df * 10 ** -6, nbins, log=True)\n", "from scipy.signal import find_peaks\n", "# for local maxima\n", "peaks = find_peaks(counts, threshold=200, distance=5)\n", "mid_bins = np.array((bins[1:] + bins[:-1])/2)\n", "ax1.scatter(mid_bins[peaks[0]], counts[peaks[0]],\n", "            color=plt.rcParams['axes.prop_cycle'].by_key()['color'][1],  marker=\".\")\n", "ax1.set_xlabel(\"Order Size $\\\\times 10^{-6}$\")\n", "ax1.set_xlim((0, 7))\n", "ax1.set_yscale(\"log\")\n", "ax1.grid(alpha=0.5)\n", "ax1.set_ylabel(\"Counts\")\n", "ax1.set_xticks([x * .5 for x in range(14)])\n", "ax1.xaxis.set_major_formatter(StrMethodFormatter('{x:1.2f}'))\n", "# Trade percentage\n", "total_volume = counts.sum()\n", "pcts = np.array([])\n", "for vol in counts:\n", "    pcts = np.append(pcts, vol/total_volume)\n", "ax2.plot(mid_bins, pcts)\n", "ax2.set_xlabel(\"Order Size $\\\\times 10^{-6}$\")\n", "ax2.set_xlim((0, 7))\n", "ax2.set_yscale(\"log\")\n", "ax2.grid(alpha=0.5)\n", "ax2.set_ylabel(\"Trade percentage\")\n", "ax2.set_xticks([x * .5 for x in range(14)])\n", "ax2.xaxis.set_major_formatter(StrMethodFormatter('{x:1.2f}'))\n", "plt.show()"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 217, "outputs": [], "source": [], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}