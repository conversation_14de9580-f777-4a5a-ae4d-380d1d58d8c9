#%% 
'''输入influxdb对应的ip接口'''
from vnpy.database.ip.ip_setting import IPSETTING
ip = IPSETTING()

ip.set_ip('DCE.dev')

import warnings
warnings.filterwarnings('ignore')

""" 'SHFE.prod'代表上期所生产机
    'SHFE.dev'代表上期所东坝机房
    'DCE.prod'代表大商所生产机
    'DCE.dev'代表大商所东坝机房    
"""
#%%
"""排队多品种撮合"""
from vnpy.app.my_portfolio_strategy.backtesting import BacktestingEngine
from strategies.basisStrategy import BasisStrategy
import datetime
from vnpy.trader.constant import Interval
import pandas as pd
import numpy as np

#%%
maker='eb2204.DCE'
refer='eb2203.DCE'

multiplier=5

engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=False,save_result=False,refer_test=False,fast_join=True,refer_late=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
engine.set_parameters(
    vt_symbols=[maker,refer], # 回测品种
    interval=Interval.TICK, # 回测模式的数据间隔

    start=datetime.datetime(2022,2, 9, 21, 10), # 开始时间
    end=datetime.datetime(2022, 2,10, 15, 0), # 结束时间
    rates={maker: 0,refer: 0}, # 手续费率
    slippages={maker: 0,refer: 0}, # 滑点
    sizes={maker: multiplier,refer: multiplier}, # 合约规模
    priceticks={maker: 1,refer: 1}, # 一个tick大小
    capital=1_000_000, # 初始资金
)

# 添加回测策略，并修改内部参数
engine.clear_data()

# engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})
maxDelta=100
minDelta=100
redifEWMAFlag=False
engine.add_strategy(BasisStrategy, {'lots': 5,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':6,'maxPos':20,'alpha':0.03,'gamma':0.5,'typ':'maker','maxDelta':maxDelta,'minDelta':minDelta,'redifEWMAFlag':redifEWMAFlag})

engine.load_data() # 加载历史数据
engine.run_backtesting() # 进行回测
df = engine.calculate_tick_result() # 计算逐日盯市盈亏
stat = engine.calculate_tick_statistics() # 统计日度策略指标
# engine.duty_statistics()

#%%
edge_list = [5, 6]
refer_alpha_list = [('eb2203.DCE', 0.03), ('eb2204.DCE', 1)]
maker = 'eb2204.DCE'
date_list = [(datetime.datetime(2022,2, 7+i, 21, 10), datetime.datetime(2022,2, 8+i, 15, 00)) for i in range(4)]
result1 = {
          'edge':[],
          'alpha':[],
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []
 }

for start, end in date_list:
    for edge in edge_list:
        for refer, alpha in refer_alpha_list:
            multiplier=5
            
            engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=False,duty=False,save_result=False,refer_test=False,fast_join=True,refer_late=True) #Counter代表是否只统计对价成交，duty代表是否统计义务
            engine.set_parameters(
                vt_symbols=[maker,refer], # 回测品种
                interval=Interval.TICK, # 回测模式的数据间隔
            
                start=start, # 开始时间
                end=end, # 结束时间
                rates={maker: 0,refer: 0}, # 手续费率
                slippages={maker: 0,refer: 0}, # 滑点
                sizes={maker: multiplier,refer: multiplier}, # 合约规模
                priceticks={maker: 1,refer: 1}, # 一个tick大小
                capital=1_000_000, # 初始资金
            )
            
            # 添加回测策略，并修改内部参数
            engine.clear_data()
            
            # engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})
            maxDelta=100
            minDelta=100
            redifEWMAFlag=False
            engine.add_strategy(BasisStrategy, {'lots': 5,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':20,'alpha':alpha,'gamma':0.5,'typ':'maker','maxDelta':maxDelta,'minDelta':minDelta,'redifEWMAFlag':redifEWMAFlag})
            
            engine.load_data() # 加载历史数据
            engine.run_backtesting() # 进行回测
            df = engine.calculate_tick_result() # 计算逐日盯市盈亏
            stat = engine.calculate_tick_statistics() # 统计日度策略指标                    
            for key in engine.result.keys():
                result1[key].append(engine.result[key])  
            result1['alpha'].append(alpha)
            result1['edge'].append(edge)


result1 = pd.DataFrame(result1)


#%%
from vnpy.trader.analysis import Analysis

analysis = Analysis(engine)
df = analysis.pnl_plot(savefig=False) 

trades = analysis.trade()

#%%


#%%

#maker='m2203.DCE'
#refer='m2201.DCE'
# maker='hc2202.SHFE'
# refer='hc2201.SHFE'

#maker='pg2112.DCE'
#refer='pg2111.DCE'
#maker='c2111.DCE'
#refer='c2201.DCE'
# maker='CF203.CZCE'
# refer='CF201.CZCE'



maker='hc2209.SHFE'
refer='hc2205.SHFE'


multiplier=10

underlying_list = ['m']
maker_list = ['2203.DCE', '2207.DCE','2208.DCE']


#time_list = list(pd.read_csv('trading_day.csv')['DateTime'])
'''time_list= [
     '2021/9/10',
 '2021/9/13',
 '2021/9/14',
 '2021/9/15',
 '2021/9/16',
 '2021/9/17',
 '2021/9/20',
 '2021/9/21',
 '2021/9/22',
 '2021/9/23',
 '2021/9/24',
 '2021/9/27',
 '2021/9/28',
 '2021/9/29',
 '2021/9/30',
 '2021/10/8',
time_list= ['2021/10/11',
 '2021/10/12',
 '2021/10/13',
 '2021/10/14',
 '2021/10/15',
 '2021/10/18',
 '2021/10/19',
 '2021/10/20',
 '2021/10/21',
 '2021/10/22',
 '2021/10/25',
 '2021/10/26',
 '2021/10/27',
 '2021/10/28',
 '2021/10/29','''
time_list= ['2021/12/24',
 '2021/12/27',
 '2021/12/28',
 '2021/12/29',
 '2021/12/30',
 '2021/12/31']
edge_list = [2,3]
gamma_list = [0.3, 0.5]
alpha_list = [0.03]
maxPos_list = [30]

# def run_backtest(i):
#%%
def run_backtest(startdate, enddate, maker, refer, edge = 2, maxPos = 3, gamma = 0.3, alpha = 0.01, save_flag = False, name = 'basis_strategy', number = 0, typ = 'taker', onlyCrossFlag=False):
    save_strategy=True
    save_risk = True
    save_order = True
    save_trade = True
    onlyCrossFlag=False
    typ = 'maker'
    number=number
    tick_size = 1
    duty=False

    lots=5
    redifEWMAFlag=False
    
    parameters = {'alpha':alpha, 'edge':edge, 'maxPos':maxPos, 'gamma':gamma}
    
    engine = BacktestingEngine(alwaysInQueueHeadFlag=False,cancelFailProba=0,onlyCrossFlag=onlyCrossFlag,duty=duty,save_result=False,refer_test=False,rule_out = False) #Counter代表是否只统计对价成交，duty代表是否统计义务
    start=datetime.datetime(startdate[0], startdate[1], startdate[2], 9, 10) # 开始时间

    end=datetime.datetime(enddate[0], enddate[1],enddate[2], 15, 0) # 结束时间
    
    engine.set_parameters(
        vt_symbols=[maker,refer], # 回测品种
        interval=Interval.TICK, # 回测模式的数据间隔
    
        start=start, # 开始时间
        end=end, # 结束时间
        rates={maker: 0,refer: 0}, # 手续费率
        slippages={maker: 0,refer: 0}, # 滑点
        sizes={maker: multiplier,refer: multiplier}, # 合约规模
        priceticks={maker: tick_size,refer: tick_size}, # 一个tick大小
        capital=1_000_000, # 初始资金
    )
    
    # 添加回测策略，并修改内部参数
    engine.clear_data()
    
    # engine.add_strategy(LiquidityStrategy, {'lots': 4,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':7})
    
    engine.add_strategy(BasisStrategy, {'lots': lots,'maker':maker,'refer':refer,'priceticks':engine.priceticks,'sizes':engine.sizes,'edge':edge,'maxPos':maxPos,'alpha':alpha,'gamma':gamma,'typ':typ,'redifEWMAFlag':redifEWMAFlag, 'maxDelta':100, 'minDelta':100})
    
    engine.load_data() # 加载历史数据
    engine.run_backtesting() # 进行回测
    
    df = engine.calculate_tick_result() # 计算逐日盯市盈亏
    stat = engine.calculate_tick_statistics() # 统计日度策略指标
    df_mkt = pd.DataFrame()
    if save_flag:
        if save_risk:
            df_mkt = engine.get_risk(multiplier=multiplier)
        else:
            df_mkt = pd.DataFrame([{'symbol':maker,'datetime':start},{'symbol':refer,'datetime':end}])
        if save_strategy:
            df_strategy = engine.get_strategy()
        else:
            df_strategy = pd.DataFrame([])
        engine.save_influx(df_mkt = df_mkt, df_strategy = df_strategy, number=number, name=name, parameters = parameters, save_risk=save_risk, save_strategy=save_strategy, save_order=save_order, save_trade=save_trade)
    
    
    # print(engine.result)
    # engine.duty_statistics()
    # from vnpy.trader.analysis import Analysis
    
    # analysis = Analysis(engine)
    # df = analysis.pnl_plot() 
    return engine, df_mkt #, df
# # =======
# # time_list = [7,8,11,12,13,14,15]
# time_list = [7,8]
#%%

multiplier=5

startdate, enddate, maker, refer, edge, maxPos, gamma, alpha, number = ([2022, 2, 9], [2022, 2, 9], 'eb2204.DCE', 'eb2204.DCE', 5, 20, 0.5, 1, 3)
engine, df_mkt = run_backtest(startdate, enddate, maker, refer, edge, maxPos, gamma, alpha, save_flag=False, number=number)

df_offer_list = pd.DataFrame(engine.strategy.offer_list)
df_offer_list.columns = engine.strategy.offer_list_head
df_mkt = engine.get_risk(multiplier=multiplier)
# df_test = df_mkt[df_mkt.symbol=='rb2202']

#%% 调试order表
# 调试order表
from influxdb import InfluxDBClient
parameters = {'alpha':alpha, 'edge':edge, 'maxPos':maxPos, 'gamma':gamma}
name = 'basis_strategy'

client = InfluxDBClient('************',9001,'reader','iamreader','omm')
measurement = name+'_future_order_vnpy'

insid_list = list(df_mkt.symbol.drop_duplicates())

t_start = int(df_mkt.iloc[0]['datetime'].timestamp()*10**9)
t_end = int(df_mkt.iloc[-1]['datetime'].timestamp()*10**9)

for insid in insid_list:

    query = "delete from "+measurement+" where time>="+str(t_start)+" and time<="+str(t_end)+" and number="+"'"+str(number)+"'"
    for parameter in parameters.keys():
        query += " and "+parameter+"="+"'"+str(parameters[parameter])+"'"
    query += " and insid="+"'"+str(insid)+"'"
    client.query(query)
print('order表删除成功')

json_body = []
orders = [{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
    'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
    } for x in engine.get_all_orders()]    

for row in orders:
    order_time = row['time']
    price = row['price']
    direction = row['direction']
    volume = row['volume']
    traded = row['traded']
    orderid = row['orderid']
    symbol = row['symbol']
    comments = row['comments']
    
    measurement = name+'_future_order_vnpy'
    
    
    
    current_time = int(datetime.datetime.strptime(order_time,'%Y-%m-%d %H:%M:%S.%f').timestamp()*10**9)
    
    order_price = float(price)
    long_short = 0 if direction == '多' else 1
    volume_original_total = int(volume)
    volume_traded = int(traded)
    volume_total = int(volume_original_total - volume_traded)
    order_id = str(orderid)
    insid = str(symbol)
    comments = str(comments)

    tags = {"insid": insid, "number": number, "order_id": order_id}
    tags.update(parameters)            
    
    body = {
            "measurement": measurement, 
            "time": current_time, 
            "tags": tags, 
            "fields": {
                "order_price": order_price, 
                "long_short": long_short,                 
                "volume_original_total": volume_original_total,    
                "volume_traded": volume_traded,   
                "volume_total": volume_total,                        
                "comments": comments   
            }, 
        }
    json_body.append(body)  
client = InfluxDBClient('************',9001,'reader','iamreader','omm')
res = client.write_points(json_body, batch_size = 10000)
print('order表存储完毕')   
    
#%%
#a = get_risk(engine, multiplier=10)
    
#test1 = a[a.symbol=='rb2202']



#%%
import time
import os

t = time.time()
result = {'insid' : [],
          'edge':[],
          'maxPos':[],
          'gamma':[],
          'alpha':[],
 '日期': [],
 '首个交易时点': [],
 '最后交易时点': [],
 '起始资金': [],
 '结束资金': [],
 '总收益率': [],
 '最大回撤': [],
 '百分比最大回撤': [],
 '最长回撤天数': [],
 '总盈亏': [],
 '总手续费': [],
 '总滑点': [],
 '总成交金额': [],
 '总成交笔数': [],
 '总成交量': [],
 '逐笔盈亏': []}
count = 0

# k = 2


number = 3

typ = 'maker'
save_flag=True


for i in range(1, len(time_list)):
    startdate = [int(x) for x in time_list[i-1].split('/')]
    enddate = [int(x) for x in time_list[i].split('/')]
    for underlying in underlying_list:
        refer = underlying + '2205.DCE'
        for m in maker_list:
            maker = underlying + m
            for edge in edge_list:
                for gamma in gamma_list:
                    for maxPos in maxPos_list:
                        for alpha in alpha_list:
                            try:
                                count += 1
                                engine, df_mkt = run_backtest(startdate=startdate, enddate=enddate, maker=maker, refer=refer, edge=edge, maxPos=maxPos, gamma=gamma, alpha=alpha, save_flag = save_flag, name = 'basis_strategy', number = number, typ=typ, onlyCrossFlag=False)
                                df = pd.DataFrame(engine.strategy.offer_list)
                                df.columns = engine.strategy.offer_list_head
                                if df_mkt.empty:
                                    df_mkt = engine.get_risk(multiplier=multiplier)
                                for key in engine.result.keys():
                                    result[key].append(engine.result[key])
                                result['insid'].append(maker)
                                result['edge'].append(edge)
                                result['maxPos'].append(maxPos)
                                result['gamma'].append(gamma)
                                result['alpha'].append(alpha)
                                
                                #path = 'C:/Users/<USER>/Desktop/backtestresult_'+typ+'/'+underlying+'/'+str(engine.end.date())+'/'
                                #folder = os.path.exists(path)
                                #if not folder:
                                    #os.makedirs(path)
                                #df.to_csv(path+typ+'_'+maker.split('.')[0]+'-'+refer.split('.')[0]+'-'+str(edge)+'-'+str(maxPos)+'-'+str(int(10*gamma))+'-'+str(int(100*alpha))+'.csv')
                                #df_mkt.to_csv(path+typ+'_mkt_'+maker.split('.')[0]+'-'+refer.split('.')[0]+'-'+str(edge)+'-'+str(maxPos)+'-'+str(int(10*gamma))+'-'+str(int(100*alpha))+'.csv')
                                
                                #df_result=pd.DataFrame(result)

                                #df_result.to_csv('C:/Users/<USER>/Desktop/backtestresult_'+typ+'/result_total_'+typ+str(k)+'.csv')
                                print(time.time()-t, count, (startdate, enddate, maker, refer, edge, maxPos, gamma, alpha), '存储成功')                                
                            except Exception as err:
                                print(time.time()-t, count, (startdate, enddate, maker, refer, edge, maxPos, gamma, alpha), '存储失败',err)





#%%
df_dict = df.to_dict('records')
df_correct = []
net_last = 0
ask_last = 0
bid_last = 0

askamount = 0
bidamount = 0
askamount_refer = 0
bidamount_refer = 0

net = 0
pnl = 0
net_refer = 0
pnl_refer = 0
multiplier = 10

for row in df_dict:
    net = row['net']
    trade = net-net_last
    net_refer = - net
    fair_refer = row['fair_refer']
    maker_bidP = row['maker_bidP']
    maker_askP = row['maker_askP']
    my_bidP = row['my_bidP']
    my_askP = row['my_askP']    
    
    askamount += -trade*bid_last if trade < 0 else 0
    bidamount += trade*ask_last if trade > 0 else 0
    askamount_refer += trade*fair_refer if trade > 0 else 0
    bidamount_refer += -trade*fair_refer if trade < 0 else 0
    
    pnl = (askamount-bidamount+net*(row['maker_bidP']+row['maker_askP'])/2)*multiplier
    pnl_refer = (askamount_refer-bidamount_refer+net_refer*fair_refer)*multiplier
    
    
    net_last = net
    row['cumPnl'] = pnl+pnl_refer
    row['pnl'] = pnl
    row['pnl_refer'] = pnl_refer
    row['trade_price'] = maker_bidP if trade < 0 else maker_askP if trade > 0 else 0
    ask_last = row['maker_askP']
    bid_last = row['maker_bidP']
    
    df_correct.append(row.copy())

df_correct = pd.DataFrame(df_correct)



#%%

import pandas as pd
import numpy as np
from datetime import timedelta

trades = pd.DataFrame([{'time':(engine.trades[x].datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),
    'price':engine.trades[x].price,'direction':-1 if engine.trades[x].direction.value=='空' else 1,'volume':engine.trades[x].volume,
    'orderid': engine.trades[x].orderid,'symbol':engine.trades[x].symbol,'comments':engine.trades[x].comments
    } for x in engine.trades])
orders = pd.DataFrame([{'time':(x.datetime).strftime('%Y-%m-%d %H:%M:%S.%f'),'price':x.price,
    'direction':x.direction.value,'volume':x.volume,'traded':x.traded, 'orderid': x.orderid,'symbol':x.symbol,'comments':x.comments
    } for x in engine.get_all_orders()])


trades['net'] = (trades.volume*trades.direction).cumsum()
trades['cash'] = (trades['direction']*(-1)*trades['volume']*trades['price'])*engine.sizes[engine.vt_symbols[0]]
trades['realPnl'] = trades['cash'].cumsum()
trades['pnl'] = trades.realPnl + trades.net * trades.price * multiplier


mkt = pd.DataFrame(engine.history_data.values())
mkt = mkt[['datetime','symbol','bid_price_1','bid_volume_1','bid_price_2','bid_volume_2','bid_price_3','bid_volume_3',
        'bid_price_4','bid_volume_4','bid_price_5','bid_volume_5','ask_price_1','ask_volume_1','ask_price_2',
        'ask_volume_2','ask_price_3','ask_volume_3','ask_price_4','ask_volume_4','ask_price_5','ask_volume_5',
        'last_price','volume','turnover']]
mkt['time'] = mkt['datetime'].apply(lambda x : (x).strftime('%Y-%m-%d %H:%M:%S.%f'))
mkt_maker = mkt[mkt.symbol==engine.target_symbol]
mkt_maker['dV'] = mkt_maker.volume - mkt_maker.volume.shift(1)
mkt_maker['dT'] = (mkt_maker.turnover - mkt_maker.turnover.shift(1)).apply(lambda x:str(x))

mkt_main = mkt[mkt.symbol==engine.main_symbol]
mkt_main['dV'] = mkt_main.volume - mkt_main.volume.shift(1)
mkt_main['dT'] = (mkt_main.turnover - mkt_main.turnover.shift(1)).apply(lambda x:str(x))

#%%
df_test = df_correct[df_correct.trade_price!=0]
df_test = df_test.reset_index()

#%%
from influxdb import InfluxDBClient
client = InfluxDBClient('************',9001,'reader','iamreader','omm')



