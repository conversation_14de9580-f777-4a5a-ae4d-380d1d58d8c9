
"""
特征生成器模块
@author: lining
"""
import pandas as pd
import numpy as np
import os
from tqdm import tqdm
import time

from core import config
from utils.utils import log_print
from .factor_manager import factor_manager, FactorCategory
from .factors_for_use import use_features

class OrderBookFeatureGenerator:
    
    def __init__(self, data):
        self.data = data
        self.features = []
        self.all_features = {}
        self.output_dir = config.OUTDIR + "/features"
        self.scalers = {}  
        
        
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_all_features(self):
        
        log_print("开始生成所有特征...", level='info')

        
        required_cols = ['mid', 'AskPrice1', 'BidPrice1', 'tradedVol']
        missing_cols = [col for col in required_cols if col not in self.data.columns]
        if missing_cols:
            log_print(f"警告：数据中缺少必要的列: {missing_cols}", level='warning')
            return pd.DataFrame(), []
            

        
        total_num = 0
        skip_num = 0
        for category in FactorCategory:
            factors = factor_manager.get_factors_by_category(category)
            for factor in tqdm(factors, desc=f"生成{category}特征"):
                total_num+=1
                calc_time = -1
                is_used = 'no'
                try:
                    
                    if factor.name not in use_features:
                        skip_num+=1
                        is_used = 'skip'
                        log_print(f"因子 {factor.name} 未在use_features列表中，跳过", level='debug')
                        continue
                    
                    if factor_manager.validate_factor(factor.name, self.data):
                        
                        start_time = time.time()
                        self.data[factor.name] = factor_manager.calculate_factor(factor.name, self.data)
                        end_time = time.time()
                        self.features.append(factor.name)
                        is_used = 'yes'
                        calc_time = end_time - start_time
                    else:
                        is_used = 'failed'
                        print(f"警告：因子 {factor.name} 验证失败")
                except Exception as e:
                    is_used = 'failed'
                    print(f"警告：因子 {factor.name} 计算失败: {str(e)}")
                finally:
                    self.all_features[factor.name] = [is_used, calc_time]
        
        
        unused_factors = [factor for factor in use_features if factor not in self.all_features]
        if unused_factors:
            log_print(f"警告：以下因子未生成: {unused_factors}", level='warning')
        
        
        nan_cols = self.data.columns[self.data.isna().any()].tolist()
        if nan_cols:
            log_print(f"警告：以下特征列包含NaN值: {nan_cols}", level='warning')
            log_print(f"NaN值数量: {self.data[nan_cols].isna().sum()}", level='warning')
            

            
        return self.data, self.features

    
    def add_interaction_features(self):
        
        interaction_features = []
        
        existing_features = [col for col in self.features if col in self.data.columns]
        
        
        for col in existing_features:
            if col in self.data.columns:
                
                self.data[col] = self.data[col].replace([np.inf, -np.inf], np.nan)
                
                self.data[col] = self.data[col].fillna(self.data[col].median())
        
        for i, col1 in enumerate(existing_features):
            for col2 in existing_features[i+1:]:
                if col1 in self.data.columns and col2 in self.data.columns:
                    
                    interaction = self.data[col1] * self.data[col2]
                    
                    interaction = interaction.clip(
                        lower=interaction.quantile(0.01),
                        upper=interaction.quantile(0.99)
                    )
                    self.data[f'{col1}_{col2}_interaction'] = interaction
                    interaction_features.append(f'{col1}_{col2}_interaction')
        
        
        for col in existing_features:
            if col in self.data.columns:
                
                squared = self.data[col]**2
                squared = squared.clip(
                    lower=squared.quantile(0.01),
                    upper=squared.quantile(0.99)
                )
                self.data[f'{col}_squared'] = squared
                
                
                
                log_input = self.data[col].abs() + 1e-10
                log_value = np.log1p(log_input)
                log_value = log_value.clip(
                    lower=log_value.quantile(0.01),
                    upper=log_value.quantile(0.99)
                )
                self.data[f'{col}_log'] = log_value
                
                interaction_features.append(f'{col}_squared')
                interaction_features.append(f'{col}_log')

        self.features += interaction_features