{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PIN computation\n", "To compute the PIN of a given day, we need to  optimize the product of the likelihood computed on each time interval in the day.\n", "In particular we fix a time interval of 5 minutes to discretize time, and since we are dealing with the dta of a single trade day we only comppute the corresponding PIN, without further analysis of its time evolution.\n", "\n", "Note that this problem must be approached by taking particular care about the optimization method choosen. We tested all the methods from scipy.optimize.minimize for bounded problems, both gradient-based and gredient-free, but most of the results exhibited high dependence on the initial guess for the set of parameters. We then choose to apply powell method, which is a gradient-free method, since it is the only one which actually exhibits an evolution and results to be unbiased by the initial point.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def likelihood(x, bid, ask, T): #x = [alpha, delta, eps, mu]\n", "    \"\"\"\n", "    likelihood function for the model\n", "    args:\n", "        x: parameters of the model\n", "        bid: observation of the bid side\n", "        ask: observation of the ask side\n", "        T: time bins\n", "    \"\"\"\n", "    #compute likelihood with <PERSON><PERSON><PERSON>'s (15) notation\n", "    from scipy.stats import poisson\n", "    likelihood = (1-x[0])*poisson.pmf(k=bid,mu=x[2]*T)*poisson.pmf(k=ask,mu=x[2]*T)+\\\n", "                +x[0]*x[1]*poisson.pmf(k=bid,mu=x[2]*T)*poisson.pmf(k=ask,mu=(x[2]+x[3])*T)+\\\n", "                +x[0]*(1-x[1])*poisson.pmf(k=bid,mu=(x[2]+x[3])*T)*poisson.pmf(k=ask,mu=x[2]*T)\n", "    return likelihood\n", "\n", "def loss (x, bid, ask, T):\n", "    \"\"\"\n", "    loss function for the model\n", "    args:\n", "        x: parameters of the model (to train)\n", "        bid: list of observations of the bid side\n", "        ask: list of observations of the ask side\n", "        T: time bin width (assumed the same for each bin)\n", "    \"\"\"\n", "    prod=[]\n", "    #restricting the loss function to values which do not kill the output\n", "    for b, a in zip(bid, ask):\n", "        l=likelihood(x, b, a, T)\n", "        if l>0: prod.append(l)\n", "        else: continue\n", "    return -np.prod(prod)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from scipy.optimize import minimize\n", "from tqdm import tqdm\n", "from datetime import timedelta\n", "time_delta = <PERSON><PERSON><PERSON>(minutes=1)\n", "\n", "occurrences = pd.read_csv(\"../data_cleaned/occurrences.csv\")\n", "np.random.seed(0)\n", "r=minimize(loss, x0=np.random.uniform(size=4),#\n", "                args=(occurrences['bid_observations'], occurrences['ask_observations'], time_delta.total_seconds()),\n", "                method='powell', bounds=[(0, 1), (0, 1), (0, None), (0, None)])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PIN: 0.24\n", "alpha: 0.64\n", "delta: 0.64\n"]}], "source": ["params = {'alpha': r.x[0], 'delta': r.x[0], 'eps': r.x[0], 'mu': r.x[0]}\n", "PIN = params['alpha']*params['mu']/(params['alpha']*params['mu']+2*params['eps'])\n", "\n", "print('PIN: {:.2f}'.format(PIN))\n", "print('alpha: {:.2f}'.format(params['alpha']))\n", "print('delta: {:.2f}'.format(params['delta']))"]}], "metadata": {"interpreter": {"hash": "18d52c8ed671898ef8933058dd13604df3989d986c9ab74e47e71b13bd58768e"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}