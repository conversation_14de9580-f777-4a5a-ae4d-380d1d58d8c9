# 核心依赖
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.4.0
seaborn>=0.11.0
scikit-learn>=1.0.0
pyarrow>=6.0.0

# 数据处理
h5py>=3.6.0
tables>=3.7.0
polars>=0.15.0
fastparquet>=0.8.0

# 机器学习
xgboost>=1.6.0
lightgbm>=3.3.0
catboost>=1.0.0
shap>=0.40.0
optuna>=3.0.0

# 深度学习 (可选)
tensorflow>=2.9.0; python_version < "3.11"
torch>=1.12.0
torchvision>=0.13.0

# 可视化
plotly>=5.9.0
kaleido>=0.2.1
bokeh>=2.4.0
holoviews>=1.15.0

# 工具
tqdm>=4.64.0
pyyaml>=6.0
joblib>=1.1.0
python-dotenv>=0.20.0
click>=8.1.0
typer>=0.6.0
rich>=12.5.0

# 测试
pytest>=7.0.0
pytest-cov>=3.0.0
