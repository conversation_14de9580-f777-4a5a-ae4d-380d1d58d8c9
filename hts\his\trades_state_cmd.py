import math
import os
import sys
import pandas as pd
import datetime
import numpy as np

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir)))

from db_solve import csvdown
from db_solve.configs import paths
from db_solve.parreader import readmd, loadtrade, load_orders, loadvol
from db_solve.utility import drop_col_nan, time_resh
from pnlback.signal.signals import *

# 设置 Pandas 显示选项
pd.set_option('display.max_columns', None)  # 显示所有列
pd.set_option('display.max_rows', None)  # 显示所有行
pd.set_option('display.width', None)  # 自动调整列宽
pd.set_option('display.max_colwidth', None)  # 显示所有单元格的内容
pd.set_option('display.expand_frame_repr', False)  # 设置Pandas选项以打印不换行的DataFrame


def main():
    # ---------------------
    # try:
    #     csvdown.download_csv(under, datetoday)
    # except Exception as e:
    #     print(e)
    print('load fut md')

    fut1_data = readmd(key1, [fut, ], datetoday, tradetime00, Source=4, coltype='all', minnum=6, cal_sig=False,
                       sig=siglist)
    fut1_data['dsvol'] = fut1_data['Volume'].diff(1)
    fut1_data['dsstate'] = fut1_data['State'].diff(1)
    fut1_data['dropcol'] = abs(fut1_data['dsmid']) + abs(fut1_data['dsvol'])
    # futdata = drop_col_nan(futdata, 'dropcol')
    fut1_data['avg_prc'] = fut1_data['avg_prc'].round(2) - fut1_data['mid']
    fut1_data['dsavg'] = fut1_data['avg_prc'].diff(1)
    fut1_data['LastPrice'] = fut1_data['LastPrice'] - fut1_data['mid']
    fut1_data['dmidminum'] = fut1_data['mid_minnum'].diff(1)
    fut1_data['emamin'] = np.round(fut1_data['dmidminum'].ewm(alpha=0.5).mean(), 2)

    fut1_data['p20'] = np.round(fut1_data['mid'] - fut1_data['mid'].rolling(window=20).mean(), 2)

    print('md done')

    fut1_data = fut1_data.add_suffix('_x')

    # siglist = signal_list

    colmix1 = list(map(lambda a: a + '_x',
                       ['LastPrice', 'dsvol', 'avg_prc', 'BidVol1', 'AskVol1', 'emamin', 'p20'] + siglist +
                       ['edge', 'mid', 'dsmid', ]))

    colmix7 = []
    colmix72 = []

    colmix2 = []
    if mix2 == 1:
        print('load fut2 md')
        md_data1 = readmd(key2, [fut2, ], datetoday, tradetime00, cal_sig=False)
        md_data1['fut'] = md_data1['mid']
        # mdData1 = mdData1[mdData1['Source'] == 7]
        # mdData1 = mdData1(futdata, 'dslast')
        md_data1['dsvol'] = md_data1['Volume'].diff(1)
        md_data1['edge'] = (md_data1['AskPrice1'] - md_data1['BidPrice1'])

        md_data1 = pd.merge_asof(md_data1, fut1_data[colmix1],
                                 left_index=True, right_index=True, direction='backward',
                                 tolerance=pd.Timedelta('100000000s'), allow_exact_matches=True)

        md_data1['basis'] = md_data1['mid_x'] - md_data1['mid']
        md_data1['dsbasis'] = md_data1['basis'].diff(1)
        md_data1['basisema'] = np.round(md_data1['dsbasis'].ewm(alpha=0.2).mean(), 2)

        md_data1 = md_data1.add_suffix('_f')
        colmix2 = list(map(lambda a: a + '_f',
                           [col2, 'dsmid', 'basisema', 'dsvol', 'edge'] + colmix72))

        fut1_data = fut1_data.join(md_data1[colmix2], lsuffix='_x', rsuffix='_y', how='outer')
        fut1_data = fut1_data.sort_index()

    colmix3 = []
    if mix3 == 1:
        print('load fut3 md2')
        md_data2 = readmd(key3, [fut3, ], datetoday, tradetime00, cal_sig=False)
        md_data2['fut'] = md_data2['mid']
        if col3 == 'ForQuoteSysID':
            md_data2 = md_data2[md_data2['Source'] == 7]
        elif fut3[0:2] == 'SH':
            md_data2 = md_data2[md_data2['Source'] == 4]
        md_data2['dslast'] = md_data2[col3].diff(1)
        md_data2 = drop_col_nan(md_data2, 'dslast')
        md_data2['dsvol'] = md_data2['Volume'].diff(1)
        md_data2 = md_data2.add_suffix('_z')

        colmix3 = list(map(lambda a: a + '_z', ['dslast', 'dsvol']))

        fut1_data = fut1_data.join(md_data2[colmix3], how='outer')
        fut1_data = fut1_data.sort_index()

    colmix_trade = []
    if mixtrd == 1:
        print('load trade')
        trade_data = loadtrade(datetoday, tradepath, tradetime00, multi=multi, multi2=multi2)
        trade_data['Spot'] = (trade_data['Spot'] * s_mult).round(1)
        print('done trade')

        trade_data['Code'] = trade_data['Code'].astype(str)
        trade_data['Edge'] = trade_data['Edge'].round(int(math.log10(s_mult)) + 2) * s_mult

        trade_data = pd.merge_asof(trade_data, fut1_data[['mid_x']].rename(columns={'mid_x': 'fut'}).dropna(),
                                   left_index=True, right_index=True, direction='backward',
                                   tolerance=pd.Timedelta('1s'), allow_exact_matches=True)

        trade_data['sdiff'] = trade_data['Spot'] - trade_data['fut']
        trade_data['dpnldiff'] = trade_data['TDeltaPNL'].round(0).diff(1)

        colmix_trade = ['Code', u'类型', u'数量', u'价格', 'Spot', 'sdiff', 'Vol', 'Basis', 'Tv', 'AdjTv', 'Delta',
                        'PDeltaU', 'Vega', 'monthPVega', 'PNL', 'TradePNL', 'Edge', 'ordertime', 'per_delta',
                        'dpnldiff']

        if mix7 == 1:
            ordersdata = load_orders(str_ord, [], datetoday, tradetime00)

            # ordersdata = ordersdata[
            #     (ordersdata['stateStr'] == 'BeforSending') | (ordersdata['stateStr'] == 'BeforCanceling')]
            # ordersdata = ordersdata[ordersdata['orderProperty'].astype(str) == '-1']

            ordersdata = ordersdata[(ordersdata['stateStr'] == 'BeforSending')]
            ordersdata.index = ordersdata['insertTime']
            ordersdata = ordersdata.sort_index()

            longdir = ['OpenLong', 'CloseShort']
            shortdir = ['OpenShort', 'CloseLong']

            mdlong = ordersdata[ordersdata['directionStr'].isin(longdir)]
            mdshort = ordersdata[ordersdata['directionStr'].isin(shortdir)]

            trade_data = pd.merge_asof(trade_data, mdlong[['insertTime', 'Symbol', 'price']],
                                       left_index=True, right_index=True, direction='backward',
                                       left_by='Code', right_by='Symbol',
                                       tolerance=pd.Timedelta('100000000s'), allow_exact_matches=True)
            trade_data = pd.merge_asof(trade_data,
                                       mdshort[['insertTime', 'Symbol', 'price', 'DeltaAdj', 'vegaAdj']],
                                       left_index=True, right_index=True, direction='backward',
                                       left_by='Code', right_by='Symbol',
                                       tolerance=pd.Timedelta('100000000s'), allow_exact_matches=True)

            trade_data = trade_data.sort_index()

            trade_data['bid_e'] = -(trade_data['price_x'] - trade_data['AdjTv']).round(
                int(math.log10(s_mult)) + 2) * s_mult
            trade_data['ask_e'] = (trade_data['price_y'] - trade_data['AdjTv']).round(
                int(math.log10(s_mult)) + 2) * s_mult
            trade_data['Edge2'] = np.maximum(trade_data['bid_e'].fillna(0), trade_data['ask_e'].fillna(0))
            trade_data['REdge_mult'] = (trade_data['Edge'] / np.maximum(trade_data['Edge2'], mintick * s_mult)).round(1)
            trade_data['edge_diff'] = -(trade_data['bid_e'] - trade_data['ask_e']).round(2)
            trade_data['diff/delta'] = (
                    trade_data['edge_diff'] / abs(trade_data['per_delta'])).round(2)
            trade_data['spotPAdj'] = (
                    trade_data['DeltaAdj'] / (trade_data['per_delta']) * s_mult).round(2)
            trade_data['volPAdj'] = (
                    trade_data['vegaAdj'] / ((trade_data['Vega']) / trade_data['multi'] / trade_data[u'数量'])).round(2)
            # trade_data['DeltaAdj'] = (
            #         trade_data['DeltaAdj'] * s_mult).round(2)
            # trade_data['vegaAdj'] = (
            #         trade_data['vegaAdj'] * s_mult).round(2)

            fut1_data = fut1_data.join(trade_data, lsuffix='_x', rsuffix='_y', how='outer')

            colmix7 = ['REdge_mult', 'insertTime_x', 'insertTime_y', 'bid_e', 'price_x', 'price_y', 'ask_e',
                       'edge_diff', 'diff/delta', 'spotPAdj', 'volPAdj', 'DeltaAdj', 'vegaAdj', ]

            fut1_data = pd.merge_asof(fut1_data,
                                      mdlong[mdlong.Symbol.isin(optcodes)][
                                          ['insertTime', 'Symbol', 'price', 'volume']].add_suffix(
                                          '_l'),
                                      left_index=True, right_index=True, direction='forward',
                                      tolerance=pd.Timedelta('20ms'), allow_exact_matches=True)
            fut1_data = pd.merge_asof(fut1_data,
                                      mdshort[mdshort.Symbol.isin(optcodes)][
                                          ['insertTime', 'Symbol', 'price', 'volume']].add_suffix(
                                          '_r'),
                                      left_index=True, right_index=True, direction='forward',
                                      tolerance=pd.Timedelta('20ms'), allow_exact_matches=True)

            fut1_data['edge_or'] = -(fut1_data['price_l'] - fut1_data['price_r']) / 2 * s_mult
            fut1_data['time_or'] = (
                    (np.where(fut1_data['insertTime_l'].notnull(), fut1_data['insertTime_l'], fut1_data['insertTime_r'])
                     - fut1_data.index.values) / np.timedelta64(1, 's') * s_mult)
            fut1_data['mid_or'] = ((fut1_data['price_l'] + fut1_data['price_r']) / 2).round(int(math.log10(multi)))
            colmix72 = ['time_or', 'volume_l', 'price_l', 'price_r', 'volume_r', 'edge_or', 'mid_or', ]

            fut1_data['drop'] = np.where(fut1_data['insertTime_l'].notnull(), fut1_data['insertTime_l'].diff(-1),
                                         fut1_data['insertTime_r'].diff(-1))
            fut1_data.loc[fut1_data['drop'] == pd.Timedelta(0), colmix72] = np.nan
        else:
            fut1_data = fut1_data.join(trade_data, lsuffix='_x', rsuffix='_y', how='outer')

    colmix4 = []
    if mix4 == 1:
        print('load fut3 md2')
        md_data2 = readmd(key4, [fut4, ], datetoday, tradetime00, cal_sig=False)
        md_data2['fut'] = md_data2['mid']
        md_data2 = md_data2[md_data2['Source'] == 7]
        md_data2['dslast'] = md_data2[col3].diff(1)
        md_data2['dd'] = abs(md_data2['dslast']) + abs(md_data2['dsmid'])

        md_data2['dvol'] = md_data2['Volume'].diff(1)
        md_data2 = drop_col_nan(md_data2, 'dvol')
        md_data2 = md_data2.add_suffix('_k')

        colmix4 = [col3 + '_k', 'dsmid_k', 'dslast_k', 'dvol_k', ]

        fut1_data = fut1_data.join(md_data2[colmix4], how='outer')
        fut1_data = fut1_data.sort_index()

    colmix44 = []
    if mix44 == 1:
        print('load fut44 md2')
        md_data2 = readmd(key44, [fut44, ], datetoday, tradetime00, cal_sig=False)
        md_data2['fut'] = md_data2[col44]
        md_data2 = md_data2[md_data2['Source'] == 7]
        md_data2['dslast'] = md_data2[col44].diff(1)
        md_data2 = md_data2.add_suffix('_i')

        colmix44 = [col44 + '_i', 'dslast_i']

        fut1_data = fut1_data.join(md_data2[colmix44], how='outer')
        fut1_data = fut1_data.sort_index()

    colmix5 = []
    if mix5 == 1:
        print('load vol')
        md_data2 = loadvol(path.str_vol % datetoday, optcodes, tradetime00, all=False)

        md_data2['forward'] = md_data2['spot'] * np.exp(
            md_data2.rf * md_data2.time2expiry - md_data2['br'] * md_data2['time2expiry'])
        md_data2['basis'] = md_data2['forward'] - md_data2['spot']
        md_data2['mid'] = md_data2['bid'] / 2 + md_data2['ask'] / 2

        md_data2['dsspot'] = md_data2['spot'].diff(1).round(int(math.log10(s_mult)) + 2)
        md_data2['dsmid'] = md_data2['mid'].diff(1).round(int(math.log10(s_mult)) + 2)
        md_data2['dsbasis'] = md_data2['basis'].diff(1).round(int(math.log10(s_mult)) + 2)
        md_data2['dsbasis_M'] = (md_data2['dsmid'] / md_data2['delta'] - md_data2['dsspot']).round(
            int(math.log10(s_mult)) + 2)

        md_data2['s'] = (md_data2['spot'].diff(1) * md_data2['delta']).round(int(math.log10(s_mult)) + 2)
        md_data2['b'] = (md_data2['basis'].diff(1) * md_data2['delta']).round(int(math.log10(s_mult)) + 2)
        md_data2['v'] = (md_data2['sigma'].diff(1) * md_data2['vega'] * 100).round(int(math.log10(s_mult)) + 2)
        md_data2['dtv'] = (md_data2['tv'].diff(1)).round(int(math.log10(s_mult)) + 2)

        voldata = md_data2.add_suffix('_v')

        colmix5 = ['spot_v', 'tv_v', 'sigma_v', 'mid_v', 'dsmid_v', 'dsbasis_v', 'dsspot_v', 'dsbasis_M_v']

        fut1_data['mid_xv'] = (fut1_data['mid_x'].fillna(0) * 10).astype(int)
        voldata['spot_v'] = (voldata['spot_v'] * s_mult * 10).astype(int)
        # fut1_data = pd.merge_asof(fut1_data, voldata[colmix5],
        #                           left_index=True, right_index=True,
        #                           left_by='mid_xv', right_by='spot_v',
        #                           direction='nearest',
        #                           tolerance=pd.Timedelta('1000ms'), allow_exact_matches=True)
        fut1_data = fut1_data.join(voldata[colmix5], how='outer')
        fut1_data['spot_v'] = fut1_data['spot_v'].astype(float) / 10
        fut1_data = fut1_data.sort_index()

    colmix6 = []
    colmix66 = []
    if mix6 == 1:
        md_data2 = readmd(str_optmd, None, datetoday, tradetime00, mult=multi, coltype='all', cal_sig=False)

        fut1_data = pd.merge_asof(fut1_data,
                                  md_data2[['timestamp_str', 'Symbol', 'BidVol1', 'BidPrice1', 'AskPrice1', 'AskVol1',
                                            'LastPrice', 'Volume', 'tradedVol', 'avg_prc']].add_suffix('_tm'),
                                  left_index=True, right_index=True, direction='forward',
                                  left_by='Code', right_by='Symbol_tm',
                                  tolerance=pd.Timedelta('600ms'), allow_exact_matches=True)
        fut1_data['avg_prc_tm'] = fut1_data['avg_prc_tm'] * multi / fut1_data['multi']

        fut1_data['avg_edge2'] = np.round(abs(fut1_data['avg_prc_tm'] - fut1_data['AdjTv']) * s_mult /
                                          abs(np.maximum(fut1_data['Edge2'], mintick * s_mult)), 1)
        fut1_data['avg_edge'] = np.round(abs(fut1_data['avg_prc_tm'] - fut1_data['AdjTv']) * s_mult /
                                         (np.maximum(abs(edgedelta * fut1_data['per_delta']), mintick) * s_mult), 1)

        colmix66 = (list(
            map(lambda a: a + '_tm',
                ['timestamp_str', 'BidVol1', 'BidPrice1', 'AskPrice1', 'AskVol1', 'LastPrice', 'tradedVol', 'avg_prc']))
                    + ['avg_edge'])

        md_data2 = md_data2[md_data2['Symbol'].isin(optcodes)].drop_duplicates(subset=['timestamp_str', 'Symbol'])
        sss = Signal(md_data2, minnum=20, roundnum=s_mult, sig=['edge_minnum'])
        sss.sigall()
        md_data2 = sss.data_md

        md_data2['dsmid'] = (md_data2['mid'].diff(1) * s_mult).round(2)
        md_data2['edge'] = (md_data2['AskPrice1'] - md_data2['BidPrice1']) * s_mult

        md_data2 = md_data2.add_suffix('_m')

        colmix6 = ['BidVol1_m', 'BidPrice1_m', 'AskPrice1_m', 'AskVol1_m', 'mid_m', 'dsmid_m',
                   'LastPrice_m', 'tradedVol_m', 'avg_prc_m']

        fut1_data = fut1_data.join(md_data2[colmix6], how='outer')
        fut1_data = fut1_data.sort_index()

    fut1_data = fut1_data.sort_index()

    fut1_data = time_resh(fut1_data)

    fut1_data[
        colmix1 + colmix2 + colmix3 + colmix72 + colmix6 + colmix4 + colmix44 + colmix5 +
        colmix_trade + colmix7 + colmix66
        ].round(roundnum).to_csv(
        path.outdirs + (datetoday + under + 'trades_stat_mix2' + now + '.csv'),
        encoding='utf-16', sep='\t')

    print('all done')


if __name__ == '__main__':
    under = 'SH500'
    optcodes = ['10007447']
    datetoday = '20240904'
    month = '2409'
    siglist = ['im5', 'mid_minnum', 'edge_minnum', 'mixedge_minnum']
    # datetoday = datetime.datetime.now().strftime('%Y%m%d')

    cwd = os.getcwd()
    path = paths.Paths(under, __file__)

    mix2 = 1
    mix3 = 1  # a50
    mix4 = 0  # etf
    mix44 = 0  # iopv
    mix5 = 0  # vol
    mix6 = 1  # opt mkt
    mix7 = 1  # opt ord
    mixtrd = 1  # traderecords

    setting = paths.UNDERCONFIG[under]
    fut = setting['spot'] + month
    fut2 = setting['spot2'] + month
    fut3 = 'CN2409'
    fut4 = '510500_tick'
    fut44 = '159915'

    col2 = 'mid'
    col3 = 'LastPrice'
    col44 = 'ForQuoteSysID'

    str1 = path.dirs + 'md_%s_cffex_multicast.parquet'
    str3 = path.dirs + 'md_%s_tcp_receiver_sub.parquet'
    # str4 = dirs + 'md_%s_sse_fast_fpga.parquet'
    str4 = path.dirs + 'md_%s_inner_receiver.parquet'
    str44 = path.dirs + 'md_%s_shm_receiver.parquet'
    tradepath=path.dirs + 'TradeRecords%s.parquet'
    str_ord = path.dirs + '%s_OrderDetails.parquet'
    str_optmd = path.dirs + 'md_%s_sse_mdgw.parquet'

    key1 = str1
    key2 = str1
    key3 = str3
    key4 = str4
    key44 = str44

    atm = 3.5
    s_mult = 1000
    mintick = 0.0001
    edgedelta = 0.0006

    now = datetime.datetime.now()
    now = datetime.datetime.strftime(now, "_%m%d_%H%M%S")

    futfreq = '500l'
    volfreq = '1000l'
    multi = 10000
    multi2 = 10148
    dsflist = [-1, 1, 2, 4, 10, 20, 60]
    dvlist = [-1, 1, 2, 4, 10, 20, 60]
    roundnum = int(math.log10(multi * 10))

    tradetime00 = [['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']]
    # tradetime00 = [['09:30:10', '10:00:00'], ['13:00:00', '13:00:00']]

    longdir = ['OpenLong', 'CloseShort']
    shortdir = ['OpenShort', 'CloseLong']

    main()
    print('success', datetoday)
    os.startfile(path.outdirs)
    sys.exit()
