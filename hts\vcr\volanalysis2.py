# -*- coding: utf-8 -*-
"""
Created on Sat Jan 20 19:38:02 2024

@author: admin
"""

# -*- coding: utf-8 -*-
"""
Created on Tue Nov 28 20:38:34 2023

@author: admin
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import re

from pandas import to_datetime


def time_add_date(time_str, date_str):
    # Parse the time string to extract hours, minutes, and seconds
    # Assuming the format is always hh:mm:ss:xxxxxx
    if not time_str or not date_str:
        return pd.NaT
    else:
        parsed_time = time_str.split(":")[:3]  # This will give ['13', '50', '12']
        formatted_time = ":".join(parsed_time)  # Joins as '13:50:12'

        # Combine with the date string
        combined_datetime_str = f"{date_str} {formatted_time}"

        # Convert to a datetime object
        datetime_obj = datetime.strptime(combined_datetime_str, "%Y/%m/%d %H:%M:%S")
        return datetime_obj


tad = np.vectorize(time_add_date, otypes=[object])


def std_with_percentile_filter(data, lower_percentile=0.5, upper_percentile=99.5):
    # Calculate the percentile values
    lower_bound = np.percentile(data, lower_percentile)
    upper_bound = np.percentile(data, upper_percentile)

    # Filter out the extreme values
    filtered_data = [x for x in data if lower_bound <= x <= upper_bound]

    # Calculate and return the standard deviation
    return np.std(filtered_data, ddof=1)


def sort_option_codes(option_codes):
    # Custom sorting function
    def custom_sort(code):
        year_month = code[:6]  # Extract year and month (e.g., cu2401)
        type_ = code[6]  # Extract type (C or P)
        strike_price = int(code[7:])  # Extract strike price as integer for proper sorting
        return (year_month, type_, strike_price)

    # Sorting the list based on the custom function
    return sorted(option_codes, key=custom_sort)


def preprocess_tradetable(time_interval, tradetable_df):
    # Convert 'time_seconds' to datetime and set as index
    tradetable_df['time_seconds'] = pd.to_datetime(tradetable_df['time_seconds'], format='%Y/%m/%d %H:%M')
    tradetable_df.set_index('time_seconds', inplace=True)

    # Replace NaN with 0
    tradetable_filled = tradetable_df.fillna(0)

    # Separate and sum positive and negative numbers
    tradetable_pos_sum = tradetable_filled.clip(lower=0).resample(time_interval).sum()
    tradetable_neg_sum = tradetable_filled.clip(upper=0).resample(time_interval).sum()

    return tradetable_pos_sum, tradetable_neg_sum


def resample_dataframes(time_interval, ask_df, bidvol_df, vol_df, tradetable_df):
    # Convert 'time_seconds' to datetime and set as index for each dataframe
    print(ask_df)
    ask_df['time_seconds'] = to_datetime(ask_df.index)
    ask_df.set_index('time_seconds', inplace=True)

    bidvol_df['time_seconds'] = to_datetime(bidvol_df.index)
    bidvol_df.set_index('time_seconds', inplace=True)

    vol_df['time_seconds'] = to_datetime(vol_df.index)
    vol_df.set_index('time_seconds', inplace=True)

    tradetable_df['time_seconds'] = to_datetime(tradetable_df.index, format='%Y/%m/%d %H:%M')
    tradetable_df.set_index('time_seconds', inplace=True)

    # Resample and calculate mean for ask, bidvol, and vol dataframes
    ask_resampled = ask_df.resample(time_interval).mean()
    bidvol_resampled = bidvol_df.resample(time_interval).mean()
    vol_resampled = vol_df.resample(time_interval).mean()

    # Resample and calculate sum of positive and negative numbers for tradetable
    # Replace NaN with 0 for accurate sum calculation
    tradetable_filled = tradetable_df.fillna(0)

    # Separate positive and negative sums
    tradetable_pos_sum = tradetable_filled.clip(lower=0).resample(time_interval).sum()
    tradetable_neg_sum = tradetable_filled.clip(upper=0).resample(time_interval).sum()

    return ask_resampled, bidvol_resampled, vol_resampled, tradetable_pos_sum, tradetable_neg_sum


# strike = "cu2402C69000"
Underlying = "cu"
contract = "cu2407"
trade_records = "TradeRecords20240520-085748.csv"
date = "2024/05/20"

file_name = "vols_20240520.csv"
columnnames = ['time', 'spot', 'contract', 'tv', 'bid', 'ask', "marketopen", 'vol', 'bidvol', 'askvol', 'delta', 'vega',
               'r', 'q', 'T', 'strike', 'x1', 'x2', 'enddate', 'basis', "unknown1", "unknown2", "unknown3", "unknown4",
               "unknown5"]

df = pd.read_csv('D:/code/htsc/daytradeanalysis/vol/' + Underlying + "/" + file_name, names=columnnames)
df["time"] = pd.to_datetime(df["time"])

df['time'] = pd.to_datetime(df['time'])

# print(df['contract'])
# Filtering data for contracts containing 'C2403'
filtered_data = df[df['contract'].str.contains(contract)]

filtered_data['time_seconds'] = filtered_data['time'].dt.floor('S')
# Creating pivot tables for 'vol', 'bidvol', and 'askvol'
pivot_vol = filtered_data.pivot_table(index='strike', columns='time_seconds', values='vol', aggfunc='first')
pivot_vol = pivot_vol.T
result = pivot_vol.std()
print(result)
pivot_bidvol = filtered_data.pivot_table(index='strike', columns='time_seconds', values='bidvol', aggfunc='first')
pivot_bidvol = pivot_bidvol.T
result = pivot_bidvol.std()
print(result)
pivot_askvol = filtered_data.pivot_table(index='strike', columns='time_seconds', values='askvol', aggfunc='first')
pivot_askvol = pivot_askvol.T
result = pivot_askvol.std()
print(result)

# Displaying the first few rows of each table to verify
pivot_vol.head(), pivot_bidvol.head(), pivot_askvol.head()
pivot_vol.to_csv("D:/code/htsc/result/vol.csv")
pivot_bidvol.to_csv("D:/code/htsc/result/bidvol.csv")
pivot_askvol.to_csv("D:/code/htsc/result/ask.csv")

# print(pivot_vol.index,111111)

# unique_optioncodes1 = df['contract'].unique()

# sorted_codes = sort_option_codes(unique_optioncodes1)

# print(sorted_codes)

###成交记录####
match = re.search(r'(\d{8})', file_name)  # Looks for 8 consecutive digits

resampletime = "60S"
if match:
    date_str = match.group(1)  # Extracts the matched string
    print(date_str)  # Outputs: 20230914
else:
    print("Date not found in file_name.")
date_formatted = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"
print(date_formatted)  # Outputs: 2023-09-14

# Convert to pandas Timestamp (optional)
date_timestamp = pd.Timestamp(date_formatted)
print(date_timestamp)

columnnames = ['成交时间', 'Code', 'Expiry', '数量', "Position"]
trade_df = pd.read_csv('D:/code/htsc/daytradeanalysis/trade/' + trade_records, encoding="utf16", sep='\t')
trade_df["formatted_time_string"] = trade_df['成交时间'].str.rsplit(":", 1).str.join(".")
trade_df['time_delta'] = pd.to_timedelta(trade_df["formatted_time_string"])
trade_df['datetime'] = date_timestamp + trade_df['time_delta']
trade_df["absVolume"] = trade_df["数量"]

filtered_data = trade_df[trade_df['Code'].str.contains(contract)]
filtered_data['time_seconds'] = filtered_data['datetime'].dt.floor('S')
# Creating pivot tables for 'vol', 'bidvol', and 'askvol'
pivot_trade = filtered_data.pivot_table(index='Strike', columns='time_seconds', values='absVolume', aggfunc='sum')
pivot_trade = pivot_trade.T
pivot_trade.to_csv("D:/code/htsc/result/tradetable.csv")

resampled_ask, resampled_bidvol, resampled_vol, tradetable_pos_sum, tradetable_neg_sum = resample_dataframes('30s',
                                                                                                             pivot_askvol,
                                                                                                             pivot_bidvol,
                                                                                                             pivot_vol,
                                                                                                             pivot_trade)

resampled_ask.to_csv("D:/code/htsc/result/resampled_ask.csv")
resampled_bidvol.to_csv("D:/code/htsc/result/resampled_bidvol.csv")
resampled_vol.to_csv("D:/code/htsc/result/resampled_vol.csv")
tradetable_pos_sum.to_csv("D:/code/htsc/result/tradetable_pos_sum.csv")
tradetable_neg_sum.to_csv("D:/code/htsc/result/tradetable_neg_sum.csv")
