{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["%pylab is deprecated, use %matplotlib inline and import the required libraries.\n", "Populating the interactive namespace from numpy and matplotlib\n"]}], "source": ["%pylab inline\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["def order_book(month,day):\n", "    data = []\n", "    datapath = '/home/<USER>/SGX-OrderBook-Tick-Data-Trading-Strategy-/High_Frequency_Trading_Strategy_using_machine_learning/Limit_Order_Book_Best_3/order_book_3_2014'\\\n", "                + '_' + str(month) + '_' + str(day) + '.csv'\n", "    order_book = pd.read_csv(datapath,sep=',')\n", "    bid_price_1 = np.array(map(float,order_book['Bid'][1::4]))/100.0\n", "    bid_price_2 = np.array(map(float,order_book['Bid'][2::4]))/100.0\n", "    bid_price_3 = np.array(map(float,order_book['Bid'][3::4]))/100.0\n", "    timestamp = np.array(order_book['Bid_Quantity'][0::4])\n", "    bid_quantity_1 = np.array(map(float,order_book['Bid_Quantity'][1::4]))\n", "    bid_quantity_2 = np.array(map(float,order_book['Bid_Quantity'][2::4]))\n", "    bid_quantity_3 = np.array(map(float,order_book['Bid_Quantity'][3::4]))\n", "    ask_price_1 = np.array(map(float,order_book['Ask'][1::4]))/100.0\n", "    ask_price_2 = np.array(map(float,order_book['Ask'][2::4]))/100.0\n", "    ask_price_3 = np.array(map(float,order_book['Ask'][3::4]))/100.0\n", "    ask_quantity_1 = np.array(map(float,order_book['Ask_Quantity'][1::4]))\n", "    ask_quantity_2 = np.array(map(float,order_book['Ask_Quantity'][2::4]))\n", "    ask_quantity_3 = np.array(map(float,order_book['Ask_Quantity'][3::4]))\n", "    \n", "    bid_quantity_1[isnan(bid_quantity_1)] = 0\n", "    bid_quantity_2[isnan(bid_quantity_2)] = 0\n", "    bid_quantity_3[isnan(bid_quantity_3)] = 0\n", "    ask_quantity_1[isnan(ask_quantity_1)] = 0\n", "    ask_quantity_2[isnan(ask_quantity_2)] = 0\n", "    ask_quantity_3[isnan(ask_quantity_3)] = 0\n", "    \n", "    return timestamp,order_book,bid_price_1,bid_price_2,bid_price_3,bid_quantity_1,\\\n", "            bid_quantity_2,bid_quantity_3,ask_price_1,ask_price_2,ask_price_3,ask_quantity_1,\\\n", "            ask_quantity_2,ask_quantity_3"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["def time_transform(timestamp_time):\n", "    time_second_basic = []\n", "    time_second = []\n", "    for i in range(0,len(timestamp_time),1):\n", "        second = float(timestamp_time[i][11])*36000 + float(timestamp_time[i][12])*3600+\\\n", "                    float(timestamp_time[i][14])*600 + float(timestamp_time[i][15])*60+\\\n", "                    float(timestamp_time[i][17])*10 + float(timestamp_time[i][18])  \n", "        time_second_basic.append(second - 32400.0)\n", "        time_second.append(second)\n", "    return np.array(time_second),np.array(time_second_basic)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": true}, "outputs": [], "source": ["def traded_label_micsecond(time1,time2,time_second_basic,bid_price_1,ask_price_1,traded_time):\n", "    traded = []\n", "    index_ = []\n", "    micsecond = time_second_basic[np.where((time_second_basic >= time1) & (time_second_basic <= time2))]\n", "    for i in range(0,len(micsecond),1):\n", "        \n", "        index = np.where(time_second_basic <= micsecond[i])[0][-1]\n", "        if i == 0:\n", "            index_.append(index)\n", "        if i == len(micsecond) - 1:\n", "            index_.append(index)\n", "        if micsecond[i] <= 25200 - traded_time:\n", "            index_min = np.where(time_second_basic <= micsecond[i] + traded_time)[0][-1]\n", "            if bid_price_1[index] > min(ask_price_1[index:index_min]):\n", "                traded.append(1)\n", "            else:\n", "                traded.append(0)\n", "        elif mi<PERSON>[i] > 25200 - traded_time:\n", "            if bid_price_1[index] > ask_price_1[-1]:\n", "                traded.append(1)\n", "            else:\n", "                traded.append(0)\n", "        #print index,index_min\n", "    return traded,index_"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["def rise_ask(Ask1,timestamp_time_second,before_time):\n", "    Ask1[Ask1 == 0] = mean(Ask1)\n", "    rise_ratio = []\n", "    index = np.where(timestamp_time_second >= before_time)[0][0]    \n", "    #open first before_time mins\n", "    for i in range(0,index,1):\n", "        rise_ratio_ = round((Ask1[i] - Ask1[0])*(1.0)/Ask1[0]*100,5)\n", "        rise_ratio.append(rise_ratio_)\n", "    for i in range(index,len(Ask1),1):\n", "        index_start = np.where(timestamp_time_second[:i] >= timestamp_time_second[i] - before_time)[0][0]\n", "        rise_ratio_ = round((Ask1[i] - Ask1[index_start])*(1.0)/Ask1[index_start]*100,5)\n", "        rise_ratio.append(rise_ratio_)\n", "    return rise_ratio"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [], "source": ["def weight_pecentage(w1,w2,w3):\n", "    Weight_Ask = (w1 * ask_quantity_1 + w2 * ask_quantity_2 + w3 * ask_quantity_3)\n", "    Weight_Bid = (w1 * bid_quantity_1 + w2 * bid_quantity_2 + w3 * bid_quantity_3)\n", "    W_AB = Weight_Ask/Weight_Bid\n", "    W_A_B = (Weight_Ask - Weight_Bid)/(Weight_Ask + Weight_Bid)\n", "    return W_AB,W_A_B"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [{"ename": "TypeError", "evalue": "unsupported operand type(s) for /: 'map' and 'float'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 6\u001b[0m\n\u001b[0;32m      1\u001b[0m month \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[0;32m      2\u001b[0m day \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m2\u001b[39m\n\u001b[0;32m      3\u001b[0m timestamp,order_book,bid_price_1,bid_price_2,bid_price_3,\\\n\u001b[0;32m      4\u001b[0m bid_quantity_1,bid_quantity_2,bid_quantity_3,\\\n\u001b[0;32m      5\u001b[0m ask_price_1,ask_price_2,ask_price_3,ask_quantity_1,\\\n\u001b[1;32m----> 6\u001b[0m ask_quantity_2,ask_quantity_3 \u001b[38;5;241m=\u001b[39m \u001b[43morder_book\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmonth\u001b[49m\u001b[43m,\u001b[49m\u001b[43mday\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[2], line 6\u001b[0m, in \u001b[0;36morder_book\u001b[1;34m(month, day)\u001b[0m\n\u001b[0;32m      3\u001b[0m datapath \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m../Data_Transformation/order book data/order_book_3_2014\u001b[39m\u001b[38;5;124m'\u001b[39m\\\n\u001b[0;32m      4\u001b[0m             \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mstr\u001b[39m(month) \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mstr\u001b[39m(day) \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m.csv\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m      5\u001b[0m order_book \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(datapath,sep\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m,\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m----> 6\u001b[0m bid_price_1 \u001b[38;5;241m=\u001b[39m \u001b[43mnp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43marray\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mfloat\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43morder_book\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mBid\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m:\u001b[49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[38;5;241;43m100.0\u001b[39;49m\n\u001b[0;32m      7\u001b[0m bid_price_2 \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;28mmap\u001b[39m(\u001b[38;5;28mfloat\u001b[39m,order_book[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBid\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;241m2\u001b[39m::\u001b[38;5;241m4\u001b[39m]))\u001b[38;5;241m/\u001b[39m\u001b[38;5;241m100.0\u001b[39m\n\u001b[0;32m      8\u001b[0m bid_price_3 \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;28mmap\u001b[39m(\u001b[38;5;28mfloat\u001b[39m,order_book[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBid\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;241m3\u001b[39m::\u001b[38;5;241m4\u001b[39m]))\u001b[38;5;241m/\u001b[39m\u001b[38;5;241m100.0\u001b[39m\n", "\u001b[1;31mTypeError\u001b[0m: unsupported operand type(s) for /: 'map' and 'float'"]}], "source": ["month = 1\n", "day = 2\n", "timestamp,order_book,bid_price_1,bid_price_2,bid_price_3,\\\n", "bid_quantity_1,bid_quantity_2,bid_quantity_3,\\\n", "ask_price_1,ask_price_2,ask_price_3,ask_quantity_1,\\\n", "ask_quantity_2,ask_quantity_3 = order_book(month,day)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["time_second,time_second_basic = time_transform(timestamp)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"collapsed": false, "scrolled": true}, "outputs": [], "source": ["traded_time = 300\n", "# 09:00 ~ 09:15\n", "time1 = 0\n", "time2 = 900\n", "traded_micsecond_1,index_1 = traded_label_micsecond(time1,time2,time_second_basic,\\\n", "                                                    bid_price_1,ask_price_1,traded_time)\n", "#len(np.where(np.array(traded_micsecond_1) == 0)[0]),len(np.where(np.array(traded_micsecond_1) == 1)[0])\n", "            \n", "# 09:15 ~ 11:30\n", "time1 = 900+1\n", "time2 = 9000\n", "traded_micsecond_2,index_2 = traded_label_micsecond(time1,time2,time_second_basic,\\\n", "                                                    bid_price_1,ask_price_1,traded_time)\n", "#len(np.where(np.array(traded_micsecond_2) == 0)[0]),len(np.where(np.array(traded_micsecond_2) == 1)[0])\n", "            \n", "# 11:30 ~ 1:00\n", "time1 = 9000+1\n", "time2 = 14400\n", "traded_micsecond_3,index_3 = traded_label_micsecond(time1,time2,time_second_basic,\\\n", "                                                    bid_price_1,ask_price_1,traded_time)\n", "#len(np.where(np.array(traded_micsecond_3) == 0)[0]),len(np.where(np.array(traded_micsecond_3) == 1)[0])\n", "            \n", "# 01:00 ~ 16:00\n", "time1 = 14400+1\n", "time2 = 25200\n", "traded_micsecond_4,index_4 = traded_label_micsecond(time1,time2,time_second_basic,\\\n", "                                                    bid_price_1,ask_price_1,traded_time)\n", "#len(np.where(np.array(traded_micsecond_4) == 0)[0]),len(np.where(np.array(traded_micsecond_4) == 1)[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["index_1, index_2, index_3, index_4"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "scrolled": true}, "outputs": [], "source": ["index_1, index_2, index_3, index_4"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "scrolled": true}, "outputs": [], "source": ["import time \n", "start = time.time()\n", "\n", "before_time = 60.0 * 5 # second\n", "index = np.where(time_second_basic <= 0.0)[0][-1]   \n", "Ask1 = ask_price_1[np.where(time_second_basic <= 0.0)[0][-1]:]\n", "rise_ratio_ask = rise_ask(Ask1,time_second_basic,before_time)\n", "\n", "#Weight Depth\n", "w1,w2,w3 = [50.0,30.0,20.0]\n", "W_AB,W_A_B = weight_pecentage(w1,w2,w3)\n", "\n", "end = time.time()  \n", "print \"Total time = %f\"%(end - start)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "scrolled": true}, "outputs": [], "source": ["#Open 0 ~ 09:15\n", "time1 ,time2 = index_1\n", "plt.figure(figsize = (20,24))\n", "plt.subplot(511)\n", "plt.grid()\n", "plot(ask_price_1[time1:time2],label = 'Ask1',color = 'b')\n", "plot(bid_price_1[time1:time2],label = 'Bid1',color = 'r')\n", "plt.ylim(7060,7140)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(512)\n", "plt.grid()\n", "plot(traded_micsecond_1,label = 'Traded_Label',color = 'b')\n", "plt.ylim(-0.5,1.5)\n", "plt.xlabel(\"Second\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(513)\n", "plt.grid()\n", "plot(rise_ratio_ask[0:(time2-time1)],label = 'Traded_Label',color = 'b')\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"Rise Rotio\")\n", "\n", "plt.subplot(514)\n", "plt.grid()\n", "plot(W_AB[time1:time2],label = 'Ask/Bid',color = 'r')\n", "#plt.ylim(0,400)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"Ask/Bid\")\n", "\n", "plt.subplot(515)\n", "plt.grid()\n", "plot(W_A_B[time1:time2],label = '(Ask-Bid)/(Ask+Bid)',color = 'r')\n", "#plt.ylim(0,400)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"(Ask-Bid)/(Ask+Bid)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["#Open 09:15 ~ 11:30\n", "time01 ,time02 = index_1\n", "time1 ,time2 = index_2\n", "plt.figure(figsize = (20,16))\n", "plt.subplot(511)\n", "plt.grid()\n", "plot(ask_price_1[time1:time2],label = 'Ask1',color = 'b')\n", "plot(bid_price_1[time1:time2],label = 'Bid1',color = 'r')\n", "plt.ylim(6980,7110)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(512)\n", "plt.grid()\n", "plot(traded_micsecond_2,label = 'Traded_Label',color = 'b')\n", "plt.ylim(-0.5,1.5)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(513)\n", "plt.grid()\n", "plot(rise_ratio_ask[time02-time01:][0:(time2-time1)],label = 'Traded_Label',color = 'b')\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"Rise Rotio\")\n", "\n", "plt.subplot(514)\n", "plt.grid()\n", "plot(W_AB[time1:time2],label = 'Ask/Bid',color = 'r')\n", "#plt.ylim(0,400)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"Ask/Bid\")\n", "\n", "plt.subplot(515)\n", "plt.grid()\n", "plot(W_A_B[time1:time2],label = '(Ask-Bid)/(Ask+Bid)',color = 'r')\n", "#plt.ylim(0,400)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"(Ask-Bid)/(Ask+Bid)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "scrolled": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["#Open 11:30 ~ 13:00 => china close\n", "time01 ,time02 = index_2\n", "time1 ,time2 = index_3\n", "plt.figure(figsize = (20,16))\n", "plt.subplot(511)\n", "plt.grid()\n", "plot(ask_price_1[time1:time2],label = 'Ask1',color = 'b')\n", "plot(bid_price_1[time1:time2],label = 'Bid1',color = 'r')\n", "plt.ylim(7005,7035)\n", "plt.xlabel(\"Micsecond\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(512)\n", "plt.grid()\n", "plot(traded_micsecond_3,label = 'Traded_Label',color = 'b')\n", "plt.ylim(-0.5,1.5)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(513)\n", "plt.grid()\n", "plot(rise_ratio_ask[time02-time01:][0:(time2-time1)],label = 'Traded_Label',color = 'b')\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"Rise Rotio\")\n", "\n", "plt.subplot(514)\n", "plt.grid()\n", "plot(W_AB[time1:time2],label = 'Ask/Bid',color = 'r')\n", "plt.xlabel(\"Micsecond\")\n", "plt.ylabel(\"Ask/Bid\")\n", "\n", "plt.subplot(515)\n", "plt.grid()\n", "plot(W_A_B[time1:time2],label = '(Ask-Bid)/(Ask+Bid)',color = 'r')\n", "plt.xlabel(\"Second\")\n", "plt.ylabel(\"(Ask-Bid)/(Ask+Bid)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["#Open 13:00 ~ 16:00\n", "time01 ,time02 = index_3\n", "time1,time2 = index_4\n", "plt.figure(figsize = (20,16))\n", "plt.subplot(511)\n", "plt.grid()\n", "plot(ask_price_1[time1:time2],label = 'Ask1',color = 'b')\n", "plot(bid_price_1[time1:time2],label = 'Bid1',color = 'r')\n", "plt.ylim(7000,7050)\n", "plt.xlabel(\"Micsecond\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(512)\n", "plt.grid()\n", "plot(traded_micsecond_4,label = 'Traded_Label',color = 'b')\n", "plt.ylim(-0.5,1.5)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"A50 Price\")\n", "plt.legend(loc = 1)\n", "\n", "plt.subplot(513)\n", "plt.grid()\n", "plot(rise_ratio_ask[time02-time01:][0:(time2-time1)],label = 'Traded_Label',color = 'b')\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"Rise Rotio\")\n", "\n", "plt.subplot(514)\n", "plt.grid()\n", "plot(W_AB[time1:time2],label = 'Ask/Bid',color = 'r')\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"Ask/Bid\")\n", "\n", "plt.subplot(515)\n", "plt.grid()\n", "plot(W_A_B[time1:time2],label = '(Ask-Bid)/(Ask+Bid)',color = 'r')\n", "plt.ylim(-1.5,1.5)\n", "plt.xlabel(\"Microsecond\")\n", "plt.ylabel(\"(Ask-Bid)/(Ask+Bid)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 0}