"""
Time insensitive attribute implementation for orderbook dynamics.
These attributes don't depend on the time sequence of order book updates.
"""
from typing import Callable, TypeVar, Generic, Optional, List
import numpy as np
from ..models import Cell, OrderBook
from .basic_attribute import BasicAttribute, BasicSet


T = TypeVar('T')


class TimeInsensitiveAttribute:
    """
    Time insensitive attribute for order book analysis.
    Applies a function to an OrderBook to produce a Cell[T].
    """
    def __init__(self, func: Callable[[OrderBook], Cell]):
        """
        Initialize a time insensitive attribute.
        
        Args:
            func: Function taking an OrderBook and returning a Cell
        """
        self.func = func
        
    def __call__(self, order_book: OrderBook) -> Cell:
        """Apply the function to the order book."""
        return self.func(order_book)
    
    def map(self, func: Callable[[T], T]) -> 'TimeInsensitiveAttribute':
        """Apply a function to the result of this attribute."""
        original_func = self.func
        return TimeInsensitiveAttribute(lambda ob: original_func(ob).map(func))


class TimeInsensitiveSet:
    """
    Set of time insensitive order book attributes.
    """
    def __init__(self, basic_set: BasicSet = None, max_level: int = 5):
        """
        Initialize a time insensitive set.
        
        Args:
            basic_set: Basic set to use
            max_level: Maximum order book level to consider
        """
        self.basic_set = basic_set or BasicSet(max_level)
        self.max_level = max_level
        
    def price_spread(self) -> TimeInsensitiveAttribute:
        """
        Get the price spread (best ask - best bid).
        
        Returns:
            TimeInsensitiveAttribute for price spread
        """
        def calc_price_spread(order_book: OrderBook) -> Cell:
            return order_book.spread()
            
        return TimeInsensitiveAttribute(calc_price_spread)
        
    def mid_price(self) -> TimeInsensitiveAttribute:
        """
        Get the mid price.
        
        Returns:
            TimeInsensitiveAttribute for mid price
        """
        def calc_mid_price(order_book: OrderBook) -> Cell:
            return order_book.mid_price()
            
        return TimeInsensitiveAttribute(calc_mid_price)
        
    def ask_step(self, level: int) -> TimeInsensitiveAttribute:
        """
        Calculate the step between current and next ask price level.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            TimeInsensitiveAttribute for ask step
        """
        def calc_ask_step(order_book: OrderBook) -> Cell:
            current = self.basic_set.ask_price(level)(order_book)
            next_level = self.basic_set.ask_price(level + 1)(order_book)
            
            if current.is_missing or next_level.is_missing:
                return Cell.empty()
                
            return Cell.value(next_level.get() - current.get())
            
        return TimeInsensitiveAttribute(calc_ask_step)
        
    def bid_step(self, level: int) -> TimeInsensitiveAttribute:
        """
        Calculate the step between current and next bid price level.
        
        Args:
            level: Level (1-indexed)
            
        Returns:
            TimeInsensitiveAttribute for bid step
        """
        def calc_bid_step(order_book: OrderBook) -> Cell:
            current = self.basic_set.bid_price(level)(order_book)
            next_level = self.basic_set.bid_price(level + 1)(order_book)
            
            if current.is_missing or next_level.is_missing:
                return Cell.empty()
                
            return Cell.value(current.get() - next_level.get())
            
        return TimeInsensitiveAttribute(calc_bid_step)
        
    def mean_ask(self) -> TimeInsensitiveAttribute:
        """
        Calculate the mean of all ask prices.
        
        Returns:
            TimeInsensitiveAttribute for mean ask price
        """
        def calc_mean_ask(order_book: OrderBook) -> Cell:
            if not order_book.asks:
                return Cell.empty()
                
            prices = list(order_book.asks.keys())
            return Cell.value(np.mean(prices))
            
        return TimeInsensitiveAttribute(calc_mean_ask)
        
    def mean_bid(self) -> TimeInsensitiveAttribute:
        """
        Calculate the mean of all bid prices.
        
        Returns:
            TimeInsensitiveAttribute for mean bid price
        """
        def calc_mean_bid(order_book: OrderBook) -> Cell:
            if not order_book.bids:
                return Cell.empty()
                
            prices = list(order_book.bids.keys())
            return Cell.value(np.mean(prices))
            
        return TimeInsensitiveAttribute(calc_mean_bid)
        
    def mean_ask_volume(self) -> TimeInsensitiveAttribute:
        """
        Calculate the mean of all ask volumes.
        
        Returns:
            TimeInsensitiveAttribute for mean ask volume
        """
        def calc_mean_ask_volume(order_book: OrderBook) -> Cell:
            if not order_book.asks:
                return Cell.empty()
                
            volumes = list(order_book.asks.values())
            return Cell.value(np.mean(volumes))
            
        return TimeInsensitiveAttribute(calc_mean_ask_volume)
        
    def mean_bid_volume(self) -> TimeInsensitiveAttribute:
        """
        Calculate the mean of all bid volumes.
        
        Returns:
            TimeInsensitiveAttribute for mean bid volume
        """
        def calc_mean_bid_volume(order_book: OrderBook) -> Cell:
            if not order_book.bids:
                return Cell.empty()
                
            volumes = list(order_book.bids.values())
            return Cell.value(np.mean(volumes))
            
        return TimeInsensitiveAttribute(calc_mean_bid_volume)
        
    def volume_spread(self) -> TimeInsensitiveAttribute:
        """
        Calculate the difference between best ask and bid volumes.
        
        Returns:
            TimeInsensitiveAttribute for volume spread
        """
        def calc_volume_spread(order_book: OrderBook) -> Cell:
            best_ask = order_book.best_ask()
            best_bid = order_book.best_bid()
            
            if best_ask.is_missing or best_bid.is_missing:
                return Cell.empty()
                
            ask_volume = order_book.asks[best_ask.get()]
            bid_volume = order_book.bids[best_bid.get()]
            
            return Cell.value(ask_volume - bid_volume)
            
        return TimeInsensitiveAttribute(calc_volume_spread)
        
    def accumulated_price_spread(self) -> TimeInsensitiveAttribute:
        """
        Calculate the accumulated spread across all price levels.
        
        Returns:
            TimeInsensitiveAttribute for accumulated price spread
        """
        def calc_accumulated_price_spread(order_book: OrderBook) -> Cell:
            if not order_book.asks or not order_book.bids:
                return Cell.empty()
                
            ask_prices = sorted(order_book.asks.keys())
            bid_prices = sorted(order_book.bids.keys(), reverse=True)
            
            spreads = []
            for i in range(min(len(ask_prices), len(bid_prices))):
                spreads.append(ask_prices[i] - bid_prices[i])
                
            if not spreads:
                return Cell.empty()
                
            return Cell.value(np.sum(spreads))
            
        return TimeInsensitiveAttribute(calc_accumulated_price_spread)
        
    def accumulated_volume_spread(self) -> TimeInsensitiveAttribute:
        """
        Calculate the accumulated volume spread across all levels.
        
        Returns:
            TimeInsensitiveAttribute for accumulated volume spread
        """
        def calc_accumulated_volume_spread(order_book: OrderBook) -> Cell:
            if not order_book.asks or not order_book.bids:
                return Cell.empty()
                
            ask_prices = sorted(order_book.asks.keys())
            bid_prices = sorted(order_book.bids.keys(), reverse=True)
            
            spreads = []
            for i in range(min(len(ask_prices), len(bid_prices))):
                ask_volume = order_book.asks[ask_prices[i]]
                bid_volume = order_book.bids[bid_prices[i]]
                spreads.append(ask_volume - bid_volume)
                
            if not spreads:
                return Cell.empty()
                
            return Cell.value(np.sum(spreads))
            
        return TimeInsensitiveAttribute(calc_accumulated_volume_spread) 