import os
import re
import sys
import threading

import pandas as pd
import numpy as np
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS
from typing import List, Optional, Union
from matplotlib.dates import date2num

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

from db_solve import greeks
from db_solve.parreader import loadtrade
from db_solve.parreader import loadvol
from pnlback.volsym.svi_cs import fit_svi_model

tradetime00 = [['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']]


class DataManager:

    def __init__(self):
        super().__init__()
        self.voltime_data = None
        self.current_time = None
        self.selected_months = "所有月份"
        self.svis = None
        self.config = None
        self.data = None
        self.plot_data = None
        self.current_data = None
        self.last_current_data = None
        self.unique_times = None
        self.trade_data = None
        self._processed_data = None
        self.tradetime_num = None
        self.influx_manager = InfluxManager()

    def updateparams(self, custom_r, use_new_iv, config):
        """
        自定义参数
        Args:
            custom_r: 自定义r
            use_new_iv: 是否使用新iv
            config: 配置文件
        """
        self.custom_r = custom_r
        self.use_new_iv = use_new_iv
        self.config = config

    def get_frommd(self, path, live_mode=False):
        """
        获取波动率数据
        Args:
            path: 数据文件路径
            live_mode: 是否使用实时模式(从py_statistics1获取数据)
        Returns:
            处理后的波动率数据DataFrame
        """
        if live_mode:
            data = self.md_manager.get_options_by_key('underlyer', '510500')  # 获取实时波动率数据
            # 确保数据格式与loadvol输出一致
            # 转为DataFrame
            data = pd.DataFrame(data)
            # 行和列反了
            data = data.T
            if data.empty:
                return None
            else:
                # Update numeric conversion to handle errors explicitly
                for column in data.columns:
                    try:
                        data[column] = data[column].astype(float)
                    except (ValueError, TypeError):
                        continue
                data.rename(
                    columns={'optioncode': 'code', 'timestamp': 'time', 'expiry': 'exp', 'strike': 'K',
                             'spotprice': 'spot',
                             'callPut': 'call', 'z': 'Z', 'bidvol': 'bid_vol', 'askvol': 'ask_vol', 'fitvol': 'sigma'},
                    inplace=True)
                data['exp'] = data['exp'].astype(int)
                data['code'] = data['code'].astype(int)
                data['vol_spread'] = data['ask_vol'] - data['bid_vol']
                data['bid'] = data['tv'] - data['vol_spread'] / 2 * data['vega'] * 100
                data['ask'] = data['tv'] + data['vol_spread'] / 2 * data['vega'] * 100
                data['rf'] = 0
                endtime = pd.to_datetime(data['exp'].astype(str) + ' 15:00:00')
                data['time'] = pd.to_datetime(data['time'].iloc[0])  # Convert time column to datetime
                data['time2expiry'] = (endtime - data[
                    'time']).dt.total_seconds() / 365 / 24 / 3600  # 年化,data['exp']是str，日期，转换为当日15点
        else:
            data = loadvol(path, [], tradetime00, all=True)
            data['forward'] = data['spot'] * np.exp((data['rf'] - data['br']) * data['time2expiry'])
        data['posi'] = 0
        data['neg_posi'] = 0
        return data.reset_index()

    def combine_delta_data(self, data):
        # 分离正delta和负delta的数据
        positive_delta = data[data['delta'] > 0].copy()
        negative_delta = data[data['delta'] < 0].copy()

        # 重命名负delta的列
        negative_delta = negative_delta.rename(columns={
            'code': 'neg_code',
            'tv': 'neg_tv',
            'bid': 'neg_bid',
            'ask': 'neg_ask',
            'bid_vol': 'neg_bid_vol',
            'ask_vol': 'neg_ask_vol',
            'sigma': 'neg_sigma',
            'delta': 'neg_delta'
        })

        # 合并数据
        combined_data = pd.merge(
            positive_delta,
            negative_delta[
                ['time', 'exp', 'K', 'neg_code', 'neg_tv', 'neg_bid', 'neg_ask', 'neg_bid_vol', 'neg_ask_vol',
                 'neg_delta']],
            on=['time', 'exp', 'K'],
            how='left'
        )
        # 修改 forward 计算，使用 PCP (Put-Call Parity) 公式
        # C - P = F - K * e^(-r*T)
        # 因此 F = C - P + K * e^(-r*T)
        combined_data['forward'] = (combined_data['tv'] - combined_data['neg_tv'] +
                                    combined_data['K'] * np.exp(-combined_data['rf'] * combined_data['time2expiry']))
        # combined_data = combined_data.drop(columns=['rf', 'br'])
        return combined_data

    def load_and_process_data(self, file_path, live_mode=False):
        """加载并处理数据"""
        data = self.get_frommd(None if live_mode else file_path, live_mode=live_mode)
        if data is not None and not data.empty:
            self.data = self.combine_delta_data(data.reset_index())
            if self.data is not None:
                self.filter_data_by_months()
                self.unique_times = sorted(self.data['time'].unique())
                self.current_time = self.unique_times[0]
                self.cal_current_data()
                return True
        return False

    def load_trade_data(self, file_path):
        """加载交易数据"""
        date_match = re.search(r'\d{8}', file_path)
        date = date_match.group()
        file_path = file_path.replace(date, "%s")
        self.trade_data = loadtrade(date, file_path, tradetime00)
        if self.trade_data is not None:
            self.trade_data = self.trade_data.reset_index()
            self._process_trade_data()
            return True
        return False

    def _process_trade_data(self):
        """处理交易数据"""
        if self.trade_data is not None:
            self._processed_data = self.trade_data.copy()
            # 添加归一化列
            for pnl_type in ['TradePNL', 'TDeltaPNL', 'TVegaPNL']:
                if f'{pnl_type}_normalized' not in self._processed_data.columns:
                    self._processed_data[f'{pnl_type}_normalized'] = self._processed_data[pnl_type] / \
                                                                     self._processed_data['TEdgePNL'].where(
                                                                         self._processed_data['TEdgePNL'] != 0, 1)

            # 设置spot轴的范围，使用0.05-0.95分位数
            spot_min = self._processed_data['Spot'].quantile(0.01)
            spot_max = self._processed_data['Spot'].quantile(0.99)
            self.spot_min = spot_min
            self.spot_max = spot_max

            # 确保 tradetime 列是 datetime 类型
            if not pd.api.types.is_datetime64_any_dtype(self._processed_data['tradetime']):
                self._processed_data['tradetime'] = pd.to_datetime(self._processed_data['tradetime'])

            # 转换时间为数值形式
            self.tradetime_num = date2num(self._processed_data['tradetime'].values)

    def cal_current_data(self):
        """获取当前时间点的数据"""
        if self.plot_data is not None:
            # 使用 .copy() 创建副本
            self.current_data = self.plot_data[self.plot_data['time'] == self.current_time].copy()
            # 获取当前时间点的数据

            if self.current_data.empty:
                print(f"时间点 {self.current_time} 没有数据")
                return

            if self.trade_data is not None:
                """更新持仓信息"""
                current_trades = self.trade_data[self.trade_data['tradetime'] <= self.current_time]
                positions = current_trades.groupby(current_trades['Code'].astype(float))['数量'].sum()

                # 使用 .loc 进行赋值，确保在原始 DataFrame 上操作
                self.current_data['posi'] = self.current_data['code'].map(positions).fillna(0)
                self.current_data['neg_posi'] = self.current_data['neg_code'].map(positions).fillna(0)
                self.current_data['all_posi'] = self.current_data['posi'] + self.current_data['neg_posi']

            # 计算无风险利率
            r = self.custom_r if self.custom_r is not None else self.current_data['rf'].iloc[0]
            if self.use_new_iv:
                for option_type in ['', 'neg_']:
                    for price_type in ['bid', 'ask']:
                        col_name = f'{option_type}{price_type}_vol'
                        # opiongreeks = greeks.OptionGreeks(self.current_data['forward'].values, self.current_data['K'].values,
                        #                                 self.current_data['time2expiry'].values, r,
                        #                                 self.current_data[f'{option_type}{price_type}'].values,
                        #                                 self.current_data['sigma'].values,
                        #                                 'call' if option_type == '' else 'put')
                self.current_data.loc[:, col_name] = greeks.implied_volatility(self.current_data['forward'].values,
                                                                               self.current_data['K'].values,
                                                                               self.current_data['time2expiry'].values,
                                                                               r,
                                                                               self.current_data[
                                                                                   f'{option_type}{price_type}'].values,
                                                                               'call' if option_type == '' else 'put')

            # 对数据进行排序
            self.current_data = self.current_data.sort_values('delta')
            if self.last_current_data is None:
                self.last_current_data = self.current_data
                self.svis = {}

            if self.config['svi_enabled']:
                # 使用 .loc 进行赋值操作
                self.current_data.loc[:, 'call_spread'] = self.current_data['ask_vol'] - self.current_data['bid_vol']
                self.current_data.loc[:, 'put_spread'] = self.current_data['neg_ask_vol'] - self.current_data[
                    'neg_bid_vol']

                # 计算call和put的中间价
                self.current_data.loc[:, 'call_mid'] = (self.current_data['bid_vol'] + self.current_data['ask_vol']) / 2
                self.current_data.loc[:, 'put_mid'] = (self.current_data['neg_bid_vol'] + self.current_data[
                    'neg_ask_vol']) / 2

                # 根据价差选择使用call还是put的中间价
                self.current_data.loc[:, 'market_sigma'] = np.where(
                    self.current_data['call_spread'] <= self.current_data['put_spread'],
                    self.current_data['call_mid'],  # 使用call中间价
                    self.current_data['put_mid']  # 使用put中间价
                )

                # 正确初始化 DataFrame
                if self.voltime_data is None:
                    self.voltime_data = pd.DataFrame()

                for i, exp in enumerate(sorted(self.current_data['exp'].unique())):
                    exp_data = self.current_data.loc[self.current_data['exp'] == exp].copy()
                    last_exp_data = (self.last_current_data.loc[self.last_current_data['exp'] == exp].copy()
                                     if self.last_current_data is not None else None)

                    self.svis[exp] = fit_svi_model(self.config, exp_data, exp, last_exp_data)

                    if exp in self.voltime_data.index:
                        self.voltime_data.loc[exp] = self.svis[exp][1]
                    else:
                        self.voltime_data = pd.concat([self.voltime_data, pd.DataFrame([self.svis[exp][1]], index=[exp])])
                    self.voltime_data.loc[exp, 'exp'] = exp
                    
                    self.current_data.loc[:, 'model_vol'] = self.svis[exp][0]
                    mask = self.current_data['exp'] == exp
                    if exp in self.svis and self.svis[exp][0] is not None:
                        self.current_data.loc[mask, 'vol_diff'] = (self.svis[exp][0] - self.current_data.loc[mask, 'market_sigma'])
                        self.current_data.loc[mask, 'vol_diff2'] = (self.svis[exp][0]
                                                                - self.current_data.loc[mask, 'sigma'])
                    else:
                        # 当SVI拟合失败时，将vol_diff设置为0或者NaN
                        self.current_data.loc[mask, 'vol_diff'] = 100  # 或者用0: = 0


        return None

    def filter_data_by_months(self):
        """按月份筛选数据"""
        if self.data is None:
            return False

        if not self.selected_months or "所有月份" in self.selected_months:
            self.plot_data = self.data.copy()
        else:
            self.plot_data = self.data[self.data['exp'].astype(str).isin(self.selected_months)].copy()
        return True

    def get_filtered_trade_data(self, current_time, show_recent=False):
        """获取过滤后的交易数据"""
        if not hasattr(self, '_processed_data') or self._processed_data is None:
            return None

        if show_recent:
            mask = (self._processed_data['tradetime'] >=
                    pd.to_datetime(current_time) - pd.Timedelta(minutes=10)) & \
                   (self._processed_data['tradetime'] <=
                    pd.to_datetime(current_time) + pd.Timedelta(minutes=10))
            return self._processed_data[mask]
        return self._processed_data

    def get_all_months(self):
        """获取所有可用的月份"""
        if self.data is not None:
            return sorted(self.data['exp'].unique())
        return []

    def get_all_codes(self):
        """获取所有代码"""
        if self.data is not None:
            return sorted(self.data['code'].unique())
        return []

    def save_to_influxdb(self, data, measurement, taglist=[], fieldlist=[]):
        """Save current data to InfluxDB asynchronously"""
        self.influx_manager.save_data(data, measurement, taglist, fieldlist)

    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'influx_manager'):
            self.influx_manager.close()


class InfluxManager:
    """InfluxDB data management class"""
    
    def __init__(self, url: str = "http://localhost:8086",
                 token: str = "yxLfIyxDcYoxM-sVZLuYZF9rzuG2J6H0phfRTujeEpvTWKpGleG7wyAuhR4GmOI1rIBERuy7EMKgOcKCdAYcog==",
                 org: str = "nino",
                 bucket: str = "VOLS"):
        """Initialize InfluxDB manager"""
        self.url = url
        self.token = token
        self.org = org
        self.bucket = bucket
        self._active_threads = []
        
        # Initialize InfluxDB client
        self.client = InfluxDBClient(url=self.url, token=self.token, org=self.org)
        self.write_api = self.client.write_api(write_options=SYNCHRONOUS)

    def save_data(self, 
                  data: pd.DataFrame, 
                  measurement: str, 
                  taglist: List[str] = [],
                  fieldlist: List[str] = [],
                  bucket: Optional[str] = None) -> None:
        """
        Asynchronously save DataFrame to InfluxDB
        
        Args:
            data: DataFrame to save
            measurement: Measurement name
            taglist: List of columns to use as tags
            fieldlist: List of columns to use as fields
            bucket: Optional bucket override
        """
        if data is None or data.empty:
            return
            
        def save_data_thread():
            try:
                # Pre-process column types
                time_col = 'time'
                if fieldlist:
                    numeric_cols = fieldlist
                else:
                    numeric_cols = data.select_dtypes(include=['int64', 'float64']).columns

                # Batch create points
                points = []
                for _, row in data.iterrows():
                    point = Point(measurement)
                    
                    # Handle timestamp - 确保时间戳存在且有效
                    # if time_col not in row:
                    #     print(f"Warning: No timestamp found in row: {row}")
                    #     continue
                        
                    # timestamp = row[time_col]
                    # if isinstance(timestamp, str):
                    #     timestamp = pd.to_datetime(timestamp)
                    # if not isinstance(timestamp, pd.Timestamp):
                    #     print(f"Warning: Invalid timestamp format: {timestamp}")
                    #     continue
                        
                    # point = point.time(timestamp.to_pydatetime())
                    # Process tags
                    for tag in taglist:
                        if not pd.isna(row[tag]):
                            point = point.tag(tag, str(row[tag]))
                    
                    # Process numeric fields
                    for col in numeric_cols:
                        if not pd.isna(row[col]) and col not in taglist and col != time_col:
                            point = point.field(col, float(row[col]))

                    points.append(point)
                
                # Batch write using existing write_api
                target_bucket = bucket or self.bucket
                self.write_api.write(bucket=target_bucket, org=self.org, record=[p for p in points])
                print(f"[{pd.Timestamp.now()}] Successfully saved {len(points)} points to "
                      f"InfluxDB measurement '{measurement}' in bucket '{target_bucket}' with tags {taglist}")
                    
            except Exception as e:
                print(f"Error saving data to InfluxDB: {e}")
                print(f"Error details: {str(e.__class__.__name__)}")
            finally:
                # Clean up completed thread reference
                if threading.current_thread() in self._active_threads:
                    self._active_threads.remove(threading.current_thread())
        
        # Create and start new thread
        save_thread = threading.Thread(target=save_data_thread)
        save_thread.daemon = True
        self._active_threads.append(save_thread)
        save_thread.start()

    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for all active save operations to complete
        
        Args:
            timeout: Maximum time to wait in seconds (None for no timeout)
            
        Returns:
            bool: True if all operations completed, False if timeout occurred
        """
        for thread in self._active_threads[:]:  # Copy list to avoid modification during iteration
            thread.join(timeout=timeout)
            if thread.is_alive():
                return False
        return True

    def close(self):
        """Close InfluxDB connection and cleanup resources"""
        try:
            self.wait_for_completion()  # 等待所有写入操作完成
            if self.client:
                self.client.close()
                self.client = None
                self.write_api = None
        except Exception as e:
            print(f"Error closing InfluxDB connection: {e}")

    def verify_write(self, measurement: str, start_time: str = "-1h"):
        """验证数据是否成功写入"""
        try:
            query = f'from(bucket:"{self.bucket}") |> range(start: {start_time})'
            result = self.client.query_api().query(query, org=self.org)
            print(f"Query result: {result}")
            return len(result) > 0
        except Exception as e:
            print(f"Error verifying data: {e}")
            return False