"""
因子计算模块

描述：
    该模块包含一系列用于计算市场因子的函数。

作者: [cyr]
日期: [250117]
版本: 1.0

依赖库：
    - pandas
    - numpy
    - utils (自定义模块，包含计算用到的函数)

使用方法：
    1. 导入该模块。
    2. 调用相应的因子计算函数，传入包含市场数据的 DataFrame。
    3. 获取计算后的因子值。

"""

import pandas as pd
import numpy as np
from utils import calculate_ema

# 订单簿信息
def order_imb(df, level=None):
    if level is None:
        level = '1'
    bidvol = 'BidVol' + level
    askvol = 'AskVol' + level
    return (df[bidvol] - df[askvol])/(df[bidvol] + df[askvol])

def order_price_mid(df, level=None):
    if level is None:
        level = '1'
    bidvol = 'BidVol' + level
    askvol = 'AskVol' + level
    bidprice = 'BidPrice' + level
    askprice = 'AskPrice' + level
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    return (df[bidvol]*df[askprice] + df[askvol]*df[bidprice]) / (df[bidvol] + df[askvol]) - mid

def nettradeprice_mid(df, contract_mul=200):
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    net_tradeprice = trade_value / (trade_vol*contract_mul)
    net_tradeprice = np.where(pd.isna(net_tradeprice) | net_tradeprice ==0, mid, net_tradeprice)
    
    return net_tradeprice - mid

def nettradeprice_vwap(df, level=None, contract_mul=200):
    if level is None:
        level = '1'
    bidvol = 'BidVol' + level
    askvol = 'AskVol' + level
    bidprice = 'BidPrice' + level
    askprice = 'AskPrice' + level

    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    net_tradeprice = trade_value / (trade_vol*contract_mul)
    net_tradeprice = np.where(pd.isna(net_tradeprice) | net_tradeprice ==0, mid, net_tradeprice)
    
    return net_tradeprice - (df[bidvol]*df[askprice] + df[askvol]*df[bidprice]) / (df[bidvol] + df[askvol])


def order_imb_diff(df, window=None, level=None):
    if window is None:
        window=1
    if level is None:
        level='1'
    return order_imb(df=df, level=level).diff(window)

def ob_depth(df):
    return np.log((df['AskPrice5'] - df['AskPrice1'])/(df['BidPrice1'] - df['BidPrice5']))

def trade_impact(df, window, contract_mul):
    if window is None:
        window = 10

    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    net_tradeprice = trade_value / (trade_vol*contract_mul)

    # 如果nettradeprice>mid 说明买方主导，价格被买方推动
    trade_price = np.where(net_tradeprice>mid, df['BidPrice1'] , -df['AskPrice1'])
    ema_vol = calculate_ema(trade_vol, window)
    return ema_vol/trade_price

def trade_flow(df,  contract_mul, window=None):
    if window is None:
        window = 10
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    net_tradeprice = trade_value / (trade_vol*contract_mul)

    # 如果nettradeprice>mid 说明买方主导，价格被买方推动
    trade_vol_with_dir = np.where(net_tradeprice>mid, trade_vol , -trade_vol)
    return calculate_ema(trade_vol_with_dir, window)

def order_flow_imb(df, level=None,  window=None):
    if window is None:
        window = 1
    if level is None:
        level = '1'
    
    bidvol = 'BidVol' + level
    askvol = 'AskVol' + level
    bidprice = 'BidPrice' + level
    askprice = 'AskPrice' + level
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
   
    ofi_bid = np.where(
    df[bidprice] > df[bidprice].shift(1),  
    df[bidvol],  
    np.where(
        df[bidprice] == df[bidprice].shift(1),  
        df[bidvol].diff(),  
        -df[bidvol]  
    ))

    ofi_ask = np.where(
    df[askprice] < df[askprice].shift(1),  
    df[askvol],  
    np.where(
        df[askprice] == df[askprice].shift(1),  
        df[askvol].diff(),  
        -df[askvol]  
    ))

    return pd.Series( ofi_bid - ofi_ask).rolling(window=window, min_periods=1).sum()


# 动量反转
def mom_last(df, window=None):
    if window is None:
        window = 1

    # 获取 shift_price
    shift_price = df['LastPrice'].shift(window)
    # 检查 shift_price 是否为 NaN 或小于等于零，替换为一个小正数（如 1e-10）
    shift_price = np.where(pd.isna(shift_price) | (shift_price <= 0), 1e-10, shift_price)
    # 计算价格比率
    price_ratio = df['LastPrice'] / shift_price
    # 检查 price_ratio 是否为 NaN 或小于等于零，替换为一个小正数（如 1e-10）
    price_ratio = np.where(pd.isna(price_ratio) | (price_ratio <= 0), np.nan, price_ratio)
    # 计算对数收益率
    result = np.where(pd.isna(price_ratio), 0, np.log(price_ratio))
    return result
    # shift_price = df['LastPrice'].shift(window)
    # return np.where(pd.isna(shift_price)| shift_price<=0, 0, np.log(df['LastPrice'] / shift_price))

def reversal_last(df, window=None):
    if window is None:
        window = 10
    ma = df['LastPrice'].rolling(window=window).mean()
    std = df['LastPrice'].rolling(window=window).std()
    
    return np.nan_to_num((df['LastPrice'] - ma)/std, posinf=0, neginf=0)

def mom_mid(df, window=None):
    if window is None:
        window = 1
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    shift_price = mid.shift(window)
    return np.where(pd.isna(shift_price), 0, np.log(mid / shift_price))

def reversal_mid(df, window=None):
    if window is None:
        window = 10
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    ma = mid.rolling(window=window).mean()
    std = mid.rolling(window=window).std()
    
    return np.nan_to_num((mid - ma)/std, posinf=0, neginf=0)

       
# 高频偏度峰度
def ret_var(df, window=None):
    if window is None:
        window = 10
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_pct = mid.pct_change()
    mid_pct2 = mid_pct**2

    return mid_pct2.rolling(window=window,min_periods=1).sum()

def ret_skew(df, window=None):
    if window is None:
        window = 240
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_pct = mid.pct_change()
    mid_var = (mid_pct**2).rolling(window=window,min_periods=1).sum()
    mid_3 = (mid_pct**3).rolling(window=window,min_periods=1).sum()*np.sqrt(window)

    return  mid_3/np.sqrt(mid_var**3)

def ret_kurtosis(df, window=None):
    if window is None:
        window = 60
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_pct = mid.pct_change()
    mid_var = (mid_pct**2).rolling(window=window,min_periods=1).sum()
    mid_4 = (mid_pct*4).rolling(window=window,min_periods=1).sum()*(window)

    return  mid_4/mid_var**2


# 高频方差等标准计算方式
def ret_var_std(df, window_var=None, window_mean=None):
    if window_var is None:
        window_var = 20
    if window_mean is None:
        window_mean = 1
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_pct = mid.pct_change()
    mid_pct2 = mid_pct**2

    pct_std = mid_pct.rolling(window=window_var, min_periods=1).std()
    return pct_std.rolling(window=window_mean,min_periods=1).mean()

def ret_skew_std(df, window_skew=None, window_mean=None):
    if window_skew is None:
        window_skew = 20
    if window_mean is None:
        window_mean = 20
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_pct = mid.pct_change()
    pct_skew = mid_pct.rolling(window=window_skew, min_periods=1).skew()

    return pct_skew.rolling(window=window_mean,min_periods=1).mean()

# 高频上下行波动占比
def up_pct(df, window=None):
    if window is None:
        window = 240
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_pct = mid.pct_change()
    is_up = np.where(mid_pct>0, 1, 0)
    mid_up_pct = is_up*mid_pct
    return (mid_up_pct**2).rolling(window=window, min_periods=1).sum()/(mid_pct**2).rolling(window=window, min_periods=1).sum()

def down_pct(df, window=None):
    if window is None:
        window = 240
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_pct = mid.pct_change()
    is_down = np.where(mid_pct>0, 0, 1)
    mid_down_pct = is_down*mid_pct
    return (mid_down_pct**2).rolling(window=window, min_periods=1).sum()/(mid_pct**2).rolling(window=window, min_periods=1).sum()


# 成交量分布因子
def volume_ratio(df, window=None):
    pass

def volumn_corr(df, window=None):
    if window is None:
        window=60
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1'])/2

    return mid.rolling(window=window, min_periods=1).corr(trade_vol)

def volumn_corr_last(df,window=None):   
    if window is None:
        window = 60
    trade_vol = df['Volume'].diff()
    return df['LastPrice'].rolling(window=window, min_periods=1).corr(trade_vol)

# 趋势强度
def trend_strength(df, window=None):
    if window is None:
        window=240
    mid = (df['AskPrice1'] + df['BidPrice1'])/2
    mid_abs_diff = np.abs(mid.diff())

    return (mid - mid.shift(window))/mid_abs_diff.rolling(window=window, min_periods=1).sum()

# alpha101: alpha_49
def alpha_49_enhanced(df, window_long=20, window_short=10, threshold_scale=0.5):
    # 计算价格变化加速度（标准化为收益率）
    price = df['LastPrice']
    ret_long = (price.shift(window_short) - price.shift(window_long)) / price.shift(window_long)  # 长期收益率
    ret_short = (price - price.shift(window_short)) / price.shift(window_short)                 # 短期收益率
    part1 = (ret_long - ret_short)
    
    # 动态阈值（基于滚动波动率）
    threshold = -threshold_scale * part1.rolling(60).std()
    
    # 因子赋值
    condition = (part1 < threshold)
    factor = np.where(condition, 1, -1 * price.pct_change(1))
    return factor

def alpha_101_enhanced(df, window=20):
    # 计算波动率调整后的价格变化
    price_diff = df['LastPrice'].diff(1)
    daily_range = df['High'] - df['Low']
    # 波动率平滑（滚动均值）
    range_smoothed = daily_range.rolling(window, min_periods=1).mean() + 1e-6
    # 标准化因子
    factor = price_diff / range_smoothed
    return factor