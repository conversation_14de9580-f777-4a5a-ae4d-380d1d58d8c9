#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
__title__ = '将python项目转换为exe文件'
__author__ = 'LINING'
"""
from PyInstaller.__main__ import run

# import sys
# reload(sys)
# sys.setdefaultencoding('GBK')

if __name__ == '__main__':
    opts = ['all.py', '-F']
    # opts = ['TargetOpinionMain.py', '-F', '-w']
    # opts = ['TargetOpinionMain.py', '-F', '-w', '--icon=TargetOpinionMain.ico','--upx-dir','upx391w']
    run(opts)
