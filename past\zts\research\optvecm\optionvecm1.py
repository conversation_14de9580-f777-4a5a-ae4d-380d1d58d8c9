# 模型相关包
import pyodbc

from datetime import datetime, timedelta

import statsmodels.api as sm
import statsmodels.stats.diagnostic
from statsmodels.tsa.api import VAR, DynamicVAR
from statsmodels.tsa.vector_ar.vecm import coint_joh<PERSON><PERSON>, VECM

from scipy.linalg import solve

from WindPy import w

# 画图包
import matplotlib.pyplot as plt
# 其他包
import pandas as pd
import numpy as np
import matplotlib as mpl

import sys
import os

sys.path.append(os.path.dirname(os.path.realpath(__file__)))
from research.optvecm import greeks, optionvecm

from sqlalchemy import create_engine


if __name__ == "__main__":
    timeS = 30  # 样本区间长度
    optnum = 1  # 期权数量

    timeRange = 4 * 60 / timeS
    allnum = optnum * 4 + 9

    # 结果储存数据库
    server2 = '***********'
    user2 = 'lining'
    password2 = 'lining'
    database2 = 'NINO'
    table = 'LeaderO{}T{}'.format(optnum, timeS)  # 存储结果

    # 数据库中最大日期
    conn2 = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server2, DATABASE=database2, PWD=password2, UID=user2)
    cursor = conn2.cursor()
    sql1 = u"SELECT max([index]) FROM [NINO].[dbo].[%s]" % table
    try:
        cursor.execute(sql1)
        dbdate = cursor.fetchone()[0]
        date1 = dbdate + timedelta(days=1)
        date2 = datetime.now()
        # date2 = u"2019-04-18"
    except:  # 新建数据库
        date1 = u"2019-04-19"
        date2 = u"2019-04-19"  # 包括第一天，包括最后一天

    conn2.commit()
    conn2.close()

    w.start()
    datetrades = w.tdays(date1, date2, "").Data[0]
    trade_hiscode = w.wsd("IH.CFE", "trade_hiscode", date1, date2, "").Data[0]

    # tick行情
    server = '10.36.18.54'
    user = 'mktdtr'
    password = '123678'
    database = 'MktDataHis'
    conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE=database, PWD=password, UID=user)

    result2 = []
    times2 = []
    # 每日提取一次数据
    futcodes = w.wset("futurecc", "startdate=%s;enddate=%s;wind_code=IH.CFE" % (date1, date2)).Data[2]
    lasttrade = w.wss(futcodes, "lasttrade_date").Data[0]
    for i in range(0, len(datetrades)):
        # 期货最后交易日转为次月合约
        lastdayt = trade_hiscode[0]
        futnum = 0
        for ii in range(0, len(futcodes)):
            if trade_hiscode[i] == futcodes[ii]:
                lastdayt = lasttrade[ii]
                futnum = ii
                break
        if datetrades[i] == lastdayt:
            forward1 = futcodes[futnum + 1]
        else:
            forward1 = trade_hiscode[i]
        # 计算贡献度
        print(str(datetrades[i]) + ' begin')
        optionvecm.mainByDay(forward1[:6], datetrades[i], datetrades[i], datetrades[i] + timedelta(days=1), result2,
                             times2, conn, optnum, timeRange)

    conn.commit()
    conn.close()

    pand = pd.DataFrame(result2, index=times2, columns=range(0, allnum))

    engine = create_engine('mssql+pyodbc://%s:%s@%s:1433/%s?driver=SQL+Server' % (user2, password2, server2, database2))

    pand.to_sql(name=table, con=engine, if_exists='append', index=True)  # replace/append

    print('all done')
