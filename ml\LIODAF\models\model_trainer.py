"""
模型训练模块
@author: lining
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import RobustScaler
import warnings
from models.set_model import set_model
from utils.utils import log_print
from core import config
from models.model_evaluate import model_evaluate
from models.paras_search import parameter_search, bayesian_optimization_search
import pickle
# import torch
# from autogluon.tabular import TabularPredictor
from datetime import datetime
import os

class OrderBookModelTrainer:
    """订单簿模型训练器"""

    def __init__(self, feature_cols=None, target_cols=None, model_type=None):
        """
        初始化模型训练器

        参数:
            data (pandas.DataFrame): 特征和标签数据
            feature_cols (list): 特征列名列表
            target_cols (list): 目标列名列表
        """
        self.feature_cols = feature_cols
        self.target_cols = target_cols
        self.model_type = model_type
        self.models = {}
        self.feature_importances = {}
        self.predictions = {}
        self.output_dir = config.OUTDIR + "/models/"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        self.model_path = config.MODEL_PATH
        # 忽略警告
        warnings.filterwarnings("ignore")

    def train_models(
        self,
        x_train,
        y_train_dict,
        params=None,
        is_model_parameter_search=False,
        cv_n_splits=3,
    ):
        """
        训练模型

        参数:
            model_type (str): 模型类型，可选值为'xgboost', 'random_forest', 'gbdt', 'linear'
            params (dict): 模型参数
            model_parameter_search (bool): 是否进行参数搜索
            cv_n_splits (int): 交叉验证折数

        返回:
            models (dict): 训练好的模型字典
        """
        # 训练模型
        for target in self.target_cols:
            model = set_model(model_type=self.model_type, params=params)
            if self.model_path is not None:
                log_print(f"加载模型 {target} 从 {self.model_path}")
                model = self.load_model(model,self.model_path, target)
            else:
                log_print(f"训练目标 {target} 的{self.model_type}模型...")
                if is_model_parameter_search:
                    search_result = parameter_search(
                        x_train[target],
                        y_train_dict[target],
                        model=model,
                        param_grid=config.MODEL_SETTING[self.model_type]["param_grid"],
                        method=config.MODEL_PARAMETER_SEARCH_METHOD,
                    )
                    model = search_result['best_model']
                else:
                    # 训练模型
                    model.fit(x_train[target], y_train_dict[target])

                # 保存模型
                self.dump_model(model, target)

            self.models[target] = model

    def dump_model(self, model, target):
        """
        根据模型类型保存模型

        参数:
            model: 训练好的模型
            target: 目标列名
        """
        date = datetime.now().strftime("%Y%m%d_%H")
        model_save_path = f"{self.output_dir}model__{config.CODE_LIST[0]}_{date}"
        if not os.path.exists(os.path.dirname(model_save_path)):
            os.makedirs(os.path.dirname(model_save_path))
        try:
            if self.model_type == "xgboost":
                # XGBoost模型使用save_model方法
                model.save_model(model_save_path+".json")
            elif self.model_type == "lstm":
                # LSTM模型保存PyTorch状态字典
                torch.save(
                    model.model.state_dict(),
                    model_save_path+".pth",
                )
            elif self.model_type == "autogluon":
                model.save(model_save_path+".pkl")
            else:
                # 其他模型使用pickle序列化
                pickle.dump(
                    model, open(model_save_path+".pkl", "wb")
                )

            log_print(f"模型 {target} 已保存到 {model_save_path}")
        except Exception as e:
            log_print(f"保存模型 {target} 时出错: {str(e)}", "error")

    def load_model(self, model,model_path, target):
        """
        加载模型
        """
        try:
            if self.model_type == "xgboost":
                model.load_model(model_path)
            elif self.model_type == "autogluon":
                model = TabularPredictor.load(model_path)
            else:
                model = pickle.load(open(model_path, "rb"))
            return model
        except Exception as e:
            log_print(f"加载模型 {target} 时出错: {str(e)}", "error")
            return None

    def predict(self, X: pd.DataFrame, target=None):
        """
        使用训练好的模型进行预测

        参数:
            X (pandas.DataFrame): 特征数据
            target (str): 目标列名，如果为None则预测所有目标

        返回:
            pandas.DataFrame: 预测结果
        """
        if not self.models:
            log_print("警告：没有训练好的模型", "warning")
            return None

        if target is not None and target not in self.models:
            log_print(f"警告：目标 {target} 的模型未训练", "warning")
            return None

        targets = [target] if target is not None else self.target_cols
        results = {}

        for t in targets:
            # 检查特征列
            missing_features = [
                col for col in self.feature_cols if col not in X.columns
            ]
            if missing_features:
                log_print(
                    f"警告：输入数据缺少以下特征列: {missing_features}", "warning"
                )
                raise ValueError(f"输入数据缺少以下特征列: {missing_features}")

            # 提取特征
            X_pred = X[self.feature_cols].copy()

            X_pred = X_pred.fillna(0)

            # 预测
            model_prediction = self.models[t].predict(X_pred)

            if len(model_prediction) != len(X_pred):
                # 如果长度不匹配，填充
                model_prediction = np.pad(
                    model_prediction,
                    (0, len(X_pred) - len(model_prediction)),
                    mode="constant",
                    constant_values=0,
                )

            results[f"{t}_pred"] = model_prediction

        return pd.DataFrame(results, index=X.index)

    def model_selection(self, x_train, y):
        """
        模型选择
        """
        # 尝试不同模型类型并记录性能
        best_model = None
        best_performance = float("-inf")
        model_performances = {}

        for model_type in [
            "xgboost",
            "linear",
            "decision_tree",
            "extra_trees",
        ]:
            self.train_models(
                x_train,
                y,
                model_type=model_type,
                params=config.MODEL_SETTING[model_type]["params"],
                model_parameter_search=config.MODEL_PARAMETER_SEARCH,
                cv_n_splits=config.CV_N_SPLITS,
            )
            pred = self.predict(x_train[self.target_cols[0]])

            train_eval = model_evaluate(
                y[self.target_cols[0]], pred[self.target_cols[0] + "_pred"], "train"
            )

            # 使用R2分数作为评判标准
            current_score = train_eval["r2"]
            if current_score > best_performance:
                best_performance = current_score
                best_model = model_type

            log_print(f"模型 {model_type} 的性能: {current_score:.4f}")

            model_performances[model_type] = train_eval

        log_print(f"最佳模型: {best_model}, R2分数: {round(best_performance, 4)}")

        return model_performances, best_model
