import datetime

DEFAULT_CONFIG = {
    'under': 'SH300',
    'under_options': ['SH500', 'SH300', 'SZ300'],
    'optcodes': ['10007447'],
    'datetoday': '20240927',
    'month': '2410',
    'month2': '2411',
    'siglist': ['im5', 'mid_minnum', 'edge_minnum', 'mixedge_minnum'],
    'mix2': 1,
    'mix3': 1,
    'mix4': 0,
    'mix44': 0,
    'mix5': 0,
    'mix6': 1,
    'mix7': 1,
    'mixtrd': 1,
    'fut3': 'CN2508',
    'fut4': '510500_tick',
    'fut44': '159915',
    'col2': 'mid',
    'col3': 'LastPrice',
    'col44': 'ForQuoteSysID',
    'atm': 3.5,
    's_mult': 1000,
    'mintick': 0.0001,
    'edgedelta': 0.0006,
    'futfreq': '500l',
    'volfreq': '1000l',
    'multi': 10000,
    'multi2': 10148,
    'dsflist': [-1, 1, 2, 4, 10, 20, 60],
    'dvlist': [-1, 1, 2, 4, 10, 20, 60],
    'roundnum': 5,  # 根据 int(math.log10(multi * 10)) 计算得出
    'tradetime00': [['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']],
    'longdir': ['OpenLong', 'CloseShort'],
    'shortdir': ['OpenShort', 'CloseLong'],
}


# 添加动态计算的配置项
def update_dynamic_config(config):
    under = config['under']
    month = config['month']
    month2 = config['month2']

    from db_solve.configs import paths
    path = paths.Paths(under, "trade_state")
    setting = paths.UNDERCONFIG[under]

    config.update({
        'fut': setting['spot'] + month,
        # 'fut2': setting['spot2'] + month,
        'fut2': setting['spot'] + month2,
        'str1': path.dirs + 'md_%s_cffex_multicast.parquet',
        'str3': path.dirs + 'md_%s_tcp_receiver_1_3276.parquet',
        'str4': path.dirs + 'md_%s_udp_receiver_1_50072.parquet',
        'str44': path.dirs + 'md_%s_shm_receiver.parquet',
        'tradepath': path.dirs + 'TradeRecords%s.parquet',
        'str_ord': path.dirs + '%s_OrderDetails.parquet',
        'str_optmd': path.dirs + 'md_%s_sse_mdgw.parquet',
        'str_vol': path.dirs + 'vols_%s.parquet',
        'outdirs': path.outdirs,
    })

    config.update({
        'key1': config['str1'],
        'key2': config['str1'],
        'key3': config['str3'],
        'key4': config['str4'],
        'key44': config['str44'],
    })

    return config


# 在使用配置之前调用此函数
DEFAULT_CONFIG = update_dynamic_config(DEFAULT_CONFIG)
