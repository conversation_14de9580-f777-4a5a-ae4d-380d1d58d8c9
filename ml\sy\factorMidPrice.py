# -*- coding: utf-8 -*-
"""
Created on Sun Mar 23 17:28:55 2025

@author: admin
"""

#Close = ct
#Lowest = lt
#Highest = ht
#Volume = Vt

#Techinal Analysis
#
import numpy as np
import pandas as pd
from scipy.signal import savgol_filter
from scipy.linalg import inv
from scipy.signal import filtfilt
# 基础工具函数
def SMA(series, window):
    return series.rolling(window).mean()

def EMA(series, window):
    return series.ewm(span=window, adjust=False).mean()

def WMA(series, window):
    weights = np.arange(1, window+1)
    return series.rolling(window).apply(lambda x: np.dot(x, weights)/weights.sum(), raw=True)



np.random.seed(42)  # 保证可重复性

# 生成基本价格序列（带有趋势和波动）
base_prices = np.cumsum(np.random.normal(0.1, 0.5, 40)) + 50

data = pd.DataFrame({
    'open': base_prices,
    'high': base_prices + np.abs(np.random.normal(0.5, 0.3, 40)),
    'low': base_prices - np.abs(np.random.normal(0.5, 0.3, 40)),
    'close': base_prices + np.random.normal(0, 0.2, 40),
    'volume': (np.abs(np.random.normal(10000, 3000, 40)) * (1 + 0.5 * np.sin(np.arange(40)/5)))})

# 确保高价>低价，并且收盘价在高低之间
data['high'] = data[['open', 'close']].max(axis=1) + np.random.uniform(0, 0.5, 40)
data['low'] = data[['open', 'close']].min(axis=1) - np.random.uniform(0, 0.5, 40)
data['volume'] = data['volume'].astype(int)

# 四舍五入到小数点后两位
data = data.round(2)

#print(data)

# 指标实现 -------------------------------------------------

def Accumulation_Distribution(high, low, close, volume):
    """
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
        volume (pd.Series): 成交量序列
    输出:
        pd.Series: ADL指标值
    """
    money_flow_multiplier = ((close - low) - (high - close)) / (high - low).replace(0, 1e-5)
    money_flow_volume = money_flow_multiplier * volume
    return money_flow_volume.cumsum()


def Awesome_Oscillator(high, low, window1=5, window2=34):
    """
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        window1 (int): 短期窗口（默认5）
        window2 (int): 长期窗口（默认34）
    输出:
        pd.Series: AO指标值
    """

    mid_price = (high + low) / 2
    return SMA(mid_price, window1) - SMA(mid_price, window2)

def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14):
    """
    计算 ADX（Average Directional Index）
    
    参数:
    - high: pd.Series, 最高价
    - low: pd.Series, 最低价
    - close: pd.Series, 收盘价
    - period: int, 平滑周期，默认 14
    
    返回:
    - pd.DataFrame，包含 +DI, -DI, DX, ADX
    """
    data = pd.DataFrame({'high': high, 'low': low, 'close': close})

    # 计算 +DM 和 -DM
    data['+DM'] = data['high'].diff()
    data['-DM'] = -data['low'].diff()

    # 只保留真正的 +DM 和 -DM
    data['+DM'] = data.apply(lambda row: row['+DM'] if row['+DM'] > row['-DM'] and row['+DM'] > 0 else 0, axis=1)
    data['-DM'] = data.apply(lambda row: row['-DM'] if row['-DM'] > row['+DM'] and row['-DM'] > 0 else 0, axis=1)
    
    data['TR14'] = 0
    data['+DM14'] = 0
    data['-DM14'] = 0
    data['ADX'] = 0
    # 计算 True Range (TR)
    data['TR'] = data[['high', 'low', 'close']].apply(
        lambda row: max(row['high'] - row['low'], 
                        abs(row['high'] - row['close']), 
                        abs(row['low'] - row['close'])), axis=1
    )

    for i in range(1, len(data)):
        if i == 1:  # 第一行数据，直接用当前 TR, +DM, -DM 作为初始值
            data.loc[i, 'TR14'] = data.loc[i, 'TR']
            data.loc[i, '+DM14'] = data.loc[i, '+DM']
            data.loc[i, '-DM14'] = data.loc[i, '-DM']
        else:  # 之后的行用 Wilder 平滑方法
            data.loc[i, 'TR14'] = data.loc[i-1, 'TR14'] - (data.loc[i-1, 'TR14'] / period) + data.loc[i, 'TR']
            data.loc[i, '+DM14'] = data.loc[i-1, '+DM14'] - (data.loc[i-1, '+DM14'] / period) + data.loc[i, '+DM']
            data.loc[i, '-DM14'] = data.loc[i-1, '-DM14'] - (data.loc[i-1, '-DM14'] / period) + data.loc[i, '-DM']

    # 计算 +DI14 和 -DI14
    data['+DI14'] = 100 * (data['+DM14'] / data['TR14'])
    data['-DI14'] = 100 * (data['-DM14'] /( data['TR14']))

    # 计算 DX（Directional Movement Index）
    data['DI_diff'] = abs(data['+DI14'] - data['-DI14'])
    data['DI_sum'] =abs( data['+DI14'] + data['-DI14'])
    data['DX'] = 100 * (data['DI_diff'] / data['DI_sum'])

    # 计算 ADX（Wilder 平滑 DX）
    data["ADXR"]=0
    for i in range(1, len(data)):
        if i == 1:  # 第一行数据，直接用当前 TR, +DM, -DM 作为初始值
            data.loc[i, 'ADX'] = data.loc[i, 'DX']
            data.loc[i,"ADXR"] = data.loc[i,"ADX"]
        else:  # 之后的行用 Wilder 平滑方法
            data.loc[i, 'ADX'] = (data.loc[i-1, 'DX']*13 + data.loc[i,"DX"])/period
            data.loc[i,"ADXR"] = (data.loc[i-1, 'ADXR'] + data.loc[i,"ADX"])/2

    data["ATR"] = 0
    for i in range(1, len(data)):
        if i == 1:  # 第一行数据，直接用当前 TR, +DM, -DM 作为初始值
            data.loc[i, 'ATR'] = data.loc[i, 'TR']
        else:  # 之后的行用 Wilder 平滑方法
            data.loc[i, 'ATR'] = (data.loc[i-1, 'ATR']*(period-1) + data.loc[i,"TR"])/period

    return data[['+DI14', '-DI14', 'DX', 'ADX',"ADXR","ATR"]]


def ATR(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14):
    """
    计算 ATR（Average True Index）
    
    参数:
    - high: pd.Series, 最高价
    - low: pd.Series, 最低价
    - close: pd.Series, 收盘价
    - period: int, 平滑周期，默认 14
    
    返回:
    - pd.Series
    """
    data = pd.DataFrame({'high': high, 'low': low, 'close': close})
    data['TR'] = data[['high', 'low', 'close']].apply(
        lambda row: max(row['high'] - row['low'], 
                        abs(row['high'] - row['close']), 
                        abs(row['low'] - row['close'])), axis=1
    )
    data["ATR"] = 0
    for i in range(1, len(data)):
        if i == 1:  # 第一行数据，直接用当前 TR, +DM, -DM 作为初始值
            data.loc[i, 'ATR'] = data.loc[i, 'TR']
        else:  # 之后的行用 Wilder 平滑方法
            data.loc[i, 'ATR'] = (data.loc[i-1, 'ATR']*(period-1) + data.loc[i,"TR"])/period
    
    return data[["ATR"]]



def Alligator(high, low, jaw=13, teeth=8, lips=5):
    """
    Williams Alligator Indicator
    """
    mid_price = (high + low) / 2
    return {
        'jaw': SMA(mid_price, jaw),
        'teeth': SMA(mid_price, teeth),
        'lips': SMA(mid_price, lips)
    }

def absolute_price_oscillator(high, low, fast_window=5, slow_window=13):
    """
    绝对价格振荡器 (APO)
    
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        fast_window (int): 快速EMA窗口 (默认5)
        slow_window (int): 慢速EMA窗口 (默认13)
        
    输出:
        pd.Series: APO指标值
        
    公式:
        M_t = (H_t + L_t)/2
        APO = EMA(M_t, fast_window) - EMA(M_t, slow_window)
    """
    mid_price = (high + low) / 2
    apo = EMA(mid_price, fast_window) - EMA(mid_price, slow_window)
    return apo

def aroon_indicator(high, low, window=20):
    """
    Aroon指标 (包含上行和下行)
    
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        window (int): 计算窗口 (默认20)
        
    输出:
        pd.DataFrame: 包含'Aroon_Up'和'Aroon_Down'两列
        
    公式:
        Aroon_Up = (window - 最高价出现周期) / window * 100
        Aroon_Down = (window - 最低价出现周期) / window * 100
    """
    def _calculate_aroon_up(series, window):
        return (window - series.rolling(window).apply(np.argmax)) / window * 100
    
    def _calculate_aroon_down(series, window):
        return (window - series.rolling(window).apply(np.argmin)) / window * 100
    
    aroon_up = _calculate_aroon_up(high, window)
    aroon_down = _calculate_aroon_down(low, window)
    
    return pd.DataFrame({
        'Aroon_Up': aroon_up,
        'Aroon_Down': aroon_down
    })


def aroon_oscillator(high, low,close, window=14):
    """
    Aroon振荡器
    
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        window (int): 计算窗口 (默认20)
        
    输出:
        pd.Series: Aroon Oscillator值
        
    公式:
        Aroon Oscillator = Aroon_Up - Aroon_Down
    """
    aroon = aroon_indicator(high, low, window)
    
    return aroon['Aroon_Up'] - aroon['Aroon_Down']


def ATR(high: pd.Series, low: pd.Series, close: pd.Series, window= 14):
    """
    Aroon振荡器
    
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        period(int): 计算窗口 (默认14)
        
    输出:
        dataframe:average true range
        """
    aroon = aroon_indicator(high, low, window)
    return aroon['Aroon_Up'] - aroon['Aroon_Down']

def Bollinger_Bands(close, window=20, std_dev=2):
    """
    输入:
        close (pd.Series): 收盘价序列
        window (int): 窗口大小（默认20）
        std_dev (float): 标准差倍数（默认2）
    输出:
        dict: 包含上轨、中轨、下轨的字典
    """
    sma = SMA(close, window)
    std = close.rolling(window).std()
    return {
        'upper': sma + std_dev * std,
        'middle': sma,
        'lower': sma - std_dev * std
    }

def Ichimoku_Cloud(high, low, conversion=9, base=26, leading_span=52):
    """
    Ichimoku Cloud
    """
    conversion_line = (high.rolling(conversion).max() + low.rolling(conversion).min()) / 2
    base_line = (high.rolling(base).max() + low.rolling(base).min()) / 2
    leading_span_a = (conversion_line + base_line) / 2
    leading_span_b = (high.rolling(leading_span).max() + low.rolling(leading_span).min()) / 2
    return {
        'conversion': conversion_line,
        'base': base_line,
        'leading_a': leading_span_a,
        'leading_b': leading_span_b
    }


def Chande_Momentum_Oscillator(close, period=19):
    """
    Chande动量振荡器
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认19）
    输出:
        pd.Series: CMO值
    """
    delta = close.diff()
    su = delta.where(delta > 0, 0).rolling(window=period).sum()
    sd = (-delta.where(delta < 0, 0)).rolling(window=period).sum()
    cmo = 100 * (su - sd) / (su + sd).replace(0, 1e-5)
    return cmo

def Chaikin_Oscillator(high, low, close, volume):
    """
    Chaikin振荡器
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
        volume (pd.Series): 成交量序列
    输出:
        pd.Series: Chaikin值
    """
    # 计算ADL
    mfm = ((close - low) - (high - close)) / (high - low).replace(0, 1e-5)
    mfv = mfm * volume
    adl = mfv.cumsum()
    
    # 计算EMA
    ema3 = adl.ewm(span=3, adjust=False).mean()
    ema10 = adl.ewm(span=10, adjust=False).mean()
    return ema3 - ema10


# Chandelier Exit
def Chandelier_Exit(high, low, close, atr_period=22, mult=3):
    """
    Chandelier离场指标
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
        atr_period (int): ATR周期（默认22）
        mult (int): ATR倍数（默认3）
    输出:
        tuple: (长线离场值, 短线离场值)
    """
    h22 = high.rolling(window=atr_period).max()
    l22 = low.rolling(window=atr_period).min()
    atr = ATR(high, low, close, atr_period)
    
    long_exit = h22 - atr * mult
    short_exit = l22 + atr * mult
    return long_exit, short_exit


# Donchian Channels
def Donchian_Channels(high, low, period=20):
    """
    Donchian通道
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        period (int): 计算周期（默认20）
    输出:
        tuple: (上轨, 中轨, 下轨)
    """
    upper = high.rolling(window=period).max()
    lower = low.rolling(window=period).min()
    middle = (upper + lower) / 2
    return upper, middle, lower


def Center_of_Gravity_Oscillator(high, low, r=0.6):
    """
    重心振荡器 (Center of Gravity Oscillator)
    输入:
        r (float): 权重系数（默认为1，可根据需求调整）
    输出:
        pd.Series: COG值
    """
    # 计算中间价 M_t = (bid + ask) / 2
    M_t = (high + low) / 2
    M_t_1 = M_t.shift(1)  # 前一期中间价
    print(M_t)
    # 计算COG: -(M_t + r*M_t_1) / (M_t + M_t_1)
    numerator = -(M_t + r * M_t_1)
    denominator = (M_t + M_t_1).replace(0, 1e-5)  # 避免除零错误
    print(numerator , denominator)
    cog = numerator / denominator
    
    return cog


def Donchian_Channels(high, low, period=20):
    """
    Donchian通道
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        period (int): 计算周期（默认20）
    输出:
        tuple: (上轨, 中轨, 下轨)
    """
    upper = high.rolling(window=period).max()
    lower = low.rolling(window=period).min()
    middle = (upper + lower) / 2
    return upper, middle, lower


# Double Exponential Moving Average (DEMA)
def DEMA(high, low, period=20):
    """
    双指数移动平均 (Double Exponential Moving Average)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        period (int): 计算周期（默认20）
    输出:
        pd.Series: DEMA值
    """
    M_t = (high + low) / 2  # 中间价
    ema1 = M_t.ewm(span=period, adjust=False).mean()
    ema2 = ema1.ewm(span=period, adjust=False).mean()
    return 2 * ema1 - ema2

# Detrended Price Oscillator (DPO)
def DPO(high, close, period=10):
    """
    去趋势价格振荡器 (Detrended Price Oscillator)
    输入:
        high (pd.Series): 最高价序列
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
    输出:
        pd.Series: DPO值
    """
    H_high = high.rolling(window=period).max()
    SMA_close = close.rolling(window=period).mean()
    return (H_high / (period + 2)) - SMA_close

def Heikin_Ashi(open_p, high, low, close):
    """
    平均K线 (Heikin-Ashi Candles)
    输入:
        open_p (pd.Series): 开盘价序列
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
    输出:
        pd.DataFrame: Heikin-Ashi四列(open, high, low, close)
    """
    ha_close = (open_p + high + low + close) / 4
    ha_open = (open_p.shift(1) + close.shift(1)) / 2
    ha_open.iloc[0] = open_p.iloc[0]  # 首项处理
    
    ha_high = pd.concat([high, ha_open, ha_close.shift(1)], axis=1).max(axis=1)
    ha_low = pd.concat([low, ha_open, ha_close.shift(1)], axis=1).min(axis=1)
    
    return pd.DataFrame({
        'HA_open': ha_open,
        'HA_high': ha_high,
        'HA_low': ha_low,
        'HA_close': ha_close
    })

def Highest_High_Lowest_Low(high, low, period=20):
    """
    最高高价和最低低价通道
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        period (int): 计算周期（默认20）
    输出:
        tuple: (最高高价序列, 最低低价序列)
    """
    return (
        high.rolling(window=period).max(),
        low.rolling(window=period).min()
    )

def Hull_MA(high, low, period=10):
    """
    赫尔移动平均线 (Hull Moving Average)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        period (int): 计算周期（默认10）
    输出:
        pd.Series: Hull MA值
    """
    AHL = (high + low) / 2  # Average High-Low
    wma_half = WMA(AHL, int(np.sqrt(period)))
    wma_full = WMA(AHL, period)
    wma_half_period = WMA(AHL, period//2)
    
    return wma_half * (2 * wma_half_period - wma_full)


def IBS(high, low, close):
    """
    内部柱强度 (Internal Bar Strength)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
    输出:
        pd.Series: IBS值 (范围0-1)
    """
    return (close - low) / (high - low).replace(0, 1e-5)


def Keltner_Channels(high, low, close, ema_period=20, atr_period=10, multiplier=2):
    """
    凯尔特纳通道 (Keltner Channels)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
        ema_period (int): 中轨EMA周期（默认20）
        atr_period (int): ATR周期（默认10）
        multiplier (int): ATR倍数（默认2）
    输出:
        tuple: (上轨, 中轨, 下轨)
    """
    # 计算中轨（使用收盘价EMA）
    middle = close.ewm(span=ema_period, adjust=False).mean()
    
    # 计算ATR
    atr = ATR(high, low, close, atr_period)
    
    upper = middle + multiplier * atr
    lower = middle - multiplier * atr
    return upper, middle, lower

# MACD Oscillator (基于高低均价)
def MACD_Oscillator(high, low, fast_period=12, slow_period=26):
    """
    MACD振荡器（基于高低均价）
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        fast_period (int): 快线周期（默认12）
        slow_period (int): 慢线周期（默认26）
    输出:
        pd.Series: MACD值
    """
    ahl = (high + low) / 2  # Average High-Low
    macd = ahl.ewm(span=fast_period, adjust=False).mean() - ahl.ewm(span=slow_period, adjust=False).mean()
    return macd


def Median_Price(high, low):
    """
    中间价 (Median Price)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
    输出:
        pd.Series: 中间价序列
    """
    return (high + low) / 2

# Momentum
def Momentum(close, period=1):
    """
    动量指标 (Momentum)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认1）
    输出:
        pd.Series: 动量值
    """
    return close.diff(periods=period)


def Variable_MA(close, period=3):
    """
    可变移动平均线 (Variable Moving Average)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认3）
    输出:
        pd.Series: VMA值
    """
    # 计算效率比率(ER)
    direction = abs(close - close.shift(period))
    volatility = abs(close.diff()).rolling(window=period).sum() * period
    er = direction / volatility.replace(0, 1e-5)
    
    # 计算自适应权重
    alpha = 2 / (period + 1)
    weights = alpha * er
    
    # 计算VMA
    vma = close.rolling(window=period).apply(
        lambda x: np.sum(weights.iloc[x.index] * x) / np.sum(weights.iloc[x.index]),
        raw=False
    )
    return vma


def NATR(high, low, close, atr_period=14):
    """
    标准化平均真实波幅 (Normalized ATR)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
        atr_period (int): ATR计算周期（默认14）
    输出:
        pd.Series: NATR值（百分比）
    """
    atr = ATR(high, low, close, atr_period)
    return (atr / close) * 100


# Percentage Price Oscillator (PPO)
def PPO(high, low, fast_period=12, slow_period=26):
    """
    百分比价格振荡器 (Percentage Price Oscillator)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        fast_period (int): 快线周期（默认12）
        slow_period (int): 慢线周期（默认26）
    输出:
        pd.Series: PPO值（百分比）
    """
    ahl = (high + low) / 2
    macd = ahl.ewm(span=fast_period, adjust=False).mean() - ahl.ewm(span=slow_period, adjust=False).mean()
    slow_ema = ahl.ewm(span=slow_period, adjust=False).mean()
    return (macd / slow_ema.replace(0, 1e-5)) * 100

def ROC(close, period=12):  
    """
    变动率指标 (Rate of Change)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认12）
    输出:
        pd.Series: ROC值（百分比）
    """
    return ((close - close.shift(period)) / close.shift(period).replace(0, 1e-5)) * 100

def RSI(close, period=14):
    """
    相对强弱指数 (Relative Strength Index)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认14）
    输出:
        pd.Series: RSI值（百分比）
    """
    delta = close.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss.replace(0, 1e-5)
    return 100 - (100 / (1 + rs))

def Rising_Falling_SAR(high, low, af_start=0.02, af_step=0.02, af_max=0.2):
    """
    分别计算上升和下降的SAR值（无自动反转逻辑）
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        af_start (float): 初始加速因子(默认0.02)
        af_step (float): 加速因子增量(默认0.02)
        af_max (float): 最大加速因子(默认0.2)
    输出:
        tuple: (Rising_SAR序列, Falling_SAR序列)
    """
    # 初始化输出序列
    rising_sar = pd.Series(index=high.index, dtype=float)
    falling_sar = pd.Series(index=high.index, dtype=float)
    
    # 初始值（假设第一日为上升趋势起点）
    rising_sar.iloc[0] = low.iloc[0]
    falling_sar.iloc[0] = high.iloc[0]
    
    # 上升趋势参数
    rising_ep = high.iloc[0]  # 极值点（最高价）
    rising_af = af_start      # 加速因子
    
    # 下降趋势参数
    falling_ep = low.iloc[0]  # 极值点（最低价）
    falling_af = af_start
    
    for i in range(1, len(high)):
        # --- 计算上升SAR ---
        rising_sar.iloc[i] = rising_sar.iloc[i-1] + rising_af * (rising_ep - rising_sar.iloc[i-1])
        # 更新极值点
        if high.iloc[i] > rising_ep:
            rising_ep = high.iloc[i]
            rising_af = min(rising_af + af_step, af_max)
        # 确保SAR不超过最低价
        rising_sar.iloc[i] = min(rising_sar.iloc[i], low.iloc[i-1], low.iloc[i])
        
        # --- 计算下降SAR ---
        falling_sar.iloc[i] = falling_sar.iloc[i-1] - falling_af * (falling_sar.iloc[i-1] - falling_ep)
        # 更新极值点
        if low.iloc[i] < falling_ep:
            falling_ep = low.iloc[i]
            falling_af = min(falling_af + af_step, af_max)
        # 确保SAR不低于最高价
        falling_sar.iloc[i] = max(falling_sar.iloc[i], high.iloc[i-1], high.iloc[i])
    
    return rising_sar, falling_sar


def Standard_Deviation(close, period=10):
    """
    标准偏差指标 (Standard Deviation)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
    输出:
        tuple: (偏差序列, 标准偏差序列)
    """
    sma = close.rolling(window=period).mean()
    deviation = close - sma
    squared_deviation = deviation.pow(2)
    std = np.sqrt(squared_deviation.rolling(window=period).mean())
    return deviation, std


# Stochastic RSI
def Stochastic_RSI(close, rsi_period=14, stoch_period=10):
    """
    随机相对强弱指数 (Stochastic RSI)
    输入:
        close (pd.Series): 收盘价序列
        rsi_period (int): RSI计算周期（默认14）
        stoch_period (int): 随机周期（默认10）
    输出:
        pd.Series: 随机RSI值
    """
    rsi_values = RSI(close, rsi_period)
    min_rsi = rsi_values.rolling(window=stoch_period).min()
    max_rsi = rsi_values.rolling(window=stoch_period).max()
    return (rsi_values - min_rsi) / (max_rsi - min_rsi).replace(0, 1e-5)





# T3 Triple Exponential Moving Average
def T3_MA(close, period=10, volume_factor=0.7):
    """
    T3三重指数移动平均 (T3 Triple EMA)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
        volume_factor (float): 成交量因子(默认0.7)
    输出:
        pd.Series: T3 MA值
    """
    # 第一层EMA
    e1 = close.ewm(span=period, adjust=False).mean()
    # 第二层EMA
    e2 = e1.ewm(span=period, adjust=False).mean()
    # 第三层EMA
    e3 = e2.ewm(span=period, adjust=False).mean()
    # 第四层EMA
    e4 = e3.ewm(span=period, adjust=False).mean()
    # 第五层EMA
    e5 = e4.ewm(span=period, adjust=False).mean()
    # 第六层EMA
    e6 = e5.ewm(span=period, adjust=False).mean()
    
    c1 = -volume_factor ** 3
    c2 = 3 * volume_factor ** 2 + 3 * volume_factor ** 3
    c3 = -6 * volume_factor ** 2 - 3 * volume_factor - 3 * volume_factor ** 3
    c4 = 1 + 3 * volume_factor + volume_factor ** 3 + 3 * volume_factor ** 2
    
    return c1 * e6 + c2 * e5 + c3 * e4 + c4 * e3


def TEMA(close, period=10):
    """
    三重指数移动平均 (Triple Exponential Moving Average)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
    输出:
        pd.Series: TEMA值
    """
    ema1 = close.ewm(span=period, adjust=False).mean()
    ema2 = ema1.ewm(span=period, adjust=False).mean()
    ema3 = ema2.ewm(span=period, adjust=False).mean()
    return 3*ema1 - 3*ema2 + ema3

def TRIMA(close, period=10):
    """
    三角移动平均 (Triangular Moving Average)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
    输出:
        pd.Series: TRIMA值
    """
    return close.rolling(window=period).mean().rolling(window=period).mean().rolling(window=period).mean()




def TRIX(close, period=10):
    """
    三重指数平均动量振荡器 (Triple Exponential Average)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
    输出:
        pd.Series: TRIX值
    """
    ema1 = close.ewm(span=period, adjust=False).mean()
    ema2 = ema1.ewm(span=period, adjust=False).mean()
    ema3 = ema2.ewm(span=period, adjust=False).mean()
    return ema3.pct_change(periods=1)  # 1-period Rate of Change

def TSI(close, long_period=26, short_period=13, apc_period=25):
    """
    真实强度指数 (True Strength Index)
    输入:
        close (pd.Series): 收盘价序列
        long_period (int): 长期EMA周期（默认26）
        short_period (int): 短期EMA周期（默认13）
        apc_period (int): 绝对价格变化EMA周期（默认25）
    输出:
        pd.Series: TSI值
    """
    pc = close.diff()  # 价格变化
    apc = abs(pc)      # 绝对价格变化
    
    ema1 = pc.ewm(span=long_period, adjust=False).mean()
    ema2 = ema1.ewm(span=short_period, adjust=False).mean()
    
    ema3 = apc.ewm(span=apc_period, adjust=False).mean()
    ema4 = ema3.ewm(span=short_period, adjust=False).mean()
    
    return 100 * ema2 / ema4.replace(0, 1e-5)

def Ultimate_Oscillator(high, low, close):
    """
    终极振荡器 (Ultimate Oscillator) - 严格遵循论文公式
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
    输出:
        pd.Series: UO值
    """
    prev_close = close.shift(1)
    bp = close - np.where(prev_close < low, prev_close, low)  # 买入压力(BP)
    
    # --- 7周期计算 ---
    tr1_7 = np.where(prev_close > high, prev_close, high)
    tr2_7 = np.where(prev_close < low, prev_close, low)
    tr_7 = (tr1_7 - tr2_7).rolling(window=7).sum()
    avg7 = bp.rolling(window=7).sum() / tr_7.replace(0, 1e-5)
    
    # --- 14周期计算 ---
    tr1_14 = np.where(prev_close > high, prev_close, high)
    tr2_14 = np.where(prev_close < low, prev_close, low)
    tr_14 = (tr1_14 - tr2_14).rolling(window=14).sum()
    avg14 = bp.rolling(window=14).sum() / tr_14.replace(0, 1e-5)
    
    # --- 28周期计算 ---
    tr1_28 = np.where(prev_close > high, prev_close, high)
    tr2_28 = np.where(prev_close < low, prev_close, low)
    tr_28 = (tr1_28 - tr2_28).rolling(window=28).sum()
    avg28 = bp.rolling(window=28).sum() / tr_28.replace(0, 1e-5)
    
    return 100 * (4*avg7 + 2*avg14 + avg28) / 7

def Weighted_Close(high, low, close):
    """
    加权收盘价 (Weighted Close)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
    输出:
        pd.Series: WCL值
    """
    return (high + low + 2 * close) / 4

def Williams_R(high, low, close, period=14):
    """
    威廉姆斯%R指标 (Williams %R)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认14）
    输出:
        pd.Series: %R值
    """
    highest_high = high.rolling(window=period).max()
    lowest_low = low.rolling(window=period).min()
    return -100 * (highest_high - close) / (highest_high - lowest_low).replace(0, 1e-5)

def ZLEMA(close, period=10):
    """
    零滞后指数移动平均线 (Zero-Lag EMA)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
    输出:
        pd.Series: ZLEMA值
    """
    lag = (period - 1) // 2  # 根据公式计算滞后
    error = close - close.shift(lag)
    input_series = close + error
    return input_series.ewm(span=period, adjust=False).mean()

def Fractals(high, low):
    """
    分形指标 (Fractals)
    输入:
        high (pd.Series): 最高价序列
        low (pd.Series): 最低价序列
    输出:
        tuple: (买分形序列, 卖分形序列)
    """
    # 初始化输出序列
    buy_fractals = pd.Series(False, index=high.index)
    sell_fractals = pd.Series(False, index=low.index)
    
    # 滑动窗口检测分形
    for i in range(2, len(high)-2):
        # 买分形条件（中间最高价高于前后两个）
        buy_condition = (
            (high.iloc[i] > high.iloc[i-2]) & 
            (high.iloc[i] > high.iloc[i-1]) & 
            (high.iloc[i] > high.iloc[i+1]) & 
            (high.iloc[i] > high.iloc[i+2]))
        # 卖分形条件（中间最低价低于前后两个）
        sell_condition = (
            (low.iloc[i] < low.iloc[i-2]) & 
            (low.iloc[i] < low.iloc[i-1]) & 
            (low.iloc[i] < low.iloc[i+1]) & 
            (low.iloc[i] < low.iloc[i+2]))
        
        buy_fractals.iloc[i] = buy_condition
        sell_fractals.iloc[i] = sell_condition
    
    return buy_fractals, sell_fractals



def Linear_Regression_Line(close, period=10):
    """
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期
    输出:
        pd.Series: 回归预测值（PV）
    """
    def calculate_regression(window):
        x = np.arange(len(window))  # 时间序列 [0,1,2,...,9]
        y = window.values
        x_mean, y_mean = np.mean(x), np.mean(y)
        
        # 计算斜率c2（公式分解为协方差/方差）
        cov = np.sum((x - x_mean) * (y - y_mean))
        var_x = np.sum((x - x_mean) ** 2)
        slope = cov / var_x if var_x != 0 else 0
        
        # 计算截距c1
        intercept = y_mean - slope * x_mean
        
        # 返回预测值 PV = c1 + c2 * x（x取最后一个时间点）
        return intercept + slope * (period - 1)
    
    return close.rolling(window=period).apply(calculate_regression, raw=False)


def Rational_Transfer_Filter(close, numerator_coeffs=[1.0], denominator_coeffs=[1.0], lookback=10):
    """
    有理传递函数滤波器 (Rational Transfer Function)
    输入:
        close (pd.Series): 收盘价序列
        numerator_coeffs (list): 分子系数（默认[1.0]）
        denominator_coeffs (list): 分母系数（默认[1.0]）
        lookback (int): 回溯周期（默认10）
    输出:
        pd.Series: 滤波后输出
    """
    from scipy.signal import lfilter
    output = pd.Series(index=close.index, dtype=float)
    
    for i in range(lookback, len(close)):
        window = close.iloc[i-lookback:i]
        # 使用SciPy的线性滤波器实现传递函数
        filtered = lfilter(numerator_coeffs, denominator_coeffs, window)
        output.iloc[i] = filtered[-1]  # 取最后一个值作为当前输出
    
    return output


def Savitzky_Golay_Filter(series, window_length=5, poly_order=2, weights=None):
    """
    Savitzky-Golay数字滤波器 (Savitzky-Golay Filter)
    输入:
        series (pd.Series): 输入信号序列
        window_length (int): 窗口长度（默认5）
        poly_order (int): 多项式阶数（默认2）
        weights (array): 权重向量（默认None，即等权重）
    输出:
        pd.Series: 滤波后信号
    """
    if window_length % 2 == 0:
        raise ValueError("窗口长度必须是奇数")
    if poly_order >= window_length:
        raise ValueError("多项式阶数必须小于窗口长度")
    
    half_window = (window_length - 1) // 2
    n = poly_order
    filtered = pd.Series(index=series.index, dtype=float)
    
    # 生成权重矩阵（默认等权重）
    if weights is None:
        weights = np.ones(window_length)
    else:
        weights = np.array(weights)
    
    # 滑动窗口处理
    for i in range(half_window, len(series)-half_window):
        # 窗口索引范围
        window_indices = np.arange(i-half_window, i+half_window+1)
        y = series.iloc[window_indices].values
        x = np.arange(-half_window, half_window+1)
        
        # --- 构造矩阵A和向量B ---
        A = np.zeros((n+1, n+1))
        B = np.zeros(n+1)
        
        # 填充矩阵A和向量B
        for k in range(n+1):
            for r in range(n+1):
                A[k, r] = np.sum(weights * x**(k + r))
            B[k] = np.sum(weights * y * x**k)
        
        # --- 求解AP = B ---
        try:
            P = inv(A) @ B  # 矩阵求逆解线性方程组
        except np.linalg.LinAlgError:
            P = np.linalg.pinv(A) @ B  # 伪逆处理奇异矩阵
        
        # --- 计算滤波值（0阶响应）---
        filtered.iloc[i] = np.sum(P * x**0)  # y[0] = p0*x^0 + p1*x^1 + ... + pn*x^n (x=0)
    
    # 处理边界值（用最近的有效值填充）
    filtered.iloc[:half_window] = filtered.iloc[half_window]
    filtered.iloc[-half_window:] = filtered.iloc[-half_window-1]
    return filtered

def Zero_Phase_Filter(series, window_length=5, polyorder=2):
    """
    零相位滤波器 (双向Savitzky-Golay滤波)
    输入:
        series (pd.Series): 输入信号序列
        window_length (int): 窗口长度（必须为奇数，默认5）
        polyorder (int): 多项式阶数（默认2）
    输出:
        pd.Series: 滤波后信号
    """
    # 参数校验
    if window_length % 2 == 0:
        raise ValueError("窗口长度必须是奇数")
    if polyorder >= window_length:
        raise ValueError("多项式阶数必须小于窗口长度")
    
    # 转换为numpy数组处理
    y = series.values.astype(float)
    
    # 计算Savitzky-Golay滤波器系数
    b = savgol_filter(np.eye(window_length), window_length, polyorder, axis=0)[window_length//2]
    
    # 双向滤波
    filtered = filtfilt(
        b,                # 分子系数
        [1.0],           # 分母系数（FIR滤波器设为1）
        y,               # 输入信号
        padtype='odd',   # 边界填充方式
        padlen=window_length-1  # 填充长度
    )
    
    return pd.Series(filtered, index=series.index)


def Remove_Offset(close, period=14):
    """
    去除偏移 (Remove Offset)
    输入:
        close (pd.Series): 收盘价序列
        period (int): 回溯周期（默认10）
    输出:
        pd.Series: 去除偏移后的序列
    """
    offset = close - close.rolling(window=period).mean()
    return offset

def Detrend_Least_Squares(close, period=10):
    """
    最小二乘去趋势 (Linear Detrending) - 修正版本
    输入:
        close (pd.Series): 收盘价序列
        period (int): 回溯周期（默认10）
    输出:
        pd.Series: 去趋势后的残差
    """
    def linear_detrend(window):
        x = np.arange(len(window))  # 强制使用0-based索引
        y = window.values
        X = np.vstack([x, np.ones(len(x))]).T
        a, b = np.linalg.lstsq(X, y, rcond=None)[0]
        return y[-1] - (a * x[-1] + b)  # 直接使用数组索引
    
    return close.rolling(window=period).apply(linear_detrend, raw=False)



def Beta_Calculation(close, period=10):
    """
    Beta类似指标
    输入:
        close (pd.Series): 收盘价序列
        period (int): 计算周期（默认10）
    输出:
        pd.Series: Beta值
    """
    # 计算指数变化
    index_cl = close.pct_change() + 1
    index_av = close.rolling(window=2).mean().pct_change() + 1
    
    # 计算偏差
    dev_cl = index_cl - index_cl.rolling(window=period).mean()
    dev_av = index_av - index_av.rolling(window=period).mean()
    
    # 计算协方差和方差
    cov = dev_cl.rolling(window=period).cov(dev_av)
    var = dev_av.rolling(window=period).var()
    
    return cov / var.replace(0, 1e-5)


adx = Detrend_Least_Squares(data.close)

