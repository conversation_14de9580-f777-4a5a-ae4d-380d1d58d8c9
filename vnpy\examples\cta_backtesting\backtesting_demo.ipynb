{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "from vnpy.trader.optimize import OptimizationSetting\n", "from vnpy_ctastrategy.backtesting import BacktestingEngine\n", "from vnpy_ctastrategy.strategies.atr_rsi_strategy import AtrRsiStrategy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["engine = BacktestingEngine()\n", "engine.set_parameters(\n", "    vt_symbol=\"IF888.CFFEX\",\n", "    interval=\"1m\",\n", "    start=datetime(2019, 1, 1),\n", "    end=datetime(2019, 4, 30),\n", "    rate=0.3/10000,\n", "    slippage=0.2,\n", "    size=300,\n", "    pricetick=0.2,\n", "    capital=1_000_000,\n", ")\n", "engine.add_strategy(AtrRsiStrategy, {})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["engine.load_data()\n", "engine.run_backtesting()\n", "df = engine.calculate_result()\n", "engine.calculate_statistics()\n", "engine.show_chart()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["setting = OptimizationSetting()\n", "setting.set_target(\"sharpe_ratio\")\n", "setting.add_parameter(\"atr_length\", 25, 27, 1)\n", "setting.add_parameter(\"atr_ma_length\", 10, 30, 10)\n", "\n", "engine.run_ga_optimization(setting)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["engine.run_bf_optimization(setting)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.2"}}, "nbformat": 4, "nbformat_minor": 2}