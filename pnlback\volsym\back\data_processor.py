import os
import re
import sys
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import multiprocessing

import pandas as pd
import numpy as np
import datetime
from matplotlib.dates import date2num

sys.path.extend([os.path.abspath(os.path.join(os.path.dirname(__file__), *(['..'] * i))) for i in range(5)])

from pnlback.volsym.back.get_ws import login, logout
from pnlback.volsym.back.savedb import InfluxManager
# 配置参数
from pnlback.volsym.ui.config import volconfig,UNDER

from db_solve import greeks
from db_solve.parreader import loadtrade
from db_solve.parreader import loadvol

tradetime00 = [['09:30:10', '11:30:00'], ['13:00:00', '14:56:00']]


class DataManager:
    def __init__(self, debug=False):
        super().__init__()
        self.debug = debug
        self.under=UNDER
        self.md_manager = None
        self.voltime_data = None
        self.current_time = None
        self.selected_months = "所有月份"
        self.svis = None
        self.fit_svi_model = None
        self.config = None
        self.data = None
        self.plot_data = None
        self.current_data = None
        self.last_current_data = None
        self.current_params = None
        self.last_params = None
        self.unique_times = None
        self.trade_data = None
        self._processed_data = None
        self.tradetime_num = None
        self.influx_manager = InfluxManager(debug=self.debug)
        # 创建进程池，使用CPU核心数-1的进程数
        self._process_pool = ProcessPoolExecutor(max_workers=max(1, multiprocessing.cpu_count() - 1))

    def updateparams(self, custom_r, use_new_iv, config):
        """
        自定义参数
        Args:
            custom_r: 自定义r
            use_new_iv: 是否使用新iv
            config: 配置文件
        """
        self.custom_r = custom_r
        self.use_new_iv = use_new_iv
        self.config = config
        self.fit_svi_model = config['FIT_SVI_MODEL']

    def get_frommd(self, path, live_mode=False):
        """
        获取波动率数据
        Args:
            path: 数据文件路径
            live_mode: 是否使用实时模式(从py_statistics1获取数据)
        Returns:
            处理后的波动率数据DataFrame
        """
        if live_mode:
            if self.md_manager is None:
                raise RuntimeError("Market data manager is not initialized")
            data = self.md_manager.get_options_by_key('underlyer', self.under)  # 获取实时波动率数据
            # 确保数据格式与loadvol输出一致
            # 转为DataFrame
            data = pd.DataFrame(data)
            # 行和列反了
            data = data.T
            if data.empty:
                return None
            else:
                # Update numeric conversion to handle errors explicitly
                for column in data.columns:
                    try:
                        data[column] = data[column].astype(float)
                    except (ValueError, TypeError):
                        continue
                data.rename(
                    columns={'optioncode': 'code', 'timestamp': 'time', 'expiry': 'exp', 'strike': 'K',
                             'spotprice': 'spot',
                             'callPut': 'call', 'z': 'Z', 'bidvol': 'bid_vol', 'askvol': 'ask_vol', 'fitvol': 'sigma'},
                    inplace=True)
                data['exp'] = data['exp'].astype(int)
                data['code'] = data['code'].astype(int)
                data['vol_spread'] = data['ask_vol'] - data['bid_vol']
                data['bid'] = data['tv'] - data['vol_spread'] / 2 * data['vega'] * 100
                data['ask'] = data['tv'] + data['vol_spread'] / 2 * data['vega'] * 100
                data['rf'] = 0
                data['time'] = pd.to_datetime(data['time'].iloc[0])  # 默认为本地时间（东八区）
                # 计算年化到期时间（工作日计）
                expiry_times = pd.to_datetime(data['exp'].astype(str) + ' 15:00:00')
                data['time2expiry'] = np.busday_count(
                    data['time'].dt.date.values.astype('datetime64[D]'),
                    expiry_times.dt.date.values.astype('datetime64[D]')
                ) / 252
        else:
            data = loadvol(path, [], tradetime00, all=True)
            data['forward'] = data['spot'] * np.exp((data['rf'] - data['br']) * data['time2expiry'])
        data['posi'] = 0
        data['neg_posi'] = 0
        return data

    def combine_delta_data(self, data):
        # 分离正delta和delta的数据
        positive_delta = data[data['delta'] > 0].copy()
        negative_delta = data[data['delta'] < 0].copy()

        # 重命名负delta的列
        negative_delta = negative_delta.rename(columns={
            'code': 'neg_code',
            'tv': 'neg_tv',
            'bid': 'neg_bid',
            'ask': 'neg_ask',
            'bid_vol': 'neg_bid_vol',
            'ask_vol': 'neg_ask_vol',
            'sigma': 'neg_sigma',
            'delta': 'neg_delta'
        })

        # 合数据
        combined_data = pd.merge(
            positive_delta,
            negative_delta[
                ['time', 'exp', 'K', 'neg_code', 'neg_tv', 'neg_bid', 'neg_ask', 'neg_bid_vol', 'neg_ask_vol',
                 'neg_delta']],
            on=['time', 'exp', 'K'],
            how='left'
        )
        # 这里 forward 的计算采用了带分红收益率 q 的期权平价（Put-Call Parity, PCP）公式
        # C - P = S * exp(-q*T) - K * exp(-r*T)
        # 故 F = S * exp((r - q) * T) = C - P + K * exp(-r*T)
        combined_data['strike'] = combined_data['K']
        combined_data['forward'] = (combined_data['tv'] - combined_data['neg_tv'] + combined_data['K'] * np.exp(-combined_data['rf'] * combined_data['time2expiry']))
        # combined_data = combined_data.drop(columns=['rf', 'br'])
        return combined_data

    def load_and_process_data(self, file_path, live_mode=False):
        """加载并处理数据"""
        data = self.get_frommd(None if live_mode else file_path, live_mode=live_mode)
        if data is not None and not data.empty:
            self.data = self.combine_delta_data(data.reset_index())
            if self.data is not None:
                self.filter_data_by_months()
                self.unique_times = sorted(self.data['time'].unique())
                self.current_time = self.unique_times[0]
                self.cal_current_data()
                return True
        return False

    def load_trade_data(self, file_path):
        """加载交易数据"""
        date_match = re.search(r'\d{8}', file_path)
        date = date_match.group()
        file_path = file_path.replace(date, "%s")
        self.trade_data = loadtrade(date, file_path, tradetime00)
        if self.trade_data is not None:
            self.trade_data = self.trade_data.reset_index()
            self._process_trade_data()
            return True
        return False

    def _process_trade_data(self):
        """处理交易数据"""
        if self.trade_data is not None:
            self._processed_data = self.trade_data.copy()
            # 添加归一化列
            for pnl_type in ['TradePNL', 'TDeltaPNL', 'TVegaPNL']:
                if f'{pnl_type}_normalized' not in self._processed_data.columns:
                    self._processed_data[f'{pnl_type}_normalized'] = self._processed_data[pnl_type] / \
                                                                     self._processed_data['TEdgePNL'].where(
                                                                         self._processed_data['TEdgePNL'] != 0, 1)

            # 设置spot轴的范围，使用0.05-0.95分位数
            spot_min = self._processed_data['Spot'].quantile(0.01)
            spot_max = self._processed_data['Spot'].quantile(0.99)
            self.spot_min = spot_min
            self.spot_max = spot_max

            # 确保 tradetime 列是 datetime 类型
            if not pd.api.types.is_datetime64_any_dtype(self._processed_data['tradetime']):
                self._processed_data['tradetime'] = pd.to_datetime(self._processed_data['tradetime'])

            # 转换时间为数值形式
            self.tradetime_num = date2num(self._processed_data['tradetime'].values)

    def cal_current_data(self):
        """获取当前时间点的数据"""
        timing_stats = {}

        if self.plot_data is not None:
            # 获取当前时间点的数据
            filter_start = time.time()
            self.current_data = self.plot_data[self.plot_data['time'] == self.current_time].copy()
            timing_stats['数据过滤'] = time.time() - filter_start

            if self.current_data.empty:
                print(f"时间点 {self.current_time} 没有数据")
                return

            # 处理交易数据
            if self.trade_data is not None:
                trade_start = time.time()
                current_trades = self.trade_data[self.trade_data['tradetime'] <= self.current_time]
                positions = current_trades.groupby(current_trades['Code'].astype(float))['数量'].sum()
                self.current_data['posi'] = self.current_data['code'].map(positions).fillna(0)
                self.current_data['neg_posi'] = self.current_data['neg_code'].map(positions).fillna(0)
                self.current_data['all_posi'] = self.current_data['posi'] + self.current_data['neg_posi']
                timing_stats['交易处理'] = time.time() - trade_start

            # IV计算
            iv_start = time.time()
            r = self.custom_r if self.custom_r is not None else self.current_data['rf'].iloc[0]
            if self.use_new_iv:
                for option_type in ['', 'neg_']:
                    for price_type in ['bid', 'ask']:
                        col_name = f'{option_type}{price_type}_vol'
                        self.current_data.loc[:, col_name] = greeks.OptionGreeks(
                            self.current_data['forward'].values,
                            self.current_data['K'].values,
                            self.current_data['time2expiry'].values,
                            r,
                            self.current_data[f'{option_type}{price_type}'].values,
                            0.3,  # isigma - initial sigma guess
                            'call' if option_type == '' else 'put'
                        ).calculate_iv()
            timing_stats['IV计算'] = time.time() - iv_start

            # 对数据进行排序
            self.current_data = self.current_data.sort_values('delta')
            if self.last_current_data is None:
                self.last_current_data = self.current_data
                self.svis = {}
            if self.current_params is None:
                self.current_params = {}
                self.last_params = {}
            # SVI处理
            if self.config['svi_enabled']:
                svi_start = time.time()

                # SVI预处理
                preprocess_start = time.time()
                """SVI数据预处理"""
                self.current_data['call_spread'] = self.current_data['ask_vol'] - self.current_data['bid_vol']
                self.current_data['put_spread'] = self.current_data['neg_ask_vol'] - self.current_data['neg_bid_vol']
                self.current_data['call_mid'] = (self.current_data['bid_vol'] + self.current_data['ask_vol']) * 0.5
                self.current_data['put_mid'] = (self.current_data['neg_bid_vol'] + self.current_data[
                    'neg_ask_vol']) * 0.5
                mask = self.current_data['call_spread'] <= self.current_data['put_spread']
                self.current_data['market_sigma'] = np.where(mask,
                                                             self.current_data['call_mid'],
                                                             self.current_data['put_mid'])
                timing_stats['SVI预处理'] = time.time() - preprocess_start

                # 并行SVI拟合
                fit_start = time.time()
                unique_exps = sorted(self.current_data['exp'].unique())

                # 准备每个进程需要的数据
                futures = []

                for exp in unique_exps:
                    exp_data = self.current_data[self.current_data['exp'] == exp].copy()
                    last_exp_data = None
                    if self.last_current_data is not None:
                        last_exp_data = self.last_current_data[self.last_current_data['exp'] == exp].copy()
                        last_params = self.current_params.copy()
                    # 使用进程池并行处理
                    future = self._process_pool.submit(self.fit_svi_model, self.config, exp_data, exp, last_params)
                    futures.append(future)

                # 收集结果
                svi_results = []
                for future in as_completed(futures):
                    try:
                        result = future.result(timeout=20)  # 20秒超时
                        if result is not None:
                            svi_results.append(result)
                    except Exception as e:
                        print(f"Error in SVI fitting process: {e}")

                timing_stats['SVI拟合'] = time.time() - fit_start

                # 更新结果
                update_start = time.time()
                self._update_svi_results(svi_results)
                timing_stats['结果更新'] = time.time() - update_start

                timing_stats['SVI总计'] = time.time() - svi_start

                # 打印性能统计
                if self.debug:
                    print("volfit性能统计: " + ", ".join(
                        [f"{operation}: {duration * 1000:.2f}ms" for operation, duration in timing_stats.items()]))

        return None

    def _update_svi_results(self, svi_results):
        """更新SVI拟合结果"""
        for svi_result in svi_results:
            exp = svi_result[0]
            exp_mask = self.current_data['exp'] == exp
            try:
                if svi_result[1] is not None and len(svi_result[1]) == len(self.current_data.loc[exp_mask, 'market_sigma']):
                    update_data = {
                        'model_vol': svi_result[1],
                        'totalv': svi_result[1]**2*self.current_data.loc[exp_mask, 'time2expiry'],
                        'vol_diff': svi_result[1] - self.current_data.loc[exp_mask, 'market_sigma'],
                        'vol_diff2': self.current_data.loc[exp_mask, 'sigma']-self.current_data.loc[exp_mask, 'market_sigma']
                    }
                    self.current_data.loc[exp_mask, list(update_data.keys())] = pd.DataFrame(update_data)
                else:
                    self.current_data.loc[exp_mask, 'vol_diff'] = 100
                self.svis[exp] = svi_result
            except Exception as e:
                print(f"Error in SVI fitting process: {e}")

            self.current_params[exp] = svi_result[2]

            if svi_result[2] is not None:
                if self.voltime_data is None:
                    self.voltime_data = pd.DataFrame([svi_result[2]], index=[exp])
                else:
                    self.voltime_data.loc[exp] = svi_result[2]

    def filter_data_by_months(self):
        """按月份筛选数据"""
        if self.data is None:
            return False

        if not self.selected_months or "所有月份" in self.selected_months:
            self.plot_data = self.data.copy()
        else:
            self.plot_data = self.data[self.data['exp'].astype(str).isin(self.selected_months)].copy()
        return True

    def get_filtered_trade_data(self, current_time, show_recent=False):
        """获取过滤后的交易数据"""
        if not hasattr(self, '_processed_data') or self._processed_data is None:
            return None

        if show_recent:
            mask = (self._processed_data['tradetime'] >=
                    pd.to_datetime(current_time) - pd.Timedelta(minutes=10)) & \
                   (self._processed_data['tradetime'] <=
                    pd.to_datetime(current_time) + pd.Timedelta(minutes=10))
            return self._processed_data[mask]
        return self._processed_data

    def get_all_months(self):
        """获取所有可用的月份"""
        if self.data is not None:
            return sorted(self.data['exp'].unique())
        return []

    def get_all_codes(self):
        """获取所有代码"""
        if self.data is not None:
            return sorted(self.data['code'].unique())
        return []

    def save_to_influxdb(self, data, measurement, taglist=[], fieldlist=[], timelist=[]):
        """Save current data to InfluxDB asynchronously"""
        self.influx_manager.save_data(data, measurement, taglist, fieldlist, timelist)

    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, '_process_pool'):
            self._process_pool.shutdown()
        if hasattr(self, '_thread_pool'):
            self._thread_pool.shutdown()
        if hasattr(self, 'influx_manager'):
            self.influx_manager.close()


class DataProcessor:
    debug = True

    def __init__(self, _config):
        self.running = True

        # 先初始化 data_manager
        self.data_manager = DataManager(debug=self.debug)

        self.data_manager.md_manager = login(self._handle_data_update)

        self.data_manager.updateparams(custom_r=0.0, use_new_iv=False, config=_config)

    def _handle_data_update(self):
        """处理市场数据更新"""
        start_time = time.time()
        print(
            f"接收到新数据更新... at {datetime.datetime.fromtimestamp(start_time)}") if self.data_manager.debug else ''
        try:
            self.data_manager.load_and_process_data(None, live_mode=True)

            # 保存当前数据
            self.data_manager.save_to_influxdb(self.data_manager.current_data, 'current_data', taglist=['exp', 'K'],
                                               fieldlist=[], timelist=['time'])
            # 合并SVI数据的ATM特征和参数
            voltime_data = self.data_manager.voltime_data
            svi_df = pd.concat([
                pd.DataFrame(voltime_data['atm_features'].tolist()),
                pd.DataFrame(voltime_data['params'].tolist())
            ], axis=1)
            svi_df.index = voltime_data.index
            self.data_manager.save_to_influxdb(svi_df, 'svis', taglist=['index'], fieldlist=[], timelist=[])

            p = f"{datetime.datetime.fromtimestamp(start_time)} 数据处理完成，耗时: {time.time() - start_time:.6f}秒, data_shape: {self.data_manager.current_data.shape},voltime_shape: {self.data_manager.voltime_data.shape}"
            if self.data_manager.debug:
                print(p)
            else:
                print(p, end='\r')
        except Exception as e:
            print(f"Error in data processing: {e}") if self.data_manager.debug else ''

    def run(self):
        """主循环，保持程序运行"""
        while self.running:
            time.sleep(0.01)  # 每10毫秒检查一次，避免占用过多CPU资源

    def stop(self):
        """停止运行"""
        self.running = False  # 设置标志位为False，退出主循环
        """清理资源"""
        if hasattr(self, 'md_manager'):
            self.data_manager.md_manager.data_updated.disconnect(self._handle_data_update)
            logout(self.data_manager.md_manager)
        if hasattr(self, 'data_manager'):
            self.data_manager.cleanup()


if __name__ == "__main__":

    # 主程序入口
    processor = DataProcessor(volconfig)
    try:
        print("程序已启动")
        processor.run()  # 启动主循环
    except KeyboardInterrupt:
        processor.stop()  # 捕获键盘中断，安全退出
        print("程序已退出 by KeyboardInterrupt")
    except Exception as e:
        processor.stop()
        print(f"程序已退出: {e}")
    finally:
        print("程序已退出")
