{"cells": [{"cell_type": "code", "execution_count": 4, "id": "d8d7654c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from utils import *"]}, {"cell_type": "code", "execution_count": 5, "id": "5a1ac1d7", "metadata": {}, "outputs": [], "source": ["# 1. 数据导入\n", "df1 = pd.read_csv('../data/md_20241128_exanic_cffex.csv')\n", "df2 = pd.read_csv('../data/md_20241129_exanic_cffex.csv')"]}, {"cell_type": "code", "execution_count": 6, "id": "ca61288a", "metadata": {}, "outputs": [], "source": ["# 2. 部分特征构造函数定义\n", "def order_imb(df, level=None):\n", "    if level is None:\n", "        level = '1'\n", "    bidvol = 'BidVol' + level\n", "    askvol = 'AskVol' + level\n", "    return (df[bidvol] - df[askvol])/(df[bidvol] + df[askvol])\n", "\n", "def order_price_mid(df, level=None):\n", "    if level is None:\n", "        level = '1'\n", "    bidvol = 'BidVol' + level\n", "    askvol = 'AskVol' + level\n", "    bidprice = 'BidPrice' + level\n", "    askprice = 'AskPrice' + level\n", "    mid = (df['AskPrice1'] + df['BidPrice1'])/2\n", "    return (df[bidvol]*df[askprice] + df[askvol]*df[bidprice]) / (df[bidvol] + df[askvol]) - mid\n", "\n", "def nettradeprice_mid(df, contract_mul=200):\n", "    trade_vol = df['Volume'].diff()\n", "    trade_value = df['TotalValueTraded'].diff()\n", "    mid = (df['AskPrice1'] + df['BidPrice1'])/2\n", "    net_tradeprice = trade_value / (trade_vol*contract_mul)\n", "    net_tradeprice = np.where(pd.isna(net_tradeprice), mid, net_tradeprice)\n", "    return net_tradeprice - mid\n", "\n", "def mom_last(df, window=None):\n", "    if window is None:\n", "        window = 1\n", "    shift_price = df['LastPrice'].shift(window)\n", "    return np.where(pd.isna(shift_price), 0, np.log(df['LastPrice'] / shift_price))\n", "\n", "def reversal_last(df, window=None):\n", "    if window is None:\n", "        window = 10\n", "    ma = df['LastPrice'].rolling(window=window).mean()\n", "    std = df['LastPrice'].rolling(window=window).std()\n", "    \n", "    return np.nan_to_num((df['LastPrice'] - ma)/std, posinf=0, neginf=0)\n", "\n", "def mom_mid(df, window=None):\n", "    if window is None:\n", "        window = 1\n", "    mid = (df['AskPrice1'] + df['BidPrice1'])/2\n", "    shift_price = mid.shift(window)\n", "    return np.where(pd.isna(shift_price), 0, np.log(mid / shift_price))\n", "\n", "def reversal_mid(df, window=None):\n", "    if window is None:\n", "        window = 10\n", "    mid = (df['AskPrice1'] + df['BidPrice1'])/2\n", "    ma = mid.rolling(window=window).mean()\n", "    std = mid.rolling(window=window).std()\n", "    \n", "    return np.nan_to_num((mid - ma)/std, posinf=0, neginf=0)\n", "\n", "\n", "def order_imb_diff(df, window=None, level=None):\n", "    if window is None:\n", "        window=1\n", "    if level is None:\n", "        level='1'\n", "    return order_imb(df=df, level=level).diff(window)\n", "\n", "def ob_depth(df):\n", "    return np.log((df['AskPrice5'] - df['AskPrice1'])/(df['BidPrice1'] - df['BidPrice5']))\n", "\n", "def trade_impact(df, window, contract_mul):\n", "    if window is None:\n", "        window = 10\n", "\n", "    trade_vol = df['Volume'].diff()\n", "    trade_value = df['TotalValueTraded'].diff()\n", "    mid = (df['AskPrice1'] + df['BidPrice1'])/2\n", "    net_tradeprice = trade_value / (trade_vol*contract_mul)\n", "\n", "    # 如果nettradeprice>mid 说明买方主导，价格被买方推动\n", "    trade_price = np.where(net_tradeprice>mid, df['BidPrice1'] , -df['AskPrice1'])\n", "    ema_vol = calculate_ema(trade_vol, window)\n", "    return ema_vol/trade_price\n", "\n", "def trade_flow(df,  contract_mul, window=None):\n", "    if window is None:\n", "        window = 10\n", "    trade_vol = df['Volume'].diff()\n", "    trade_value = df['TotalValueTraded'].diff()\n", "    mid = (df['AskPrice1'] + df['BidPrice1'])/2\n", "    net_tradeprice = trade_value / (trade_vol*contract_mul)\n", "\n", "    # 如果nettradeprice>mid 说明买方主导，价格被买方推动\n", "    trade_vol_with_dir = np.where(net_tradeprice>mid, trade_vol , -trade_vol)\n", "    return calculate_ema(trade_vol_with_dir, window)\n", "\n", "def order_flow_imb(df, level=None,  window=None):\n", "    if window is None:\n", "        window = 1\n", "    if level is None:\n", "        level = '1'\n", "    \n", "    bidvol = 'BidVol' + level\n", "    askvol = 'AskVol' + level\n", "    bidprice = 'BidPrice' + level\n", "    askprice = 'AskPrice' + level\n", "    mid = (df['AskPrice1'] + df['BidPrice1'])/2\n", "   \n", "    ofi_bid = np.where(\n", "    df[bidprice] > df[bidprice].shift(1),  \n", "    df[bidvol],  \n", "    np.where(\n", "        df[bidprice] == df[bidprice].shift(1),  \n", "        df[bidvol].diff(),  \n", "        -df[bidvol]  \n", "    ))\n", "\n", "    ofi_ask = np.where(\n", "    df[askprice] < df[askprice].shift(1),  \n", "    df[askvol],  \n", "    np.where(\n", "        df[askprice] == df[askprice].shift(1),  \n", "        df[askvol].diff(),  \n", "        -df[askvol]  \n", "    ))\n", "\n", "    return pd.Series( ofi_bid - ofi_ask).rolling(window=window, min_periods=1).sum()\n", "    \n", "    \n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "ca624993", "metadata": {}, "outputs": [], "source": ["# 对单因子进行筛选分析\n", "# 计算ic值的函数\n", "def calculate_ic(factor, target):\n", "    \"\"\"\n", "    计算因子值与目标变量之间的 Pearson IC 和 Rank IC\n", "    \"\"\"\n", "    pearson_ic = np.corrcoef(factor, target)[0, 1]\n", "    rank_ic = np.corrcoef(factor.rank(), target.rank())[0, 1]\n", "    return pearson_ic, rank_ic\n", "\n", "# 定义单因子分析函数\n", "def single_factor_analyze(factor, mid,  windows=[5, 10, 30, 60]):\n", "    \"\"\"\n", "    计算单个因子在不同时间窗口的 IC 值或 Rank IC 值。\n", "\n", "    Args:\n", "        factor: 因子序列。\n", "        mid: 中间价时间序列,用于计算target。\n", "        windows (list): 时间窗口列表, 默认为 [5, 10, 30, 100]。\n", "\n", "    Returns:\n", "        None: 直接打印每个时间窗口的 IC 值或 Rank IC 值。\n", "    \"\"\"\n", "    factor = pd.Series(factor)\n", "    results = {}\n", "    \n", "    for window in windows:\n", "        # 计算滚动窗口的因子值和目标值\n", "        target = pd.Series((mid.rolling(window=window*2).mean().shift(-window*2+1) - mid)/mid)\n", "\n", "        # 去除空值\n", "        valid_idx = target.notna() & factor.notna()\n", "        target = target[valid_idx]\n", "        factor = factor[valid_idx]\n", "        print(factor.shape)\n", "        # 计算 IC\n", "        pearson_ic, rank_ic = calculate_ic(factor, target)\n", "        results[f'{window}s'] = {'Pearson IC': pearson_ic, 'Rank IC': rank_ic}\n", "\n", "    # 输出结果\n", "    print(f\"因子在不同时间窗口的 IC 值：\")\n", "    for window, ic_values in results.items():\n", "        print(f\"{window}: Pearson IC = {ic_values['Pearson IC']:.4f}, Rank IC = {ic_values['Rank IC']:.4f}\")\n", "\n", "def factor_analyze( func, daily_data, type_name, windows=[]):\n", "    \n", "    #  存放每一日的target,factor矩阵\n", "    daily_target=[]\n", "    daily_factor=[]\n", "\n", "    for daily_dt in daily_data:\n", "        target_mat = pd.DataFrame()\n", "\n", "        # 示例：type_name = 'IC2412'\n", "        data_type = daily_data[daily_data['Symbol'].str.startswith(type_name)].reset_index(drop=True)\n", "\n", "        # 剔除不需要的列\n", "        data_type_filter = data_type.iloc[:,:35]\n", "        # 剔除全都是0的列\n", "        data_type_filter = data_type_filter.loc[:, (data_type_filter!= 0).any(axis=0)]\n", "        \n", "        # 计算不同窗口的特征\n", "        mid = (data_type_filter['AskPrice1'] + data_type_filter['BidPrice1'])/2\n", "\n", "        for window in windows:\n", "            window_size = \"window\" + str(window)\n", "            target_mat[window_size] = (mid.rolling(window=window*2).mean().shift(-window*2+1) - mid)/mid\n", "        \n", "        daily_target.append(target_mat)\n", "        daily_factor.append(func(data_type_filter), )\n", "            "]}, {"cell_type": "code", "execution_count": 124, "id": "efad832a", "metadata": {}, "outputs": [], "source": ["# 3. 构建训练和测试数据\n", "def data_generate(daily_data, type_name, if_test_factor=0):\n", "    # 示例：type_name = 'IC2412'\n", "    data_type = daily_data[daily_data['Symbol'].str.startswith(type_name)].reset_index(drop=True)\n", "\n", "    # 剔除不需要的列\n", "    data_type_filter = data_type.iloc[:,:35]\n", "    # 剔除全都是0的列\n", "    data_type_filter = data_type_filter.loc[:, (data_type_filter!= 0).any(axis=0)]\n", "    \n", "    # 计算特征 mid未来5s的pctchange\n", "    mid = (data_type_filter['AskPrice1'] + data_type_filter['BidPrice1'])/2\n", "    target = (mid.shift(-10) - mid) / mid\n", "\n", "    # 计算平均涨幅\n", "    # 未来5s mid价格的平均\n", "    mid = (data_type_filter['AskPrice1'] + data_type_filter['BidPrice1'])/2\n", "    forward_rolling_mid = mid.rolling(window=10).mean().shift(-9)\n", "    target = (forward_rolling_mid - mid)/mid\n", "\n", "    data_stand = pd.DataFrame({'TimeStamp':data_type_filter['TimeStamp'],\n", "                          'Feature1':order_imb(data_type_filter),\n", "                          'Feature1-2':order_imb(data_type_filter,level='2'),\n", "                          'Feature1-3':order_imb(data_type_filter,level='3'),\n", "                          'Feature2':order_price_mid(data_type_filter),\n", "                          'Feature2-2':order_price_mid(data_type_filter, level='2'),\n", "                          'Feature2-3':order_price_mid(data_type_filter, level='3'),\n", "                          'Feature3':nettradeprice_mid(data_type_filter),\n", "                          'Feature4':mom_last(data_type_filter),\n", "                          'Feature5':reversal_last(data_type_filter),\n", "                          'Feature4-1':mom_last(data_type_filter, window=10),\n", "                          'Feature5-1':reversal_last(data_type_filter, window=60),\n", "                          'Feature6':order_imb_diff(data_type_filter),\n", "                          'Feature7':ob_depth(data_type_filter),\n", "                          'Feature8':trade_flow(data_type_filter, contract_mul=200, window=1),\n", "                          'Feature8-1':trade_flow(data_type_filter, contract_mul=200, window=60),\n", "                          'Feature9':order_flow_imb(data_type_filter),\n", "                          'Feature9-1':order_flow_imb(data_type_filter, window=60),\n", "                          'target':target})\n", "        \n", "    return data_stand\n"]}, {"cell_type": "code", "execution_count": 126, "id": "0b2a10e4", "metadata": {}, "outputs": [], "source": ["# 示例：对IC2412合约进行预测 进行数据构造\n", "df1_stand = data_generate(df1, 'IC2412')\n", "df2_stand = data_generate(df2, 'IC2412')"]}, {"cell_type": "code", "execution_count": 127, "id": "ba31b48e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 56220 entries, 0 to 56219\n", "Data columns (total 19 columns):\n", " #   Column      Non-Null Count  Dtype  \n", "---  ------      --------------  -----  \n", " 0   TimeStamp   56220 non-null  int64  \n", " 1   Feature1    56220 non-null  float64\n", " 2   Feature1-2  56220 non-null  float64\n", " 3   Feature1-3  56220 non-null  float64\n", " 4   Feature2    56220 non-null  float64\n", " 5   Feature2-2  56220 non-null  float64\n", " 6   Feature2-3  56220 non-null  float64\n", " 7   Feature3    56220 non-null  float64\n", " 8   Feature4    56220 non-null  float64\n", " 9   Feature5    56220 non-null  float64\n", " 10  Feature4-1  56220 non-null  float64\n", " 11  Feature5-1  56220 non-null  float64\n", " 12  Feature6    56220 non-null  float64\n", " 13  Feature7    56220 non-null  float64\n", " 14  Feature8    56220 non-null  float64\n", " 15  Feature8-1  56220 non-null  float64\n", " 16  Feature9    56220 non-null  float64\n", " 17  Feature9-1  56220 non-null  float64\n", " 18  target      56220 non-null  float64\n", "dtypes: float64(18), int64(1)\n", "memory usage: 8.1 MB\n"]}], "source": ["# 由于数据量足够，故暂时考虑剔除掉包含na的数据样本对\n", "df1_stand.dropna(inplace=True)\n", "df2_stand.dropna(inplace=True)\n", "df_all = pd.concat([df1_stand, df2_stand]).reset_index(drop=True)\n", "df_all.info()\n", "df_all.to_csv('./data/temp.csv',index=False)"]}, {"cell_type": "code", "execution_count": 128, "id": "e059b6ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["特征Feature1的ic值:0.07536010284324307\n", "特征Feature1-2的ic值:0.07446512660373625\n", "特征Feature1-3的ic值:0.03556420994053409\n", "特征Feature2的ic值:0.062293050832495755\n", "特征Feature2-2的ic值:0.13248861570702555\n", "特征Feature2-3的ic值:0.09905633265150124\n", "特征Feature3的ic值:0.12288629845007032\n", "特征Feature4的ic值:0.09322034926753425\n", "特征Feature5的ic值:0.106148212757001\n", "特征Feature4-1的ic值:0.035913872341303464\n", "特征Feature5-1的ic值:0.045995505250295395\n", "特征Feature6的ic值:0.0677687493468536\n", "特征Feature7的ic值:0.14226153976787256\n", "特征Feature8的ic值:0.0663620722391492\n", "特征Feature8-1的ic值:-0.020233684094352694\n", "特征Feature9的ic值:0.12456906806753995\n", "特征Feature9-1的ic值:0.03151798951902264\n"]}], "source": ["# 单因子分析\n", "features = [name for name in df_all.columns if name.startswith('Feature')]\n", "# 4. 对各个特征进行单因子分析\n", "def features_ic(df, name):\n", "    print(f\"特征{name}的ic值:{np.corrcoef(df['target'], df[name])[0, 1]}\")\n", "\n", "for feature in features:\n", "    features_ic(df_all, feature)"]}, {"cell_type": "markdown", "id": "b8ddddcc", "metadata": {}, "source": ["## 特征相关性展示"]}, {"cell_type": "code", "execution_count": 12, "id": "6d95f79c", "metadata": {}, "outputs": [], "source": ["feature = df_all.loc[:,df_all.columns.str.startswith('Feature')]"]}, {"cell_type": "code", "execution_count": 13, "id": "c19fc8d2", "metadata": {}, "outputs": [], "source": ["correlation_matrix = feature.dropna().corr()"]}, {"cell_type": "code", "execution_count": 14, "id": "d24cacd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Correlation matrix:\n", "             Feature1  Feature1-2  Feature1-3  Feature2  Feature2-2  \\\n", "Feature1    1.000000    0.018858   -0.011040  0.863311    0.014847   \n", "Feature1-2  0.018858    1.000000    0.029296  0.017509    0.815679   \n", "Feature1-3 -0.011040    0.029296    1.000000 -0.011131    0.024164   \n", "Feature2    0.863311    0.017509   -0.011131  1.000000    0.015915   \n", "Feature2-2  0.014847    0.815679    0.024164  0.015915    1.000000   \n", "Feature2-3 -0.016969    0.043819    0.839709 -0.015837    0.222933   \n", "Feature3    0.038506   -0.000178   -0.028546  0.015713    0.097953   \n", "Feature4   -0.021664    0.019681    0.038150 -0.015594    0.000598   \n", "Feature5   -0.022818    0.025199    0.045532 -0.008318    0.021010   \n", "Feature4-1 -0.024521    0.013001    0.033173 -0.013163    0.012857   \n", "Feature5-1 -0.025136   -0.007547    0.001565 -0.019755   -0.030564   \n", "Feature6    0.617357   -0.122039   -0.059928  0.538279   -0.080254   \n", "Feature7   -0.021904    0.027140    0.029158 -0.018780    0.302546   \n", "Feature8   -0.007879   -0.002341   -0.024556 -0.014818    0.069717   \n", "Feature8-1 -0.011638   -0.015170   -0.017575 -0.001314   -0.010276   \n", "Feature9   -0.035823    0.058074    0.047294 -0.030151    0.039421   \n", "Feature9-1 -0.012095   -0.016104   -0.023086 -0.016643   -0.038141   \n", "\n", "            Feature2-3  Feature3  Feature4  Feature5  Feature4-1  Feature5-1  \\\n", "Feature1     -0.016969  0.038506 -0.021664 -0.022818   -0.024521   -0.025136   \n", "Feature1-2    0.043819 -0.000178  0.019681  0.025199    0.013001   -0.007547   \n", "Feature1-3    0.839709 -0.028546  0.038150  0.045532    0.033173    0.001565   \n", "Feature2     -0.015837  0.015713 -0.015594 -0.008318   -0.013163   -0.019755   \n", "Feature2-2    0.222933  0.097953  0.000598  0.021010    0.012857   -0.030564   \n", "Feature2-3    1.000000  0.042040  0.036761  0.064435    0.053381   -0.017339   \n", "Feature3      0.042040  1.000000  0.088272  0.065803    0.012624    0.063113   \n", "Feature4      0.036761  0.088272  1.000000  0.561286    0.322098    0.273555   \n", "Feature5      0.064435  0.065803  0.561286  1.000000    0.694982    0.591588   \n", "Feature4-1    0.053381  0.012624  0.322098  0.694982    1.000000    0.682079   \n", "Feature5-1   -0.017339  0.063113  0.273555  0.591588    0.682079    1.000000   \n", "Feature6     -0.046942  0.113641 -0.045300 -0.021429   -0.013672   -0.008699   \n", "Feature7      0.380566  0.081466  0.035874  0.111767    0.098031   -0.016408   \n", "Feature8      0.028073  0.572454 -0.075561 -0.062319   -0.062408   -0.028778   \n", "Feature8-1   -0.010706  0.093101 -0.051187 -0.060711   -0.123942   -0.093282   \n", "Feature9      0.051207 -0.229876  0.250945  0.276374    0.179187    0.169705   \n", "Feature9-1   -0.048147 -0.011472  0.054984  0.191170    0.257189    0.500731   \n", "\n", "            Feature6  Feature7  Feature8  Feature8-1  Feature9  Feature9-1  \n", "Feature1    0.617357 -0.021904 -0.007879   -0.011638 -0.035823   -0.012095  \n", "Feature1-2 -0.122039  0.027140 -0.002341   -0.015170  0.058074   -0.016104  \n", "Feature1-3 -0.059928  0.029158 -0.024556   -0.017575  0.047294   -0.023086  \n", "Feature2    0.538279 -0.018780 -0.014818   -0.001314 -0.030151   -0.016643  \n", "Feature2-2 -0.080254  0.302546  0.069717   -0.010276  0.039421   -0.038141  \n", "Feature2-3 -0.046942  0.380566  0.028073   -0.010706  0.051207   -0.048147  \n", "Feature3    0.113641  0.081466  0.572454    0.093101 -0.229876   -0.011472  \n", "Feature4   -0.045300  0.035874 -0.075561   -0.051187  0.250945    0.054984  \n", "Feature5   -0.021429  0.111767 -0.062319   -0.060711  0.276374    0.191170  \n", "Feature4-1 -0.013672  0.098031 -0.062408   -0.123942  0.179187    0.257189  \n", "Feature5-1 -0.008699 -0.016408 -0.028778   -0.093282  0.169705    0.500731  \n", "Feature6    1.000000 -0.000677  0.044991    0.004226 -0.079794   -0.004169  \n", "Feature7   -0.000677  1.000000  0.062149    0.017281  0.053930   -0.060533  \n", "Feature8    0.044991  0.062149  1.000000    0.134140 -0.205385   -0.029151  \n", "Feature8-1  0.004226  0.017281  0.134140    1.000000 -0.015903   -0.062748  \n", "Feature9   -0.079794  0.053930 -0.205385   -0.015903  1.000000    0.153625  \n", "Feature9-1 -0.004169 -0.060533 -0.029151   -0.062748  0.153625    1.000000  \n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"Correlation matrix:\\n\", correlation_matrix)\n", "\n", "# 绘制相关系数矩阵的热力图\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)\n", "plt.title('Correlation Matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 15, "id": "8e7e6dcc", "metadata": {}, "outputs": [], "source": ["# model\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.model_selection import train_test_split, TimeSeriesSplit, GridSearchCV\n", "from sklearn.preprocessing import StandardScaler  \n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, mean_absolute_percentage_error\n", "\n", "# data = pd.read_csv('./data/temp.csv')\n", "data = df_all\n", "# data = data[:1000]\n", "# 2. 数据预处理，提取特征\n", "X = data.drop(['target'], axis=1)\n", "y = data['target']\n", "if 'TimeStamp' in X.columns:\n", "    X.drop(['TimeStamp'], axis=1, inplace=True)\n", "\n", "# 3. 划分训练集和测试集: 如果包含label列，则有限按照label进行划分，标签0为训练集，1为测试集\n", "if 'label' in X.columns:\n", "    X.drop(['TimeStamp'], axis=1, inplace=True)\n", "    X_train, y_train = X[X.label == 0], y[X.label == 0]\n", "    X_test, y_test = X[X.label == 1], y[X.label == 1]\n", "else: \n", "    # 默认方法\n", "    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, shuffle=False)\n", "\n", "# 使用训练集的均值和标准差对训练集和测试集进行归一化\n", "scaler = StandardScaler()\n", "X_train = scaler.fit_transform(X_train)  # 拟合训练集并转换\n", "X_test = scaler.transform(X_test)        # 使用训练集的参数转换测试集\n", "\n", "# 5. 构建随机森林模型\n", "model = RandomForestRegressor(random_state=42, max_depth=10, min_samples_leaf=4, min_samples_split=10, n_estimators=200 )\n"]}, {"cell_type": "code", "execution_count": 106, "id": "048730ea", "metadata": {}, "outputs": [], "source": ["model.fit(X_train, y_train)\n", "y_pred = model.predict(X_test)"]}, {"cell_type": "code", "execution_count": 107, "id": "c6457233", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["特征重要性:\n", " Feature3      0.144408\n", "Feature9      0.112654\n", "Feature7      0.097739\n", "Feature8-1    0.089242\n", "Feature8      0.080539\n", "Feature2-2    0.071260\n", "Feature4-1    0.063104\n", "Feature9-1    0.046972\n", "Feature2-3    0.041855\n", "Feature4      0.041237\n", "Feature6      0.038220\n", "Feature5-1    0.036867\n", "Feature5      0.036757\n", "Feature2      0.035048\n", "Feature1      0.027113\n", "Feature1-2    0.020869\n", "Feature1-3    0.016115\n", "dtype: float64\n"]}], "source": ["# 输出特征重要性\n", "feature_importance = pd.Series(model.feature_importances_, index=X.columns)\n", "print(\"特征重要性:\\n\", feature_importance.sort_values(ascending=False))\n"]}, {"cell_type": "code", "execution_count": 25, "id": "3b6cf305", "metadata": {}, "outputs": [], "source": ["# 9. 定义评价函数\n", "def evaluate_model(y_true, y_pred, set_name):\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    # mape = mean_absolute_percentage_error(y_true, y_pred)\n", "    \n", "    # 计算准确率: 方向预测正确的概率\n", "    accuracy = np.mean(np.sign(y_true) == np.sign(y_pred) )\n", "\n", "\n", "    # 不同涨跌幅大小的具体预测准确率\n", "    big_up = np.mean(np.sign(y_pred[y_true > 5e-5]) == np.sign(y_true[y_true > 5e-5]))\n", "    big_down = np.mean(np.sign(y_pred[y_true < -5e-5]) == np.sign(y_true[y_true < -5e-5]))\n", "    print(f'较大涨幅正确率:{big_up}')\n", "    print(f'较大跌幅正确率:{big_down}')\n", "\n", "\n", "    # alpha_size_mean\n", "    alpha_size_mean= y_pred.mean()\n", "    print(f'alpha_size_mean:{alpha_size_mean}')\n", "    \n", "    \n", "    # 输出评价结果\n", "    print(f'Evaluation on {set_name}:')\n", "    print(f'MSE: {mse}')\n", "    print(f'MAE: {mae}')\n", "    print(f'R²: {r2}')\n", "    # print(f'MAPE: {mape}')\n", "    print(f'Accuracy: {accuracy * 100:.2f}%')\n", "    print('-' * 40)\n", "\n", "# 10. 在训练集和测试集上评估模型\n", "evaluate_model(y_test, y_pred, 'Test Set')        # 测试集评价\n"]}, {"cell_type": "code", "execution_count": 26, "id": "1be23e55", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["较大涨幅正确率:0.638780245276765\n", "较大跌幅正确率:0.6029373024236038\n", "alpha_size_mean:1.4784075774503877e-06\n", "Evaluation on Train Set:\n", "MSE: 2.0183767646847132e-08\n", "MAE: 0.00010511675045419553\n", "R²: 0.07017525922365053\n", "Accuracy: 58.70%\n", "----------------------------------------\n", "较大涨幅正确率:0.6622854077253219\n", "较大跌幅正确率:0.6197574532592218\n", "alpha_size_mean:1.0718585103178075e-06\n", "Evaluation on Test Set:\n", "MSE: 1.8175656125276943e-08\n", "MAE: 0.00010313763116470443\n", "R²: 0.09244622287171955\n", "Accuracy: 60.40%\n", "----------------------------------------\n"]}], "source": ["from sklearn.linear_model import LinearRegression\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import r2_score, mean_squared_error\n", "# 创建线性回归模型\n", "model = LinearRegression()\n", "\n", "# 拟合模型\n", "model.fit(X_train, y_train)\n", "y_pred_train = model.predict(X_train)\n", "\n", "# 在测试集上进行预测\n", "Y_pred_ols = model.predict(X_test)\n", "\n", "# 在训练集和测试集上评估模型\n", "\n", "evaluate_model(y_train, y_pred_train, 'Train Set')  \n", "evaluate_model(y_test, Y_pred_ols, 'Test Set')        "]}, {"cell_type": "code", "execution_count": 28, "id": "899a77ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:                 target   R-squared:                       0.070\n", "Model:                            OLS   Adj. R-squared:                  0.070\n", "Method:                 Least Squares   F-statistic:                     199.6\n", "Date:                Wed, 15 Jan 2025   Prob (F-statistic):               0.00\n", "Time:                        13:48:48   Log-Likelihood:             3.3463e+05\n", "No. Observations:               44976   AIC:                        -6.692e+05\n", "Df Residuals:                   44958   BIC:                        -6.691e+05\n", "Df Model:                          17                                         \n", "Covariance Type:            nonrobust                                         \n", "==============================================================================\n", "                 coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const       1.478e-06    6.7e-07      2.206      0.027    1.65e-07    2.79e-06\n", "x1          6.969e-06   1.43e-06      4.860      0.000    4.16e-06    9.78e-06\n", "x2         -2.474e-06   1.37e-06     -1.810      0.070   -5.15e-06    2.05e-07\n", "x3         -2.284e-06   1.65e-06     -1.385      0.166   -5.52e-06    9.48e-07\n", "x4         -1.594e-07   1.34e-06     -0.119      0.905   -2.78e-06    2.46e-06\n", "x5          1.404e-05   1.48e-06      9.497      0.000    1.11e-05    1.69e-05\n", "x6          7.326e-06   1.81e-06      4.042      0.000    3.77e-06    1.09e-05\n", "x7          1.698e-05   8.63e-07     19.683      0.000    1.53e-05    1.87e-05\n", "x8          4.762e-06   8.29e-07      5.745      0.000    3.14e-06    6.39e-06\n", "x9          1.089e-05   1.11e-06      9.857      0.000    8.73e-06    1.31e-05\n", "x10        -1.272e-05   1.06e-06    -11.998      0.000   -1.48e-05   -1.06e-05\n", "x11         1.249e-06   1.07e-06      1.168      0.243   -8.47e-07    3.34e-06\n", "x12         6.014e-06   8.78e-07      6.851      0.000    4.29e-06    7.74e-06\n", "x13         1.026e-05   9.09e-07     11.280      0.000    8.47e-06     1.2e-05\n", "x14         2.328e-06   8.35e-07      2.787      0.005    6.91e-07    3.96e-06\n", "x15        -5.203e-06   6.82e-07     -7.627      0.000   -6.54e-06   -3.87e-06\n", "x16          1.86e-05   7.36e-07     25.250      0.000    1.72e-05       2e-05\n", "x17         8.934e-07   7.89e-07      1.133      0.257   -6.53e-07    2.44e-06\n", "==============================================================================\n", "Omnibus:                     2844.569   <PERSON><PERSON><PERSON>-<PERSON>:                   0.296\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            11549.136\n", "Skew:                           0.184   Prob(JB):                         0.00\n", "Kurtosis:                       5.455   Cond. No.                         6.70\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n"]}], "source": ["import pandas as pd\n", "import statsmodels.api as sm\n", "\n", "# 添加常数项（截距）\n", "X_train_const = sm.add_constant(X_train)\n", "X_test_const = sm.add_constant(X_test)\n", "# 拟合 OLS 模型\n", "model = sm.OLS(y_train, X_train_const)\n", "results = model.fit()\n", "\n", "# 输出回归结果\n", "print(results.summary())\n"]}, {"cell_type": "code", "execution_count": null, "id": "2fa2aeae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "a11caf2739b322e743177d7012cedd9f56f5af391e0eaf7be97ba604ec2bc2b9"}, "kernelspec": {"display_name": "Python 3.8.13", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}