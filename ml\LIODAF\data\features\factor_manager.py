"""
因子管理模块
用于集中管理和维护所有因子
@author: lining
"""

from typing import Dict, List, Optional, Callable
import pandas as pd
from dataclasses import dataclass
from enum import Enum


class FactorCategory(Enum):
    """因子类别枚举"""

    BASIC = "basic"  # 基础因子
    ADVANCED = "advanced"  # 高级因子
    DIY = "diy"  # 自定义因子
    TECHNICAL = "technical"  # 技术指标因子

    TIME_SERIES = "time_series"  # 时间序列因子
    INTERACTION = "interaction"  # 交互因子


@dataclass
class Factor:
    """因子定义类
    传入数据格式:
    data: pd.DataFrame
    数据格式:
    all_col = [
    'Symbol', 'timestamp_str', 'Volume',
    'BidPrice5', 'BidPrice4', 'BidPrice3', 'BidPrice2', 'BidPrice1',
    'AskPrice1', 'AskPrice2', 'AskPrice3', 'AskPrice4', 'AskPrice5',
    'BidVol5', 'BidVol4', 'BidVol3', 'BidVol2', 'BidVol1',
    'AskVol1', 'AskVol2', 'AskVol3', 'AskVol4', 'AskVol5',
    'LastPrice', 'High', 'Low', 'TotalValueTraded', 'ForQuoteSysID', 'Source', 'State',
    ]
    """

    name: str  # 因子名称
    category: FactorCategory  # 因子类别
    description: str  # 因子描述
    calculation: Callable  # 因子计算函数,需要返回pd.Series,输入pd.DataFrame
    dependencies: List[str]  # 依赖的其他因子
    parameters: Dict = None  # 因子参数
    validation_rules: List[Callable] = None  # 因子验证规则
    source: str = "lining"  # 因子来源


class FactorManager:
    """因子管理器"""

    def __init__(self):
        self._factors: Dict[str, Factor] = {}

    def register_factor(self, factor: Factor):
        """注册新因子"""
        self._factors[factor.name] = factor

    def get_factor(self, name: str) -> Optional[Factor]:
        """获取指定因子"""
        return self._factors.get(name)

    def get_factors_by_category(self, category: FactorCategory) -> List[Factor]:
        """获取指定类别的所有因子"""
        return [f for f in self._factors.values() if f.category == category]

    def calculate_factor(self, name: str, data: pd.DataFrame) -> pd.Series:
        """计算指定因子"""
        factor = self.get_factor(name)
        if not factor:
            raise ValueError(f"因子 {name} 未注册")
        return factor.calculation(data)

    def validate_factor(self, name: str, data: pd.DataFrame) -> bool:
        """验证因子有效性"""
        factor = self.get_factor(name)
        if not factor or not factor.validation_rules:
            return True

        for rule in factor.validation_rules:
            if not rule(data):
                return False
        return True

    def get_factor_documentation(self, all_features: Dict[str, List[str]]) -> pd.DataFrame:
        """生成因子DataFrame，可以保存为CSV"""
        # 创建存储数据的列表
        data = []
        # 填充数据
        x = 0
        for category in FactorCategory:
            factors = self.get_factors_by_category(category)
            for factor in factors:
                x += 1
                params_str = str(factor.parameters) if factor.parameters else ""
                dependencies_str = ", ".join(factor.dependencies)
                data.append(
                    {
                        "序号": f"{x:03d}",
                        "因子名称": factor.name,
                        "类别": category.value,
                        "使用情况": all_features[factor.name][0] if factor.name in all_features else "未使用",
                        "计算时间": round(all_features[factor.name][1], 4) if factor.name in all_features else 0,
                        "描述": factor.description,
                        "依赖": dependencies_str,
                        "参数": params_str,
                    }
                )

        return pd.DataFrame(data)


# 创建全局因子管理器实例
factor_manager = FactorManager()

from .libs import *
