
"""
模型训练模块
@author: lining
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import RobustScaler
import warnings
from models.set_model import set_model
from utils.utils import log_print
from core import config
from models.model_evaluate import model_evaluate
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
import time
import pickle

class OrderBookModelTrainer:
    

    def __init__(self, feature_cols=None, target_cols=None, model_type=None):
        
        self.feature_cols = feature_cols
        self.target_cols = target_cols
        self.model_type = model_type
        self.models = {}
        self.feature_importances = {}
        self.predictions = {}
        self.output_dir = config.OUTDIR
        
        warnings.filterwarnings("ignore")

    def train_models(self, x_train, y_train_dict, params=None, model_parameter_search=False, cv_n_splits=3):
        
        
        for target in self.target_cols:
            log_print(f"训练目标 {target} 的{self.model_type}模型...")
            model = set_model(model_type=self.model_type, params=params)
            if model_parameter_search:
                model = self.model_parameter_search(
                    x_train[target], 
                    y_train_dict[target], 
                    model, 
                    config.MODEL_SETTING[self.model_type]['param_grid'],
                    n_splits=cv_n_splits
                )
            else:
                
                model.fit(x_train[target], y_train_dict[target])

            
            self.models[target] = model
            pickle.dump(model, open(self.output_dir + f"/model_{target}.dat","wb"))

    def predict(self, X: pd.DataFrame, target=None):
        
        if not self.models:
            log_print("警告：没有训练好的模型", 'warning')
            return None

        if target is not None and target not in self.models:
            log_print(f"警告：目标 {target} 的模型未训练", 'warning')
            return None

        targets = [target] if target is not None else self.target_cols
        results = {}

        for t in targets:
            
            missing_features = [col for col in self.feature_cols if col not in X.columns]
            if missing_features:
                log_print(f"警告：输入数据缺少以下特征列: {missing_features}", 'warning')
                raise ValueError(f"输入数据缺少以下特征列: {missing_features}")

            
            X_pred = X[self.feature_cols].copy()

            X_pred = X_pred.fillna(0)

            
            model_prediction = self.models[t].predict(X_pred)
            
            if len(model_prediction) != len(X_pred):
                
                model_prediction = np.pad(model_prediction, (0, len(X_pred) - len(model_prediction)), mode='constant', constant_values=0)

            results[f"{t}_pred"] = model_prediction
            
        return pd.DataFrame(results, index=X.index)

    def model_selection(self, x_train, y):
        
        
        best_model = None
        best_performance = float('-inf')
        model_performances = {}

        for model_type in ['xgboost', 'linear','decision_tree','extra_trees',]:
            self.train_models(
                x_train, 
                y, 
                model_type=model_type, 
                params=config.MODEL_SETTING[model_type]['params'],
                model_parameter_search=config.MODEL_PARAMETER_SEARCH,
                cv_n_splits=config.CV_N_SPLITS
            )
            pred = self.predict(x_train[self.target_cols[0]])

            train_eval = model_evaluate(y[self.target_cols[0]], pred[self.target_cols[0] + '_pred'], 'train')

            
            current_score = train_eval['r2']
            if current_score > best_performance:
                best_performance = current_score
                best_model = model_type

            log_print(f"模型 {model_type} 的性能: {current_score:.4f}")

            model_performances[model_type] = train_eval

        log_print(f"最佳模型: {best_model}, R2分数: {round(best_performance, 4)}")

        return model_performances, best_model
    
    def model_parameter_search(self, X_train_sd, y_train, model, param_grid, n_splits=3):
        
        
        tscv = TimeSeriesSplit(n_splits=n_splits)  
        
        
        scoring_metrics = {
            'r2': 'r2',                        
            'neg_mse': 'neg_mean_squared_error', 
            'neg_rmse': 'neg_root_mean_squared_error', 
            'neg_mae': 'neg_mean_absolute_error'  
        }
        
        
        scoring = scoring_metrics['r2']
        
        
        log_print(f"开始参数搜索，模型类型: {type(model).__name__}", 'info')
        log_print(f"参数网格: {param_grid}", 'debug')
        log_print(f"评分指标: {scoring}", 'debug')
        log_print(f"交叉验证: 时间序列分割(n_splits={n_splits})", 'debug')
        
        
        grid_search = GridSearchCV(
            estimator=model, 
            param_grid=param_grid, 
            cv=tscv, 
            scoring=scoring, 
            n_jobs=-1,
            return_train_score=True,  
            verbose=1  
        )
        
        
        log_print("开始执行参数网格搜索...", 'info')
        start_time = time.time()
        grid_search.fit(X_train_sd, y_train)
        search_time = time.time() - start_time
        log_print(f"参数搜索完成，耗时: {search_time:.2f}秒", 'info')
        
        try:
            
            best_score = grid_search.best_score_
            best_params = grid_search.best_params_
            log_print(f"最佳参数: {best_params}", 'info')
            log_print(f"最佳得分: {best_score:.4f}", 'info')
            
            
            cv_results = grid_search.cv_results_
            top_indices = np.argsort(-cv_results['mean_test_score'])[:3]
            
            log_print("前3名参数组合:", 'info')
            for i, idx in enumerate(top_indices):
                mean_score = cv_results['mean_test_score'][idx]
                std_score = cv_results['std_test_score'][idx]
                params = cv_results['params'][idx]
                log_print(f"#{i+1} 得分: {mean_score:.4f} (±{std_score:.4f}), 参数: {params}", 'info')
            
            
            model = grid_search.best_estimator_
        except Exception as e:
            log_print(f"获取最佳参数时出错: {e}", 'error')
            log_print("使用原始模型参数", 'warning')
            return model

        return model