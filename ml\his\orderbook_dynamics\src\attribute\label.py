"""
Label implementation for orderbook dynamics.
Labels are used to classify order book movements for prediction.
"""
from enum import Enum
from typing import Generic, List, Optional, TypeVar, Callable
from ..models import Cell, OrderBook
from .basic_attribute import BasicSet


T = TypeVar('T')


class Label:
    """
    Base class for labels that classify orderbook state changes.
    """
    def __init__(self, values: List[T]):
        """Initialize with possible label values."""
        self.values = values

    def __call__(self, current: OrderBook, future: OrderBook) -> Optional[T]:
        """Classify the change between current and future order books."""
        raise NotImplementedError("Subclasses must implement this method")

    def encode(self, label_encode: 'LabelEncode[T]') -> 'Label':
        """Encode the label using a LabelEncode instance."""
        values_int = [label_encode.encode(v) for v in self.values]
        
        class EncodedLabel(Label):
            def __call__(self, current: OrderBook, future: OrderBook) -> Optional[int]:
                result = self.__call__(current, future)
                if result is not None:
                    return label_encode.encode(result)
                return None
                
        return EncodedLabel(values_int)


class LabelEncode:
    """Base class for encoding labels as integers for machine learning."""
    def __init__(self, values: List[T]):
        self.values = values
        self._value_to_idx = {value: idx for idx, value in enumerate(values)}
        
    @property
    def num_classes(self) -> int:
        """Get the number of classes."""
        return len(self.values)
        
    def decode(self, idx: int) -> T:
        """Decode an integer to a label value."""
        if 0 <= idx < len(self.values):
            return self.values[idx]
        raise ValueError(f"Invalid index: {idx}")
        
    def encode(self, label: T) -> int:
        """Encode a label value to an integer."""
        if label in self._value_to_idx:
            return self._value_to_idx[label]
        raise ValueError(f"Unknown label: {label}")


class MeanPriceMove(Enum):
    """Enum for classifying mean price movements."""
    UP = "UP"
    DOWN = "DOWN"
    STATIONARY = "STATIONARY"


class MeanPriceMoveEncode(LabelEncode):
    """Encoder for MeanPriceMove labels."""
    def __init__(self):
        super().__init__([MeanPriceMove.UP, MeanPriceMove.DOWN, MeanPriceMove.STATIONARY])


class MeanPriceMovementLabel(Label):
    """Label for classifying mean price movements."""
    def __init__(self):
        super().__init__([MeanPriceMove.UP, MeanPriceMove.DOWN, MeanPriceMove.STATIONARY])
        self.basic_set = BasicSet()
        
    def __call__(self, current: OrderBook, future: OrderBook) -> Optional[MeanPriceMove]:
        """Classify the mean price movement between current and future order books."""
        current_mean_price = self.basic_set.mean_price()(current)
        future_mean_price = self.basic_set.mean_price()(future)
        
        if current_mean_price.is_missing or future_mean_price.is_missing:
            return None
            
        current_value = current_mean_price.get()
        future_value = future_mean_price.get()
        
        if current_value == future_value:
            return MeanPriceMove.STATIONARY
        elif current_value > future_value:
            return MeanPriceMove.DOWN
        else:
            return MeanPriceMove.UP 