import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from factors.ob_std import *
from factors.ob_extra_YA import *

# 构建训练和测试数据
def data_generate(daily_data, type_name):
    data_type = daily_data[daily_data['Symbol'].str.startswith(type_name)].reset_index(drop=True)
    # 剔除不需要的列
    data_type_filter = data_type.iloc[1:,:35].reset_index()  #.reset_index改正过
    # 剔除全都是0的列
    data_type_filter = data_type_filter.loc[:, (data_type_filter!= 0).any(axis=0)]
    # 计算特征 mid未来5s的pctchange
    mid = (data_type_filter['AskPrice1'] + data_type_filter['BidPrice1'])/2
    target = (mid.shift(-10) - mid) / mid

    data_stand = pd.DataFrame({'TimeStamp':data_type_filter['TimeStamp'],
                               # 'Feature1': order_imb(data_type_filter),
                               # 'Feature2': order_price_mid(data_type_filter),
                               # 'Feature3': nettradeprice_mid(data_type_filter),
                               # 'Feature4': mom_last(data_type_filter),
                               # 'Feature5': reversal_last(data_type_filter),
                               #
                               # 'Feature_long_short_mean_diff': feature_long_short_mean_diff(data_type_filter),
                               # 'Feature_long_short_min_diff': feature_long_short_min_diff(data_type_filter),
                               # 'Feature_corr_value_lastprice': feature_corr_value_lastprice(data_type_filter),
                               #
                               # 'Feature_vwap_1': feature_vwap_1(data_type_filter),
                               # 'Feature_vwap_2': feature_vwap_2(data_type_filter),
                               # 'Feature_vwap_3': feature_vwap_3(data_type_filter),
                               #
                               # 'Feature_vol_mul_rank': feature_vol_mul_rank(data_type_filter),
                               # 'Feature_price_mul_rank': feature_price_mul_rank(data_type_filter),
                               # 'Feature_rising_n_falling_trends': feature_rising_n_falling_trends(data_type_filter),
                               # 'Feature_rising_n_falling_trends2': feature_rising_n_falling_trends_2(data_type_filter),
                               # 'Feature_sign_vol_lastprice': feature_sign_vol_lastprice(data_type_filter),

                               # 'Feature_rank_cov_1': feature_rank_cov_1(data_type_filter),
                               # 'Feature_rank_cov_2': feature_rank_cov_2(data_type_filter),
                               # 'Feature_rank_cov_3': feature_rank_cov_3(data_type_filter),
                               # 'Feature_rank_cov_4': feature_rank_cov_4(data_type_filter),
                               # 'Feature_rank_cov_5': feature_rank_cov_5(data_type_filter),
                               # 'Feature_rank_cov_6': feature_rank_cov_6(data_type_filter),
                               #
                               # 'Feature_rank_rolling_std_1': feature_rank_rolling_std_1(data_type_filter),
                               # 'Feature_rank_rolling_std_2': feature_rank_rolling_std_2(data_type_filter),
                               # 'Feature_rank_rolling_std_3': feature_rank_rolling_std_3(data_type_filter),
                               # 'Feature_rank_rolling_std_4': feature_rank_rolling_std_4(data_type_filter),
                               #
                               # 'Feature_alpha_31': alpha_31(data_type_filter),
                               # 'Feature_alpha_32_1': alpha_32_1(data_type_filter),
                               # 'Feature_alpha_32_2': alpha_32_2(data_type_filter),
                               # 'Feature_alpha_32_3': alpha_32_3(data_type_filter),
                               # 'Feature_alpha_32_4': alpha_32_4(data_type_filter),
                               # 'Feature_alpha_34_1': alpha_34_1(data_type_filter),
                               # 'Feature_alpha_34_2': alpha_34_2(data_type_filter),
                               # 'Feature_alpha_34_3': alpha_34_3(data_type_filter),
                               # 'Feature_alpha_34_4': alpha_34_4(data_type_filter),
                               # 'Feature_alpha_34_5': alpha_34_5(data_type_filter),
                               # 'Feature_alpha_34_6': alpha_34_6(data_type_filter),
                               #
                               #
                               # 'Feature_alpha_35': alpha_35(data_type_filter),
                               # 'Feature_alpha_37': alpha_37(data_type_filter),
                               # 'Feature_alpha_38': alpha_38(data_type_filter),
                               # 'Feature_alpha_39': alpha_39(data_type_filter),
                               # 'Feature_alpha_40': alpha_40(data_type_filter),
                               #
                               # 'Feature_alpha_41': alpha_41(data_type_filter),
                               # 'Feature_alpha_42': alpha_42(data_type_filter),
                               # 'Feature_alpha_43': alpha_43(data_type_filter),
                               # 'Feature_alpha_44': alpha_44(data_type_filter),
                               # 'Feature_alpha_45': alpha_45(data_type_filter),
                               # 'Feature_alpha_46': alpha_46(data_type_filter),
                               # 'Feature_alpha_47': alpha_47(data_type_filter),
                               # 'Feature_alpha_49': alpha_49(data_type_filter),
                               #
                               # 'Feature_alpha_51': alpha_51(data_type_filter),
                               # 'Feature_alpha_52': alpha_52(data_type_filter),
                               # 'Feature_alpha_53': alpha_53(data_type_filter),
                               # 'Feature_alpha_55': alpha_55(data_type_filter),
                               # 'Feature_alpha_56': alpha_56(data_type_filter),
                               # 'Feature_alpha_57': alpha_57(data_type_filter),
                               # 'Feature_alpha_60': alpha_60(data_type_filter),
                               #
                               # 'Feature_alpha_62': alpha_62(data_type_filter),
                               # 'Feature_alpha_65': alpha_65(data_type_filter),
                               # 'Feature_alpha_66': alpha_66(data_type_filter),
                               # 'Feature_alpha_68': alpha_68(data_type_filter),
                               #
                               # 'Feature_alpha_71': alpha_71(data_type_filter),
                               # 'Feature_alpha_72': alpha_72(data_type_filter),
                               # 'Feature_alpha_73': alpha_73(data_type_filter),
                               # 'Feature_alpha_75': alpha_75(data_type_filter),
                               # 'Feature_alpha_77': alpha_77(data_type_filter),
                               # 'Feature_alpha_78': alpha_78(data_type_filter),
                               #
                               # 'Feature_alpha_85': alpha_85(data_type_filter),
                               # 'Feature_alpha_86': alpha_86(data_type_filter),
                               # 'Feature_alpha_88': alpha_88(data_type_filter),
                               #
                               'Feature_alpha_92': alpha_92(data_type_filter),
                               'Feature_alpha_94': alpha_94(data_type_filter),
                               'Feature_alpha_96': alpha_96(data_type_filter),
                               'Feature_alpha_98': alpha_98(data_type_filter),
                               'Feature_alpha_99': alpha_99(data_type_filter),
                               'Feature_alpha_101': alpha_101(data_type_filter),

                               'target':target})
    return data_stand



if __name__ =='__main__':

    # 数据导入
    df1 = pd.read_csv('../data/md_20241230_exanic_cffex.csv')
    df2 = pd.read_csv('../data/md_20241231_exanic_cffex.csv')
    df3 = pd.read_csv('../data/md_20250102_exanic_cffex.csv')
    df4 = pd.read_csv('../data/backtest/md_20250103_exanic_cffex.csv')

    print("generate dataset 1")
    df1_stand = data_generate(df1, 'IC2501')
    print("generate dataset 2")
    df2_stand = data_generate(df2, 'IC2501')
    print("generate dataset 3")
    df3_stand = data_generate(df3, 'IC2501')
    print("generate dataset 4")
    df4_stand = data_generate(df4, 'IC2501')

    # # 由于数据量足够，故暂时考虑剔除掉包含na的数据样本对
    # df1_stand.dropna(inplace=True)
    # df2_stand.dropna(inplace=True)
    # df3_stand.dropna(inplace=True)
    # df4_stand.dropna(inplace=True)

    df_all = pd.concat([df1_stand,df2_stand,df3_stand,df4_stand]).reset_index(drop=True)

    # print(df_all.head())
    # df_all.info()
    df_all.to_csv('./data/temp.csv', index=False)
    feature = df_all.loc[:, df_all.columns.str.startswith('Feature') | (df_all.columns == 'target')]
    #correlation_matrix = feature.dropna().corr()

    correlation_matrix = feature.corr()
    print(correlation_matrix)

    # 可视化相关性矩阵
    plt.figure(figsize=(8, 6))
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)
    plt.title("Correlation Matrix")
    plt.show()


    #因子与target:|r|<0.1，相关性很差；|r|>0.1,可能有价值；|r|>0.3,有中等预测能力；|r|>0.5,有较强预测能力；|r|>0.7,可能有高预测能力；
    #因子之间： |r| > 0.8 表示存在高度的相关性；0.6 < |r| ≤ 0.8，表明存在中度相关性；|r| ≤ 0.6 时，因子之间的相关性较低，通常不会导致严重的多重共线性问题；