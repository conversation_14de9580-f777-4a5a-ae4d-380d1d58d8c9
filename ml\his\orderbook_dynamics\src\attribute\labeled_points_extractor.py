"""
Labeled points extractor implementation for orderbook dynamics.
This module handles the extraction of labeled data points for machine learning.
"""
from dataclasses import dataclass
from typing import List, Callable, Dict, Tuple, TypeVar, Generic, Optional, Union
import numpy as np
from ..models import Cell, OrderBook, OpenBookMsg
from .basic_attribute import BasicAttribute, BasicSet
from .time_insensitive_attribute import TimeInsensitiveAttribute, TimeInsensitiveSet
from .time_sensitive_attribute import TimeSensitiveAttribute, TimeSensitiveSet
from .label import Label, LabelEncode, MeanPriceMove, MeanPriceMoveEncode, MeanPriceMovementLabel


T = TypeVar('T')


@dataclass
class LabeledPoint:
    """
    Representation of a labeled data point for machine learning.
    
    Similar to Apache Spark's LabeledPoint.
    """
    label: float
    features: np.ndarray
    
    @staticmethod
    def create(label: float, features: Union[List[float], Dict[int, float], np.ndarray]) -> 'LabeledPoint':
        """Create a LabeledPoint from a label and features."""
        if isinstance(features, dict):
            # Create a sparse vector
            size = max(features.keys()) + 1 if features else 0
            arr = np.zeros(size)
            for idx, value in features.items():
                arr[idx] = value
            return LabeledPoint(label, arr)
        elif isinstance(features, list):
            return LabeledPoint(label, np.array(features))
        else:
            return LabeledPoint(label, features)


@dataclass
class LabeledOrderLog:
    """Container for labeled order book data for a specific symbol."""
    symbol: str
    labeled_points: List[LabeledPoint]


@dataclass
class AttributeSet(Generic[T]):
    """Set of attributes and functions to extract them."""
    set: T
    attributes: List[Callable[[T], Callable[[OrderBook], Cell]]]


class ReadBasicAttribute:
    """Wrapper for reading a basic attribute."""
    def __init__(self, func: Callable[[BasicSet], BasicAttribute]):
        self.func = func


class ReadTimeInsensitiveAttribute:
    """Wrapper for reading a time-insensitive attribute."""
    def __init__(self, func: Callable[[TimeInsensitiveSet], TimeInsensitiveAttribute]):
        self.func = func


class ReadTimeSensitiveAttribute:
    """Wrapper for reading a time-sensitive attribute."""
    def __init__(self, func: Callable[[TimeSensitiveSet], TimeSensitiveAttribute]):
        self.func = func


class LabeledPointsExtractorBuilder:
    """Builder for creating labeled points extractors."""
    def __init__(self, max_level: int = 5):
        self.basic_attributes: List[Callable[[BasicSet], BasicAttribute]] = []
        self.time_insensitive_attributes: List[Callable[[TimeInsensitiveSet], TimeInsensitiveAttribute]] = []
        self.time_sensitive_attributes: List[Callable[[TimeSensitiveSet], TimeSensitiveAttribute]] = []
        self.max_level = max_level
        
    def add(self, attribute: Union[ReadBasicAttribute, ReadTimeInsensitiveAttribute, ReadTimeSensitiveAttribute]) -> 'LabeledPointsExtractorBuilder':
        """Add an attribute to the builder."""
        if isinstance(attribute, ReadBasicAttribute):
            self.basic_attributes.append(attribute.func)
        elif isinstance(attribute, ReadTimeInsensitiveAttribute):
            self.time_insensitive_attributes.append(attribute.func)
        elif isinstance(attribute, ReadTimeSensitiveAttribute):
            self.time_sensitive_attributes.append(attribute.func)
        return self
        
    def result(self, symbol: str, label: Label, time_window_ms: int = 1000) -> 'LabeledPointsExtractor':
        """Create a LabeledPointsExtractor with the added attributes."""
        basic_set = BasicSet(self.max_level)
        time_insensitive_set = TimeInsensitiveSet(basic_set, self.max_level)
        time_sensitive_set = TimeSensitiveSet(time_window_ms, basic_set)
        
        basic_attr_set = AttributeSet(basic_set, self.basic_attributes)
        time_insensitive_attr_set = AttributeSet(time_insensitive_set, self.time_insensitive_attributes)
        time_sensitive_attr_set = AttributeSet(time_sensitive_set, self.time_sensitive_attributes)
        
        return LabeledPointsExtractor(
            symbol, 
            label, 
            time_window_ms,
            basic_attr_set,
            time_insensitive_attr_set,
            time_sensitive_attr_set
        )


def basic(func: Callable[[BasicSet], BasicAttribute]) -> ReadBasicAttribute:
    """Create a basic attribute reader."""
    return ReadBasicAttribute(func)


def time_insensitive(func: Callable[[TimeInsensitiveSet], TimeInsensitiveAttribute]) -> ReadTimeInsensitiveAttribute:
    """Create a time-insensitive attribute reader."""
    return ReadTimeInsensitiveAttribute(func)


def time_sensitive(func: Callable[[TimeSensitiveSet], TimeSensitiveAttribute]) -> ReadTimeSensitiveAttribute:
    """Create a time-sensitive attribute reader."""
    return ReadTimeSensitiveAttribute(func)


class AttributesCursor:
    """Cursor for accessing order book attributes at a specific point in time."""
    def __init__(self, order: OpenBookMsg, order_book: OrderBook, trail: List[OpenBookMsg]):
        self.order = order
        self.order_book = order_book
        self.trail = trail


class LabelCursor:
    """Cursor for accessing order book label at a specific point in time."""
    def __init__(self, order: OpenBookMsg, order_book: OrderBook):
        self.order = order
        self.order_book = order_book


class OrdersCursorTraversal:
    """Traverses through order messages to create attribute and label cursors."""
    def __init__(self, orders: List[OpenBookMsg], symbol: str, time_window_ms: int = 1000):
        self.orders = orders
        self.symbol = symbol
        self.time_window_ms = time_window_ms
        
    def cursor(self) -> List[Tuple[AttributesCursor, LabelCursor]]:
        """
        Generate pairs of attribute and label cursors.
        
        The attribute cursor points to the current state, while the label cursor 
        points to a future state after time_window_ms.
        """
        result = []
        orders_iter = iter(self.orders)
        order_books = self._generate_order_books()
        trails = self._generate_trails()
        
        # Combine into attribute cursors
        attrs = []
        for i, (order, order_book, trail) in enumerate(zip(self.orders, order_books, trails)):
            attrs.append(AttributesCursor(order, order_book, trail))
            
        # Find matching label cursors
        for i, attr in enumerate(attrs):
            # Find a label cursor that's at least time_window_ms ahead
            label_idx = i
            while (label_idx < len(self.orders) and 
                   self.orders[label_idx].source_time - attr.order.source_time < self.time_window_ms):
                label_idx += 1
                
            if label_idx < len(self.orders):
                label = LabelCursor(self.orders[label_idx], order_books[label_idx])
                result.append((attr, label))
                
        return result
    
    def _generate_order_books(self) -> List[OrderBook]:
        """Generate order books for each order message."""
        result = []
        order_book = OrderBook.empty(self.symbol)
        
        for order in self.orders:
            order_book = order_book.update(order)
            result.append(order_book)
            
        return result
    
    def _generate_trails(self) -> List[List[OpenBookMsg]]:
        """Generate order trails for each order message."""
        result = []
        current_trail = []
        
        for order in self.orders:
            # Update trail with time window
            cutoff_time = order.source_time - self.time_window_ms
            current_trail = [o for o in current_trail if o.source_time >= cutoff_time]
            current_trail.append(order)
            result.append(current_trail.copy())
            
        return result


class LabeledPointsExtractor:
    """Extracts labeled points from order book data for machine learning."""
    def __init__(
        self, 
        symbol: str,
        label: Label,
        time_window_ms: int,
        basic_attr_set: AttributeSet[BasicSet],
        time_insensitive_attr_set: AttributeSet[TimeInsensitiveSet],
        time_sensitive_attr_set: AttributeSet[TimeSensitiveSet]
    ):
        self.symbol = symbol
        self.label = label
        self.time_window_ms = time_window_ms
        self.basic_attr_set = basic_attr_set
        self.time_insensitive_attr_set = time_insensitive_attr_set
        self.time_sensitive_attr_set = time_sensitive_attr_set
        
        # Create feature extractors
        self.feature_extractors = []
        
        # Add basic attributes
        for func in self.basic_attr_set.attributes:
            attr = func(self.basic_attr_set.set)
            self.feature_extractors.append(
                lambda cursor, attr=attr: attr(cursor.order_book)
            )
            
        # Add time-insensitive attributes
        for func in self.time_insensitive_attr_set.attributes:
            attr = func(self.time_insensitive_attr_set.set)
            self.feature_extractors.append(
                lambda cursor, attr=attr: attr(cursor.order_book)
            )
            
        # Add time-sensitive attributes
        for func in self.time_sensitive_attr_set.attributes:
            attr = func(self.time_sensitive_attr_set.set)
            self.feature_extractors.append(
                lambda cursor, attr=attr: attr(cursor.trail)
            )
    
    def labeled_points(self, orders: List[OpenBookMsg]) -> List[LabeledPoint]:
        """Extract labeled points from order messages."""
        traversal = OrdersCursorTraversal(orders, self.symbol, self.time_window_ms)
        cursor_pairs = traversal.cursor()
        
        result = []
        for attr_cursor, label_cursor in cursor_pairs:
            # Extract features
            features = {}
            for i, extractor in enumerate(self.feature_extractors):
                cell = extractor(attr_cursor)
                if cell.is_value:
                    features[i] = float(cell.get())
                    
            # Extract label
            label_value = self.label(attr_cursor.order_book, label_cursor.order_book)
            
            if label_value is not None and features:
                if isinstance(label_value, (int, float)):
                    label_float = float(label_value)
                else:
                    # Handle enum labels
                    label_float = float(getattr(label_value, 'value', 0))
                
                result.append(LabeledPoint.create(label_float, features))
                
        return result 