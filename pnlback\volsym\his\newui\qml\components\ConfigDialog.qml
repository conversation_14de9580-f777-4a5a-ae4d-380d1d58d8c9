import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Window

Window {
    id: root
    title: "SVI拟合参数配置"
    width: 400
    height: 500
    modality: Qt.ApplicationModal
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10
        
        TabBar {
            id: tabBar
            Layout.fillWidth: true
            
            TabButton { text: "SVI总体权重" }
            TabButton { text: "参数权重" }
            TabButton { text: "ATM特征权重" }
        }
        
        StackLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            currentIndex: tabBar.currentIndex
            
            // SVI总体权重页
            GridLayout {
                columns: 2
                rowSpacing: 10
                columnSpacing: 10
                
                Repeater {
                    model: backend.sviWeights
                    
                    delegate: RowLayout {
                        Label { text: modelData.name + "权重:" }
                        SpinBox {
                            value: modelData.value * 100
                            from: 0
                            to: 10000
                            stepSize: 10
                            onValueChanged: backend.updateSviWeight(modelData.name, value / 100)
                        }
                    }
                }
            }
            
            // 参数权重页
            GridLayout {
                columns: 2
                rowSpacing: 10
                columnSpacing: 10
                
                Repeater {
                    model: backend.paramWeights
                    
                    delegate: RowLayout {
                        Label { text: modelData.name + "参数权重:" }
                        SpinBox {
                            value: modelData.value * 10
                            from: 0
                            to: 100
                            stepSize: 1
                            onValueChanged: backend.updateParamWeight(modelData.name, value / 10)
                        }
                    }
                }
            }
            
            // ATM特征权重页
            GridLayout {
                columns: 2
                rowSpacing: 10
                columnSpacing: 10
                
                Repeater {
                    model: backend.atmWeights
                    
                    delegate: RowLayout {
                        Label { text: modelData.name + "特征权重:" }
                        SpinBox {
                            value: modelData.value * 10
                            from: 0
                            to: 100
                            stepSize: 1
                            onValueChanged: backend.updateAtmWeight(modelData.name, value / 10)
                        }
                    }
                }
            }
        }
        
        // 底部控制栏
        RowLayout {
            Layout.fillWidth: true
            
            CheckBox {
                text: "启用SVI拟合"
                checked: backend.sviEnabled
                onCheckedChanged: backend.setSviEnabled(checked)
            }
            
            Item { Layout.fillWidth: true }
            
            Button {
                text: "确定"
                onClicked: root.close()
            }
            
            Button {
                text: "取消"
                onClicked: root.close()
            }
        }
    }
}