# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-23
from WindPy import w
import pyodbc
from datetime import datetime, timedelta
import pandas as pd

import sys
sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = datetime.now()
sectorcodelist = ["000300.SH", "000016.SH", "000905.SH"]
# beginDate = "2017-12-29"
w.start()

conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user, autocommit=True)
cursor = conn.cursor()


def tosql(sectorcode):
    if sectorcode == "000300.SH":
        table = "StkType300_New"
    elif sectorcode == "000016.SH":
        table = "StkType50_New"
    elif sectorcode == "000905.SH":
        table = "StkType500_New"

    print('-----通过wset来取%s数据集数据,获取%s代码列表-----' % (dt,table))
    wsetdata1 = w.wset("indexconstituent","date= %s ;windcode= %s " % (dt, sectorcode))
    # print(wsetdata1)

    sql0 = "SELECT DISTINCT [STKID] FROM %s" % table
    pf = pd.read_sql(sql0, conn, index_col=None, coerce_float=True, params=None, parse_dates=None,
                     columns=None, chunksize=None)

    # 通过wset来取数据集数据
    codes = pf['STKID'].values

    stocks = list((set(wsetdata1.Data[1])-set(codes)))
    if stocks == []:
        print("%s指数成分股无变化" % table)
        return
    else:
        print(stocks)

    s = ("""
     IF OBJECT_ID( '%s' , 'U') IS NOT NULL
        DROP TABLE %s
     CREATE TABLE %s (
        STKID varchar(20) not null,
        StkName nchar(10),
        Wgt float,
        RefreshTime datetime,
        Classification nchar(10)
        )
     """)
    cursor.execute(s % (table, table, table))
    sql = "INSERT INTO %s VALUES (?, ?,?,?,?)" % (table)

    for i in range(0, len(wsetdata1.Data[1])):
        sqllist = []
        for k in range(1, len(wsetdata1.Fields)):
            sqllist.append(wsetdata1.Data[k][i])
        sqllist.append(wsetdata1.Data[0][i])
        sqllist.append("")
        sqltuple = tuple(sqllist)
        cursor.execute(sql, sqltuple)
        conn.commit()

    s1 = "insert into %s select * from %s" % (table[:-4],table)
    cursor.execute(s1)

    return stocks


def newmin(codes):
    import sys
    sys.path.append(u'D:\\onedrive\\中泰衍生\\mine_python\\quantpy')
    from SQL.windtosql.update import datemakerMin

    if sectorcode == "000300.SH":
        table = "IndexStkInfo300_new"
    elif sectorcode == "000016.SH":
        table = "StkType50_New"
        return
    elif sectorcode == "000905.SH":
        table = "StkType800_New"
        return

    indexnum = 270
    dt, beginDate = datemakerMin.datemaker(table, conn, cursor, indexnum)
    dt=beginDate- timedelta(hours=17)
    beginDate=beginDate-timedelta(days=90)
    print(u"\n***追加更新%s只从 %s ---- % s 的数据***" % (len(codes), str(beginDate), str(dt)))

    num = 0
    sql = "INSERT INTO %s VALUES (?,?,?,?,?,?,?,?,?,?)" % table
    # sql0= "select A.STKID from [Alex].[dbo].[StkType300_new] as A left join [Alex].[dbo].[IndexStkInfo300_new] as B "\
    #       "on A.STKID=B.STKID where B.STKID is null "

    for j in range(0, len(codes)):
        # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
        # sys.stdout.write('\r' + u"-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----\r" % (j, codes[j]))
        print("\r", end="")
        print(u"\r-----第 %i 次通过wsd来提取 %s 开高低收成交量数据-----" % (j, codes[j]), end="")
        wsddata1 = w.wsi(codes[j], "open,high,low,close,volume,amt", beginDate,
                         dt, "PriceAdj=F")

        # wsddata1 = w.wsd(str(wsetdata.Data[1][j]), "open,high,low,close,volume,amt", beginDate, dt, "Fill=Previous")
        if wsddata1.ErrorCode != 0:
            continue
        lenth = len(wsddata1.Data[1])
        num = num + lenth
        num2 = 0
        for i in range(0, lenth):
            sqllist = []

            if len(wsddata1.Times) > 1:
                sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
                sqllist.append(wsddata1.Times[i].strftime('%H:%M:%S'))

            sqllist.append(codes[j])

            for k in range(0, len(wsddata1.Fields)):
                sqllist.append(wsddata1.Data[k][i])

            sqllist.append(-2)  # 补充数据标记为-2，原始数据为-1

            sqltuple = tuple(sqllist)

            try:
                cursor.execute(sql, sqltuple)
                conn.commit()
            except:  # Exception as e:
                # print(u'str(Exception):\t', str(Exception))
                # print(u'str(e):\t\t', str(e))
                num2 += 1
        if num2 != 0:
            print(lenth, num2)
            num = num - num2

    print(num)


for sectorcode in sectorcodelist:
    stocks = tosql(sectorcode)
    if stocks is None:
        continue
    newmin(stocks)
conn.close()
