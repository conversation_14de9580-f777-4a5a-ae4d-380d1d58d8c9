from PySide6.QtCore import QAbstractListModel, Qt, QModelIndex

class MonthModel(QAbstractListModel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._data = []
    
    def rowCount(self, parent=QModelIndex()):
        return len(self._data)
    
    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or not 0 <= index.row() < len(self._data):
            return None
            
        item = self._data[index.row()]
        if role == Qt.DisplayRole:
            return item["text"]
        elif role == Qt.CheckStateRole:
            return Qt.Checked if item["checked"] else Qt.Unchecked
            
        return None
    
    def setData(self, index, value, role=Qt.EditRole):
        if not index.isValid() or not 0 <= index.row() < len(self._data):
            return False
            
        if role == Qt.CheckStateRole:
            self._data[index.row()]["checked"] = value == Qt.Checked
            self.dataChanged.emit(index, index, [role])
            return True
            
        return False
    
    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags
            
        return Qt.ItemIsEnabled | Qt.ItemIsUserCheckable
    
    def setMonths(self, months):
        self.beginResetModel()
        self._data = [{"text": month, "checked": False} for month in months]
        self.endResetModel()