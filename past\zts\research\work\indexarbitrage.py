# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-11-29


import calendar
import pandas as pd
import datetime
import numpy as np
from scipy.linalg import solve

from WindPy import w

w.start()

code = "IF1609.CFE"
# stock = "000300.SH"
stocks = ['399004.SZ',"000002.SH",'399107.SZ']
dtbegin = "2018-01-01 09:00:00"
dtend = "2018-01-03 15:00:59"
dates = w.tdays(dtbegin, dtend, "")
path2net = "\\\\***********\\share\\共享\\李宁\\weight\\diy100"


def arbiprice():
    wsd_data = w.wsi(code, "close,volume,oi", dtbegin, dtend, "PriceAdj=F")

    fm = pd.DataFrame(wsd_data.Data, index=wsd_data.Fields, columns=wsd_data.Times)
    fm = fm.T  # 将矩阵转置
    fm = fm.rename(columns={'close': 'futures'})

    wsd_data2 = w.wsi(stock, "close", dtbegin, dtend, "PriceAdj=F")
    # fm.insert(0, 'stock', wsd_data2.Data[0])
    fm2 = pd.DataFrame(wsd_data2.Data, index=wsd_data2.Fields, columns=wsd_data2.Times)
    fm2 = fm2.T  # 将矩阵转置
    fm2 = fm2.rename(columns={'close': 'stock'})

    # fm32 = pd.concat([fm, fm2], axis=0)
    # fm32=fm.append(fm2)

    fm = fm.join(fm2, on=fm.index.values)

    fm.loc[:, 'diff'] = fm.loc[:, 'stock'] - fm.loc[:, 'futures']
    fm = fm.dropna()  # how='all')

    # sqllist2 = []
    # for index, row in fm.iterrows():
    #
    #

    fm.to_excel('arbitr.xls', sheet_name='arbitr')


def judgeLevel(df):
    if df['per'] <= 0.1:
        return df['per']
    elif df['per'] <= 0.2:
        return 0.2
    elif df['per'] <= 0.3:
        return 0.3
    elif df['per'] <= 0.4:
        return 0.4
    elif df['per'] <= 0.5:
        return 0.5
    elif df['per'] <= 0.6:
        return 0.6
    elif df['per'] <= 0.7:
        return 0.7
    elif df['per'] <= 0.8:
        return 0.8
    elif df['per'] <= 1:
        return 1
    else:
        return 1


def weight(stock):
    for i in dates.Data[0]:
        print((dates.Data[0][-1] - i).days)
        # i = '2016-09-23'
        year = i.year
        month = i.month
        if month == 1:
            month = 12
            year -= 1
        else:
            month -= 1
        firstDayWeekDay, monthRange = calendar.monthrange(year, month)
        lastDay = datetime.date(year=year, month=month, day=monthRange)
        wdata = w.wset("indexconstituent", "date=%s;windcode=%s" % (i, stock))
        coded = pd.DataFrame(wdata.Data, index=wdata.Fields, columns=None)
        coded = coded.T  # 将矩阵转置

        codes = coded['wind_code'].values.tolist()

        pricedata = w.wss(codes, "pre_close,close,total_shares,free_float_shares,share_totala,float_a_shares",
                          "tradeDate=%s;priceAdj=U;cycle=D;unit=1" % lastDay)
        coded.loc[:, 'pre_close'] = pricedata.Data[0]
        coded.loc[:, 'close'] = pricedata.Data[1]
        coded.loc[:, 'total_shares'] = pricedata.Data[2]
        coded.loc[:, 'free_float_shares'] = pricedata.Data[3]
        coded.loc[:, 'share_totala'] = pricedata.Data[4]
        coded.loc[:, 'float_a_shares'] = pricedata.Data[5]
        coded.loc[:, 'per'] = coded['free_float_shares'] / coded['share_totala']
        coded.loc[:, 'factor'] = coded.apply(lambda r: judgeLevel(r), axis=1)
        coded.loc[:, 'shares_in_index'] = coded.loc[:, 'factor'] * coded.loc[:, 'share_totala']
        coded.loc[:, 'market_cap_in_index'] = coded.loc[:, 'shares_in_index'] * coded.loc[:, 'pre_close']
        coded.loc[:, 'weight'] = coded['market_cap_in_index'] / coded['market_cap_in_index'].sum()

        pricedata2 = w.wss(codes, "pre_close,close,total_shares,free_float_shares,share_totala,float_a_shares",
                           "tradeDate=%s;priceAdj=U;cycle=D;unit=1" % i)
        coded.loc[:, 'pre_close2'] = pricedata2.Data[0]
        coded.loc[:, 'close2'] = pricedata2.Data[1]
        coded.loc[:, 'total_shares2'] = pricedata2.Data[2]
        coded.loc[:, 'free_float_shares2'] = pricedata2.Data[3]
        coded.loc[:, 'share_totala2'] = pricedata2.Data[4]
        coded.loc[:, 'float_a_shares2'] = pricedata2.Data[5]
        coded.loc[:, 'per2'] = coded['close2'] / coded['share_totala2']
        coded.loc[:, 'market_cap_totala2'] = coded['close2'] * coded['share_totala2']
        coded.loc[:, 'factor2'] = coded.apply(lambda r: judgeLevel(r), axis=1)
        coded.loc[:, 'shares_in_index2'] = coded.loc[:, 'factor2'] * coded.loc[:, 'share_totala2']
        coded.loc[:, 'market_cap_in_index2'] = coded.loc[:, 'shares_in_index2'] * coded.loc[:, 'close2']
        coded.loc[:, 'weight2'] = coded['market_cap_in_index2'] / coded['market_cap_in_index2'].sum()
        # s=coded2['weight'].sum()
        # coded2.loc[:, 'diff']=(coded2['weight']-coded2['i_weight']/100)*100
        coded.loc[:, 'weight3'] = coded['i_weight'] * coded['weight2'] / coded['weight']
        coded.loc[:, 'weight3'] = coded.loc[:, 'weight3'] / coded.loc[:, 'weight3'].sum() * 100
        coded.loc[:, 'free_float_shares3'] = coded.loc[:, 'weight3'] / coded.loc[:, 'weight3'].sum() * 100

        # list2 = []
        # list0 = []
        # for i in range(0, len(coded.loc[:, 'weight3'])):
        #     list1 = []
        #     index = coded.index[i]
        #     weighti = coded.loc[index, 'weight3'] / 100
        #     for p in coded.loc[:, 'close2']:
        #         list1.append(weighti)#* p)
        #     list1[i] = (weighti - 1) #* coded.loc[index, 'close2']
        #     list2.append(list1)
        #     list0.append(0)

        # a = np.array(list2)
        # b = np.array(list0)
        #
        # x = solve(a, b)
        #
        # a = np.array([[3, 1, -2], [1, -1, 4], [2, 0, 3]])
        # b = np.array([5, -2, 2.5])
        # x = solve(a, b)
        # print(x)

        coded2 = coded.loc[:,
                 ['date', 'wind_code', 'sec_name', 'i_weight', 'close2', 'share_totala2', 'free_float_shares2',
                  'shares_in_index2', 'market_cap_in_index2', 'weight2', 'free_float_shares3','market_cap_totala2',
                  'market_cap_in_index2', 'weight3']]
        coded2.to_excel(path2net + '\\%sclose_weight%s.xls' % (stock[:-3],datetime.datetime.strftime(i, "%Y%m%d")),
                        sheet_name='arbitr')


for stock in stocks:
    print(stock)
    weight(stock)
