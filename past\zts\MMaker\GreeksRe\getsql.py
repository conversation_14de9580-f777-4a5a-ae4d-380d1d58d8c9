# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-29

import pandas as pd
import pyodbc
from datetime import datetime
import sys
import os

def getpsw(classname,name):
    import json
    data = open(u"D:/onedrive/文档/package.json", encoding='utf-8')
    strJson = json.load(data)
    users = strJson[classname]
    for user in users:
        if name == user["name"]:
            return user


dbjson=getpsw("workdatabase","option-SH")
server = dbjson["server"]
user = dbjson["user"]
password = dbjson["password"]
dt = datetime.now()
# beginDate = "2017-06-24"

# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='option-SH', PWD=password, UID=user)

sql = "SELECT * FROM [option-SH].[dbo].[OptMarketDaily_1] WHERE tradeDate='%s'" % datetime.strftime(dt, '%Y-%m-%d')

try:
    pf = pd.read_sql(sql, conn, index_col='tradeDate', coerce_float=True, params=None, parse_dates=True, columns=None,
                     chunksize=None)
    conn.commit()
    print(u'持仓提取完毕')
    # print(pf)
except:
    print(datetime.strftime(dt, '%Y-%m-%d'), u'未更新1')
    conn.close()
    sys.exit()

if len(pf['netPosition']) > 0:
    print(u'持仓提取完毕')
else:
    print(datetime.strftime(dt, '%Y-%m-%d'), u'持仓未更新')
    conn.close()
    sys.exit()

# pf.loc[:,'absPosition']=abs(pf.loc[:,'netPosition'])
abssum = abs(pf.loc[:, 'netPosition']).sum()


def getzero(x):
    if x > 0:
        return x
    else:
        return 0


# pf.loc[:,'zhengPos']=pf['netPosition'].apply(lambda x:getzero(x))
zhengsum = pf[pf.loc[:, 'netPosition'] > 0].loc[:, 'netPosition'].sum()

sql2 = "SELECT * FROM [option-SH].[dbo].[OptMarketDaily_2] WHERE tradeDate='%s'" % datetime.strftime(dt, '%Y-%m-%d')

try:
    pf2 = pd.read_sql(sql2, conn, index_col='tradeDate', coerce_float=True, params=None, parse_dates=True, columns=None,
                      chunksize=None)
    conn.commit()
    print(u'Greeks提取完毕')
    # print(pf2)
except:
    print(datetime.strftime(dt, '%Y-%m-%d'), u'未更新2')
    conn.close()
    sys.exit()

conn.close()

import xlwings as xw

filename = u"\\做市业务数据报送--拷贝.xlsm"
filepath = u'D:\\works\\中泰衍生\\做市组日常工作\\业务数据报送\\'
# filepath = sys.path[0]

app = xw.App(visible=False, add_book=False)
app.display_alerts = False

# 打开 data.xlsx 文件到 wookbook 中
wb = app.books.open(filepath + filename)

# wb = xw.Book(filename)
sht = wb.sheets[0]

rng = sht.range('A2').expand()
nrows = rng.rows.count
ncols = rng.columns.count

sht.range('A2').expand().clear_contents()

sht.range('A2').value = pf

sht.range('K2').value = abssum
sht.range('J2').value = zhengsum

sht.range('N2').options(transpose=True).value = pf2[['stock', 'fut1', 'fut2']].values

sht.range('Q2').options(transpose=True).value = pf2[['deltaTotal', 'delta', 'gamma', 'vega', 'theta']].values

wb.save()

app.quit()
app.kill()

print(u'done')
