"""
特征生成器模块
```python
# 系统思维分析
我需要整理汇总OrderBookFeatureGenerator类生成的所有特征标签，对特征进行分类和归纳，便于理解和使用。
```

```python
# 特征分析
从代码中可以提取的特征类别主要包括基本特征、时间序列特征和高级特征，每类包含多个不同的特征标签。
```

以下是OrderBookFeatureGenerator生成的所有特征标签汇总：

### 基本特征
- **价差特征**：
  - `spread`：卖一价与买一价之差
  - `spread_ratio`：价差与中间价的比率

- **订单簿不平衡特征**：
  - `imbalance_1` 到 `imbalance_5`：各档位的买卖委托量不平衡度
  - `total_bid_vol`：所有可用档位的总买单量
  - `total_ask_vol`：所有可用档位的总卖单量
  - `vol_imbalance`：总买卖委托量不平衡度

- **价格压力特征**：
  - `bid_price_diff_1` 到 `bid_price_diff_n`：各买入档位间的价格差异
  - `ask_price_diff_1` 到 `ask_price_diff_n`：各卖出档位间的价格差异
  - `bid_price_pressure`：买方价格压力总和
  - `ask_price_pressure`：卖方价格压力总和
  - `price_pressure`：总价格压力

### 时间序列特征
- **价格动量特征**：
  - `mid_return_5/10/20`：中间价的5/10/20周期收益率
  - `mid_ma_5/10/20`：中间价的5/10/20周期移动平均
  - `mid_std_5/10/20`：中间价的5/10/20周期标准差

- **交易量特征**：
  - `vol_ma_5/10/20`：成交量的5/10/20周期移动平均
  - `vol_std_5/10/20`：成交量的5/10/20周期标准差

- **不平衡滚动特征**：
  - `imbalance_ma_5/10/20`：一档不平衡度的5/10/20周期移动平均

### 高级特征
- **订单流特征**：
  - `bid_vol_change`：买一量的变化
  - `ask_vol_change`：卖一量的变化
  - `OFI`：订单流不平衡指标
  - `OFI_ma_5`：订单流不平衡的5周期移动平均

- **交易强度特征**：
  - `volume_intensity`：交易量与价差的比率，反映交易强度

- **波动率特征**：
  - `volatility_5/10`：中间价5/10周期收益率的滚动标准差

"""
import pandas as pd
import numpy as np




class OrderBookFeatureGenerator:
    """订单簿特征生成器"""
    features=[
        # 基础数据列
        # "mid", 
        "tradedVol",
        
        # 基本特征 - 价差特征
        # "spread", 
        "spread_ratio",
        
        # 基本特征 - 订单簿不平衡特征
        # "imbalance_1", 
        # "imbalance_2", 
        # "imbalance_3", 
        # "imbalance_4", 
        # "imbalance_5",
        # "total_bid_vol", 
        # "total_ask_vol", 
        # #"vol_imbalance",
        
        # 基本特征 - 价格压力特征
        # "bid_price_diff_1", 
        # "bid_price_diff_2", 
        # "bid_price_diff_3", 
        # "bid_price_diff_4",
        # "ask_price_diff_1", 
        # "ask_price_diff_2", 
        # "ask_price_diff_3", 
        # "ask_price_diff_4",
        # "bid_price_pressure", 
        # "ask_price_pressure", 
        # #"price_pressure",
        
        # 时间序列特征 - 价格动量特征
        "mid_return_5", 
        # "mid_return_10", 
        # "mid_return_20",
        # "mid_ma_5", 
        # "mid_ma_10", 
        "mid_ma_20",
        # "mid_std_5", 
        # "mid_std_10", 
        "mid_std_20",
        
        # 时间序列特征 - 交易量特征
        # "vol_ma_5", 
        "vol_ma_10", 
        # "vol_ma_20",
        # "vol_std_5", 
        # "vol_std_10", 
        # "vol_std_20",
        
        # 时间序列特征 - 不平衡滚动特征
        # "imbalance_ma_5", 
        # "imbalance_ma_10", 
        "imbalance_ma_20",
        
        # 高级特征 - 订单流特征
        "bid_vol_change", 
        "ask_vol_change", 
        "OFI", 
        "OFI_ma_5",
        
        # 高级特征 - 交易强度特征
        # "volume_intensity",
        
        # 高级特征 - 波动率特征
        "volatility_5", 
        # "volatility_10"

        # 订单簿特征 - 买卖失衡指标
        'im5',          # 5档买卖失衡指标
        'im5vol',       # 5档成交量失衡指标
        'mid_minnum',   # 基于最小成交量的中间价格偏离
        # 'mid_minnum2',  # 基于加权平均价格的中间价格偏离
        # 'mid_level',    # 中间价格档位
        'turnover_mid', # 成交均价与中间价格的偏离
        'im1',          # 1档买卖失衡指标
        # 'press',        # 价格压力指标
        # 'im2mult',      # 2档加权买卖失衡指标
        'voi',          # 订单失衡量指标
        # 'edge_minnum'   # 基于最小成交量的买卖价差
    ]
    
    def __init__(self, data):
        self.data = data
    
    def generate_all_features(self):
        """生成所有特征"""
        print("开始生成所有特征...")

        # 检查必要的列是否存在
        required_cols = ['mid', 'AskPrice1', 'BidPrice1', 'tradedVol']
        missing_cols = [col for col in required_cols if col not in self.data.columns]
        if missing_cols:
            log_print(f"警告：数据中缺少必要的列: {missing_cols}", 'warning')
            return pd.DataFrame(), []
            
        # 创建中间价格列
        if 'mid' not in self.data.columns:
            self.data['mid'] = (self.data['bid_price_1'] + self.data['ask_price_1']) / 2
            log_print("已创建mid列")
            
        # 创建成交量列
        if 'tradedVol' not in self.data.columns:
            self.data['tradedVol'] = self.data['bid_volume_1'] + self.data['ask_volume_1']
            log_print("已创建tradedVol列")
            
        # 生成基本特征
        start_cols = len(self.data.columns)
        self.generate_basic_features()
        # 生成时间序列特征
        self.generate_time_series_features(windows=[5, 10, 20])
        # 生成高级特征
        self.generate_advanced_features()

        from ml.LIODAF.data.features.his.features_diy import Signal
        signal = Signal(self.data, sig=self.features)
        self.data, self.addcol = signal.sigall()
        # 添加特征交互项
        # self.add_interaction_features()
        print(f"所有特征生成完成{len(self.data.columns)-start_cols}")
        total_cols = set(self.data.columns)
        # 删除特征列表中不存在的特征
        features_cols = [col for col in self.features if col in self.data.columns]
        # 删除data中不存在的特征列
        self.data = self.data[[col for col in self.data.columns if col in self.features]]
        # 输出删除的特征
        print(f"删除的特征: {total_cols - set(self.data.columns)}")
        print(f"剩余特征: {features_cols}")
        # 检查是否有NaN值
        nan_cols = self.data.columns[self.data.isna().any()].tolist()
        if nan_cols:
            print(f"警告：以下特征列包含NaN值: {nan_cols}")
            print(f"NaN值数量: {self.data[nan_cols].isna().sum()}")
        return self.data, features_cols
    
    def generate_basic_features(self):
        """生成基本订单簿特征"""
        df = self.data.copy()

        self.data['mid'] = (self.data['AskPrice1'] + self.data['BidPrice1']) / 2
        # print("已创建mid列")

        self.data['tradedVol'] = self.data['Volume'].diff(1)
        self.data.loc[self.data['tradedVol'] < 0, 'tradedVol'] = 0
        # print("已创建tradedVol列")
        
        # 价差特征
        df['spread'] = df['AskPrice1'] - df['BidPrice1']
        df['spread_ratio'] = df['spread'] / df['mid']
        
        # 订单簿不平衡特征
        for i in range(1, 6):
            if f'BidVol{i}' in df.columns and f'AskVol{i}' in df.columns:
                df[f'imbalance_{i}'] = (df[f'BidVol{i}'] - df[f'AskVol{i}']) / (df[f'BidVol{i}'] + df[f'AskVol{i}'])
        
        bid_cols = [f'BidVol{i}' for i in range(1, 6) if f'BidVol{i}' in df.columns]
        ask_cols = [f'AskVol{i}' for i in range(1, 6) if f'AskVol{i}' in df.columns]
        
        if bid_cols and ask_cols:
            df['total_bid_vol'] = df[bid_cols].sum(axis=1)
            df['total_ask_vol'] = df[ask_cols].sum(axis=1)
            df['vol_imbalance'] = (df['total_bid_vol'] - df['total_ask_vol']) / (df['total_bid_vol'] + df['total_ask_vol'])
        
        # 价格压力特征
        bid_price_cols = [f'BidPrice{i}' for i in range(1, 6) if f'BidPrice{i}' in df.columns]
        ask_price_cols = [f'AskPrice{i}' for i in range(1, 6) if f'AskPrice{i}' in df.columns]
        
        if len(bid_price_cols) > 1 and len(ask_price_cols) > 1:
            # 计算相邻档位间的价格差
            for i in range(min(len(bid_price_cols), len(ask_price_cols))-1):
                if i+1 < len(bid_price_cols):
                    df[f'bid_price_diff_{i+1}'] = df[bid_price_cols[i]] - df[bid_price_cols[i+1]]
                if i+1 < len(ask_price_cols):
                    df[f'ask_price_diff_{i+1}'] = df[ask_price_cols[i+1]] - df[ask_price_cols[i]]
            
            # 计算价格压力
            bid_diff_cols = [col for col in df.columns if col.startswith('bid_price_diff_')]
            ask_diff_cols = [col for col in df.columns if col.startswith('ask_price_diff_')]
            
            if bid_diff_cols and ask_diff_cols:
                df['bid_price_pressure'] = df[bid_diff_cols].sum(axis=1)
                df['ask_price_pressure'] = df[ask_diff_cols].sum(axis=1)
                df['price_pressure'] = df['ask_price_pressure'] - df['bid_price_pressure']
        
        self.data = df
    
    def generate_time_series_features(self, windows=[5, 10, 20]):
        """生成时间序列特征"""
        df = self.data.copy()
        
        # 价格动量特征
        for window in windows:
            df[f'mid_return_{window}'] = df['mid'].pct_change(window)
            df[f'mid_ma_{window}'] = df['mid'].rolling(window=window).mean()
            df[f'mid_std_{window}'] = df['mid'].rolling(window=window).std()
            
            # 交易量特征
            df[f'vol_ma_{window}'] = df['tradedVol'].rolling(window=window).mean()
            df[f'vol_std_{window}'] = df['tradedVol'].rolling(window=window).std()
            
            # 订单簿不平衡滚动特征
            if 'imbalance_1' in df.columns:
                df[f'imbalance_ma_{window}'] = df['imbalance_1'].rolling(window=window).mean()
        
        self.data = df
    
    def generate_advanced_features(self):
        """生成高级订单簿特征"""
        df = self.data.copy()
        
        # 订单流失衡(OFI)简化实现
        if 'BidPrice1' in df.columns and 'AskPrice1' in df.columns and 'BidVol1' in df.columns and 'AskVol1' in df.columns:
            # 计算买卖方向的价格变化
            bid_price_diff = df['BidPrice1'] - df['BidPrice1'].shift(1)
            ask_price_diff = df['AskPrice1'] - df['AskPrice1'].shift(1)
            
            # 计算OFI_bid (买方订单流)
            df['OFI_bid'] = np.where(
                bid_price_diff > 0, df['BidVol1'],
                np.where(bid_price_diff == 0, df['BidVol1'] - df['BidVol1'].shift(1),
                        -df['BidVol1'])
            )
            
            # 计算OFI_ask (卖方订单流)
            df['OFI_ask'] = np.where(
                ask_price_diff > 0, df['AskVol1'],
                np.where(ask_price_diff == 0, df['AskVol1'] - df['AskVol1'].shift(1),
                        -df['AskVol1'])
            )
            
            # 计算订单流不平衡(OFI)
            df['OFI'] = df['OFI_bid'] - df['OFI_ask']
            df['OFI_ma_5'] = df['OFI'].rolling(window=5).mean()
            
            # 买卖量变化
            df['bid_vol_change'] = df['BidVol1'].diff()
            df['ask_vol_change'] = df['AskVol1'].diff()
        
        # 交易量强度
        if 'tradedVol' in df.columns and 'spread' in df.columns:
            df['volume_intensity'] = df['tradedVol'] / df['spread']
        
        # 波动率特征
        for window in [5, 10]:
            df[f'volatility_{window}'] = df['mid'].pct_change().rolling(window=window).std()
        
        self.data = df 
    

    def add_interaction_features(self):
        """
        添加特征交互项
        
        参数:
            data (pandas.DataFrame): 数据
        """
        interaction_features = []
        # 获取实际存在的特征列表
        existing_features = [col for col in self.features if col in self.data.columns]
        
        # 处理无穷值和异常值
        for col in existing_features:
            if col in self.data.columns:
                # 将无穷值替换为NaN
                self.data[col] = self.data[col].replace([np.inf, -np.inf], np.nan)
                # 使用中位数填充NaN
                self.data[col] = self.data[col].fillna(self.data[col].median())
        
        for i, col1 in enumerate(existing_features):
            for col2 in existing_features[i+1:]:
                if col1 in self.data.columns and col2 in self.data.columns:
                    # 计算交互特征
                    interaction = self.data[col1] * self.data[col2]
                    # 处理异常值
                    interaction = interaction.clip(
                        lower=interaction.quantile(0.01),
                        upper=interaction.quantile(0.99)
                    )
                    self.data[f'{col1}_{col2}_interaction'] = interaction
                    interaction_features.append(f'{col1}_{col2}_interaction')
        
        # 添加非线性变换
        for col in existing_features:
            if col in self.data.columns:
                # 平方变换
                squared = self.data[col]**2
                squared = squared.clip(
                    lower=squared.quantile(0.01),
                    upper=squared.quantile(0.99)
                )
                self.data[f'{col}_squared'] = squared
                
                # 对数变换
                # 添加小量值避免log(0)
                log_input = self.data[col].abs() + 1e-10
                log_value = np.log1p(log_input)
                log_value = log_value.clip(
                    lower=log_value.quantile(0.01),
                    upper=log_value.quantile(0.99)
                )
                self.data[f'{col}_log'] = log_value
                
                interaction_features.append(f'{col}_squared')
                interaction_features.append(f'{col}_log')

        self.features += interaction_features




