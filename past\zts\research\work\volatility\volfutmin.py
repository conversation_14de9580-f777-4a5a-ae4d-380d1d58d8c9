# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-29

import pandas as pd
import numpy as np
import pyodbc
from datetime import datetime
import math
import xlwt

server = '10.25.18.36'
user = 'Alex'
password = '789456'
dt = datetime.now()
# beginDate = "2017-06-24"

day1=20

# Specifying the ODBC driver, server name, database, etc. directly
conn = pyodbc.connect(DRIVER='{SQL Server}', SERVER=server, DATABASE='Alex', PWD=password, UID=user)

sql = 'SELECT [DateTime],[Code],[OpenPrice],[High],[Low],[ClosePrice],[Volume],[Amount],[oi] ' \
      'FROM [Alex].[dbo].[M_Future_min]'

pf = pd.read_sql(sql, conn, index_col='DateTime', coerce_float=True, params=None, parse_dates=True, columns=None,
                 chunksize=None)
conn.commit()
conn.close()

print(pf)

#pf2 = pf.ix['2017-07-24', :]
#print(pf2)
pf['date'] = 0
pf['time'] = 0
pf['log_return'] = 0
pf['daily_vol'] = 0
pf['ann_vol'] = 0

pf.index = pd.DatetimeIndex(pf.index)
pf0=pf.resample('20T').asfreq()
pf0=pf0.dropna(axis=0,how='any') #drop all rows that have any NaN values
pf1=pf0.to_period('D')

# pf0 = pf[pf['DateTime'] == '0']
pf12 = pf1.index.tolist()
codedata = list(set(pf12))
codedata.sort()

print(len(codedata))
num = 0

for j in range(1,len(pf0.index)):
    index2 = pf0.index[j]
    num+=1
    print(num)
    b = float(str(pf0.loc[pf0.index[j], ['ClosePrice']].values[0]))
    c = float(str(pf0.loc[pf0.index[j - 1], ['ClosePrice']].values[0]))
    a = b / c
    a = math.log(a)
    pf0.loc[index2, ['log_return']] = a

sqllist2=[]
num = 0
for ii in range(20, len(codedata)):

    i = codedata[ii]
    sqllist = []
    print(i)
    num+=1
    pf5 = pf0[str(codedata[ii-20]):str(i)]
    asd=pf5.index
    pydate_array = asd.to_pydatetime()
    time_only_array = np.vectorize(lambda s: s.strftime('%H:%M:%S'))(pydate_array)
    time_only_series = pd.Series(time_only_array)
    # pf5 = pf5[:"%s 15:00:00.000" % str(i)]
    pf5.insert(0, 'Time1', time_only_series.values)
    pf5['Time1'] = pd.to_datetime(pf5['Time1'], format='%H:%M:%S')
    print(num)

    #pf5=pf5[pf5['Time1']<"1900-01-01 15:00:01"]
    aarray = pf5[1:]['log_return']
    stan = np.std(aarray)
    sqllist.append(i)
    sqllist.append(stan)
    sqllist.append(stan*math.sqrt(252*6.5*60/20))
    sqllist2.append(sqllist)


f = xlwt.Workbook()  # 创建工作簿
sheet1 = f.add_sheet(u'opt_fut', cell_overwrite_ok=True)  # 创建sheet
for i, row in enumerate(sqllist2):
    for j, col in enumerate(row):
        sheet1.write(i, j, str(col))
f.save('vofutmin202.xls')  # 保存文件

