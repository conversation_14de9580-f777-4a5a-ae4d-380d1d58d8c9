{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import db_lob as lob\n", "from tqdm import tqdm\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["path = '/home/<USER>/Documents/lob/2505133.csv'\n", "\n", "messages = lob.parse_FullMessages(path)\n", "\n", "volume = 1000000\n", "ticks = 0.0001\n", "\n", "book = lob.LimitOrderBook(volume, ticks)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Reconstructing the book: 100%|██████████| 659568/659568 [03:39<00:00, 3003.82it/s]\n"]}], "source": ["orders = np.array([])\n", "for msg in tqdm(messages[:int(0.4* len(messages))], desc=\"Reconstructing the book\"):\n", "    maybe_bars = book.generic_incremental_update(msg)\n", "    orders = np.append(orders, book.lastOrder.volume)\n", "\n", "        \n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(orders[orders < 1e6], bins=100)\n", "#plt.xlim(0, 1e6)\n", "plt.title('Order Size distribution')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["y,x,z = plt.hist(orders[orders < 1e6]/10000, bins=np.arange(100))\n", "plt.xlim(0, 1e2)\n", "plt.title('Order Size distribution')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["max_x = x[y.argsort()[::-1][:5]]\n", "max_y = y[y.argsort()[::-1][:5]]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[25 50 34 16 40] [119352. 118935.  84445.  33858.  31640.]\n"]}], "source": ["print(max_x, max_y)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.bar(x[0:-1:5], y[0::5], color='b')\n", "plt.bar(max_x, max_y, color='r')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "4632010a9e13d22e5db6181f606bb74cee322bdf2be96a9f982ade689500be32"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}