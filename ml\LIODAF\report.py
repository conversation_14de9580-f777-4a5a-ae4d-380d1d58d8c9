"""
订单簿分析汇总报告
@author: lining
"""
import os
import datetime
import base64
import glob
from bs4 import BeautifulSoup

 
# 设置报告内容显示的默认宽度
browser_width = 1200
file_size_str = f'width:100%; max-width:{browser_width}px; height:800px;'

class OrderBookReport:
    def __init__(self, plots_dir):
        self.plots_dir = plots_dir

    def generate_summary_report(self):
        """
        生成汇总所有可视化结果的HTML报告
        
        该方法读取plots目录下的所有HTML和PNG文件，
        并将它们整合到一个单一的HTML文件中
        """
        print("生成可视化汇总报告...")
        
        # 报告文件路径
        report_path = os.path.join(self.plots_dir, 'summary_report.html')
        
        # 获取所有的HTML和PNG文件,剔除summary_report.html
        html_files = glob.glob(os.path.join(self.plots_dir, '*.html'))
        png_files = glob.glob(os.path.join(self.plots_dir, '*.png'))
        html_files = [file for file in html_files if file != report_path]
        
        # 按文件名排序
        html_files.sort()
        png_files.sort()
        
        # 如果没有找到任何文件，直接返回
        if not html_files and not png_files:
            print("未找到可视化文件，无法生成汇总报告")
            return
        
        # HTML模板的头部
        html_template_header = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>订单簿分析汇总报告</title>
            <link rel="preconnect" href="https://fonts.googleapis.com">
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
            <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
            <style>
                :root {
                    --primary-color: #2563eb;
                    --primary-hover: #1d4ed8;
                    --text-color: #1f2937;
                    --text-light: #6b7280;
                    --bg-color: #f3f4f6;
                    --card-bg: #ffffff;
                    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
                    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                    --border-color: #e5e7eb;
                    --radius: 0.5rem;
                }
                
                * {
                    box-sizing: border-box;
                    margin: 0;
                    padding: 0;
                }
                
                body {
                    font-family: 'Noto Sans SC', sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: var(--bg-color);
                    color: var(--text-color);
                    line-height: 1.6;
                }
                
                .content {
                    max-width: 1200px;
                    margin: 2rem auto;
                    padding: 0 1.5rem;
                }
                
                .header {
                    margin-bottom: 2rem;
                    text-align: center;
                }
                
                .header h1 {
                    color: var(--primary-color);
                    font-size: 2rem;
                    padding-bottom: 0.75rem;
                    margin-bottom: 0.5rem;
                    border-bottom: 2px solid var(--primary-color);
                }
                
                .timestamp {
                    color: var(--text-light);
                    font-size: 0.875rem;
                    margin-top: 0.5rem;
                }
                
                .visualization-container {
                    margin-bottom: 2rem;
                    padding: 1.5rem;
                    border-radius: var(--radius);
                    box-shadow: var(--shadow);
                    background-color: var(--card-bg);
                    transition: transform 0.2s, box-shadow 0.2s;
                }
                
                .visualization-container:hover {
                    transform: translateY(-3px);
                    box-shadow: var(--shadow-md);
                }
                
                .visualization-container h3 {
                    color: var(--text-color);
                    font-size: 1.25rem;
                    padding-bottom: 0.75rem;
                    margin-bottom: 1rem;
                    border-bottom: 1px solid var(--border-color);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .image-container img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 0.25rem;
                    box-shadow: var(--shadow-sm);
                }
                
                .nav-container {
                    position: sticky;
                    top: 0;
                    z-index: 100;
                    background-color: rgba(255, 255, 255, 0.95);
                    border-radius: var(--radius);
                    box-shadow: var(--shadow);
                    padding: 1rem;
                    margin-bottom: 2rem;
                    backdrop-filter: blur(10px);
                }
                
                .visualization-nav {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 0.75rem;
                }
                
                .nav-button {
                    display: inline-block;
                    padding: 0.75rem 1rem;
                    background-color: var(--primary-color);
                    color: white;
                    text-decoration: none;
                    border-radius: 0.375rem;
                    font-weight: 500;
                    box-shadow: var(--shadow-sm);
                    transition: all 0.2s ease;
                    font-size: 0.875rem;
                }
                
                .nav-button:hover {
                    background-color: var(--primary-hover);
                    transform: translateY(-2px);
                    box-shadow: var(--shadow);
                }
                
                .top-link {
                    position: fixed;
                    bottom: 1.5rem;
                    right: 1.5rem;
                    background-color: var(--primary-color);
                    color: white;
                    width: 3rem;
                    height: 3rem;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-decoration: none;
                    opacity: 0;
                    transform: translateY(10px);
                    transition: opacity 0.3s, transform 0.3s;
                    box-shadow: var(--shadow);
                    z-index: 1000;
                }
                
                .top-link.visible {
                    opacity: 0.9;
                    transform: translateY(0);
                }
                
                .top-link:hover {
                    opacity: 1;
                    background-color: var(--primary-hover);
                }
                
                .top-link:before {
                    content: "↑";
                    font-size: 1.25rem;
                }
                
                .open-file-link {
                    font-size: 0.875rem;
                    color: var(--primary-color);
                    text-decoration: none;
                    padding: 0.25rem 0.5rem;
                    border-radius: 0.25rem;
                    background-color: rgba(37, 99, 235, 0.1);
                    transition: background-color 0.2s;
                }
                
                .open-file-link:hover {
                    background-color: rgba(37, 99, 235, 0.2);
                }
                
                .file-link {
                    font-size: 0.875rem;
                    color: var(--primary-color);
                    text-decoration: none;
                    display: inline-block;
                    padding: 0.25rem 0.75rem;
                    border: 1px solid var(--primary-color);
                    border-radius: 0.25rem;
                    margin-top: 0.75rem;
                    background-color: rgba(37, 99, 235, 0.05);
                    transition: all 0.2s;
                }
                
                .file-link:hover {
                    background-color: rgba(37, 99, 235, 0.1);
                    transform: translateY(-1px);
                }
                
                .section-divider {
                    height: 1px;
                    background-color: var(--border-color);
                    margin: 2rem 0;
                }
                
                /* 响应式设计 */
                @media (max-width: 768px) {
                    .header h1 {
                        font-size: 1.5rem;
                    }
                    
                    .visualization-container {
                        padding: 1rem;
                    }
                    
                    .nav-button {
                        padding: 0.5rem 0.75rem;
                        font-size: 0.75rem;
                    }
                }
                
                /* 加载动画 */
                .loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: var(--bg-color);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    transition: opacity 0.5s;
                }
                
                .spinner {
                    width: 50px;
                    height: 50px;
                    border: 5px solid rgba(37, 99, 235, 0.2);
                    border-radius: 50%;
                    border-top-color: var(--primary-color);
                    animation: spin 1s ease-in-out infinite;
                }
                
                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
                
                /* 打印样式优化 */
                @media print {
                    .top-link, .nav-container {
                        display: none;
                    }
                    
                    .visualization-container {
                        break-inside: avoid;
                        box-shadow: none;
                        border: 1px solid var(--border-color);
                    }
                    
                    body {
                        background-color: white;
                    }
                }
            </style>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // 添加页面加载动画
                    const loadingOverlay = document.createElement('div');
                    loadingOverlay.className = 'loading-overlay';
                    loadingOverlay.innerHTML = '<div class="spinner"></div>';
                    document.body.appendChild(loadingOverlay);
                    
                    // 页面加载完成后移除加载动画
                    window.addEventListener('load', function() {
                        setTimeout(function() {
                            loadingOverlay.style.opacity = '0';
                            setTimeout(function() {
                                loadingOverlay.remove();
                            }, 500);
                        }, 300);
                    });
                    
                    // 返回顶部按钮逻辑
                    const topLink = document.querySelector('.top-link');
                    
                    if (topLink) {
                        window.addEventListener('scroll', function() {
                            if (window.pageYOffset > 300) {
                                topLink.classList.add('visible');
                            } else {
                                topLink.classList.remove('visible');
                            }
                        });
                        
                        topLink.addEventListener('click', function(e) {
                            e.preventDefault();
                            window.scrollTo({
                                top: 0,
                                behavior: 'smooth'
                            });
                        });
                    }
                    
                    // 平滑滚动导航
                    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                        anchor.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            const targetId = this.getAttribute('href');
                            if (targetId === '#') return;
                            
                            const targetElement = document.querySelector(targetId);
                            if (targetElement) {
                                targetElement.scrollIntoView({
                                    behavior: 'smooth'
                                });
                            }
                        });
                    });
                    
                    // 添加图表容器悬停效果
                    const containers = document.querySelectorAll('.visualization-container');
                    containers.forEach(container => {
                        container.addEventListener('mouseenter', function() {
                            this.style.transform = 'translateY(-3px)';
                            this.style.boxShadow = 'var(--shadow-md)';
                        });
                        container.addEventListener('mouseleave', function() {
                            this.style.transform = 'translateY(0)';
                            this.style.boxShadow = 'var(--shadow)';
                        });
                    });
                });
                
                function openOriginalFile(filePath) {
                    window.open('file:///' + filePath.replace(/\\/g, '/'), '_blank');
                }
            </script>
        </head>
        <body>
            <div class="content">
                <div class="header">
                    <h1>订单簿分析汇总报告</h1>
        """
        
        # 构建导航菜单内容
        nav_menu = ""
        for html_file in html_files:
            file_name = os.path.basename(html_file)
            section_name = self._get_section_name(file_name)
            section_id = file_name.replace('.html', '')
            nav_menu += f'<li><a href="#{section_id}">{section_name}</a></li>\n'
        
        for png_file in png_files:
            file_name = os.path.basename(png_file)
            section_name = self._get_section_name(file_name)
            section_id = file_name.replace('.png', '')
            nav_menu += f'<li><a href="#{section_id}">{section_name}</a></li>\n'
        
        # HTML模板的侧边栏和内容区域开始
        html_template_content_start = """
                    <div class="timestamp">生成时间: {timestamp}</div>
                </div>
                
                <div class="nav-container">
                    <div class="visualization-nav">
        """.format(timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # 创建快速导航按钮
        quick_nav = ""
        for html_file in html_files:
            file_name = os.path.basename(html_file)
            section_name = self._get_section_name(file_name)
            section_id = file_name.replace('.html', '')
            quick_nav += f'<a class="nav-button" href="#{section_id}">{section_name}</a>\n'
        
        for png_file in png_files:
            file_name = os.path.basename(png_file)
            section_name = self._get_section_name(file_name)
            section_id = file_name.replace('.png', '')
            quick_nav += f'<a class="nav-button" href="#{section_id}">{section_name}</a>\n'
        
        # 结束概览部分
        html_overview_end = """
                    </div>
                </div>
                <div class="section-divider"></div>
        """
        
        # 处理HTML文件内容
        html_content = ""
        for html_file in html_files:
            file_name = os.path.basename(html_file)
            section_id = file_name.replace('.html', '')
            section_name = self._get_section_name(file_name)
            
            # 提取HTML文件中的图表内容
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content_raw = f.read()
                
                # 使用BeautifulSoup提取图表内容
                soup = BeautifulSoup(html_content_raw, 'html.parser')
                # 查找主要内容 - 通常在Plotly图表中是div.plotly-graph-div
                main_content = soup.find('div', {'class': 'plotly-graph-div'})
                
                if main_content:
                    # 设置Plotly图表的宽高
                    main_content['style'] = file_size_str
                    
                    # 提取必要的脚本标签
                    scripts = soup.find_all('script')
                    script_content = ""
                    for script in scripts:
                        if script.has_attr('src'):
                            continue  # 跳过外部脚本
                        script_content += str(script)
                    
                    # 构建该部分的HTML，添加打开原文件的链接
                    html_content += f"""
                    <div id="{section_id}" class="visualization-container">
                        <h3>
                            <span>{section_name}</span>
                            <a class="open-file-link" href="file:///{os.path.abspath(html_file).replace('\\', '/')}" target="_blank" title="打开原文件">查看原文件</a>
                        </h3>
                        <div class="html-embed" style="{file_size_str}">
                            {str(main_content)}
                        </div>
                        {script_content}
                    </div>
                    """
                else:
                    # 如果无法提取，使用iframe，添加打开原文件的链接
                    html_content += f"""
                    <div id="{section_id}" class="visualization-container">
                        <h3>
                            <span>{section_name}</span>
                            <a class="open-file-link" href="file:///{os.path.abspath(html_file).replace('\\', '/')}" target="_blank" title="打开原文件">查看原文件</a>
                        </h3>
                        <iframe src="{file_name}" class="html-content" style="{file_size_str}"></iframe>
                    </div>
                    """
            except Exception as e:
                print(f"处理HTML文件 {file_name} 时出错: {e}")
                # 出错时使用iframe作为后备，添加打开原文件的链接
                html_content += f"""
                <div id="{section_id}" class="visualization-container">
                    <h3>
                        <span>{section_name}</span>
                        <a class="open-file-link" href="file:///{os.path.abspath(html_file).replace('\\', '/')}" target="_blank" title="打开原文件">查看原文件</a>
                    </h3>
                    <iframe src="{file_name}" class="html-content" style="{file_size_str}"></iframe>
                </div>
                """
        
        # 处理PNG文件内容
        for png_file in png_files:
            file_name = os.path.basename(png_file)
            section_id = file_name.replace('.png', '')
            section_name = self._get_section_name(file_name)
            
            # 将PNG文件转换为base64
            try:
                with open(png_file, 'rb') as f:
                    img_data = f.read()
                img_base64 = base64.b64encode(img_data).decode('utf-8')
                
                # 添加打开原文件的链接
                html_content += f"""
                <div id="{section_id}" class="visualization-container">
                    <h3>
                        <span>{section_name}</span>
                        <a class="open-file-link" href="file:///{os.path.abspath(png_file).replace('\\', '/')}" target="_blank" title="打开原文件">查看原文件</a>
                    </h3>
                    <div class="image-container">
                        <img src="data:image/png;base64,{img_base64}" alt="{section_name}" loading="lazy">
                    </div>
                    <a class="file-link" href="file:///{os.path.abspath(png_file).replace('\\', '/')}" target="_blank" title="完整路径: {os.path.abspath(png_file)}">{file_name}</a>
                </div>
                """
            except Exception as e:
                print(f"处理PNG文件 {file_name} 时出错: {e}")
        
        # HTML模板的尾部
        html_template_footer = """
            </div>
            <a href="#" class="top-link" aria-label="返回顶部"></a>
        </body>
        </html>
        """
        
        # 合并所有内容
        full_html = (
            html_template_header + 
            html_template_content_start.format(timestamp=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')) + 
            quick_nav + 
            html_overview_end + 
            html_content + 
            html_template_footer
        )
        
        # 写入文件
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(full_html)
            print(f"汇总报告已生成: {report_path}")
        except Exception as e:
            print(f"生成汇总报告时出错: {e}")
    
    def _get_section_name(self, file_name):
        """
        根据文件名获取章节名称
        
        参数:
            file_name (str): 文件名
            
        返回:
            str: 格式化的章节名称
        """
        # 移除扩展名
        base_name = os.path.splitext(file_name)[0]
        
        # 映射常见文件名到更友好的显示名称
        name_mapping = {
            'backtest_results': '回测结果',
            'feature_correlation': '特征相关性',
            'feature_importance': '特征重要性',
            'shap_importance': 'SHAP重要性',
            'shap_values': 'SHAP值分析',
            'trade_analysis': '交易分析',
        }
        
        # 返回映射的名称或原始名称的格式化版本
        return name_mapping.get(base_name, ' '.join(word.capitalize() for word in base_name.split('_')))


if __name__ == "__main__":
    report = OrderBookReport(plots_dir='output/plots')
    report.generate_summary_report()

