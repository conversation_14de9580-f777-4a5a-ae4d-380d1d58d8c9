from urllib import request
from bs4 import BeautifulSoup  # Beautiful Soup是一个可以从HTML或XML文件中提取结构化数据的Python库

# 构造头文件，模拟浏览器访问
url = "http://www.sse.com.cn/disclosure/announcement/general/c/c_20190418_4771628.shtml"
for i in range(10):
    url = u"http://query.sse.com.cn//search/getSearchResult.do?jsonCallBack=jQuery111206246616474743316_1555564868418&search=ehdwdjs&page={}&searchword=SNS_A_CONTENT+T_E+T_L%E6%B5%81%E5%8A%A8%E6%80%A7T_R&orderby=-SEARCH_TIME&perpage=10&isNew=true&_=1555564868454".format(
         i )
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) '
                      'Chrome/70.0.3538.67 Safari/537.36'}
    page = request.Request(url, headers=headers)
    page_info = request.urlopen(page).read().decode('utf-8')  # 打开Url,获取HttpResponse返回对象并读取其ResposneBody

    # 将获取到的内容转换成BeautifulSoup格式，并将html.parser作为解析器
    soup = BeautifulSoup(page_info, 'html.parser')
    # soup.select('#sse_query_list > dl:nth-child(5) > dd > a')
    # 以格式化的形式打印html
    # print(soup.prettify())

    titles = soup.find_all('a')  # 查找所有a标签中class='title'的语句
    titles2 =soup.find_all('div','allZoom')
    titles3 = soup.find_all('h2')
    # sse_query_list > dl:nth-child(10) > dd > a

    '''
    # 打印查找到的每一个a标签的string和文章链接
        for title in titles:
            print(title.string)
            print("http://www.jianshu.com" + title.get('href'))   
    '''
    # print(soup.prettify())
    # open()是读写文件的函数,with语句会自动close()已打开文件
    with open(r"articles.txt", "w") as file:  # 在磁盘以只写的方式打开/创建一个名为 articles 的txt文件
        for title in titles2:
            file.write(title.get_text() + '\n')
            # file.write(title.get('href') + '\n\n')
            print(title.name, title.get_text())


