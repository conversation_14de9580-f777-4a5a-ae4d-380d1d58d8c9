{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import scipy as sp\n", "import scipy.optimize as opt\n", "import scipy.stats as rnd\n", "from scipy.optimize import minimize\n", "import time\n", "from datetime import datetime as dtm\n", "from datetime import date as dt\n", "from datetime import timedelta as td\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mCannot execute code, session has been disposed. Please try restarting the Kernel."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mCannot execute code, session has been disposed. Please try restarting the Kernel. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["from WindPy import *\n", "w.start()\n", "# 统一日期管理 - 使用单一日期对象生成所有格式\n", "base_date = dtm(2025, 7, 1)\n", "tradedate = base_date.strftime('%Y%m%d')\n", "tradedate2 = base_date.strftime('%Y-%m-%d')\n", "tradedate3 = base_date\n", "under = \"510500.SH\"\n"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['10009359.SH', '10009360.SH', '10009361.SH', '10009362.SH', '10009363.SH', '10009364.SH', '10009365.SH', '10009366.SH', '10009367.SH', '10009505.SH', '10009368.SH', '10009369.SH', '10009370.SH', '10009371.SH', '10009372.SH', '10009373.SH', '10009374.SH', '10009375.SH', '10009376.SH', '10009506.SH', '10009545.SH', '10009546.SH', '10009547.SH', '10009548.SH', '10009549.SH', '10009550.SH', '10009551.SH', '10009552.SH', '10009553.SH', '10009554.SH', '10009555.SH', '10009556.SH', '10009557.SH', '10009558.SH', '10009559.SH', '10009560.SH', '10009561.SH', '10009562.SH', '10009193.SH', '10008829.SH', '10008830.SH', '10008831.SH', '10008832.SH', '10008833.SH', '10008834.SH', '10008835.SH', '10008836.SH', '10008837.SH', '10008887.SH', '10008903.SH', '10009047.SH', '10009194.SH', '10008838.SH', '10008839.SH', '10008840.SH', '10008841.SH', '10008842.SH', '10008843.SH', '10008844.SH', '10008845.SH', '10008846.SH', '10008888.SH', '10008904.SH', '10009048.SH', '10009307.SH', '10009253.SH', '10009254.SH', '10009255.SH', '10009256.SH', '10009257.SH', '10009258.SH', '10009259.SH', '10009260.SH', '10009261.SH', '10009507.SH', '10009308.SH', '10009262.SH', '10009263.SH', '10009264.SH', '10009265.SH', '10009266.SH', '10009267.SH', '10009268.SH', '10009269.SH', '10009270.SH', '10009508.SH']\n", "10009359.SH,10009360.SH,10009361.SH,10009362.SH,10009363.SH,10009364.SH,10009365.SH,10009366.SH,10009367.SH,10009505.SH,10009368.SH,10009369.SH,10009370.SH,10009371.SH,10009372.SH,10009373.SH,10009374.SH,10009375.SH,10009376.SH,10009506.SH,10009545.SH,10009546.SH,10009547.SH,10009548.SH,10009549.SH,10009550.SH,10009551.SH,10009552.SH,10009553.SH,10009554.SH,10009555.SH,10009556.SH,10009557.SH,10009558.SH,10009559.SH,10009560.SH,10009561.SH,10009562.SH,10009193.SH,10008829.SH,10008830.SH,10008831.SH,10008832.SH,10008833.SH,10008834.SH,10008835.SH,10008836.SH,10008837.SH,10008887.SH,10008903.SH,10009047.SH,10009194.SH,10008838.SH,10008839.SH,10008840.SH,10008841.SH,10008842.SH,10008843.SH,10008844.SH,10008845.SH,10008846.SH,10008888.SH,10008904.SH,10009048.SH,10009307.SH,10009253.SH,10009254.SH,10009255.SH,10009256.SH,10009257.SH,10009258.SH,10009259.SH,10009260.SH,10009261.SH,10009507.SH,10009308.SH,10009262.SH,10009263.SH,10009264.SH,10009265.SH,10009266.SH,10009267.SH,10009268.SH,10009269.SH,10009270.SH,10009508.SH\n", "             EXE_PRICE UNDERLYINGWINDCODE EXE_ENDDATE EXE_TYPE EXE_MODE\n", "10009359.SH       4.90          510500.SH  2025-07-23       欧式       认购\n", "10009360.SH       5.00          510500.SH  2025-07-23       欧式       认购\n", "10009361.SH       5.25          510500.SH  2025-07-23       欧式       认购\n", "10009362.SH       5.50          510500.SH  2025-07-23       欧式       认购\n", "10009363.SH       5.75          510500.SH  2025-07-23       欧式       认购\n", "...                ...                ...         ...      ...      ...\n", "10009267.SH       6.00          510500.SH  2025-12-24       欧式       认沽\n", "10009268.SH       6.25          510500.SH  2025-12-24       欧式       认沽\n", "10009269.SH       6.50          510500.SH  2025-12-24       欧式       认沽\n", "10009270.SH       6.75          510500.SH  2025-12-24       欧式       认沽\n", "10009508.SH       7.00          510500.SH  2025-12-24       欧式       认沽\n", "\n", "[86 rows x 5 columns]\n"]}], "source": ["codes=w.wset(\"optionchain\",f\"date={tradedate2};us_code={under};option_var=all;call_put=all\").Data[3]\n", "codes_str = ','.join([f\"{c}\" for c in codes])\n", "print(codes_str)\n", "basic = w.wss(codes_str,\"exe_price,underlyingwindcode,exe_enddate,exe_type,exe_mode\",f\"tradeDate={tradedate}\",usedf=True)[1]\n", "print(basic)\n", "basic.to_pickle(\"basic.pkl\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["basic = pd.read_pickle(\"basic.pkl\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["basic[\"code\"]=basic.index\n", "und = w.wsi(under, \"close\", f\"{tradedate2} 09:00:00\", f\"{tradedate2} 15:00:00\", \"BarSize=60\",usedf=True)[1][\"close\"]\n", "# und=2.559\n", "r = w.wss(\"TB1Y.WI\", \"close\",\"tradeDate=20200424;priceAdj=U;cycle=D\").Data[0][0]/100"]}, {"cell_type": "code", "execution_count": 190, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[                      close\n", "2025-07-01 10:30:00  1.0130\n", "2025-07-01 11:30:00  1.0100\n", "2025-07-01 14:00:00  1.0282\n", "2025-07-01 15:00:00  1.0237,                       close\n", "2025-07-01 10:30:00  0.9150\n", "2025-07-01 11:30:00  0.9080\n", "2025-07-01 14:00:00  0.9291\n", "2025-07-01 15:00:00  0.9234,                       close\n", "2025-07-01 10:30:00  0.6650\n", "2025-07-01 11:30:00  0.6589\n", "2025-07-01 14:00:00  0.6810\n", "2025-07-01 15:00:00  0.6760,                       close\n", "2025-07-01 10:30:00  0.4207\n", "2025-07-01 11:30:00  0.4137\n", "2025-07-01 14:00:00  0.4330\n", "2025-07-01 15:00:00  0.4300,                       close\n", "2025-07-01 10:30:00  0.1969\n", "2025-07-01 11:30:00  0.1889\n", "2025-07-01 14:00:00  0.2067\n", "2025-07-01 15:00:00  0.2014,                       close\n", "2025-07-01 10:30:00  0.0575\n", "2025-07-01 11:30:00  0.0526\n", "2025-07-01 14:00:00  0.0600\n", "2025-07-01 15:00:00  0.0561,                       close\n", "2025-07-01 10:30:00  0.0157\n", "2025-07-01 11:30:00  0.0128\n", "2025-07-01 14:00:00  0.0156\n", "2025-07-01 15:00:00  0.0136,                       close\n", "2025-07-01 10:30:00  0.0066\n", "2025-07-01 11:30:00  0.0053\n", "2025-07-01 14:00:00  0.0064\n", "2025-07-01 15:00:00  0.0058,                       close\n", "2025-07-01 10:30:00  0.0043\n", "2025-07-01 11:30:00  0.0032\n", "2025-07-01 14:00:00  0.0041\n", "2025-07-01 15:00:00  0.0037,                       close\n", "2025-07-01 10:30:00  0.0026\n", "2025-07-01 11:30:00  0.0019\n", "2025-07-01 14:00:00  0.0025\n", "2025-07-01 15:00:00  0.0023,                       close\n", "2025-07-01 10:30:00  0.0011\n", "2025-07-01 11:30:00  0.0012\n", "2025-07-01 14:00:00  0.0012\n", "2025-07-01 15:00:00  0.0015,                       close\n", "2025-07-01 10:30:00  0.0016\n", "2025-07-01 11:30:00  0.0017\n", "2025-07-01 14:00:00  0.0017\n", "2025-07-01 15:00:00  0.0016,                       close\n", "2025-07-01 10:30:00  0.0025\n", "2025-07-01 11:30:00  0.0028\n", "2025-07-01 14:00:00  0.0026\n", "2025-07-01 15:00:00  0.0025,                       close\n", "2025-07-01 10:30:00  0.0066\n", "2025-07-01 11:30:00  0.0069\n", "2025-07-01 14:00:00  0.0062\n", "2025-07-01 15:00:00  0.0065,                       close\n", "2025-07-01 10:30:00  0.0318\n", "2025-07-01 11:30:00  0.0326\n", "2025-07-01 14:00:00  0.0289\n", "2025-07-01 15:00:00  0.0289,                       close\n", "2025-07-01 10:30:00  0.1422\n", "2025-07-01 11:30:00  0.1453\n", "2025-07-01 14:00:00  0.1323\n", "2025-07-01 15:00:00  0.1327,                       close\n", "2025-07-01 10:30:00  0.3503\n", "2025-07-01 11:30:00  0.3559\n", "2025-07-01 14:00:00  0.3374\n", "2025-07-01 15:00:00  0.3378,                       close\n", "2025-07-01 10:30:00  0.5901\n", "2025-07-01 11:30:00  0.5990\n", "2025-07-01 14:00:00  0.5773\n", "2025-07-01 15:00:00  0.5802,                       close\n", "2025-07-01 10:30:00  0.8366\n", "2025-07-01 11:30:00  0.8508\n", "2025-07-01 14:00:00  0.8189\n", "2025-07-01 15:00:00  0.8290,                       close\n", "2025-07-01 10:30:00     NaN\n", "2025-07-01 11:30:00     NaN\n", "2025-07-01 14:00:00     NaN\n", "2025-07-01 15:00:00  1.0757,                       close\n", "2025-07-01 10:30:00  0.8610\n", "2025-07-01 11:30:00  0.8501\n", "2025-07-01 14:00:00  0.8899\n", "2025-07-01 15:00:00  0.8865,                       close\n", "2025-07-01 10:30:00  0.6392\n", "2025-07-01 11:30:00  0.6188\n", "2025-07-01 14:00:00  0.6500\n", "2025-07-01 15:00:00  0.6481,                       close\n", "2025-07-01 10:30:00  0.4100\n", "2025-07-01 11:30:00  0.3988\n", "2025-07-01 14:00:00  0.4220\n", "2025-07-01 15:00:00  0.4234,                       close\n", "2025-07-01 10:30:00  0.2235\n", "2025-07-01 11:30:00  0.2160\n", "2025-07-01 14:00:00  0.2321\n", "2025-07-01 15:00:00  0.2275,                       close\n", "2025-07-01 10:30:00  0.1048\n", "2025-07-01 11:30:00  0.0982\n", "2025-07-01 14:00:00  0.1070\n", "2025-07-01 15:00:00  0.1050,                       close\n", "2025-07-01 10:30:00  0.0474\n", "2025-07-01 11:30:00  0.0429\n", "2025-07-01 14:00:00  0.0482\n", "2025-07-01 15:00:00  0.0471,                       close\n", "2025-07-01 10:30:00  0.0245\n", "2025-07-01 11:30:00  0.0211\n", "2025-07-01 14:00:00  0.0241\n", "2025-07-01 15:00:00  0.0233,                       close\n", "2025-07-01 10:30:00  0.0143\n", "2025-07-01 11:30:00  0.0120\n", "2025-07-01 14:00:00  0.0138\n", "2025-07-01 15:00:00  0.0139,                       close\n", "2025-07-01 10:30:00  0.0097\n", "2025-07-01 11:30:00  0.0079\n", "2025-07-01 14:00:00  0.0091\n", "2025-07-01 15:00:00  0.0093,                       close\n", "2025-07-01 10:30:00  0.0069\n", "2025-07-01 11:30:00  0.0070\n", "2025-07-01 14:00:00  0.0068\n", "2025-07-01 15:00:00  0.0072,                       close\n", "2025-07-01 10:30:00  0.0140\n", "2025-07-01 11:30:00  0.0153\n", "2025-07-01 14:00:00  0.0143\n", "2025-07-01 15:00:00  0.0143,                       close\n", "2025-07-01 10:30:00  0.0351\n", "2025-07-01 11:30:00  0.0366\n", "2025-07-01 14:00:00  0.0342\n", "2025-07-01 15:00:00  0.0350,                       close\n", "2025-07-01 10:30:00  0.0966\n", "2025-07-01 11:30:00  0.0996\n", "2025-07-01 14:00:00  0.0932\n", "2025-07-01 15:00:00  0.0933,                       close\n", "2025-07-01 10:30:00  0.2266\n", "2025-07-01 11:30:00  0.2329\n", "2025-07-01 14:00:00  0.2185\n", "2025-07-01 15:00:00  0.2184,                       close\n", "2025-07-01 10:30:00  0.4200\n", "2025-07-01 11:30:00  0.4300\n", "2025-07-01 14:00:00  0.4077\n", "2025-07-01 15:00:00  0.4088,                       close\n", "2025-07-01 10:30:00  0.6559\n", "2025-07-01 11:30:00  0.6565\n", "2025-07-01 14:00:00  0.6296\n", "2025-07-01 15:00:00  0.6375,                       close\n", "2025-07-01 10:30:00  0.9013\n", "2025-07-01 11:30:00  0.8918\n", "2025-07-01 14:00:00  0.8693\n", "2025-07-01 15:00:00  0.8732,                      close\n", "2025-07-01 10:30:00  1.151\n", "2025-07-01 11:30:00    NaN\n", "2025-07-01 14:00:00    NaN\n", "2025-07-01 15:00:00  1.151,                       close\n", "2025-07-01 10:30:00  1.1170\n", "2025-07-01 11:30:00  1.1209\n", "2025-07-01 14:00:00  1.1484\n", "2025-07-01 15:00:00  1.1430,                       close\n", "2025-07-01 10:30:00  1.0216\n", "2025-07-01 11:30:00     NaN\n", "2025-07-01 14:00:00  1.0567\n", "2025-07-01 15:00:00  1.0567,                       close\n", "2025-07-01 10:30:00     NaN\n", "2025-07-01 11:30:00  0.9188\n", "2025-07-01 14:00:00     NaN\n", "2025-07-01 15:00:00  0.9188,                       close\n", "2025-07-01 10:30:00  0.8321\n", "2025-07-01 11:30:00  0.8279\n", "2025-07-01 14:00:00  0.8625\n", "2025-07-01 15:00:00  0.8531,                      close\n", "2025-07-01 10:30:00  0.616\n", "2025-07-01 11:30:00  0.610\n", "2025-07-01 14:00:00  0.634\n", "2025-07-01 15:00:00  0.625,                       close\n", "2025-07-01 10:30:00  0.4051\n", "2025-07-01 11:30:00  0.3972\n", "2025-07-01 14:00:00  0.4185\n", "2025-07-01 15:00:00  0.4127,                       close\n", "2025-07-01 10:30:00  0.2398\n", "2025-07-01 11:30:00  0.2330\n", "2025-07-01 14:00:00  0.2476\n", "2025-07-01 15:00:00  0.2424,                       close\n", "2025-07-01 10:30:00  0.1315\n", "2025-07-01 11:30:00  0.1265\n", "2025-07-01 14:00:00  0.1359\n", "2025-07-01 15:00:00  0.1318,                       close\n", "2025-07-01 10:30:00  0.0716\n", "2025-07-01 11:30:00  0.0670\n", "2025-07-01 14:00:00  0.0742\n", "2025-07-01 15:00:00  0.0708,                       close\n", "2025-07-01 10:30:00  0.0420\n", "2025-07-01 11:30:00  0.0396\n", "2025-07-01 14:00:00  0.0439\n", "2025-07-01 15:00:00  0.0413,                       close\n", "2025-07-01 10:30:00  0.0259\n", "2025-07-01 11:30:00  0.0230\n", "2025-07-01 14:00:00  0.0269\n", "2025-07-01 15:00:00  0.0257,                       close\n", "2025-07-01 10:30:00  0.0171\n", "2025-07-01 11:30:00  0.0148\n", "2025-07-01 14:00:00  0.0179\n", "2025-07-01 15:00:00  0.0163,                       close\n", "2025-07-01 10:30:00  0.0120\n", "2025-07-01 11:30:00  0.0102\n", "2025-07-01 14:00:00  0.0127\n", "2025-07-01 15:00:00  0.0120,                       close\n", "2025-07-01 10:30:00  0.0097\n", "2025-07-01 11:30:00  0.0098\n", "2025-07-01 14:00:00  0.0098\n", "2025-07-01 15:00:00  0.0089,                       close\n", "2025-07-01 10:30:00  0.0120\n", "2025-07-01 11:30:00  0.0123\n", "2025-07-01 14:00:00  0.0121\n", "2025-07-01 15:00:00  0.0112,                       close\n", "2025-07-01 10:30:00  0.0146\n", "2025-07-01 11:30:00  0.0153\n", "2025-07-01 14:00:00  0.0148\n", "2025-07-01 15:00:00  0.0139,                       close\n", "2025-07-01 10:30:00  0.0181\n", "2025-07-01 11:30:00  0.0189\n", "2025-07-01 14:00:00  0.0181\n", "2025-07-01 15:00:00  0.0179,                       close\n", "2025-07-01 10:30:00  0.0348\n", "2025-07-01 11:30:00  0.0364\n", "2025-07-01 14:00:00  0.0341\n", "2025-07-01 15:00:00  0.0335,                       close\n", "2025-07-01 10:30:00  0.0742\n", "2025-07-01 11:30:00  0.0769\n", "2025-07-01 14:00:00  0.0717\n", "2025-07-01 15:00:00  0.0721,                       close\n", "2025-07-01 10:30:00  0.1558\n", "2025-07-01 11:30:00  0.1601\n", "2025-07-01 14:00:00  0.1503\n", "2025-07-01 15:00:00  0.1516,                       close\n", "2025-07-01 10:30:00  0.2962\n", "2025-07-01 11:30:00  0.3023\n", "2025-07-01 14:00:00  0.2860\n", "2025-07-01 15:00:00  0.2864,                       close\n", "2025-07-01 10:30:00  0.4941\n", "2025-07-01 11:30:00  0.4921\n", "2025-07-01 14:00:00  0.4713\n", "2025-07-01 15:00:00  0.4752,                       close\n", "2025-07-01 10:30:00  0.7113\n", "2025-07-01 11:30:00     NaN\n", "2025-07-01 14:00:00  0.7038\n", "2025-07-01 15:00:00  0.7038,                       close\n", "2025-07-01 10:30:00  0.9450\n", "2025-07-01 11:30:00     NaN\n", "2025-07-01 14:00:00  0.9346\n", "2025-07-01 15:00:00  0.9346,                       close\n", "2025-07-01 10:30:00  1.1930\n", "2025-07-01 11:30:00     NaN\n", "2025-07-01 14:00:00  1.1628\n", "2025-07-01 15:00:00  1.1628,                       close\n", "2025-07-01 10:30:00  1.4389\n", "2025-07-01 11:30:00  1.4470\n", "2025-07-01 14:00:00  1.4050\n", "2025-07-01 15:00:00  1.4050,                       close\n", "2025-07-01 10:30:00  0.9590\n", "2025-07-01 11:30:00  0.9397\n", "2025-07-01 14:00:00  0.9720\n", "2025-07-01 15:00:00  0.9698,                       close\n", "2025-07-01 10:30:00  0.8566\n", "2025-07-01 11:30:00  0.8674\n", "2025-07-01 14:00:00     NaN\n", "2025-07-01 15:00:00  0.8674,                       close\n", "2025-07-01 10:30:00  0.7747\n", "2025-07-01 11:30:00  0.7764\n", "2025-07-01 14:00:00  0.7937\n", "2025-07-01 15:00:00  0.7887,                       close\n", "2025-07-01 10:30:00  0.5819\n", "2025-07-01 11:30:00  0.5700\n", "2025-07-01 14:00:00  0.5940\n", "2025-07-01 15:00:00  0.5880,                       close\n", "2025-07-01 10:30:00  0.4060\n", "2025-07-01 11:30:00  0.3982\n", "2025-07-01 14:00:00  0.4209\n", "2025-07-01 15:00:00  0.4159,                       close\n", "2025-07-01 10:30:00  0.2720\n", "2025-07-01 11:30:00  0.2639\n", "2025-07-01 14:00:00  0.2791\n", "2025-07-01 15:00:00  0.2783,                       close\n", "2025-07-01 10:30:00  0.1759\n", "2025-07-01 11:30:00  0.1704\n", "2025-07-01 14:00:00  0.1832\n", "2025-07-01 15:00:00  0.1801,                       close\n", "2025-07-01 10:30:00  0.1165\n", "2025-07-01 11:30:00  0.1101\n", "2025-07-01 14:00:00  0.1173\n", "2025-07-01 15:00:00  0.1165,                       close\n", "2025-07-01 10:30:00  0.0788\n", "2025-07-01 11:30:00  0.0730\n", "2025-07-01 14:00:00  0.0787\n", "2025-07-01 15:00:00  0.0782,                       close\n", "2025-07-01 10:30:00  0.0554\n", "2025-07-01 11:30:00  0.0493\n", "2025-07-01 14:00:00  0.0546\n", "2025-07-01 15:00:00  0.0548,                       close\n", "2025-07-01 10:30:00  0.0408\n", "2025-07-01 11:30:00  0.0358\n", "2025-07-01 14:00:00  0.0397\n", "2025-07-01 15:00:00  0.0395,                       close\n", "2025-07-01 10:30:00  0.0435\n", "2025-07-01 11:30:00  0.0458\n", "2025-07-01 14:00:00  0.0455\n", "2025-07-01 15:00:00  0.0449,                       close\n", "2025-07-01 10:30:00  0.0535\n", "2025-07-01 11:30:00  0.0560\n", "2025-07-01 14:00:00  0.0556\n", "2025-07-01 15:00:00  0.0547,                       close\n", "2025-07-01 10:30:00  0.0666\n", "2025-07-01 11:30:00  0.0679\n", "2025-07-01 14:00:00  0.0668\n", "2025-07-01 15:00:00  0.0656,                       close\n", "2025-07-01 10:30:00  0.1114\n", "2025-07-01 11:30:00  0.1138\n", "2025-07-01 14:00:00  0.1107\n", "2025-07-01 15:00:00  0.1102,                       close\n", "2025-07-01 10:30:00  0.1866\n", "2025-07-01 11:30:00  0.1904\n", "2025-07-01 14:00:00  0.1789\n", "2025-07-01 15:00:00  0.1832,                       close\n", "2025-07-01 10:30:00  0.2982\n", "2025-07-01 11:30:00  0.3003\n", "2025-07-01 14:00:00  0.2873\n", "2025-07-01 15:00:00  0.2884,                       close\n", "2025-07-01 10:30:00  0.4560\n", "2025-07-01 11:30:00  0.4660\n", "2025-07-01 14:00:00  0.4395\n", "2025-07-01 15:00:00  0.4421,                       close\n", "2025-07-01 10:30:00  0.6442\n", "2025-07-01 11:30:00  0.6480\n", "2025-07-01 14:00:00  0.6230\n", "2025-07-01 15:00:00  0.6274,                       close\n", "2025-07-01 10:30:00  0.8468\n", "2025-07-01 11:30:00     NaN\n", "2025-07-01 14:00:00  0.8337\n", "2025-07-01 15:00:00  0.8360,                       close\n", "2025-07-01 10:30:00  1.0693\n", "2025-07-01 11:30:00  1.0689\n", "2025-07-01 14:00:00  1.0699\n", "2025-07-01 15:00:00  1.0699,                       close\n", "2025-07-01 10:30:00  1.3041\n", "2025-07-01 11:30:00  1.3133\n", "2025-07-01 14:00:00  1.2889\n", "2025-07-01 15:00:00  1.2830]\n"]}], "source": ["opt_close = []\n", "for code in basic.index:\n", "    ret = w.wsi(code, \"close\", f\"{tradedate2} 09:30:00\", f\"{tradedate2} 15:00:00\", \"BarSize=60\",usedf=True)[1]\n", "    opt_close.append(ret)\n", "print(opt_close)"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                     10008829.SH  10008830.SH  10008831.SH  10008832.SH  \\\n", "2025-07-01 10:30:00       1.0216          NaN       0.8321        0.616   \n", "2025-07-01 11:30:00          NaN       0.9188       0.8279        0.610   \n", "2025-07-01 14:00:00       1.0567          NaN       0.8625        0.634   \n", "2025-07-01 15:00:00       1.0567       0.9188       0.8531        0.625   \n", "\n", "                     10008833.SH  10008834.SH  10008835.SH  10008836.SH  \\\n", "2025-07-01 10:30:00       0.4051       0.2398       0.1315       0.0716   \n", "2025-07-01 11:30:00       0.3972       0.2330       0.1265       0.0670   \n", "2025-07-01 14:00:00       0.4185       0.2476       0.1359       0.0742   \n", "2025-07-01 15:00:00       0.4127       0.2424       0.1318       0.0708   \n", "\n", "                     10008837.SH  10008838.SH  ...  10009553.SH  10009554.SH  \\\n", "2025-07-01 10:30:00       0.0420       0.0120  ...       0.0097       0.0069   \n", "2025-07-01 11:30:00       0.0396       0.0123  ...       0.0079       0.0070   \n", "2025-07-01 14:00:00       0.0439       0.0121  ...       0.0091       0.0068   \n", "2025-07-01 15:00:00       0.0413       0.0112  ...       0.0093       0.0072   \n", "\n", "                     10009555.SH  10009556.SH  10009557.SH  10009558.SH  \\\n", "2025-07-01 10:30:00       0.0140       0.0351       0.0966       0.2266   \n", "2025-07-01 11:30:00       0.0153       0.0366       0.0996       0.2329   \n", "2025-07-01 14:00:00       0.0143       0.0342       0.0932       0.2185   \n", "2025-07-01 15:00:00       0.0143       0.0350       0.0933       0.2184   \n", "\n", "                     10009559.SH  10009560.SH  10009561.SH  10009562.SH  \n", "2025-07-01 10:30:00       0.4200       0.6559       0.9013        1.151  \n", "2025-07-01 11:30:00       0.4300       0.6565       0.8918          NaN  \n", "2025-07-01 14:00:00       0.4077       0.6296       0.8693          NaN  \n", "2025-07-01 15:00:00       0.4088       0.6375       0.8732        1.151  \n", "\n", "[4 rows x 86 columns]\n"]}], "source": ["data = pd.concat({c:x[\"close\"] for c,x in zip(basic.index,opt_close) if \"close\" in x},axis=1)\n", "print(data)"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [], "source": ["call_code = pd.pivot_table(basic[basic[\"EXE_MODE\"]==\"认购\"].reset_index(),index=\"EXE_ENDDATE\",columns=\"EXE_PRICE\",values=\"code\",aggfunc=\"sum\")\n", "put_code = pd.pivot_table(basic[basic[\"EXE_MODE\"]==\"认沽\"].reset_index(),index=\"EXE_ENDDATE\",columns=\"EXE_PRICE\",values=\"code\",aggfunc=\"sum\")"]}, {"cell_type": "code", "execution_count": 192, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\553740145.py:5: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  call_close = call_code.applymap(find_val)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\553740145.py:6: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  put_close = put_code.applymap(find_val)\n"]}], "source": ["def find_val(x):\n", "    if x is np.nan or x not in data.iloc[0]:\n", "        return np.nan\n", "    return data.iloc[0][x]\n", "call_close = call_code.applymap(find_val)\n", "put_close = put_code.applymap(find_val)\n", "call_close.to_pickle(\"call_close.pkl\")\n", "put_close.to_pickle(\"put_close.pkl\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["call_close = pd.read_pickle(\"call_close.pkl\")\n", "put_close = pd.read_pickle(\"put_close.pkl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### calc q"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'und' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m S\u001b[38;5;241m=\u001b[39m\u001b[43mund\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m      3\u001b[0m atmx \u001b[38;5;241m=\u001b[39m (call_close \u001b[38;5;241m-\u001b[39m put_close)\u001b[38;5;241m.\u001b[39mabs()\u001b[38;5;241m.\u001b[39midxmin(axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m      5\u001b[0m tau \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mSeries((atmx\u001b[38;5;241m.\u001b[39mindex \u001b[38;5;241m-\u001b[39m tradedate3)\u001b[38;5;241m.\u001b[39mdays, index\u001b[38;5;241m=\u001b[39matmx\u001b[38;5;241m.\u001b[39mindex) \u001b[38;5;241m/\u001b[39m \u001b[38;5;241m365\u001b[39m\n", "\u001b[1;31mNameError\u001b[0m: name 'und' is not defined"]}], "source": ["S=und[0]\n", "\n", "atmx = (call_close - put_close).abs().idxmin(axis=1)\n", "\n", "tau = pd.Series((atmx.index - tradedate3).days, index=atmx.index) / 365\n", "print(tau)\n", "\n", "spread = (call_close-put_close)\n", "spread = pd.Series([spread.loc[row,col] for row,col in atmx.items()], index=spread.index)\n", "\n", "q = r-np.log((spread*np.exp(r*tau)+atmx)/S)/tau\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### calc iv"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'call_close' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 31\u001b[0m\n\u001b[0;32m     28\u001b[0m             vega[row,col] \u001b[38;5;241m=\u001b[39m calc_vega(S,IV[row,col],tau[row],strike,r)\n\u001b[0;32m     29\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m pd\u001b[38;5;241m.\u001b[39mDataFrame(IV,index\u001b[38;5;241m=\u001b[39mdata\u001b[38;5;241m.\u001b[39mindex,columns\u001b[38;5;241m=\u001b[39mdata\u001b[38;5;241m.\u001b[39mcolumns),pd\u001b[38;5;241m.\u001b[39mDataFrame(vega,index\u001b[38;5;241m=\u001b[39mdata\u001b[38;5;241m.\u001b[39mindex,columns\u001b[38;5;241m=\u001b[39mdata\u001b[38;5;241m.\u001b[39mcolumns)\n\u001b[1;32m---> 31\u001b[0m call_iv \u001b[38;5;241m=\u001b[39m calc_iv(\u001b[43mcall_close\u001b[49m,S,tau,r,q,\u001b[38;5;28;01mTrue\u001b[39;00m)[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m     32\u001b[0m vega \u001b[38;5;241m=\u001b[39m calc_iv(call_close,S,tau,r,q,\u001b[38;5;28;01mTrue\u001b[39;00m)[\u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m     33\u001b[0m put_iv \u001b[38;5;241m=\u001b[39m calc_iv(put_close,S,tau,r,q,\u001b[38;5;28;01mFalse\u001b[39;00m)[\u001b[38;5;241m0\u001b[39m]\n", "\u001b[1;31mNameError\u001b[0m: name 'call_close' is not defined"]}], "source": ["import numpy as np\n", "from scipy.stats import norm\n", "\n", "def bsm(iv,s,x,t,r,q,call=True):\n", "    d1 = (np.log(s/x) + (r-q+iv**2/2)*t)/(iv*np.sqrt(t))\n", "    d2 = d1 - iv*np.sqrt(t)\n", "    if call:\n", "        return s*np.exp(-q*t)*sp.stats.norm.cdf(d1)-x*np.exp(-r*t)*sp.stats.norm.cdf(d2)\n", "    else:\n", "        return x*np.exp(-r*t)*sp.stats.norm.cdf(-d2)-s*np.exp(-q*t)*sp.stats.norm.cdf(-d1)\n", "    \n", "def mae(iv,s,x,t,r,q,tar,call):\n", "    bsmv = bsm(iv,s,x,t,r,q,call)\n", "    return np.abs(bsmv-tar)\n", "\n", "def calc_vega(s,sigma,T,K,r):\n", "    d1=(np.log(s/K)+(r+0.5*sigma**2)*T)/(sigma*np.sqrt(T))\n", "    return s*np.sqrt(T)*norm.pdf(d1)\n", "\n", "def calc_iv(data,S,tau,r,q,call=True):\n", "    IV = np.full(data.shape,np.nan)\n", "    vega = np.full(data.shape,np.nan)\n", "    for row,idx in enumerate(data.index):\n", "        for col,strike in enumerate(data.columns):\n", "            if pd.isna(data.loc[idx,strike]):continue            \n", "            tar = data.loc[idx,strike]\n", "            IV[row,col] = opt.minimize_scalar(mae,bounds=(0.01,0.99),args=(S,strike,tau[row],r,q[row],tar,call)).x\n", "            vega[row,col] = calc_vega(S,IV[row,col],tau[row],strike,r)\n", "    return pd.DataFrame(IV,index=data.index,columns=data.columns),pd.DataFrame(vega,index=data.index,columns=data.columns)\n", "\n", "call_iv = calc_iv(call_close,S,tau,r,q,True)[0]\n", "vega = calc_iv(call_close,S,tau,r,q,True)[1]\n", "put_iv = calc_iv(put_close,S,tau,r,q,False)[0]\n", "\n", "print(vega)\n", "iv_surf = pd.concat([put_iv.iloc[:,put_iv.columns<S],call_iv.iloc[:,call_iv.columns>S]],axis=1)\n", "iv_surf.iloc[0].dropna().plot.line()\n", "totalv = np.square(iv_surf)*tau.values[(slice(None),None)]"]}, {"cell_type": "code", "execution_count": 225, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def svi_2steps(iv,x,init_msigma,vega_weights,maxiter=10,exit=1e-12,verbose=True):\n", "    opt_rmse=1\n", "\n", "    def svi_quasi(y,a,d,c):\n", "        return a+d*y+c*np.sqrt(np.square(y)+1)\n", "\n", "    def svi_quasi_rmse(iv,y,a,d,c):\n", "        return np.sqrt(np.mean(np.square(svi_quasi(y,a,d,c)-iv)))\n", "    \n", "    # 计算a,d,c\n", "    def calc_adc(iv,x,_m,_sigma):\n", "        y = (x-_m)/_sigma\n", "        s = max(_sigma,1e-6)\n", "        bnd = ((0,0,0),(max(iv.max(),1e-6),2*np.sqrt(2)*s,2*np.sqrt(2)*s))\n", "        z = np.sqrt(np.square(y)+1)\n", "        \n", "        # 此处等价于坐标轴旋转45°，这样写运行更快\n", "        A = np.column_stack([np.ones(len(iv)),np.sqrt(2)/2*(y+z),np.sqrt(2)/2*(-y+z)])\n", "        \n", "        a,d,c = opt.lsq_linear(A,iv,bnd,tol=1e-12,verbose=False).x\n", "        return a,np.sqrt(2)/2*(d-c),np.sqrt(2)/2*(d+c)\n", "    \n", "\n", "    def opt_msigma(msigma):\n", "        _m,_sigma = msigma\n", "        _y = (x-_m)/_sigma \n", "        _a,_d,_c = calc_adc(iv,x,_m,_sigma)\n", "        return np.sum(np.square(_a+_d*_y+_c*np.sqrt(np.square(_y)+1)-iv))\n", "\n", "    for i in range(1,maxiter+1):\n", "        #a_star,d_star,c_star = calc_adc(iv,x,init_msigma)       \n", "        m_star,sigma_star = opt.minimize(opt_msigma,\n", "                                         init_msigma,\n", "                                         method='<PERSON><PERSON><PERSON>-<PERSON>',\n", "                                         bounds=((2*min(x.min(),0), 2*max(x.max(),0)),(1e-6,1)),\n", "                                         tol=1e-12).x\n", "        \n", "        a_star,d_star,c_star = calc_adc(iv,x,m_star,sigma_star)\n", "        opt_rmse1 = svi_quasi_rmse(iv,(x-m_star)/sigma_star,a_star,d_star,c_star)\n", "        if verbose:\n", "            print(f\"round {i}: RMSE={opt_rmse1} para={[a_star,d_star,c_star,m_star,sigma_star]}     \")\n", "        if i>1 and opt_rmse-opt_rmse1<exit:\n", "            break\n", "        opt_rmse = opt_rmse1\n", "        init_msigma = [m_star,sigma_star]\n", "        \n", "    result = np.array([a_star,d_star,c_star,m_star,sigma_star,opt_rmse1])\n", "    if verbose:\n", "        print(f\"\\nfinished. params = {result[:5].round(10)}\")\n", "    return result\n", "\n", "\n", "def svi_quasi(x,a,d,c,m,sigma):\n", "    y = (x-m)/sigma\n", "    return a+d*y+c*np.sqrt(np.square(y)+1)\n", "\n", "\n", "class svi_quasi_model:\n", "    def __init__(self,a,d,c,m,sigma):\n", "        self.a = a\n", "        self.d = d\n", "        self.c = c\n", "        self.m = m\n", "        self.sigma = sigma\n", "    def __call__(self,x):\n", "        return svi_quasi(x,self.a,self.d,self.c,self.m,self.sigma)\n", "\n", "\n", "res = []\n", "for i in range(len(totalv)):\n", "    mix_data = pd.concat([totalv.iloc[i],vega.iloc[i]],axis=1).dropna()\n", "    ava_data = mix_data.iloc[:,0]\n", "    vega_data = mix_data.iloc[:,1]\n", "    vega_weights = vega_data/vega_data.sum()\n", "    logm = np.log(ava_data.index.values/(S*np.exp((r-q.iloc[i])*tau.iloc[i])))\n", "    a,d,c,m,sigma,rmse = svi_2steps(ava_data.values,logm,[0.05,0.1],vega_weights.values,10,verbose=False)\n", "    res.append((logm,ava_data,(a,d,c,m,sigma),rmse))\n", "\n", "lmax,lmin = 0.28,-0.18\n", "lin = np.linspace(lmin,lmax,100)\n", "plt.figure(figsize=( 8,4))\n", "for i in range(len(res)):\n", "    model = svi_quasi_model(*res[i][2])\n", "    t=tau.iloc[i]\n", "    logm=res[i][0]\n", "    tv = res[i][1]    \n", "    plt.plot(np.exp(logm), np.sqrt(tv/t), '+', markersize=12)\n", "    plt.plot(np.exp(lin),np.sqrt(model(lin)/t),linewidth=1,label=str(int(tau.iloc[i]*365)))\n", "    plt.title(\"Implied Volatility Curve\")\n", "    plt.xlabel(\"Moneyness\", fontsize=12)\n", "    plt.legend()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 226, "metadata": {}, "outputs": [], "source": ["def raw_svi(par, k):\n", "    w = par[0] + par[1] * (par[2] * (k - par[3]) + (\n", "                (k - par[3]) ** 2 + par[4] ** 2) ** 0.5)\n", "    return w\n", "def diff_svi(par, k):\n", "    a, b, rho, m, sigma = par\n", "    return b*(rho+(k-m)/(np.sqrt((k-m)**2+sigma**2)))\n", "def diff2_svi(par, k):\n", "    a, b, rho, m, sigma = par\n", "    disc = (k-m)**2 + sigma**2\n", "    return (b*sigma**2)/((disc)**(3/2))\n", "def gfun(par, k):\n", "    w = raw_svi(par, k)\n", "    w1 = diff_svi(par, k)\n", "    w2 = diff2_svi(par, k)\n", "    g = (1-0.5*(k*w1/w))**2 - (0.25*w1**2)*(w**-1+0.25) + 0.5*w2\n", "    return g\n", "def d2(par, k):\n", "    v = np.sqrt(raw_svi(par, k))\n", "    return -k/v - 0.5*v\n", "def density(par, k):\n", "    g = gfun(par, k)\n", "    w = raw_svi(par, k)\n", "    dtwo = d2(par, k)\n", "    dens = (g / np.sqrt(2 * np.pi * w)) * np.exp(-0.5 * dtwo**2)\n", "    return dens"]}, {"cell_type": "code", "execution_count": 227, "metadata": {}, "outputs": [], "source": ["def g(par,k):\n", "    a,b,rho,m,sig = par\n", "    discr = np.sqrt((k-m)*(k-m) + sig*sig);\n", "    w = a + b *(rho*(k-m)+ discr);\n", "    dw = b*rho + b *(k-m)/discr;\n", "    d2w = b*sig**2/(discr*discr*discr);\n", "    \n", "    return 1 - k*dw/w + dw*dw/4*(-1/w+k*k/(w*w)-4) +d2w/2"]}, {"cell_type": "code", "execution_count": 228, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\2391418233.py:5: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  t=tau[i]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\3878821878.py:19: RuntimeWarning: invalid value encountered in sqrt\n", "  v = np.sqrt(raw_svi(par, k))\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\3878821878.py:25: RuntimeWarning: invalid value encountered in sqrt\n", "  dens = (g / np.sqrt(2 * np.pi * w)) * np.exp(-0.5 * dtwo**2)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\2391418233.py:8: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  plt.plot(np.exp(lin),density(res[i][2],lin),linewidth=1,label=str(int(tau[i]*365)))\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lmax,lmin = 0.28,-0.18\n", "lin = np.linspace(lmin,lmax,100)\n", "plt.figure(figsize=(8,4))\n", "for i in range(len(res)):\n", "    t=tau[i]\n", "    logm=res[i][0]\n", "    tv = res[i][1]    \n", "    plt.plot(np.exp(lin),density(res[i][2],lin),linewidth=1,label=str(int(tau[i]*365)))\n", "plt.plot(np.exp(lin),np.zeros(100),linewidth=1,color=\"black\")\n", "plt.title(\"Density\")\n", "plt.xlabel(\"Moneyness\", fontsize=12)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 213, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\858903500.py:5: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  t=tau[i]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\3878821878.py:19: RuntimeWarning: invalid value encountered in sqrt\n", "  v = np.sqrt(raw_svi(par, k))\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\3878821878.py:25: RuntimeWarning: invalid value encountered in sqrt\n", "  dens = (g / np.sqrt(2 * np.pi * w)) * np.exp(-0.5 * dtwo**2)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_117476\\858903500.py:8: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  plt.plot(np.exp(lin),density(res[i][2],lin),linewidth=1,label=str(int(tau[i]*365)))\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lmax,lmin = 0.28,-0.18\n", "lin = np.linspace(lmin,lmax,100)\n", "plt.figure(figsize=(8,4))\n", "for i in range(len(res)):\n", "    t=tau[i]\n", "    logm=res[i][0]\n", "    tv = res[i][1]    \n", "    plt.plot(np.exp(lin),density(res[i][2],lin),linewidth=1,label=str(int(tau[i]*365)))"]}, {"cell_type": "code", "execution_count": 214, "metadata": {}, "outputs": [], "source": ["surf = np.zeros((4,25))\n", "lin = np.linspace(0.9,1.15,25)\n", "for i in range(4):\n", "    model = svi_quasi_model(*res[i][2])\n", "    surf[i,:] = model(np.log(lin))"]}, {"cell_type": "code", "execution_count": 215, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "A value (187.0) in x_new is above the interpolation range's maximum value (176.0).", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[215], line 4\u001b[0m\n\u001b[0;32m      2\u001b[0m tt \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mlinspace(\u001b[38;5;241m33\u001b[39m,\u001b[38;5;241m243\u001b[39m,\u001b[38;5;241m16\u001b[39m)\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m j \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m25\u001b[39m):\n\u001b[1;32m----> 4\u001b[0m     final_surf[:,j] \u001b[38;5;241m=\u001b[39m sp\u001b[38;5;241m.\u001b[39minterpolate\u001b[38;5;241m.\u001b[39minterp1d(tau\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m365\u001b[39m,surf[:,j])(tt)\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\scipy\\interpolate\\_polyint.py:81\u001b[0m, in \u001b[0;36m_Interpolator1D.__call__\u001b[1;34m(self, x)\u001b[0m\n\u001b[0;32m     60\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     61\u001b[0m \u001b[38;5;124;03mEvaluate the interpolant\u001b[39;00m\n\u001b[0;32m     62\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     78\u001b[0m \n\u001b[0;32m     79\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     80\u001b[0m x, x_shape \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_x(x)\n\u001b[1;32m---> 81\u001b[0m y \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_evaluate(x)\n\u001b[0;32m     82\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_finish_y(y, x_shape)\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\scipy\\interpolate\\_interpolate.py:766\u001b[0m, in \u001b[0;36minterp1d._evaluate\u001b[1;34m(self, x_new)\u001b[0m\n\u001b[0;32m    764\u001b[0m y_new \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_call(\u001b[38;5;28mself\u001b[39m, x_new)\n\u001b[0;32m    765\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_extrapolate:\n\u001b[1;32m--> 766\u001b[0m     below_bounds, above_bounds \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_bounds(x_new)\n\u001b[0;32m    767\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(y_new) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m    768\u001b[0m         \u001b[38;5;66;03m# Note fill_value must be broadcast up to the proper size\u001b[39;00m\n\u001b[0;32m    769\u001b[0m         \u001b[38;5;66;03m# and flattened to work here\u001b[39;00m\n\u001b[0;32m    770\u001b[0m         y_new[below_bounds] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fill_value_below\n", "File \u001b[1;32me:\\anaconda3\\Lib\\site-packages\\scipy\\interpolate\\_interpolate.py:799\u001b[0m, in \u001b[0;36minterp1d._check_bounds\u001b[1;34m(self, x_new)\u001b[0m\n\u001b[0;32m    797\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbounds_error \u001b[38;5;129;01mand\u001b[39;00m above_bounds\u001b[38;5;241m.\u001b[39many():\n\u001b[0;32m    798\u001b[0m     above_bounds_value \u001b[38;5;241m=\u001b[39m x_new[np\u001b[38;5;241m.\u001b[39margmax(above_bounds)]\n\u001b[1;32m--> 799\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValue<PERSON>rror\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mA value (\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mabove_bounds_value\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m) in x_new is above \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    800\u001b[0m                      \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthe interpolation range\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124ms maximum value (\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mx[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m).\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    802\u001b[0m \u001b[38;5;66;03m# !! Should we emit a warning if some values are out of bounds?\u001b[39;00m\n\u001b[0;32m    803\u001b[0m \u001b[38;5;66;03m# !! matlab does not.\u001b[39;00m\n\u001b[0;32m    804\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m below_bounds, above_bounds\n", "\u001b[1;31mValueError\u001b[0m: A value (187.0) in x_new is above the interpolation range's maximum value (176.0)."]}], "source": ["final_surf = np.zeros((16,25))\n", "tt = np.linspace(33,243,16)\n", "for j in range(25):\n", "    final_surf[:,j] = sp.interpolate.interp1d(tau*365,surf[:,j])(tt)"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [], "source": ["from mpl_toolkits.mplot3d import Axes3D"]}, {"cell_type": "code", "execution_count": 186, "metadata": {}, "outputs": [{"data": {"text/plain": ["(16, 25)"]}, "execution_count": 186, "metadata": {}, "output_type": "execute_result"}], "source": ["final_surf.shape"]}, {"cell_type": "code", "execution_count": 191, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig = plt.figure() \n", "axes3d = Axes3D(fig) \n", "ttt = tt/365\n", "<PERSON>,T = np.meshgrid(lin,tt)\n", "axes3d.plot_surface(M,T,np.sqrt(final_surf/tt[slice(None),None]),rstride=1,cstride=1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 175, "metadata": {}, "outputs": [{"data": {"text/plain": ["(16, 25)"]}, "execution_count": 175, "metadata": {}, "output_type": "execute_result"}], "source": ["final_surf.shape"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [{"data": {"text/plain": ["<mpl_toolkits.mplot3d.art3d.Poly3DCollection at 0x1e8011629c8>"]}, "execution_count": 170, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig = plt.figure()\n", "'''创建3D轴对象'''\n", "ax = Axes3D(fig)\n", "'''X坐标数据'''\n", "X = np.arange(-2,2,0.1)\n", "'''Y坐标数据'''\n", "Y = np.arange(-2,2,0.1)\n", "'''计算3维曲面分格线坐标'''\n", "X,Y = np.meshgrid(X,Y)\n", "'''用于计算X/Y对应的Z值'''\n", "def f(x,y):\n", "    return (1-y**5+x**5)*np.exp(-x**2-y**2)\n", "'''plot_surface函数可绘制对应的曲面'''\n", "ax.plot_surface(X,Y,f(X,Y),rstride=1,cstride=1)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}