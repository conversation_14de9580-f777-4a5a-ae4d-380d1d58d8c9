# -*- coding:utf-8 -*-
# Author:lining
# Editdate:2017-8-24
from WindPy import w
import pymssql
from datetime import datetime

server = '***********'
user = 'Alex'
password = '789456'
dt = datetime.now()
# beginDate = "2017-06-24"
w.start()

# 命令如何写可以用命令生成器来辅助完成
# 定义打印输出函数，用来展示数据使用
# 连接数据库
conn = pymssql.connect(server, user, password, 'Alex')
cursor = conn.cursor()

def tosql(code):
    sql = "INSERT INTO ZZStks_IPO VALUES (%s,%d, %s, %d, %d, %d, %d, %d, %d,%d)"

    # 通过wsd来提取时间序列数据，比如取开高低收成交量，成交额数据
    print("\n\n-----通过wsd来提取 %s 开高低收成交量数据-----\n" % (code,))

    wssdata = w.wss(str(code), 'ipo_date')
    wsddata1 = w.wsd(code, "open,high,low,close,volume,amt", wssdata.Data[0][0], dt,
                     "Fill=Previous")

    print(wsddata1)
    for i in range(0, len(wsddata1.Data[1])):
        sqllist = []
        sqltuple = ()
        if len(wsddata1.Times) > 1:
            sqllist.append(wsddata1.Times[i].strftime('%Y-%m-%d'))
            sqllist.append("00:00:00.0000001")

        sqllist.append(code)

        for k in range(0, len(wsddata1.Fields)):
            sqllist.append(wsddata1.Data[k][i])

        sqllist.append(-1)

        sqltuple = tuple(sqllist)
        cursor.execute(sql, sqltuple)
        conn.commit()


tosql(code="000021.SZ")

conn.close()