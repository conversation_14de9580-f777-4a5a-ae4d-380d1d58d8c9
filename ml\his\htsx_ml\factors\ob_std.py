import pandas as pd
import numpy as np


#计算window窗口内的差
def delta(df, column, window):
    return df[column].diff(periods=window)
def delta2(df, window):
    return df.diff(periods=window)

#排名函数
def rank(series):
    """对时间序列数据进行滚动排名，避免未来数据泄露"""
    #这样算太慢了，感觉rank用到的未来的信息很弱？
    #return series.expanding().apply(lambda x: x.rank(pct=True).iloc[-1])

    return series.rank(pct=True)

#线性衰减移动平均 decay_linear(x, d)
def decay_linear(series, d):
    weights = np.linspace(d, 1, d)  # 权重是线性衰减
    weights /= np.sum(weights)  # 归一化，使得总和为1
    return series.rolling(window=d).apply(lambda x: np.dot(x, weights), raw=True)


#符号函数 sign(x)
def sign(series):
    return np.sign(series)

#计算时间序列相关性 correlation(x, y, d)
def correlation(df, col1, col2, d):
    return df[col1].rolling(window=d).corr(df[col2])

def correlation2(x, y, d):
    """计算x和y的d天时间序列相关性"""
    return x.rolling(window=d).corr(y)


#计算 sum(x, d) — 计算过去d天的和
def mysum(series, d):
    return series.rolling(window=d).sum()

#标准化 scale(x, a) — 将数据标准化为 abs(x)之和为a
def scale(series, a=1):
    return series / series.abs().sum() * a

#计算时间序列的最小值 ts_min(x, d)
def ts_min(series, d):
    return series.rolling(window=d).min()

def ts_max(x, d):
    """计算x的d天时间序列最大值"""
    return x.rolling(window=d).max()

#计算时间序列的排名 ts_rank(x, d)
def ts_rank(x, d):
    """计算x的d天时间序列排名"""
    return x.rolling(window=d).apply(lambda x: (x.rank(pct=True).iloc[-1]), raw=False)

def ts_argmax(x, d):
    """计算x的d天时间序列最大值的位置"""
    return x.rolling(window=d).apply(lambda x: x.argmax(), raw=True)

def ts_argmin(x, d):
    """计算过去d天内x的最小值所在的位置（天数索引）"""
    return x.rolling(window=d).apply(lambda x: x.argmin(), raw=True)


#计算 delay(x, d) — 获取d天前的值
def delay(series, d):
    return series.shift(d)

def signedpower(x, a):
    """计算x的a次方"""
    return x ** a

def stddev(x, d):
    """计算x的d天滚动标准差"""
    return x.rolling(window=d).std()


def vwap(df):
    """计算VWAP"""
    volume_diff = df['Volume'].diff()  # 成交量变化
    vwap = (df['LastPrice'] * volume_diff).cumsum() / volume_diff.cumsum()
    return vwap



# 部分特征构造函数定义
def order_imb(df, level=None):  #IC 0.05
    if level is None:
        level = '1'
    bidvol = 'BidVol' + level
    askvol = 'AskVol' + level
    return (df[bidvol] - df[askvol]) / (df[bidvol] + df[askvol])


def order_price_mid(df, level=None):   #IC 0.05
    if level is None:
        level = '1'
    bidvol = 'BidVol' + level
    askvol = 'AskVol' + level
    bidprice = 'BidPrice' + level
    askprice = 'AskPrice' + level
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    return (df[bidvol] * df[askprice] + df[askvol] * df[bidprice]) / (df[bidvol] + df[askvol]) - mid


def nettradeprice_mid(df, contract_mul=200):  #IC 0.11
    trade_vol = df['Volume'].diff()
    trade_value = df['TotalValueTraded'].diff()
    mid = (df['AskPrice1'] + df['BidPrice1']) / 2
    net_tradeprice = trade_value / (trade_vol * contract_mul)
    net_tradeprice = np.where(pd.isna(net_tradeprice), mid, net_tradeprice)
    return net_tradeprice - mid


def mom_last(df, window=None):  #IC 0.07
    if window is None:
        window = 1
    shift_price = df['LastPrice'].shift(window)
    return np.where(pd.isna(shift_price), 0, np.log(df['LastPrice'] / shift_price))


def reversal_last(df, window=None): #IC 0.08
    if window is None:
        window = 10
    ma = df['LastPrice'].rolling(window=window).mean()
    std = df['LastPrice'].rolling(window=window).std()

    return np.nan_to_num((df['LastPrice'] - ma) / std, posinf=0, neginf=0)




#factor from YA
#lastprice长短线均值之差
def feature_long_short_mean_diff(df,long_window=None,short_window=None): #IC -0.02
    if long_window is None:
        long_window = 50
    if short_window is None:
        short_window = 10
    long_mean= df['LastPrice'].rolling(window=long_window).mean()
    short_mean=df['LastPrice'].rolling(window=short_window).mean()
    factor= np.nan_to_num((short_mean-long_mean)/df['LastPrice'], posinf=0, neginf=0)
    return np.nan_to_num(factor, posinf=0, neginf=0)
    #return np.nan_to_num((short_mean-long_mean), posinf=0, neginf=0)

# #lastprice长短线最大值之差
# def long_short_max_diff(df,long_window=None,short_window=None): #IC -0.01
#     if long_window is None:
#         long_window = 50
#     if short_window is None:
#         short_window = 10
#     long_max= df['LastPrice'].rolling(window=long_window).max()
#     short_max=df['LastPrice'].rolling(window=short_window).max()
#     return np.nan_to_num((short_max-long_max)/df['LastPrice'], posinf=0, neginf=0)
#     #return np.nan_to_num((short_max - long_max), posinf=0, neginf=0)

#lastprice长短线最小值之差
def feature_long_short_min_diff(df,long_window=None,short_window=None): #IC -0.02
    if long_window is None:
        long_window = 50
    if short_window is None:
        short_window = 10
    long_min= df['LastPrice'].rolling(window=long_window).min()
    short_min=df['LastPrice'].rolling(window=short_window).min()
    factor = np.nan_to_num((short_min-long_min)/df['LastPrice'], posinf=0, neginf=0)
    return np.nan_to_num(factor, posinf=0, neginf=0)
    #return np.nan_to_num((short_min - long_min), posinf=0, neginf=0)

# #lastprice过去一段时间的趋势因子
# def rate_mean(df,window=None): #IC -0.01
#     if window is None:
#         window = 100
#     rate=(df['LastPrice']-df['LastPrice'].shift(1))
#     return np.nan_to_num((rate.rolling(window=window).apply(lambda x: (x > 0).sum(),\
#                         raw=True))/window, posinf=0, neginf=0)



#相关性类因子(算trade_vol,trade_value,LastPrice两两的相关性,发现trade_value和LastPrice相关性因子有效，IC达到0.06)
def feature_corr_value_lastprice(df,window=None):   #IC 0.02
    if window is None:
        window = 6
    a = df['TotalValueTraded'].diff()
    b = df['LastPrice']
    factor= pd.concat([a, b], axis=1).\
               rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)
    return np.nan_to_num(factor, posinf=0, neginf=0)



# def corr_value_bidprice(df,window=None):   #IC 0.01
#     if window is None:
#         window = 6
#     a = df['TotalValueTraded'].diff()
#     b = df['BidPrice1']
#     return pd.concat([a, b], axis=1).\
#                rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)


# def feature_corr_value_askprice_rank(df,window=None):   #IC -0.00 0  value与askprice的秩相关系数因子的IC值要比相关系数因子的IC值高
#     if window is None:
#         window = 6
#     a = df['TotalValueTraded'].diff().rank()
#     b = df['AskPrice1'].rank()
#     return pd.concat([a, b], axis=1).\
#                rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)



#根据askprice和bidprice的volume算加权价格，据此构造的一系列因子
def feature_vwap_1(df):  #IC 0.01
    vwap_bid=(df['BidPrice1']*df['BidVol1']+df['BidPrice2']*df['BidVol2']\
             +df['BidPrice3']*df['BidVol3']+df['BidPrice4']*df['BidVol4']\
             +df['BidPrice5']*df['BidVol5'])/(df['BidVol1']+df['BidVol2']\
                                              +df['BidVol3']+df['BidVol4']+df['BidVol5'])
    vwap_ask=(df['AskPrice1']*df['AskVol1']+df['AskPrice2']*df['AskVol2']\
             +df['AskPrice3']*df['AskVol3']+df['AskPrice4']*df['AskVol4']\
             +df['AskPrice5']*df['AskVol5'])/(df['AskVol1']+df['AskVol2']\
                                              +df['AskVol3']+df['AskVol4']+df['AskVol5'])
    # 这两个是否加上abs(共4种情况)都试过了,相关性均为+-1,一个因子就行了
    factor =(df['BidPrice1'] -vwap_bid)*(df['AskPrice1'] - vwap_ask)
    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_vwap_2(df): #IC -0.01
    vwap_bid=(df['BidPrice1']*df['BidVol1']+df['BidPrice2']*df['BidVol2']\
             +df['BidPrice3']*df['BidVol3']+df['BidPrice4']*df['BidVol4']\
             +df['BidPrice5']*df['BidVol5'])/(df['BidVol1']+df['BidVol2']\
                                              +df['BidVol3']+df['BidVol4']+df['BidVol5'])
    vwap_ask=(df['AskPrice1']*df['AskVol1']+df['AskPrice2']*df['AskVol2']\
             +df['AskPrice3']*df['AskVol3']+df['AskPrice4']*df['AskVol4']\
             +df['AskPrice5']*df['AskVol5'])/(df['AskVol1']+df['AskVol2']\
                                              +df['AskVol3']+df['AskVol4']+df['AskVol5'])

    factor =(df['BidPrice1'] -vwap_bid.rolling(window=10, min_periods=1).mean()).abs()*\
            (df['AskPrice1'] - vwap_ask.rolling(window=10, min_periods=1).mean())

    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_vwap_3(df): #IC -0.01
    vwap_bid=(df['BidPrice1']*df['BidVol1']+df['BidPrice2']*df['BidVol2']\
             +df['BidPrice3']*df['BidVol3']+df['BidPrice4']*df['BidVol4']\
             +df['BidPrice5']*df['BidVol5'])/(df['BidVol1']+df['BidVol2']\
                                              +df['BidVol3']+df['BidVol4']+df['BidVol5'])
    vwap_ask=(df['AskPrice1']*df['AskVol1']+df['AskPrice2']*df['AskVol2']\
             +df['AskPrice3']*df['AskVol3']+df['AskPrice4']*df['AskVol4']\
             +df['AskPrice5']*df['AskVol5'])/(df['AskVol1']+df['AskVol2']\
                                              +df['AskVol3']+df['AskVol4']+df['AskVol5'])

    factor =(df['BidPrice1'] -vwap_bid.rolling(window=10, min_periods=1).mean())*\
            (df['AskPrice1'] - vwap_ask.rolling(window=10, min_periods=1).mean()).abs()

    return np.nan_to_num(factor, posinf=0, neginf=0)



#vol_mul_rank因子(LastPrice测试效果IC为0)
def feature_vol_mul_rank(df,window1=None,window2=None): #IC -0.01
    if window1 is None:
        window1 = 5
    if window2 is None:
        window2 = 10

    a=df['AskVol1'].rolling(window=window1, min_periods=1).mean()
    b=df['BidVol1'].rolling(window=window1, min_periods=1).mean()

    def rank_percent(series):
        rank = series.rank(method='min', ascending=False)
        return rank.iloc[-1] / len(rank)  # 当前行排名占总元素个数的百分比

    # 对每列分别计算滚动位次百分比
    factor=(a*b).rolling(window=len(a*b), min_periods=1).apply(rank_percent, raw=False) #-(a*b).shift(window2)
    return np.nan_to_num(factor, posinf=0, neginf=0)



def feature_price_mul_rank(df, window1=None, window2=None): #IC 0.01
    if window1 is None:
        window1 = 5
    if window2 is None:
        window2 = 10

    a = df['AskPrice1'].rolling(window=window1, min_periods=1).mean()
    b = df['BidPrice1'].rolling(window=window1, min_periods=1).mean()

    def rank_percent(series):
        rank = series.rank(method='min', ascending=False)
        return rank.iloc[-1] / len(rank)  # 当前行排名占总元素个数的百分比

    # 对每列分别计算滚动位次百分比
    factor = (a * b).rolling(window=len(a * b), min_periods=1).apply(rank_percent,raw=False)  # -(a*b).shift(window2)
    return np.nan_to_num(factor, posinf=0, neginf=0)
    #参考因子8:temp = -1 * rank(sum(df['open_badj'], 5) * sum(df['ret'], 5))
    # - delay(sum(df['open_badj'], 5) * sum(df['ret'], 5),10)



def feature_rising_n_falling_trends(df, window1=None, window2=None):   #IC -0.06  精品
    #此因子逻辑为若window2长度窗口上lp_rate值均大于0或均小于0，则lp_rate为原值；
    #若window2长度窗口上lp_rate最小值小于0，最大值大于0，则lp_rate为其相反数
    #可以加上rank,但提升不大
    if window1 is None:
        window1 = 1
    if window2 is None:
        window2 = 5

    lp_rate = df['LastPrice'].diff(window1)
    # 计算 s1：过去 window1 窗口内 lp_rate 的最小值是否大于 0
    s1 = lp_rate.rolling(window=window2, min_periods=1).min() > 0
    # 计算 s2：过去 window1 窗口内 lp_rate 的最大值是否小于 0
    s2 = lp_rate.rolling(window=window2, min_periods=1).max() < 0

    factor=s1*lp_rate+(~s1)*(s2*lp_rate+(~s2)*(-lp_rate))
    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_rising_n_falling_trends_2(df, window1=None, window2=None):   #IC -0.05  精品
    #此因子逻辑为若window2长度窗口上lp_rate值均大于0或均小于0，则lp_rate为原值；
    #若window2长度窗口上lp_rate最小值小于0，最大值大于0，则lp_rate为其相反数
    if window1 is None:
        window1 = 1
    if window2 is None:
        window2 = 5

    lp_rate = ((df['BidVol1'] - df['AskVol1']) / (df['BidVol1'] + df['AskVol1'])).diff(window1)
    # 计算 s1：过去 window1 窗口内 lp_rate 的最小值是否大于 0
    s1 = lp_rate.rolling(window=window2, min_periods=1).min() > 0
    # 计算 s2：过去 window1 窗口内 lp_rate 的最大值是否小于 0
    s2 = lp_rate.rolling(window=window2, min_periods=1).max() < 0

    factor=s1*lp_rate+(~s1)*(s2*lp_rate+(~s2)*(-lp_rate))
    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_sign_vol_lastprice(df): #IC -0.02
    factor=np.sign((df['Volume'].diff()).diff())*(-1)*df['LastPrice'].diff()
    return np.nan_to_num(factor, posinf=0, neginf=0)
    #参考alpha12: temp = np.sign(diff(df['volume'], 1)) * (-1) * diff(df['close_badj'], 1)



#cov和rank结合起来的一系列因子
def feature_rank_cov_1(df,window=None): #IC -0.02
    if window is None:
        window = 6
    a = df['LastPrice']
    b = df['TotalValueTraded'].diff()
    c = df['LastPrice'].diff()
    factor= (-1)*c.rank()*pd.concat([a, b], axis=1).\
               rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)
    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_rank_cov_2(df,window=None): #IC -0.05
    if window is None:
        window = 6
    a = df['AskPrice1']
    b = df['BidPrice1']
    c = df['LastPrice'].diff()
    factor= (-1)*c.rank()*pd.concat([a, b], axis=1).\
               rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)
    return np.nan_to_num(factor, posinf=0, neginf=0)

def feature_rank_cov_3(df,window=None): #IC -0.04
    if window is None:
        window = 6
    a = df['LastPrice']
    b = df['AskPrice1']
    c = df['LastPrice'].diff()
    factor= (-1)*c.rank()*pd.concat([a, b], axis=1).\
               rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)
    return np.nan_to_num(factor, posinf=0, neginf=0)

def feature_rank_cov_4(df,window=None): #IC -0.04
    if window is None:
        window = 6
    a = df['LastPrice']
    b = df['BidPrice1']
    c = df['LastPrice'].diff()
    factor= (-1)*c.rank()*pd.concat([a, b], axis=1).\
               rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)
    return np.nan_to_num(factor, posinf=0, neginf=0)

# def feature_rank_cov_5(df,window=None): #IC -0.02   与feature_rank_cov_1的相关性完全为1，舍弃该因子
#     if window is None:
#         window = 6
#     a = df['LastPrice']
#     b = df['Volume'].diff()
#     c = df['LastPrice'].diff()
#     return (-1)*c.rank()*pd.concat([a, b], axis=1).\
#                rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)


def feature_rank_cov_5(df,window=None): #IC -0.07
    if window is None:
        window = 6
    a = df['TotalValueTraded'].diff()
    b = df['Volume'].diff()
    c = df['LastPrice'].diff()
    factor= (-1)*c.rank()*pd.concat([a, b], axis=1).\
               rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)
    return np.nan_to_num(factor, posinf=0, neginf=0)

def feature_rank_cov_6(df, window=None):  # IC 0.02
    if window is None:
        window = 6
    a = df['LastPrice']
    b = df['TotalValueTraded'].diff()

    factor = pd.concat([a.rank(), b.rank()], axis=1). \
                 rolling(window=window, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)
    factor = factor.rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_rank_rolling_std_1(df, window1=None, window2=None):#IC -0.04
    #参考alpha18
    if window1 is None:
        window1 = 5
    if window2 is None:
        window2 = 10

    a = df['LastPrice']
    b = df['BidPrice1']

    p = a - b
    factor=(-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_rank_rolling_std_2(df, window1=None, window2=None):#IC 0.02
    #参考alpha18
    if window1 is None:
        window1 = 5
    if window2 is None:
        window2 = 10

    a = df['LastPrice']
    b = df['BidVol1']
    p = a - b
    factor=(-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)


def feature_rank_rolling_std_3(df, window1=None, window2=None):#IC -0.02
    #参考alpha18
    if window1 is None:
        window1 = 5
    if window2 is None:
        window2 = 10

    a = df['Volume'].diff()
    b = df['AskVol1']
    p = a - b
    factor=(-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)

def feature_rank_rolling_std_4(df, window1=None, window2=None):#IC -0.04
    #参考alpha18
    if window1 is None:
        window1 = 5
    if window2 is None:
        window2 = 10

    a = df['BidVol1']
    b = df['AskVol1']
    p = a - b
    factor=(-1)*(p.abs().rolling(window1).std()+p+pd.concat([a, b], axis=1).\
               rolling(window=window2, min_periods=1).corr().iloc[::2, 1].reset_index(drop=True)).rank()
    return np.nan_to_num(factor, posinf=0, neginf=0)




#alpha30以后的因子

# 计算 Alpha#31 因子
def alpha_31(df): #IC -0.03
    # 1. 计算 delta(close, 10)
    delta_close_10 = delta(df, 'BidPrice1', 10)

    # 4. 计算 delta(close, 3)
    delta_close_3 = delta(df, 'BidPrice1', 3)

    # 7. 标准化 correlation(adv20, low, 12)
    scaled_corr = scale(correlation(df, 'LastPrice', 'BidPrice1', 12))

    # 8. 最终因子 Alpha#31
    factor = (rank(rank(rank(-1 * rank(delta_close_10)))) + rank(-delta_close_3)) + sign(scaled_corr)

    return np.nan_to_num(factor, posinf=0, neginf=0)


# 计算 Alpha#32 因子
def alpha_32_1(df, window1=None, window2=None): #IC -0.02
    if window1 is None:
        window1 = 7
    if window2 is None:
        window2 = 230
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale(df['BidPrice1'].rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)

def alpha_32_2(df, window1=None, window2=None): #IC -0.02
    if window1 is None:
        window1 = 7
    if window2 is None:
        window2 = 230
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale((df['Volume'].diff()).rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)

def alpha_32_3(df, window1=None, window2=None): #IC -0.02
    if window1 is None:
        window1 = 7
    if window2 is None:
        window2 = 230
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale(df['BidVol1'].rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)

def alpha_32_4(df, window1=None, window2=None): #IC -0.01
    if window1 is None:
        window1 = 7
    if window2 is None:
        window2 = 230
    scale_a_diff = scale(mysum(df['LastPrice'], window1) / window1 - df['LastPrice'])
    scale_corr = scale(df['AskVol1'].rolling(window=window2).corr(df['LastPrice'])) * 20
    factor = scale_a_diff + scale_corr
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#34: rank(((1 - rank((stddev(returns, 2) / stddev(returns, 5)))) + (1 - rank(delta(close, 1)))))
def alpha_34_1(df,window1=None, window2=None):  #IC -0.05
    if window1 is None:
        window1 = 2
    if window2 is None:
        window2 = 5
    returns = df['LastPrice'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['LastPrice'], window=1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

def alpha_34_2(df,window1=None, window2=None): #IC -0.01
    if window1 is None:
        window1 = 2
    if window2 is None:
        window2 = 5
    returns = ((df['BidVol1'] - df['AskVol1']) / (df['BidVol1'] + df['AskVol1'])).pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(((df['BidVol1'] - df['AskVol1']) / (df['BidVol1'] + df['AskVol1'])), 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

def alpha_34_3(df,window1=None, window2=None): #IC -0.03
    if window1 is None:
        window1 = 2
    if window2 is None:
        window2 = 5
    returns = df['BidPrice1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['BidPrice1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)


def alpha_34_4(df,window1=None, window2=None):  #IC -0.02
    if window1 is None:
        window1 = 2
    if window2 is None:
        window2 = 5
    returns = df['AskPrice1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['AskPrice1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)

def alpha_34_5(df,window1=None, window2=None):  #IC -0.02
    if window1 is None:
        window1 = 2
    if window2 is None:
        window2 = 5
    returns = df['BidVol1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['BidVol1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)


def alpha_34_6(df,window1=None, window2=None): #IC 0.03
    if window1 is None:
        window1 = 2
    if window2 is None:
        window2 = 5
    returns = df['AskVol1'].pct_change()
    stddev_2 = stddev(returns, window1)
    stddev_5 = stddev(returns, window2)
    ratio = stddev_2 / stddev_5
    rank_ratio = 1 - rank(ratio)
    delta_close_1 = delta2(df['AskVol1'], 1)
    rank_delta_close_1 = 1 - rank(delta_close_1)
    factor = rank(rank_ratio + rank_delta_close_1)
    return np.nan_to_num(factor, posinf=0, neginf=0)



# Alpha#35: ((Ts_Rank(volume, 32) * (1 - Ts_Rank(((close + high) - low), 16))) * (1 - Ts_Rank(returns, 32)))
def alpha_35(df,window1=None, window2=None): #IC -0.06
    if window1 is None:
        window1 = 32
    if window2 is None:
        window2 = 16
    volume_ts_rank = ts_rank(df['Volume'].diff(),window1)

    close_high_low = (df['LastPrice'] + df['High']) - df['Low']
    close_high_low_ts_rank = 1 - ts_rank(close_high_low, window2)

    returns = df['LastPrice'].pct_change()

    returns_ts_rank = 1 - ts_rank(returns,window2)
    factor = volume_ts_rank * close_high_low_ts_rank * returns_ts_rank
    return np.nan_to_num(factor, posinf=0, neginf=0)



# Alpha#37
def alpha_37(df,window1=None, window2=None): #IC -0.05
    if window1 is None:
        window1 = 1
    if window2 is None:
        window2 = 200
    delay_open_close_1 = delay(df['LastPrice'].shift(1) - df['LastPrice'], window1)
    corr_delay_open_close_close = correlation2(delay_open_close_1, df['LastPrice'], window2)
    part1 = rank(corr_delay_open_close_close)

    open_close = df['LastPrice'].shift(1) - df['LastPrice']
    part2 = rank(open_close)

    factor = part1 + part2
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#38
def alpha_38(df,window=None): #IC -0.01
    if window is None:
        window=10
    ts_rank_close_10 = ts_rank(df['LastPrice'], window)
    part1 = -1 * rank(ts_rank_close_10)

    close_open_ratio = (df['LastPrice'].shift(1))/df['LastPrice']
    part2 = rank(close_open_ratio)

    factor = part1 * part2
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#39
def alpha_39(df): #IC -0.06
    delta_close_7 = delta2(df['LastPrice'], 7)
    volume_adv20_ratio = (df['Volume'].diff()) / (df['Volume'].diff()).rolling(window=20).mean()
    decay_volume_adv20 = decay_linear(volume_adv20_ratio, 9)
    part1 = -1 * rank(delta_close_7 * (1 - rank(decay_volume_adv20)))

    sum_returns_250 = mysum(df['LastPrice'].pct_change(), 250)
    part2 = 1 + rank(sum_returns_250)

    factor = part1 * part2
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#40 不行
# def alpha_40(df):  #IC -0.00
#     stddev_high_10 = stddev(df['High'], 10)
#     part1 = -1 * rank(stddev_high_10)
#
#     corr_high_volume = correlation2(df['High'], df['Volume'].diff(), 10)
#     part2 = corr_high_volume
#
#     factor = part1 * part2
#     return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#41: (((high * low)^0.5) - vwap)
def alpha_41(df): #IC -0.01
    high_low_sqrt = signedpower(df['High'] * df['Low'], 0.5)
    factor = high_low_sqrt - vwap(df)
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#42: (rank((vwap - close)) / rank((vwap + close)))
def alpha_42(df): #IC 0.02
    vwap_close_diff = vwap(df) - df['LastPrice']
    vwap_close_sum = vwap(df) + df['LastPrice']
    factor = rank(vwap_close_diff) / rank(vwap_close_sum)
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#43: (ts_rank((volume / adv20), 20) * ts_rank((-1 * delta(close, 7)), 8))
def alpha_43(df): #IC -0.04
    volume_adv20_ratio = (df['Volume'].diff())/ (df['Volume'].diff()).rolling(window=20).mean()
    ts_rank_volume_adv20 = ts_rank(volume_adv20_ratio, 20)
    delta_close_7 = delta2(df['LastPrice'], 7)
    ts_rank_delta_close_7 = ts_rank(-1 * delta_close_7, 8)
    factor = ts_rank_volume_adv20 * ts_rank_delta_close_7
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#44: (-1 * correlation(high, rank(volume), 5))
def alpha_44(df): # IC 0.01
    rank_volume = rank(df['Volume'].diff())
    corr_high_rank_volume = correlation2(df['High'], rank_volume, 5)
    factor = -1 * corr_high_rank_volume
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#45: (-1 * ((rank((sum(delay(close, 5), 20) / 20)) * correlation(close, volume, 2)) * rank(correlation(sum(close, 5), sum(close, 20), 2))))
def alpha_45(df): #IC -0.02
    delay_close_5 = delay(df['LastPrice'], 5)
    sum_delay_close_20 = mysum(delay_close_5, 20)
    rank_sum_delay_close = rank(sum_delay_close_20 / 20)

    corr_close_volume = correlation2(df['LastPrice'],df['Volume'].diff(), 2)

    sum_close_5 = mysum(df['LastPrice'], 5)
    sum_close_20 = mysum(df['LastPrice'], 20)
    corr_sum_close_5_sum_close_20 = correlation2(sum_close_5, sum_close_20, 2)
    rank_corr_sum_close = rank(corr_sum_close_5_sum_close_20)

    factor = -1 * (rank_sum_delay_close * corr_close_volume * rank_corr_sum_close)
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#46
def alpha_46(df): #IC -0.06
    delay_close_20 = delay(df['LastPrice'], 20)
    delay_close_10 = delay(df['LastPrice'], 10)
    part1 = ((delay_close_20 - delay_close_10) / 10) - ((delay_close_10 - df['LastPrice']) / 10)

    condition1 = (part1 > 0.25)
    condition2 = (part1 < 0)

    factor = np.where(condition1, -1, np.where(condition2, 1, (-1) * (df['LastPrice'] - delay(df['LastPrice'], 1))))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#47
def alpha_47(df): #IC 0.02
    rank_1_close = rank(1 / df['LastPrice'])
    part1 = (rank_1_close * df['Volume'].diff()) /(df['Volume'].diff()).rolling(window=20).mean()

    high_rank = rank(df['High'] - df['LastPrice'])
    part2 = (df['High'] * high_rank) / (mysum(df['High'], 5) / 5)

    part3 = rank(vwap(df) - delay(vwap(df), 5))

    factor = (part1 * part2) - part3
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#49
def alpha_49(df): #IC -0.07
    delay_close_20 = delay(df['LastPrice'], 20)
    delay_close_10 = delay(df['LastPrice'], 10)
    part1 = ((delay_close_20 - delay_close_10) / 10) - ((delay_close_10 - df['LastPrice']) / 10)

    condition = (part1 < -0.1)

    factor = np.where(condition, 1, (-1) * (df['LastPrice'] - delay(df['LastPrice'], 1)))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#50不行


# Alpha#51
def alpha_51(df): #IC -0.07
    delay_close_20 = delay(df['LastPrice'], 20)
    delay_close_10 = delay(df['LastPrice'], 10)
    part1 = ((delay_close_20 - delay_close_10) / 10) - ((delay_close_10 - df['LastPrice']) / 10)

    condition = (part1 < -0.05)

    factor = np.where(condition, 1, (-1) * (df['LastPrice'] - delay(df['LastPrice'], 1)))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#52
def alpha_52(df): #IC 0.02
    ts_min_low_5 = ts_min(df['Low'], 5)
    part1 = (-1 * ts_min_low_5) + delay(ts_min_low_5, 5)

    returns = df['LastPrice'].pct_change()
    part2 = rank((mysum(returns, 240) - mysum(returns, 20)) / 220)

    part3 = ts_rank(df['Volume'].diff(), 5)

    factor = (part1 * part2) * part3
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#53
def alpha_53(df): #IC -0.02
    part1 = ((df['LastPrice'] - df['Low']) - (df['High'] - df['LastPrice'])) / (df['LastPrice'] - df['Low'])
    factor = -1 * delta2(part1, 9)
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#54 不行


# Alpha#55
def alpha_55(df): #IC -0.03
    ts_min_low_12 = ts_min(df['Low'], 12)
    ts_max_high_12 = ts_max(df['High'], 12)
    part1 = (df['LastPrice'] - ts_min_low_12) / (ts_max_high_12 - ts_min_low_12)
    part2 = rank(part1)
    part3 = rank(df['Volume'].diff())
    factor = -1 * correlation2(part2, part3, 6)
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#56
def alpha_56(df, cap=None): #IC -0.05
    if cap is None:
        cap=1
    returns = df['LastPrice'].pct_change()
    part1 = rank(mysum(returns, 10) / mysum(mysum(returns, 2), 3))
    part2 = rank(returns * cap)
    factor = 0 - (1 * (part1 * part2))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#57
def alpha_57(df): #IC 0.02
    part1 = (df['LastPrice'] - vwap(df))
    part2 = decay_linear(rank(ts_argmax(df['LastPrice'], 30)), 2)
    factor = 0 - (1 * (part1 / part2))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#60
def alpha_60(df): #IC 0.04
    part1 = ((df['LastPrice'] - df['Low']) - (df['High'] - df['LastPrice'])) / (df['High'] - df['Low']) *(df['Volume'].diff())
    part1_scaled = scale(rank(part1))
    part2 = scale(rank(ts_argmax(df['LastPrice'], 10)))
    factor = 0 - (1 * ((2 * part1_scaled) - part2))
    return np.nan_to_num(factor, posinf=0, neginf=0)


# Alpha#61 不行
# def alpha_61(df):
#     """Alpha#61: 基于VWAP和ADV180的因子"""
#     vwap_val = vwap(df)
#     adv180_val = (df['Volume'].diff()).rolling(window=180).mean()
#     part1 = rank(vwap_val - ts_min(vwap_val, 16))
#     part2 = rank(correlation2(vwap_val, adv180_val, 18))
#     return part1 < part2

# Alpha#62
def alpha_62(df): # IC -0.01
    """Alpha#62: 基于VWAP、ADV20、开盘价、最高价和最低价的因子"""
    vwap_val = vwap(df)
    adv20_val = (df['Volume'].diff()).rolling(window=20).mean()
    part1 = rank(correlation2(vwap_val, mysum(adv20_val, 22), 10))
    part2 = (rank(df['LastPrice'].shift(1)) + rank(df['LastPrice'].shift(1))) < (rank((df['High'] + df['Low']) / 2) + rank(df['High']))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#63不行

# Alpha#64  不行
# def alpha_64(df):
#     """Alpha#64: 基于开盘价、最低价、ADV120、最高价和VWAP的因子"""
#     vwap_val = vwap(df)
#     adv120_val = (df['Volume'].diff()).rolling(window=120).mean()
#     part1 = rank(correlation2(mysum(df['LastPrice'].shift(1) * 0.178404 + df['Low'] * (1 - 0.178404), 13), mysum(adv120_val, 13), 17))
#     part2 = rank(delta2((df['High'] + df['Low']) / 2 * 0.178404 + vwap_val * (1 - 0.178404), 4))
#     return (part1 < part2) * -1

# Alpha#65
def alpha_65(df): #IC -0.03
    """Alpha#65: 基于开盘价、VWAP和ADV60的因子"""
    vwap_val = vwap(df)
    adv60_val =(df['Volume'].diff()).rolling(window=60).mean()
    part1 = rank(correlation2(df['LastPrice'].shift(1) * 0.00817205 + vwap_val * (1 - 0.00817205), mysum(adv60_val, 9), 6))
    part2 = rank(df['LastPrice'].shift(1) - ts_min(df['LastPrice'].shift(1), 14))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#66
def alpha_66(df):  #IC 0.01
    """Alpha#66: 基于VWAP、最低价、开盘价和最高价的因子"""
    vwap_val = vwap(df)
    part1 = rank(decay_linear(delta2(vwap_val, 4), 7))
    part2 = ts_rank(decay_linear((df['Low'] * 0.96633 + df['Low'] * (1 - 0.96633) - vwap_val) / \
                                 (df['LastPrice'].shift(1) - (df['High'] + df['Low']) / 2), 11), 7)
    factor= (part1 + part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#67 不行


# Alpha#68
def alpha_68(df): #IC 0.02
    """Alpha#68: 基于最高价、ADV15、收盘价和最低价的因子"""
    adv15_val = (df['Volume'].diff()).rolling(window=15).mean()
    part1 = ts_rank(correlation2(rank(df['High']), rank(adv15_val), 9), 14)
    part2 = rank(delta2(df['LastPrice'] * 0.518371 + df['Low'] * (1 - 0.518371), 1))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#69 不行

# Alpha#70 不行

# Alpha#71 不行
# def alpha_71(df): #IC -0.00
#     """Alpha#71: 基于收盘价、ADV180、最低价、开盘价和VWAP的因子"""
#     ts_rank_close = ts_rank(df['LastPrice'], 3)
#     ts_rank_adv180 = ts_rank((df['Volume'].diff()).rolling(window=180).mean(), 12)
#     part1 = ts_rank(decay_linear(correlation2(ts_rank_close, ts_rank_adv180, 18), 4), 16)
#
#     part2 = ts_rank(decay_linear((rank((df['Low'] + df['LastPrice'].shift(1)) - (vwap(df) + vwap(df))) ** 2), 16), 4)
#
#     factor= np.maximum(part1, part2)
#     return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#72
def alpha_72(df): #IC 0.02
    """Alpha#72: 基于最高价、最低价、ADV40、VWAP和成交量的因子"""
    part1 = rank(decay_linear(
        correlation2((df['High'] + df['Low']) / 2,(df['Volume'].diff()).rolling(window=40).mean(), 9), 10))
    part2 = rank(decay_linear(correlation2(ts_rank(vwap(df), 4), ts_rank(df['Volume'], 19), 7), 3))
    factor= part1 / part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#73
def alpha_73(df): #IC 0.02
    """Alpha#73: 基于VWAP、开盘价和最低价的因子"""
    part1 = rank(decay_linear(delta2(vwap(df), 5), 3))

    weighted_price = (df['Volume'].shift(1))* 0.147155 + df['Low'] * (1 - 0.147155)
    part2 = ts_rank(decay_linear((delta2(weighted_price, 2) / weighted_price) * -1, 3), 17)

    factor= np.maximum(part1, part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#74 不行
# def alpha_74(df):
#     """Alpha#74: 基于收盘价、ADV30、最高价、VWAP和成交量的因子"""
#     part1 = rank(correlation2(df['LastPrice'], mysum((df['Volume'].diff()).rolling(window=30).mean(), 37), 15))
#     part2 = rank(correlation2(rank(df['High'] * 0.0261661 + vwap(df) * (1 - 0.0261661)), rank(df['Volume']), 11))
#
#     return (part1 < part2) * -1

# Alpha#75
def alpha_75(df): #IC 0.01
    """Alpha#75: 基于VWAP、成交量、最低价和ADV50的因子"""
    part1 = rank(correlation2(vwap(df), df['Volume'], 4))
    part2 = rank(correlation2(rank(df['Low']), rank((df['Volume'].diff()).rolling(window=50).mean()), 12))

    factor= part1 < part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

# 76不行

# Alpha#77  不行
def alpha_77(df): #IC 0.01
    """Alpha#77: 基于最高价、最低价、VWAP和ADV40的因子"""
    part1 = rank(decay_linear((((df['High'] + df['Low']) / 2) + df['High']) - (vwap(df) + df['High']), 20))
    part2 = rank(decay_linear(
        correlation2((df['High'] + df['Low']) / 2,(df['Volume'].diff()).rolling(window=40).mean(), 3), 6))

    factor= np.minimum(part1, part2)
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#78
def alpha_78(df): #IC -0.01
    """Alpha#78: 基于最低价、VWAP、ADV40和成交量的因子"""
    weighted_price = df['Low'] * 0.352233 + vwap(df) * (1 - 0.352233)
    part1 = rank(
        correlation2(mysum(weighted_price, 20), mysum((df['Volume'].diff()).rolling(window=40).mean(), 20), 7))
    part2 = rank(correlation2(rank(vwap(df)), rank(df['Volume']), 6))

    factor= part1 ** part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

# Alpha#79不行

# Alpha#80不行

#Alpha#81 不行

#Alpha#82 不行

#Alpha#83 不行
# def alpha_83(df):
#     """Alpha#83: 基于最高价、最低价、收盘价、成交量和VWAP的因子"""
#     part1 = rank(delay((df['High'] - df['Low']) / (mysum(df['LastPrice'], 5) / 5), 2))
#     part2 = rank(rank(df['Volume']))
#     part3 = ((df['High'] - df['Low']) / (mysum(df['LastPrice'], 5) / 5)) / (vwap(df) - df['LastPrice'])
#     return (part1 * part2) / part3

#Alpha#84 不行
# def alpha_84(df):
#     """Alpha#84: 基于VWAP和收盘价的因子"""
#     part1 = ts_rank(vwap(df) - ts_max(vwap(df), 15), 21)
#     part2 = delta2(df['LastPrice'], 5)
#     return signedpower(part1, part2)

#Alpha#85 不行
# def alpha_85(df): #IC -0.00
#     """Alpha#85: 基于最高价、收盘价、ADV30和成交量的因子"""
#     part1 = rank(correlation2(df['High'] * 0.876703 + df['LastPrice']* (1 - 0.876703),(df['Volume'].diff()).rolling(window=30).mean(), 10))
#     part2 = rank(correlation2(ts_rank((df['High'] + df['Low']) / 2, 4), ts_rank(df['Volume'], 10), 7))
#     factor= part1 ** part2
#     return np.nan_to_num(factor, posinf=0, neginf=0)


#Alpha#86
def alpha_86(df): #IC 0.02
    """Alpha#86: 基于收盘价、ADV20、开盘价和VWAP的因子"""
    part1 = ts_rank(correlation2(df['LastPrice'], mysum((df['Volume'].diff()).rolling(window=20).mean(), 15), 6), 20)
    part2 = rank((df['LastPrice'].shift(1) + df['LastPrice']) - (vwap(df) + df['LastPrice'].shift(1)))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#87 不行

#Alpha#88
def alpha_88(df): #IC -0.02
    """Alpha#88: 基于开盘价、最低价、最高价、收盘价和ADV60的因子"""
    part1 = rank(decay_linear((df['LastPrice'].shift(1) + rank(df['Low'])) - (rank(df['High']) + rank(df['LastPrice'])), 8))
    part2 = ts_rank(decay_linear(correlation2(ts_rank(df['LastPrice'], 8), ts_rank((df['Volume'].diff()).rolling(window=60).mean(), 21), 8), 7), 3)
    factor= np.minimum(part1, part2)
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#89 不行

#Alpha#90 不行

#Alpha#91 不行

#Alpha#92
def alpha_92(df): #IC 0.03
    """Alpha#92: 基于最高价、最低价、收盘价、开盘价和ADV30的因子"""
    part1 = ts_rank(decay_linear((((df['High'] + df['Low']) / 2) + df['LastPrice']) < (df['Low'] + df['LastPrice'].shift(1)), 15), 19)
    part2 = ts_rank(decay_linear(correlation2(rank(df['Low']), rank((df['Volume'].diff()).rolling(window=30).mean()), 8), 7), 7)
    factor= np.minimum(part1, part2)
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#93 不行

#Alpha#94 不行
# def alpha_94(df): #IC 0.00
#     """Alpha#94: 基于VWAP、ADV60的因子"""
#     part1 = rank(vwap(df) - ts_min(vwap(df), 12))
#     part2 = ts_rank(correlation2(ts_rank(vwap(df), 20), ts_rank((df['Volume'].diff()).rolling(window=60).mean(), 4), 18), 3)
#     factor= (part1 ** part2) * -1
#     return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#95 不行
# def alpha_95(df): #IC 0.00
#     """Alpha#95: 基于开盘价、最高价、最低价、ADV40的因子"""
#     part1 = rank(df['LastPrice'].shift(1) - ts_min(df['LastPrice'].shift(1), 12))
#     part2 = ts_rank((rank(correlation2(mysum((df['High'] + df['Low']) / 2, 19), mysum((df['Volume'].diff()).rolling(window=40).mean(), 19), 13)) ** 5), 12)
#     return part1 < part2

#Alpha#96
def alpha_96(df): #IC -0.02
    """Alpha#96: 基于VWAP、成交量、收盘价、ADV60的因子"""
    part1 = ts_rank(decay_linear(correlation2(rank(vwap(df)), rank(df['Volume']), 4), 4), 8)
    part2 = ts_rank(decay_linear(ts_argmax(correlation2(ts_rank(df['LastPrice'], 7), ts_rank((df['Volume'].diff()).rolling(window=60).mean(), 4), 4), 13), 14), 13)
    factor= np.maximum(part1, part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#97 不行

#Alpha#98
def alpha_98(df): #IC -0.01
    """Alpha#98: 基于VWAP、ADV5、开盘价、ADV15的因子"""
    part1 = rank(decay_linear(correlation2(vwap(df), mysum((df['Volume'].diff()).rolling(window=5).mean(), 26), 5), 7))
    part2 = rank(decay_linear(ts_rank(ts_argmin(correlation2(rank(df['LastPrice'].shift(1)), rank((df['Volume'].diff()).rolling(window=15).mean()), 21), 9), 7), 8))
    factor= part1 - part2
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#99
def alpha_99(df): #IC -0.01
    """Alpha#99: 基于最高价、最低价、ADV60、成交量的因子"""
    part1 = rank(correlation2(mysum((df['High'] + df['Low']) / 2, 20), mysum((df['Volume'].diff()).rolling(window=60).mean(), 20), 9))
    part2 = rank(correlation2(df['Low'], df['Volume'], 6))
    factor= (part1 < part2) * -1
    return np.nan_to_num(factor, posinf=0, neginf=0)

#Alpha#100 不行

#Alpha#101
def alpha_101(df): #IC 0.05
    """Alpha#101: 基于收盘价、开盘价、最高价、最低价的因子"""
    factor= (df['LastPrice'] - df['LastPrice'].shift(1)) / ((df['High'] - df['Low']) + 0.001)
    return np.nan_to_num(factor, posinf=0, neginf=0)


# 2_20_18:00

#待测试的东西:
#1 df['LastPrice']
#2 df['BidPrice1']
#3 df['AskPrice1']
#4 df['TotalValueTraded'].diff()
#5 df['Volume'].diff()
#6 df['BidVol1']
#7 df['AskVol1']

#8 ((df['BidVol1'] - df['AskVol1']) / (df['BidVol1'] + df['AskVol1']))
#9 ((df['BidVol1'] * df['AskPrice1'] + df['AskVol1'] * df['BidPrice1']) / (df['BidVol1'] + df['AskVol1']) - (df['AskPrice1'] + df['BidPrice1']) / 2)  #(与6相关性太高)
