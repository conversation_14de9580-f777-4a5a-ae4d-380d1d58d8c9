import pandas as pd
import numpy as np
import os
import re
from utils import *
from factors.ob_std import *
from factors.ob_extra import *
import seaborn as sns
import matplotlib.pyplot as plt
import math


class BaseDataGenerator:
    """
    数据生成器基类。

    属性：
        data_dir (str): 数据文件所在的目录。
        future (str): 目标合约的名称前缀（如 'IC'）。
        daily_md_list (list): 存储每日市场数据的列表。
    """

    def __init__(self, data_dir: str, future: str):
        """
        初始化 BaseDataGenerator 实例。

        Args:
            data_dir (str): 数据文件所在的目录。
            future (str): 目标合约的名称前缀（如 'IC'）。
        """
        self.data_dir = data_dir
        self.future = future
        self.daily_md_list = []

    def load_data(self):
        """
        从指定目录加载市场数据文件。

        Returns:
            None: 数据存储在 self.daily_md_list 中。
        """
        print("data load...")
        # 获取目录下所有文件名
        all_files = os.listdir(self.data_dir)
        all_files.sort()

        # 筛选出以 md 开头且以 .csv 结尾的文件
        md_files = [file for file in all_files if file.startswith('md') and file.endswith('.csv')]
        # 遍历文件，读取并存储到列表中
        for file in md_files:
            file_path = os.path.join(self.data_dir, file)  # 拼接完整文件路径
            df = pd.read_csv(file_path)  # 读取 CSV 文件
            d = file.split('_')[1]
            df['timestamp_str'] = df['timestamp_str'].apply(lambda s: f'{d} {s}')
            self.daily_md_list.append(df)  # 将数据框添加到列表中

    def _filter_and_clean_data(self, daily_data: pd.DataFrame, type_name: str) -> pd.DataFrame:
        """
        筛选指定合约的数据，并剔除不需要的列和全为0的列。

        Args:
            daily_data (pd.DataFrame): 每日市场数据，包含时间戳、买卖价格等信息。
            type_name (str): 目标合约的名称（如 'IC2412'）。

        Returns:
            pd.DataFrame: 筛选并清理后的数据。
        """
        # 筛选指定合约的数据
        data_type = daily_data[daily_data['Symbol'].str.startswith(type_name)].reset_index(drop=True)
        # 选择有准确时间戳的列
        data_type['TimeStamp'] = data_type['timestamp_str']
        # 剔除不需要的列
        data_type_filter = data_type.iloc[:, :35]

        # 剔除全都是0的列
        data_type_filter = data_type_filter.loc[:, (data_type_filter != 0).any(axis=0)]

        return data_type_filter


class FactorDataGenerator(BaseDataGenerator):
    """
    因子数据生成器类。

    属性：
        daily_standmd_list (list): 存储每日因子数据的列表。
        df_all (pd.DataFrame): 所有因子数据的汇总,直接用于模型建立 DataFrame。
    """

    def __init__(self, data_dir: str, future: str):
        """
        初始化 FactorDataGenerator 实例。

        Args:
            data_dir (str): 数据文件所在的目录。
            future (str): 目标合约的名称前缀（如 'IC'）。
        """
        super().__init__(data_dir, future)
        self.daily_standmd_list = []
        self.daily_submd_list = []
        self.df_all = None

    def generate_factor_data(self, use_subfactor=False, use_similar=False, split_method=None, percent=0.2):
        """
        生成因子数据。
        
        Args:
            use_similar : 是否利用相似合约构建的因子值
            use_subfactor : 是否利用已有的合约构建的因子值。
            split_method : 添加label标签,label=0表示训练集,label=1表示测试集,方便后续回测

        Returns:
            None: 因子数据存储在 self.daily_standmd_list 和 self.df_all 中。
        """
        print("factor generate...")
        for daily_md in self.daily_md_list:
            # 提取合约中的数字部分
            trade_exp = daily_md['Symbol'].apply(lambda x: int(re.search(r'\d+', x).group()))

            # 找到最小的数字，作为主力合约（后续需要处理主力合约到期的情况）
            min_number = trade_exp.min()
            target_future = self.future + str(min_number)
            # 生成因子数据
            df_stand = self._data_generate(daily_md, target_future)
            df_stand['TimeStamp'] = pd.to_datetime(df_stand['TimeStamp'])
            if use_subfactor:
                # 找到最小的两个唯一值,这里利用次月合约的数据
                unique_nsmallest = trade_exp.drop_duplicates().nsmallest(2)
                second_min_number = unique_nsmallest.iloc[-1]
                sub_future = self.future + str(second_min_number)
                df_stand_sub = self._data_generate_subfactor(daily_md, sub_future)
                df_stand_sub['TimeStamp'] = pd.to_datetime(df_stand_sub['TimeStamp'])
                # merge因子数据
                df_stand = pd.merge_asof(df_stand, df_stand_sub, on='TimeStamp', direction='nearest')
            if use_similar:
                future_map = {'IC': 'IM', 'IM': 'IC', 'IF': 'IH', 'IH': 'IF'}
                similar_future = future_map[self.future]
                similar_future = similar_future + str(min_number)
                df_stand_sim = self._data_generate_similarfactor(daily_md, similar_future)
                df_stand_sim['TimeStamp'] = pd.to_datetime(df_stand_sim['TimeStamp'])
                # merge因子数据
                df_stand = pd.merge_asof(df_stand, df_stand_sim, on='TimeStamp', direction='nearest')
            self.daily_standmd_list.append(df_stand)

        # 按照日划分训练集和测试集
        if split_method == 'day':
            self.label_test_set(percent=percent)

        # 按行拼接所有数据
        self.df_all = pd.concat(self.daily_standmd_list, axis=0, ignore_index=True)
        # 去除空值
        self.df_all.dropna(inplace=True)

    def label_test_set(self, percent):
        number_of_days = len(self.daily_standmd_list)
        test_days = max(math.floor(number_of_days * percent), 1)
        for i, df in enumerate(self.daily_standmd_list):
            df['label'] = 0 if i < number_of_days - test_days else 1

    def _data_generate(self, daily_data: pd.DataFrame, type_name: str) -> pd.DataFrame:
        """
        从每日市场数据中生成因子数据，包括目标变量。

        Args:
            daily_data (pd.DataFrame): 每日市场数据，包含时间戳、买卖价格等信息。
            type_name (str): 目标合约的名称（如 'IC2412'）。

        Returns:
            pd.DataFrame: 包含因子数据和目标变量的 DataFrame。
        """
        # 筛选并清理数据
        data_type_filter = self._filter_and_clean_data(daily_data, type_name)
        # 计算 mid 价格（买卖价格的中间值）
        mid = (data_type_filter['AskPrice1'] + data_type_filter['BidPrice1']) / 2
        # 计算未来5秒的 mid 价格均值
        forward_rolling_mid = mid.rolling(window=10).mean().shift(-9)
        # 计算目标变量（未来 mid 价格的变化率）
        target = (forward_rolling_mid - mid) / mid

        # 构建因子数据
        data_stand = pd.DataFrame({
            'TimeStamp': data_type_filter['TimeStamp'],  # 时间戳
            'Feature1': order_imb(data_type_filter),  # 订单不平衡因子
            'Feature1-2': order_imb(data_type_filter, level='2'),  # 订单不平衡因子（第二档）
            'Feature1-3': order_imb(data_type_filter, level='3'),  # 订单不平衡因子（第三档）
            'Feature2': order_price_mid(data_type_filter),  # 订单价格中间值因子
            'Feature2-2': order_price_mid(data_type_filter, level='2'),  # 订单价格中间值因子（第二档）
            'Feature2-3': order_price_mid(data_type_filter, level='3'),  # 订单价格中间值因子（第三档）
            'Feature3': nettradeprice_mid(data_type_filter),  # 净交易价格中间值因子
            'Feature4': mom_last(data_type_filter),  # 动量因子
            'Feature5': reversal_last(data_type_filter),  # 反转因子
            'Feature4-1': mom_last(data_type_filter, window=10),  # 动量因子（10秒窗口）
            'Feature5-1': reversal_last(data_type_filter, window=60),  # 反转因子（60秒窗口）

            'Feature6': order_imb_diff(data_type_filter),  # 订单不平衡差异因子
            'Feature7': ob_depth(data_type_filter),  # 订单簿深度因子
            'Feature8': trade_flow(data_type_filter, contract_mul=200, window=1),  # 交易流因子（1秒窗口）
            'Feature8-1': trade_flow(data_type_filter, contract_mul=200, window=60),  # 交易流因子（60秒窗口）
            'Feature9': order_flow_imb(data_type_filter),  # 订单流不平衡因子
            'Feature10': ret_var(data_type_filter),  # 收益方差因子
            'Feature11': ret_skew(data_type_filter),  # 收益偏度因子
            'Feature12': ret_kurtosis(data_type_filter),  # 收益峰度因子
            'Feature13': ret_var_std(data_type_filter),  # 收益方差标准差因子
            'Feature14': up_pct(data_type_filter),  # 上涨百分比因子
            'Feature16': volumn_corr(data_type_filter),  # 成交量相关性因子
            'Feature17': trend_strength(data_type_filter),  # 趋势强度因子

            #Factor from YA only IC>=0.03
            # 'Feature_rising_n_falling_trends': feature_rising_n_falling_trends(data_type_filter),
            # 'Feature_rising_n_falling_trends2': feature_rising_n_falling_trends_2(data_type_filter),
            # 'Feature_rank_cov_2': feature_rank_cov_2(data_type_filter),
            # 'Feature_rank_cov_3': feature_rank_cov_3(data_type_filter),
            # 'Feature_rank_cov_4': feature_rank_cov_4(data_type_filter),
            # 'Feature_rank_cov_5': feature_rank_cov_5(data_type_filter),
            # 'Feature_rank_rolling_std_1': feature_rank_rolling_std_1(data_type_filter),
            # 'Feature_rank_rolling_std_2': feature_rank_rolling_std_2(data_type_filter),
            # 'Feature_rank_rolling_std_3': feature_rank_rolling_std_3(data_type_filter),
            # 'Feature_rank_rolling_std_4': feature_rank_rolling_std_4(data_type_filter),
            #
            # 'Feature_alpha_31': alpha_31(data_type_filter),
            # 'Feature_alpha_32_1': alpha_32_1(data_type_filter),
            # 'Feature_alpha_34_1': alpha_34_1(data_type_filter),
            # 'Feature_alpha_34_2': alpha_34_2(data_type_filter),
            # 'Feature_alpha_34_3': alpha_34_3(data_type_filter),
            # 'Feature_alpha_34_5': alpha_34_5(data_type_filter),
            # 'Feature_alpha_34_6': alpha_34_6(data_type_filter),
            #
            #
            # 'Feature_alpha_35': alpha_35(data_type_filter),
            # 'Feature_alpha_37': alpha_37(data_type_filter),
            # 'Feature_alpha_39': alpha_39(data_type_filter),
            # 'Feature_alpha_40': alpha_40(data_type_filter),
            # 'Feature_alpha_43': alpha_43(data_type_filter),
            # 'Feature_alpha_44': alpha_44(data_type_filter),
            # 'Feature_alpha_45': alpha_45(data_type_filter),
            # 'Feature_alpha_46': alpha_46(data_type_filter),
            # 'Feature_alpha_49': alpha_49(data_type_filter),
            #
            'Feature_alpha_51': alpha_51(data_type_filter),
            # 'Feature_alpha_55': alpha_55(data_type_filter),
            # 'Feature_alpha_56': alpha_56(data_type_filter),
            # 'Feature_alpha_60': alpha_60(data_type_filter),
            # 'Feature_alpha_65': alpha_65(data_type_filter),
            # 'Feature_alpha_72': alpha_72(data_type_filter),
            #
            # 'Feature_alpha_92': alpha_92(data_type_filter),
            # 'Feature_alpha_96': alpha_96(data_type_filter),
            'Feature_alpha_101': alpha_101(data_type_filter),

            'target': target  # 目标变量
        })
        return data_stand

    def _data_generate_subfactor(self, daily_data: pd.DataFrame, type_name: str) -> pd.DataFrame:
        """
        从每日市场数据中利用次级合约的信息生成因子数据，不包括目标变量。
        可以指定计算选入的因子数据。

        Args:
            daily_data (pd.DataFrame): 每日市场数据，包含时间戳、买卖价格等信息。
            type_name (str): 目标合约的名称（如 'IC2412'）。

        Returns:
            pd.DataFrame: 包含因子数据和目标变量的 DataFrame。
        """
        # 筛选并清理数据
        data_type_filter = self._filter_and_clean_data(daily_data, type_name)
        # 构建因子数据
        data_stand = pd.DataFrame({
            'TimeStamp': data_type_filter['TimeStamp'],  # 时间戳
            # 'Feature1': order_imb(data_type_filter),  # 订单不平衡因子
            # 'Feature2': order_price_mid(data_type_filter),  # 订单价格中间值因子
            # 'Feature3': nettradeprice_mid(data_type_filter),  # 净交易价格中间值因子
            'Feature4': mom_last(data_type_filter),  # 动量因子
            'Feature5': reversal_last(data_type_filter),  # 反转因子
            'Feature4-1': mom_last(data_type_filter, window=10),  # 动量因子（10秒窗口）
            'Feature5-1': reversal_last(data_type_filter, window=60),  # 反转因子（60秒窗口）
            # 'Feature10': ret_var(data_type_filter),  # 收益方差因子
            # 'Feature11': ret_skew(data_type_filter),  # 收益偏度因子
            # 'Feature12': ret_kurtosis(data_type_filter),  # 收益峰度因子
            # 'Feature13': ret_var_std(data_type_filter),  # 收益方差标准差因子
            # 'Feature14': up_pct(data_type_filter),  # 上涨百分比因子
            # 'Feature16': volumn_corr(data_type_filter),  # 成交量相关性因子
            'Feature17': trend_strength(data_type_filter),  # 趋势强度因子
        })
        return data_stand

    def _data_generate_similarfactor(self, daily_data: pd.DataFrame, type_name: str) -> pd.DataFrame:
        """
        从每日市场数据中相似的合约的信息生成因子数据。
        可以指定计算选入的因子数据。

        Args:
            daily_data (pd.DataFrame): 每日市场数据，包含时间戳、买卖价格等信息。
            type_name (str): 目标合约的名称（如 'IC2412'）。

        Returns:
            pd.DataFrame: 包含因子数据和目标变量的 DataFrame。
        """
        # 筛选并清理数据
        data_type_filter = self._filter_and_clean_data(daily_data, type_name)

        # 构建因子数据
        data_stand = pd.DataFrame({
            'TimeStamp': data_type_filter['TimeStamp'],  # 时间戳
            'Feature1': order_imb(data_type_filter),  # 订单不平衡因子
            'Feature1-2': order_imb(data_type_filter, level='2'),  # 订单不平衡因子（第二档）
            'Feature1-3': order_imb(data_type_filter, level='3'),  # 订单不平衡因子（第三档）
            'Feature2': order_price_mid(data_type_filter),  # 订单价格中间值因子
            'Feature2-2': order_price_mid(data_type_filter, level='2'),  # 订单价格中间值因子（第二档）
            'Feature2-3': order_price_mid(data_type_filter, level='3'),  # 订单价格中间值因子（第三档）
            'Feature3': nettradeprice_mid(data_type_filter),  # 净交易价格中间值因子
            'Feature4': mom_last(data_type_filter),  # 动量因子
            'Feature5': reversal_last(data_type_filter),  # 反转因子
            'Feature4-1': mom_last(data_type_filter, window=10),  # 动量因子（10秒窗口）
            'Feature5-1': reversal_last(data_type_filter, window=60),  # 反转因子（60秒窗口）
            'Feature6': order_imb_diff(data_type_filter),  # 订单不平衡差异因子
            'Feature7': ob_depth(data_type_filter),  # 订单簿深度因子
            'Feature8': trade_flow(data_type_filter, contract_mul=200, window=1),  # 交易流因子（1秒窗口）
            'Feature8-1': trade_flow(data_type_filter, contract_mul=200, window=60),  # 交易流因子（60秒窗口）
            'Feature9': order_flow_imb(data_type_filter),  # 订单流不平衡因子
            'Feature10': ret_var(data_type_filter),  # 收益方差因子
            'Feature11': ret_skew(data_type_filter),  # 收益偏度因子
            'Feature12': ret_kurtosis(data_type_filter),  # 收益峰度因子
            'Feature13': ret_var_std(data_type_filter),  # 收益方差标准差因子
            'Feature14': up_pct(data_type_filter),  # 上涨百分比因子
            'Feature16': volumn_corr(data_type_filter),  # 成交量相关性因子
            'Feature17': trend_strength(data_type_filter),  # 趋势强度因子
        })

        return data_stand

    def get_df_all(self):
        return self.df_all

    def save_to_csv(self, output_path: str):
        """
        将生成的因子数据保存到 CSV 文件。

        Args:
            output_path (str): 输出文件的路径。
        """
        if self.df_all is not None:
            feature = self.df_all.loc[:,
                      self.df_all.columns.str.startswith('Feature') | (self.df_all.columns == 'target')]
            correlation_matrix = feature.corr()

            # 可视化相关性矩阵
            plt.figure(figsize=(16, 12))
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)
            plt.title("Correlation Matrix")
            plt.savefig('C:/Users/<USER>/Desktop/data/fig/corr_matrix.png')

            self.df_all.to_csv(output_path, index=False)
        else:
            raise ValueError("未生成因子数据，请先调用 generate_factor_data 方法。")


class SingleFactorAnalyzeDataGenerator(BaseDataGenerator):
    """
    单因子分析数据生成器类。

    属性：
        data_for_analyze (list): 存储每日原始数据的列表，用于单因子测试。
    """

    def __init__(self, data_dir: str, future: str):
        """
        初始化 FactorAnalyzeDataGenerator 实例。

        Args:
            data_dir (str): 数据文件所在的目录。
            future (str): 目标合约的名称前缀（如 'IC'）。
        """
        super().__init__(data_dir, future)
        self.data_for_analyze = []

    def generate_analyze_data(self):
        """
        生成用于因子分析的原始数据。

        该方法遍历每日市场数据，提取目标合约的数据，并筛选清理后存储到 `self.data_for_analyze` 中。

        Returns:
            None: 数据存储在 `self.data_for_analyze` 列表中。
        """
        for daily_md in self.daily_md_list:
            # 提取合约中的数字部分
            trade_exp = daily_md['Symbol'].apply(lambda x: int(re.search(r'\d+', x).group()))

            # 找到最小的数字，作为主力合约（后续需要处理主力合约到期的情况）
            min_number = trade_exp.min()
            target_future = self.future + str(min_number)

            # 生成简单处理后的行情数据
            data_analyze = self._filter_and_clean_data(daily_md, target_future)
            self.data_for_analyze.append(data_analyze)

    def get_data_for_analyze(self):
        return self.data_for_analyze

    def save_to_csv(self, output_path: str):
        """
        将生成的原始数据保存到 CSV 文件。

        Args:
            output_path (str): 输出文件的路径。
        """
        if self.data_for_analyze:
            df_all = pd.concat(self.data_for_analyze, axis=0, ignore_index=True)
            df_all.to_csv(output_path, index=False)
        else:
            raise ValueError("未生成原始数据，请先调用 generate_analyze_data 方法。")


class BacktestDataGenerator(BaseDataGenerator):
    """
    用于回测的数据生成器类。

    属性：
        data_for_analyze (list): 存储每日原始数据的列表，用于回测测试。
    """

    def __init__(self, data_dir: str, future: str):
        """
        初始化 FactorAnalyzeDataGenerator 实例。

        Args:
            data_dir (str): 数据文件所在的目录。
            future (str): 目标合约的名称前缀（如 'IC'）。
        """
        super().__init__(data_dir, future)
        self.data_for_backtest = []

    def generate_backtest_data(self):
        """
        生成用于回测的原始数据。

        该方法遍历每日市场数据，提取目标合约的数据，并筛选清理后存储到 `self.data_for_backtest` 中。

        Returns:
            None: 数据存储在 `self.data_for_backtest` 列表中。
        """
        for daily_md in self.daily_md_list:
            # 提取合约中的数字部分
            trade_exp = daily_md['Symbol'].apply(lambda x: int(re.search(r'\d+', x).group()))

            # 找到最小的数字，作为主力合约（后续需要处理主力合约到期的情况）
            min_number = trade_exp.min()
            target_future = self.future + str(min_number)

            # 生成简单处理后的行情数据
            data_bt = self._filter_and_clean_data(daily_md, target_future)
            self.data_for_backtest.append(data_bt)

    def get_data_for_bt(self):
        return self.data_for_backtest
