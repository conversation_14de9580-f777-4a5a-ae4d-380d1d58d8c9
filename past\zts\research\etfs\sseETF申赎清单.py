# -*- coding:utf-8 -*-
import json
import time
import requests
import re


def get_curls(begin, end):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.75 Safari/537.36'
            ,'Referer': 'http://www.sse.com.cn/disclosure/fund/etflist/'
    }

    etf3 = []
    num=0
    for market in range(begin-1, end):
        market += 1
        result = []
        result2 = []
        print("\r\nmarket",market,"\r\n")
        try:
            for i in range(0, 5):
                i += 1
                url3 = u"http://query.sse.com.cn/infodisplay/queryETFNewAllInfo.do?jsonCallBack=jsonpCallback22222&isPagination=true&type=%s&pageHelp.pageSize=25&pageHelp.pageCount=50&pageHelp.pageNo=%s&pageHelp.beginPage=%s&pageHelp.cacheSize=1&pageHelp.endPage=%s&_=222222222222" % (
                market, i, i, i * 10 + 1)
                web_data2 = requests.get(url3, headers=headers).text
                result.extend(json.loads(re.search('{"isPagination(.*)', web_data2).group()[:-1])['result'])
        except:
            print(u"该市场读取完毕")
            pass
        for types in result:
            num+=1
            if int(num/90)==num/90:
                time.sleep(5)
            type1 = types['etftype']
            url = "http://query.sse.com.cn/infodisplay/queryConstituentStockInfo.do?jsonCallBack=jsonpCallback22222&isPagination=false&type=%s&market=&etfClass=%s&_=2222222222222" % (
            type1, market)
            web_data = requests.get(url, headers=headers).text
            result2.append(json.loads(web_data.strip('jsonpCallback22222(')[:-1])['result'])
            print(num, types['etfFullName'])
        etf3.append(result2)
    return etf3


if __name__ == "__main__":
    etf3 = get_curls(1, 5)  # 1-5为五个etf市场
    ii = 0
    for txts in etf3:
        ii = ii + 1
        with open(r"etfxls%s.txt" % ii, "w") as file:
            for txt in txts:
                file.write(str(txt) + "\n")
        file.close()
    print('done')
